<?php

namespace App\Services;

use App\Repositories\ExampleRepository;
use App\Repositories\RepositoryInterface;
use App\Responses\ResponseInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;

class ExampleService extends ServiceAbstract
{
    // Importante: Mantenha a tipagem como RepositoryInterface para compatibilidade com a classe pai
    public RepositoryInterface $repository;

    public function __construct(ExampleRepository $repository, ResponseInterface $response)
    {
        parent::__construct($repository, $response);
        // O repository já foi atribuído no construtor pai
    }

    /**
     * Obtém uma lista paginada de exemplos
     */
    public function getPaginated(int $perPage): LengthAwarePaginator
    {
        return $this->repository->paginate($perPage);
    }

    /**
     * Encontra um exemplo pelo ID
     */
    public function find($id): ?Model
    {
        return $this->repository->find($id);
    }

    /**
     * Cria um novo exemplo
     */
    public function create(array $data): Model
    {
        // Aqui você pode adicionar validações ou lógica de negócio
        return $this->repository->create($data);
    }

    /**
     * Atualiza um exemplo existente
     */
    public function update($id, array $data): Model
    {
        // Aqui você pode adicionar validações ou lógica de negócio
        return $this->repository->update($id, $data);
    }

    /**
     * Remove um exemplo
     */
    public function delete($id): bool
    {
        return $this->repository->delete($id);
    }

    /**
     * Método específico para buscar por nome
     */
    public function findByName(string $name): ?Model
    {
        // Aqui usamos o método específico do ExampleRepository
        if ($this->repository instanceof ExampleRepository) {
            return $this->repository->findByName($name);
        }

        return null;
    }
}
