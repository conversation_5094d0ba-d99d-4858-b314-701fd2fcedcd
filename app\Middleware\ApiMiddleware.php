<?php

namespace App\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class ApiMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        // Forçar JSON
        $request->headers->set('Accept', 'application/json');

        // Gerar um ID de requisição se não existir
        $requestId = $request->header('X-Request-ID');
        if (!$requestId) {
            $requestId = (string) Str::uuid();
            $request->headers->set('X-Request-ID', $requestId);
        }

        // Processar a requisição
        $response = $next($request);

        // Adicionar headers padrão
        return $response->withHeaders([
            'X-API-Version' => config('app.api_version', '1.0'),
            'X-Request-ID' => $requestId,
        ]);
    }
}
