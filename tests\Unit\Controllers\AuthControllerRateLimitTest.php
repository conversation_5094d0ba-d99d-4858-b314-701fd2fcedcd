<?php

namespace Tests\Unit\Controllers;

use App\Controllers\AuthController;
use App\Exceptions\JWT\InvalidRefreshTokenException;
use App\Exceptions\JWT\TokenRefreshException;
use App\Responses\ResponseInterface;
use App\Services\AuthService;
use App\Services\JwtService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Validator;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Mockery;

class AuthControllerRateLimitTest extends TestCase
{
    protected $controller;
    protected $mockAuthService;
    protected $mockResponse;
    protected $mockJwtService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockAuthService = Mockery::mock(AuthService::class);
        $this->mockJwtService = Mockery::mock(JwtService::class);

        // Configuração crítica: todos os métodos retornam JsonResponse
        $this->mockResponse = Mockery::mock(ResponseInterface::class);
        $this->mockResponse->shouldReceive('ok')
            ->andReturnUsing(function ($data, $message = '') {
                return new JsonResponse(['data' => $data, 'message' => $message], 200);
            });

        $this->mockResponse->shouldReceive('created')
            ->andReturnUsing(function ($data, $message = '') {
                return new JsonResponse(['data' => $data, 'message' => $message], 201);
            });

        $this->mockResponse->shouldReceive('unauthorized')
            ->andReturnUsing(function ($message = '') {
                return new JsonResponse(['message' => $message], 401);
            });

        $this->mockResponse->shouldReceive('badRequest')
            ->andReturnUsing(function ($message = '', $errors = []) {
                return new JsonResponse(['message' => $message, 'errors' => $errors], 400);
            });

        $this->mockResponse->shouldReceive('serverError')
            ->andReturnUsing(function ($message = '') {
                return new JsonResponse(['message' => $message], 500);
            });

        $this->mockResponse->shouldReceive('tooManyRequests')
            ->andReturnUsing(function ($message = '') {
                return new JsonResponse([
                    'status' => 'error',
                    'message' => $message,
                    'code' => 429
                ], 429);
            });

        $this->mockResponse->shouldReceive('notFound')
            ->andReturnUsing(function ($message = '') {
                return new JsonResponse(['message' => $message], 404);
            });

        $this->controller = new AuthController(
            $this->mockAuthService,
            $this->mockResponse,
            $this->mockJwtService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    #[Test]
    public function it_logs_in_user_successfully()
    {
        // Preparar request
        $request = Request::create('/api/auth/login', 'POST', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // Mockando Validator
        Validator::shouldReceive('make')
            ->once()
            ->andReturn(Mockery::mock(['fails' => false]));

        // Mockando serviço de autenticação
        $tokenData = [
            'access_token' => 'test-token',
            'refresh_token' => 'test-refresh-token',
            'token_type' => 'bearer',
            'expires_in' => 3600
        ];

        $this->mockAuthService->shouldReceive('login')
            ->once()
            ->with(['email' => '<EMAIL>', 'password' => 'password123'])
            ->andReturn($tokenData);

        // Executar
        $response = $this->controller->login($request);

        // Verificar
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $this->assertArrayHasKey('authorization', $content['data']);
        $this->assertEquals('test-token', $content['data']['authorization']['token']);
    }

    #[Test]
    public function it_applies_rate_limiting_to_refresh_token_endpoint()
    {
        // Configurar o mock do Rate Limiter
        RateLimiter::shouldReceive('tooManyAttempts')
            ->once()
            ->andReturn(true);

        RateLimiter::shouldReceive('availableIn')
            ->once()
            ->andReturn(60);

        // Criar request
        $request = Request::create('/api/auth/refresh', 'POST', [
            'refresh_token' => 'test-token'
        ]);

        // Executar
        $response = $this->controller->refresh($request);

        // Verificar
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(429, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertStringContainsString('60 segundos', $content['message']);
    }

    #[Test]
    public function it_refreshes_token_successfully()
    {
        // Mock RateLimiter para não bloquear
        RateLimiter::shouldReceive('tooManyAttempts')->andReturn(false);
        RateLimiter::shouldReceive('hit')->once();

        // Mock Validator
        Validator::shouldReceive('make')
            ->once()
            ->andReturn(Mockery::mock(['fails' => false]));

        // Dados da requisição
        $request = Request::create('/api/auth/refresh', 'POST', [
            'refresh_token' => 'valid-refresh-token'
        ]);

        // Mock do serviço de autenticação
        $tokenData = [
            'access_token' => 'new-token',
            'refresh_token' => 'new-refresh-token',
            'token_type' => 'bearer',
            'expires_in' => 3600
        ];

        $this->mockAuthService->shouldReceive('refreshToken')
            ->once()
            ->with('valid-refresh-token')
            ->andReturn($tokenData);

        // Executar
        $response = $this->controller->refresh($request);

        // Verificar
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('new-token', $content['data']['authorization']['token']);
    }

    #[Test]
    public function it_handles_invalid_refresh_token()
    {
        // Mock RateLimiter
        RateLimiter::shouldReceive('tooManyAttempts')->andReturn(false);
        RateLimiter::shouldReceive('hit')->once();

        // Mock Validator
        Validator::shouldReceive('make')
            ->once()
            ->andReturn(Mockery::mock(['fails' => false]));

        // Dados da requisição
        $request = Request::create('/api/auth/refresh', 'POST', [
            'refresh_token' => 'invalid-token'
        ]);

        // Mock do serviço com exceção
        $this->mockAuthService->shouldReceive('refreshToken')
            ->once()
            ->andThrow(new InvalidRefreshTokenException('Token expirado', 'expired', 'invalid-token'));

        // Executar
        $response = $this->controller->refresh($request);

        // Verificar
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
    }
}
