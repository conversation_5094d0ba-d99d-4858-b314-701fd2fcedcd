<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Logging</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #2980b9;
            margin-top: 40px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        h3 {
            color: #3498db;
            margin-top: 25px;
        }

        .code-section {
            margin-bottom: 50px;
        }

        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', Courier, monospace;
            overflow-x: auto;
        }

        .example {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .example h4 {
            margin-top: 0;
            color: #2980b9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        table,
        th,
        td {
            border: 1px solid #ddd;
        }

        th {
            background-color: #f2f2f2;
            padding: 12px;
            text-align: left;
        }

        td {
            padding: 10px;
        }

        .best-practice {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }

        .bad-practice {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }

        .note {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
            padding: 10px;
        }

        footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }

        #sumario {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }

        #sumario ul {
            list-style-type: none;
            padding-left: 20px;
        }

        #sumario ul li {
            margin-bottom: 8px;
        }

        #sumario a {
            color: #2980b9;
            text-decoration: none;
        }

        #sumario a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <header>
        <h1 style="color: white; border-bottom: none;">Manual de Logging</h1>
        <p>Guia para implementação e padronização de logs na aplicação</p>
    </header>

    <section id="sumario">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#niveis">2. Níveis de Log</a></li>
            <li><a href="#estrutura">3. Estrutura de Logs</a>
                <ul>
                    <li><a href="#estrutura-basica">3.1. Estrutura Básica</a></li>
                    <li><a href="#contexto">3.2. Informações de Contexto</a></li>
                    <li><a href="#formato">3.3. Formato de Mensagens</a></li>
                </ul>
            </li>
            <li><a href="#configuracao">4. Configuração do Sistema de Logs</a>
                <ul>
                    <li><a href="#config-laravel">4.1. Configuração no Laravel</a></li>
                    <li><a href="#config-canais">4.2. Canais de Log</a></li>
                    <li><a href="#config-env">4.3. Configurações por Ambiente</a></li>
                </ul>
            </li>
            <li><a href="#implementacao">5. Implementação</a>
                <ul>
                    <li><a href="#impl-facade">5.1. Uso da Facade Log</a></li>
                    <li><a href="#impl-custom-channel">5.2. Canais Personalizados</a></li>
                    <li><a href="#impl-contextual">5.3. Logging Contextual</a></li>
                    <li><a href="#impl-excecoes">5.4. Logging de Exceções</a></li>
                </ul>
            </li>
            <li><a href="#monitoramento">6. Monitoramento e Alertas</a></li>
            <li><a href="#seguranca">7. Segurança e Privacidade</a></li>
            <li><a href="#performance">8. Considerações de Performance</a></li>
            <li><a href="#boas-praticas">9. Boas Práticas</a></li>
            <li><a href="#exemplos">10. Exemplos Práticos</a></li>
        </ul>
    </section>

    <section id="introducao">
        <h2>1. Introdução</h2>
        <p>O sistema de logging é uma parte crucial de qualquer aplicação em produção, fornecendo visibilidade sobre o
            comportamento da aplicação, facilitando a detecção e resolução de problemas, e contribuindo para a segurança
            e auditoria do sistema.</p>

        <p>Este manual estabelece os padrões e práticas recomendadas para implementação de logs na nossa aplicação,
            garantindo que as informações registradas sejam consistentes, úteis e adequadamente estruturadas.</p>

        <div class="note">
            <p><strong>Nota:</strong> Este documento é complementar ao Manual de Arquitetura e ao Manual de
                Implementação. As diretrizes aqui apresentadas devem ser seguidas por todos os componentes da aplicação.
            </p>
        </div>

        <h3>Objetivos do Logging</h3>
        <ul>
            <li><strong>Diagnóstico:</strong> Identificar e resolver problemas e erros</li>
            <li><strong>Monitoramento:</strong> Acompanhar o comportamento da aplicação em tempo real</li>
            <li><strong>Auditoria:</strong> Manter um registro de ações importantes para fins de segurança e compliance
            </li>
            <li><strong>Análise:</strong> Possibilitar a análise de tendências e comportamentos para melhorias</li>
            <li><strong>Depuração:</strong> Auxiliar no processo de desenvolvimento e testes</li>
        </ul>
    </section>

    <section id="niveis">
        <h2>2. Níveis de Log</h2>
        <p>Utilizamos diferentes níveis de log para categorizar as mensagens de acordo com sua severidade e importância.
        </p>

        <table>
            <thead>
                <tr>
                    <th>Nível</th>
                    <th>Descrição</th>
                    <th>Uso</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>EMERGENCY</td>
                    <td>Sistema inutilizável</td>
                    <td>Falhas críticas que tornam a aplicação completamente inoperante (ex: banco de dados inacessível)
                    </td>
                </tr>
                <tr>
                    <td>ALERT</td>
                    <td>Ação imediata necessária</td>
                    <td>Problemas graves que requerem intervenção imediata (ex: memória da aplicação quase esgotada)
                    </td>
                </tr>
                <tr>
                    <td>CRITICAL</td>
                    <td>Condições críticas</td>
                    <td>Componentes críticos da aplicação com falhas severas (ex: falha em serviço essencial)</td>
                </tr>
                <tr>
                    <td>ERROR</td>
                    <td>Condições de erro</td>
                    <td>Erros de runtime que não requerem ação imediata, mas devem ser investigados (ex: exceções
                        tratadas)</td>
                </tr>
                <tr>
                    <td>WARNING</td>
                    <td>Condições de alerta</td>
                    <td>Situações excepcionais que não são erros, mas podem indicar problemas potenciais (ex: tentativa
                        de login inválida)</td>
                </tr>
                <tr>
                    <td>NOTICE</td>
                    <td>Eventos normais significativos</td>
                    <td>Eventos normais mas significativos (ex: operações administrativas, usuário criado)</td>
                </tr>
                <tr>
                    <td>INFO</td>
                    <td>Mensagens informativas</td>
                    <td>Eventos normais do fluxo de execução (ex: início e fim de processos importantes)</td>
                </tr>
                <tr>
                    <td>DEBUG</td>
                    <td>Informações detalhadas de debug</td>
                    <td>Informações detalhadas para depuração, normalmente não ativadas em produção</td>
                </tr>
            </tbody>
        </table>

        <div class="best-practice">
            <h4>Escolhendo o Nível Adequado:</h4>
            <p>Selecione o nível de log com base no impacto do evento:</p>
            <ul>
                <li>Os níveis <code>EMERGENCY</code>, <code>ALERT</code> e <code>CRITICAL</code> devem ser reservados
                    para eventos que requerem ação imediata</li>
                <li>Use <code>ERROR</code> para eventos que representam falhas que precisam ser corrigidas, mas não
                    impedem a operação total da aplicação</li>
                <li>Use <code>WARNING</code> para situações potencialmente problemáticas que não são erros atualmente
                </li>
                <li>Use <code>NOTICE</code> e <code>INFO</code> para eventos operacionais importantes</li>
                <li>Use <code>DEBUG</code> apenas para informações detalhadas úteis durante o desenvolvimento</li>
            </ul>
        </div>
    </section>

    <section id="estrutura">
        <h2>3. Estrutura de Logs</h2>
        <p>Para maximizar a utilidade dos logs, todas as entradas de log devem seguir uma estrutura padronizada e
            consistente.</p>

        <section id="estrutura-basica">
            <h3>3.1. Estrutura Básica</h3>
            <p>Cada entrada de log deve conter as seguintes informações básicas:</p>

            <div class="code-block">
                {
                "timestamp": "2023-09-01T10:15:30.123456Z", // Data e hora no formato ISO 8601
                "level": "ERROR", // Nível do log
                "message": "Falha ao processar pagamento", // Mensagem descritiva
                "context": { // Informações de contexto
                "request_id": "req-123456789",
                "user_id": 1001,
                "order_id": 5002,
                "error": "Gateway de pagamento indisponível"
                }
                }
            </div>
        </section>

        <section id="contexto">
            <h3>3.2. Informações de Contexto</h3>
            <p>Adicione informações de contexto relevantes para facilitar a correlação de eventos e diagnóstico de
                problemas:</p>

            <ul>
                <li><strong>Identificadores:</strong> IDs de usuário, sessão, requisição, transação, etc.</li>
                <li><strong>Origem:</strong> Módulo, classe, método ou serviço que gerou o log</li>
                <li><strong>Dados Relevantes:</strong> Parâmetros, valores ou estados importantes para entender o
                    contexto</li>
                <li><strong>Informações de Rastreamento:</strong> Trace IDs para correlacionar logs em sistemas
                    distribuídos</li>
            </ul>

            <div class="example">
                <h4>Exemplo de Contexto em Diferentes Cenários</h4>
                <div class="code-block">
                    // Log de autenticação
                    Log::info('Tentativa de login bem-sucedida', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent()
                    ]);

                    // Log de transação
                    Log::info('Nova compra realizada', [
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'total_amount' => $order->total,
                    'payment_method' => $order->payment_method,
                    'items_count' => $order->items->count()
                    ]);

                    // Log de erro em API externa
                    Log::error('Falha ao conectar com serviço externo', [
                    'service' => 'payment_gateway',
                    'endpoint' => '/api/v1/transactions',
                    'method' => 'POST',
                    'status_code' => $response->status(),
                    'response' => $response->body(),
                    'request_id' => $requestId
                    ]);
                </div>
            </div>
        </section>

        <section id="formato">
            <h3>3.3. Formato de Mensagens</h3>
            <p>As mensagens de log devem seguir estas diretrizes de formatação:</p>

            <ul>
                <li><strong>Clareza:</strong> A mensagem deve ser clara e auto-explicativa</li>
                <li><strong>Concisão:</strong> Evite mensagens muito longas, coloque detalhes no contexto</li>
                <li><strong>Consistência:</strong> Mantenha um padrão de formatação entre mensagens similares</li>
                <li><strong>Acionabilidade:</strong> A mensagem deve indicar claramente o que aconteceu</li>
            </ul>

            <div class="best-practice">
                <h4>Padrões para Formatação de Mensagens</h4>
                <ul>
                    <li>Use frases completas com inicial maiúscula</li>
                    <li>Para eventos, prefira voz passiva: "Pedido criado" em vez de "Criou pedido"</li>
                    <li>Para erros, seja descritivo sobre o que falhou: "Falha ao processar pagamento" em vez de "Erro
                        de pagamento"</li>
                    <li>Não inclua dados variáveis na mensagem principal, coloque-os no contexto</li>
                </ul>
            </div>

            <div class="bad-practice">
                <h4>Exemplos de Mensagens Ruins</h4>
                <div class="code-block">
                    // Muito genérica
                    Log::error('Erro');

                    // Dados variáveis na mensagem (em vez de no contexto)
                    Log::info('Usuário 1001 fez login às 14:30');

                    // Inconsistente e difícil de filtrar
                    Log::warning('Problema!!!!! Não conseguiu fazer o pagamento do pedido 12345');
                </div>
            </div>

            <div class="best-practice">
                <h4>Exemplos de Mensagens Boas</h4>
                <div class="code-block">
                    // Clara e consistente, com dados no contexto
                    Log::error('Falha ao processar pagamento', [
                    'order_id' => 12345,
                    'payment_method' => 'credit_card',
                    'error_code' => 'GATEWAY_TIMEOUT'
                    ]);

                    // Mensagem acionável
                    Log::warning('Tentativa de acesso a recurso não autorizado', [
                    'user_id' => $user->id,
                    'resource' => 'admin_panel',
                    'ip_address' => $request->ip()
                    ]);

                    // Clara e concisa
                    Log::info('Sessão de usuário iniciada', [
                    'user_id' => $user->id,
                    'email' => $user->email
                    ]);
                </div>
            </div>
        </section>
    </section>

    <section id="configuracao">
        <h2>4. Configuração do Sistema de Logs</h2>
        <p>A configuração adequada do sistema de logs é essencial para garantir que as informações certas sejam
            registradas e armazenadas de forma apropriada.</p>

        <section id="config-laravel">
            <h3>4.1. Configuração no Laravel</h3>
            <p>O Laravel oferece um sistema de logging robusto e configurável através do arquivo
                <code>config/logging.php</code>.
            </p>

            <div class="code-block">
                // config/logging.php
                return [
                'default' => env('LOG_CHANNEL', 'stack'),

                'channels' => [
                'stack' => [
                'driver' => 'stack',
                'channels' => ['daily', 'slack'],
                'ignore_exceptions' => false,
                ],

                'single' => [
                'driver' => 'single',
                'path' => storage_path('logs/laravel.log'),
                'level' => env('LOG_LEVEL', 'debug'),
                ],

                'daily' => [
                'driver' => 'daily',
                'path' => storage_path('logs/laravel.log'),
                'level' => env('LOG_LEVEL', 'debug'),
                'days' => 14,
                ],

                'slack' => [
                'driver' => 'slack',
                'url' => env('LOG_SLACK_WEBHOOK_URL'),
                'username' => 'Laravel Log',
                'emoji' => ':boom:',
                'level' => env('LOG_SLACK_LEVEL', 'critical'),
                ],

                // Outros canais como 'papertrail', 'syslog', etc.
                ],
                ];
            </div>
        </section>

        <section id="config-canais">
            <h3>4.2. Canais de Log</h3>
            <p>Utilizamos diferentes canais de log para diferentes propósitos:</p>

            <table>
                <thead>
                    <tr>
                        <th>Canal</th>
                        <th>Uso</th>
                        <th>Configuração</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>daily</td>
                        <td>Logs gerais da aplicação, rotacionados diariamente</td>
                        <td>Retenção de 14 dias, nível debug em desenvolvimento, info em produção</td>
                    </tr>
                    <tr>
                        <td>slack</td>
                        <td>Alertas para erros críticos</td>
                        <td>Apenas níveis critical e acima são enviados para o Slack</td>
                    </tr>
                    <tr>
                        <td>audit</td>
                        <td>Log específico para eventos de auditoria</td>
                        <td>Canal separado para facilitar consulta e retenção prolongada</td>
                    </tr>
                    <tr>
                        <td>transactions</td>
                        <td>Logs específicos de transações financeiras</td>
                        <td>Canal separado com retenção estendida para rastreabilidade</td>
                    </tr>
                    <tr>
                        <td>security</td>
                        <td>Eventos relacionados à segurança</td>
                        <td>Canal com alertas configurados e retenção prolongada</td>
                    </tr>
                </tbody>
            </table>

            <div class="code-block">
                // Exemplo de configuração para canais personalizados
                'audit' => [
                'driver' => 'daily',
                'path' => storage_path('logs/audit.log'),
                'level' => 'info',
                'days' => 365,
                ],

                'transactions' => [
                'driver' => 'daily',
                'path' => storage_path('logs/transactions.log'),
                'level' => 'info',
                'days' => 90,
                ],

                'security' => [
                'driver' => 'stack',
                'channels' => ['security_file', 'slack'],
                'ignore_exceptions' => false,
                ],

                'security_file' => [
                'driver' => 'daily',
                'path' => storage_path('logs/security.log'),
                'level' => 'debug',
                'days' => 90,
                ],
            </div>
        </section>

        <section id="config-env">
            <h3>4.3. Configurações por Ambiente</h3>
            <p>As configurações de log devem ser ajustadas de acordo com o ambiente:</p>

            <table>
                <thead>
                    <tr>
                        <th>Ambiente</th>
                        <th>Nível de Log</th>
                        <th>Canais</th>
                        <th>Considerações</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>local</td>
                        <td>debug</td>
                        <td>single ou stderr</td>
                        <td>Máxima verbosidade para facilitar desenvolvimento</td>
                    </tr>
                    <tr>
                        <td>testing</td>
                        <td>debug</td>
                        <td>null ou stderr</td>
                        <td>Logs desabilitados por padrão ou redirecionados para stderr</td>
                    </tr>
                    <tr>
                        <td>staging</td>
                        <td>debug ou info</td>
                        <td>daily, logstash</td>
                        <td>Configuração similar à produção, mas com mais verbosidade para testes</td>
                    </tr>
                    <tr>
                        <td>production</td>
                        <td>info</td>
                        <td>stack (daily + vendor/alerts)</td>
                        <td>Foco em performance, apenas logs relevantes, integração com sistema de monitoramento</td>
                    </tr>
                </tbody>
            </table>

            <div class="note">
                <p>As configurações específicas para cada ambiente são definidas através de variáveis de ambiente no
                    arquivo <code>.env</code> e valores padrão adequados no arquivo <code>config/logging.php</code>.</p>
            </div>
        </section>
    </section>

    <section id="implementacao">
        <h2>5. Implementação</h2>
        <p>A implementação adequada do sistema de logs garante que todas as partes da aplicação registrem informações de
            maneira consistente.</p>

        <section id="impl-facade">
            <h3>5.1. Uso da Facade Log</h3>
            <p>A forma mais comum de registrar logs no Laravel é através da facade <code>Log</code>:</p>

            <div class="code-block">
                use Illuminate\Support\Facades\Log;

                // Logs básicos
                Log::emergency('Sistema completamente indisponível');
                Log::alert('Banco de dados está quase sem espaço livre');
                Log::critical('Serviço de pagamento não está respondendo');
                Log::error('Falha ao processar pedido');
                Log::warning('Usuário com permissões insuficientes tentou acessar recurso');
                Log::notice('Usuário alterou configurações importantes');
                Log::info('Pedido #12345 foi finalizado');
                Log::debug('Variáveis de estado: ' . json_encode($debugData));

                // Log com informações de contexto
                Log::info('Novo usuário registrado', [
                'id' => $user->id,
                'email' => $user->email,
                'registration_method' => 'email',
                ]);

                // Especificando o canal
                Log::channel('audit')->info('Administrador alterou permissões de usuário', [
                'admin_id' => $admin->id,
                'user_id' => $user->id,
                'old_permissions' => $oldPermissions,
                'new_permissions' => $newPermissions
                ]);

                // Usando stack de canais
                Log::stack(['daily', 'slack'])->critical('Tentativa de invasão detectada', [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'endpoint' => $request->fullUrl(),
                'payload' => $request->all()
                ]);
            </div>
        </section>

        <section id="impl-custom-channel">
            <h3>5.2. Canais Personalizados</h3>
            <p>Para casos específicos, podemos criar canais de log personalizados:</p>

            <div class="code-block">
                // Em um Service Provider
                public function boot()
                {
                // Registrar um canal customizado
                $this->app->make('log')->extend('cloudwatch', function ($app, array $config) {
                return new \Monolog\Logger('cloudwatch', [
                new \App\Logging\CloudWatchHandler($config),
                ]);
                });
                }

                // Uso do canal personalizado em config/logging.php
                'channels' => [
                // ...
                'cloudwatch' => [
                'driver' => 'cloudwatch',
                'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
                'group' => env('CLOUDWATCH_LOG_GROUP', 'laravel'),
                'stream' => env('CLOUDWATCH_LOG_STREAM', 'application'),
                'retention' => env('CLOUDWATCH_LOG_RETENTION', 14),
                'level' => env('LOG_LEVEL', 'debug'),
                ],
                ]
            </div>
        </section>

        <section id="impl-contextual">
            <h3>5.3. Logging Contextual</h3>
            <p>Para enriquecer logs com informações de contexto consistentes, utilize o método <code>withContext</code>:
            </p>

            <div class="code-block">
                // Adicionar contexto que será incluído em todos os logs subsequentes
                Log::withContext([
                'request_id' => $requestId,
                'user_id' => auth()->id(),
                'session_id' => session()->getId()
                ]);

                // Em middleware de request:
                public function handle($request, Closure $next)
                {
                // Gerar um ID único para a requisição
                $requestId = (string) Str::uuid();

                // Adicionar ao header da resposta
                $request->headers->set('X-Request-ID', $requestId);

                // Adicionar ao contexto de log
                Log::withContext([
                'request_id' => $requestId,
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'user_agent' => $request->userAgent()
                ]);

                return $next($request);
                }
            </div>
        </section>

        <section id="impl-excecoes">
            <h3>5.4. Logging de Exceções</h3>
            <p>Para registrar exceções de forma adequada:</p>

            <div class="code-block">
                try {
                // Código que pode gerar exceções
                } catch (\Exception $e) {
                // Log da exceção com contexto
                Log::error('Falha ao processar pedido', [
                'order_id' => $order->id,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
                ]);

                // Ou usando o helper report()
                report($e);
                }

                // Em Exception Handler para formatação padronizada
                public function report(Throwable $exception)
                {
                if ($this->shouldntReport($exception)) {
                return;
                }

                $context = [
                'exception_class' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine()
                ];

                // Adicionar contexto específico para certos tipos de exceção
                if ($exception instanceof HttpException) {
                $context['status_code'] = $exception->getStatusCode();
                }

                if ($exception instanceof ValidationException) {
                $context['validation_errors'] = $exception->validator->errors()->toArray();
                }

                // Determinar o nível de log adequado baseado no tipo de exceção
                $level = 'error';

                if ($exception instanceof AuthenticationException) {
                $level = 'warning';
                $context['user_ip'] = request()->ip();
                } elseif ($exception instanceof HttpException && $exception->getStatusCode() === 404) {
                $level = 'notice';
                $context['requested_url'] = request()->fullUrl();
                } elseif ($exception instanceof \App\Exceptions\CriticalException) {
                $level = 'critical';
                }

                // Registrar o log com o nível e contexto apropriados
                Log::channel('daily')->log($level, "Exceção: {$exception->getMessage()}", $context);
                }
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Logging de Exceções</h4>
                <ul>
                    <li>Sempre inclua o tipo da exceção, mensagem e stack trace</li>
                    <li>Adicione informações contextuais que ajudem a identificar o problema</li>
                    <li>Use níveis de log adequados de acordo com a gravidade da exceção</li>
                    <li>Utilize mecanismos centralizados como o Exception Handler para garantir consistência</li>
                    <li>Tome cuidado para não registrar dados sensíveis no stack trace</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="monitoramento">
        <h2>6. Monitoramento e Alertas</h2>
        <p>Um sistema de logs eficaz não está completo sem um mecanismo de monitoramento e alertas que permita
            identificar e responder rapidamente a problemas.</p>

        <h3>6.1. Ferramentas de Monitoramento</h3>
        <p>Integramos nosso sistema de logs com as seguintes ferramentas de monitoramento:</p>

        <table>
            <thead>
                <tr>
                    <th>Ferramenta</th>
                    <th>Uso</th>
                    <th>Integração</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Elasticsearch + Kibana</td>
                    <td>Análise centralizada de logs</td>
                    <td>Configurado como canal de log via Monolog Elasticsearch Handler</td>
                </tr>
                <tr>
                    <td>New Relic</td>
                    <td>Monitoramento de performance e erros</td>
                    <td>Via driver de log personalizado e middleware de rastreamento</td>
                </tr>
                <tr>
                    <td>Sentry</td>
                    <td>Rastreamento de erros e exceções</td>
                    <td>Integrado via Sentry Laravel SDK</td>
                </tr>
                <tr>
                    <td>Slack</td>
                    <td>Notificações de alertas críticos</td>
                    <td>Canal de log configurado para enviar apenas logs de alta severidade</td>
                </tr>
            </tbody>
        </table>

        <h3>6.2. Configuração de Alertas</h3>
        <p>Configure alertas baseados em padrões ou frequência de logs para notificar a equipe sobre problemas:</p>

        <div class="code-block">
            // Exemplo de configuração para alertas no Kibana
            {
            "trigger": {
            "schedule": { "interval": "5m" }
            },
            "input": {
            "search": {
            "request": {
            "body": {
            "size": 0,
            "query": {
            "bool": {
            "must": [
            { "match": { "level": "error" } },
            { "match": { "application": "nome-da-aplicacao" } }
            ],
            "filter": {
            "range": {
            "@timestamp": {
            "gte": "now-5m",
            "lte": "now"
            }
            }
            }
            }
            }
            }
            }
            }
            },
            "condition": {
            "compare": {
            "ctx.payload.hits.total": {
            "gt": 10
            }
            }
            },
            "actions": {
            "slack": {
            "message": {
            "from": "Monitoramento de Logs",
            "to": ["#alertas-producao"],
            "text": "Mais de 10 erros detectados nos últimos 5 minutos"
            }
            }
            }
            }
        </div>

        <h3>6.3. Métricas de Monitoramento</h3>
        <p>Além dos logs de texto, utilize métricas para monitorar a saúde da aplicação:</p>

        <div class="code-block">
            // Registrar métricas via StatsD
            $statsd = $this->app->make(StatsdClient::class);

            // Registrar contador de erros
            $statsd->increment('errors.count', 1, [
            'service' => 'payment_processing',
            'error_type' => 'gateway_timeout'
            ]);

            // Medir tempo de execução
            $startTime = microtime(true);
            // ... execução de operação ...
            $executionTime = microtime(true) - $startTime;
            $statsd->timing('operation.duration', $executionTime * 1000, [
            'operation' => 'import_products'
            ]);
        </div>
    </section>

    <section id="seguranca">
        <h2>7. Segurança e Privacidade</h2>
        <p>A segurança e a privacidade dos dados nos logs são tão importantes quanto as informações registradas.</p>

        <h3>7.1. Dados Sensíveis</h3>
        <p>Nunca registre informações sensíveis em logs, incluindo:</p>
        <ul>
            <li>Senhas (mesmo que em hash)</li>
            <li>Tokens de autenticação</li>
            <li>Informações pessoais identificáveis completas (CPF, cartão de crédito, etc.)</li>
            <li>Dados médicos ou financeiros detalhados</li>
            <li>Segredos de API ou chaves privadas</li>
        </ul>

        <div class="best-practice">
            <h4>Mascaramento de Dados Sensíveis</h4>
            <div class="code-block">
                // Implementando um processador de logs para mascarar dados sensíveis
                use Monolog\Processor\ProcessorInterface;

                class SensitiveDataMaskProcessor implements ProcessorInterface
                {
                protected $fieldsToMask = [
                'password', 'senha', 'credit_card', 'cartao', 'cpf', 'token', 'secret'
                ];

                protected $maskPattern = '***MASKED***';

                public function __invoke(array $record): array
                {
                $record['context'] = $this->maskData($record['context']);
                return $record;
                }

                protected function maskData($data)
                {
                if (!is_array($data)) {
                return $data;
                }

                foreach ($data as $key => $value) {
                if (is_array($value)) {
                $data[$key] = $this->maskData($value);
                continue;
                }

                if (is_string($value) && $this->shouldMaskField($key)) {
                $data[$key] = $this->maskPattern;
                }
                }

                return $data;
                }

                protected function shouldMaskField($fieldName)
                {
                $fieldName = strtolower($fieldName);

                foreach ($this->fieldsToMask as $sensitive) {
                if (strpos($fieldName, $sensitive) !== false) {
                return true;
                }
                }

                return false;
                }
                }

                // Registrando o processador no canal de log
                $this->app->make('log')->channel('daily')->pushProcessor(new SensitiveDataMaskProcessor());
            </div>
        </div>

        <h3>7.2. Armazenamento Seguro</h3>
        <p>Os arquivos de log devem ser armazenados com segurança:</p>
        <ul>
            <li>Aplique permissões de sistema adequadas (apenas usuários autorizados podem ler)</li>
            <li>Criptografe logs com informações sensíveis</li>
            <li>Utilize retenção apropriada e eliminação segura de logs antigos</li>
            <li>Backup regular dos logs em locais seguros</li>
        </ul>

        <h3>7.3. Compliance e Auditoria</h3>
        <p>Os logs de auditoria devem atender aos requisitos regulatórios:</p>
        <ul>
            <li>LGPD: Assegure que dados pessoais são tratados de acordo com a lei</li>
            <li>PCI DSS: Para sistemas que processam dados de cartões de pagamento</li>
            <li>Outros frameworks regulatórios específicos ao domínio da aplicação</li>
        </ul>

        <div class="note">
            <p>Considere a implementação de logs de auditoria separados, imutáveis e com trilha de verificação para
                eventos críticos como alterações de configuração, acesso a dados sensíveis e outras ações
                administrativas.</p>
        </div>
    </section>

    <section id="performance">
        <h2>8. Considerações de Performance</h2>
        <p>O sistema de logs não deve impactar significativamente a performance da aplicação:</p>

        <h3>8.1. Impacto na Performance</h3>
        <p>Operações de log podem afetar a performance da aplicação por diversas razões:</p>
        <ul>
            <li>I/O de disco ao gravar logs em arquivos</li>
            <li>Operações de rede ao enviar logs para serviços remotos</li>
            <li>Serialização e formatação de dados de contexto complexos</li>
            <li>Volume excessivo de logs gerados</li>
        </ul>

        <h3>8.2. Técnicas de Otimização</h3>
        <p>Técnicas para minimizar o impacto na performance:</p>

        <div class="code-block">
            // Uso de canais assíncronos
            'channels' => [
            'async' => [
            'driver' => 'monolog',
            'handler' => \Monolog\Handler\AsyncHandler::class,
            'handler_with' => [
            'handler' => new \Monolog\Handler\StreamHandler(storage_path('logs/async.log')),
            ],
            'level' => 'debug',
            ],
            ]

            // Uso de bufferização de logs
            'buffered' => [
            'driver' => 'monolog',
            'handler' => \Monolog\Handler\BufferHandler::class,
            'handler_with' => [
            'handler' => new \Monolog\Handler\RotatingFileHandler(storage_path('logs/buffered.log')),
            'buffer_size' => 100, // Número de registros para acumular antes de despejar
            ],
            'level' => 'debug',
            ]
        </div>

        <h3>8.3. Logging Condicional</h3>
        <p>Otimize a performance usando logs condicionais:</p>

        <div class="code-block">
            // Log condicional apenas quando necessário
            if (config('app.env') === 'local' || config('app.debug')) {
            Log::debug('Detalhes de depuração', $largeDebugData);
            }

            // Verificação rápida de nível antes de processamentos pesados
            if (Log::getLogger()->isDebugEnabled()) {
            $expensiveData = $this->generateExpensiveDebugData();
            Log::debug('Dados de depuração detalhados', $expensiveData);
            }

            // Classe helper para lazy evaluation de logs
            class LazyLogger
            {
            public static function debug($message, callable $contextCallback = null)
            {
            if (Log::getLogger()->isHandling(LogLevel::DEBUG)) {
            $context = $contextCallback ? $contextCallback() : [];
            Log::debug($message, $context);
            }
            }

            // Métodos similares para outros níveis...
            }

            // Uso:
            LazyLogger::debug('Dados de diagnóstico', function() {
            // Este callback só será executado se o nível de debug estiver ativado
            return [
            'expensive_data' => $this->computeExpensiveMetrics(),
            'memory_usage' => memory_get_usage(true)
            ];
            });
        </div>

        <h3>8.4. Métricas de Uso de Logs</h3>
        <p>Monitore o próprio sistema de logs para identificar problemas de performance:</p>
        <ul>
            <li>Volume de logs gerados por período e por nível</li>
            <li>Tempo gasto em operações de registro de logs</li>
            <li>Consumo de recursos (CPU, memória, disco, rede) pelo sistema de logs</li>
            <li>Falhas no sistema de logs (erros de escrita, conectividade, etc.)</li>
        </ul>
    </section>

    <section id="boas-praticas">
        <h2>9. Boas Práticas</h2>
        <p>Resumo das melhores práticas para implementação e uso do sistema de logs:</p>

        <h3>9.1. Padrões Gerais</h3>
        <ul>
            <li>Use níveis de log apropriados para cada tipo de mensagem</li>
            <li>Inclua sempre informações de contexto relevantes</li>
            <li>Mantenha uma estrutura consistente em todos os logs</li>
            <li>Use identificadores únicos para correlacionar logs relacionados</li>
            <li>Registre eventos significativos em pontos estratégicos do código</li>
            <li>Configure alertas para condições críticas de log</li>
        </ul>

        <h3>9.2. O que Registrar</h3>
        <ul>
            <li><strong>Eventos de Sistema:</strong> Inicialização, desligamento, conexões de banco de dados</li>
            <li><strong>Eventos de Segurança:</strong> Login, logout, tentativas de acesso não autorizado</li>
            <li><strong>Operações de Negócio:</strong> Transações importantes, marcos em processamentos de workflow</li>
            <li><strong>Exceções e Erros:</strong> Falhas esperadas e inesperadas, com detalhes para diagnóstico</li>
            <li><strong>Performance:</strong> Operações lentas, gargalos identificados</li>
            <li><strong>Acesso a Dados:</strong> Operações de leitura/escrita em dados sensíveis</li>
        </ul>

        <h3>9.3. O que Evitar</h3>
        <ul>
            <li>Logs extremamente verbosos em produção</li>
            <li>Informações sensíveis ou pessoais</li>
            <li>Dados redundantes ou irrelevantes</li>
            <li>Mensagens genéricas sem informações de contexto</li>
            <li>Logs dentro de loops de alta frequência sem controle</li>
            <li>Dependências síncronas de serviços externos para logging em operações críticas</li>
        </ul>

        <div class="best-practice">
            <h4>Checklist de Qualidade de Logs</h4>
            <ul>
                <li>✅ Os logs possuem timestamp preciso e em formato padronizado</li>
                <li>✅ Contêm identificadores para correlacionamento (request_id, session_id, etc.)</li>
                <li>✅ Mensagens são claras e descrevem precisamente o evento</li>
                <li>✅ Incluem informações de contexto com dados relevantes</li>
                <li>✅ Utilizam o nível de log adequado para a severidade do evento</li>
                <li>✅ Não contêm dados sensíveis ou confidenciais</li>
                <li>✅ São facilmente filtráveis e pesquisáveis</li>
                <li>✅ Estão configurados com retenção apropriada</li>
                <li>✅ Podem ser correlacionados com outros logs de componentes relacionados</li>
            </ul>
        </div>
    </section>

    <section id="exemplos">
        <h2>10. Exemplos Práticos</h2>
        <p>Exemplos de implementação em situações comuns:</p>

        <h3>10.1. Logging em API Controllers</h3>
        <div class="code-block">
            namespace App\Http\Controllers;

            use App\Http\Resources\OrderResource;
            use App\Services\OrderService;
            use Illuminate\Http\Request;
            use Illuminate\Support\Facades\Log;

            class OrderController extends Controller
            {
            protected $orderService;

            public function __construct(OrderService $orderService)
            {
            $this->orderService = $orderService;
            }

            public function store(Request $request)
            {
            // Log de início da operação
            Log::info('Iniciando criação de pedido', [
            'user_id' => auth()->id(),
            'items_count' => count($request->items)
            ]);

            try {
            $validatedData = $request->validate([
            'items' => 'required|array|min:1',
            'shipping_address_id' => 'required|exists:addresses,id',
            'payment_method_id' => 'required|exists:payment_methods,id'
            ]);

            $order = $this->orderService->createOrder($validatedData);

            // Log de sucesso com dados relevantes
            Log::info('Pedido criado com sucesso', [
            'order_id' => $order->id,
            'total_value' => $order->total_value,
            'items_count' => $order->items->count()
            ]);

            return response()->json([
            'status' => 'success',
            'message' => 'Pedido criado com sucesso',
            'data' => new OrderResource($order)
            ], 201);

            } catch (\App\Exceptions\PaymentException $e) {
            // Log específico para erro de pagamento
            Log::error('Falha no processamento do pagamento', [
            'user_id' => auth()->id(),
            'exception' => get_class($e),
            'message' => $e->getMessage(),
            'payment_method_id' => $request->payment_method_id
            ]);

            return response()->json([
            'status' => 'error',
            'message' => 'Não foi possível processar o pagamento',
            'errors' => ['payment' => $e->getMessage()]
            ], 400);

            } catch (\Exception $e) {
            // Log para exceções genéricas
            Log::error('Erro ao criar pedido', [
            'user_id' => auth()->id(),
            'exception' => get_class($e),
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
            'status' => 'error',
            'message' => 'Ocorreu um erro ao criar o pedido'
            ], 500);
            }
            }
            }
        </div>

        <h3>10.2. Logging em Services</h3>
        <div class="code-block">
            namespace App\Services;

            use App\Models\Order;
            use App\Repositories\OrderRepository;
            use App\Services\PaymentService;
            use Illuminate\Support\Facades\Log;

            class OrderService
            {
            protected $orderRepository;
            protected $paymentService;

            public function __construct(
            OrderRepository $orderRepository,
            PaymentService $paymentService
            ) {
            $this->orderRepository = $orderRepository;
            $this->paymentService = $paymentService;
            }

            public function createOrder(array $data)
            {
            // Logging do início da operação no serviço
            $logContext = [
            'user_id' => auth()->id(),
            'items_count' => count($data['items'])
            ];

            Log::info('Iniciando criação de pedido no serviço', $logContext);

            // Criar o pedido no banco de dados
            Log::debug('Salvando dados do pedido no repositório', $logContext);
            $order = $this->orderRepository->create($data);
            $logContext['order_id'] = $order->id;

            // Processar pagamento
            Log::info('Processando pagamento do pedido', $logContext + [
            'payment_method' => $data['payment_method_id'],
            'order_total' => $order->calculateTotal()
            ]);

            $paymentResult = $this->paymentService->processPayment($order, $data['payment_method_id']);

            if ($paymentResult['status'] === 'success') {
            Log::info('Pagamento processado com sucesso', $logContext + [
            'transaction_id' => $paymentResult['transaction_id']
            ]);

            $order = $this->orderRepository->updateStatus($order->id, 'paid');
            } else {
            Log::warning('Falha no processamento do pagamento', $logContext + [
            'error_code' => $paymentResult['error_code'],
            'error_message' => $paymentResult['error_message']
            ]);

            $order = $this->orderRepository->updateStatus($order->id, 'payment_failed');

            throw new \App\Exceptions\PaymentException($paymentResult['error_message']);
            }

            // Logging da finalização bem-sucedida
            Log::info('Pedido criado e processado com sucesso', $logContext + [
            'status' => $order->status
            ]);

            return $order;
            }
            }
        </div>

        <h3>10.3. Logging em Jobs e Processos em Background</h3>
        <div class="code-block">
            namespace App\Jobs;

            use App\Models\Order;
            use App\Services\ShippingService;
            use Illuminate\Bus\Queueable;
            use Illuminate\Contracts\Queue\ShouldQueue;
            use Illuminate\Foundation\Bus\Dispatchable;
            use Illuminate\Queue\InteractsWithQueue;
            use Illuminate\Queue\SerializesModels;
            use Illuminate\Support\Facades\Log;

            class ProcessOrderShipment implements ShouldQueue
            {
            use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

            protected $order;

            public function __construct(Order $order)
            {
            $this->order = $order;
            }

            public function handle(ShippingService $shippingService)
            {
            $logContext = [
            'order_id' => $this->order->id,
            'job_id' => $this->job->getJobId(),
            'attempt' => $this->attempts()
            ];

            Log::info('Iniciando processamento de envio do pedido', $logContext);

            try {
            // Processar envio
            $shipmentResult = $shippingService->createShipment($this->order);

            // Log do resultado
            Log::info('Envio processado com sucesso', $logContext + [
            'tracking_code' => $shipmentResult['tracking_code'],
            'estimated_delivery' => $shipmentResult['estimated_delivery']
            ]);

            } catch (\Exception $e) {
            Log::error('Falha ao processar envio', $logContext + [
            'exception' => get_class($e),
            'message' => $e->getMessage()
            ]);

            // Se ainda houver tentativas restantes, liberar para retry
            if ($this->attempts() < $this->tries) {
                Log::info('Agendando nova tentativa de processamento de envio', $logContext + [
                'next_attempt' => now()->addMinutes(5 * $this->attempts())
                ]);

                // Lançar a exceção para o Laravel processar o retry
                throw $e;
                } else {
                Log::critical('Todas as tentativas de processamento de envio falharam', $logContext);
                }
                }
                }

                public function failed(\Throwable $exception)
                {
                Log::critical('Job de processamento de envio falhou definitivamente', [
                'order_id' => $this->order->id,
                'exception' => get_class($exception),
                'message' => $exception->getMessage()
                ]);

                // Notificar o time sobre a falha
                // ...
                }
                }
        </div>

        <h3>10.4. Logging em Middleware</h3>
        <div class="code-block">
            namespace App\Http\Middleware;

            use Closure;
            use Illuminate\Http\Request;
            use Illuminate\Support\Facades\Log;
            use Illuminate\Support\Str;

            class RequestLogging
            {
            public function handle(Request $request, Closure $next)
            {
            // Gerar ID único para a requisição
            $requestId = (string) Str::uuid();
            $request->headers->set('X-Request-ID', $requestId);

            // Preparar contexto básico
            $context = [
            'request_id' => $requestId,
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
            ];

            // Adicionar informações do usuário se autenticado
            if (auth()->check()) {
            $context['user_id'] = auth()->id();
            $context['user_email'] = auth()->user()->email;
            }

            // Log de início da requisição
            Log::info('Requisição HTTP recebida', $context);

            // Medir tempo de resposta
            $startTime = microtime(true);

            // Processar a requisição
            $response = $next($request);

            // Calcular tempo de processamento
            $duration = round((microtime(true) - $startTime) * 1000, 2);

            // Adicionar informações da resposta ao contexto
            $context['duration_ms'] = $duration;
            $context['status_code'] = $response->getStatusCode();

            // Se for erro 4xx ou 5xx, usar nível warning ou error
            if ($response->getStatusCode() >= 500) {
            Log::error('Erro interno do servidor na requisição', $context);
            } elseif ($response->getStatusCode() >= 400) {
            Log::warning('Erro de cliente na requisição', $context);
            } else {
            Log::info('Requisição HTTP completada', $context);
            }

            // Adicionar header com Request ID na resposta
            $response->headers->set('X-Request-ID', $requestId);

            return $response;
            }
            }
        </div>
    </section>

    <footer>
        <p>Manual de Logging - Versão 1.0</p>
        <p>Última atualização: <span id="current-date"></span></p>
        <script>
            document.getElementById('current-date').textContent = new Date().toLocaleDateString();
        </script>
    </footer>
</body>

</html>