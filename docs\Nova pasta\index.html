<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manuais Técnicos de Desenvolvimento</title>
    <link rel="stylesheet" href="css/manual.css">
</head>

<body>
    <div class="hero-section">
        <h1>Manuais Técnicos de Desenvolvimento</h1>
        <p>Biblioteca de manuais e guias para melhores práticas, padrões e técnicas para desenvolvimento de aplicações
            com Laravel 12 e Vue.js</p>
    </div>

    <div class="container">
        <div class="search-container">
            <input type="text" id="searchInput" placeholder="Buscar manuais..." onkeyup="filterManuals()">
        </div>

        <!-- BACKEND SECTION -->
        <h2 class="section-title">Backend (Laravel 12)</h2>
        <p>Manuais essenciais para o desenvolvimento do backend com Laravel 12</p>

        <div class="manual-grid" id="backendManuals">
            <!-- Manual de Arquitetura -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Arquitetura</h3>
                </div>
                <div class="manual-card-body">
                    <p>Princípios e padrões para construção de uma arquitetura robusta e escalável para APIs Laravel,
                        incluindo SOLID, camadas de aplicação e organização de código.</p>
                    <div class="tags">
                        <span class="tag">Arquitetura</span>
                        <span class="tag">SOLID</span>
                        <span class="tag">Design Patterns</span>
                        <span class="tag">Laravel</span>
                    </div>
                    <a href="ManualArquitetura.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Validação e Tratamento de Dados (Sugestão para próximo manual) -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Validação e Tratamento de Dados</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação de validações robustas com Form Requests, regras de validação customizadas,
                        tratamento de dados e sanitização de inputs.</p>
                    <div class="tags">
                        <span class="tag">Validação</span>
                        <span class="tag">Form Requests</span>
                        <span class="tag">Sanitização</span>
                        <span class="tag">Laravel</span>
                    </div>
                    <span class="btn">Em Desenvolvimento</span>
                </div>
            </div>

            <!-- Manual de Implementação -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Implementação</h3>
                </div>
                <div class="manual-card-body">
                    <p>Guia completo para implementação de APIs RESTful com Laravel, abordando boas práticas,
                        organização de código e padrões de desenvolvimento.</p>
                    <div class="tags">
                        <span class="tag">Implementação</span>
                        <span class="tag">REST</span>
                        <span class="tag">API</span>
                        <span class="tag">Laravel</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Segurança -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Segurança</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação de segurança em APIs Laravel, incluindo autenticação, autorização, proteção contra
                        vulnerabilidades OWASP e melhores práticas de segurança.</p>
                    <div class="tags">
                        <span class="tag">Segurança</span>
                        <span class="tag">Autenticação</span>
                        <span class="tag">OWASP</span>
                        <span class="tag">Laravel</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Testes -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Testes</h3>
                </div>
                <div class="manual-card-body">
                    <p>Estratégias e ferramentas para testes unitários, integração e ponta a ponta em APIs Laravel,
                        incluindo TDD, PHPUnit e ferramentas para garantir a qualidade.</p>
                    <div class="tags">
                        <span class="tag">Testes</span>
                        <span class="tag">PHPUnit</span>
                        <span class="tag">TDD</span>
                        <span class="tag">Quality Assurance</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Respostas Padronizadas -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Respostas Padrão</h3>
                </div>
                <div class="manual-card-body">
                    <p>Padrões de formatação de respostas para APIs RESTful, incluindo estrutura de JSON, códigos de
                        status HTTP, tratamento de erros e mensagens consistentes.</p>
                    <div class="tags">
                        <span class="tag">API</span>
                        <span class="tag">REST</span>
                        <span class="tag">JSON</span>
                        <span class="tag">Padronização</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Autenticação e Autorização -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Autenticação e Autorização</h3>
                </div>
                <div class="manual-card-body">
                    <p>Guia completo para implementação de autenticação segura com JWT, Sanctum, OAuth, bem como gestão
                        de permissões, roles e controle de acesso (ACL).</p>
                    <div class="tags">
                        <span class="tag">Autenticação</span>
                        <span class="tag">JWT</span>
                        <span class="tag">Sanctum</span>
                        <span class="tag">ACL</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Banco de Dados e Eloquent -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Banco de Dados e Eloquent</h3>
                </div>
                <div class="manual-card-body">
                    <p>Diretrizes para modelagem de dados, relacionamentos, migrações, seeds e uso eficiente do Eloquent
                        ORM no contexto do projeto.</p>
                    <div class="tags">
                        <span class="tag">Eloquent</span>
                        <span class="tag">Migrations</span>
                        <span class="tag">Relacionamentos</span>
                        <span class="tag">Query Builder</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Performance -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Performance</h3>
                </div>
                <div class="manual-card-body">
                    <p>Técnicas avançadas para otimização de performance em APIs Laravel, incluindo queries eficientes,
                        cache, filas e melhores práticas para escalabilidade.</p>
                    <div class="tags">
                        <span class="tag">Performance</span>
                        <span class="tag">Otimização</span>
                        <span class="tag">Laravel</span>
                        <span class="tag">Cache</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Logging -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Logging</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação de logging eficiente para monitoramento, debugging e auditoria em APIs Laravel,
                        incluindo ferramentas, níveis de log e boas práticas.</p>
                    <div class="tags">
                        <span class="tag">Logging</span>
                        <span class="tag">Monitoramento</span>
                        <span class="tag">Debug</span>
                        <span class="tag">Auditoria</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Swagger -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Swagger</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação e manutenção de documentação de API com OpenAPI/Swagger no Laravel, incluindo
                        automação, exemplos e melhores práticas.</p>
                    <div class="tags">
                        <span class="tag">Swagger</span>
                        <span class="tag">OpenAPI</span>
                        <span class="tag">Documentação</span>
                        <span class="tag">API</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>
        </div>

        <!-- FRONTEND SECTION -->
        <h2 class="section-title frontend">Frontend (Vue.js)</h2>
        <p>Manuais recomendados para o desenvolvimento frontend com Vue.js:</p>

        <div class="manual-grid" id="frontendManuals">
            <!-- Manual de Arquitetura Vue.js -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header frontend">
                    <h3>Manual de Arquitetura Vue.js</h3>
                </div>
                <div class="manual-card-body">
                    <p>Princípios e padrões para construção de aplicações frontend escaláveis com Vue.js, incluindo
                        organização de componentes, gerenciamento de estado e boas práticas.</p>
                    <div class="tags">
                        <span class="tag">Vue.js</span>
                        <span class="tag">Arquitetura</span>
                        <span class="tag">Frontend</span>
                        <span class="tag">Componentes</span>
                    </div>
                    <span class="btn btn-frontend">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Integração API Laravel-Vue -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header frontend">
                    <h3>Manual de Integração API Laravel-Vue</h3>
                </div>
                <div class="manual-card-body">
                    <p>Guia completo para integração eficiente entre APIs Laravel e aplicações Vue.js, incluindo
                        autenticação, gerenciamento de requisições e tratamento de respostas.</p>
                    <div class="tags">
                        <span class="tag">Vue.js</span>
                        <span class="tag">Laravel</span>
                        <span class="tag">API</span>
                        <span class="tag">Integração</span>
                    </div>
                    <span class="btn btn-frontend">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Componentes Vue.js -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header frontend">
                    <h3>Manual de Componentes Vue.js</h3>
                </div>
                <div class="manual-card-body">
                    <p>Padrões e melhores práticas para criação de componentes reutilizáveis, escaláveis e testáveis em
                        Vue.js, incluindo props, eventos e slots.</p>
                    <div class="tags">
                        <span class="tag">Vue.js</span>
                        <span class="tag">Componentes</span>
                        <span class="tag">Reutilização</span>
                        <span class="tag">Frontend</span>
                    </div>
                    <span class="btn btn-frontend">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Estilo e Design System -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header frontend">
                    <h3>Manual de Estilo e Design System</h3>
                </div>
                <div class="manual-card-body">
                    <p>Diretrizes para criação e implementação de um design system consistente, incluindo componentes
                        UI, tokens de design e padrões visuais.</p>
                    <div class="tags">
                        <span class="tag">Design System</span>
                        <span class="tag">UI/UX</span>
                        <span class="tag">CSS</span>
                        <span class="tag">Componentes</span>
                    </div>
                    <span class="btn btn-frontend">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Gerenciamento de Estado -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header frontend">
                    <h3>Manual de Gerenciamento de Estado</h3>
                </div>
                <div class="manual-card-body">
                    <p>Estratégias e padrões para gerenciamento eficiente de estado em aplicações Vue.js usando
                        Pinia/Vuex, incluindo organização de stores e gerenciamento de side-effects.</p>
                    <div class="tags">
                        <span class="tag">Vue.js</span>
                        <span class="tag">Pinia</span>
                        <span class="tag">Vuex</span>
                        <span class="tag">Estado</span>
                    </div>
                    <span class="btn btn-frontend">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Testes Vue.js -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header frontend">
                    <h3>Manual de Testes Vue.js</h3>
                </div>
                <div class="manual-card-body">
                    <p>Estratégias e ferramentas para testes unitários, de componentes e end-to-end em aplicações
                        Vue.js, incluindo Jest, Vue Test Utils e Cypress.</p>
                    <div class="tags">
                        <span class="tag">Vue.js</span>
                        <span class="tag">Testes</span>
                        <span class="tag">Jest</span>
                        <span class="tag">Cypress</span>
                    </div>
                    <span class="btn btn-frontend">Em Breve</span>
                </div>
            </div>
        </div>

        <!-- AUXILIAR SECTION -->
        <h2 class="section-title auxiliar">Manuais Auxiliares</h2>
        <p>Manuais complementares para boas práticas de desenvolvimento:</p>

        <div class="manual-grid" id="auxiliarManuals">
            <!-- Manual de Deployment -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header auxiliar">
                    <h3>Manual de Deployment</h3>
                </div>
                <div class="manual-card-body">
                    <p>Processos e ferramentas para automação de deployment seguro de APIs Laravel e aplicações Vue.js
                        em ambientes de
                        produção, homologação e desenvolvimento.</p>
                    <div class="tags">
                        <span class="tag">Deployment</span>
                        <span class="tag">DevOps</span>
                        <span class="tag">CI/CD</span>
                        <span class="tag">Automação</span>
                    </div>
                    <span class="btn btn-auxiliar">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Commits -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header auxiliar">
                    <h3>Manual de Boas Práticas para Commits Git</h3>
                </div>
                <div class="manual-card-body">
                    <p>Um guia completo sobre como criar commits efetivos que facilitam a colaboração, revisão de código
                        e manutenção de projetos.</p>
                    <div class="tags">
                        <span class="tag">Git</span>
                        <span class="tag">Controle de Versão</span>
                        <span class="tag">Boas Práticas</span>
                    </div>
                    <span class="btn btn-auxiliar">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Code Review -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header auxiliar">
                    <h3>Manual de Code Review</h3>
                </div>
                <div class="manual-card-body">
                    <p>Guia para conduzir e participar de revisões de código eficientes, incluindo checklists, boas
                        práticas e ferramentas para melhorar a qualidade do código.</p>
                    <div class="tags">
                        <span class="tag">Code Review</span>
                        <span class="tag">Qualidade</span>
                        <span class="tag">Colaboração</span>
                        <span class="tag">Pull Requests</span>
                    </div>
                    <span class="btn btn-auxiliar">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Documentação -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header auxiliar">
                    <h3>Manual de Documentação de Código</h3>
                </div>
                <div class="manual-card-body">
                    <p>Padrões e melhores práticas para documentação de código, incluindo PHPDoc, JSDoc, README e
                        documentação técnica para desenvolvedores.</p>
                    <div class="tags">
                        <span class="tag">Documentação</span>
                        <span class="tag">PHPDoc</span>
                        <span class="tag">JSDoc</span>
                        <span class="tag">Markdown</span>
                    </div>
                    <span class="btn btn-auxiliar">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Ambiente de Desenvolvimento -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header auxiliar">
                    <h3>Manual de Ambiente de Desenvolvimento</h3>
                </div>
                <div class="manual-card-body">
                    <p>Configuração padronizada do ambiente de desenvolvimento local, incluindo Docker, ferramentas,
                        extensões e configurações necessárias para o projeto.</p>
                    <div class="tags">
                        <span class="tag">Ambiente</span>
                        <span class="tag">Docker</span>
                        <span class="tag">Ferramentas</span>
                        <span class="tag">Configuração</span>
                    </div>
                    <span class="btn btn-auxiliar">Em Breve</span>
                </div>
            </div>

            <!-- Manual de CI/CD -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header auxiliar">
                    <h3>Manual de CI/CD</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação e configuração de pipelines de integração contínua e entrega contínua para projetos
                        Laravel e Vue.js, automatizando testes, build e deploy.</p>
                    <div class="tags">
                        <span class="tag">CI/CD</span>
                        <span class="tag">DevOps</span>
                        <span class="tag">GitHub Actions</span>
                        <span class="tag">Automação</span>
                    </div>
                    <span class="btn btn-auxiliar">Em Breve</span>
                </div>
            </div>
        </div>

        <!-- COMING SOON SECTION -->
        <h2 class="section-title coming-soon-section">Manuais Futuros</h2>
        <p>Estes manuais estão em desenvolvimento e estarão disponíveis em breve:</p>

        <div class="manual-grid" id="comingSoonManuals">
            <!-- Manual de Versionamento de APIs -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Versionamento de APIs</h3>
                </div>
                <div class="manual-card-body">
                    <p>Estratégias e implementação para versionamento eficiente de APIs em projetos Laravel, incluindo
                        gestão de compatibilidade, migrações e evolução de interfaces.</p>
                    <div class="tags">
                        <span class="tag">Versionamento</span>
                        <span class="tag">API</span>
                        <span class="tag">Compatibilidade</span>
                        <span class="tag">Evolução</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Escalabilidade -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Escalabilidade para APIs Laravel</h3>
                </div>
                <div class="manual-card-body">
                    <p>Estratégias e técnicas para escalar APIs Laravel de forma horizontal e vertical, incluindo
                        balanceamento de carga, estratégias de cache distribuído e otimização.</p>
                    <div class="tags">
                        <span class="tag">Escalabilidade</span>
                        <span class="tag">Alta Disponibilidade</span>
                        <span class="tag">Performance</span>
                        <span class="tag">Cloud</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Cache -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Estratégias de Cache</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação de estratégias avançadas de cache em diferentes níveis para APIs Laravel, incluindo
                        Redis, Memcached, cache HTTP e cache de queries.</p>
                    <div class="tags">
                        <span class="tag">Cache</span>
                        <span class="tag">Redis</span>
                        <span class="tag">Performance</span>
                        <span class="tag">Otimização</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>
        </div>

        <div class="suggest-section">
            <h2 class="section-title">Sugira um Manual</h2>
            <p>Tem uma ideia para um novo manual técnico? Envie sua sugestão para o time de desenvolvimento!</p>
            <a href="#" class="btn btn-outline">Enviar Sugestão</a>
        </div>
    </div>

    <footer class="manual-footer">
        <div class="footer-content">
            <p>Manuais Técnicos de Desenvolvimento © 2023</p>
            <p>Desenvolvido pela equipe de Engenharia de Software</p>
        </div>
    </footer>

    <script>
        // Função para filtrar os manuais com base na busca
        function filterManuals() {
            let input = document.getElementById('searchInput');
            let filter = input.value.toUpperCase();
            let backendCards = document.querySelectorAll('#backendManuals .manual-card');
            let frontendCards = document.querySelectorAll('#frontendManuals .manual-card');
            let auxiliarCards = document.querySelectorAll('#auxiliarManuals .manual-card');
            let comingSoonCards = document.querySelectorAll('#comingSoonManuals .manual-card');

            // Filtrar manuais de backend
            filterCards(backendCards, filter);

            // Filtrar manuais de frontend
            filterCards(frontendCards, filter);

            // Filtrar manuais auxiliares
            filterCards(auxiliarCards, filter);

            // Filtrar manuais futuros
            filterCards(comingSoonCards, filter);
        }

        function filterCards(cards, filter) {
            for (let i = 0; i < cards.length; i++) {
                let card = cards[i];
                let title = card.querySelector('h3').textContent;
                let description = card.querySelector('p').textContent;
                let tags = card.querySelectorAll('.tag');

                let found = title.toUpperCase().indexOf(filter) > -1 ||
                    description.toUpperCase().indexOf(filter) > -1;

                // Verificar também nas tags
                if (!found) {
                    for (let j = 0; j < tags.length; j++) {
                        if (tags[j].textContent.toUpperCase().indexOf(filter) > -1) {
                            found = true;
                            break;
                        }
                    }
                }

                card.style.display = found ? "" : "none";
            }
        }
    </script>
</body>

</html>
