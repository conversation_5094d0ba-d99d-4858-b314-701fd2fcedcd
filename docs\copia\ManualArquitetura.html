<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Arquitetura</title>
    <link rel="stylesheet" href="css/manual.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Arquitetura</h1>
        <p>Descrição da arquitetura do sistema e seus componentes</p>
        <div class="version">Laravel 12</div>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#visao-geral">Visão Geral</a></li>
            <li><a href="#camadas">Camadas</a></li>
            <li><a href="#componentes">Componentes</a></li>
            <li><a href="#fluxo-dados">Fluxo de Dados</a></li>
            <li><a href="#seguranca">Segurança</a></li>
            <li><a href="#integracoes">Integrações</a></li>
            <li><a href="#escalabilidade">Escalabilidade</a></li>
            <li><a href="#decisoes">Decisões</a></li>
            <li><a href="#laravel12">Laravel 12</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#visao-geral">2. Visão Geral da Arquitetura</a></li>
            <li><a href="#camadas">3. Arquitetura em Camadas</a></li>
            <li><a href="#componentes">4. Principais Componentes</a></li>
            <li><a href="#fluxo-dados">5. Fluxo de Dados</a></li>
            <li><a href="#seguranca">6. Segurança na Arquitetura</a></li>
            <li><a href="#integracoes">7. Integrações Externas</a></li>
            <li><a href="#escalabilidade">8. Escalabilidade e Performance</a></li>
            <li><a href="#decisoes">9. Decisões Arquiteturais</a></li>
            <li><a href="#laravel12">10. Novidades do Laravel 12</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <p class="intro-text">Este documento descreve a arquitetura do sistema, baseada no framework Laravel 12,
            detalhando os componentes,
            camadas, fluxos de dados e princípios arquiteturais adotados.</p>

        <p>A arquitetura foi projetada seguindo os princípios SOLID e padrões de design que promovem:</p>
        <ul>
            <li>Baixo acoplamento entre componentes</li>
            <li>Alta coesão dentro dos módulos</li>
            <li>Testabilidade e manutenibilidade</li>
            <li>Escalabilidade horizontal e vertical</li>
            <li>Segurança em múltiplas camadas</li>
        </ul>

        <div class="alerts-section">
            <h4>Atualização Laravel 12</h4>
            <p>O Laravel 12 introduziu mudanças significativas na estrutura do framework, incluindo um novo sistema de
                inicialização, tipagem estrita e melhorias no sistema de cache e filas. Este manual foi atualizado para
                refletir essas mudanças.</p>
        </div>
    </section>

    <section id="visao-geral" class="manual-section">
        <h2>2. Visão Geral da Arquitetura</h2>
        <p>O sistema segue uma arquitetura em camadas que separa claramente as responsabilidades entre diferentes
            componentes do software:</p>

        <div class="architecture-diagram">
            <div class="arch-layer">
                <h4>Camada de Apresentação</h4>
                <p>Controllers, Requests, Resources, Middlewares</p>
            </div>
            <div class="arch-layer">
                <h4>Camada de Aplicação</h4>
                <p>Services, DTOs, Validação, Transações</p>
            </div>
            <div class="arch-layer">
                <h4>Camada de Domínio</h4>
                <p>Models, Value Objects, Enums, Regras de Negócio</p>
            </div>
            <div class="arch-layer">
                <h4>Camada de Infraestrutura</h4>
                <p>Repositories, External Services, Cache, Queue, Storage</p>
            </div>
        </div>

        <p>A arquitetura do sistema está estruturada nas seguintes camadas principais:</p>
        <ul>
            <li><strong>Camada de Apresentação</strong>: Responsável pela interação com o usuário e formatação das
                respostas</li>
            <li><strong>Camada de Aplicação</strong>: Orquestra as operações do sistema e implementa a lógica de negócio
            </li>
            <li><strong>Camada de Domínio</strong>: Contém as regras de negócio e entidades do domínio</li>
            <li><strong>Camada de Infraestrutura</strong>: Lida com interações com sistemas externos, persistência e
                operações técnicas</li>
        </ul>
    </section>

    <section id="camadas" class="manual-section">
        <h2>3. Arquitetura em Camadas</h2>
        <p>Detalhamento de cada camada da arquitetura:</p>

        <div class="subsection">
            <h3>3.1. Camada de Apresentação</h3>
            <p>Esta camada é responsável pela interface com o usuário, seja através de endpoints API ou interfaces web.
            </p>
            <ul>
                <li><strong>Controllers</strong>: Recebem as requisições HTTP, delegam o processamento para a camada de
                    aplicação e retornam respostas apropriadas.</li>
                <li><strong>Requests</strong>: Validam e normalizam os dados de entrada.</li>
                <li><strong>Resources</strong>: Formatam os dados de saída em formato padronizado JSON.</li>
                <li><strong>Middlewares</strong>: Processam requisições antes que cheguem aos controllers (autenticação,
                    logs, etc).</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Http\Controllers;

use App\Http\Requests\UserRequest;
use App\Http\Resources\UserResource;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;

class UserController extends Controller
{
    protected UserService $service;

    public function __construct(UserService $service)
    {
        $this->service = $service;
    }

    public function store(UserRequest $request): JsonResponse
    {
        $user = $this->service->create($request->validated());
        
        return response()->json([
            'status' => 'success',
            'message' => 'Usuário criado com sucesso',
            'data' => new UserResource($user)
        ], 201);
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 reforça o uso de tipagem estrita em todos os métodos. Note o uso de tipos de retorno
                    explícitos (<code>JsonResponse</code>) e tipagem de propriedades
                    (<code>UserService $service</code>).</p>
            </div>
        </div>

        <div class="subsection">
            <h3>3.2. Camada de Aplicação (Services)</h3>
            <p>Esta camada contém a lógica de aplicação e orquestra as operações entre as diferentes partes do sistema.
            </p>
            <ul>
                <li><strong>Services</strong>: Implementam a lógica de negócio, orquestrando operações entre diferentes
                    repositórios.</li>
                <li><strong>DTOs</strong> (Data Transfer Objects): Transportam dados entre as camadas do sistema.</li>
                <li><strong>Validação</strong>: Implementam regras de validação complexas a nível de negócio.</li>
                <li><strong>Transações</strong>: Gerenciam transações que envolvem múltiplas operações.</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Services;

use App\Repositories\Contracts\UserRepositoryInterface;
use App\Exceptions\UserException;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UserService
{
    protected UserRepositoryInterface $repository;

    public function __construct(UserRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function create(array $data): User
    {
        return DB::transaction(function () use ($data) {
            // Verificar se email já existe
            if ($this->repository->findByEmail($data['email'])) {
                throw new UserException('Email já está em uso');
            }
            
            $user = $this->repository->create($data);
            
            // Lógica adicional, como enviar email, atribuir perfil, etc.
            
            return $user;
        });
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 introduziu uma API mais limpa para transações de banco de dados. O método
                    <code>DB::transaction()</code> agora retorna diretamente o resultado da closure. Note também o uso
                    de interfaces para injeção de dependência, facilitando testes e substituições.
                </p>
            </div>
        </div>

        <div class="subsection">
            <h3>3.3. Camada de Domínio</h3>
            <p>Esta camada contém as entidades de negócio e as regras que governam o domínio da aplicação.</p>
            <ul>
                <li><strong>Models</strong>: Representam as entidades de negócio no sistema.</li>
                <li><strong>Value Objects</strong>: Encapsulam valores com significado no domínio.</li>
                <li><strong>Enums</strong>: Representam conjuntos fixos de valores relacionados.</li>
                <li><strong>Regras de Negócio</strong>: Implementam as regras que são intrínsecas ao domínio.</li>
                <li><strong>Contratos (Interfaces)</strong>: Definem contratos para serviços e repositórios.</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Models;

use App\Enums\OrderStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use SoftDeletes;

    protected $fillable = ['customer_id', 'total', 'status'];

    protected $casts = [
        'total' => 'decimal:2',
        'created_at' => 'datetime',
        'status' => OrderStatus::class
    ];

    // Relacionamentos
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    // Métodos de negócio
    public function canBeCancelled(): bool
    {
        return $this->status === OrderStatus::PENDING || 
               $this->status === OrderStatus::PROCESSING;
    }

    public function cancel(): void
    {
        if (!$this->canBeCancelled()) {
            throw new \Exception('Pedido não pode ser cancelado');
        }

        $this->status = OrderStatus::CANCELLED;
        $this->save();
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 reforça o uso de tipos de retorno explícitos para relacionamentos
                    (<code>BelongsTo</code>, <code>HasMany</code>) e métodos de negócio (<code>bool</code>,
                    <code>void</code>). Também incentiva o uso de Enums do PHP 8.1+ para representar estados e valores
                    fixos.
                </p>
            </div>

            <div class="code-block">
                <pre><code>namespace App\Enums;

enum OrderStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case SHIPPED = 'shipped';
    case DELIVERED = 'delivered';
    case CANCELLED = 'cancelled';
    case REFUNDED = 'refunded';

    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pendente',
            self::PROCESSING => 'Em processamento',
            self::SHIPPED => 'Enviado',
            self::DELIVERED => 'Entregue',
            self::CANCELLED => 'Cancelado',
            self::REFUNDED => 'Reembolsado',
        };
    }
}</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>3.4. Camada de Infraestrutura</h3>
            <p>Esta camada fornece suporte técnico para as outras camadas, lidando com aspectos como persistência,
                integração com sistemas externos, etc.</p>
            <ul>
                <li><strong>Repositories</strong>: Implementam o acesso aos dados persistentes.</li>
                <li><strong>External Services</strong>: Integram com APIs e serviços externos.</li>
                <li><strong>Logging</strong>: Implementam mecanismos de registro de logs.</li>
                <li><strong>Cache</strong>: Gerenciam o cache do sistema.</li>
                <li><strong>Queue</strong>: Gerenciam filas de processamento assíncrono.</li>
                <li><strong>File Storage</strong>: Gerenciam operações de armazenamento de arquivos.</li>
                <li><strong>Jobs</strong>: Implementam tarefas agendadas e processamento em background.</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Repositories\Eloquent;

use App\Models\User;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Support\Facades\Cache;

class UserRepository implements UserRepositoryInterface
{
    protected User $model;

    public function __construct(User $model)
    {
        $this->model = $model;
    }

    public function findById(int $id): ?User
    {
        return Cache::remember("user:{$id}", 3600, function () use ($id) {
            return $this->model->find($id);
        });
    }

    public function create(array $data): User
    {
        return $this->model->create($data);
    }

    public function update(int $id, array $data): ?User
    {
        $entity = $this->findById($id);
        if (!$entity) {
            return null;
        }
        
        $entity->update($data);
        $this->clearCache($id);
        return $entity;
    }

    public function delete(int $id): bool
    {
        $this->clearCache($id);
        return (bool) $this->model->destroy($id);
    }

    public function findByEmail(string $email): ?User
    {
        return $this->model->where('email', $email)->first();
    }
    
    protected function clearCache(int $id): void
    {
        Cache::forget("user:{$id}");
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 reforça o uso de tipos de retorno explícitos e parâmetros tipados. O sistema de cache
                    também foi aprimorado com melhor suporte para tags e tempos de expiração mais precisos.</p>
            </div>

            <div class="code-block">
                <h4>Exemplo de External Service:</h4>
                <pre><code>namespace App\Services\External;

use App\DTOs\PaymentDTO;
use App\Exceptions\PaymentException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaymentGatewayService
{
    protected string $apiKey;
    protected string $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.payment.key');
        $this->baseUrl = config('services.payment.url');
    }

    public function processPayment(PaymentDTO $payment): array
    {
        try {
            $response = Http::withToken($this->apiKey)
                ->post("{$this->baseUrl}/v1/charges", $payment->toArray());

            if ($response->successful()) {
                return $this->parseSuccessResponse($response);
            }

            throw new PaymentException(
                $response->json('error.message') ?? 'Erro desconhecido no gateway de pagamento',
                $response->status()
            );
        } catch (\Exception $e) {
            Log::error('Erro ao processar pagamento', [
                'error' => $e->getMessage(),
                'payment' => $payment->toArray()
            ]);

            throw new PaymentException(
                'Falha ao processar pagamento: ' . $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    protected function parseSuccessResponse(Response $response): array
    {
        return [
            'transaction_id' => $response->json('id'),
            'status' => $response->json('status'),
            'amount' => $response->json('amount') / 100,
            'created_at' => date('Y-m-d H:i:s', $response->json('created'))
        ];
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 aprimorou o cliente HTTP com melhor suporte para tipagem e tratamento de respostas. Note
                    o uso da classe <code>Response</code> tipada e métodos auxiliares como <code>successful()</code> e
                    <code>json()</code>.
                </p>
            </div>
        </div>
    </section>

    <section id="componentes" class="manual-section">
        <h2>4. Principais Componentes</h2>

        <div class="subsection">
            <h3>4.1. Controllers</h3>
            <p>Os Controllers são responsáveis por receber as requisições HTTP, delegá-las para a camada de serviço
                apropriada e retornar as respostas. Seguem o princípio de manter controllers "magros", com mínima
                lógica.
            </p>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>No Laravel 12, os controllers são mais enxutos e focados em delegação. Eles agora usam tipagem
                    estrita e retornos explícitos.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>4.2. Services</h3>
            <p>Os Services encapsulam a lógica de negócio da aplicação. Eles são responsáveis por orquestrar as
                operações
                entre diferentes repositórios e componentes, garantindo a consistência dos dados e implementando regras
                de
                negócio.</p>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>Os services no Laravel 12 são mais fortemente tipados e utilizam a nova API de transações do DB para
                    operações que envolvem múltiplas etapas.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>4.3. Repositories</h3>
            <p>Os Repositories abstraem a camada de persistência, fornecendo métodos para acesso e manipulação de dados.
                Isso desacopla a lógica de negócio do acesso a dados, facilitando testes e manutenção.</p>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>Os repositories agora implementam interfaces bem definidas e utilizam tipagem estrita para melhorar a
                    segurança de tipos e facilitar testes.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>4.4. Models</h3>
            <p>Os Models representam as entidades do domínio e seus relacionamentos. No contexto do Laravel, eles também
                incluem métodos de consulta e definições de relacionamentos.</p>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>Os models no Laravel 12 utilizam tipagem estrita para relacionamentos e métodos. Também fazem uso
                    extensivo de Enums para representar estados e valores fixos.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>4.5. DTOs (Data Transfer Objects)</h3>
            <p>Os DTOs são objetos simples usados para transferir dados entre subsistemas da aplicação. Eles não contêm
                lógica de negócio, apenas dados e seus acessores.</p>

            <div class="code-block">
                <pre><code>namespace App\DTOs;

readonly class UserDTO
{
    public function __construct(
        public ?int $id = null,
        public string $name,
        public string $email,
        public array $roles = []
    ) {}

    public static function fromModel(User $user): self
    {
        return new self(
            $user->id,
            $user->name,
            $user->email,
            $user->roles->pluck('name')->toArray()
        );
    }
    
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'roles' => $this->roles
        ];
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 incentiva o uso de classes <code>readonly</code> do PHP 8.2+ e constructor property
                    promotion para DTOs, tornando-os mais concisos e imutáveis.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>4.6. Resources</h3>
            <p>As classes de Resource padronizam o formato de respostas da API, garantindo consistência em toda a
                aplicação.</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'roles' => RoleResource::collection($this->whenLoaded('roles')),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 reforça o uso de tipagem estrita em Resources. O método <code>toArray</code> agora
                    recebe explicitamente o objeto <code>Request</code> e deve retornar um <code>array</code>.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>4.7. Exceptions</h3>
            <p>As Exceptions personalizadas ajudam a identificar e tratar erros de forma específica para cada contexto
                da
                aplicação.</p>

            <div class="code-block">
                <pre><code>namespace App\Exceptions;

use Exception;

abstract class DomainException extends Exception
{
    protected array $errors = [];

    public function __construct(string $message = "", int $code = 0, ?Exception $previous = null, array $errors = [])
    {
        parent::__construct($message, $code, $previous);
        $this->errors = $errors;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}

class UserException extends DomainException {}
class OrderException extends DomainException {}
class PaymentException extends DomainException {}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 introduziu melhorias no sistema de tratamento de exceções, incluindo o método
                    <code>register()</code> no Handler para registrar handlers de exceção e o uso de closures tipadas.
                </p>
            </div>
        </div>
    </section>

    <section id="fluxo-dados" class="manual-section">
        <h2>5. Fluxo de Dados</h2>

        <p>O fluxo de dados na arquitetura segue um padrão consistente para todas as operações:</p>

        <div class="layers-diagram">
            <div class="layer">
                <h4>1. Request HTTP</h4>
                <p>Cliente envia requisição para um endpoint da API</p>
            </div>
            <div class="layer">
                <h4>2. Middleware</h4>
                <p>Autenticação, logging, validação de token, etc.</p>
            </div>
            <div class="layer">
                <h4>3. Controller</h4>
                <p>Recebe a requisição e delega para o service</p>
            </div>
            <div class="layer">
                <h4>4. Validation</h4>
                <p>Validação dos dados de entrada</p>
            </div>
            <div class="layer">
                <h4>5. Service</h4>
                <p>Implementa a lógica de negócio</p>
            </div>
            <div class="layer">
                <h4>6. Repository</h4>
                <p>Executa operações de persistência</p>
            </div>
            <div class="layer">
                <h4>7. Model</h4>
                <p>Representa as entidades do domínio</p>
            </div>
            <div class="layer">
                <h4>8. Response</h4>
                <p>Formatação da resposta e retorno ao cliente</p>
            </div>
        </div>

        <div class="subsection">
            <h3>5.1. Exemplo de Fluxo Completo</h3>

            <div class="example">
                <h4>Fluxo de criação de um usuário:</h4>
                <ol>
                    <li>O cliente envia uma requisição POST para <code>/api/users</code> com dados do usuário.</li>
                    <li>Os middlewares de autenticação e autorização verificam se o requisitante tem permissão.</li>
                    <li>O <code>UserController@store</code> recebe a requisição e a encaminha para validação.</li>
                    <li>O <code>UserRequest</code> valida os dados de entrada conforme as regras definidas.</li>
                    <li>O controller chama o método <code>create</code> do <code>UserService</code> com os dados
                        validados.</li>
                    <li>O service verifica regras de negócio adicionais (ex: verificar se o email já está em uso).</li>
                    <li>O service chama o método <code>create</code> do <code>UserRepository</code>.</li>
                    <li>O repository cria um novo registro no banco de dados.</li>
                    <li>O service pode executar ações adicionais (enviar email de boas-vindas, atribuir perfil padrão).
                    </li>
                    <li>O controller recebe o resultado e o formata usando <code>UserResource</code>.</li>
                    <li>A resposta HTTP é enviada ao cliente com o código 201 (Created) e os dados do usuário criado.
                    </li>
                </ol>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 mantém o mesmo fluxo de dados, mas com tipagem mais estrita e melhor tratamento de erros
                    em cada etapa.</p>
            </div>
        </div>
        <div class="subsection">
            <h3>5.2. Fluxos Assíncronos</h3>

            <p>Para operações que podem levar tempo ou que não precisam de retorno imediato, utilizamos processamento
                assíncrono:</p>

            <ol>
                <li>O controller recebe a requisição e a valida.</li>
                <li>O service dispara um job para execução em background.</li>
                <li>O job é colocado em uma fila para processamento posterior.</li>
                <li>O controller retorna imediatamente uma resposta de sucesso.</li>
                <li>O job é processado em background pelo worker de filas.</li>
                <li>O resultado do processamento pode ser armazenado ou notificado ao usuário posteriormente.</li>
            </ol>

            <div class="code-block">
                <pre><code>// Controller
public function sendBulkEmails(BulkEmailRequest $request): JsonResponse
{
    ProcessBulkEmailJob::dispatch($request->validated())
        ->onQueue('emails');

    return response()->json([
        'status' => 'success',
        'message' => 'Os emails foram enfileirados para envio'
    ], 202);
}

// Job
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\EmailService;

class ProcessBulkEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $data;
    public int $tries = 3;
    public int $backoff = 60;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function handle(EmailService $emailService): void
    {
        $emailService->sendBulkEmails(
            $this->data['recipients'],
            $this->data['subject'],
            $this->data['template'],
            $this->data['variables'] ?? []
        );
    }

    public function failed(\Throwable $exception): void
    {
        // Registrar falha
        logger()->error('Falha ao enviar emails em massa', [
            'error' => $exception->getMessage(),
            'recipients_count' => count($this->data['recipients'])
        ]);
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 introduziu melhorias no sistema de filas, incluindo melhor suporte para tipagem de
                    parâmetros no método <code>handle()</code> e propriedades de classe fortemente tipadas. O método
                    <code>failed()</code> agora recebe explicitamente um objeto <code>\Throwable</code>.
                </p>
            </div>
        </div>

        <section id="seguranca" class="manual-section">
            <h2>6. Segurança na Arquitetura</h2>

            <p>A segurança é implementada em múltiplas camadas da arquitetura:</p>

            <div class="subsection">
                <h3>6.1. Autenticação e Autorização</h3>
                <ul>
                    <li>Autenticação via Sanctum para APIs</li>
                    <li>Controle de acesso baseado em função (RBAC) através de políticas e gates</li>
                    <li>Middleware de autenticação que verifica tokens e sessões</li>
                    <li>Proteção CSRF para formulários web</li>
                </ul>

                <div class="code-block">
                    <pre><code>// routes/api.php
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    
    Route::apiResource('products', ProductController::class);
});

// app/Policies/ProductPolicy.php
namespace App\Policies;

use App\Models\Product;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProductPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Product $product): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return $user->hasPermission('products.create');
    }

    public function update(User $user, Product $product): bool
    {
        return $user->hasPermission('products.update');
    }

    public function delete(User $user, Product $product): bool
    {
        return $user->hasPermission('products.delete');
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 reforça o uso de tipagem estrita em políticas e gates. O Laravel Sanctum é agora o
                        método recomendado para autenticação de APIs, substituindo o JWT.</p>
                </div>
            </div>

            <div class="subsection">
                <h3>6.2. Proteção de Dados</h3>
                <ul>
                    <li>Criptografia de dados sensíveis em trânsito (HTTPS)</li>
                    <li>Criptografia de dados sensíveis em repouso</li>
                    <li>Hash seguro para senhas usando bcrypt</li>
                    <li>Rate limiting para prevenir ataques de força bruta</li>
                </ul>

                <div class="code-block">
                    <pre><code>// Middleware de taxa de requisições
Route::middleware(['throttle:api'])->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// Criptografia de atributo sensível
namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    protected function cpf(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? decrypt($value) : null,
            set: fn ($value) => $value ? encrypt($value) : null
        );
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 utiliza a API de Attribute para criptografia de atributos, substituindo os métodos
                        get/set tradicionais. O sistema de rate limiting também foi aprimorado com melhor suporte para
                        configuração dinâmica.</p>
                </div>
            </div>

            <div class="subsection">
                <h3>6.3. Validação e Sanitização</h3>
                <ul>
                    <li>Validação rigorosa de todos os dados de entrada</li>
                    <li>Sanitização de dados para prevenir XSS e injeção SQL</li>
                    <li>Validação em múltiplas camadas (frontend, controllers, services)</li>
                </ul>

                <div class="code-block">
                    <pre><code>namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class UserStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => [
                'required', 
                'confirmed',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()
            ],
        ];
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 introduziu novas regras de validação e aprimorou as existentes. A classe Password
                        oferece uma API fluente para definir requisitos de senha complexos.</p>
                </div>
            </div>

            <div class="subsection">
                <h3>6.4. Logging e Auditoria</h3>
                <ul>
                    <li>Logging de todas as ações sensíveis</li>
                    <li>Trilha de auditoria para operações críticas</li>
                    <li>Alertas para atividades suspeitas</li>
                </ul>

                <div class="code-block">
                    <pre><code>namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class AuditService
{
    public function logUserAction(User $user, string $action, array $data = []): void
    {
        Log::channel('audit')->info("User action: {$action}", [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'data' => $data
        ]);
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 aprimorou o sistema de logging com melhor suporte para canais personalizados e
                        formatação estruturada de logs.</p>
                </div>
            </div>
        </section>

        <section id="integracoes" class="manual-section">
            <h2>7. Integrações Externas</h2>

            <p>A arquitetura permite a integração com sistemas externos através de interfaces bem definidas:</p>

            <div class="subsection">
                <h3>7.1. APIs de Terceiros</h3>
                <p>Para integrar com APIs de terceiros, utilizamos services específicos na camada de infraestrutura:</p>

                <div class="code-block">
                    <pre><code>namespace App\Services\External;

use App\DTOs\PaymentDTO;
use App\Exceptions\PaymentException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaymentGatewayService
{
    protected string $apiKey;
    protected string $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.payment.key');
        $this->baseUrl = config('services.payment.url');
    }

    public function processPayment(PaymentDTO $payment): array
    {
        try {
            $response = Http::withToken($this->apiKey)
                ->withHeaders([
                    'X-Api-Version' => '2023-05-01',
                    'Accept' => 'application/json'
                ])
                ->post("{$this->baseUrl}/v1/charges", $payment->toArray());

            if ($response->successful()) {
                return $this->parseSuccessResponse($response);
            }

            throw new PaymentException(
                $response->json('error.message') ?? 'Erro desconhecido no gateway de pagamento',
                $response->status()
            );
        } catch (\Exception $e) {
            Log::error('Erro ao processar pagamento', [
                'error' => $e->getMessage(),
                'payment' => $payment->toArray()
            ]);

            throw new PaymentException(
                'Falha ao processar pagamento: ' . $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    protected function parseSuccessResponse(Response $response): array
    {
        return [
            'transaction_id' => $response->json('id'),
            'status' => $response->json('status'),
            'amount' => $response->json('amount') / 100,
            'created_at' => date('Y-m-d H:i:s', $response->json('created'))
        ];
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 aprimorou o cliente HTTP com melhor suporte para tipagem, tratamento de respostas e
                        métodos fluentes para configuração de requisições.</p>
                </div>
            </div>

            <div class="subsection">
                <h3>7.2. Webhooks</h3>
                <p>Para receber notificações de sistemas externos, implementamos endpoints de webhook:</p>

                <div class="code-block">
                    <pre><code>namespace App\Http\Controllers;

use App\Http\Requests\WebhookRequest;
use App\Services\WebhookService;
use Illuminate\Http\JsonResponse;

class WebhookController extends Controller
{
    protected WebhookService $webhookService;

    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    public function handlePaymentNotification(WebhookRequest $request): JsonResponse
    {
        $payload = $request->validated();
        $signature = $request->header('X-Signature');

        if (!$this->webhookService->validateSignature($payload, $signature)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Assinatura inválida'
            ], 401);
        }

        $this->webhookService->processPaymentNotification($payload);

        return response()->json([
            'status' => 'success',
            'message' => 'Notificação recebida com sucesso'
        ]);
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 reforça o uso de Form Requests para validação de webhooks e tipagem estrita para
                        retornos de métodos.</p>
                </div>
            </div>

            <div class="subsection">
                <h3>7.3. Filas e Mensageria</h3>
                <p>Para comunicação assíncrona com sistemas externos, utilizamos filas e sistemas de mensageria:</p>

                <ul>
                    <li>Redis ou Amazon SQS para filas</li>
                    <li>RabbitMQ ou Apache Kafka para mensageria</li>
                    <li>Implementação de padrões como Circuit Breaker para lidar com falhas de sistemas externos</li>
                </ul>

                <div class="code-block">
                    <pre><code>namespace App\Services;

use App\Jobs\SyncProductsJob;
use App\Jobs\NotifyExternalSystemJob;
use Illuminate\Support\Facades\Bus;

class IntegrationService
{
    public function syncWithExternalSystem(array $data): void
    {
        // Criar um batch de jobs para sincronização
        Bus::batch([
            new SyncProductsJob($data['products']),
            new NotifyExternalSystemJob($data['notification'])
        ])
        ->name('sync-external-system')
        ->allowFailures()
        ->dispatch();
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 aprimorou o sistema de filas com melhor suporte para batches, monitoramento e
                        tratamento de falhas.</p>
                </div>
            </div>
        </section>

        <section id="escalabilidade" class="manual-section">
            <h2>8. Escalabilidade e Performance</h2>

            <div class="subsection">
                <h3>8.1. Estratégias de Escalabilidade</h3>
                <ul>
                    <li><strong>Escalabilidade Horizontal</strong>: Adição de mais instâncias da aplicação para
                        distribuir carga</li>
                    <li><strong>Escalabilidade Vertical</strong>: Aumento de recursos de cada instância</li>
                    <li><strong>Microsserviços</strong>: Decomposição em serviços menores e especializados quando
                        necessário</li>
                    <li><strong>Distribuição Geográfica</strong>: CDN e replicação geográfica para melhorar latência
                    </li>
                </ul>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 foi otimizado para melhor performance e menor uso de memória, facilitando a
                        escalabilidade horizontal.</p>
                </div>
            </div>

            <div class="subsection">
                <h3>8.2. Cache</h3>
                <p>Implementamos múltiplas estratégias de cache para melhorar a performance:</p>

                <ul>
                    <li>Cache de dados frequentemente acessados em Redis</li>
                    <li>Cache de consultas complexas no nível do repositório</li>
                    <li>Cache HTTP para respostas de API</li>
                    <li>Cache de configuração e rotas em nível de aplicação</li>
                </ul>

                <div class="code-block">
                    <pre><code>namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class CachedProductService
{
    protected int $cacheTime = 3600; // 1 hora

    public function getFeaturedProducts(): Collection
    {
        return Cache::tags(['products', 'featured'])
            ->remember('featured_products', $this->cacheTime, function () {
                return Product::active()
                    ->featured()
                    ->with('category')
                    ->orderBy('featured_order')
                    ->get();
            });
    }

    public function invalidateProductCache(Product $product): void
    {
        Cache::tags(['products'])->flush();
        Cache::forget("product:{$product->id}");
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 aprimorou o sistema de cache com melhor suporte para tags e invalidação seletiva de
                        cache.</p>
                </div>
            </div>

            <div class="subsection">
                <h3>8.3. Otimização de Banco de Dados</h3>
                <ul>
                    <li>Índices apropriados para consultas frequentes</li>
                    <li>Consultas eficientes com eager loading para evitar o problema N+1</li>
                    <li>Paginação para conjuntos grandes de dados</li>
                    <li>Replicação de leitura/escrita para distribuir carga</li>
                </ul>

                <div class="code-block">
                    <pre><code>namespace App\Repositories\Eloquent;

use App\Models\Order;
use App\Repositories\Contracts\OrderRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;

class OrderRepository implements OrderRepositoryInterface
{
    public function getRecentOrders(int $perPage = 15): LengthAwarePaginator
    {
        return Order::query()
            ->with(['customer', 'items.product']) // Eager loading
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function searchOrders(array $filters): LengthAwarePaginator
    {
        $query = Order::query();

        // Aplicar filtros
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Eager loading para evitar N+1
        $query->with(['customer', 'items.product']);

        // Ordenação
        $sortField = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        // Paginação
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 introduziu melhorias no Query Builder e Eloquent para consultas mais eficientes e
                        melhor suporte para eager loading.</p>
                </div>
            </div>

            <div class="subsection">
                <h3>8.4. Processamento em Background</h3>
                <p>Operações pesadas são movidas para processamento em segundo plano:</p>

                <ul>
                    <li>Processamento de relatórios</li>
                    <li>Envio de emails em massa</li>
                    <li>Sincronização com sistemas externos</li>
                    <li>Processamento de uploads de arquivos grandes</li>
                </ul>

                <div class="code-block">
                    <pre><code>namespace App\Http\Controllers;

use App\Http\Requests\ReportRequest;
use App\Jobs\GenerateReportJob;
use Illuminate\Http\JsonResponse;

class ReportController extends Controller
{
    public function generate(ReportRequest $request): JsonResponse
    {
        $reportId = uuid_create();
        
        GenerateReportJob::dispatch(
            $reportId,
            $request->user()->id,
            $request->validated()
        )->onQueue('reports');
        
        return response()->json([
            'status' => 'success',
            'message' => 'Relatório está sendo gerado',
            'report_id' => $reportId
        ], 202);
    }
}</code></pre>
                </div>

                <div class="alerts-section">
                    <h4>Atualização Laravel 12</h4>
                    <p>O Laravel 12 introduziu melhorias no sistema de filas e jobs, incluindo melhor suporte para
                        monitoramento e tratamento de falhas.</p>
                </div>
            </div>
        </section>

        <section id="decisoes" class="manual-section">
            <h2>9. Decisões Arquiteturais</h2>

            <p>As principais decisões arquiteturais tomadas e suas justificativas:</p>

            <div class="subsection">
                <h3>9.1. Por que Laravel 12?</h3>
                <p>O Laravel 12 foi escolhido como framework base porque:</p>
                <ul>
                    <li>Oferece um ecossistema completo de ferramentas para desenvolvimento web</li>
                    <li>Alta produtividade com convenções claras e recursos prontos para uso</li>
                    <li>Suporte robusto para APIs com recursos como API resources e throttling</li>
                    <li>Facilidade de teste com ferramentas integradas</li>
                    <li>Comunidade ativa e documentação excelente</li>
                    <li>Novo sistema de inicialização mais eficiente e flexível</li>
                    <li>Melhor suporte para tipagem estrita e PHP 8.2+</li>
                </ul>
            </div>

            <div class="subsection">
                <h3>9.2. Por que Arquitetura em Camadas?</h3>
                <p>A arquitetura em camadas foi adotada para:</p>
                <ul>
                    <li>Separação clara de responsabilidades</li>
                    <li>Facilidade de teste unitário e de integração</li>
                    <li>Manutenibilidade a longo prazo</li>
                    <li>Possibilidade de evolução independente de cada camada</li>
                    <li>Reutilização de componentes</li>
                </ul>
            </div>

            <div class="subsection">
                <h3>9.3. Por que Repositories e Services?</h3>
                <p>O padrão Repository + Service foi escolhido para:</p>
                <ul>
                    <li>Abstrair o acesso a dados da lógica de negócio</li>
                    <li>Facilitar a substituição da fonte de dados (ex: mudar de MySQL para MongoDB)</li>
                    <li>Centralizar lógica de negócio em services para evitar duplicação</li>
                    <li>Melhorar testabilidade com possibilidade de mock de repositories</li>
                </ul>
            </div>

            <div class="key-points">
                <p><strong>Nota importante:</strong> Esta arquitetura é projetada para ser flexível. Nem todos os
                    componentes precisam ser implementados para todas as funcionalidades. Para casos simples, pode-se
                    omitir
                    camadas como DTOs ou services específicos, dependendo da complexidade da operação.</p>
            </div>
        </section>

        <section id="laravel12" class="manual-section">
            <h2>10. Novidades do Laravel 12</h2>

            <p>O Laravel 12 introduziu várias melhorias e novos recursos que impactam nossa arquitetura:</p>

            <div class="subsection">
                <h3>10.1. Novo Sistema de Inicialização</h3>
                <p>O Laravel 12 introduziu um novo sistema de inicialização (bootstrap) mais eficiente e flexível:</p>

                <div class="code-block">
                    <pre><code>// bootstrap/app.php
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure()
    ->withProviders()
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Configuração de middleware
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Configuração de exceções
    })
    ->create();</code></pre>
                </div>

                <p>Este novo sistema permite uma configuração mais clara e flexível da aplicação, com melhor separação
                    de responsabilidades.</p>
            </div>

            <div class="subsection">
                <h3>10.2. Tipagem Estrita</h3>
                <p>O Laravel 12 incentiva fortemente o uso de tipagem estrita em todo o código:</p>

                <div class="code-block">
                    <pre><code>// Antes (Laravel 11 e anteriores)
public function show($id)
{
    $product = Product::find($id);
    return view('products.show', compact('product'));
}

// Agora (Laravel 12)
public function show(int $id): View
{
    $product = Product::findOrFail($id);
    return view('products.show', compact('product'));
}</code></pre>
                </div>

                <p>A tipagem estrita melhora a segurança do código, facilita a detecção de erros em tempo de
                    desenvolvimento e melhora a documentação do código.</p>
            </div>

            <div class="subsection">
                <h3>10.3. Melhorias no Sistema de Cache</h3>
                <p>O Laravel 12 introduziu melhorias significativas no sistema de cache:</p>

                <div class="code-block">
                    <pre><code>// Novo suporte para tags de cache
Cache::tags(['products', 'featured'])->put('key', $value, $seconds);

// Novo método remember() com callback tipado
$value = Cache::remember('key', $seconds, fn (): array => [
    // Computação cara
]);

// Novo método sole() para obter um único item que corresponda a uma condição
$user = User::sole(['email' => '<EMAIL>']);</code></pre>
                </div>
            </div>

            <div class="subsection">
                <h3>10.4. Melhorias no Sistema de Filas</h3>
                <p>O Laravel 12 aprimorou o sistema de filas com novas funcionalidades:</p>

                <div class="code-block">
                    <pre><code>// Novo método dispatchSync() para executar jobs imediatamente
ProcessPayment::dispatchSync($order);

// Novo método dispatchAfterResponse() para executar após a resposta HTTP
SendOrderConfirmation::dispatchAfterResponse($order);

// Novo método batch() com dependências
Bus::batch([
    new ProcessPayment($order),
    new UpdateInventory($order),
    new SendOrderConfirmation($order),
])->then(function (Batch $batch) {
    // Todos os jobs foram processados com sucesso
})->catch(function (Batch $batch, Throwable $e) {
    // Um job falhou
})->dispatch();</code></pre>
                </div>
            </div>

            <div class="subsection">
                <h3>10.5. Novas Regras de Validação</h3>
                <p>O Laravel 12 introduziu novas regras de validação:</p>

                <div class="code-block">
                    <pre><code>// Nova regra 'missing' - verifica se um valor NÃO existe na tabela
'email' => 'required|email|missing:users,email'

// Nova regra 'decimal' com precisão e escala
'price' => 'required|decimal:2,2' // Exige exatamente 2 casas decimais

// Nova regra 'prohibits' - campos mutuamente exclusivos
'credit_card' => 'required_without:paypal|prohibits:paypal',
'paypal' => 'required_without:credit_card|prohibits:credit_card',

// Nova regra 'enum' para validar enums PHP
'status' => ['required', new Enum(OrderStatus::class)]</code></pre>
                </div>
            </div>

            <div class="subsection">
                <h3>10.6. Requisitos Atualizados</h3>
                <ul>
                    <li>PHP 8.2+ (recomendado PHP 8.3)</li>
                    <li>Node.js 20 LTS para compilação de assets</li>
                    <li>Composer 2.6+</li>
                </ul>

                <p>Estes requisitos atualizados permitem aproveitar os recursos mais recentes do PHP e melhorar a
                    performance da aplicação.</p>
            </div>

            <div class="subsection">
                <h3>10.7. Impacto na Arquitetura</h3>
                <p>As mudanças do Laravel 12 impactam nossa arquitetura das seguintes formas:</p>

                <ul>
                    <li>Maior uso de tipagem estrita em todas as camadas</li>
                    <li>Uso de Enums do PHP para representar estados e valores fixos</li>
                    <li>Melhor organização do código de inicialização da aplicação</li>
                    <li>Uso de classes readonly para DTOs e Value Objects</li>
                    <li>Melhor gerenciamento de cache com tags e invalidação seletiva</li>
                    <li>Processamento assíncrono mais robusto com o sistema de filas aprimorado</li>
                    <li>Validação mais expressiva com novas regras e API fluente</li>
                    <li>Melhor tratamento de exceções com closures tipadas</li>
                </ul>

                <p>Estas mudanças resultam em um código mais seguro, mais fácil de manter e com melhor performance.</p>
            </div>

            <div class="subsection">
                <h3>10.8. Migração de Aplicações Existentes</h3>
                <p>Para migrar aplicações existentes para o Laravel 12, siga estas etapas:</p>

                <ol>
                    <li>Atualize o PHP para 8.2+ (preferencialmente 8.3)</li>
                    <li>Atualize as dependências no composer.json</li>
                    <li>Adapte o bootstrap/app.php para o novo formato de inicialização</li>
                    <li>Adicione tipagem estrita a controllers, services e repositories</li>
                    <li>Converta estados e valores fixos para Enums do PHP</li>
                    <li>Atualize as classes de validação para usar as novas APIs</li>
                    <li>Refatore DTOs para usar classes readonly quando apropriado</li>
                    <li>Atualize o sistema de cache para usar tags quando necessário</li>
                    <li>Adapte jobs e listeners para usar tipagem estrita</li>
                    <li>Execute testes para garantir compatibilidade</li>
                </ol>

                <div class="alerts-section">
                    <h4>Importante</h4>
                    <p>O Laravel 12 introduziu várias breaking changes. Recomenda-se realizar a migração em etapas,
                        começando por um ambiente de desenvolvimento ou staging antes de aplicar as mudanças em
                        produção.</p>
                </div>
            </div>
        </section>

        <section id="conclusao" class="manual-section">
            <h2>11. Conclusão</h2>

            <p>A arquitetura apresentada neste manual foi projetada para aproveitar ao máximo os recursos do Laravel 12,
                seguindo as melhores práticas de desenvolvimento de software e padrões de design.</p>

            <p>Os principais benefícios desta arquitetura incluem:</p>

            <ul>
                <li>Separação clara de responsabilidades entre as camadas</li>
                <li>Alta testabilidade de todos os componentes</li>
                <li>Flexibilidade para evolução e manutenção</li>
                <li>Escalabilidade para lidar com crescimento do sistema</li>
                <li>Segurança em múltiplas camadas</li>
                <li>Aproveitamento dos recursos modernos do PHP 8.2+ e Laravel 12</li>
            </ul>

            <p>Ao seguir as diretrizes deste manual, as equipes de desenvolvimento podem construir aplicações robustas,
                seguras e de alta qualidade, aproveitando todo o potencial do Laravel 12.</p>

            <div class="key-points">
                <p><strong>Lembre-se:</strong> A arquitetura deve servir ao projeto, não o contrário. Adapte as
                    recomendações deste manual às necessidades específicas do seu projeto, mantendo o equilíbrio entre
                    complexidade e pragmatismo.</p>
            </div>
        </section>

        <footer class="manual-footer">
            <p>Manual de Arquitetura - Laravel 12</p>
            <p>Última atualização: <span id="current-date"></span></p>
            <script>
                document.getElementById('current-date').textContent = new Date().toLocaleDateString();
            </script>
        </footer>
</body>

</html>