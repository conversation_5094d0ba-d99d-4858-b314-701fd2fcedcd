<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Respostas Padronizadas - Laravel 12</title>
    <link rel="stylesheet" href="css/manual.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Respostas Padronizadas</h1>
        <p>Guia para formatação padronizada de respostas da API no Laravel 12</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#estrutura">Estrutura de Respostas</a></li>
            <li><a href="#codigos-http">Códigos HTTP</a></li>
            <li><a href="#tabela-referencia">Tabela de Referência</a></li>
            <li><a href="#implementacao">Implementação</a></li>
            <li><a href="#casos-uso">Casos de Uso</a></li>
            <li><a href="#logging">Logging</a></li>
            <li><a href="#boas-praticas">Boas Práticas</a></li>
            <li><a href="#boas-praticas-codigos">Boas Práticas de Códigos</a></li>
            <li><a href="#conclusao">Conclusão</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#estrutura">2. Estrutura de Respostas</a>
                <ul>
                    <li><a href="#estrutura-sucesso">2.1. Respostas de Sucesso</a></li>
                    <li><a href="#estrutura-erro">2.2. Respostas de Erro</a></li>
                </ul>
            </li>
            <li><a href="#codigos-http">3. Códigos HTTP</a>
                <ul>
                    <li><a href="#codigos-sucesso">3.1. Códigos de Sucesso (2xx)</a></li>
                    <li><a href="#codigos-redirecionamento">3.2. Códigos de Redirecionamento (3xx)</a></li>
                    <li><a href="#codigos-cliente">3.3. Códigos de Erro do Cliente (4xx)</a></li>
                    <li><a href="#codigos-servidor">3.4. Códigos de Erro do Servidor (5xx)</a></li>
                </ul>
            </li>
            <li><a href="#tabela-referencia">4. Tabela de Referência Rápida</a></li>
            <li><a href="#implementacao">5. Implementação</a>
                <ul>
                    <li><a href="#response-abstract">5.1. Classe ResponseAbstract</a></li>
                    <li><a href="#api-response">5.2. Classe ApiResponse</a></li>
                    <li><a href="#metodos-disponiveis">5.3. Métodos Disponíveis</a></li>
                </ul>
            </li>
            <li><a href="#casos-uso">6. Casos de Uso Comuns</a>
                <ul>
                    <li><a href="#caso-listagem">6.1. Listagem de Recursos</a></li>
                    <li><a href="#caso-detalhes">6.2. Detalhes de um Recurso</a></li>
                    <li><a href="#caso-criacao">6.3. Criação de Recurso</a></li>
                    <li><a href="#caso-atualizacao">6.4. Atualização de Recurso</a></li>
                    <li><a href="#caso-remocao">6.5. Remoção de Recurso</a></li>
                    <li><a href="#caso-validacao">6.6. Erros de Validação</a></li>
                    <li><a href="#caso-autenticacao">6.7. Erros de Autenticação</a></li>
                    <li><a href="#caso-batch">6.8. Operações em Lote</a></li>
                </ul>
            </li>
            <li><a href="#logging">7. Sistema de Logging</a>
                <ul>
                    <li><a href="#logging-config">7.1. Configuração de Logging</a></li>
                    <li><a href="#logging-context">7.2. Contexto de Log</a></li>
                    <li><a href="#logging-disable">7.3. Desativação de Logs</a></li>
                </ul>
            </li>
            <li><a href="#boas-praticas">8. Boas Práticas</a></li>
            <li><a href="#boas-praticas-codigos">9. Boas Práticas para Uso dos Códigos de Resposta</a></li>
            <li><a href="#conclusao">10. Conclusão</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <div class="intro-text">
            <p>Este manual define o padrão de formatação para todas as respostas HTTP geradas pela API do nosso sistema,
                desenvolvido em Laravel 12 com PHP 8.2. A padronização de respostas é essencial para garantir uma
                experiência consistente para os consumidores da API, facilitando a integração e o tratamento de erros.
            </p>

            <p>Nosso projeto implementa um sistema robusto de respostas através das classes
                <code>ResponseAbstract</code> e <code>ApiResponse</code>, que fornecem métodos específicos para cada
                tipo de resposta HTTP, minimizando erros humanos na escolha dos códigos de status e garantindo um
                formato consistente para todas as respostas.
            </p>
        </div>

        <div class="key-points">
            <h3>Principais Benefícios</h3>
            <ul>
                <li>Consistência em todas as respostas da API</li>
                <li>Minimização de erros humanos na escolha de códigos HTTP</li>
                <li>Formato padronizado para respostas de sucesso e erro</li>
                <li>Logging automático de todas as respostas</li>
                <li>Suporte para internacionalização de mensagens</li>
                <li>Aproveitamento dos recursos modernos do PHP 8.2 e Laravel 12</li>
            </ul>
        </div>

        <div class="alerts-section">
            <h4>Importante</h4>
            <p>Todos os desenvolvedores devem utilizar as classes <code>ResponseAbstract</code> ou
                <code>ApiResponse</code> para gerar respostas HTTP em endpoints da API. Não utilize diretamente os
                métodos <code>response()->json()</code> do Laravel para garantir a consistência das respostas.
            </p>
        </div>
    </section>

    <section id="estrutura" class="manual-section">
        <h2>2. Estrutura de Respostas</h2>
        <p>Todas as respostas da API seguem uma estrutura comum que facilita o consumo e a interpretação pelos clientes.
        </p>

        <div id="estrutura-sucesso" class="subsection">
            <h3>2.1. Respostas de Sucesso</h3>
            <p>As respostas de sucesso seguem a estrutura abaixo:</p>

            <div class="code-block">
                <h4>Formato de Resposta de Sucesso</h4>
                <pre>
{
    "status": "success",
    "message": "Mensagem descritiva do resultado da operação",
    "code": 200,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0",
    "data": {
        // Dados retornados pela API
    }
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de Resposta de Sucesso</h4>
                <pre>
{
    "status": "success",
    "message": "Produto recuperado com sucesso",
    "code": 200,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0",
    "request_id": "f7cdf52e-1c3d-4a7f-b9c1-d8e5a6c7b9a8",
    "data": {
        "id": 1,
        "name": "Smartphone XYZ",
        "price": 1299.99,
        "description": "Um smartphone incrível com recursos avançados",
        "category": {
            "id": 3,
            "name": "Eletrônicos"
        },
        "created_at": "2023-06-15T14:30:45.000000Z",
        "updated_at": "2023-06-15T14:30:45.000000Z"
    }
}
                </pre>
            </div>
        </div>

        <div id="estrutura-erro" class="subsection">
            <h3>2.2. Respostas de Erro</h3>
            <p>As respostas de erro seguem a estrutura abaixo:</p>

            <div class="code-block">
                <h4>Formato de Resposta de Erro</h4>
                <pre>
{
    "status": "error",
    "message": "Mensagem descritiva do erro",
    "code": 400,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0",
    "errors": {
        // Detalhes do erro (opcional)
    }
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de Resposta de Erro de Validação</h4>
                <pre>
{
    "status": "error",
    "message": "Erro de validação",
    "code": 422,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0",
    "request_id": "f7cdf52e-1c3d-4a7f-b9c1-d8e5a6c7b9a8",
    "errors": {
        "name": [
            "O campo nome é obrigatório"
        ],
        "price": [
            "O preço deve ser um valor numérico",
            "O preço deve ser maior que zero"
        ]
    }
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de Resposta de Erro de Recurso Não Encontrado</h4>
                <pre>
{
    "status": "error",
    "message": "Recurso não encontrado",
    "code": 404,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0",
    "request_id": "f7cdf52e-1c3d-4a7f-b9c1-d8e5a6c7b9a8"
}
                </pre>
            </div>
        </div>
    </section>

    <section id="codigos-http" class="manual-section">
        <h2>3. Códigos HTTP</h2>
        <p>Os códigos HTTP são utilizados para indicar o resultado da operação. Nossa implementação fornece métodos
            específicos para cada código HTTP relevante, minimizando erros humanos na escolha dos códigos.</p>

        <div id="codigos-sucesso" class="subsection">
            <h3>3.1. Códigos de Sucesso (2xx)</h3>
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>Método</th>
                            <th>Uso</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>200</td>
                            <td>OK</td>
                            <td><code>ok()</code></td>
                            <td>Requisição bem-sucedida. Usado para GET, PUT/PATCH ou POST que não cria um novo recurso.
                            </td>
                        </tr>
                        <tr>
                            <td>201</td>
                            <td>Created</td>
                            <td><code>created()</code></td>
                            <td>Recurso criado com sucesso. Geralmente usado após um POST que cria um novo recurso.</td>
                        </tr>
                        <tr>
                            <td>204</td>
                            <td>No Content</td>
                            <td><code>noContent()</code></td>
                            <td>Requisição bem-sucedida, mas sem conteúdo para retornar. Comum em operações DELETE.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="codigos-redirecionamento" class="subsection">
            <h3>3.2. Códigos de Redirecionamento (3xx)</h3>
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>Método</th>
                            <th>Uso</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>304</td>
                            <td>Not Modified</td>
                            <td><code>notModified()</code></td>
                            <td>O recurso não foi modificado desde a última requisição (usado com cache).</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="codigos-cliente" class="subsection">
            <h3>3.3. Códigos de Erro do Cliente (4xx)</h3>
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>Método</th>
                            <th>Uso</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>400</td>
                            <td>Bad Request</td>
                            <td><code>badRequest()</code></td>
                            <td>A requisição está mal formada, com sintaxe inválida ou parâmetros incorretos.</td>
                        </tr>
                        <tr>
                            <td>401</td>
                            <td>Unauthorized</td>
                            <td><code>unauthorized()</code></td>
                            <td>Autenticação é necessária e falhou ou não foi fornecida.</td>
                        </tr>
                        <tr>
                            <td>403</td>
                            <td>Forbidden</td>
                            <td><code>forbidden()</code></td>
                            <td>O servidor entendeu a requisição, mas se recusa a autorizá-la (o usuário não tem
                                permissão).</td>
                        </tr>
                        <tr>
                            <td>404</td>
                            <td>Not Found</td>
                            <td><code>notFound()</code></td>
                            <td>O recurso solicitado não foi encontrado.</td>
                        </tr>
                        <tr>
                            <td>405</td>
                            <td>Method Not Allowed</td>
                            <td><code>methodNotAllowed()</code></td>
                            <td>O método HTTP usado não é permitido para este recurso.</td>
                        </tr>
                        <tr>
                            <td>409</td>
                            <td>Conflict</td>
                            <td><code>conflict()</code></td>
                            <td>A requisição conflita com o estado atual do recurso.</td>
                        </tr>
                        <tr>
                            <td>422</td>
                            <td>Unprocessable Entity</td>
                            <td><code>unprocessableEntity()</code></td>
                            <td>A requisição está bem formada, mas contém dados inválidos (muito usado em validações).
                            </td>
                        </tr>
                        <tr>
                            <td>429</td>
                            <td>Too Many Requests</td>
                            <td><code>tooManyRequests()</code></td>
                            <td>Muitas requisições em um determinado período (rate limiting).</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="codigos-servidor" class="subsection">
            <h3>3.4. Códigos de Erro do Servidor (5xx)</h3>
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>Método</th>
                            <th>Uso</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>500</td>
                            <td>Internal Server Error</td>
                            <td><code>serverError()</code></td>
                            <td>Erro genérico do servidor.</td>
                        </tr>
                        <tr>
                            <td>503</td>
                            <td>Service Unavailable</td>
                            <td><code>serviceUnavailable()</code></td>
                            <td>O servidor não está disponível no momento (manutenção ou sobrecarga).</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="best-practice">
            <h4>Boa Prática:</h4>
            <p>Sempre use o método específico para cada código HTTP em vez de escolher manualmente o código. Isso
                garante consistência e minimiza erros.</p>
        </div>
    </section>

    <section id="tabela-referencia" class="manual-section">
        <h2>4. Tabela de Referência Rápida</h2>
        <p>Esta tabela fornece uma referência rápida para escolher o código de resposta HTTP mais apropriado para cada
            situação.</p>

        <div class="comparison-table">
            <table>
                <thead>
                    <tr>
                        <th>Situação</th>
                        <th>Código</th>
                        <th>Método</th>
                        <th>Quando Usar</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Respostas de Sucesso -->
                    <tr>
                        <td>Operação bem-sucedida</td>
                        <td>200 OK</td>
                        <td><code>ok()</code></td>
                        <td>Quando uma requisição foi bem-sucedida e retorna dados (GET, PUT, PATCH)</td>
                    </tr>
                    <tr>
                        <td>Recurso criado</td>
                        <td>201 Created</td>
                        <td><code>created()</code></td>
                        <td>Quando um novo recurso foi criado com sucesso (POST)</td>
                    </tr>
                    <tr>
                        <td>Operação sem retorno</td>
                        <td>204 No Content</td>
                        <td><code>noContent()</code></td>
                        <td>Quando uma operação foi bem-sucedida mas não há conteúdo para retornar (DELETE)</td>
                    </tr>
                    <tr>
                        <td>Recurso não modificado</td>
                        <td>304 Not Modified</td>
                        <td><code>notModified()</code></td>
                        <td>Quando o recurso não foi modificado desde a última requisição (usado com cache)</td>
                    </tr>

                    <!-- Respostas de Erro do Cliente -->
                    <tr>
                        <td>Requisição inválida</td>
                        <td>400 Bad Request</td>
                        <td><code>badRequest()</code></td>
                        <td>Quando a requisição contém sintaxe inválida ou não pode ser processada</td>
                    </tr>
                    <tr>
                        <td>Não autenticado</td>
                        <td>401 Unauthorized</td>
                        <td><code>unauthorized()</code></td>
                        <td>Quando autenticação é necessária e não foi fornecida ou falhou</td>
                    </tr>
                    <tr>
                        <td>Sem permissão</td>
                        <td>403 Forbidden</td>
                        <td><code>forbidden()</code></td>
                        <td>Quando o usuário está autenticado mas não tem permissão para a operação</td>
                    </tr>
                    <tr>
                        <td>Recurso não encontrado</td>
                        <td>404 Not Found</td>
                        <td><code>notFound()</code></td>
                        <td>Quando o recurso solicitado não existe</td>
                    </tr>
                    <tr>
                        <td>Método não permitido</td>
                        <td>405 Method Not Allowed</td>
                        <td><code>methodNotAllowed()</code></td>
                        <td>Quando o método HTTP usado não é suportado para o recurso</td>
                    </tr>
                    <tr>
                        <td>Conflito</td>
                        <td>409 Conflict</td>
                        <td><code>conflict()</code></td>
                        <td>Quando há um conflito com o estado atual do recurso (ex: violação de unicidade)</td>
                    </tr>
                    <tr>
                        <td>Erro de validação</td>
                        <td>422 Unprocessable Entity</td>
                        <td><code>unprocessableEntity()</code></td>
                        <td>Quando os dados fornecidos não passam na validação</td>
                    </tr>
                    <tr>
                        <td>Muitas requisições</td>
                        <td>429 Too Many Requests</td>
                        <td><code>tooManyRequests()</code></td>
                        <td>Quando o cliente excedeu o limite de requisições (rate limiting)</td>
                    </tr>

                    <!-- Respostas de Erro do Servidor -->
                    <tr>
                        <td>Erro interno</td>
                        <td>500 Internal Server Error</td>
                        <td><code>serverError()</code></td>
                        <td>Quando ocorre um erro inesperado no servidor</td>
                    </tr>
                    <tr>
                        <td>Serviço indisponível</td>
                        <td>503 Service Unavailable</td>
                        <td><code>serviceUnavailable()</code></td>
                        <td>Quando o servidor está temporariamente indisponível (manutenção ou sobrecarga)</td>
                    </tr>

                    <!-- Respostas Especiais -->
                    <tr>
                        <td>Dados paginados</td>
                        <td>200 OK</td>
                        <td><code>paginated()</code></td>
                        <td>Para retornar coleções paginadas com metadados de paginação</td>
                    </tr>
                    <tr>
                        <td>Resposta em cache</td>
                        <td>200 OK</td>
                        <td><code>cached()</code></td>
                        <td>Para retornar dados com cabeçalhos de cache apropriados</td>
                    </tr>
                    <tr>
                        <td>Operações em lote</td>
                        <td>200/207/400</td>
                        <td><code>batch()</code></td>
                        <td>Para operações que processam múltiplos itens de uma vez</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    <section id="implementacao" class="manual-section">
        <h2>4. Implementação</h2>
        <p>Nosso projeto implementa um sistema de respostas padronizadas através de duas classes principais:
            <code>ResponseAbstract</code> e <code>ApiResponse</code>.
        </p>

        <div id="response-abstract" class="subsection">
            <h3>4.1. Classe ResponseAbstract</h3>
            <p>A classe <code>ResponseAbstract</code> é a base do sistema de respostas, fornecendo métodos para criar
                respostas padronizadas e gerenciar logs.</p>

            <div class="code-block">
                <h4>Estrutura da Classe ResponseAbstract</h4>
                <pre>
namespace App\Responses;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

abstract class ResponseAbstract
{
    protected $apiVersion = '1.0';
    protected $autoLogging = true;
    protected $logContext = [];

    // Métodos para configuração de logging
    public function enableAutoLogging(bool $enable): self { ... }
    public function withLogContext(array $context): self { ... }
    protected function logResponse(string $level, string $message, array $context = []): void { ... }

    // Método base para construção de respostas
    protected function response(string $status, string $message, $data = null, int $statusCode = 200, array $headers = []): JsonResponse { ... }

    // Métodos para diferentes tipos de respostas
    public function ok($data = null, ?string $message = null, array $headers = []): JsonResponse { ... }
    public function created($data = null, ?string $message = null, ?string $location = null): JsonResponse { ... }
    public function noContent(?string $message = null): JsonResponse { ... }
    // ... outros métodos para diferentes códigos HTTP
}
                </pre>
            </div>

            <p>A classe <code>ResponseAbstract</code> fornece:</p>
            <ul>
                <li>Métodos para cada código HTTP comum</li>
                <li>Sistema de logging automático</li>
                <li>Formatação consistente de respostas</li>
                <li>Métodos estáticos para uso sem instanciação</li>
            </ul>
        </div>

        <div id="api-response" class="subsection">
            <h3>4.2. Classe ApiResponse</h3>
            <p>A classe <code>ApiResponse</code> estende <code>ResponseAbstract</code> e fornece uma interface mais
                limpa para uso nos controladores.</p>

            <div class="code-block">
                <h4>Estrutura da Classe ApiResponse</h4>
                <pre>
namespace App\Responses;

use Illuminate\Http\JsonResponse;

class ApiResponse extends ResponseAbstract
{
    protected $apiVersion = '1.0';

    public function __construct(?string $apiVersion = null) { ... }

    // Métodos de configuração
    public static function withVersion(string $version): self { ... }
    public static function withoutLogging(): self { ... }
    public static function withContext(array $context): self { ... }

    // Métodos estáticos simplificados (sem sufixo _static)
    public static function ok($data = null, ?string $message = null): JsonResponse { ... }
    public static function created($data = null, ?string $message = null, ?string $location = null): JsonResponse { ... }
    public static function noContent(?string $message = null): JsonResponse { ... }
    // ... outros métodos para diferentes códigos HTTP
}
                </pre>
            </div>

            <p>A classe <code>ApiResponse</code> fornece:</p>
            <ul>
                <li>Métodos estáticos com nomes mais limpos (sem sufixo _static)</li>
                <li>Métodos de configuração para versão da API, logging e contexto</li>
                <li>Interface mais amigável para uso em controladores</li>
            </ul>
        </div>

        <div id="metodos-disponiveis" class="subsection">
            <h3>4.3. Métodos Disponíveis</h3>
            <p>Abaixo estão os principais métodos disponíveis para uso nos controladores:</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Método</th>
                            <th>Descrição</th>
                            <th>Parâmetros</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>ok()</code></td>
                            <td>Resposta de sucesso (200)</td>
                            <td><code>$data</code>, <code>$message</code></td>
                        </tr>
                        <tr>
                            <td><code>created()</code></td>
                            <td>Recurso criado (201)</td>
                            <td><code>$data</code>, <code>$message</code>, <code>$location</code></td>
                        </tr>
                        <tr>
                            <td><code>noContent()</code></td>
                            <td>Sem conteúdo (204)</td>
                            <td><code>$message</code></td>
                        </tr>
                        <tr>
                            <td><code>notModified()</code></td>
                            <td>Não modificado (304)</td>
                            <td><code>$message</code>, <code>$headers</code></td>
                        </tr>
                        <tr>
                            <td><code>badRequest()</code></td>
                            <td>Requisição inválida (400)</td>
                            <td><code>$message</code>, <code>$errors</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>unauthorized()</code></td>
                            <td>Não autorizado (401)</td>
                            <td><code>$message</code>, <code>$errors</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>forbidden()</code></td>
                            <td>Acesso negado (403)</td>
                            <td><code>$message</code>, <code>$errors</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>notFound()</code></td>
                            <td>Recurso não encontrado (404)</td>
                            <td><code>$message</code>, <code>$errors</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>methodNotAllowed()</code></td>
                            <td>Método não permitido (405)</td>
                            <td><code>$message</code>, <code>$errors</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>conflict()</code></td>
                            <td>Conflito (409)</td>
                            <td><code>$message</code>, <code>$errors</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>unprocessableEntity()</code></td>
                            <td>Erro de validação (422)</td>
                            <td><code>$errors</code>, <code>$message</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>tooManyRequests()</code></td>
                            <td>Muitas requisições (429)</td>
                            <td><code>$message</code>, <code>$errors</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>serverError()</code></td>
                            <td>Erro interno do servidor (500)</td>
                            <td><code>$message</code>, <code>$errors</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>serviceUnavailable()</code></td>
                            <td>Serviço indisponível (503)</td>
                            <td><code>$message</code>, <code>$errors</code>, <code>$additionalData</code></td>
                        </tr>
                        <tr>
                            <td><code>cachedResponse()</code></td>
                            <td>Resposta em cache</td>
                            <td><code>$data</code>, <code>$seconds</code>, <code>$message</code>, <code>$headers</code>
                            </td>
                        </tr>
                        <tr>
                            <td><code>batchResponse()</code></td>
                            <td>Resposta para operações em lote</td>
                            <td><code>$results</code>, <code>$errors</code>, <code>$status</code></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="tip">
                <h4>Dica</h4>
                <p>Para uso em controladores, recomendamos utilizar os métodos estáticos da classe
                    <code>ApiResponse</code>:
                </p>
                <pre>
use App\Responses\ApiResponse;

// Em um controlador
public function show($id)
{
    $product = $this->service->find($id);
    
    if (!$product) {
        return ApiResponse::notFound('Produto não encontrado');
    }
    
    return ApiResponse::ok(new ProductResource($product), 'Produto recuperado com sucesso');
}
                </pre>
            </div>
        </div>
    </section>

    <section id="casos-uso" class="manual-section">
        <h2>5. Casos de Uso Comuns</h2>
        <p>A seguir, exemplos de como utilizar as classes de resposta em cenários comuns da API.</p>

        <div id="caso-listagem" class="subsection">
            <h3>5.1. Listagem de Recursos</h3>
            <div class="code-block">
                <h4>Código no Controller</h4>
                <pre>
namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProductResource;
use App\Responses\ApiResponse;
use App\Services\ProductService;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    protected $service;

    public function __construct(ProductService $service)
    {
        $this->service = $service;
    }

    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $products = $this->service->getAllPaginated($perPage);
        
        return ApiResponse::ok(
            ProductResource::collection($products),
            'Produtos recuperados com sucesso'
        );
    }
}
                </pre>
            </div>
        </div>

        <div id="caso-detalhes" class="subsection">
            <h3>5.2. Detalhes de um Recurso</h3>
            <div class="code-block">
                <h4>Código no Controller</h4>
                <pre>
public function show($id)
{
    $product = $this->service->find($id);
    
    if (!$product) {
        return ApiResponse::notFound('Produto não encontrado');
    }
    
    return ApiResponse::ok(
        new ProductResource($product),
        'Produto recuperado com sucesso'
    );
}
                </pre>
            </div>
        </div>

        <div id="caso-criacao" class="subsection">
            <h3>5.3. Criação de Recurso</h3>
            <div class="code-block">
                <h4>Código no Controller</h4>
                <pre>
public function store(ProductRequest $request)
{
    try {
        $product = $this->service->create($request->validated());
        
        return ApiResponse::created(
            new ProductResource($product),
            'Produto criado com sucesso',
            route('api.v1.products.show', $product->id)
        );
    } catch (\Exception $e) {
        return ApiResponse::serverError('Erro ao criar produto');
    }
}
                </pre>
            </div>
        </div>

        <div id="caso-atualizacao" class="subsection">
            <h3>5.3. Atualização de Recurso</h3>
            <div class="code-block">
                <h4>Código no Controller</h4>
                <pre>
public function update(ProductRequest $request, $id)
{
    try {
        $product = $this->service->update($id, $request->validated());
        
        return ApiResponse::ok(
            new ProductResource($product),
            'Produto atualizado com sucesso'
        );
    } catch (NotFoundException $e) {
        return ApiResponse::notFound($e->getMessage());
    } catch (\Exception $e) {
        return ApiResponse::serverError('Erro ao atualizar produto');
    }
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de resposta</h4>
                <pre>
{
    "status": "success",
    "message": "Produto atualizado com sucesso",
    "code": 200,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0",
    "data": {
        "id": 1,
        "name": "Smartphone XYZ Atualizado",
        "price": 1499.99,
        "description": "Descrição atualizada do produto",
        "category": {
            "id": 3,
            "name": "Eletrônicos"
        },
        "created_at": "2023-06-15T14:30:45.000000Z",
        "updated_at": "2023-07-15T14:30:45.000000Z"
    }
}
                </pre>
            </div>
        </div>

        <div id="caso-remocao" class="subsection">
            <h3>5.4. Remoção de Recurso</h3>
            <div class="code-block">
                <h4>Código no Controller</h4>
                <pre>
public function destroy($id)
{
    try {
        $this->service->delete($id);
        
        return ApiResponse::noContent();
    } catch (NotFoundException $e) {
        return ApiResponse::notFound($e->getMessage());
    } catch (\Exception $e) {
        return ApiResponse::serverError('Erro ao excluir produto');
    }
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de resposta</h4>
                <pre>
{
    "status": "success",
    "message": "No content",
    "code": 204,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0"
}
                </pre>
            </div>
        </div>

        <div id="caso-validacao" class="subsection">
            <h3>5.5. Erros de Validação</h3>
            <p>Os erros de validação são tratados automaticamente pelo Laravel e formatados usando nossa classe de
                resposta:</p>

            <div class="code-block">
                <h4>Exemplo de tratamento de validação</h4>
                <pre>
// No Exception Handler
if ($exception instanceof ValidationException) {
    return ApiResponse::unprocessableEntity(
        $exception->validator->errors()->toArray(),
        'Erro de validação'
    );
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de resposta de erro de validação</h4>
                <pre>
{
    "status": "error",
    "message": "Erro de validação",
    "code": 422,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0",
    "errors": {
        "name": [
            "O campo nome é obrigatório"
        ],
        "price": [
            "O preço deve ser um valor numérico",
            "O preço deve ser maior que zero"
        ]
    }
}
                </pre>
            </div>
        </div>

        <div id="caso-autenticacao" class="subsection">
            <h3>5.6. Erros de Autenticação</h3>
            <p>Quando um usuário não está autenticado ou não tem permissão para acessar um recurso:</p>

            <div class="code-block">
                <h4>Exemplo de tratamento de autenticação</h4>
                <pre>
// No Exception Handler
if ($exception instanceof AuthenticationException) {
    return ApiResponse::unauthorized('Autenticação necessária para acessar este recurso');
}

if ($exception instanceof AuthorizationException) {
    return ApiResponse::forbidden('Você não tem permissão para realizar esta ação');
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de resposta de erro de autenticação</h4>
                <pre>
{
    "status": "error",
    "message": "Não autorizado",
    "code": 401,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0"
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de resposta de erro de autorização</h4>
                <pre>
{
    "status": "error",
    "message": "Acesso negado",
    "code": 403,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0"
}
                </pre>
            </div>
        </div>

        <div id="caso-batch" class="subsection">
            <h3>5.7. Operações em Lote</h3>
            <p>Para operações que processam múltiplos itens de uma vez:</p>

            <div class="code-block">
                <h4>Código no Controller</h4>
                <pre>
public function batchUpdate(BatchUpdateRequest $request)
{
    $results = [];
    $errors = [];
    
    foreach ($request->items as $item) {
        try {
            $product = $this->service->update($item['id'], $item);
            $results[] = [
                'id' => $item['id'],
                'status' => 'success',
                'data' => new ProductResource($product)
            ];
        } catch (\Exception $e) {
            $errors[] = [
                'id' => $item['id'],
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }
    
    return ApiResponse::withContext(['operation' => 'batch_update'])
        ->batchResponse($results, $errors);
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de resposta de operação em lote</h4>
                <pre>
{
    "status": "success",
    "message": "Processamento em lote concluído com sucesso",
    "code": 200,
    "timestamp": "2023-07-15T14:30:45.000000Z",
    "api_version": "1.0",
    "data": {
        "results": [
            {
                "id": 1,
                "status": "success",
                "data": {
                    "id": 1,
                    "name": "Produto Atualizado 1",
                    "price": 199.99
                }
            },
            {
                "id": 2,
                "status": "success",
                "data": {
                    "id": 2,
                    "name": "Produto Atualizado 2",
                    "price": 299.99
                }
            }
        ],
        "errors": [
            {
                "id": 3,
                "status": "error",
                "message": "Produto não encontrado"
            }
        ]
    }
}
                </pre>
            </div>
        </div>
    </section>

    <section id="logging" class="manual-section">
        <h2>6. Sistema de Logging</h2>
        <p>Nossa implementação inclui um sistema de logging automático para todas as respostas da API, facilitando o
            monitoramento e a depuração.</p>

        <div id="logging-config" class="subsection">
            <h3>6.1. Configuração de Logging</h3>
            <p>O sistema de logging é habilitado por padrão e registra informações sobre cada resposta da API:</p>

            <div class="code-block">
                <h4>Método de Logging na Classe ResponseAbstract</h4>
                <pre>
protected function logResponse(string $level, string $message, array $context = []): void
{
    if (!$this->autoLogging) {
        return;
    }

    $mergedContext = array_merge($this->logContext, $context);
    Log::$level($message, $mergedContext);
}
                </pre>
            </div>

            <div class="code-block">
                <h4>Uso no Método de Resposta</h4>
                <pre>
protected function response(string $status, string $message, $data = null, int $statusCode = 200, array $headers = []): JsonResponse
{
    $response = [
        'status' => $status,
        'message' => $message,
        'code' => $statusCode,
        'timestamp' => Carbon::now()->toIso8601String(),
        'api_version' => $this->apiVersion,
    ];

    // Adicionar ID da requisição se disponível
    $requestId = Request::header('X-Request-ID');
    if ($requestId) {
        $response['request_id'] = $requestId;
    }

    // Adicionar dados se fornecidos
    if ($data !== null) {
        $response['data'] = $data;
    }

    // Log da resposta
    $logLevel = ($status === 'error' || $statusCode >= 400) ? 'error' : 'info';
    $this->logResponse($logLevel, "API Response: $message", [
        'status_code' => $statusCode,
        'request_id' => $requestId ?? 'unknown',
        'path' => Request::path(),
        'method' => Request::method(),
    ]);

    return new JsonResponse($response, $statusCode, $headers);
}
                </pre>
            </div>

            <div class="best-practice">
                <h4>Informações Registradas</h4>
                <p>Cada log de resposta inclui:</p>
                <ul>
                    <li>Mensagem da resposta</li>
                    <li>Código de status HTTP</li>
                    <li>ID da requisição (se disponível)</li>
                    <li>Caminho da requisição (URL)</li>
                    <li>Método HTTP (GET, POST, etc.)</li>
                    <li>Contexto adicional (se fornecido)</li>
                </ul>
            </div>
        </div>

        <div id="logging-context" class="subsection">
            <h3>6.2. Contexto de Log</h3>
            <p>É possível adicionar contexto personalizado aos logs para facilitar a identificação e filtragem:</p>

            <div class="code-block">
                <h4>Adicionando Contexto de Log</h4>
                <pre>
// Em um controller
public function store(ProductRequest $request)
{
    $response = ApiResponse::withContext([
        'user_id' => auth()->id(),
        'operation' => 'product_creation',
        'source' => 'admin_panel'
    ]);
    
    try {
        $product = $this->service->create($request->validated());
        return $response->created(new ProductResource($product));
    } catch (\Exception $e) {
        return $response->serverError('Erro ao criar produto');
    }
}
                </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de Log Gerado</h4>
                <pre>
[2023-07-15 14:30:45] local.INFO: API Response: Recurso criado com sucesso {
    "status_code": 201,
    "request_id": "f7cdf52e-1c3d-4a7f-b9c1-d8e5a6c7b9a8",
    "path": "api/products",
    "method": "POST",
    "user_id": 42,
    "operation": "product_creation",
    "source": "admin_panel"
}
                </pre>
            </div>
        </div>

        <div id="logging-disable" class="subsection">
            <h3>6.3. Desativação de Logs</h3>
            <p>Em alguns casos, pode ser necessário desativar o logging automático:</p>

            <div class="code-block">
                <h4>Desativando Logs para uma Resposta Específica</h4>
                <pre>
// Em um controller
public function healthCheck()
{
    // Desativa logs para evitar poluir os arquivos de log com verificações frequentes
    return ApiResponse::withoutLogging()->ok(['status' => 'healthy']);
}
                </pre>
            </div>

            <div class="tip">
                <h4>Dica</h4>
                <p>Considere desativar o logging para endpoints que são chamados com muita frequência (como health
                    checks) ou para respostas que contêm dados sensíveis que não devem ser registrados.</p>
            </div>
        </div>
    </section>

    <section id="boas-praticas" class="manual-section">
        <h2>7. Boas Práticas</h2>
        <p>Para garantir a consistência e qualidade das respostas da API, siga estas boas práticas:</p>

        <div class="best-practice">
            <h4>Uso Consistente</h4>
            <ul>
                <li>Sempre use a classe <code>ApiResponse</code> para formatar respostas da API</li>
                <li>Evite usar <code>response()->json()</code> diretamente nos controllers</li>
                <li>Mantenha a estrutura de resposta consistente em todos os endpoints</li>
                <li>Use os métodos específicos para cada código HTTP (não escolha códigos manualmente)</li>
            </ul>
        </div>

        <div class="best-practice">
            <h4>Mensagens</h4>
            <ul>
                <li>Use mensagens claras e descritivas que expliquem o resultado da operação</li>
                <li>Padronize as mensagens para operações semelhantes (ex: "Recurso criado com sucesso")</li>
                <li>Evite mensagens técnicas ou com detalhes de implementação</li>
                <li>Considere usar internacionalização para mensagens em diferentes idiomas</li>
            </ul>
        </div>

        <div class="best-practice">
            <h4>Tratamento de Erros</h4>
            <ul>
                <li>Sempre capture exceções e retorne respostas de erro apropriadas</li>
                <li>Use códigos HTTP específicos para diferentes tipos de erro</li>
                <li>Inclua detalhes de erro úteis, mas não exponha informações sensíveis</li>
                <li>Em produção, limite os detalhes técnicos de erros internos</li>
            </ul>
        </div>

        <div class="best-practice">
            <h4>Logging</h4>
            <ul>
                <li>Adicione contexto relevante aos logs para facilitar a depuração</li>
                <li>Use níveis de log apropriados (info para sucesso, error para erros)</li>
                <li>Inclua IDs de requisição para rastreabilidade</li>
                <li>Desative logs para endpoints de alta frequência ou com dados sensíveis</li>
            </ul>
        </div>

        <div class="best-practice">
            <h4>Versionamento</h4>
            <ul>
                <li>Inclua a versão da API em todas as respostas</li>
                <li>Mantenha compatibilidade com versões anteriores ao fazer alterações</li>
                <li>Documente mudanças na estrutura de resposta entre versões</li>
            </ul>
        </div>

        <div class="tip">
            <h4>Dica para Laravel 12</h4>
            <p>Aproveite os recursos do PHP 8.2 e Laravel 12 para melhorar suas respostas:</p>
            <ul>
                <li>Use tipagem estrita para melhorar a segurança e clareza do código</li>
                <li>Aproveite enums para representar estados e status</li>
                <li>Utilize classes readonly para DTOs imutáveis</li>
                <li>Implemente o novo sistema de validação fluente</li>
            </ul>
        </div>
    </section>
    <section id="boas-praticas-codigos" class="manual-section">
        <h2>10. Boas Práticas para Uso dos Códigos de Resposta</h2>

        <div class="best-practice">
            <h4>Escolha do Código Correto</h4>
            <ul>
                <li><strong>Seja específico:</strong> Use o código mais específico para cada situação, não apenas 200
                    para sucesso e 400 para erro</li>
                <li><strong>Siga os padrões HTTP:</strong> Respeite o significado padrão dos códigos HTTP conforme
                    definido nas RFCs</li>
                <li><strong>Consistência:</strong> Use o mesmo código para situações semelhantes em toda a API</li>
            </ul>
        </div>

        <div class="best-practice">
            <h4>Códigos 2xx (Sucesso)</h4>
            <ul>
                <li><strong>200 OK:</strong> Use para operações bem-sucedidas que retornam dados</li>
                <li><strong>201 Created:</strong> Use apenas quando um novo recurso é criado, e inclua o cabeçalho
                    Location</li>
                <li><strong>204 No Content:</strong> Use para operações bem-sucedidas que não retornam dados, como
                    exclusões</li>
            </ul>
        </div>

        <div class="best-practice">
            <h4>Códigos 4xx (Erro do Cliente)</h4>
            <ul>
                <li><strong>400 Bad Request:</strong> Use para erros gerais de requisição, quando nenhum outro código
                    4xx é mais apropriado</li>
                <li><strong>401 Unauthorized:</strong> Use apenas para falhas de autenticação, não para problemas de
                    autorização</li>
                <li><strong>403 Forbidden:</strong> Use quando o usuário está autenticado mas não tem permissão para a
                    operação</li>
                <li><strong>404 Not Found:</strong> Use quando o recurso não existe ou quando não quer revelar a
                    existência de um recurso por razões de segurança</li>
                <li><strong>422 Unprocessable Entity:</strong> Use para erros de validação, com detalhes específicos
                    sobre cada campo inválido</li>
            </ul>
        </div>

        <div class="best-practice">
            <h4>Códigos 5xx (Erro do Servidor)</h4>
            <ul>
                <li><strong>500 Internal Server Error:</strong> Use para erros inesperados, mas sempre tente fornecer
                    uma mensagem útil</li>
                <li><strong>503 Service Unavailable:</strong> Use durante manutenção programada ou quando um serviço
                    externo está indisponível</li>
                <li><strong>Evite expor detalhes sensíveis:</strong> Em produção, não inclua stacktraces ou detalhes de
                    implementação nas respostas 5xx</li>
            </ul>
        </div>

        <div class="best-practice">
            <h4>Respostas Especiais</h4>
            <ul>
                <li><strong>Paginação:</strong> Use <code>paginated()</code> para todas as listagens que podem retornar
                    muitos itens</li>
                <li><strong>Cache:</strong> Use <code>cached()</code> para recursos que não mudam frequentemente,
                    definindo um tempo de cache apropriado</li>
                <li><strong>Operações em Lote:</strong> Use <code>batch()</code> para operações que processam múltiplos
                    itens, com detalhes sobre sucessos e falhas</li>
                <li><strong>Código 207 Multi-Status:</strong> Apropriado para operações em lote com resultados mistos
                    (alguns sucessos, alguns erros)</li>
            </ul>
        </div>

        <div class="tip">
            <h4>Dica para Testes</h4>
            <p>Ao escrever testes, verifique não apenas o código de status, mas também a estrutura completa da resposta,
                incluindo:</p>
            <ul>
                <li>O status correto ("success" ou "error")</li>
                <li>A presença de uma mensagem apropriada</li>
                <li>A estrutura correta dos dados retornados</li>
                <li>A presença de cabeçalhos específicos quando necessário (ex: Location para 201 Created)</li>
            </ul>
        </div>
    </section>
    <section id="conclusao" class="manual-section">
        <h2>8. Conclusão</h2>
        <p>A padronização de respostas da API é fundamental para criar uma experiência consistente e previsível para os
            consumidores da API. Nosso sistema de respostas padronizadas, implementado através das classes
            <code>ResponseAbstract</code> e <code>ApiResponse</code>, oferece:
        </p>

        <div class="conclusion">
            <ul>
                <li>Formato consistente para todas as respostas</li>
                <li>Uso correto e padronizado de códigos HTTP</li>
                <li>Sistema de logging automático para monitoramento e depuração</li>
                <li>Métodos específicos para diferentes tipos de resposta</li>
                <li>Suporte para contexto personalizado e configurações</li>
                <li>Compatibilidade com os recursos modernos do Laravel 12 e PHP 8.2</li>
            </ul>

            <p>Ao seguir as diretrizes deste manual, garantimos que nossa API seja robusta, fácil de usar e manter,
                proporcionando uma experiência de desenvolvimento melhor tanto para os desenvolvedores internos quanto
                para os consumidores da API.</p>
        </div>

        <div class="best-practice">
            <h4>Próximos Passos</h4>
            <ul>
                <li>Migrar controllers existentes para usar o novo sistema de respostas</li>
                <li>Implementar testes automatizados para verificar a conformidade das respostas</li>
                <li>Documentar a API usando OpenAPI/Swagger</li>
                <li>Configurar monitoramento para logs de resposta</li>
                <li>Treinar a equipe no uso correto do sistema de respostas</li>
            </ul>
        </div>
    </section>



    <footer class="manual-footer">
        <p>Manual de Respostas Padronizadas - Laravel 12</p>
        <p>Versão 2.0 - Última atualização: Maio 2023</p>
    </footer>
</body>

</html>