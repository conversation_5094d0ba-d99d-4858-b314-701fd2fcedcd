<!DOCTYPE html>
<html>

<head>
    <title>Exceção Detectada</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }

        .container {
            padding: 20px;
        }

        .error-details {
            background: #f8f8f8;
            padding: 15px;
            margin: 15px 0;
        }

        .stack-trace {
            background: #f1f1f1;
            padding: 15px;
            overflow-x: auto;
        }
    </style>
</head>

<body>
    <div class="container">
        <h2>Uma exceção foi detectada</h2>

        <div class="error-details">
            <p><strong>URL:</strong> {{ $url }}</p>
            <p><strong>Serviço:</strong> {{ $service }}</p>
            <p><strong>Mensagem:</strong> {{ $exception->getMessage() }}</p>
            <p><strong>Arquivo:</strong> {{ $exception->getFile() }}</p>
            <p><strong>Linha:</strong> {{ $exception->getLine() }}</p>
        </div>

        <h3>Stack Trace:</h3>
        <div class="stack-trace">
            <pre>{{ $exception->getTraceAsString() }}</pre>
        </div>
    </div>
</body>

</html>
