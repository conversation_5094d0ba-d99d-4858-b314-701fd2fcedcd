<?php

namespace App\Responses;

/**
 * Implementação concreta da classe de resposta para API
 * 
 * Fornece métodos para gerar respostas HTTP padronizadas para a API
 */
class ApiResponse extends ResponseAbstract
{
    /**
     * Versão da API
     * 
     * @var string
     */
    protected $apiVersion = '1.0';

    /**
     * Retorna uma resposta de erro de autenticação
     * 
     * @param string $message Mensagem personalizada
     * @param array $headers Cabeçalhos HTTP adicionais
     * @return \Illuminate\Http\JsonResponse
     */
    public function authenticationError($message = 'Credenciais inválidas.', array $headers = [])
    {
        return $this->error($message, self::HTTP_UNAUTHORIZED, [], $headers);
    }

    /**
     * Retorna uma resposta de sucesso para login
     * 
     * @param array $userData Dados do usuário
     * @param string $token Token de acesso
     * @param string $message Mensagem personalizada
     * @param array $headers Cabeçalhos HTTP adicionais
     * @return \Illuminate\Http\JsonResponse
     */
    public function loginSuccess($userData, $token, $message = 'Login realizado com sucesso.', array $headers = [])
    {
        $data = [
            'user' => $userData,
            'access_token' => $token,
            'token_type' => 'Bearer'
        ];

        return $this->success($data, $message, $headers);
    }

    /**
     * Retorna uma resposta de sucesso para logout
     * 
     * @param string $message Mensagem personalizada
     * @param array $headers Cabeçalhos HTTP adicionais
     * @return \Illuminate\Http\JsonResponse
     */
    public function logoutSuccess($message = 'Logout realizado com sucesso.', array $headers = [])
    {
        return $this->success(null, $message, $headers);
    }
}
