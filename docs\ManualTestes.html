<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Testes</title>
    <link rel="stylesheet" href="css/manual.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Testes</h1>
        <p>Guia completo para implementação, execução e manutenção de testes no projeto Laravel 12</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#tipos-testes">Tipos de Testes</a></li>
            <li><a href="#frameworks">Frameworks e Ferramentas</a></li>
            <li><a href="#config-ambiente">Configuração do Ambiente</a></li>
            <li><a href="#escrevendo-testes">Escrevendo Testes</a></li>
            <li><a href="#testes-dominio">Testes de Domínio</a></li>
            <li><a href="#tdd">TDD</a></li>
            <li><a href="#execucao-testes">Execução de Testes</a></li>
            <li><a href="#cobertura">Cobertura de Testes</a></li>
            <li><a href="#boas-praticas">Boas Práticas</a></li>
            <li><a href="#troubleshooting">Troubleshooting</a></li>
            <li><a href="#fluxo-trabalho">Fluxo de Trabalho</a></li>
            <li><a href="#conclusao">Conclusão</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#tipos-testes">2. Tipos de Testes</a>
                <ul>
                    <li><a href="#testes-unitarios">2.1. Testes Unitários</a></li>
                    <li><a href="#testes-integracao">2.2. Testes de Integração</a></li>
                    <li><a href="#testes-api">2.3. Testes de API</a></li>
                    <li><a href="#testes-browser">2.4. Testes de Browser</a></li>
                    <li><a href="#testes-performance">2.5. Testes de Performance</a></li>
                    <li><a href="#testes-seguranca">2.6. Testes de Segurança</a></li>
                </ul>
            </li>
            <li><a href="#frameworks">3. Frameworks e Ferramentas</a>
                <ul>
                    <li><a href="#phpunit">3.1. PHPUnit</a></li>
                    <li><a href="#pest">3.2. Pest</a></li>
                    <li><a href="#outras-ferramentas">3.3. Outras Ferramentas Úteis</a></li>
                </ul>
            </li>
            <li><a href="#config-ambiente">4. Configuração do Ambiente de Testes</a>
                <ul>
                    <li><a href="#config-basica">4.1. Configuração Básica</a></li>
                    <li><a href="#banco-dados-teste">4.2. Banco de Dados para Testes</a></li>
                    <li><a href="#factories-seeders">4.3. Factories e Seeders</a></li>
                    <li><a href="#mocks-stubs">4.4. Mocks e Stubs</a></li>
                </ul>
            </li>
            <li><a href="#escrevendo-testes">5. Escrevendo Testes Eficazes</a>
                <ul>
                    <li><a href="#estrutura-teste">5.1. Estrutura de um Bom Teste</a></li>
                    <li><a href="#nomeando-testes">5.2. Nomeando Testes</a></li>
                    <li><a href="#assertions">5.3. Assertions Eficazes</a></li>
                    <li><a href="#data-providers">5.4. Data Providers</a></li>
                </ul>
            </li>
            <li><a href="#testes-dominio">6. Testes Específicos do Domínio</a>
                <ul>
                    <li><a href="#testes-autenticacao">6.1. Testes de Autenticação e Autorização</a></li>
                    <li><a href="#testes-regras-negocio">6.2. Testes de Regras de Negócio</a></li>
                    <li><a href="#testes-integracao-externa">6.3. Testes de Integração Externa</a></li>
                </ul>
            </li>
            <li><a href="#tdd">7. Test-Driven Development (TDD)</a>
                <ul>
                    <li><a href="#tdd-ciclo">7.1. O Ciclo do TDD</a></li>
                    <li><a href="#tdd-projeto">7.2. TDD no Nosso Projeto</a></li>
                </ul>
            </li>
            <li><a href="#execucao-testes">8. Execução de Testes</a>
                <ul>
                    <li><a href="#linha-comando">8.1. Execução via Linha de Comando</a></li>
                    <li><a href="#testes-paralelos">8.2. Testes Paralelos</a></li>
                    <li><a href="#ci-cd">8.3. Integração com CI/CD</a></li>
                </ul>
            </li>
            <li><a href="#cobertura">9. Cobertura de Testes</a>
                <ul>
                    <li><a href="#ferramentas-cobertura">9.1. Ferramentas de Cobertura</a></li>
                    <li><a href="#metricas-cobertura">9.2. Métricas e Metas de Cobertura</a></li>
                    <li><a href="#estrategias-cobertura">9.3. Estratégias para Aumentar a Cobertura</a></li>
                </ul>
            </li>
            <li><a href="#boas-praticas">10. Boas Práticas</a>
                <ul>
                    <li><a href="#organizacao">10.1. Organização de Testes</a></li>
                    <li><a href="#anti-patterns">10.2. Anti-Patterns a Evitar</a></li>
                    <li><a href="#testes-legados">10.3. Testando Código Legado</a></li>
                    <li><a href="#testes-eficientes">10.4. Escrevendo Testes Eficientes</a></li>
                </ul>
            </li>
            <li><a href="#troubleshooting">11. Troubleshooting</a>
                <ul>
                    <li><a href="#problemas-comuns">11.1. Problemas Comuns e Soluções</a></li>
                    <li><a href="#depurando-testes">11.2. Depurando Testes</a></li>
                    <li><a href="#testes-flaky">11.3. Lidando com Testes Flaky</a></li>
                </ul>
            </li>
            <li><a href="#fluxo-trabalho">12. Fluxo de Trabalho de Testes</a>
                <ul>
                    <li><a href="#processo-desenvolvimento">12.1. Testes no Processo de Desenvolvimento</a></li>
                    <li><a href="#revisao-codigo">12.2. Testes e Revisão de Código</a></li>
                    <li><a href="#onboarding">12.3. Guia de Onboarding para Testes</a></li>
                </ul>
            </li>
            <li><a href="#conclusao">13. Conclusão</a></li>
        </ul>
    </section>


    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <p>Este manual fornece diretrizes abrangentes para testes em nosso projeto Laravel 12, abordando diferentes
            tipos
            de testes, ferramentas, metodologias e boas práticas. O objetivo é estabelecer um padrão de teste
            consistente que
            garanta a qualidade e a robustez do nosso software.</p>

        <section id="objetivos">
            <h3>1.1. Objetivos</h3>
            <p>Os principais objetivos da nossa estratégia de testes são:</p>
            <ul>
                <li>Garantir que o software atenda aos requisitos funcionais e não-funcionais</li>
                <li>Identificar e corrigir defeitos o mais cedo possível no ciclo de desenvolvimento</li>
                <li>Proporcionar confiança para refatoração e evolução do código</li>
                <li>Documentar o comportamento esperado do sistema</li>
                <li>Reduzir o custo total de manutenção do software</li>
                <li>Prevenir regressões durante a evolução do produto</li>
                <li>Melhorar a qualidade geral do código e da arquitetura</li>
                <li>Atingir uma cobertura de código de pelo menos 80% nas áreas críticas do sistema</li>
                <li>Garantir a segurança e integridade dos dados dos usuários</li>
                <li>Validar a correta implementação das regras de negócio específicas do nosso domínio</li>
            </ul>
        </section>

        <section id="principios">
            <h3>1.2. Princípios de Teste</h3>
            <div class="best-practice">
                <ul>
                    <li><strong>Teste Cedo e Frequentemente:</strong> Quanto mais cedo um defeito for encontrado, menor
                        o custo para corrigi-lo.</li>
                    <li><strong>Independência:</strong> Os testes devem ser independentes e não devem depender uns dos
                        outros.</li>
                    <li><strong>Repetibilidade:</strong> Os testes devem produzir os mesmos resultados quando executados
                        nas mesmas condições.</li>
                    <li><strong>Determinismo:</strong> Testes não devem apresentar comportamento aleatório ou depender
                        de condições externas não controladas.</li>
                    <li><strong>Isolamento:</strong> Os testes devem isolar as funcionalidades testadas, controlando
                        todas as dependências externas.</li>
                    <li><strong>Objetividade:</strong> Cada teste deve ter um propósito claro e verificar um único
                        conceito.</li>
                    <li><strong>Manutenibilidade:</strong> Os testes devem ser fáceis de entender e manter.</li>
                    <li><strong>Velocidade:</strong> Os testes devem ser rápidos para fornecer feedback imediato.</li>
                </ul>
            </div>
        </section>

        <section id="estrategia-geral">
            <h3>1.3. Estratégia Geral de Testes</h3>
            <p>Nossa estratégia de testes segue a <a
                    href="https://martinfowler.com/articles/practical-test-pyramid.html" target="_blank">Pirâmide de
                    Testes</a>, com ênfase especial em testes de API e integração, dada a natureza do nosso projeto:</p>

            <ul>
                <li><strong>Base:</strong> Testes unitários para lógica de negócio e serviços</li>
                <li><strong>Meio:</strong> Testes de integração e API para validar endpoints e fluxos completos</li>
                <li><strong>Topo:</strong> Testes de ponta a ponta para fluxos críticos do usuário</li>
            </ul>

            <div class="example">
                <h4>Distribuição Recomendada para Nosso Projeto</h4>
                <ul>
                    <li>~50% Testes Unitários (foco em regras de negócio)</li>
                    <li>~40% Testes de Integração e API</li>
                    <li>~10% Testes End-to-End / UI</li>
                </ul>
            </div>

            <div class="note">
                <p>Esta distribuição foi definida considerando que nosso projeto é principalmente uma API com regras de
                    negócio complexas. Priorizamos testes de integração e API para garantir que os endpoints funcionem
                    corretamente e que as regras de negócio sejam aplicadas de forma consistente.</p>
            </div>
        </section>
    </section>

    <section id="tipos-testes" class="manual-section">
        <h2>2. Tipos de Testes</h2>
        <p>Esta seção descreve os diferentes tipos de testes que implementamos em nosso projeto Laravel 12.</p>

        <section id="testes-unitarios">
            <h3>2.1. Testes Unitários</h3>
            <p>Testes unitários verificam o funcionamento isolado de componentes individuais do código, como classes ou
                métodos.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam uma única unidade de código</li>
                    <li>São rápidos (milissegundos)</li>
                    <li>Não dependem de sistemas externos (banco de dados, API, sistema de arquivos)</li>
                    <li>Utilizam mocks ou stubs para simular dependências</li>
                    <li>Não requerem configurações complexas</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste Unitário em Nosso Projeto</h4>
                <pre>
namespace Tests\Unit\Services;

use App\Services\TaxCalculatorService;
use PHPUnit\Framework\TestCase;

class TaxCalculatorServiceTest extends TestCase
{
    public function test_it_calculates_tax_correctly_for_standard_products(): void
    {
        // Arrange
        $calculator = new TaxCalculatorService();
        $productPrice = 100.00;
        $taxRate = 0.18; // 18% - taxa padrão

        // Act
        $result = $calculator->calculateTax($productPrice, $taxRate);

        // Assert
        $this->assertEquals(18.00, $result);
    }

    public function test_it_applies_tax_exemption_for_eligible_products(): void
    {
        // Arrange
        $calculator = new TaxCalculatorService();
        $productPrice = 100.00;
        $isExempt = true;

        // Act
        $result = $calculator->calculateWithExemption($productPrice, $isExempt);

        // Assert
        $this->assertEquals(0.00, $result);
    }
}
                </pre>
            </div>

            <p>Em nosso projeto, os testes unitários são especialmente importantes para validar a lógica de negócio
                complexa, como cálculos de impostos, regras de desconto, validações de dados e transformações.
                Aproveitamos os tipos de retorno e propriedades tipadas do PHP 8.2 para melhorar a qualidade dos testes.
            </p>
        </section>

        <section id="testes-integracao">
            <h3>2.2. Testes de Integração</h3>
            <p>Testes de integração verificam se diferentes componentes do sistema funcionam juntos corretamente.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam a interação entre múltiplos componentes</li>
                    <li>Podem incluir acesso a banco de dados real ou simulado</li>
                    <li>São mais lentos que os testes unitários</li>
                    <li>Podem verificar fluxos de trabalho completos</li>
                    <li>Geralmente requerem configuração mais elaborada</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Integração em Nosso Projeto</h4>
                <pre>
namespace Tests\Integration;

use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Services\OrderService;
use App\Repositories\OrderRepository;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class OrderProcessingTest extends TestCase
{
    use RefreshDatabase;

    public function test_order_is_processed_and_saved_to_database(): void
    {
        // Arrange
        $user = User::factory()->create();
        $product = Product::factory()->create(['price' => 100.00]);
        
        $orderData = [
            'user_id' => $user->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ]
        ];

        $orderService = app(OrderService::class);

        // Act
        $order = $orderService->createOrder($orderData);

        // Assert
        $this->assertInstanceOf(Order::class, $order);
        $this->assertEquals($user->id, $order->user_id);
        $this->assertEquals(200.00, $order->total);
        $this->assertCount(1, $order->items);
        $this->assertEquals(2, $order->items[0]->quantity);
        $this->assertEquals($product->id, $order->items[0]->product_id);
        
        // Verifica se foi salvo no banco
        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'user_id' => $user->id,
            'total' => 200.00
        ]);
    }
}
                </pre>
            </div>

            <p>Os testes de integração são particularmente importantes em nosso projeto para verificar o fluxo completo
                de processamento de pedidos, interações com o sistema de pagamento, e outras operações que envolvem
                múltiplos componentes.</p>
        </section>

        <section id="testes-api">
            <h3>2.3. Testes de API</h3>
            <p>Os testes de API são fundamentais em nosso projeto, pois verificam se os endpoints da aplicação retornam
                as respostas esperadas para diferentes solicitações.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam as interfaces públicas da aplicação</li>
                    <li>Verificam códigos de status HTTP, estrutura de resposta e cabeçalhos</li>
                    <li>Validam tanto caminhos felizes quanto tratamento de erros</li>
                    <li>Testam autorização e autenticação</li>
                    <li>Geralmente utilizam banco de dados de teste</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de API em Nosso Projeto</h4>
                <pre>
namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Product;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_returns_list_of_products(): void
    {
        // Arrange
        Product::factory()->count(3)->create();

        // Act
        $response = $this->getJson('/api/v1/products');

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(3, 'data')
            ->assertJsonStructure([
                'data' => [
                    '*' => ['id', 'name', 'price', 'description', 'created_at']
                ],
                'links',
                'meta'
            ]);
    }

    public function test_it_returns_product_details(): void
    {
        // Arrange
        $product = Product::factory()->create();

        // Act
        $response = $this->getJson("/api/v1/products/{$product->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $product->price,
                ]
            ]);
    }

    public function test_it_returns_404_for_nonexistent_product(): void
    {
        $response = $this->getJson("/api/v1/products/999");
        $response->assertStatus(404);
    }

    public function test_authenticated_user_can_create_product(): void
    {
        // Arrange
        Sanctum::actingAs(
            User::factory()->create(['role' => 'admin']),
            ['*']
        );

        $productData = [
            'name' => 'Novo Produto',
            'price' => 99.99,
            'description' => 'Descrição do produto',
            'category_id' => 1
        ];

        // Act
        $response = $this->postJson('/api/v1/products', $productData);

        // Assert
        $response->assertStatus(201)
            ->assertJsonFragment(['name' => 'Novo Produto']);

        $this->assertDatabaseHas('products', ['name' => 'Novo Produto']);
    }
}
                </pre>
            </div>

            <p>Os testes de API são essenciais em nosso projeto, pois nossa aplicação expõe principalmente interfaces de
                API para consumo por aplicações frontend e integrações com sistemas de terceiros. Garantimos que todos
                os endpoints sejam testados para verificar autenticação, autorização, validação de entrada e formato de
                resposta.</p>
        </section>

        <section id="testes-browser">
            <h3>2.4. Testes de Browser</h3>
            <p>Testes de browser (ou end-to-end) simulam a interação do usuário com a aplicação através da interface do
                navegador.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam o sistema completo em um ambiente similar ao de produção</li>
                    <li>Verificam a integração frontend-backend</li>
                    <li>Simulam interações reais do usuário (cliques, preenchimento de formulários)</li>
                    <li>São mais lentos e mais frágeis que outros tipos de testes</li>
                    <li>Geralmente requerem um navegador real ou headless</li>
                </ul>
            </div>

            <div class="note">
                <p>Em nosso projeto atual, os testes de browser são utilizados principalmente para validar fluxos
                    críticos do painel administrativo. A maioria das funcionalidades é testada via testes de API, já que
                    nossa aplicação é principalmente uma API REST.</p>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Browser com Laravel Dusk</h4>
                <pre>
namespace Tests\Browser;

use App\Models\User;
use Tests\DuskTestCase;
use Laravel\Dusk\Browser;

class AdminLoginTest extends DuskTestCase
{
    public function test_admin_can_login_and_access_dashboard(): void
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'admin'
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/admin/login')
                ->type('email', '<EMAIL>')
                ->type('password', 'password123')
                ->press('Login')
                ->assertPathIs('/admin/dashboard')
                ->assertSee('Dashboard Administrativo')
                ->assertSee('Produtos')
                ->assertSee('Usuários');
        });
    }
}
                </pre>
            </div>
        </section>

        <section id="testes-performance">
            <h3>2.5. Testes de Performance</h3>
            <p>Testes de performance avaliam como o sistema se comporta sob carga e se ele atende aos requisitos de
                desempenho.</p>

            <div class="best-practice">
                <h4>Tipos de Testes de Performance</h4>
                <ul>
                    <li><strong>Testes de carga:</strong> Verificam se o sistema funciona adequadamente sob carga
                        esperada</li>
                    <li><strong>Testes de estresse:</strong> Avaliam os limites do sistema aumentando a carga além do
                        esperado</li>
                    <li><strong>Testes de resistência:</strong> Verificam estabilidade e comportamento durante longos
                        períodos</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Performance com k6</h4>
                <pre>
// tests/performance/api_load_test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export const options = {
    stages: [
        { duration: '30s', target: 20 }, // ramp up to 20 users
        { duration: '1m', target: 20 },  // stay at 20 users for 1 minute
        { duration: '30s', target: 0 },  // ramp down to 0 users
    ],
    thresholds: {
        http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms
        http_req_failed: ['rate<0.01'],   // less than 1% can fail
    },
};

export default function() {
    const BASE_URL = 'https://api-staging.nossoapp.com/api/v1';
    
    // Get products listing
    const productsResponse = http.get(`${BASE_URL}/products`);
    check(productsResponse, {
        'products status 200': (r) => r.status === 200,
        'products response time < 200ms': (r) => r.timings.duration < 200,
        'products returns data': (r) => r.json('data').length > 0,
    });

    // Get a single product
    const productId = 1;
    const productResponse = http.get(`${BASE_URL}/products/${productId}`);
    check(productResponse, {
        'product status 200': (r) => r.status === 200,
        'product response time < 150ms': (r) => r.timings.duration < 150,
    });

    sleep(1);
}
                </pre>
            </div>

            <p>Em nosso projeto, executamos testes de performance regularmente em ambientes de staging para garantir
                que a API mantenha tempos de resposta aceitáveis mesmo sob carga. Nosso SLA interno exige que 95%
                das requisições sejam respondidas em menos de 300ms.</p>
        </section>

        <section id="testes-seguranca">
            <h3>2.6. Testes de Segurança</h3>
            <p>Testes de segurança identificam vulnerabilidades e garantem que a aplicação proteja adequadamente dados e
                funcionalidades sensíveis.</p>

            <div class="best-practice">
                <h4>Áreas de Foco em Testes de Segurança</h4>
                <ul>
                    <li><strong>Autenticação e Autorização:</strong> Verificar controles de acesso e proteção de rotas
                    </li>
                    <li><strong>Injeção de SQL:</strong> Garantir que consultas ao banco de dados estejam protegidas
                    </li>
                    <li><strong>Cross-Site Scripting (XSS):</strong> Verificar se a aplicação sanitiza corretamente
                        entradas de usuário</li>
                    <li><strong>Cross-Site Request Forgery (CSRF):</strong> Confirmar que formulários estão protegidos
                        com tokens CSRF</li>
                    <li><strong>Exposição de dados sensíveis:</strong> Verificar se dados confidenciais são protegidos
                        adequadamente</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Segurança em Nosso Projeto</h4>
                <pre>
namespace Tests\Security;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Customer;

class AuthorizationTest extends TestCase
{
    use RefreshDatabase;

    public function test_regular_user_cannot_access_admin_routes(): void
    {
        // Arrange
        $regularUser = User::factory()->create(['role' => 'user']);

        // Act
        $response = $this->actingAs($regularUser)
            ->get('/api/v1/admin/users');

        // Assert
        $response->assertStatus(403);
    }

    public function test_user_cannot_access_another_users_data(): void
    {
        // Arrange
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        $customer = Customer::factory()->create([
            'user_id' => $user2->id
        ]);

        // Act: user1 tries to access user2's customer data
        $response = $this->actingAs($user1)
            ->getJson("/api/v1/customers/{$customer->id}");

        // Assert
        $response->assertStatus(403);
    }
}
                </pre>
            </div>
        </section>
    </section>

    <section id="frameworks" class="manual-section">
        <h2>3. Frameworks e Ferramentas</h2>
        <p>Esta seção detalha os frameworks e ferramentas que utilizamos para implementação de testes em nosso projeto
            Laravel 12.</p>

        <section id="phpunit">
            <h3>3.1. PHPUnit</h3>
            <p>PHPUnit é o framework de teste padrão para PHP e vem integrado com o Laravel 12. É nossa principal
                ferramenta para testes unitários e de integração.</p>

            <div class="best-practice">
                <h4>Prós do PHPUnit</h4>
                <ul>
                    <li>Integração nativa com Laravel 12</li>
                    <li>Ampla documentação e comunidade</li>
                    <li>Suporte extensivo para mocks e stubs</li>
                    <li>Assertions poderosas para diversos cenários</li>
                    <li>Facilidade de integração com ferramentas de cobertura de código</li>
                    <li>Suporte completo para PHP 8.3 e tipos de retorno</li>
                </ul>
            </div>

            <div class="example">
                <h4>Estrutura Básica de um Teste PHPUnit em Nosso Projeto</h4>
                <pre>
namespace Tests\Unit\Services;

use PHPUnit\Framework\TestCase;
use App\Services\PaymentService;
use App\Exceptions\PaymentException;

class PaymentServiceTest extends TestCase
{
    protected PaymentService $paymentService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->paymentService = new PaymentService();
    }

    public function test_calculates_payment_fee_correctly(): void
    {
        // Arrange
        $amount = 100;
        $paymentMethod = 'credit_card';

        // Act
        $result = $this->paymentService->calculateFee($amount, $paymentMethod);

        // Assert
        $this->assertEquals(2.5, $result); // 2.5% para cartão de crédito
    }

    public function test_throws_exception_for_invalid_payment_method(): void
    {
        $this->expectException(PaymentException::class);
        $this->expectExceptionMessage('Método de pagamento inválido');
        
        $this->paymentService->calculateFee(100, 'invalid_method');
    }
}
            </pre>
            </div>
        </section>

        <section id="pest">
            <h3>3.2. Pest</h3>
            <p>Pest é um framework de teste elegante construído sobre o PHPUnit, que adotamos recentemente para novos
                testes devido à sua sintaxe mais expressiva e legível.</p>

            <div class="best-practice">
                <h4>Prós do Pest</h4>
                <ul>
                    <li>Sintaxe mais limpa e expressiva</li>
                    <li>Compatível com todas as funcionalidades do PHPUnit</li>
                    <li>Melhor legibilidade de testes</li>
                    <li>Expectativas encadeáveis fluentes</li>
                    <li>Hooks de teste mais intuitivos</li>
                    <li>Integração perfeita com Laravel 12</li>
                    <li>Suporte para arquivos de teste com tipagem estrita</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste com Pest em Nosso Projeto</h4>
                <pre>
// tests/Feature/OrderProcessingTest.php

use App\Models\User;
use App\Models\Product;
use App\Services\OrderService;
use function Pest\Laravel\actingAs;

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->product = Product::factory()->create(['price' => 100.00]);
    $this->orderService = app(OrderService::class);
});

test('usuário pode criar um pedido com produtos válidos', function () {
    // Arrange
    $orderData = [
        'items' => [
            [
                'product_id' => $this->product->id,
                'quantity' => 2
            ]
        ]
    ];

    // Act
    $order = $this->orderService->createOrder($this->user->id, $orderData);

    // Assert
    expect($order)->toHaveKey('id')
        ->and($order->total)->toBe(200.00)
        ->and($order->user_id)->toBe($this->user->id);
        
    expect($order->items)->toHaveCount(1);
    expect($order->items[0]->product_id)->toBe($this->product->id);
});

test('pedido não pode ser criado com produto inexistente', function () {
    $orderData = [
        'items' => [
            [
                'product_id' => 9999, // ID inexistente
                'quantity' => 1
            ]
        ]
    ];

    expect(fn() => $this->orderService->createOrder($this->user->id, $orderData))
        ->toThrow(Exception::class, 'Produto não encontrado');
});

test('pedido não pode ser criado com quantidade zero ou negativa', function ($quantity) {
    $orderData = [
        'items' => [
            [
                'product_id' => $this->product->id,
                'quantity' => $quantity
            ]
        ]
    ];

    expect(fn() => $this->orderService->createOrder($this->user->id, $orderData))
        ->toThrow(Exception::class, 'Quantidade inválida');
})->with([0, -1]);
            </pre>
            </div>

            <div class="note">
                <p>Em nosso projeto, estamos gradualmente migrando de PHPUnit para Pest em novos testes, mantendo os
                    testes existentes em PHPUnit. Ambos os frameworks coexistem sem problemas.</p>
            </div>
        </section>

        <section id="outras-ferramentas">
            <h3>3.3. Outras Ferramentas Úteis</h3>

            <div class="tools-list">
                <ul>
                    <li>
                        <strong>Laravel Pint</strong>
                        <p>Ferramenta de estilo de código baseada em PHP-CS-Fixer que ajuda a manter a consistência do
                            código, incluindo nos testes.</p>
                        <pre>./vendor/bin/pint --test</pre>
                    </li>
                    <li>
                        <strong>PHPStan</strong>
                        <p>Analisador estático de código que encontra erros sem executar o código. Configuramos para
                            nível 5 em nosso projeto.</p>
                        <pre>./vendor/bin/phpstan analyse app tests</pre>
                    </li>
                    <li>
                        <strong>Larastan</strong>
                        <p>Extensão do PHPStan específica para Laravel, que usamos para análise mais precisa do código
                            Laravel.</p>
                        <pre>./vendor/bin/phpstan analyse --memory-limit=2G</pre>
                    </li>
                    <li>
                        <strong>Mockery</strong>
                        <p>Framework de mock para criar objetos simulados em testes, especialmente útil para isolar
                            componentes.</p>
                        <pre>$mock = Mockery::mock(PaymentGateway::class);</pre>
                    </li>
                    <li>
                        <strong>Faker</strong>
                        <p>Biblioteca para gerar dados falsos para testes, que configuramos com localização pt_BR para
                            dados mais realistas.</p>
                        <pre>$faker = \Faker\Factory::create('pt_BR');</pre>
                    </li>
                    <li>
                        <strong>k6</strong>
                        <p>Ferramenta de teste de carga que utilizamos para testes de performance da API.</p>
                        <pre>k6 run tests/performance/api_load_test.js</pre>
                    </li>
                </ul>
            </div>

            <div class="checklist-group">
                <h4>Ferramentas por Tipo de Teste</h4>
                <ul>
                    <li>✅ <strong>Testes Unitários:</strong> PHPUnit, Pest, Mockery</li>
                    <li>✅ <strong>Testes de Integração:</strong> PHPUnit, Pest, Laravel TestCase</li>
                    <li>✅ <strong>Testes de API:</strong> PHPUnit, Pest, Laravel HTTP Testing</li>
                    <li>✅ <strong>Testes de Browser:</strong> Laravel Dusk (apenas para fluxos críticos)</li>
                    <li>✅ <strong>Testes de Performance:</strong> k6, Artillery</li>
                    <li>✅ <strong>Análise Estática:</strong> PHPStan, Larastan, Laravel Pint</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="config-ambiente" class="manual-section">
        <h2>4. Configuração do Ambiente de Testes</h2>
        <p>Esta seção descreve como configurar adequadamente o ambiente para executar testes em nosso projeto Laravel
            12.</p>

        <section id="config-basica">
            <h3>4.1. Configuração Básica</h3>
            <p>O Laravel 12 já vem com uma configuração básica para testes, mas fizemos alguns ajustes específicos para
                otimizar o ambiente de teste do nosso projeto.</p>

            <div class="example">
                <h4>Arquivo phpunit.xml do Nosso Projeto</h4>
                <pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         cacheDirectory=".phpunit.cache"
>
    &lt;testsuites>
        &lt;testsuite name="Unit">
            &lt;directory>tests/Unit&lt;/directory>
        &lt;/testsuite>
        &lt;testsuite name="Feature">
            &lt;directory>tests/Feature&lt;/directory>
        &lt;/testsuite>
        &lt;testsuite name="Integration">
            &lt;directory>tests/Integration&lt;/directory>
        &lt;/testsuite>
        &lt;testsuite name="Security">
            &lt;directory>tests/Security&lt;/directory>
        &lt;/testsuite>
    &lt;/testsuites>
    &lt;source>
        &lt;include>
            &lt;directory>app&lt;/directory>
        &lt;/include>
    &lt;/source>
    &lt;php>
        &lt;env name="APP_ENV" value="testing"/>
        &lt;env name="BCRYPT_ROUNDS" value="4"/>
        &lt;env name="CACHE_DRIVER" value="array"/>
        &lt;env name="DB_CONNECTION" value="sqlite"/>
        &lt;env name="DB_DATABASE" value=":memory:"/>
        &lt;env name="MAIL_MAILER" value="array"/>
        &lt;env name="QUEUE_CONNECTION" value="sync"/>
        &lt;env name="SESSION_DRIVER" value="array"/>
        &lt;env name="TELESCOPE_ENABLED" value="false"/>
        &lt;env name="PAYMENT_GATEWAY" value="mock"/>
    &lt;/php>
&lt;/phpunit>
            </pre>
            </div>

            <div class="best-practice">
                <h4>Configurações Específicas do Nosso Projeto</h4>
                <ul>
                    <li>Usamos SQLite em memória para testes mais rápidos</li>
                    <li>Configuramos um gateway de pagamento mock para testes</li>
                    <li>Desativamos serviços externos e usamos implementações em memória</li>
                    <li>Organizamos os testes em suítes específicas (Unit, Feature, Integration, Security)</li>
                    <li>Reduzimos o número de rounds de bcrypt para acelerar os testes</li>
                    <li>Desativamos o Telescope durante os testes para melhorar a performance</li>
                </ul>
            </div>
        </section>

        <section id="banco-dados-teste">
            <h3>4.2. Banco de Dados para Testes</h3>
            <p>A configuração adequada do banco de dados é crucial para testes eficientes e isolados.</p>

            <div class="best-practice">
                <h4>Nossa Configuração de Banco de Dados para Testes</h4>
                <ul>
                    <li><strong>Ambiente Local:</strong> SQLite em memória para testes rápidos</li>
                    <li><strong>CI/CD:</strong> MySQL em container Docker para testes mais fiéis ao ambiente de produção
                    </li>
                    <li><strong>Traits Utilizadas:</strong> RefreshDatabase para maioria dos testes,
                        DatabaseTransactions para testes específicos</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Configuração para Testes com Banco de Dados</h4>
                <pre>
namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;

class OrderTest extends TestCase
{
    use RefreshDatabase;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed dados necessários para todos os testes desta classe
        $this->seed(\Database\Seeders\PaymentMethodsSeeder::class);
        $this->seed(\Database\Seeders\TaxRatesSeeder::class);
    }
    
    public function test_order_can_be_created_with_valid_data(): void
    {
        // Arrange
        $user = User::factory()->create();
        
        // Act & Assert
        $this->actingAs($user)
            ->postJson('/api/v1/orders', [
                'items' => [
                    ['product_id' => 1, 'quantity' => 2]
                ],
                'payment_method_id' => 1
            ])
            ->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id', 'total', 'status', 'created_at'
                ]
            ]);
            
        $this->assertDatabaseHas('orders', [
            'user_id' => $user->id,
            'status' => 'pending'
        ]);
    }
}
            </pre>
            </div>

            <div class="tip">
                <p>Para testes que exigem um grande volume de dados ou estruturas complexas, criamos seeders específicos
                    para testes que podem ser executados conforme necessário.</p>
            </div>
        </section>

        <section id="factories-seeders">
            <h3>4.3. Factories e Seeders</h3>
            <p>Factories e seeders são essenciais para criar dados de teste consistentes e realistas em nosso projeto.
            </p>

            <div class="example">
                <h4>Exemplo de Factory em Nosso Projeto</h4>
                <pre>
namespace Database\Factories;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->productName(),
            'description' => $this->faker->paragraph(),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'stock_quantity' => $this->faker->numberBetween(0, 100),
            'category_id' => Category::factory(),
            'is_active' => true,
            'sku' => $this->faker->unique()->ean13(),
        ];
    }

    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock_quantity' => 0,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function promotional(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_promotional' => true,
            'promotional_price' => $this->faker->randomFloat(2, 5, $attributes['price'] * 0.8),
        ]);
    }
}
            </pre>
            </div>

            <div class="example">
                <h4>Usando Factories em Testes</h4>
                <pre>
public function test_promotional_products_are_displayed_with_discount(): void
{
    // Criar produtos normais e promocionais
    Product::factory()->count(3)->create();
    $promotional = Product::factory()->promotional()->create();
    
    // Testar a API
    $response = $this->getJson('/api/v1/products/promotional');
    
    $response->assertStatus(200)
        ->assertJsonCount(1, 'data')
        ->assertJsonPath('data.0.id', $promotional->id)
        ->assertJsonPath('data.0.discount_percentage', function($discountPercentage) use ($promotional) {
            // Verifica se o desconto está calculado corretamente
            $expectedDiscount = round((1 - $promotional->promotional_price / $promotional->price) * 100);
            return abs($discountPercentage - $expectedDiscount) < 0.01;
        });
}
            </pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Factories em Nosso Projeto</h4>
                <ul>
                    <li>Crie factories para todos os modelos principais</li>
                    <li>Use estados para variações comuns (inactive, promotional, featured, etc.)</li>
                    <li>Defina relacionamentos nas factories</li>
                    <li>Use dados realistas com Faker configurado para pt_BR</li>
                    <li>Mantenha as factories atualizadas quando o modelo mudar</li>
                    <li>Crie seeders específicos para testes que exigem dados complexos</li>
                </ul>
            </div>
        </section>

        <section id="mocks-stubs">
            <h3>4.4. Mocks e Stubs</h3>
            <p>Mocks e stubs são essenciais para isolar o código sendo testado de suas dependências externas,
                especialmente importante em nosso projeto que integra com diversos serviços externos.</p>

            <div class="example">
                <h4>Exemplo de Mock com Mockery em Nosso Projeto</h4>
                <pre>
public function test_payment_service_processes_payment_correctly(): void
{
    // Criar mock do gateway de pagamento
    $paymentGateway = Mockery::mock(PaymentGatewayInterface::class);
    
    // Definir expectativa - o método processPayment deve ser chamado uma vez
    // com os parâmetros corretos e deve retornar um ID de transação
    $paymentGateway->shouldReceive('processPayment')
        ->once()
        ->with(Mockery::on(function($paymentData) {
            return $paymentData['amount'] == 100.00 &&
                   $paymentData['currency'] == 'BRL' &&
                   isset($paymentData['card_number']);
        }))
        ->andReturn([
            'transaction_id' => 'txn_123456',
            'status' => 'approved'
        ]);
    
    // Injetar o mock no container
    $this->app->instance(PaymentGatewayInterface::class, $paymentGateway);
    
    // Criar ordem
    $order = Order::factory()->create(['total' => 100.00]);
    
    // Executar o serviço que estamos testando
    $paymentService = app(PaymentService::class);
    $result = $paymentService->processOrderPayment($order, [
        'card_number' => '****************',
        'expiry_month' => '12',
        'expiry_year' => '2025',
        'cvv' => '123'
    ]);
    
    // Verificar resultado
    $this->assertTrue($result['success']);
    $this->assertEquals('txn_123456', $result['transaction_id']);
    
    // Verificar que a ordem foi atualizada
    $this->assertEquals('paid', $order->fresh()->status);
}
            </pre>
            </div>

            <div class="example">
                <h4>Exemplo de Fake em Nosso Projeto</h4>
                <pre>
public function test_notification_is_sent_when_order_is_shipped(): void
{
    // Usar o fake de Notification do Laravel
    Notification::fake();
    
    // Criar usuário e pedido
    $user = User::factory()->create();
    $order = Order::factory()->create([
        'user_id' => $user->id,
        'status' => 'processing'
    ]);
    
    // Executar o método que deve enviar a notificação
    $orderService = app(OrderService::class);
    $orderService->markAsShipped($order->id, 'BR1234567890');
    
    // Verificar que a notificação foi enviada
    Notification::assertSentTo(
        $user,
        OrderShippedNotification::class,
        function ($notification) use ($order) {
            return $notification->order->id === $order->id &&
                   $notification->trackingNumber === 'BR1234567890';
        }
    );
    
    // Verificar que o pedido foi atualizado
    $this->assertEquals('shipped', $order->fresh()->status);
    $this->assertEquals('BR1234567890', $order->fresh()->tracking_number);
}
            </pre>
            </div>

            <div class="best-practice">
                <h4>Quando Usar Mocks vs. Fakes vs. Objetos Reais em Nosso Projeto</h4>
                <ul>
                    <li><strong>Mocks:</strong> Para serviços externos como gateways de pagamento, APIs de frete, e
                        serviços de email</li>
                    <li><strong>Stubs:</strong> Para simular respostas específicas de componentes internos</li>
                    <li><strong>Fakes:</strong> Para serviços do Laravel como Mail, Notification, Event, etc.</li>
                    <li><strong>Objetos Reais:</strong> Para componentes simples ou quando testamos a integração real
                        entre componentes</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="escrevendo-testes" class="manual-section">
        <h2>5. Escrevendo Testes Eficazes</h2>
        <p>Esta seção aborda as melhores práticas para escrever testes claros, eficazes e de fácil manutenção em nosso
            projeto.</p>

        <section id="estrutura-teste">
            <h3>5.1. Estrutura de um Bom Teste</h3>
            <p>Em nosso projeto, seguimos o padrão AAA (Arrange-Act-Assert) para estruturar todos os testes de forma
                consistente.</p>

            <div class="example">
                <h4>Exemplo de Estrutura AAA em Nosso Projeto</h4>
                <pre>
public function test_product_can_be_added_to_cart(): void
{
    // Arrange (Given) - Configurar o cenário de teste
    $user = User::factory()->create();
    $product = Product::factory()->create([
        'price' => 50.00,
        'stock_quantity' => 10
    ]);
    
    $cartService = app(CartService::class);
    
    // Act (When) - Executar a ação que está sendo testada
    $result = $cartService->addToCart($user->id, $product->id, 2);
    
    // Assert (Then) - Verificar o resultado esperado
    $this->assertTrue($result);
    
    $cart = $cartService->getCart($user->id);
    $this->assertCount(1, $cart['items']);
    $this->assertEquals($product->id, $cart['items'][0]['product_id']);
    $this->assertEquals(2, $cart['items'][0]['quantity']);
    $this->assertEquals(100.00, $cart['total']);
}
            </pre>
            </div>

            <div class="best-practice">
                <h4>Características de um Bom Teste em Nosso Projeto</h4>
                <ul>
                    <li><strong>Foco:</strong> Cada teste verifica uma única funcionalidade ou comportamento</li>
                    <li><strong>Independência:</strong> Testes não dependem do estado de outros testes</li>
                    <li><strong>Repetibilidade:</strong> Testes produzem o mesmo resultado em execuções sucessivas</li>
                    <li><strong>Clareza:</strong> A intenção do teste é clara pelo seu nome e estrutura</li>
                    <li><strong>Rapidez:</strong> Testes executam rapidamente para feedback imediato</li>
                    <li><strong>Robustez:</strong> Testes não são frágeis a pequenas mudanças na implementação</li>
                    <li><strong>Documentação:</strong> Testes servem como documentação viva do comportamento esperado
                    </li>
                </ul>
            </div>
        </section>

        <section id="nomeando-testes">
            <h3>5.2. Nomeando Testes</h3>
            <p>Nomes de testes claros e descritivos são essenciais para entender rapidamente o que está sendo testado e
                o comportamento esperado.</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Ruim</th>
                            <th>Bom</th>
                            <th>Explicação</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>test_checkout()</code></td>
                            <td><code>test_checkout_calculates_total_with_taxes_and_shipping()</code></td>
                            <td>Descreve claramente o que está sendo verificado</td>
                        </tr>
                        <tr>
                            <td><code>test_validation()</code></td>
                            <td><code>test_order_creation_fails_with_invalid_payment_method()</code></td>
                            <td>Especifica o cenário e o resultado esperado</td>
                        </tr>
                        <tr>
                            <td><code>test_admin_feature()</code></td>
                            <td><code>test_only_admin_can_access_sales_report_page()</code></td>
                            <td>Descreve a restrição de acesso específica</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="best-practice">
                <h4>Convenções de Nomenclatura em Nosso Projeto</h4>
                <ul>
                    <li>Use o prefixo <code>test_</code> para métodos de teste em PHPUnit</li>
                    <li>Em Pest, use descrições claras sem o prefixo <code>test_</code></li>
                    <li>Siga o padrão <code>test_[o que está sendo testado]_[condições]_[resultado esperado]()</code>
                    </li>
                    <li>Use verbos que indicam ação ou verificação: can, should, fails, returns, etc.</li>
                    <li>Seja específico sobre as condições e resultados</li>
                    <li>Mantenha consistência em todo o projeto</li>
                </ul>
            </div>
        </section>

        <section id="assertions">
            <h3>5.3. Assertions Eficazes</h3>
            <p>Assertions (afirmações) são o coração de um teste, verificando se o comportamento real corresponde ao
                esperado.</p>

            <div class="example">
                <h4>Assertions Comuns em Nosso Projeto</h4>
                <pre>
// Assertions básicas
$this->assertTrue($result);
$this->assertEquals(5, $count);
$this->assertSame($expectedObject, $actualObject);
$this->assertInstanceOf(User::class, $user);

// Assertions de coleção
$this->assertCount(3, $collection);
$this->assertContains('expected', $array);
$this->assertEmpty($collection);

// Assertions de banco de dados
$this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
$this->assertDatabaseMissing('posts', ['title' => 'Deleted Post']);
$this->assertDatabaseCount('comments', 5);

// Assertions de resposta HTTP
$response->assertStatus(200);
$response->assertRedirect('/dashboard');
$response->assertJson(['status' => 'success']);
$response->assertJsonStructure(['data' => ['*' => ['id', 'name']]]);
$response->assertSee('Bem-vindo');
$response->assertDontSee('Erro');

// Assertions de autenticação
$this->assertAuthenticated();
$this->assertGuest();
$this->assertAuthenticatedAs($user);

// Assertions de validação
$response->assertValid(['name', 'email']);
$response->assertInvalid(['password' => 'O campo senha é obrigatório.']);

// Assertions com Pest
expect($result)->toBeTrue();
expect($user)->toBeInstanceOf(User::class);
expect($array)->toHaveCount(3);
expect($response->status())->toBe(200);
            </pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Assertions em Nosso Projeto</h4>
                <ul>
                    <li>Use assertions específicas em vez de genéricas quando possível</li>
                    <li>Verifique apenas o que é relevante para o teste atual</li>
                    <li>Prefira assertions que fornecem mensagens de erro claras</li>
                    <li>Use múltiplas assertions quando necessário, mas mantenha o foco</li>
                    <li>Para APIs, sempre verifique o código de status e a estrutura da resposta</li>
                    <li>Para operações de banco de dados, verifique se os registros foram criados/atualizados
                        corretamente</li>
                    <li>Para testes de autorização, sempre verifique tanto o acesso permitido quanto o negado</li>
                </ul>
            </div>
        </section>

        <section id="data-providers">
            <h3>5.4. Data Providers</h3>
            <p>Data providers permitem executar o mesmo teste com diferentes conjuntos de dados, reduzindo duplicação de
                código e aumentando a cobertura de casos de teste.</p>

            <div class="example">
                <h4>Exemplo de Data Provider em PHPUnit em Nosso Projeto</h4>
                <pre>
namespace Tests\Unit\Services;

use App\Services\TaxCalculatorService;
use PHPUnit\Framework\TestCase;

class TaxCalculatorServiceTest extends TestCase
{
    /**
     * @dataProvider taxRateProvider
     */
    public function test_it_calculates_tax_correctly_for_different_regions(
        string $region, 
        float $amount, 
        float $expectedTax
    ): void {
        $calculator = new TaxCalculatorService();
        $result = $calculator->calculateRegionalTax($amount, $region);
        $this->assertEquals($expectedTax, $result, "Taxa incorreta para região {$region}");
    }
    
    public static function taxRateProvider(): array
    {
        return [
            'São Paulo' => ['SP', 100.00, 18.00],
            'Rio de Janeiro' => ['RJ', 100.00, 20.00],
            'Minas Gerais' => ['MG', 100.00, 17.00],
            'Região Norte' => ['AM', 100.00, 12.00],
            'Região Nordeste' => ['BA', 100.00, 15.00],
        ];
    }
    
    /**
     * @dataProvider discountProvider
     */
    public function test_it_applies_correct_discount_based_on_order_total(
        float $orderTotal, 
        float $expectedDiscountPercentage
    ): void {
        $calculator = new TaxCalculatorService();
        $discount = $calculator->calculateDiscount($orderTotal);
        $expectedDiscount = $orderTotal * ($expectedDiscountPercentage / 100);
        $this->assertEquals($expectedDiscount, $discount, "Desconto incorreto para total {$orderTotal}");
    }
    
    public static function discountProvider(): array
    {
        return [
            'Sem desconto' => [99.99, 0],
            'Desconto pequeno' => [100.00, 5],
            'Desconto médio' => [500.00, 10],
            'Desconto grande' => [1000.00, 15],
            'Desconto máximo' => [5000.00, 20],
        ];
    }
}
        </pre>
            </div>

            <div class="example">
                <h4>Exemplo de Data Provider em Pest em Nosso Projeto</h4>
                <pre>
// tests/Unit/PaymentValidatorTest.php

use App\Services\PaymentValidator;

it('valida corretamente diferentes números de cartão de crédito', function (string $cardNumber, bool $shouldBeValid) {
    $validator = new PaymentValidator();
    $result = $validator->validateCreditCard($cardNumber);
    
    expect($result)->toBe($shouldBeValid);
})->with([
    'Visa válido' => ['****************', true],
    'Mastercard válido' => ['****************', true],
    'Amex válido' => ['***************', true],
    'Número inválido' => ['1234567890123456', false],
    'Número muito curto' => ['41111', false],
    'Com letras' => ['4111111a1111111', false],
    'Número vazio' => ['', false],
]);

it('valida corretamente diferentes formatos de CPF', function (string $cpf, bool $shouldBeValid) {
    $validator = new PaymentValidator();
    $result = $validator->validateCpf($cpf);
    
    expect($result)->toBe($shouldBeValid);
})->with([
    'CPF válido sem formatação' => ['12345678909', true],
    'CPF válido com formatação' => ['123.456.789-09', true],
    'CPF inválido' => ['11111111111', false],
    'CPF com tamanho incorreto' => ['123456', false],
    'CPF com letras' => ['123456789ab', false],
]);
        </pre>
            </div>

            <div class="best-practice">
                <h4>Quando Usar Data Providers em Nosso Projeto</h4>
                <ul>
                    <li>Para testar a mesma funcionalidade com diferentes entradas</li>
                    <li>Para testar casos limites e valores extremos</li>
                    <li>Para testar validações com diferentes tipos de dados</li>
                    <li>Para testar regras de negócio que variam conforme parâmetros (impostos, descontos, etc.)</li>
                    <li>Para testar formatação e parsing de dados em diferentes formatos</li>
                    <li>Para reduzir duplicação de código de teste</li>
                </ul>
            </div>

            <div class="tip">
                <p>Em nosso projeto, preferimos usar nomes descritivos para os casos de teste em data providers, em vez
                    de índices numéricos, para facilitar a identificação de falhas específicas.</p>
            </div>
        </section>
    </section>

    <section id="testes-dominio" class="manual-section">
        <h2>6. Testes Específicos do Domínio</h2>
        <p>Esta seção aborda os testes específicos para as regras de negócio e funcionalidades do nosso domínio de
            aplicação.</p>

        <section id="testes-autenticacao">
            <h3>6.1. Testes de Autenticação e Autorização</h3>
            <p>A autenticação e autorização são aspectos críticos do nosso sistema e exigem testes abrangentes.</p>

            <div class="best-practice">
                <h4>O que Testar em Autenticação</h4>
                <ul>
                    <li>Login com credenciais válidas</li>
                    <li>Login com credenciais inválidas</li>
                    <li>Registro de novos usuários</li>
                    <li>Recuperação de senha</li>
                    <li>Bloqueio após múltiplas tentativas falhas</li>
                    <li>Expiração de tokens</li>
                    <li>Refresh de tokens</li>
                    <li>Logout</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Autenticação em Nosso Projeto</h4>
                <pre>
namespace Tests\Feature\Auth;

use App\Models\User;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_login_with_valid_credentials(): void
    {
        // Arrange
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123')
        ]);

        // Act
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'token',
                    'user' => [
                        'id',
                        'name',
                        'email'
                    ]
                ]
            ]);
    }

    public function test_user_cannot_login_with_invalid_credentials(): void
    {
        // Arrange
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123')
        ]);

        // Act
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong_password'
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJson([
                'message' => 'Credenciais inválidas'
            ]);
    }

    public function test_user_is_locked_after_five_failed_attempts(): void
    {
        // Arrange
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123')
        ]);

        // Act - 5 tentativas falhas
        for ($i = 0; $i < 5; $i++) {
            $this->postJson('/api/v1/auth/login', [
                'email' => '<EMAIL>',
                'password' => 'wrong_password'
            ]);
        }

        // Tenta com senha correta após bloqueio
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // Assert
        $response->assertStatus(423) // Locked
            ->assertJson([
                'message' => 'Conta bloqueada temporariamente'
            ]);
    }
}
            </pre>
            </div>

            <div class="best-practice">
                <h4>O que Testar em Autorização</h4>
                <ul>
                    <li>Acesso a recursos protegidos com permissões adequadas</li>
                    <li>Negação de acesso a recursos sem permissões</li>
                    <li>Verificação de políticas de acesso para diferentes perfis</li>
                    <li>Acesso baseado em propriedade de recursos</li>
                    <li>Escopo de tokens de API</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Autorização em Nosso Projeto</h4>
                <pre>
namespace Tests\Feature\Auth;

use App\Models\User;
use App\Models\Order;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AuthorizationTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_access_all_orders(): void
    {
        // Arrange
        $admin = User::factory()->create(['role' => 'admin']);
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        $order1 = Order::factory()->create(['user_id' => $user1->id]);
        $order2 = Order::factory()->create(['user_id' => $user2->id]);
        
        // Act
        Sanctum::actingAs($admin, ['*']);
        $response = $this->getJson('/api/v1/admin/orders');
        
        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(2, 'data')
            ->assertJsonPath('data.0.id', $order1->id)
            ->assertJsonPath('data.1.id', $order2->id);
    }
    
    public function test_user_can_only_access_own_orders(): void
    {
        // Arrange
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        $order1 = Order::factory()->create(['user_id' => $user1->id]);
        $order2 = Order::factory()->create(['user_id' => $user2->id]);
        
        // Act - user1 tenta acessar seus pedidos
        Sanctum::actingAs($user1, ['*']);
        $response1 = $this->getJson('/api/v1/orders');
        
        // Assert - user1 vê apenas seus pedidos
        $response1->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $order1->id);
            
        // Act - user1 tenta acessar pedido do user2
        $response2 = $this->getJson("/api/v1/orders/{$order2->id}");
        
        // Assert - acesso negado
        $response2->assertStatus(403);
    }
}
            </pre>
            </div>
        </section>

        <section id="testes-regras-negocio">
            <h3>6.2. Testes de Regras de Negócio</h3>
            <p>As regras de negócio específicas do nosso domínio precisam ser testadas minuciosamente para garantir
                o comportamento correto do sistema.</p>

            <div class="best-practice">
                <h4>Regras de Negócio Críticas em Nosso Projeto</h4>
                <ul>
                    <li>Cálculo de impostos por região</li>
                    <li>Aplicação de descontos progressivos</li>
                    <li>Verificação de estoque</li>
                    <li>Validação de pagamentos</li>
                    <li>Cálculo de frete</li>
                    <li>Políticas de devolução</li>
                    <li>Regras de fidelidade e pontos</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Regra de Negócio em Nosso Projeto</h4>
                <pre>
namespace Tests\Unit\Services;

use App\Services\DiscountService;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DiscountServiceTest extends TestCase
{
    use RefreshDatabase;
    
    private DiscountService $discountService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->discountService = app(DiscountService::class);
    }
    
    public function test_first_time_customer_gets_welcome_discount(): void
    {
        // Arrange
        $user = User::factory()->create();
        $product = Product::factory()->create(['price' => 100.00]);
        
        // Act
        $discount = $this->discountService->calculateDiscount($user->id, [$product->id => 1]);
        
        // Assert
        $this->assertEquals(10.00, $discount); // 10% de desconto para primeira compra
    }
    
    public function test_loyal_customer_gets_higher_discount(): void
    {
        // Arrange
        $user = User::factory()->create();
        
        // Cria 5 pedidos anteriores para o usuário
        Order::factory()->count(5)->create([
            'user_id' => $user->id,
            'status' => 'completed'
        ]);
        
        $product = Product::factory()->create(['price' => 100.00]);
        
        // Act
        $discount = $this->discountService->calculateDiscount($user->id, [$product->id => 1]);
        
        // Assert
        $this->assertEquals(15.00, $discount); // 15% de desconto para cliente fiel
    }
    
    public function test_bulk_purchase_gets_volume_discount(): void
    {
        // Arrange
        $user = User::factory()->create();
        $product = Product::factory()->create(['price' => 100.00]);
        
        // Act - compra de 10 unidades
        $discount = $this->discountService->calculateDiscount($user->id, [$product->id => 10]);
        
        // Assert
        $this->assertEquals(80.00, $discount); // 8% de desconto por unidade em compra em volume
    }
    
    public function test_promotional_products_cannot_get_additional_discounts(): void
    {
        // Arrange
        $user = User::factory()->create();
        $product = Product::factory()->create([
            'price' => 100.00,
            'is_promotional' => true,
            'promotional_price' => 80.00
        ]);
        
        // Act
        $discount = $this->discountService->calculateDiscount($user->id, [$product->id => 1]);
        
        // Assert
        $this->assertEquals(0.00, $discount); // Sem desconto adicional para produtos promocionais
    }
}
            </pre>
            </div>

            <div class="tip">
                <p>Para regras de negócio complexas, é útil criar diagramas de decisão ou tabelas de decisão para
                    identificar todos os casos de teste necessários.</p>
            </div>
        </section>

        <section id="testes-integracao-externa">
            <h3>6.3. Testes de Integração Externa</h3>
            <p>Nosso sistema integra com diversos serviços externos que precisam ser testados adequadamente.</p>

            <div class="best-practice">
                <h4>Integrações Externas em Nosso Projeto</h4>
                <ul>
                    <li>Gateway de pagamento</li>
                    <li>Serviços de cálculo de frete</li>
                    <li>APIs de consulta de CEP</li>
                    <li>Serviços de notificação (SMS, WhatsApp)</li>
                    <li>Integrações com marketplaces</li>
                    <li>Serviços de verificação de fraude</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Integração Externa com Mock</h4>
                <pre>
namespace Tests\Integration;

use App\Services\ShippingService;
use App\Services\External\FreteRapidoClient;
use Tests\TestCase;
use Mockery;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ShippingServiceTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_shipping_calculation_integrates_correctly_with_external_api(): void
    {
        // Arrange - Mock da API externa
        $apiResponse = [
            'shipping_options' => [
                [
                    'name' => 'PAC',
                    'price' => 25.50,
                    'days' => 5
                ],
                [
                    'name' => 'SEDEX',
                    'price' => 45.30,
                    'days' => 1
                ]
            ]
        ];
        
        $mockClient = Mockery::mock(FreteRapidoClient::class);
        $mockClient->shouldReceive('calculateShipping')
            ->once()
            ->with(
                Mockery::on(function($data) {
                    return $data['zipcode'] === '01001000' &&
                           $data['weight'] === 1.5 &&
                           $data['length'] === 20 &&
                           $data['width'] === 15 &&
                           $data['height'] === 10;
                })
            )
            ->andReturn($apiResponse);
            
        $this->app->instance(FreteRapidoClient::class, $mockClient);
        
        // Act
        $shippingService = app(ShippingService::class);
        $options = $shippingService->getShippingOptions('01001000', [
            'weight' => 1.5,
            'dimensions' => [
                'length' => 20,
                'width' => 15,
                'height' => 10
            ]
        ]);
        
        // Assert
        $this->assertCount(2, $options);
        $this->assertEquals('PAC', $options[0]['name']);
        $this->assertEquals(25.50, $options[0]['price']);
        $this->assertEquals('SEDEX', $options[1]['name']);
        $this->assertEquals(45.30, $options[1]['price']);
    }
    
    public function test_shipping_service_handles_api_errors_gracefully(): void
    {
        // Arrange - Mock que simula erro na API
        $mockClient = Mockery::mock(FreteRapidoClient::class);
        $mockClient->shouldReceive('calculateShipping')
            ->once()
            ->andThrow(new \Exception('API indisponível'));
            
        $this->app->instance(FreteRapidoClient::class, $mockClient);
        
        // Act
        $shippingService = app(ShippingService::class);
        $options = $shippingService->getShippingOptions('01001000', [
            'weight' => 1.5,
            'dimensions' => [
                'length' => 20,
                'width' => 15,
                'height' => 10
            ]
        ]);
        
        // Assert - deve retornar opções de frete padrão em caso de erro
        $this->assertCount(1, $options);
        $this->assertEquals('Frete Padrão', $options[0]['name']);
        $this->assertEquals(30.00, $options[0]['price']);
    }
}
            </pre>
            </div>

            <div class="best-practice">
                <h4>Estratégias para Testar Integrações Externas</h4>
                <ul>
                    <li>Use mocks para testes unitários e de integração</li>
                    <li>Crie ambientes de sandbox para testes mais realistas</li>
                    <li>Implemente testes de contrato para validar interfaces</li>
                    <li>Teste cenários de erro e timeout</li>
                    <li>Verifique o comportamento de fallback quando o serviço externo falha</li>
                    <li>Considere testes de integração reais em ambientes de staging</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="tdd" class="manual-section">
        <h2>7. Test-Driven Development (TDD)</h2>
        <p>TDD é uma metodologia de desenvolvimento que adotamos para partes críticas do nosso sistema, colocando os
            testes em primeiro lugar.</p>

        <section id="tdd-ciclo">
            <h3>7.1. O Ciclo do TDD</h3>
            <p>O TDD segue um ciclo simples conhecido como "Red-Green-Refactor":</p>

            <div class="best-practice">
                <ol>
                    <li><strong>Red:</strong> Escreva um teste que falha para a funcionalidade desejada</li>
                    <li><strong>Green:</strong> Implemente o código mínimo necessário para fazer o teste passar</li>
                    <li><strong>Refactor:</strong> Melhore o código mantendo os testes passando</li>
                </ol>
            </div>

            <div class="example">
                <h4>Exemplo de Ciclo TDD em Nosso Projeto</h4>
                <p><strong>1. Red:</strong> Escreva um teste que falha</p>
                <pre>
// tests/Unit/Services/CouponServiceTest.php
namespace Tests\Unit\Services;

use PHPUnit\Framework\TestCase;
use App\Services\CouponService;
use App\Exceptions\InvalidCouponException;

class CouponServiceTest extends TestCase
{
    public function test_it_validates_coupon_format(): void
    {
        $service = new CouponService();
        
        // Formato válido: PROMO-12345
        $this->assertTrue($service->isValidFormat('PROMO-12345'));
        
        // Formatos inválidos
        $this->assertFalse($service->isValidFormat('PROMO12345'));
        $this->assertFalse($service->isValidFormat('promo-12345'));
        $this->assertFalse($service->isValidFormat('PROMO-ABCDE'));
    }
    
    public function test_it_throws_exception_for_invalid_format(): void
    {
        $this->expectException(InvalidCouponException::class);
        $this->expectExceptionMessage('Formato de cupom inválido');
        
        $service = new CouponService();
        $service->validateCoupon('INVALID');
    }
}
            </pre>

                <p><strong>2. Green:</strong> Implemente o código mínimo para passar</p>
                <pre>
// app/Services/CouponService.php
namespace App\Services;

use App\Exceptions\InvalidCouponException;

class CouponService
{
    public function isValidFormat(string $coupon): bool
    {
        return (bool) preg_match('/^[A-Z]+-\d{5}$/', $coupon);
    }
    
    public function validateCoupon(string $coupon): bool
    {
        if (!$this->isValidFormat($coupon)) {
            throw new InvalidCouponException('Formato de cupom inválido');
        }
        
        return true;
    }
}
            </pre>

                <p><strong>3. Refactor:</strong> Melhore o código mantendo os testes passando</p>
                <pre>
// app/Services/CouponService.php (refatorado)
namespace App\Services;

use App\Exceptions\InvalidCouponException;
use App\Repositories\CouponRepository;

class CouponService
{
    private const COUPON_PATTERN = '/^[A-Z]+-\d{5}$/';
    
    public function __construct(
        private CouponRepository $couponRepository
    ) {}
    
    public function isValidFormat(string $coupon): bool
    {
        return (bool) preg_match(self::COUPON_PATTERN, $coupon);
    }
    
    public function validateCoupon(string $coupon): bool
    {
        if (!$this->isValidFormat($coupon)) {
            throw new InvalidCouponException('Formato de cupom inválido');
        }
        
        // Lógica adicional de validação pode ser adicionada aqui
        
        return true;
    }
}
            </pre>
            </div>
        </section>

        <section id="tdd-projeto">
            <h3>7.2. TDD no Nosso Projeto</h3>
            <p>Aplicamos TDD principalmente em componentes críticos do sistema e em correções de bugs.</p>

            <div class="best-practice">
                <h4>Áreas Onde Aplicamos TDD</h4>
                <ul>
                    <li>Regras de negócio complexas (cálculo de impostos, descontos, etc.)</li>
                    <li>Validações de entrada de dados</li>
                    <li>Processamento de pagamentos</li>
                    <li>Correções de bugs para evitar regressões</li>
                    <li>APIs públicas consumidas por outros sistemas</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de TDD para Correção de Bug</h4>
                <pre>
// 1. Escreva um teste que reproduz o bug
public function test_discount_is_not_applied_twice_on_promotional_items(): void
{
    // Arrange
    $product = Product::factory()->create([
        'price' => 100.00,
        'is_promotional' => true,
        'promotional_price' => 80.00
    ]);
    
    $cart = new ShoppingCart();
    $cart->add($product, 1);
    
    // Act
    $cart->applyDiscount(10); // 10% de desconto
    
    // Assert
    // O bug era: o desconto estava sendo aplicado sobre o preço original,
    // mesmo para produtos promocionais
    $this->assertEquals(80.00, $cart->total());
}

// 2. Corrija o código para fazer o teste passar
public function applyDiscount(int $percentage): void
{
    foreach ($this->items as $item) {
        // Correção: não aplicar desconto adicional em itens promocionais
        if (!$item->product->is_promotional) {
            $item->discount = $item->product->price * ($percentage / 100) * $item->quantity;
        }
    }
}
            </pre>
            </div>

            <div class="tip">
                <p>Para facilitar o TDD em nosso projeto, configuramos o comando
                    <code>php artisan test --watch</code> que monitora alterações nos arquivos e executa os testes
                    automaticamente, acelerando o ciclo de feedback.
                </p>
            </div>

            <div class="best-practice">
                <h4>Dicas para TDD Eficaz em Nosso Projeto</h4>
                <ul>
                    <li>Comece com testes pequenos e específicos</li>
                    <li>Escreva apenas o código necessário para passar no teste</li>
                    <li>Refatore regularmente para manter o código limpo</li>
                    <li>Use mocks para isolar o componente sendo testado</li>
                    <li>Mantenha o ciclo de feedback rápido</li>
                    <li>Não pule a fase de refatoração</li>
                    <li>Combine TDD com revisão de código para melhores resultados</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="execucao-testes" class="manual-section">
        <h2>8. Execução de Testes</h2>
        <p>Esta seção aborda as diferentes formas de executar testes em nosso projeto Laravel 12.</p>

        <section id="linha-comando">
            <h3>8.1. Execução via Linha de Comando</h3>
            <p>O Laravel 12 oferece várias opções para executar testes via linha de comando, que adaptamos para
                nosso projeto.</p>

            <div class="example">
                <h4>Comandos Básicos</h4>
                <pre>
# Executar todos os testes
php artisan test

# Executar testes em paralelo (recomendado para CI/CD)
php artisan test --parallel

# Executar um arquivo de teste específico
php artisan test tests/Feature/OrderTest.php

# Executar um método de teste específico
php artisan test --filter=test_order_can_be_created_with_valid_data

# Executar testes com saída detalhada
php artisan test --verbose

# Executar testes e parar no primeiro erro
php artisan test --stop-on-failure

# Executar testes com PHPUnit diretamente
./vendor/bin/phpunit

# Executar testes com Pest
./vendor/bin/pest
            </pre>
            </div>

            <div class="example">
                <h4>Comandos Personalizados para Nosso Projeto</h4>
                <pre>
# Executar apenas testes unitários
php artisan test --testsuite=Unit

# Executar apenas testes de API
php artisan test tests/Feature/Api/

# Executar testes de integração
php artisan test --testsuite=Integration

# Executar testes de segurança
php artisan test --testsuite=Security

# Executar testes com cobertura (requer Xdebug ou PCOV)
php artisan test --coverage --min=80

# Executar testes e gerar relatório HTML de cobertura
XDEBUG_MODE=coverage php artisan test --coverage-html reports/coverage
            </pre>
            </div>

            <div class="best-practice">
                <h4>Aliases Úteis para Nosso Projeto</h4>
                <p>Adicionamos os seguintes aliases ao arquivo <code>composer.json</code> para facilitar a execução de
                    testes:</p>
                <pre>
"scripts": {
    "test": "php artisan test",
    "test:unit": "php artisan test --testsuite=Unit",
    "test:feature": "php artisan test --testsuite=Feature",
    "test:integration": "php artisan test --testsuite=Integration",
    "test:security": "php artisan test --testsuite=Security",
    "test:coverage": "XDEBUG_MODE=coverage php artisan test --coverage",
    "test:coverage-html": "XDEBUG_MODE=coverage php artisan test --coverage-html reports/coverage",
    "test:watch": "php artisan test --watch",
    "test:parallel": "php artisan test --parallel"
}
    </pre>
                <p>Assim, podemos executar comandos como <code>composer test:unit</code> ou
                    <code>composer test:coverage</code> de forma mais simples.
                </p>
            </div>

            <div class="tip">
                <p>Para desenvolvedores que preferem usar Pest, também adicionamos aliases específicos:</p>
                <pre>
"scripts": {
    "pest": "./vendor/bin/pest",
    "pest:coverage": "XDEBUG_MODE=coverage ./vendor/bin/pest --coverage",
    "pest:watch": "./vendor/bin/pest --watch"
}
    </pre>
            </div>
        </section>

        <section id="testes-paralelos">
            <h3>8.2. Testes Paralelos</h3>
            <p>A execução de testes em paralelo pode reduzir significativamente o tempo de execução, especialmente em
                pipelines de CI/CD.</p>

            <div class="example">
                <h4>Configuração de Testes Paralelos em Nosso Projeto</h4>
                <pre>
// phpunit.xml
&lt;phpunit ...>
    ...
    &lt;php>
        ...
        &lt;server name="CACHE_DRIVER" value="array"/>
        &lt;server name="DB_CONNECTION" value="sqlite"/>
        &lt;server name="DB_DATABASE" value=":memory:"/>
        &lt;server name="TELESCOPE_ENABLED" value="false"/>
    &lt;/php>
&lt;/phpunit>
            </pre>
            </div>

            <div class="example">
                <h4>Executando Testes em Paralelo</h4>
                <pre>
# Executa testes em paralelo com o número padrão de processos (baseado em CPUs)
php artisan test --parallel

# Especifica o número de processos
php artisan test --parallel --processes=4

# Executa apenas testes unitários em paralelo
php artisan test --testsuite=Unit --parallel
            </pre>
            </div>

            <div class="best-practice">
                <h4>Considerações para Testes Paralelos</h4>
                <ul>
                    <li>Certifique-se de que os testes sejam independentes e não compartilhem estado</li>
                    <li>Use banco de dados em memória para evitar conflitos</li>
                    <li>Evite dependências de arquivos compartilhados</li>
                    <li>Tenha cuidado com recursos globais como cache e filas</li>
                    <li>Considere usar <code>RefreshDatabase</code> em vez de <code>DatabaseTransactions</code></li>
                    <li>Desative serviços pesados como Telescope durante os testes</li>
                </ul>
            </div>

            <div class="note">
                <p>Em nosso projeto, conseguimos reduzir o tempo de execução dos testes de 3 minutos para menos de 1
                    minuto usando testes paralelos em nosso pipeline de CI/CD.</p>
            </div>
        </section>

        <section id="ci-cd">
            <h3>8.3. Integração com CI/CD</h3>
            <p>Integramos a execução de testes em nosso pipeline de CI/CD para garantir que cada alteração seja testada
                antes de ser implantada.</p>

            <div class="example">
                <h4>Configuração do GitHub Actions para Nosso Projeto</h4>
                <pre>
# .github/workflows/tests.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, mysql, pdo_mysql
        coverage: pcov
    
    - name: Install Dependencies
      run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
    
    - name: Copy .env
      run: cp .env.ci .env
    
    - name: Generate key
      run: php artisan key:generate
    
    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache
    
    - name: Run Static Analysis
      run: ./vendor/bin/phpstan analyse --memory-limit=2G
    
    - name: Run Unit & Feature Tests
      run: php artisan test --parallel
    
    - name: Run Integration Tests
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: testing
        DB_USERNAME: root
        DB_PASSWORD: password
      run: php artisan test --testsuite=Integration
    
    - name: Generate Test Coverage Report
      run: php artisan test --coverage --min=80
            </pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para CI/CD em Nosso Projeto</h4>
                <ul>
                    <li>Execute testes em cada pull request antes de permitir o merge</li>
                    <li>Divida os testes em jobs separados para execução mais rápida</li>
                    <li>Use caching para dependências do Composer para acelerar o processo</li>
                    <li>Execute análise estática de código junto com os testes</li>
                    <li>Defina um limite mínimo de cobertura de código</li>
                    <li>Execute testes unitários e de feature em paralelo</li>
                    <li>Use um banco de dados real para testes de integração</li>
                    <li>Armazene relatórios de teste como artefatos para análise posterior</li>
                </ul>
            </div>

            <div class="example">
                <h4>Configuração do GitLab CI para Nosso Projeto</h4>
                <pre>
# .gitlab-ci.yml
stages:
  - test
  - deploy

variables:
  MYSQL_DATABASE: testing
  MYSQL_ROOT_PASSWORD: password
  DB_HOST: mysql
  DB_USERNAME: root
  DB_PASSWORD: password

cache:
  paths:
    - vendor/
    - node_modules/

test:
  stage: test
  image: php:8.2-cli
  services:
    - mysql:8.0
  before_script:
    - apt-get update -yqq
    - apt-get install -yqq git libzip-dev zip unzip libonig-dev libxml2-dev
    - docker-php-ext-install pdo_mysql zip
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - composer install --prefer-dist --no-ansi --no-interaction --no-progress
    - cp .env.ci .env
    - php artisan key:generate
    - php artisan config:clear
    - php artisan migrate --seed
  script:
    - php artisan test --parallel
    - php artisan test --testsuite=Integration
  artifacts:
    paths:
      - storage/logs
    expire_in: 1 day
    when: on_failure

deploy_staging:
  stage: deploy
  image: alpine:latest
  script:
    - echo "Deploy to staging server"
  only:
    - develop
  when: on_success
            </pre>
            </div>
        </section>
    </section>

    <section id="cobertura" class="manual-section">
        <h2>9. Cobertura de Testes</h2>
        <p>A cobertura de testes é uma métrica importante para avaliar a qualidade e abrangência dos nossos testes.</p>

        <section id="ferramentas-cobertura">
            <h3>9.1. Ferramentas de Cobertura</h3>
            <p>Utilizamos várias ferramentas para medir e analisar a cobertura de testes em nosso projeto.</p>

            <div class="best-practice">
                <h4>Ferramentas de Cobertura em Nosso Projeto</h4>
                <ul>
                    <li><strong>Xdebug:</strong> Para geração de relatórios de cobertura detalhados</li>
                    <li><strong>PCOV:</strong> Alternativa mais rápida ao Xdebug para CI/CD</li>
                    <li><strong>PHPUnit:</strong> Integração nativa para relatórios de cobertura</li>
                    <li><strong>Laravel Artisan:</strong> Comandos simplificados para cobertura</li>
                    <li><strong>SonarQube:</strong> Para análise contínua de qualidade de código</li>
                </ul>
            </div>

            <div class="example">
                <h4>Gerando Relatórios de Cobertura</h4>
                <pre>
# Usando Xdebug
XDEBUG_MODE=coverage php artisan test --coverage

# Gerando relatório HTML detalhado
XDEBUG_MODE=coverage php artisan test --coverage-html reports/coverage

# Usando PCOV (mais rápido para CI)
php artisan test --coverage --coverage-html reports/coverage

# Verificando cobertura mínima
php artisan test --coverage --min=80

# Gerando relatório Clover XML para integração com SonarQube
XDEBUG_MODE=coverage php artisan test --coverage-clover=coverage.xml
            </pre>
            </div>

            <div class="tip">
                <p>Para desenvolvimento local, recomendamos configurar o PHPStorm para executar testes com cobertura
                    diretamente da IDE, o que facilita a visualização das linhas não cobertas.</p>
            </div>
        </section>

        <section id="metricas-cobertura">
            <h3>9.2. Métricas e Metas de Cobertura</h3>
            <p>Definimos metas específicas de cobertura para diferentes partes do nosso código.</p>

            <div class="best-practice">
                <h4>Metas de Cobertura por Tipo de Código</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Tipo de Código</th>
                            <th>Meta de Cobertura</th>
                            <th>Justificativa</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Regras de Negócio (Services)</td>
                            <td>90%+</td>
                            <td>Lógica crítica que precisa ser altamente testada</td>
                        </tr>
                        <tr>
                            <td>Controllers de API</td>
                            <td>85%+</td>
                            <td>Interfaces públicas que precisam ser bem testadas</td>
                        </tr>
                        <tr>
                            <td>Models</td>
                            <td>70%+</td>
                            <td>Foco em métodos personalizados e escopos</td>
                        </tr>
                        <tr>
                            <td>Repositories</td>
                            <td>80%+</td>
                            <td>Camada de acesso a dados crítica</td>
                        </tr>
                        <tr>
                            <td>Middleware</td>
                            <td>75%+</td>
                            <td>Componentes de segurança importantes</td>
                        </tr>
                        <tr>
                            <td>Jobs e Listeners</td>
                            <td>80%+</td>
                            <td>Processamento assíncrono que precisa ser confiável</td>
                        </tr>
                        <tr>
                            <td>Helpers e Utils</td>
                            <td>85%+</td>
                            <td>Funções utilitárias usadas em todo o sistema</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="best-practice">
                <h4>Métricas que Monitoramos</h4>
                <ul>
                    <li><strong>Cobertura de Linhas:</strong> Percentual de linhas de código executadas pelos testes
                    </li>
                    <li><strong>Cobertura de Branches:</strong> Percentual de caminhos de decisão testados</li>
                    <li><strong>Cobertura de Funções/Métodos:</strong> Percentual de funções executadas pelos testes
                    </li>
                    <li><strong>Cobertura de Classes:</strong> Percentual de classes testadas</li>
                    <li><strong>Complexidade Ciclomática:</strong> Garantir que código complexo tenha maior cobertura
                    </li>
                </ul>
            </div>

            <div class="note">
                <p>Nosso objetivo não é atingir 100% de cobertura em todo o código, mas garantir que as partes críticas
                    e complexas sejam bem testadas. Priorizamos a qualidade dos testes sobre a quantidade.</p>
            </div>
        </section>

        <section id="estrategias-cobertura">
            <h3>9.3. Estratégias para Aumentar a Cobertura</h3>
            <p>Implementamos várias estratégias para melhorar a cobertura de testes em nosso projeto.</p>

            <div class="best-practice">
                <h4>Estratégias Eficazes em Nosso Projeto</h4>
                <ul>
                    <li><strong>Identificar Hotspots:</strong> Focar em áreas críticas com baixa cobertura</li>
                    <li><strong>TDD para Novas Funcionalidades:</strong> Garantir cobertura desde o início</li>
                    <li><strong>Testes de Regressão:</strong> Adicionar testes para bugs corrigidos</li>
                    <li><strong>Revisão de Código:</strong> Exigir testes em pull requests</li>
                    <li><strong>Limites de Cobertura:</strong> Configurar CI para falhar se a cobertura cair abaixo do
                        limite</li>
                    <li><strong>Refatoração Gradual:</strong> Melhorar a testabilidade do código legado</li>
                    <li><strong>Pair Programming:</strong> Colaborar em testes para código complexo</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Estratégia para Código Legado</h4>
                <pre>
// 1. Identificar código não testado
// Executar análise de cobertura para encontrar áreas críticas

// 2. Extrair lógica complexa para classes testáveis
// Antes: Método grande e difícil de testar em um controller
public function processOrder(Request $request)
{
    // 100+ linhas de lógica de negócio complexa
}

// Depois: Lógica extraída para um serviço testável
public function processOrder(Request $request)
{
    $result = $this->orderProcessingService->process(
        $request->user()->id,
        $request->validated()
    );
    
    return response()->json($result);
}

// 3. Escrever testes para o serviço extraído
public function test_order_processing_service_handles_valid_data(): void
{
    // Arrange
    $service = new OrderProcessingService(
        $this->createMock(PaymentGateway::class),
        $this->createMock(InventoryManager::class)
    );
    
    $userData = ['id' => 1];
    $orderData = ['items' => [['product_id' => 1, 'quantity' => 2]]];
    
    // Act
    $result = $service->process($userData, $orderData);
    
    // Assert
    $this->assertTrue($result['success']);
}
            </pre>
            </div>

            <div class="tip">
                <p>Implementamos um "Test Coverage Day" mensal, onde a equipe dedica um dia inteiro para melhorar a
                    cobertura de testes em áreas críticas do sistema.</p>
            </div>
        </section>
    </section>

    <section id="boas-praticas" class="manual-section">
        <h2>10. Boas Práticas</h2>
        <p>Esta seção compila as melhores práticas para testes que adotamos em nosso projeto Laravel 12.</p>

        <section id="organizacao">
            <h3>10.1. Organização de Testes</h3>
            <p>Uma boa organização dos testes facilita a manutenção e compreensão do código de teste.</p>

            <div class="best-practice">
                <h4>Estrutura de Diretórios de Testes em Nosso Projeto</h4>
                <pre>
tests/
├── Unit/                  # Testes unitários
│   ├── Services/          # Testes para serviços
│   ├── Models/            # Testes para modelos
│   └── Helpers/           # Testes para helpers
├── Feature/               # Testes de feature
│   ├── Api/               # Testes de API
│   ├── Admin/             # Testes do painel admin
│   └── Auth/              # Testes de autenticação
├── Integration/           # Testes de integração
│   ├── Database/          # Testes de integração com banco
│   └── External/          # Testes de integração com serviços externos
├── Browser/               # Testes de browser com Dusk
├── Security/              # Testes de segurança
├── Performance/           # Testes de performance
├── TestCase.php           # Classe base para testes
└── CreatesApplication.php # Trait para criar a aplicação
            </pre>
            </div>

            <div class="best-practice">
                <h4>Convenções de Nomenclatura</h4>
                <ul>
                    <li>Arquivos de teste terminam com <code>Test.php</code> (ex: <code>OrderServiceTest.php</code>)
                    </li>
                    <li>Classes de teste correspondem à classe testada (ex: <code>OrderService</code> →
                        <code>OrderServiceTest</code>)
                    </li>
                    <li>Métodos de teste começam com <code>test_</code> seguido por descrição clara</li>
                    <li>Namespaces de teste refletem a estrutura do código (ex: <code>App\Services</code> →
                        <code>Tests\Unit\Services</code>)
                    </li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Organização de Classe de Teste</h4>
                <pre>
namespace Tests\Unit\Services;

use App\Services\PaymentService;
use PHPUnit\Framework\TestCase;

class PaymentServiceTest extends TestCase
{
    // 1. Setup e teardown
    protected function setUp(): void
    {
        parent::setUp();
        // Configuração comum
    }
    
    // 2. Testes de casos de sucesso
    public function test_it_processes_valid_credit_card_payment(): void
    {
        // ...
    }
    
    public function test_it_processes_valid_pix_payment(): void
    {
        // ...
    }
    
    // 3. Testes de casos de erro
    public function test_it_throws_exception_for_invalid_card(): void
    {
        // ...
    }
    
    public function test_it_handles_gateway_timeout(): void
    {
        // ...
    }
    
    // 4. Testes de edge cases
    public function test_it_handles_zero_amount_payment(): void
    {
        // ...
    }
    
    // 5. Métodos auxiliares no final
    private function createValidPaymentData(): array
    {
        // ...
    }
}
            </pre>
            </div>
        </section>

        <section id="anti-patterns">
            <h3>10.2. Anti-Patterns a Evitar</h3>
            <p>Identificamos vários anti-patterns que prejudicam a qualidade e manutenibilidade dos testes.</p>

            <div class="best-practice">
                <h4>Anti-Patterns Comuns e Como Evitá-los</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Anti-Pattern</th>
                            <th>Problema</th>
                            <th>Solução</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Testes Frágeis</td>
                            <td>Testes que quebram com mudanças não relacionadas</td>
                            <td>Testar comportamento, não implementação; usar mocks adequadamente</td>
                        </tr>
                        <tr>
                            <td>Testes Lentos</td>
                            <td>Testes que demoram muito para executar</td>
                            <td>Usar banco em memória; mock de serviços externos; otimizar setup</td>
                        </tr>
                        <tr>
                            <td>Testes Interdependentes</td>
                            <td>Testes que dependem do estado de outros testes</td>
                            <td>Isolar cada teste; usar RefreshDatabase; resetar estado no tearDown</td>
                        </tr>
                        <tr>
                            <td>Testes Obscuros</td>
                            <td>Testes difíceis de entender</td>
                            <td>Nomes claros; comentários; padrão AAA; assertions expressivas</td>
                        </tr>
                        <tr>
                            <td>Testes Duplicados</td>
                            <td>Código de teste repetido</td>
                            <td>Usar data providers; extrair métodos auxiliares; usar traits</td>
                        </tr>
                        <tr>
                            <td>Testes de Implementação</td>
                            <td>Testar como algo é feito, não o que faz</td>
                            <td>Focar em testar a API pública e resultados, não detalhes internos</td>
                        </tr>
                        <tr>
                            <td>Testes de Configuração</td>
                            <td>Testar configuração do framework</td>
                            <td>Focar em testar código personalizado, não o framework</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="example">
                <h4>Exemplo de Refatoração de Anti-Pattern</h4>
                <pre>
// Anti-pattern: Teste frágil que depende de detalhes de implementação
public function test_order_is_processed()
{
    $order = Order::factory()->create();
    $service = new OrderService();
    
    $service->process($order);
    
    // Verifica detalhes específicos de implementação
    $this->assertTrue($service->paymentProcessor->wasCalledWith($order));
    $this->assertEquals(1, $service->processCount);
}

// Melhor abordagem: Testar comportamento observável
public function test_order_is_processed()
{
    $order = Order::factory()->create(['status' => 'pending']);
    $service = new OrderService();
    
    $result = $service->process($order);
    
    // Verifica o resultado e efeitos observáveis
    $this->assertTrue($result);
    $this->assertEquals('processed', $order->fresh()->status);
    $this->assertNotNull($order->fresh()->processed_at);
}
            </pre>
            </div>
        </section>

        <section id="testes-legados">
            <h3>10.3. Testando Código Legado</h3>
            <p>Testar código legado apresenta desafios específicos que exigem abordagens diferentes.</p>

            <div class="best-practice">
                <h4>Estratégias para Testar Código Legado</h4>
                <ul>
                    <li><strong>Caracterization Tests:</strong> Documentar o comportamento atual antes de modificar</li>
                    <li><strong>Seams:</strong> Identificar pontos onde o código pode ser "quebrado" para testes</li>
                    <li><strong>Extract and Override:</strong> Extrair métodos para facilitar o mock</li>
                    <li><strong>Refatoração Gradual:</strong> Melhorar a testabilidade em pequenos passos</li>
                    <li><strong>Testes de Integração:</strong> Começar com testes de caixa preta</li>
                    <li><strong>Cobertura Estratégica:</strong> Focar em áreas de alto risco</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Abordagem para Código Legado</h4>
                <pre>
// 1. Código legado difícil de testar
class LegacyOrderProcessor
{
    public function processOrder($orderId)
    {
        $order = DB::table('orders')->where('id', $orderId)->first();
        if (!$order) return false;
        
        // Lógica complexa com dependências globais
        $tax = Config::get('tax_rate') * $order->total;
        $payment = PaymentGateway::charge($order->customer_id, $order->total + $tax);
        
        if ($payment->success) {
            DB::table('orders')->where('id', $orderId)->update([
                'status' => 'paid',
                'payment_id' => $payment->id
            ]);
            return true;
        }
        
        return false;
    }
}

// 2. Refatoração para testabilidade
class RefactoredOrderProcessor
{
    protected $db;
    protected $config;
    protected $paymentGateway;
    
    public function __construct($db, $config, $paymentGateway)
    {
        $this->db = $db;
        $this->config = $config;
        $this->paymentGateway = $paymentGateway;
    }
    
    public function processOrder($orderId)
    {
        $order = $this->getOrder($orderId);
        if (!$order) return false;
        
        $tax = $this->calculateTax($order);
        $payment = $this->processPayment($order, $tax);
        
        if ($payment->success) {
            $this->updateOrderStatus($orderId, $payment->id);
            return true;
        }
        
        return false;
    }
    
    // Métodos extraídos para facilitar testes
    protected function getOrder($orderId)
    {
        return $this->db->table('orders')->where('id', $orderId)->first();
    }
    
    protected function calculateTax($order)
    {
        return $this->config->get('tax_rate') * $order->total;
    }
    
    protected function processPayment($order, $tax)
    {
        return $this->paymentGateway->charge($order->customer_id, $order->total + $tax);
    }
    
    protected function updateOrderStatus($orderId, $paymentId)
    {
        $this->db->table('orders')->where('id', $orderId)->update([
            'status' => 'paid',
            'payment_id' => $paymentId
        ]);
    }
}

// 3. Teste para o código refatorado
public function test_process_order_with_successful_payment()
{
    // Arrange
    $order = (object)[
        'id' => 1,
        'customer_id' => 100,
        'total' => 200.00
    ];
    
    $payment = (object)[
        'success' => true,
        'id' => 'pay_123'
    ];
    
    $db = Mockery::mock('DB');
    $db->shouldReceive('table')->with('orders')->andReturnSelf();
    $db->shouldReceive('where')->with('id', 1)->andReturnSelf();
    $db->shouldReceive('first')->andReturn($order);
    $db->shouldReceive('update')->once()->with([
        'status' => 'paid',
        'payment_id' => 'pay_123'
    ]);
    
    $config = Mockery::mock('Config');
    $config->shouldReceive('get')->with('tax_rate')->andReturn(0.1);
    
    $paymentGateway = Mockery::mock('PaymentGateway');
    $paymentGateway->shouldReceive('charge')
        ->with(100, 220.00) // total + tax
        ->andReturn($payment);
    
    $processor = new RefactoredOrderProcessor($db, $config, $paymentGateway);
    
    // Act
    $result = $processor->processOrder(1);
    
    // Assert
    $this->assertTrue($result);
}
            </pre>
            </div>
        </section>

        <section id="testes-eficientes">
            <h3>10.4. Escrevendo Testes Eficientes</h3>
            <p>Testes eficientes são rápidos, confiáveis e fáceis de manter.</p>

            <div class="best-practice">
                <h4>Características de Testes Eficientes</h4>
                <ul>
                    <li><strong>Rápidos:</strong> Executam em milissegundos, não segundos</li>
                    <li><strong>Independentes:</strong> Não dependem de outros testes ou estado global</li>
                    <li><strong>Repetíveis:</strong> Produzem o mesmo resultado em cada execução</li>
                    <li><strong>Auto-validantes:</strong> Falham ou passam sem intervenção manual</li>
                    <li><strong>Oportunos:</strong> Escritos no momento certo (idealmente antes do código)</li>
                    <li><strong>Focados:</strong> Testam uma única funcionalidade ou comportamento</li>
                    <li><strong>Legíveis:</strong> Fáceis de entender o que está sendo testado</li>
                    <li><strong>Mantíveis:</strong> Fáceis de atualizar quando o código muda</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste Eficiente em Nosso Projeto</h4>
                <pre>
public function test_product_discount_calculation(): void
{
    // Arrange - Configuração mínima necessária
    $product = new Product([
        'price' => 100.00,
        'discount_percentage' => 15
    ]);
    
    // Act - Operação simples e direta
    $discountedPrice = $product->getDiscountedPrice();
    
    // Assert - Verificação clara e específica
    $this->assertEquals(85.00, $discountedPrice);
}
        </pre>
            </div>

            <div class="best-practice">
                <h4>Dicas para Testes Mais Eficientes</h4>
                <ul>
                    <li>Use fixtures e factories para criar dados de teste de forma eficiente</li>
                    <li>Minimize o uso de banco de dados quando possível</li>
                    <li>Prefira SQLite em memória para testes que precisam de banco de dados</li>
                    <li>Use mocks para dependências externas e serviços lentos</li>
                    <li>Evite sleep() em testes; use técnicas de sincronização melhores</li>
                    <li>Agrupe testes relacionados em classes de teste coesas</li>
                    <li>Reutilize configuração comum com traits e métodos auxiliares</li>
                    <li>Mantenha os testes simples e diretos</li>
                    <li>Teste apenas o que é necessário, não tudo o que é possível</li>
                </ul>
            </div>

            <div class="example">
                <h4>Comparação: Teste Ineficiente vs. Eficiente</h4>
                <pre>
// Teste ineficiente
public function test_order_processing_inefficient(): void
{
    // Setup excessivo
    $this->seed(DatabaseSeeder::class); // Seed completo do banco
    
    $user = User::factory()->create();
    $this->actingAs($user);
    
    $product1 = Product::factory()->create(['price' => 100]);
    $product2 = Product::factory()->create(['price' => 200]);
    
    sleep(1); // Espera desnecessária
    
    // Teste que faz muitas coisas
    $response = $this->post('/api/orders', [
        'products' => [
            ['id' => $product1->id, 'quantity' => 2],
            ['id' => $product2->id, 'quantity' => 1]
        ],
        'shipping_address' => '123 Test St',
        'payment_method' => 'credit_card',
        'card_number' => '****************',
        // ... muitos outros campos
    ]);
    
    // Múltiplas assertions não relacionadas
    $response->assertStatus(201);
    $this->assertDatabaseHas('orders', ['user_id' => $user->id]);
    $this->assertDatabaseHas('order_items', ['product_id' => $product1->id]);
    $this->assertDatabaseHas('order_items', ['product_id' => $product2->id]);
    $this->assertDatabaseHas('payments', ['amount' => 400]);
    $this->assertDatabaseHas('shipping_addresses', ['address' => '123 Test St']);
    // ... muitas outras assertions
}

// Teste eficiente
public function test_order_creation_returns_correct_response(): void
{
    // Setup mínimo necessário
    $user = User::factory()->create();
    $product = Product::factory()->create(['price' => 100]);
    
    $this->actingAs($user);
    
    // Teste focado
    $response = $this->postJson('/api/orders', [
        'products' => [
            ['id' => $product->id, 'quantity' => 2]
        ],
        'shipping_address_id' => 1,
        'payment_method_id' => 1
    ]);
    
    // Assertions específicas para este teste
    $response->assertStatus(201)
        ->assertJsonStructure([
            'data' => ['id', 'total', 'status']
        ]);
}

// Outro teste focado para outra funcionalidade
public function test_order_items_are_saved_correctly(): void
{
    // Setup específico
    $user = User::factory()->create();
    $product = Product::factory()->create(['price' => 100]);
    
    $orderService = app(OrderService::class);
    
    // Ação específica
    $order = $orderService->createOrder($user->id, [
        'products' => [
            ['id' => $product->id, 'quantity' => 2]
        ]
    ]);
    
    // Assertions específicas
    $this->assertCount(1, $order->items);
    $this->assertEquals($product->id, $order->items[0]->product_id);
    $this->assertEquals(2, $order->items[0]->quantity);
    $this->assertEquals(200, $order->total);
}
        </pre>
            </div>
        </section>
    </section>

    <section id="troubleshooting" class="manual-section">
        <h2>11. Troubleshooting</h2>
        <p>Esta seção aborda problemas comuns encontrados durante o desenvolvimento e execução de testes em nosso
            projeto Laravel 12.</p>

        <section id="problemas-comuns">
            <h3>11.1. Problemas Comuns e Soluções</h3>
            <p>Compilamos uma lista dos problemas mais frequentes e suas soluções com base na experiência da equipe.
            </p>

            <div class="best-practice">
                <h4>Problemas de Banco de Dados</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Problema</th>
                            <th>Causa Provável</th>
                            <th>Solução</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Testes deixam dados no banco</td>
                            <td>Não está usando RefreshDatabase ou DatabaseTransactions</td>
                            <td>Adicione o trait RefreshDatabase à classe de teste</td>
                        </tr>
                        <tr>
                            <td>Erro "No such table"</td>
                            <td>Migrações não foram executadas</td>
                            <td>Verifique se está usando RefreshDatabase ou execute migrações manualmente</td>
                        </tr>
                        <tr>
                            <td>Testes de banco muito lentos</td>
                            <td>Usando banco real em vez de SQLite em memória</td>
                            <td>Configure DB_CONNECTION=sqlite e DB_DATABASE=:memory: para testes</td>
                        </tr>
                        <tr>
                            <td>Erro de chave estrangeira</td>
                            <td>Ordem incorreta de criação de registros</td>
                            <td>Crie registros na ordem correta respeitando as relações</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="best-practice">
                <h4>Problemas de Autenticação</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Problema</th>
                            <th>Causa Provável</th>
                            <th>Solução</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Erro 401 em testes de API</td>
                            <td>Não está autenticando o usuário</td>
                            <td>Use Sanctum::actingAs($user) ou $this->actingAs($user)</td>
                        </tr>
                        <tr>
                            <td>Erro 403 em testes de API</td>
                            <td>Usuário sem permissões necessárias</td>
                            <td>Verifique as permissões do usuário ou use um usuário com role adequada</td>
                        </tr>
                        <tr>
                            <td>Token de API não funciona</td>
                            <td>Configuração incorreta do Sanctum</td>
                            <td>Verifique a configuração do Sanctum e use os scopes corretos</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="best-practice">
                <h4>Problemas de Asserção</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Problema</th>
                            <th>Causa Provável</th>
                            <th>Solução</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>assertJsonStructure falha</td>
                            <td>Estrutura JSON diferente do esperado</td>
                            <td>Verifique a estrutura real com dd($response->json()) e ajuste o teste</td>
                        </tr>
                        <tr>
                            <td>assertDatabaseHas falha</td>
                            <td>Dados não estão no banco ou diferem do esperado</td>
                            <td>Verifique se os dados foram realmente salvos e se os campos correspondem exatamente
                            </td>
                        </tr>
                        <tr>
                            <td>assertEquals falha com objetos</td>
                            <td>Comparação de referência em vez de valores</td>
                            <td>Use assertEquals com arrays ou propriedades específicas, ou use
                                assertEqualsCanonicalizing</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="example">
                <h4>Exemplo de Solução para Problema Comum</h4>
                <pre>
// Problema: Teste falha com "No such table: users"

// Solução 1: Adicionar RefreshDatabase
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserTest extends TestCase
{
    use RefreshDatabase; // Isso executa as migrações automaticamente
    
    public function test_user_can_be_created(): void
    {
        // Teste aqui
    }
}

// Solução 2: Executar migrações manualmente no setUp
protected function setUp(): void
{
    parent::setUp();
    $this->artisan('migrate:fresh');
}

// Solução 3: Usar SQLite em memória (em phpunit.xml)
// &lt;env name="DB_CONNECTION" value="sqlite"/>
// &lt;env name="DB_DATABASE" value=":memory:"/>
            </pre>
            </div>
        </section>

        <section id="depurando-testes">
            <h3>11.2. Depurando Testes</h3>
            <p>Técnicas eficazes para depurar testes que estão falhando.</p>

            <div class="best-practice">
                <h4>Ferramentas de Depuração</h4>
                <ul>
                    <li><strong>dd() e dump():</strong> Para inspecionar variáveis e estado</li>
                    <li><strong>Laravel Telescope:</strong> Para monitorar queries, requests, etc.</li>
                    <li><strong>Laravel Debug Bar:</strong> Para visualizar informações de depuração</li>
                    <li><strong>Xdebug:</strong> Para depuração passo a passo</li>
                    <li><strong>Log::info():</strong> Para registrar informações em logs</li>
                </ul>
            </div>

            <div class="example">
                <h4>Técnicas de Depuração em Testes</h4>
                <pre>
public function test_order_calculation(): void
{
    // Criar dados de teste
    $product = Product::factory()->create(['price' => 100.00]);
    $order = Order::factory()->create();
    $order->items()->create([
        'product_id' => $product->id,
        'quantity' => 2,
        'unit_price' => $product->price
    ]);
    
    // Depurar estado inicial
    Log::info('Order before calculation', ['order' => $order->toArray()]);
    
    // Executar cálculo
    $orderService = app(OrderService::class);
    $result = $orderService->calculateTotals($order->id);
    
    // Depurar resultado
    dump($result); // Visualizar resultado no output do teste
    
    // Se o teste estiver falhando, inspecionar o estado atual
    if ($result['total'] !== 200.00) {
        dd([
            'expected' => 200.00,
            'actual' => $result['total'],
            'order' => $order->fresh()->toArray(),
            'items' => $order->fresh()->items->toArray(),
            'calculations' => $orderService->getLastCalculations()
        ]);
    }
    
    // Assertion
    $this->assertEquals(200.00, $result['total']);
}
            </pre>
            </div>

            <div class="best-practice">
                <h4>Dicas para Depuração Eficaz</h4>
                <ul>
                    <li>Execute apenas o teste problemático com <code>--filter</code></li>
                    <li>Aumente o nível de verbosidade com <code>--verbose</code></li>
                    <li>Use <code>DB::enableQueryLog()</code> e <code>DB::getQueryLog()</code> para depurar queries
                    </li>
                    <li>Verifique o estado do banco antes e depois das operações</li>
                    <li>Isole o problema reduzindo o teste ao mínimo necessário</li>
                    <li>Use breakpoints com Xdebug para depuração passo a passo</li>
                    <li>Verifique os logs em <code>storage/logs/laravel.log</code></li>
                </ul>
            </div>
        </section>

        <section id="testes-flaky">
            <h3>11.3. Lidando com Testes Flaky</h3>
            <p>Testes "flaky" (instáveis) são aqueles que passam e falham intermitentemente sem mudanças no código.
            </p>

            <div class="best-practice">
                <h4>Causas Comuns de Testes Flaky</h4>
                <ul>
                    <li><strong>Dependência de Ordem:</strong> Testes que dependem da ordem de execução</li>
                    <li><strong>Estado Compartilhado:</strong> Testes que compartilham estado global</li>
                    <li><strong>Condições de Corrida:</strong> Problemas de sincronização em código assíncrono</li>
                    <li><strong>Dependências Externas:</strong> Serviços externos instáveis</li>
                    <li><strong>Dependência de Tempo:</strong> Testes que dependem de data/hora atual</li>
                    <li><strong>Recursos Limitados:</strong> Testes que competem por recursos limitados</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Correção de Teste Flaky</h4>
                <pre>
// Teste flaky que depende da data/hora atual
public function test_subscription_expiration_flaky(): void
{
    $subscription = Subscription::factory()->create([
        'expires_at' => now()->addDays(30)
    ]);
    
    $this->assertTrue($subscription->isActive());
    
    // Problema: Este teste pode falhar se executado próximo à meia-noite
    // pois now() pode mudar entre a criação e a verificação
}

// Versão corrigida com tempo fixo
public function test_subscription_expiration_fixed(): void
{
    // Fixar o tempo para o teste
    $now = now();
    $this->travel(to: $now);
    
    $subscription = Subscription::factory()->create([
        'expires_at' => $now->copy()->addDays(30)
    ]);
    
    $this->assertTrue($subscription->isActive());
    
    // Voltar ao tempo normal
    $this->travelBack();
}
            </pre>
            </div>

            <div class="example">
                <h4>Exemplo de Correção de Teste com Dependência Externa</h4>
                <pre>
// Teste flaky que depende de API externa
public function test_payment_processing_flaky(): void
{
    $order = Order::factory()->create(['total' => 100.00]);
    $paymentService = app(PaymentService::class);
    
    $result = $paymentService->processPayment($order, [
        'card_number' => '****************',
        'expiry' => '12/25',
        'cvv' => '123'
    ]);
    
    // Pode falhar se a API externa estiver instável
    $this->assertTrue($result['success']);
}

// Versão corrigida com mock
public function test_payment_processing_fixed(): void
{
    $order = Order::factory()->create(['total' => 100.00]);
    
    // Criar mock do gateway de pagamento
    $mockGateway = $this->createMock(PaymentGatewayInterface::class);
    $mockGateway->method('charge')
        ->willReturn([
            'success' => true,
            'transaction_id' => 'test_123'
        ]);
    
    // Injetar o mock
    $this->app->instance(PaymentGatewayInterface::class, $mockGateway);
    
    $paymentService = app(PaymentService::class);
    $result = $paymentService->processPayment($order, [
        'card_number' => '****************',
        'expiry' => '12/25',
        'cvv' => '123'
    ]);
    
    // Agora o teste é estável
    $this->assertTrue($result['success']);
}
            </pre>
            </div>

            <div class="best-practice">
                <h4>Estratégias para Lidar com Testes Flaky</h4>
                <ul>
                    <li>Identifique e marque testes flaky com anotações ou tags</li>
                    <li>Isole testes flaky em uma suite separada</li>
                    <li>Use retry para testes que podem falhar intermitentemente</li>
                    <li>Fixe o tempo com helpers como travel() ou Carbon::setTestNow()</li>
                    <li>Mock todas as dependências externas</li>
                    <li>Evite sleep() e use técnicas de sincronização adequadas</li>
                    <li>Garanta que cada teste limpe completamente seu estado</li>
                    <li>Use ferramentas de detecção de testes flaky como Laravel Dusk Flaky Tests Recorder</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="fluxo-trabalho" class="manual-section">
        <h2>12. Fluxo de Trabalho de Testes</h2>
        <p>Esta seção descreve como os testes se integram ao nosso fluxo de trabalho de desenvolvimento.</p>

        <section id="processo-desenvolvimento">
            <h3>12.1. Testes no Processo de Desenvolvimento</h3>
            <p>Integramos testes em todas as etapas do nosso processo de desenvolvimento.</p>

            <div class="best-practice">
                <h4>Ciclo de Desenvolvimento com Testes</h4>
                <ol>
                    <li><strong>Planejamento:</strong> Definir requisitos e critérios de aceitação testáveis</li>
                    <li><strong>Desenvolvimento:</strong> Escrever testes e código seguindo TDD quando apropriado
                    </li>
                    <li><strong>Revisão de Código:</strong> Verificar cobertura e qualidade dos testes</li>
                    <li><strong>Integração:</strong> Executar testes automatizados no CI/CD</li>
                    <li><strong>Implantação:</strong> Executar testes de smoke após deploy</li>
                    <li><strong>Monitoramento:</strong> Analisar falhas e melhorar testes continuamente</li>
                </ol>
            </div>

            <div class="example">
                <h4>Fluxo de Trabalho para Nova Funcionalidade</h4>
                <pre>
# 1. Criar branch para a funcionalidade
git checkout -b feature/new-payment-method

# 2. Escrever testes para a nova funcionalidade
# tests/Feature/PaymentMethods/PixPaymentTest.php

# 3. Implementar a funcionalidade fazendo os testes passarem
# app/Services/Payment/PixPaymentProcessor.php

# 4. Executar testes localmente
php artisan test --filter=PixPaymentTest

# 5. Verificar cobertura
XDEBUG_MODE=coverage php artisan test --coverage --filter=PixPaymentTest

# 6. Criar pull request
git push origin feature/new-payment-method

# 7. CI/CD executa testes automaticamente

# 8. Após aprovação e merge, testes são executados novamente

# 9. Deploy para staging e execução de testes de smoke

# 10. Deploy para produção após validação
            </pre>
            </div>

            <div class="best-practice">
                <h4>Quando Escrever Testes</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Cenário</th>
                            <th>Abordagem Recomendada</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Nova funcionalidade</td>
                            <td>TDD ou testes escritos junto com o código</td>
                        </tr>
                        <tr>
                            <td>Correção de bug</td>
                            <td>Primeiro escrever teste que reproduz o bug, depois corrigir</td>
                        </tr>
                        <tr>
                            <td>Refatoração</td>
                            <td>Garantir testes existentes antes de refatorar</td>
                        </tr>
                        <tr>
                            <td>Código legado</td>
                            <td>Adicionar testes gradualmente, priorizando áreas críticas</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="revisao-codigo">
            <h3>12.2. Testes e Revisão de Código</h3>
            <p>A revisão de código é uma etapa crucial para garantir a qualidade dos testes.</p>

            <div class="best-practice">
                <h4>Checklist para Revisão de Testes</h4>
                <ul>
                    <li>Os testes cobrem todos os requisitos e casos de uso?</li>
                    <li>Os testes verificam tanto casos de sucesso quanto de erro?</li>
                    <li>Os nomes dos testes são claros e descritivos?</li>
                    <li>Os testes são independentes e não têm efeitos colaterais?</li>
                    <li>As assertions são específicas e verificam o comportamento correto?</li>
                    <li>O código de teste segue as convenções do projeto?</li>
                    <li>Os testes são rápidos e eficientes?</li>
                    <li>A cobertura de código é adequada?</li>
                    <li>Os mocks e stubs são usados apropriadamente?</li>
                    <li>Os testes são mantíveis e não muito frágeis?</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Feedback em Revisão de Código</h4>
                <pre>
// Comentário em PR: "Este teste poderia ser melhorado"

// Versão original
public function test_something(): void
{
    $user = User::factory()->create();
    $this->actingAs($user);
    
    $response = $this->post('/api/orders', [
        'product_id' => 1,
        'quantity' => 2
    ]);
    
    $this->assertEquals(201, $response->status());
}

// Versão melhorada após feedback
public function test_authenticated_user_can_create_order_with_valid_data(): void
{
    // Arrange
    $user = User::factory()->create();
    $product = Product::factory()->create(['price' => 100.00]);
    
    $this->actingAs($user);
    
    // Act
    $response = $this->postJson('/api/orders', [
        'product_id' => $product->id,
        'quantity' => 2
    ]);
    
    // Assert
    $response->assertStatus(201)
        ->assertJsonStructure(['data' => ['id', 'total']]);
        
    $this->assertDatabaseHas('orders', [
        'user_id' => $user->id,
        'total' => 200.00
    ]);
}
            </pre>
            </div>

            <div class="best-practice">
                <h4>Regras para Pull Requests em Nosso Projeto</h4>
                <ul>
                    <li>Todo código novo deve ter testes associados</li>
                    <li>Correções de bugs devem incluir testes que reproduzem o bug</li>
                    <li>A cobertura de código não deve diminuir</li>
                    <li>Todos os testes devem passar antes do merge</li>
                    <li>Pelo menos um revisor deve aprovar os testes</li>
                    <li>Testes lentos devem ser marcados apropriadamente</li>
                </ul>
            </div>
        </section>

        <section id="onboarding">
            <h3>12.3. Guia de Onboarding para Testes</h3>
            <p>Este guia ajuda novos membros da equipe a entender e contribuir com nossa estratégia de testes.</p>

            <div class="best-practice">
                <h4>Primeiros Passos para Novos Desenvolvedores</h4>
                <ol>
                    <li>Configure o ambiente de desenvolvimento local</li>
                    <li>Execute a suite de testes completa para verificar se tudo está funcionando</li>
                    <li>Revise este manual de testes para entender nossas práticas</li>
                    <li>Estude os testes existentes para entender os padrões</li>
                    <li>Comece escrevendo testes simples com a ajuda de um mentor</li>
                    <li>Participe de sessões de pair programming focadas em testes</li>
                </ol>
            </div>

            <div class="example">
                <h4>Comandos Essenciais para Novos Desenvolvedores</h4>
                <pre>
# Configurar ambiente de testes
cp .env.example .env.testing
php artisan key:generate --env=testing

# Executar todos os testes
php artisan test

# Executar um grupo específico de testes
php artisan test --testsuite=Unit

# Executar um arquivo de teste específico
php artisan test tests/Feature/OrderTest.php

# Executar um teste específico
php artisan test --filter=test_order_can_be_created

# Verificar cobertura de código
XDEBUG_MODE=coverage php artisan test --coverage

# Executar testes em modo de observação (requer Laravel Sail ou instalação manual)
php artisan test --watch

# Executar análise estática
./vendor/bin/phpstan analyse
            </pre>
            </div>

            <div class="best-practice">
                <h4>Recursos de Aprendizado Recomendados</h4>
                <ul>
                    <li><a href="https://laravel.com/docs/testing" target="_blank">Documentação oficial de testes do
                            Laravel</a></li>
                    <li><a href="https://pestphp.com/docs/installation" target="_blank">Documentação do Pest PHP</a>
                    </li>
                    <li><a href="https://phpunit.de/documentation.html" target="_blank">Documentação do PHPUnit</a>
                    </li>
                    <li><a href="https://laracasts.com/series/phpunit-testing-in-laravel" target="_blank">Série de
                            testes no Laracasts</a></li>
                    <li><a href="https://course.testdrivenlaravel.com/" target="_blank">Test-Driven Laravel (Adam
                            Wathan)</a></li>
                    <li>Repositório interno de exemplos de testes:
                        <code>**************:nossa-empresa/laravel-testing-examples.git</code>
                    </li>
                </ul>
            </div>

            <div class="tip">
                <p>Organizamos sessões mensais de "Testing Dojo" onde a equipe resolve problemas de teste juntos,
                    ótima oportunidade para novos membros aprenderem na prática.</p>
            </div>
        </section>
    </section>

    <section id="conclusao" class="manual-section">
        <h2>13. Conclusão</h2>
        <p>Este manual estabelece as diretrizes e melhores práticas para testes em nosso projeto Laravel 12.
            Seguindo estas orientações, podemos garantir um código mais robusto, confiável e de fácil manutenção.
        </p>

        <div class="best-practice">
            <h4>Princípios-Chave a Lembrar</h4>
            <ul>
                <li>Testes são um investimento, não um custo</li>
                <li>Testes devem dar confiança para refatorar e evoluir o código</li>
                <li>A qualidade dos testes é mais importante que a quantidade</li>
                <li>Testes devem ser tratados com o mesmo cuidado que o código de produção</li>
                <li>Testes automatizados são essenciais para entregas contínuas</li>
                <li>Diferentes tipos de testes servem a diferentes propósitos</li>
                <li>Testes devem ser rápidos, confiáveis e mantíveis</li>
            </ul>
        </div>

        <div class="note">
            <p>Este manual é um documento vivo que será atualizado conforme evoluímos nossas práticas de teste.
                Sugestões de melhoria são sempre bem-vindas e podem ser enviadas através de pull requests para o
                repositório de documentação.</p>
        </div>

        <div class="best-practice">
            <h4>Próximos Passos para Nossa Estratégia de Testes</h4>
            <ul>
                <li>Implementar testes de mutação para avaliar a qualidade dos testes existentes</li>
                <li>Expandir testes de segurança com ferramentas automatizadas</li>
                <li>Melhorar a integração de testes de acessibilidade</li>
                <li>Implementar testes de contrato para APIs</li>
                <li>Desenvolver mais testes de performance para endpoints críticos</li>
                <li>Criar dashboards de monitoramento de qualidade de testes</li>
            </ul>
        </div>

        <div class="example">
            <h4>Métricas de Sucesso</h4>
            <p>Saberemos que nossa estratégia de testes está funcionando quando:</p>
            <ul>
                <li>Bugs em produção diminuírem significativamente</li>
                <li>Tempo para detectar e corrigir problemas for reduzido</li>
                <li>Equipe se sentir confiante para refatorar e adicionar novas funcionalidades</li>
                <li>Ciclos de release se tornarem mais previsíveis e estáveis</li>
                <li>Cobertura de código se mantiver acima dos níveis mínimos definidos</li>
                <li>Testes se tornarem parte natural do fluxo de trabalho de todos</li>
            </ul>
        </div>
    </section>

    <footer class="manual-footer">
        <p>Manual de Testes - Projeto Laravel 12 - Última atualização: Agosto 2023</p>
        <p>Desenvolvido pela Equipe de Engenharia</p>
    </footer>
</body>

</html>