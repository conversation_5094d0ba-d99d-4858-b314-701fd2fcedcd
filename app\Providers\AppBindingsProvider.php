<?php

namespace App\Providers;

use App\Exceptions\InvalidBindingException;
use App\Repositories\RepositoryInterface;
use App\Responses\ApiResponse;
use App\Responses\ResponseInterface;
use App\Services\ServiceInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;
use Psr\SimpleCache\InvalidArgumentException;

/**
 * AppBindingsProvider - Registra automaticamente os bindings de injeção de dependência
 *
 * Responsável pelo registro automático de bindings entre:
 * - Controllers ← Services ← Repositories ← Models
 *
 * Implementa:
 * - Cache de descoberta de classes
 * - Validações rigorosas de interfaces
 * - Isolamento entre microserviços
 * - Suporte para múltiplos sistemas com estrutura flexível
 */
class AppBindingsProvider extends ServiceProvider
{
    /**
     * Chave para cache de bindings
     * @var string
     */
    protected const BINDINGS_CACHE_KEY = 'service_bindings';

    /**
     * Tempo de vida do cache em segundos (1 hora)
     * @var int
     */
    protected const CACHE_TTL = 3600;

    /**
     * Tempo de vida do cache para ambiente de produção (24 horas)
     * @var int
     */
    protected function getCacheTtl(): int
    {
        return env('BINDINGS_CACHE_TTL', match (env('APP_ENV')) {
            'production' => 86400,
            'staging'    => 7200,
            default      => self::CACHE_TTL
        });
    }

    /**
     * Bindings base que não requerem descoberta automática
     * @var array
     */
    protected const BASE_BINDINGS = [
        ResponseInterface::class => ApiResponse::class,
    ];

    /**
     * @var array Cache interno de bindings já registrados
     */
    protected array $registeredBindings = [];

    /**
     * Registra os bindings no container de serviço
     *
     * @return void
     * @throws InvalidBindingException
     */
    public function register(): void
    {
        try {
            $this->registerBaseBindings();
            $this->registerDiscoveredBindings();
        } catch (InvalidArgumentException $e) {
            throw new InvalidBindingException('Erro ao acessar cache de bindings', 0, $e);
        }
    }

    /**
     * Registra os bindings base da aplicação
     *
     * @return void
     * @throws InvalidBindingException
     */
    protected function registerBaseBindings(): void
    {
        foreach (self::BASE_BINDINGS as $abstract => $concrete) {
            $this->validateBinding($abstract, $concrete);
            $this->app->bind($abstract, $concrete);
            $this->registeredBindings[$abstract] = $concrete;
        }
    }

    /**
     * Registra bindings descobertos automaticamente
     *
     * @return void
     * @throws InvalidBindingException|InvalidArgumentException
     */
    protected function registerDiscoveredBindings(): void
    {
        $bindings = $this->getCachedBindings();

        foreach ($bindings as $binding) {
            $this->validateBinding($binding['abstract'], $binding['concrete']);

            if (isset($binding['context'])) {
                $this->app->when($binding['context'])
                    ->needs($binding['abstract'])
                    ->give($binding['concrete']);
            } else {
                $this->app->bind($binding['abstract'], $binding['concrete']);
            }

            $this->registeredBindings[$binding['abstract']] = $binding['concrete'];
        }
    }

    /**
     * Obtém bindings do cache ou descobre novos
     *
     * @return array
     * @throws InvalidArgumentException
     */
    protected function getCachedBindings(): array
    {
        if (app()->isProduction()) {
            return Cache::remember(
                self::BINDINGS_CACHE_KEY,
                $this->getCacheTtl(), // Usa o TTL dinâmico
                fn() => $this->discoverBindings()
            );
        }
        return $this->discoverBindings();
    }

    /**
     * Descobre todos os bindings automaticamente
     *
     * @return array
     */
    protected function discoverBindings(): array
    {
        return array_merge(
            $this->discoverAppRootBindings(),
            $this->discoverSystemsBindings()
        );
    }

    /**
     * Descobre bindings na estrutura raiz do app
     *
     * @return array
     */
    protected function discoverAppRootBindings(): array
    {
        return array_merge(
            $this->discoverControllerBindings("App\\Controllers", "App\\Services"),
            $this->discoverServiceBindings("App\\Services", "App\\Repositories"),
            $this->discoverRepositoryBindings("App\\Repositories", "App\\Models")
        );
    }

    /**
     * Descobre bindings nos sistemas (qualquer pasta em app/Http/)
     *
     * @return array
     */
    protected function discoverSystemsBindings(): array
    {
        $bindings = [];
        $systemsPath = app_path('Http');

        if (!File::isDirectory($systemsPath)) {
            return $bindings;
        }

        foreach (File::directories($systemsPath) as $systemPath) {
            $systemName = basename($systemPath);
            if (!$this->isReservedDirectory($systemName)) {
                $bindings = array_merge($bindings, $this->discoverSystemBindings($systemName));
            }
        }

        return $bindings;
    }

    /**
     * Descobre bindings para um sistema específico
     *
     * @param string $systemName Nome do sistema (pasta em app/Http/)
     * @return array
     */
    protected function discoverSystemBindings(string $systemName): array
    {
        $bindings = [];
        $systemPath = app_path("Http/{$systemName}");

        foreach (File::directories($systemPath) as $microservicePath) {
            $bindings = array_merge(
                $bindings,
                $this->discoverMicroserviceBindings($systemName, basename($microservicePath))
            );
        }

        return $bindings;
    }

    /**
     * Descobre bindings para um microserviço específico
     *
     * @param string $systemName Nome do sistema/pasta
     * @param string $microserviceName Nome do microserviço
     * @return array
     */
    protected function discoverMicroserviceBindings(string $systemName, string $microserviceName): array
    {
        $baseNamespace = "App\\Http\\{$systemName}\\{$microserviceName}";

        return array_merge(
            $this->discoverControllerBindings("{$baseNamespace}\\Controllers", "{$baseNamespace}\\Services"),
            $this->discoverServiceBindings("{$baseNamespace}\\Services", "{$baseNamespace}\\Repositories"),
            $this->discoverRepositoryBindings("{$baseNamespace}\\Repositories", "{$baseNamespace}\\Models")
        );
    }

    /**
     * Descobre bindings entre controllers e services
     *
     * @param string $controllersNamespace Namespace dos controllers
     * @param string $servicesNamespace Namespace dos services
     * @return array
     */
    protected function discoverControllerBindings(string $controllersNamespace, string $servicesNamespace): array
    {
        $bindings = [];
        foreach ($this->findClassesInNamespace($controllersNamespace) as $controller) {
            $serviceClass = $servicesNamespace . '\\' . str_replace('Controller', 'Service', class_basename($controller));
            if (class_exists($serviceClass)) {
                $bindings[] = [
                    'abstract' => ServiceInterface::class,
                    'concrete' => $serviceClass,
                    'context' => $controller
                ];
            }
        }
        return $bindings;
    }

    /**
     * Descobre bindings entre services e repositories
     *
     * @param string $servicesNamespace Namespace dos services
     * @param string $repositoriesNamespace Namespace dos repositories
     * @return array
     */
    protected function discoverServiceBindings(string $servicesNamespace, string $repositoriesNamespace): array
    {
        $bindings = [];
        foreach ($this->findClassesInNamespace($servicesNamespace) as $service) {
            $repoClass = $repositoriesNamespace . '\\' . str_replace('Service', 'Repository', class_basename($service));
            if (class_exists($repoClass)) {
                $bindings[] = [
                    'abstract' => RepositoryInterface::class,
                    'concrete' => $repoClass,
                    'context' => $service
                ];
            }
        }
        return $bindings;
    }

    /**
     * Descobre bindings entre repositories e models
     *
     * @param string $repositoriesNamespace Namespace dos repositories
     * @param string $modelsNamespace Namespace dos models
     * @return array
     */
    protected function discoverRepositoryBindings(string $repositoriesNamespace, string $modelsNamespace): array
    {
        $bindings = [];
        foreach ($this->findClassesInNamespace($repositoriesNamespace) as $repository) {
            $modelClass = $modelsNamespace . '\\' . str_replace('Repository', 'Model', class_basename($repository));
            if (!class_exists($modelClass)) {
                $modelClass = "App\\Models\\" . str_replace('Repository', 'Model', class_basename($repository));
            }
            if (class_exists($modelClass)) {
                $bindings[] = [
                    'abstract' => \Illuminate\Database\Eloquent\Model::class,
                    'concrete' => $modelClass,
                    'context' => $repository
                ];
            }
        }
        return $bindings;
    }

    /**
     * Encontra classes em um namespace específico
     *
     * @param string $namespace Namespace a ser pesquisado
     * @return array Lista de classes encontradas
     */
    protected function findClassesInNamespace(string $namespace): array
    {
        $classes = [];
        $path = $this->namespaceToPath($namespace);

        if (File::isDirectory($path)) {
            foreach (File::allFiles($path) as $file) {
                if ($file->getExtension() === 'php') {
                    $className = $this->getClassFromFile($file->getPathname());
                    if ($className && class_exists($className)) {
                        $classes[] = $className;
                    }
                }
            }
        }

        return $classes;
    }

    /**
     * Converte namespace para caminho de arquivo
     *
     * @param string $namespace Namespace completo
     * @return string Caminho físico correspondente
     */
    protected function namespaceToPath(string $namespace): string
    {
        return app_path(str_replace('\\', '/', str_replace('App\\', '', $namespace)));
    }

    /**
     * Extrai o nome da classe de um arquivo PHP
     *
     * @param string $filePath Caminho completo do arquivo
     * @return string|null Namespace completo da classe ou null se não encontrar
     */
    protected function getClassFromFile(string $filePath): ?string
    {
        $content = file_get_contents($filePath);
        $namespace = $class = null;

        if (preg_match('/namespace\s+([^;]+);/', $content, $matches)) {
            $namespace = $matches[1];
        }

        if (preg_match('/class\s+(\w+)/', $content, $matches)) {
            $class = $matches[1];
        }

        return $namespace && $class ? "{$namespace}\\{$class}" : null;
    }

    /**
     * Valida se uma classe concreta implementa a interface abstrata
     *
     * @param string $abstract Interface ou classe abstrata
     * @param string $concrete Implementação concreta
     * @throws InvalidBindingException
     */
    protected function validateBinding(string $abstract, string $concrete): void
    {
        if (!interface_exists($abstract) && !class_exists($abstract)) {
            throw new InvalidBindingException("Tipo abstrato {$abstract} não existe");
        }

        if (!class_exists($concrete)) {
            throw new InvalidBindingException("Classe {$concrete} não existe");
        }

        if (interface_exists($abstract) && !in_array($abstract, class_implements($concrete))) {
            throw new InvalidBindingException("Classe {$concrete} deve implementar {$abstract}");
        }

        if (class_exists($abstract) && !is_subclass_of($concrete, $abstract)) {
            throw new InvalidBindingException("Classe {$concrete} deve estender {$abstract}");
        }

        $this->validateMicroserviceIsolation($concrete);
    }

    /**
     * Valida o isolamento entre microserviços
     *
     * @param string $class Classe a ser validada
     * @throws InvalidBindingException
     */
    protected function validateMicroserviceIsolation(string $class): void
    {
        $caller = $this->getCallerClass();
        $callerContext = $this->getClassContext($caller);
        $targetContext = $this->getClassContext($class);

        if (
            $callerContext['is_microservice'] && $targetContext['is_microservice'] &&
            $callerContext['microservice'] !== $targetContext['microservice']
        ) {
            throw new InvalidBindingException(sprintf(
                "Violação de isolamento: %s → %s",
                $callerContext['microservice'],
                $targetContext['microservice']
            ));
        }
    }

    /**
     * Obtém a classe chamadora do binding atual
     *
     * @return string Namespace completo da classe chamadora
     */
    protected function getCallerClass(): string
    {
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5);
        return $trace[4]['class'] ?? '';
    }

    /**
     * Analisa o contexto de uma classe (sistema/microserviço)
     *
     * @param string $class Nome completo da classe
     * @return array Contexto identificado
     */
    protected function getClassContext(string $class): array
    {
        $parts = explode('\\', $class);
        $isMicroservice = count($parts) > 4 && $parts[1] === 'Http';

        return [
            'is_microservice' => $isMicroservice,
            'system' => $isMicroservice ? $parts[2] ?? null : null,
            'microservice' => $isMicroservice ? ($parts[2] ?? '') . '.' . ($parts[3] ?? '') : null
        ];
    }

    /**
     * Verifica se um diretório é reservado (não deve ser processado)
     *
     * @param string $directoryName Nome do diretório
     * @return bool
     */
    protected function isReservedDirectory(string $directoryName): bool
    {
        return in_array($directoryName, ['Controllers', 'Middleware', 'Requests', 'Resources']);
    }
}
