<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * Política de autorização para o modelo User
 */
class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determina se o usuário pode visualizar qualquer modelo.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function viewAny(User $user)
    {
        // Apenas administradores podem listar todos os usuários
        return $user->isAdmin();
    }

    /**
     * Determina se o usuário pode visualizar o modelo.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\User  $model
     * @return bool
     */
    public function view(User $user, User $model)
    {
        // Usuários podem ver seus próprios perfis ou administradores podem ver qualquer perfil
        return $user->id === $model->id || $user->isAdmin();
    }

    /**
     * Determina se o usuário pode criar modelos.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function create(User $user)
    {
        // Apenas administradores podem criar usuários (exceto pelo registro público)
        return $user->isAdmin();
    }

    /**
     * Determina se o usuário pode atualizar o modelo.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\User  $model
     * @return bool
     */
    public function update(User $user, User $model)
    {
        // Usuários podem atualizar seus próprios perfis ou administradores podem atualizar qualquer perfil
        return $user->id === $model->id || $user->isAdmin();
    }

    /**
     * Determina se o usuário pode excluir o modelo.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\User  $model
     * @return bool
     */
    public function delete(User $user, User $model)
    {
        // Apenas administradores podem excluir usuários e não podem excluir a si mesmos
        return $user->isAdmin() && $user->id !== $model->id;
    }

    /**
     * Determina se o usuário pode restaurar o modelo.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\User  $model
     * @return bool
     */
    public function restore(User $user)
    {
        // Apenas administradores podem restaurar usuários
        return $user->isAdmin();
    }

    /**
     * Determina se o usuário pode excluir permanentemente o modelo.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\User  $model
     * @return bool
     */
    public function forceDelete(User $user, User $model)
    {
        // Apenas administradores podem excluir permanentemente usuários e não podem excluir a si mesmos
        return $user->isAdmin() && $user->id !== $model->id;
    }
}
