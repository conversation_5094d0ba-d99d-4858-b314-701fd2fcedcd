<?php

namespace App\Middleware;

use Closure;
use Illuminate\Http\Request;
use PHPOpenSourceSaver\JWTAuth\Exceptions\TokenExpiredException;
use PHPOpenSourceSaver\JWTAuth\Exceptions\TokenInvalidException;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use App\Responses\ResponseInterface;
use Symfony\Component\HttpFoundation\Response;

class JwtVerify
{
    protected ResponseInterface $response;

    public function __construct(ResponseInterface $response)
    {
        $this->response = $response;
    }

    public function handle(Request $request, Closure $next): Response
    {
        try {
            $user = JWTAuth::parseToken()->authenticate();

            if (!$user) {
                return $this->response->notFound('User not found');
            }
        } catch (TokenExpiredException $e) {
            return $this->response->unauthorized('Token has expired');
        } catch (TokenInvalidException $e) {
            return $this->response->unauthorized('Token is invalid');
        } catch (\Exception $e) {
            return $this->response->unauthorized('Authorization token not found');
        }

        return $next($request);
    }
}
