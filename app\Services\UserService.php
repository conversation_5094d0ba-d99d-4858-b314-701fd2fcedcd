<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Hash;

/**
 * @OA\Schema(
 *     schema="User",
 *     title="User",
 *     description="Modelo de Usuário",
 *     @OA\Property(property="id", type="integer", format="int64", example=1),
 *     @OA\Property(property="name", type="string", example="João Silva"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T00:00:00.000000Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2023-01-01T00:00:00.000000Z")
 * )
 */
class UserService extends ServiceAbstract
{
    public array $createRules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:users',
        'password' => 'required|string|min:8'
    ];

    public array $updateRules = [
        'name' => 'sometimes|string|max:255',
        'email' => 'sometimes|email|unique:users,email,{id}',
        'password' => 'sometimes|string|min:8'
    ];

    public function registerUser(array $userData): User
    {
        // Validação
        $validated = $this->validate($userData, $this->createRules);

        // Processamento
        $processed = $this->beforeCreate($validated);
        $processed['password'] = Hash::make($processed['password']);

        // Persistência
        $user = $this->executeSafely(
            fn() => $this->repository->create($processed),
            'Failed to create user'
        );

        // Pós-processamento
        $this->afterCreate($user, $processed);
        return $user;
    }

    public function updateUser(int $userId, array $userData): User
    {
        // Validação
        $rules = str_replace('{id}', $userId, $this->updateRules);
        $validated = $this->validate($userData, $rules);

        // Processamento
        $processed = $this->beforeUpdate($validated);
        if (isset($processed['password'])) {
            $processed['password'] = Hash::make($processed['password']);
        }

        // Persistência
        $user = $this->executeSafely(
            fn() => $this->repository->update($userId, $processed),
            'Failed to update user'
        );

        // Pós-processamento
        $this->afterUpdate($user, $processed);
        return $user;
    }

    public function deleteUser(int $userId): bool
    {
        $success = $this->executeSafely(
            fn() => $this->repository->delete($userId),
            'Failed to delete user'
        );

        if ($success) {
            $this->afterDelete($userId);
        }

        return $success;
    }
}
