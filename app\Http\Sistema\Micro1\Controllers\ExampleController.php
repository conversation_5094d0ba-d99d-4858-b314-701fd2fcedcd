<?php

namespace App\Http\Sistema\Micro1\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Micro1\Services\ExampleService;
use App\Responses\ResponseInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExampleController extends ControllerAbstract
{
    /**
     * Construtor
     *
     * @param ExampleService $service
     * @param ResponseInterface $response
     */
    public function __construct(ExampleService $service, ResponseInterface $response)
    {
        parent::__construct($service, $response);
    }

    /**
     * Lista todos os usuários
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Habilitar o log de consultas SQL
        DB::enableQueryLog();

        // Executar a consulta original
        $users = $this->service->getAllUsers();

        // Obter o log de consultas
        $queries = DB::getQueryLog();


        // Registrar cada consulta na debugbar
        foreach ($queries as $index => $query) {
            $sql = $this->formatSqlQuery($query);
        }

        // Retornar a resposta normal
        return $this->response->success(['users' => $users]);
    }

    /**
     * Formata uma consulta SQL com seus parâmetros
     *
     * @param array $query
     * @return string
     */
    private function formatSqlQuery(array $query): string
    {
        $sql = $query['query'];
        $bindings = $query['bindings'];

        // Substituir parâmetros no SQL
        foreach ($bindings as $binding) {
            $value = is_numeric($binding) ? $binding : "'" . $binding . "'";
            $sql = preg_replace('/\?/', $value, $sql, 1);
        }

        return $sql . ' [' . ($query['time'] / 1000) . ' segundos]';
    }
}
