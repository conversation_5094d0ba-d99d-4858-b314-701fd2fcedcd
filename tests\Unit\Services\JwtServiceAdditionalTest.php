<?php

namespace Tests\Unit\Services;

use App\Exceptions\JWT\InvalidRefreshTokenException;
use App\Services\JwtService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Mockery;

class JwtServiceAdditionalTest extends TestCase
{
    protected $jwtService;
    protected $realJwtService;

    protected function setUp(): void
    {
        parent::setUp();
        // Mock parcial para os testes que precisam simular comportamentos
        $this->jwtService = Mockery::mock(JwtService::class)->makePartial();

        // Instância real para testes de métodos privados
        $this->realJwtService = new JwtService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    #[Test]
    public function it_cleans_up_expired_tokens()
    {
        // Criar usuários mock
        $user1 = Mockery::mock('stdClass');
        $user1->id = 1;

        $user2 = Mockery::mock('stdClass');
        $user2->id = 2;

        $users = collect([$user1, $user2]);

        // Mockamos o método que obtém usuários diretamente no jwtService
        $this->jwtService->shouldReceive('getUsersForCleanup')
            ->once()
            ->andReturn($users);

        // Configurar dados no cache
        $now = Carbon::now();

        // Usuário 1 tem um token expirado e um válido
        Cache::shouldReceive('get')
            ->with('user_tokens:1', [])
            ->andReturn([
                'token1' => [
                    'expires_at' => $now->copy()->subHour()->toIso8601String(), // Expirado
                ],
                'token2' => [
                    'expires_at' => $now->copy()->addHour()->toIso8601String(), // Válido
                ]
            ]);

        // Usuário 2 tem todos os tokens válidos
        Cache::shouldReceive('get')
            ->with('user_tokens:2', [])
            ->andReturn([
                'token3' => [
                    'expires_at' => $now->copy()->addHour()->toIso8601String(), // Válido
                ],
                'token4' => [
                    'expires_at' => $now->copy()->addHour()->toIso8601String(), // Válido
                ]
            ]);

        // Deve atualizar apenas o cache do usuário 1
        Cache::shouldReceive('put')
            ->once()
            ->with('user_tokens:1', Mockery::on(function ($tokens) {
                return count($tokens) === 1 && isset($tokens['token2']);
            }), Mockery::type('object'))
            ->andReturn(true);

        // Executar
        $result = $this->jwtService->cleanupExpiredTokens();

        // Verificar que um token foi removido
        $this->assertEquals(1, $result);
    }

    #[Test]
    public function it_refreshes_token_with_invalid_user_id()
    {
        // Preparação dos dados de teste
        $userId = 999; // ID inexistente
        $tokenId = 'test-id';
        $expiryTime = Carbon::now()->addHour()->toIso8601String();

        // Criar payload do token
        $payload = [
            'user_id' => $userId,
            'token_id' => $tokenId,
            'expires_at' => $expiryTime
        ];

        // Criar o refresh token
        $refreshToken = base64_encode(json_encode($payload));

        // Mock do cache para retornar o payload quando solicitado
        Cache::shouldReceive('get')
            ->with('refresh_token:' . $tokenId)
            ->andReturn($payload);

        // Mock para o método findUserById retornar null (usuário não existe)
        $this->jwtService->shouldReceive('findUserById')
            ->once()
            ->with($userId)
            ->andReturn(null);

        // Esperar a exceção
        $this->expectException(InvalidRefreshTokenException::class);
        $this->expectExceptionMessage("Usuário não encontrado");

        // Executar
        $this->jwtService->refreshToken($refreshToken);
    }

    #[Test]
    public function it_handles_decode_refresh_token_with_invalid_format()
    {
        // Token com formato inválido
        $invalidToken = "not-a-valid-base64-encoded-token";

        // Usar reflection para acessar método privado na instância REAL
        $reflection = new \ReflectionClass($this->realJwtService);
        $method = $reflection->getMethod('decodeRefreshToken');
        $method->setAccessible(true);

        // Esperar exceção
        $this->expectException(InvalidRefreshTokenException::class);
        $this->expectExceptionMessage("Formato de token inválido");

        // Executar no objeto real, não no mock
        $method->invokeArgs($this->realJwtService, [$invalidToken]);
    }

    #[Test]
    public function it_detects_platform_from_user_agent()
    {
        // Exemplos de user agents
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' => 'windows',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)' => 'mac',
            'Mozilla/5.0 (Linux; Android 10; SM-G960U)' => 'android',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)' => 'ios',
            'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0)' => 'linux',
            'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)' => 'other'
        ];

        // Usar reflection para acessar método privado na instância REAL
        $reflection = new \ReflectionClass($this->realJwtService);
        $method = $reflection->getMethod('detectPlatform');
        $method->setAccessible(true);

        // Testar cada user agent no objeto real
        foreach ($userAgents as $userAgent => $expectedPlatform) {
            $result = $method->invokeArgs($this->realJwtService, [$userAgent]);
            $this->assertEquals($expectedPlatform, $result, "Falha ao detectar plataforma para: $userAgent");
        }
    }
}
