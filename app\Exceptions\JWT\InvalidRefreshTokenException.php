<?php

namespace App\Exceptions\JWT;

class InvalidRefreshTokenException extends \Exception
{
    protected $refreshToken;
    protected $reason;

    public function __construct(string $message = "Refresh token inválido", string $reason = '', string $refreshToken = '', \Throwable $previous = null)
    {
        $this->refreshToken = $refreshToken;
        $this->reason = $reason;
        parent::__construct($message, 0, $previous);
    }

    public function getReason(): string
    {
        return $this->reason;
    }

    public function getTokenPreview(): string
    {
        return substr($this->refreshToken, 0, 10) . '...';
    }

    public function getContext(): array
    {
        return [
            'refresh_token' => $this->getTokenPreview(),
            'reason' => $this->reason,
        ];
    }
}
