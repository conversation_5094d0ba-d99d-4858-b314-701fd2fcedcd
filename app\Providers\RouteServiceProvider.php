<?php

namespace App\Providers;

use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->routes(function () {
            // Rotas padrão de API
            $this->load('api', 'routes/api.php');
            // Rotas do sistema 1
            $this->load('api/sistema', 'routes/sistema/micro1.php');
            // Rotas do sistema 2
            $this->load('api/sistema2', 'routes/sistema/micro2.php');
        });
    }

    protected function load($prefix, $path)
    {
        Route::middleware('api')->prefix($prefix)->group(base_path($path));
    }
}
