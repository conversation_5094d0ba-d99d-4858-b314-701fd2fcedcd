<?php

namespace App\Exceptions\JWT;

class TokenRefreshException extends \Exception
{
    protected $refreshToken;

    public function __construct(string $message = "Erro ao atualizar token", string $refreshToken = '', \Throwable $previous = null)
    {
        $this->refreshToken = $refreshToken;
        parent::__construct($message, 0, $previous);
    }

    public function getTokenPreview(): string
    {
        return substr($this->refreshToken, 0, 10) . '...';
    }

    public function getContext(): array
    {
        return [
            'refresh_token' => $this->getTokenPreview(),
        ];
    }
}
