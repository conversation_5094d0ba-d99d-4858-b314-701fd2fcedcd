<?php

namespace App\Models;

use App\Models\RoleModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;

class UserModel extends Authenticatable implements JWTSubject
{
    protected $table = 'users';

    use Notifiable, HasFactory;

    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|string|email|max:255|unique:users',
        'password' => 'required|string|min:8',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Relacionamento com roles
     */
    public function roles()
    {
        return $this->belongsToMany(RoleModel::class, 'user_roles', 'user_id', 'role_id')
            ->withTimestamps();
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     */
    public function getJWTCustomClaims()
    {
        return [
            'email' => $this->email,
            'name' => $this->name,
            'roles' => $this->roles ? $this->roles->pluck('name')->toArray() : [],
        ];
    }

    /**
     * Verifica se o usuário tem uma role específica
     */
    public function hasRole($role)
    {
        return $this->roles->contains('name', $role);
    }

    /**
     * Atribui uma role ao usuário
     */
    public function assignRole($role)
    {
        if (is_string($role)) {
            $role = RoleModel::where('name', $role)->firstOrFail();
        }
        return $this->roles()->syncWithoutDetaching($role);
    }

    /**
     * Remove uma role do usuário
     */
    public function removeRole($role)
    {
        if (is_string($role)) {
            $role = RoleModel::where('name', $role)->firstOrFail();
        }
        return $this->roles()->detach($role);
    }

    /**
     * Obtém todas as permissões do usuário através de suas roles
     */
    public function getAllPermissions()
    {
        return $this->roles->map->permissions->flatten()->unique('id');
    }
}
