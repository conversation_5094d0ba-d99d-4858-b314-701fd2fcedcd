<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Deployment</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #2980b9;
            margin-top: 40px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        h3 {
            color: #3498db;
            margin-top: 25px;
        }

        .code-section {
            margin-bottom: 50px;
        }

        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', Courier, monospace;
            overflow-x: auto;
        }

        .example {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .example h4 {
            margin-top: 0;
            color: #2980b9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        table,
        th,
        td {
            border: 1px solid #ddd;
        }

        th {
            background-color: #f2f2f2;
            padding: 12px;
            text-align: left;
        }

        td {
            padding: 10px;
        }

        .best-practice {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }

        .bad-practice {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }

        .note {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
            padding: 10px;
        }

        footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }

        #sumario {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }

        #sumario ul {
            list-style-type: none;
            padding-left: 20px;
        }

        #sumario ul li {
            margin-bottom: 8px;
        }

        #sumario a {
            color: #2980b9;
            text-decoration: none;
        }

        #sumario a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <header>
        <h1 style="color: white; border-bottom: none;">Manual de Deployment</h1>
        <p>Guia para implantação, configuração e gerenciamento de ambientes da aplicação</p>
    </header>

    <section id="sumario">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#requisitos">2. Requisitos de Sistema</a>
                <ul>
                    <li><a href="#req-hardware">2.1. Requisitos de Hardware</a></li>
                    <li><a href="#req-software">2.2. Requisitos de Software</a></li>
                    <li><a href="#req-rede">2.3. Requisitos de Rede</a></li>
                </ul>
            </li>
            <li><a href="#ambientes">3. Ambientes de Implantação</a>
                <ul>
                    <li><a href="#amb-desenvolvimento">3.1. Ambiente de Desenvolvimento</a></li>
                    <li><a href="#amb-homologacao">3.2. Ambiente de Homologação</a></li>
                    <li><a href="#amb-producao">3.3. Ambiente de Produção</a></li>
                    <li><a href="#amb-isolamento">3.4. Isolamento de Ambientes</a></li>
                </ul>
            </li>
            <li><a href="#build">4. Processo de Build</a>
                <ul>
                    <li><a href="#build-dependencies">4.1. Gerenciamento de Dependências</a></li>
                    <li><a href="#build-assets">4.2. Compilação de Assets</a></li>
                    <li><a href="#build-otimizacao">4.3. Otimização para Produção</a></li>
                    <li><a href="#build-artefatos">4.4. Gerenciamento de Artefatos</a></li>
                </ul>
            </li>
            <li><a href="#deployment">5. Processo de Deployment</a>
                <ul>
                    <li><a href="#deploy-estrategias">5.1. Estratégias de Deployment</a></li>
                    <li><a href="#deploy-manual">5.2. Deployment Manual</a></li>
                    <li><a href="#deploy-automatico">5.3. Deployment Automatizado</a></li>
                    <li><a href="#deploy-rollback">5.4. Procedimentos de Rollback</a></li>
                </ul>
            </li>
            <li><a href="#infra">6. Configuração de Infraestrutura</a>
                <ul>
                    <li><a href="#infra-servidor">6.1. Configuração de Servidor Web</a></li>
                    <li><a href="#infra-banco">6.2. Configuração de Banco de Dados</a></li>
                    <li><a href="#infra-cache">6.3. Configuração de Cache</a></li>
                    <li><a href="#infra-fila">6.4. Configuração de Filas</a></li>
                </ul>
            </li>
            <li><a href="#cicd">7. Integração Contínua (CI/CD)</a>
                <ul>
                    <li><a href="#cicd-pipeline">7.1. Pipeline CI/CD</a></li>
                    <li><a href="#cicd-testes">7.2. Testes Automatizados</a></li>
                    <li><a href="#cicd-qualidade">7.3. Análise de Qualidade de Código</a></li>
                </ul>
            </li>
            <li><a href="#monitoramento">8. Monitoramento e Logs</a>
                <ul>
                    <li><a href="#monitoramento-ferramentas">8.1. Ferramentas de Monitoramento</a></li>
                    <li><a href="#monitoramento-alertas">8.2. Configuração de Alertas</a></li>
                    <li><a href="#monitoramento-logs">8.3. Centralização de Logs</a></li>
                </ul>
            </li>
            <li><a href="#seguranca">9. Segurança</a>
                <ul>
                    <li><a href="#seguranca-ssl">9.1. Configuração de SSL/TLS</a></li>
                    <li><a href="#seguranca-firewall">9.2. Configuração de Firewall</a></li>
                    <li><a href="#seguranca-secrets">9.3. Gerenciamento de Segredos</a></li>
                </ul>
            </li>
            <li><a href="#troubleshooting">10. Troubleshooting</a>
                <ul>
                    <li><a href="#troubleshooting-comuns">10.1. Problemas Comuns e Soluções</a></li>
                    <li><a href="#troubleshooting-diagnostico">10.2. Ferramentas de Diagnóstico</a></li>
                    <li><a href="#troubleshooting-performance">10.3. Diagnóstico de Performance</a></li>
                </ul>
            </li>
        </ul>
    </section>

    <section id="introducao">
        <h2>1. Introdução</h2>
        <p>Este manual tem como objetivo fornecer diretrizes completas para o processo de implantação da aplicação,
            desde o ambiente de desenvolvimento até a produção. O documento aborda todos os aspectos necessários para
            garantir uma implantação bem-sucedida, incluindo requisitos de sistema, configuração de ambiente, automação
            de processos, monitoramento e solução de problemas.</p>

        <div class="note">
            <p><strong>Nota:</strong> Este documento é complementar aos outros manuais da aplicação, como o Manual de
                Arquitetura, Manual de Implementação e Manual de Logging. Recomenda-se a leitura desses documentos para
                uma compreensão mais completa do sistema.</p>
        </div>

        <h3>1.1. Público-alvo</h3>
        <p>Este manual destina-se a:</p>
        <ul>
            <li>DevOps Engineers</li>
            <li>SREs (Site Reliability Engineers)</li>
            <li>Administradores de sistemas</li>
            <li>Desenvolvedores envolvidos no processo de implantação</li>
            <li>Equipe de QA</li>
        </ul>

        <h3>1.2. Princípios de Deployment</h3>
        <p>Nossa estratégia de deployment é guiada pelos seguintes princípios:</p>
        <ul>
            <li><strong>Automação:</strong> Minimizar intervenção manual para reduzir erros humanos</li>
            <li><strong>Consistência:</strong> Garantir que todos os ambientes sejam o mais similares possíveis</li>
            <li><strong>Rastreabilidade:</strong> Manter histórico de todas as alterações e deployments</li>
            <li><strong>Reversibilidade:</strong> Permitir retorno rápido a versões anteriores em caso de problemas</li>
            <li><strong>Zero downtime:</strong> Realizar implantações sem interrupção de serviço</li>
            <li><strong>Segurança:</strong> Garantir a integridade e confidencialidade em todo o processo</li>
        </ul>
    </section>

    <section id="requisitos">
        <h2>2. Requisitos de Sistema</h2>
        <p>Esta seção detalha os requisitos necessários para a implantação da aplicação em cada ambiente.</p>

        <section id="req-hardware">
            <h3>2.1. Requisitos de Hardware</h3>
            <p>Os requisitos de hardware variam de acordo com o ambiente e a carga esperada:</p>

            <table>
                <thead>
                    <tr>
                        <th>Ambiente</th>
                        <th>CPU</th>
                        <th>Memória RAM</th>
                        <th>Armazenamento</th>
                        <th>Observações</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Desenvolvimento</td>
                        <td>2+ cores</td>
                        <td>8+ GB</td>
                        <td>50+ GB SSD</td>
                        <td>Configuração mínima para ambiente local</td>
                    </tr>
                    <tr>
                        <td>Homologação</td>
                        <td>4+ cores</td>
                        <td>16+ GB</td>
                        <td>100+ GB SSD</td>
                        <td>Similar à produção para testes realistas</td>
                    </tr>
                    <tr>
                        <td>Produção (básico)</td>
                        <td>8+ cores</td>
                        <td>32+ GB</td>
                        <td>200+ GB SSD</td>
                        <td>Para tráfego moderado</td>
                    </tr>
                    <tr>
                        <td>Produção (escala)</td>
                        <td>16+ cores</td>
                        <td>64+ GB</td>
                        <td>500+ GB SSD</td>
                        <td>Para alto tráfego, distribuído em múltiplos servidores</td>
                    </tr>
                </tbody>
            </table>

            <div class="note">
                <p>Para ambientes de produção, recomenda-se uma infraestrutura escalável horizontalmente, permitindo
                    adicionar mais recursos conforme necessário. Considere o uso de serviços em nuvem como AWS, Google
                    Cloud ou Azure para maior flexibilidade.</p>
            </div>
        </section>

        <section id="req-software">
            <h3>2.2. Requisitos de Software</h3>
            <p>Os seguintes componentes de software são necessários para executar a aplicação:</p>

            <table>
                <thead>
                    <tr>
                        <th>Componente</th>
                        <th>Versão</th>
                        <th>Notas</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>PHP</td>
                        <td>8.2+</td>
                        <td>Com extensões: BCMath, Ctype, Fileinfo, JSON, Mbstring, OpenSSL, PDO, Tokenizer, XML, GD
                        </td>
                    </tr>
                    <tr>
                        <td>Nginx</td>
                        <td>1.20+</td>
                        <td>Alternativa: Apache 2.4+</td>
                    </tr>
                    <tr>
                        <td>MySQL</td>
                        <td>8.0+</td>
                        <td>Alternativas: MariaDB 10.5+, PostgreSQL 14+</td>
                    </tr>
                    <tr>
                        <td>Redis</td>
                        <td>6.2+</td>
                        <td>Para cache e filas</td>
                    </tr>
                    <tr>
                        <td>Node.js</td>
                        <td>16+</td>
                        <td>Para compilação de assets (apenas no build)</td>
                    </tr>
                    <tr>
                        <td>Composer</td>
                        <td>2.0+</td>
                        <td>Gerenciador de dependências PHP</td>
                    </tr>
                    <tr>
                        <td>Git</td>
                        <td>2.30+</td>
                        <td>Para controle de versão</td>
                    </tr>
                    <tr>
                        <td>Supervisor</td>
                        <td>4.0+</td>
                        <td>Para gerenciamento de processos</td>
                    </tr>
                    <tr>
                        <td>Sistema Operacional</td>
                        <td>Ubuntu 20.04+ LTS</td>
                        <td>Alternativas: Debian 11+, CentOS 8+, Amazon Linux 2</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <section id="req-rede">
            <h3>2.3. Requisitos de Rede</h3>
            <p>Configurações de rede necessárias para a aplicação funcionar corretamente:</p>

            <table>
                <thead>
                    <tr>
                        <th>Serviço</th>
                        <th>Porta</th>
                        <th>Protocolo</th>
                        <th>Acesso</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>HTTP</td>
                        <td>80</td>
                        <td>TCP</td>
                        <td>Público (redirecionado para HTTPS)</td>
                    </tr>
                    <tr>
                        <td>HTTPS</td>
                        <td>443</td>
                        <td>TCP</td>
                        <td>Público</td>
                    </tr>
                    <tr>
                        <td>MySQL/MariaDB</td>
                        <td>3306</td>
                        <td>TCP</td>
                        <td>Privado (apenas servidores da aplicação)</td>
                    </tr>
                    <tr>
                        <td>PostgreSQL</td>
                        <td>5432</td>
                        <td>TCP</td>
                        <td>Privado (apenas servidores da aplicação)</td>
                    </tr>
                    <tr>
                        <td>Redis</td>
                        <td>6379</td>
                        <td>TCP</td>
                        <td>Privado (apenas servidores da aplicação)</td>
                    </tr>
                    <tr>
                        <td>SSH</td>
                        <td>22 (ou personalizado)</td>
                        <td>TCP</td>
                        <td>Privado (apenas IPs autorizados)</td>
                    </tr>
                </tbody>
            </table>

            <div class="warning">
                <p><strong>Importante:</strong> Em ambientes de produção, configure sempre um firewall para limitar o
                    acesso aos serviços. Exponha publicamente apenas as portas estritamente necessárias (tipicamente
                    apenas 80 e 443).</p>
            </div>

            <h4>2.3.1. Integrações Externas</h4>
            <p>Certifique-se de que os servidores têm acesso às seguintes APIs e serviços externos:</p>
            <ul>
                <li>APIs de pagamento (Stripe, PayPal, etc.)</li>
                <li>Serviços de e-mail (SMTP)</li>
                <li>APIs de armazenamento (S3, etc.)</li>
                <li>Serviços de monitoramento</li>
                <li>CDNs</li>
            </ul>
        </section>
    </section>

    <section id="ambientes">
        <h2>3. Ambientes de Implantação</h2>
        <p>A aplicação utiliza múltiplos ambientes para garantir qualidade e estabilidade antes da disponibilização para
            os usuários finais.</p>

        <section id="amb-desenvolvimento">
            <h3>3.1. Ambiente de Desenvolvimento</h3>
            <p>Ambiente utilizado pelos desenvolvedores para codificação e testes iniciais.</p>

            <div class="best-practice">
                <h4>Características do Ambiente de Desenvolvimento</h4>
                <ul>
                    <li>Configuração simplificada, geralmente local ou em containers</li>
                    <li>Debugging habilitado</li>
                    <li>Banco de dados com dados de teste</li>
                    <li>Serviços externos simulados (mocks) ou apontando para sandbox</li>
                    <li>Compilação de assets em tempo real (hot reload)</li>
                    <li>Otimizações desabilitadas para facilitar a depuração</li>
                </ul>
            </div>

            <div class="code-block">
                # Configuração típica do .env para desenvolvimento
                APP_ENV=local
                APP_DEBUG=true
                APP_URL=http://localhost:8000

                LOG_LEVEL=debug

                DB_CONNECTION=mysql
                DB_HOST=127.0.0.1
                DB_PORT=3306
                DB_DATABASE=app_development
                DB_USERNAME=root
                DB_PASSWORD=secret

                CACHE_DRIVER=file
                SESSION_DRIVER=file
                QUEUE_CONNECTION=sync

                # APIs em modo sandbox/teste
                PAYMENT_API_MODE=sandbox
                MAIL_MAILER=log
            </div>

            <h4>3.1.1. Setup do Ambiente de Desenvolvimento</h4>
            <div class="code-block">
                # Clone do repositório
                <NAME_EMAIL>:empresa/projeto.git
                cd projeto

                # Instalação de dependências
                composer install
                npm install

                # Configuração do ambiente
                cp .env.example .env
                php artisan key:generate

                # Criação e migração do banco de dados
                php artisan migrate:fresh --seed

                # Compilação de assets
                npm run dev

                # Iniciar servidor de desenvolvimento
                php artisan serve
            </div>
        </section>

        <section id="amb-homologacao">
            <h3>3.2. Ambiente de Homologação</h3>
            <p>Ambiente intermediário que simula o ambiente de produção, utilizado para testes de integração, validação
                de funcionalidades e aprovação por stakeholders.</p>

            <div class="best-practice">
                <h4>Características do Ambiente de Homologação</h4>
                <ul>
                    <li>Configuração similar à produção</li>
                    <li>Dados anonimizados de produção ou dataset representativo</li>
                    <li>Conectado a versões de sandbox de serviços externos</li>
                    <li>Otimização de assets como em produção</li>
                    <li>Logging mais detalhado que em produção</li>
                    <li>Acesso restrito a usuários autorizados (equipe interna e clientes para UAT)</li>
                </ul>
            </div>

            <div class="code-block">
                # Configuração típica do .env para homologação
                APP_ENV=staging
                APP_DEBUG=false
                APP_URL=https://staging.exemplo.com

                LOG_LEVEL=debug

                DB_CONNECTION=mysql
                DB_HOST=staging-db.internal
                DB_PORT=3306
                DB_DATABASE=app_staging
                DB_USERNAME=app_staging_user
                DB_PASSWORD=${STAGING_DB_PASSWORD}

                CACHE_DRIVER=redis
                SESSION_DRIVER=redis
                QUEUE_CONNECTION=redis

                # APIs em modo sandbox/teste
                PAYMENT_API_MODE=sandbox
                MAIL_MAILER=smtp
                MAIL_HOST=mailhog.internal
            </div>

            <h4>3.2.1. Deploy para Homologação</h4>
            <div class="code-block">
                # Script simplificado de deploy para homologação

                # Definir variáveis
                DEPLOY_PATH="/var/www/staging"
                REPO_URL="**************:empresa/projeto.git"
                BRANCH="develop"

                # 1. Atualizar código-fonte
                cd $DEPLOY_PATH
                git fetch --all
                git checkout $BRANCH
                git pull origin $BRANCH

                # 2. Instalar dependências
                composer install --no-dev --optimize-autoloader
                npm install
                npm run build

                # 3. Atualizar configurações
                cp .env.staging .env
                php artisan config:cache
                php artisan route:cache
                php artisan view:cache

                # 4. Executar migrações
                php artisan migrate --force

                # 5. Limpar cache
                php artisan cache:clear

                # 6. Reiniciar serviços
                sudo supervisorctl restart app-staging-worker:*
                sudo systemctl reload php8.2-fpm
                sudo systemctl reload nginx
            </div>
        </section>

        <section id="amb-producao">
            <h3>3.3. Ambiente de Produção</h3>
            <p>Ambiente final, otimizado para performance, segurança e disponibilidade, onde os usuários acessam a
                aplicação.</p>

            <div class="best-practice">
                <h4>Características do Ambiente de Produção</h4>
                <ul>
                    <li>Alta disponibilidade e escalabilidade</li>
                    <li>Debug desabilitado</li>
                    <li>Máxima otimização de recursos</li>
                    <li>Cache configurado para melhor desempenho</li>
                    <li>Conexão com serviços externos em modo de produção</li>
                    <li>Monitoramento e alertas completos</li>
                    <li>Backups automáticos</li>
                    <li>Segurança reforçada</li>
                </ul>
            </div>

            <div class="code-block">
                # Configuração típica do .env para produção
                APP_ENV=production
                APP_DEBUG=false
                APP_URL=https://exemplo.com

                LOG_LEVEL=warning

                DB_CONNECTION=mysql
                DB_HOST=${PROD_DB_HOST}
                DB_PORT=3306
                DB_DATABASE=${PROD_DB_NAME}
                DB_USERNAME=${PROD_DB_USER}
                DB_PASSWORD=${PROD_DB_PASSWORD}

                CACHE_DRIVER=redis
                SESSION_DRIVER=redis
                QUEUE_CONNECTION=redis
                REDIS_URL=${PROD_REDIS_URL}

                # APIs em modo de produção
                PAYMENT_API_MODE=production
                MAIL_MAILER=smtp
                MAIL_HOST=${PROD_SMTP_HOST}
                MAIL_PORT=${PROD_SMTP_PORT}
                MAIL_USERNAME=${PROD_SMTP_USER}
                MAIL_PASSWORD=${PROD_SMTP_PASSWORD}
                MAIL_ENCRYPTION=tls
            </div>

            <h4>3.3.1. Configuração de Servidor Web para Produção</h4>
            <div class="code-block">
                # Nginx configuration

                server {
                listen 80;
                server_name exemplo.com www.exemplo.com;

                # Redirect to HTTPS
                location / {
                return 301 https://$host$request_uri;
                }
                }

                server {
                listen 443 ssl http2;
                server_name exemplo.com www.exemplo.com;

                ssl_certificate /etc/letsencrypt/live/exemplo.com/fullchain.pem;
                ssl_certificate_key /etc/letsencrypt/live/exemplo.com/privkey.pem;
                ssl_protocols TLSv1.2 TLSv1.3;
                ssl_prefer_server_ciphers on;
                ssl_ciphers
                ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

                # SSL optimizations
                ssl_session_cache shared:SSL:10m;
                ssl_session_timeout 10m;
                ssl_session_tickets off;

                # HSTS (optional - uncomment after testing)
                # add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

                # Security headers
                add_header X-Content-Type-Options "nosniff" always;
                add_header X-XSS-Protection "1; mode=block" always;
                add_header X-Frame-Options "SAMEORIGIN" always;
                add_header Referrer-Policy "strict-origin-when-cross-origin" always;

                root /var/www/production/public;
                index index.php;

                location / {
                try_files $uri $uri/ /index.php?$query_string;
                }

                location ~ \.php$ {
                fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                include fastcgi_params;
                fastcgi_intercept_errors on;
                fastcgi_buffer_size 16k;
                fastcgi_buffers 4 16k;
                }

                # Static files caching
                location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
                expires 7d;
                add_header Cache-Control "public, no-transform";
                }

                # Deny access to .htaccess files
                location ~ /\.ht {
                deny all;
                }

                # Deny access to hidden files and directories
                location ~ /\. {
                deny all;
                }
                }
            </div>
        </section>

        <section id="amb-isolamento">
            <h3>3.4. Isolamento de Ambientes</h3>
            <p>Para garantir segurança, estabilidade e evitar contaminação de dados, os ambientes devem ser
                completamente isolados uns dos outros.</p>

            <div class="best-practice">
                <h4>Práticas de Isolamento</h4>
                <ul>
                    <li>Utilize servidores/instâncias separados para cada ambiente</li>
                    <li>Bancos de dados separados, preferencialmente em servidores diferentes</li>
                    <li>Controle de acesso específico para cada ambiente</li>
                    <li>Configurações e variáveis de ambiente distintas</li>
                    <li>Integração com serviços externos em modos compatíveis (sandbox para desenvolvimento e
                        homologação)</li>
                    <li>Firewalls e regras de rede separadas para cada ambiente</li>
                    <li>Subdomínios distintos para cada ambiente (ex: dev.exemplo.com, staging.exemplo.com, exemplo.com)
                    </li>
                    <li>Nunca copiar dados de produção para outros ambientes sem anonimização</li>
                </ul>

                <div class="warning">
                    <p><strong>Importante:</strong> Dados sensíveis de produção nunca devem estar disponíveis em
                        ambientes de desenvolvimento ou homologação sem o devido processo de anonimização.</p>
                </div>

                <h4>3.4.1. Anonimização de Dados</h4>
                <p>Quando necessário utilizar dados de produção em outros ambientes, utilize técnicas de anonimização:
                </p>

                <div class="code-block">
                    # Script de exemplo para anonimizar dados (execute em staging, nunca em produção)
                    php artisan backup:anonymize-database --output=staging_db.sql

                    # Exemplo do que o comando acima poderia fazer:
                    namespace App\Console\Commands;

                    use Illuminate\Console\Command;
                    use Illuminate\Support\Facades\DB;
                    use Faker\Factory as Faker;

                    class AnonymizeDatabaseCommand extends Command
                    {
                    protected $signature = 'backup:anonymize-database {--output=anonymized_db.sql}';
                    protected $description = 'Cria um dump do banco com dados sensíveis anonimizados';

                    public function handle()
                    {
                    $this->info('Anonimizando dados sensíveis...');
                    $faker = Faker::create('pt_BR');

                    // Atualiza dados sensíveis nas tabelas
                    DB::table('users')->update([
                    'email' => DB::raw("CONCAT('user_', id, '@example.com')"),
                    'name' => DB::raw("CONCAT('User ', id)"),
                    'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' // 'password'
                    ]);

                    DB::table('customers')->update([
                    'phone' => DB::raw("CONCAT('+551199', LPAD(id, 7, '0'))"),
                    'document' => DB::raw("LPAD(id, 11, '0')"),
                    'address' => 'Rua de Exemplo, 123'
                    ]);

                    // Exporta banco anonimizado
                    $output = $this->option('output');
                    $this->info("Exportando para $output...");

                    // Lógica de exportação do banco aqui...

                    $this->info('Concluído!');
                    }
                    }
                </div>
        </section>
    </section>

    <section id="build">
        <h2>4. Processo de Build</h2>
        <p>O processo de build transforma o código fonte em artefatos prontos para implantação, incluindo compilação,
            otimização e empacotamento.</p>

        <section id="build-dependencies">
            <h3>4.1. Gerenciamento de Dependências</h3>
            <p>O gerenciamento correto de dependências é crucial para garantir builds consistentes e confiáveis.</p>

            <div class="best-practice">
                <h4>Boas Práticas para Dependências</h4>
                <ul>
                    <li>Especifique versões exatas nas dependências para evitar incompatibilidades inesperadas</li>
                    <li>Utilize lock files (composer.lock, package-lock.json) e mantenha-os no controle de versão</li>
                    <li>Revise regularmente as dependências em busca de vulnerabilidades</li>
                    <li>Mantenha um registro das dependências e suas licenças</li>
                    <li>Considere utilizar repositórios privados de pacotes para dependências críticas</li>
                </ul>
            </div>

            <div class="code-block">
                # Instalação de dependências PHP para produção
                composer install --no-dev --optimize-autoloader

                # Instalação de dependências JavaScript para produção
                npm ci --production

                # Verificação de vulnerabilidades
                composer audit
                npm audit
            </div>

            <h4>4.1.1. Otimização do Autoloader</h4>
            <p>Para melhorar a performance em produção, otimize o autoloader do Composer:</p>

            <div class="code-block">
                composer dump-autoload --optimize --no-dev --classmap-authoritative
            </div>

            <p>Essa configuração gera um mapa de classes estático para todas as classes que podem ser carregadas
                automaticamente, melhorando significativamente o tempo de carregamento.</p>
        </section>

        <section id="build-assets">
            <h3>4.2. Compilação de Assets</h3>
            <p>A compilação de assets frontend é uma etapa essencial do processo de build:</p>

            <div class="code-block">
                # Compilação básica para desenvolvimento
                npm run dev

                # Compilação com hot reload (útil para desenvolvimento)
                npm run hot

                # Compilação para produção (minificada e otimizada)
                npm run build
            </div>

            <h4>4.2.1. Configuração do Laravel Mix/Vite</h4>
            <p>Exemplo de configuração do Laravel Mix para diferentes ambientes:</p>

            <div class="code-block">
                // vite.config.js (para Laravel 9+)
                import { defineConfig } from 'vite';
                import laravel from 'laravel-vite-plugin';

                export default defineConfig({
                plugins: [
                laravel({
                input: ['resources/css/app.css', 'resources/js/app.js'],
                refresh: true,
                }),
                ],
                build: {
                // Configurações de build de produção
                minify: true,
                sourcemap: false,
                rollupOptions: {
                output: {
                manualChunks: {
                vendor: ['vue', 'axios'],
                }
                }
                }
                }
                });

                // webpack.mix.js (para Laravel 8 e anteriores)
                const mix = require('laravel-mix');

                mix.js('resources/js/app.js', 'public/js')
                .vue()
                .sass('resources/sass/app.scss', 'public/css')
                .version();

                // Configurações específicas para produção
                if (mix.inProduction()) {
                mix.version()
                .sourceMaps()
                .disableNotifications();
                }
            </div>
        </section>

        <section id="build-otimizacao">
            <h3>4.3. Otimização para Produção</h3>
            <p>Além da compilação de assets, outras otimizações devem ser aplicadas durante o build para produção:</p>

            <div class="code-block">
                # Cache de configurações
                php artisan config:cache

                # Cache de rotas
                php artisan route:cache

                # Cache de views
                php artisan view:cache

                # Otimização do framework
                php artisan optimize
            </div>

            <div class="warning">
                <p><strong>Atenção:</strong> O cache de configurações impede alterações dinâmicas em arquivos .env. O
                    cache de rotas pode causar problemas se sua aplicação utiliza closures para definir rotas.</p>
            </div>

            <h4>4.3.1. Personalização da Build por Ambiente</h4>
            <p>É possível personalizar o processo de build para cada ambiente:</p>

            <div class="code-block">
                # Script de build para diferentes ambientes
                function build_for_environment() {
                ENV=$1

                echo "Iniciando build para ambiente: $ENV"

                # Copiar arquivo de ambiente apropriado
                cp .env.$ENV .env

                # Instalar dependências
                if [ "$ENV" == "production" ] || [ "$ENV" == "staging" ]; then
                composer install --no-dev --optimize-autoloader
                npm ci --production
                else
                composer install
                npm ci
                fi

                # Compilar assets
                if [ "$ENV" == "production" ]; then
                npm run build
                else
                npm run dev
                fi

                # Otimizações para ambientes de homologação e produção
                if [ "$ENV" == "production" ] || [ "$ENV" == "staging" ]; then
                php artisan config:cache
                php artisan route:cache
                php artisan view:cache
                php artisan optimize
                fi

                echo "Build concluída para ambiente: $ENV"
                }

                # Uso: ./build.sh production
                build_for_environment "${1:-development}"
            </div>
        </section>

        <section id="build-artefatos">
            <h3>4.4. Gerenciamento de Artefatos</h3>
            <p>O resultado do processo de build deve ser gerenciado de forma organizada:</p>

            <div class="best-practice">
                <h4>Práticas para Gerenciamento de Artefatos</h4>
                <ul>
                    <li>Versione claramente cada artefato gerado (ex: app-v1.2.3.zip)</li>
                    <li>Mantenha um repositório de artefatos com controle de acesso</li>
                    <li>Inclua metadados como data de build, commit hash e ambiente de destino</li>
                    <li>Mantenha versões anteriores para facilitar rollbacks</li>
                    <li>Automatize a verificação de integridade dos artefatos</li>
                </ul>
            </div>

            <div class="code-block">
                # Script para gerar artefato para deploy
                #!/bin/bash

                VERSION=$(git describe --tags --always)
                COMMIT=$(git rev-parse --short HEAD)
                DATE=$(date +%Y%m%d%H%M%S)
                ARTIFACT_NAME="app-${VERSION}-${DATE}.zip"

                # Build da aplicação
                composer install --no-dev --optimize-autoloader
                npm ci --production
                npm run build

                # Otimizações
                php artisan config:cache
                php artisan route:cache
                php artisan view:cache

                # Criar arquivo com metadados
                echo "Version: ${VERSION}" > build-info.txt
                echo "Commit: ${COMMIT}" >> build-info.txt
                echo "Build Date: $(date)" >> build-info.txt
                echo "Environment: production" >> build-info.txt

                # Criar arquivo zip com os arquivos necessários
                zip -r ${ARTIFACT_NAME} \
                app \
                bootstrap \
                config \
                database \
                lang \
                public \
                resources/lang \
                resources/views \
                routes \
                storage/app/public \
                storage/framework/cache/data \
                storage/framework/sessions \
                storage/framework/views \
                vendor \
                artisan \
                build-info.txt \
                composer.json \
                composer.lock

                # Upload do artefato para repositório (exemplo com AWS S3)
                aws s3 cp ${ARTIFACT_NAME} s3://artifacts-bucket/releases/

                echo "Artefato criado: ${ARTIFACT_NAME}"
            </div>
        </section>
    </section>

    <section id="deployment">
        <h2>5. Processo de Deployment</h2>
        <p>O processo de deployment consiste na implantação dos artefatos gerados no processo de build para os ambientes
            de destino.</p>

        <section id="deploy-estrategias">
            <h3>5.1. Estratégias de Deployment</h3>
            <p>Existem várias estratégias de deployment, cada uma com suas vantagens e desvantagens:</p>

            <table>
                <thead>
                    <tr>
                        <th>Estratégia</th>
                        <th>Descrição</th>
                        <th>Vantagens</th>
                        <th>Desvantagens</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Big Bang / All-at-once</td>
                        <td>Atualiza toda a aplicação de uma só vez</td>
                        <td>Simplicidade, fácil de implementar</td>
                        <td>Downtime durante a implantação, risco maior</td>
                    </tr>
                    <tr>
                        <td>Rolling Deployment</td>
                        <td>Atualiza servidores gradualmente, um a um</td>
                        <td>Minimiza o impacto de falhas, pouco ou nenhum downtime</td>
                        <td>Tempo maior para completar, maior complexidade</td>
                    </tr>
                    <tr>
                        <td>Blue/Green Deployment</td>
                        <td>Mantém dois ambientes idênticos e alterna entre eles</td>
                        <td>Zero downtime, rollback imediato</td>
                        <td>Duplicação de infraestrutura, maior custo</td>
                    </tr>
                    <tr>
                        <td>Canary Deployment</td>
                        <td>Libera nova versão para uma pequena porcentagem dos usuários</td>
                        <td>Teste em produção com exposição limitada</td>
                        <td>Complexidade na implementação, tempo de implantação maior</td>
                    </tr>
                </tbody>
            </table>

            <div class="note">
                <p>Para nossa aplicação, recomendamos a estratégia <strong>Blue/Green Deployment</strong> para produção
                    e <strong>All-at-once</strong> para ambientes de desenvolvimento e homologação.</p>
            </div>
        </section>

        <section id="deploy-manual">
            <h3>5.2. Deployment Manual</h3>
            <p>Embora recomendemos a automação do processo, este é o procedimento para deploy manual:</p>

            <div class="code-block">
                # Script para deploy manual
                #!/bin/bash

                # Configurações
                APP_PATH="/var/www/production"
                BACKUP_PATH="/var/www/backups"
                DEPLOY_LOG="/var/log/deploy.log"
                RELEASE_TAG=$1

                if [ -z "$RELEASE_TAG" ]; then
                echo "Uso: ./deploy.sh v1.2.3"
                exit 1
                fi

                echo "$(date) - Iniciando deploy da versão $RELEASE_TAG" >> $DEPLOY_LOG

                # 1. Criar backup do ambiente atual
                BACKUP_DIR="${BACKUP_PATH}/backup-$(date +%Y%m%d%H%M%S)"
                echo "Criando backup em $BACKUP_DIR" >> $DEPLOY_LOG
                mkdir -p $BACKUP_DIR
                cp -r $APP_PATH/* $BACKUP_DIR/

                # 2. Atualizar código-fonte
                cd $APP_PATH
                git fetch --all
                git checkout $RELEASE_TAG

                # 3. Instalar dependências
                echo "Instalando dependências" >> $DEPLOY_LOG
                composer install --no-dev --optimize-autoloader

                # 4. Colocar aplicação em modo de manutenção
                php artisan down --render="errors::maintenance" --secret="1630542a-246b-4b66-afa1-dd72a4c43515"

                # 5. Limpar caches
                php artisan cache:clear
                php artisan config:clear
                php artisan route:clear
                php artisan view:clear

                # 6. Executar migrações
                echo "Executando migrações" >> $DEPLOY_LOG
                php artisan migrate --force

                # 7. Recriar caches para otimização
                php artisan config:cache
                php artisan route:cache
                php artisan view:cache

                # 8. Reiniciar workers e filas
                echo "Reiniciando workers" >> $DEPLOY_LOG
                php artisan queue:restart
                sudo supervisorctl restart all

                # 9. Tirar site do modo de manutenção
                php artisan up

                echo "$(date) - Deploy concluído com sucesso" >> $DEPLOY_LOG
            </div>

            <div class="warning">
                <p><strong>Atenção:</strong> O deploy manual está sujeito a erros humanos e inconsistências. É altamente
                    recomendável utilizar processos automatizados para garantir consistência e confiabilidade.</p>
            </div>
        </section>

        <section id="deploy-automatico">
            <h3>5.3. Deployment Automatizado</h3>
            <p>A automação do deployment traz diversos benefícios, como consistência, velocidade e redução de erros
                humanos.</p>

            <h4>5.3.1. Usando Ferramentas de Deployment</h4>
            <p>Existem diversas ferramentas que podem ajudar na automação do processo:</p>

            <ul>
                <li><strong>Laravel Forge:</strong> Serviço específico para aplicações Laravel</li>
                <li><strong>Laravel Envoyer:</strong> Especializado em deployments com zero downtime</li>
                <li><strong>GitHub Actions:</strong> Para integrar CI/CD diretamente no repositório</li>
                <li><strong>Jenkins:</strong> Servidor de automação de código aberto</li>
                <li><strong>GitLab CI/CD:</strong> Solução integrada ao GitLab</li>
                <li><strong>Deployer:</strong> Ferramenta de deploy em PHP</li>
            </ul>

            <h4>5.3.2. Exemplo com Deployer</h4>
            <div class="code-block">
                # Instalação: composer require deployer/deployer --dev

                # deploy.php
                <?php
                namespace Deployer;
                
                require 'recipe/laravel.php';
                
                // Configuração do projeto
                set('application', 'meu-projeto');
                set('repository', '**************:empresa/projeto.git');
                set('git_tty', true);
                set('keep_releases', 5);
                
                // Hosts
                host('producao')
                    ->hostname('servidor.exemplo.com')
                    ->user('deploy')
                    ->identityFile('~/.ssh/id_rsa')
                    ->set('deploy_path', '/var/www/production');
                
                host('homologacao')
                    ->hostname('staging.exemplo.com')
                    ->user('deploy')
                    ->identityFile('~/.ssh/id_rsa')
                    ->set('deploy_path', '/var/www/staging');
                
                // Tarefas
                task('build:assets', function () {
                    cd('{{release_path}}');
                    run('npm ci --production');
                    run('npm run build');
                });
                
                task('artisan:optimize', function () {
                    cd('{{release_path}}');
                    run('php artisan optimize');
                });
                
                // Pipeline de deploy
                after('deploy:vendors', 'build:assets');
                after('deploy:vendors', 'artisan:optimize');
                
                // Hooks
                after('deploy:failed', 'deploy:unlock');
                        </div>
                
                        <p>Usando o exemplo acima, o deploy pode ser executado com:</p>
                
                        <div class="code-block">
                # Deploy para ambiente de homologação
                php vendor/bin/dep deploy homologacao
                
                # Deploy para produção
                php vendor/bin/dep deploy producao
                        </div>
                    </section>
                
                    <section id="deploy-rollback">
                        <h3>5.4. Procedimentos de Rollback</h3>
                        <p>É essencial ter um plano bem definido para reverter as alterações em caso de problemas.</p>
                
                        <div class="best-practice">
                            <h4>Práticas para Rollback Efetivo</h4>
                            <ul>
                                <li>Mantenha sempre a versão anterior funcionando</li>
                                <li>Realize backups de banco de dados antes de migrações críticas</li>
                                <li>Documente e teste procedimentos de rollback</li>
                                <li>Utilize ferramentas que suportam rollback com um único comando</li>
                                <li>Monitore ativamente após um deploy para detectar problemas rapidamente</li>
                            </ul>
                        </div>
                
                        <h4>5.4.1. Rollback Manual</h4>
                
                        <div class="code-block">
                # Script para rollback manual
                #!/bin/bash
                
                # Configurações
                APP_PATH="/var/www/production"
                BACKUP_PATH="/var/www/backups"
                DEPLOY_LOG="/var/log/deploy.log"
                
                # Encontrar o backup mais recente
                LATEST_BACKUP=$(ls -td -- $BACKUP_PATH/* | head -n 1)
                
                if [ -z "$LATEST_BACKUP" ]; then
                    echo "Nenhum backup encontrado para rollback" >> $DEPLOY_LOG
                    exit 1
                fi
                
                echo "$(date) - Iniciando rollback para backup: $LATEST_BACKUP" >> $DEPLOY_LOG
                
                # 1. Colocar o site em modo de manutenção
                cd $APP_PATH
                php artisan down --render="errors::maintenance" --secret="1630542a-246b-4b66-afa1-dd72a4c43515"
                
                # 2. Restaurar a partir do backup
                echo "Restaurando a partir de: $LATEST_BACKUP" >> $DEPLOY_LOG
                rsync -avz --delete $LATEST_BACKUP/ $APP_PATH/
                
                # 3. Reiniciar serviços
                echo "Reiniciando serviços" >> $DEPLOY_LOG
                php artisan queue:restart
                sudo supervisorctl restart all
                
                # 4. Tirar o site do modo de manutenção
                php artisan up
                
                echo "$(date) - Rollback concluído com sucesso" >> $DEPLOY_LOG
                        </div>
                
                        <h4>5.4.2. Rollback Automatizado</h4>
                        <p>Com ferramentas como Deployer, o rollback pode ser simples:</p>
                
                        <div class="code-block">
                # Rollback com Deployer
                php vendor/bin/dep rollback producao
                
                # Com GitHub Actions ou outras ferramentas de CI/CD
                # Configurar um job específico para rollback que pode ser acionado manualmente
                        </div>
                
                        <div class="note">
                            <p>Teste regularmente os procedimentos de rollback. Um plano de rollback não testado pode falhar quando mais necessário.</p>
                        </div>
                    </section>
                </section>
                
                <section id="infra">
                    <h2>6. Configuração de Infraestrutura</h2>
                    <p>Esta seção descreve as configurações necessárias para cada componente da infraestrutura.</p>
                
                    <section id="infra-servidor">
                        <h3>6.1. Configuração de Servidor Web</h3>
                        <p>Configuração recomendada para os principais servidores web:</p>
                
                        <h4>6.1.1. Nginx (Recomendado)</h4>
                        <div class="code-block">
                # /etc/nginx/sites-available/example.com.conf
                
                server {
                    listen 80;
                    server_name example.com www.example.com;
                    
                    # Redirect all HTTP to HTTPS
                    return 301 https://$host$request_uri;
                }
                
                server {
                    listen 443 ssl http2;
                    server_name example.com www.example.com;
                    
                    # SSL Configuration
                    ssl_certificate /etc/letsencrypt/live/example.com/fullchain.pem;
                    ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;
                    ssl_protocols TLSv1.2 TLSv1.3;
                    
                    # Document root
                    root /var/www/production/public;
                    index index.php;
                    
                    # Main location
                    location / {
                        try_files $uri $uri/ /index.php?$query_string;
                    }
                    
                    # PHP FPM Configuration
                    location ~ \.php$ {
                        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
                        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
                        include fastcgi_params;
                    }
                    
                    # Deny access to hidden files
                    location ~ /\. {
                        deny all;
                    }
                    
                    # Static files caching
                    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff2|woff|ttf)$ {
                        expires 30d;
                        add_header Cache-Control "public, no-transform";
                    }
                    
                    # Security headers
                    add_header X-Content-Type-Options "nosniff" always;
                    add_header X-XSS-Protection "1; mode=block" always;
                    add_header X-Frame-Options "SAMEORIGIN" always;
                    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
                    
                    # Logging
                    access_log /var/log/nginx/example.com-access.log;
                    error_log /var/log/nginx/example.com-error.log;
                }
                        </div>
                
                        <h4>6.1.2. Apache</h4>
                        <div class="code-block">
                # /etc/apache2/sites-available/example.com.conf
                
                <VirtualHost *:80>
                    ServerName example.com
                    ServerAlias www.example.com
                    
                    # Redirect to HTTPS
                    RewriteEngine On
                    RewriteRule ^(.*)$ https://%{HTTP_HOST}$1 [R=301,L]
                </VirtualHost>
                
                <VirtualHost *:443>
                    ServerName example.com
                    ServerAlias www.example.com
                    
                    # Document root
                    DocumentRoot /var/www/production/public
                    
                    # SSL Configuration
                    SSLEngine on
                    SSLCertificateFile /etc/letsencrypt/live/example.com/fullchain.pem
                    SSLCertificateKeyFile /etc/letsencrypt/live/example.com/privkey.pem
                    
                    <Directory /var/www/production/public>
                        Options -Indexes +FollowSymLinks
                        AllowOverride All
                        Require all granted
                    </Directory>
                    
                    # Security headers
                    Header always set X-Content-Type-Options "nosniff"
                    Header always set X-XSS-Protection "1; mode=block"
                    Header always set X-Frame-Options "SAMEORIGIN"
                    Header always set Referrer-Policy "strict-origin-when-cross-origin"
                    
                    # Logging
                    ErrorLog ${APACHE_LOG_DIR}/example.com-error.log
                    CustomLog ${APACHE_LOG_DIR}/example.com-access.log combined
                </VirtualHost>
                        </div>
                    </section>
                
                    <section id="infra-banco">
                        <h3>6.2. Configuração de Banco de Dados</h3>
                        <p>Configurações recomendadas para bancos de dados:</p>
                
                        <h4>6.2.1. MySQL/MariaDB</h4>
                        <div class="code-block">
                # /etc/mysql/my.cnf ou /etc/mysql/mysql.conf.d/mysqld.cnf
                
                [mysqld]
                # Performance
                innodb_buffer_pool_size = 2G              # Ajustar conforme RAM disponível (50-70% para servidores dedicados)
                innodb_log_file_size = 512M               # Maior para melhor performance em escrita
                innodb_flush_method = O_DIRECT            # Bypassar cache do sistema operacional
                innodb_flush_log_at_trx_commit = 2        # 1 para durabilidade total, 2 para melhor performance
                max_connections = 150                      # Ajustar conforme necessidade
                table_open_cache = 2000                   # Tabelas são mantidas em cache
                
                # Charset e Collation
                character-set-server = utf8mb4
                collation-server = utf8mb4_unicode_ci
                
                # Query cache (desabilitado no MySQL 8+)
                # MySQL 5.7 e anteriores:
                query_cache_type = 0                      # Desabilitado por questões de performance
                query_cache_size = 0
                
                # Logging
                slow_query_log = 1
                slow_query_log_file = /var/log/mysql/mysql-slow.log
                long_query_time = 2                       # Queries mais longas que 2 segundos são logadas
                
                # Segurança
                max_allowed_packet = 16M
                sql_mode = STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION
                        </div>
                
                        <h4>6.2.2. PostgreSQL</h4>
                        <div class="code-block">
                # /etc/postgresql/14/main/postgresql.conf
                
                # Conexões
                max_connections = 100                     # Ajustar conforme necessidade
                shared_buffers = 1GB                      # 25% da RAM para início (ajustar conforme uso)
                work_mem = 32MB                           # Memória de trabalho por operação
                
                # Escrita e checkpoints
                wal_level = replica                       # Mínimo para replicação
                max_wal_size = 1GB                        # Tamanho máximo de WAL antes de checkpoint
                checkpoint_timeout = 15min                # Tempo entre checkpoints automáticos
                random_page_cost = 1.1                    # Menor para SSDs
                
                # Cache
                effective_cache_size = 3GB                # Estimativa de cache do SO (50-75% da RAM)
                
                # Logs
                log_min_duration_statement = 1000         # Logar queries com duração maior que 1s
                log_statement = 'none'                    # Opções: 'none', 'ddl', 'mod', 'all'
                log_line_prefix = '%t [%p]: [%l-1] %u@%d '
                
                # Locale
                datestyle = 'iso, mdy'
                timezone = 'UTC'
                lc_messages = 'en_US.UTF-8'
                lc_monetary = 'en_US.UTF-8'
                lc_numeric = 'en_US.UTF-8'
                lc_time = 'en_US.UTF-8'
                        </div>
                
                        <div class="best-practice">
                            <h4>Boas Práticas para Banco de Dados</h4>
<ul>
    <li>Configure backups automáticos regulares</li>
    <li>Implemente replicação para alta disponibilidade</li>
    <li>Utilize índices adequadamente para otimizar consultas frequentes</li>
    <li>Execute regularmente processos de vacuum/otimização</li>
    <li>Monitore o crescimento do banco e planeje expansões</li>
    <li>Implemente particionamento para tabelas grandes</li>
    <li>Configure conexões seguras (SSL/TLS) entre aplicação e banco</li>
    <li>Mantenha as estatísticas do banco atualizadas para melhorar o planejamento de queries</li>
    <li>Limite o acesso direto ao banco de dados apenas a usuários autorizados</li>
    <li>Use usuários diferentes com permissões específicas para cada ambiente</li>
</ul>

<h4>6.2.3. Backups de Banco de Dados</h4>
<div class="code-block">
# Script para backup de banco MySQL
#!/bin/bash

# Configurações
DB_USER="backup_user"
DB_PASS="senha_segura"
DB_HOST="localhost"
BACKUP_DIR="/var/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=14

# Criar diretório se não existir
mkdir -p $BACKUP_DIR

# Gerar lista de todos os bancos de dados, exceto os do sistema
DATABASES=$(mysql -u$DB_USER -p$DB_PASS -h$DB_HOST -e "SHOW DATABASES;" | grep -Ev "(Database|information_schema|performance_schema|mysql|sys)")

# Realizar backup para cada banco de dados
for db in $DATABASES; do
    echo "Backup do banco: $db"
    mysqldump --single-transaction --quick --lock-tables=false \
        -u$DB_USER -p$DB_PASS -h$DB_HOST $db | gzip > "$BACKUP_DIR/$db-$DATE.sql.gz"
done

# Adicionar backup ao registro
echo "$(date) - Backup concluído. Arquivos salvos em $BACKUP_DIR" >> /var/log/mysql-backup.log

# Remover backups antigos (mais de X dias)
find $BACKUP_DIR -type f -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
echo "$(date) - Backups com mais de $RETENTION_DAYS dias foram removidos." >> /var/log/mysql-backup.log
</div>

<div class="example">
    <h4>Automação de Backups com Cron</h4>
    <p>Adicione o script de backup ao crontab para execução automática:</p>
    <div class="code-block">
# Execute o backup diariamente às 02:00
0 2 * * * /usr/local/bin/database-backup.sh >> /var/log/backup-cron.log 2>&1
    </div>
</div>
</section>

<section id="infra-cache">
    <h3>6.3. Configuração de Cache</h3>
    <p>A configuração adequada do cache melhora significativamente a performance da aplicação:</p>

    <h4>6.3.1. Redis (Recomendado)</h4>
    <div class="code-block">
# /etc/redis/redis.conf

# Básico
port 6379
bind 127.0.0.1
protected-mode yes
daemonize yes

# Persistência
save 900 1           # Persistir se houver pelo menos 1 alteração em 15 minutos
save 300 10          # Persistir se houver pelo menos 10 alterações em 5 minutos
save 60 10000        # Persistir se houver pelo menos 10000 alterações em 1 minuto
stop-writes-on-bgsave-error yes
rdbcompression yes
dir /var/lib/redis

# Memória e políticas de evicção
maxmemory 1gb        # Ajustar conforme necessidade
maxmemory-policy allkeys-lru  # Remover chaves menos usadas recentemente quando necessário

# Segurança
requirepass sua_senha_forte_aqui  # Definir senha forte

# Ajustes de performance
tcp-keepalive 300
databases 16
    </div>

    <h4>6.3.2. Configuração no Laravel</h4>
    <div class="code-block">
# .env para configuração de cache no Laravel

# Cache
CACHE_DRIVER=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=sua_senha_forte_aqui
REDIS_PORT=6379
REDIS_CLIENT=phpredis
REDIS_CACHE_DB=1

# Session (também usando Redis)
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_CONNECTION=default
    </div>

    <div class="best-practice">
        <h4>Estratégias de Cache</h4>
        <ul>
            <li>Utilize cache em múltiplos níveis (application, query, HTTP)</li>
            <li>Configure TTLs (Time-to-Live) apropriados para diferentes tipos de dados</li>
            <li>Use tags para facilitar a invalidação de grupos de cache relacionados</li>
            <li>Aplique cache de página para rotas de alto tráfego</li>
            <li>Implemente bust de cache em novas versões da aplicação</li>
            <li>Monitore o uso do cache e ajuste as políticas de evicção conforme necessário</li>
        </ul>
    </div>

    <h4>6.3.3. Exemplo de Implementação de Cache</h4>
    <div class="code-block">
namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Support\Facades\Cache;

class ProductController extends Controller
{
    public function index()
    {
        // Cache por 60 minutos
        $products = Cache::remember('products.all', 3600, function () {
            return Product::with(['category', 'tags'])->active()->get();
        });
        
        return view('products.index', compact('products'));
    }
    
    public function show($slug)
    {
        // Cache por produto individual
        $product = Cache::remember("products.{$slug}", 3600, function () use ($slug) {
            return Product::where('slug', $slug)
                ->with(['category', 'tags', 'reviews'])
                ->firstOrFail();
        });
        
        return view('products.show', compact('product'));
    }
    
    public function store(Request $request)
    {
        // Lógica para salvar produto...
        
        // Limpar cache após alteração
        Cache::forget('products.all');
        Cache::tags(['products'])->flush();
        
        return redirect()->route('products.index');
    }
}
    </div>
</section>

<section id="infra-fila">
    <h3>6.4. Configuração de Filas</h3>
    <p>As filas são essenciais para processar tarefas assíncronas e melhorar a experiência do usuário:</p>

    <h4>6.4.1. Configuração de Queue no Laravel</h4>
    <div class="code-block">
# .env para configuração de filas

QUEUE_CONNECTION=redis
REDIS_QUEUE=default
    </div>

    <h4>6.4.2. Supervisor para Gerenciamento de Workers</h4>
    <div class="code-block">
# /etc/supervisor/conf.d/laravel-worker.conf

[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/production/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/var/www/production/storage/logs/worker.log
stopwaitsecs=3600
    </div>

    <p>Após criar o arquivo de configuração, execute:</p>
    <div class="code-block">
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start laravel-worker:*
    </div>

    <div class="best-practice">
        <h4>Boas Práticas para Filas</h4>
        <ul>
            <li>Divida as filas por tipo/prioridade de trabalho (emails, processamento pesado, etc.)</li>
            <li>Configure tentativas (retries) e backoff para lidar com falhas temporárias</li>
            <li>Implemente mecanismos de dead-letter para jobs que falham sistematicamente</li>
            <li>Monitore o tamanho das filas e o tempo de processamento</li>
            <li>Dimensione o número de workers conforme a carga de trabalho</li>
            <li>Documente os tipos de jobs e seus requisitos</li>
        </ul>
    </div>

    <h4>6.4.3. Exemplo de Configuração Multi-filas</h4>
    <div class="code-block">
# /etc/supervisor/conf.d/laravel-workers.conf

[program:high-priority]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/production/artisan queue:work redis --queue=high,notifications --sleep=3 --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/www/production/storage/logs/worker-high.log

[program:default-priority]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/production/artisan queue:work redis --queue=default --sleep=3 --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/production/storage/logs/worker-default.log

[program:low-priority]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/production/artisan queue:work redis --queue=low,exports --sleep=3 --tries=2
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/production/storage/logs/worker-low.log
    </div>
</section>
</section>

<section id="cicd">
    <h2>7. Integração Contínua (CI/CD)</h2>
    <p>A integração contínua e entrega contínua (CI/CD) são práticas essenciais para a automação do ciclo de desenvolvimento e implantação.</p>

    <section id="cicd-pipeline">
        <h3>7.1. Pipeline CI/CD</h3>
        <p>Um pipeline CI/CD completo automatiza todo o processo de teste, build e deploy da aplicação:</p>

        <div class="example">
            <h4>Pipeline CI/CD com GitHub Actions</h4>
            <div class="code-block">
# .github/workflows/ci-cd.yml

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_DATABASE: app_test
          MYSQL_USER: user
          MYSQL_PASSWORD: secret
          MYSQL_ROOT_PASSWORD: secret
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:6.2
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, dom, fileinfo, mysql
          coverage: xdebug
      
      - name: Copy .env file
        run: cp .env.example .env.testing
      
      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist
      
      - name: Generate key
        run: php artisan key:generate --env=testing
      
      - name: Run migrations
        run: php artisan migrate --env=testing --force
      
      - name: Run tests
        run: php artisan test --coverage
      
      - name: Run static analysis
        run: ./vendor/bin/phpstan analyse

  build:
    name: Build
    needs: test
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install PHP dependencies
        run: composer install --no-interaction --prefer-dist --no-dev --optimize-autoloader
      
      - name: Install Node.js dependencies
        run: npm ci
      
      - name: Build assets
        run: npm run build
      
      - name: Create artifact
        run: |
          tar -czf app-${{ github.sha }}.tar.gz \
            app \
            bootstrap \
            config \
            database \
            lang \
            public \
            resources/lang \
            resources/views \
            routes \
            storage/app/public \
            storage/framework/cache/data \
            storage/framework/sessions \
            storage/framework/views \
            vendor \
            artisan \
            composer.json \
            composer.lock
      
      - name: Upload artifact
        uses: actions/upload-artifact@v3
        with:
          name: app-build
          path: app-${{ github.sha }}.tar.gz
          retention-days: 7

  deploy-staging:
    name: Deploy to Staging
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    environment:
      name: staging
      url: https://staging.example.com
    
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v3
        with:
          name: app-build
      
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      
      - name: Deploy to Staging
        run: |
          scp -o StrictHostKeyChecking=no app-${{ github.sha }}.tar.gz ${{ secrets.SSH_USER }}@${{ secrets.STAGING_HOST }}:/tmp/
          ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.STAGING_HOST }} << 'EOF'
            cd /var/www/staging
            tar -xzf /tmp/app-${{ github.sha }}.tar.gz -C .
            cp /var/www/env-files/.env.staging .env
            php artisan migrate --force
            php artisan config:cache
            php artisan route:cache
            php artisan view:cache
            php artisan storage:link
            sudo supervisorctl restart all
            sudo systemctl reload php8.2-fpm
            sudo systemctl reload nginx
            rm /tmp/app-${{ github.sha }}.tar.gz
          EOF

  deploy-production:
    name: Deploy to Production
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment:
      name: production
      url: https://example.com
    
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v3
        with:
          name: app-build
      
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      
      - name: Deploy to Production
        run: |
          scp -o StrictHostKeyChecking=no app-${{ github.sha }}.tar.gz ${{ secrets.SSH_USER }}@${{ secrets.PRODUCTION_HOST }}:/tmp/
          ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.PRODUCTION_HOST }} << 'EOF'
            cd /var/www/production
            tar -xzf /tmp/app-${{ github.sha }}.tar.gz -C .
            cp /var/www/env-files/.env.production .env
            php artisan migrate --force
            php artisan config:cache
            php artisan route:cache
            php artisan view:cache
            php artisan storage:link
            sudo supervisorctl restart all
            sudo systemctl reload php8.2-fpm
            sudo systemctl reload nginx
            rm /tmp/app-${{ github.sha }}.tar.gz
          EOF
            </div>
        </div>
    </section>

    <section id="cicd-testes">
        <h3>7.2. Testes Automatizados</h3>
        <p>Os testes automatizados são fundamentais para garantir a qualidade do código e prevenir regressões:</p>

        <div class="best-practice">
            <h4>Estratégia de Testes</h4>
            <ul>
                <li>Testes unitários para lógica de negócios e componentes isolados</li>
                <li>Testes de integração para verificar a interação entre componentes</li>
                <li>Testes de API para endpoints RESTful</li>
                <li>Testes de navegador para fluxos de usuário end-to-end</li>
                <li>Testes de performance para verificar tempos de resposta e throughput</li>
            </ul>
        </div>

        <h4>7.2.1. Configuração PHPUnit</h4>
        <div class="code-block">
# phpunit.xml
<?xml version="1.0" encoding="UTF-8"?>
                <phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd" bootstrap="vendor/autoload.php"
                    colors="true">
                    <testsuites>
                        <testsuite name="Unit">
                            <directory suffix="Test.php">./tests/Unit</directory>
                        </testsuite>
                        <testsuite name="Feature">
                            <directory suffix="Test.php">./tests/Feature</directory>
                        </testsuite>
                        <testsuite name="Integration">
                            <directory suffix="Test.php">./tests/Integration</directory>
                        </testsuite>
                    </testsuites>
                    <coverage processUncoveredFiles="true">
                        <include>
                            <directory suffix=".php">./app</directory>
                        </include>
                        <exclude>
                            <directory suffix=".php">./app/Providers</directory>
                            <file>./app/Http/Middleware/Authenticate.php</file>
                            <file>./app/Http/Middleware/RedirectIfAuthenticated.php</file>
                            <file>./app/Http/Middleware/TrustHosts.php</file>
                        </exclude>
                    </coverage>
                    <php>
                        <server name="APP_ENV" value="testing" />
                        <server name="BCRYPT_ROUNDS" value="4" />
                        <server name="CACHE_DRIVER" value="array" />
                        <server name="DB_CONNECTION" value="sqlite" />
                        <server name="DB_DATABASE" value=":memory:" />
                        <server name="MAIL_MAILER" value="array" />
                        <server name="QUEUE_CONNECTION" value="sync" />
                        <server name="SESSION_DRIVER" value="array" />
                        <server name="TELESCOPE_ENABLED" value="false" />
                    </php>
                </phpunit>
            </div>

            <h4>7.2.2. Exemplo de Teste de API</h4>
            <div class="code-block">
                namespace Tests\Feature\Api;

                use App\Models\User;
                use Illuminate\Foundation\Testing\RefreshDatabase;
                use Tests\TestCase;

                class AuthenticationTest extends TestCase
                {
                use RefreshDatabase;

                public function test_users_can_authenticate_using_api()
                {
                $user = User::factory()->create();

                $response = $this->post('/api/login', [
                'email' => $user->email,
                'password' => 'password',
                ]);

                $response->assertStatus(200);
                $response->assertJsonStructure([
                'data' => [
                'token',
                'user' => [
                'id',
                'name',
                'email',
                ]
                ]
                ]);

                $this->assertAuthenticated('api');
                }

                public function test_users_can_not_authenticate_with_invalid_password()
                {
                $user = User::factory()->create();

                $response = $this->post('/api/login', [
                'email' => $user->email,
                'password' => 'wrong-password',
                ]);

                $response->assertStatus(401);
                $response->assertJson([
                'message' => 'Unauthorized',
                ]);

                $this->assertGuest('api');
                }
                }
            </div>
        </section>

        <section id="cicd-qualidade">
            <h3>7.3. Análise de Qualidade de Código</h3>
            <p>A análise estática de código ajuda a manter alta qualidade e identificar problemas potenciais:</p>

            <h4>7.3.1. PHP CodeSniffer</h4>
            <div class="code-block">
                # .php_cs
                <?php

$finder = PhpCsFixer\Finder::create()
    ->in([
        __DIR__ . '/app',
        __DIR__ . '/config',
        __DIR__ . '/database',
        __DIR__ . '/routes',
        __DIR__ . '/tests',
    ])
    ->name('*.php')
    ->notName('*.blade.php')
    ->ignoreDotFiles(true)
    ->ignoreVCS(true);

return PhpCsFixer\Config::create()
    ->setRules([
        '@PSR2' => true,
        'array_syntax' => ['syntax' => 'short'],
        'ordered_imports' => ['sort_algorithm' => 'alpha'],
        'no_unused_imports' => true,
        'not_operator_with_successor_space' => true,
        'trailing_comma_in_multiline_array' => true,
        'phpdoc_scalar' => true,
        'unary_operator_spaces' => true,
        'binary_operator_spaces' => true,
        'blank_line_before_statement' => [
            'statements' => ['break', 'continue', 'declare', 'return', 'throw', 'try'],
        ],
        'phpdoc_single_line_var_spacing' => true,
        'phpdoc_var_without_name' => true,
        'class_attributes_separation' => [
            'elements' => [
                'method',
            ],
        ],
        'method_argument_space' => [
            'on_multiline' => 'ensure_fully_multiline',
            'keep_multiple_spaces_after_comma' => true,
        ],
        'single_trait_insert_per_statement' => true,
    ])
    ->setFinder($finder);
        </div>

        <h4>7.3.2. PHPStan</h4>
        <div class="code-block">
# phpstan.neon
parameters:
  level: 5
  paths:
    - app
    - tests
  excludePaths:
    - app/Console/Kernel.php
  checkMissingIterableValueType: false
  ignoreErrors:
    - '#Access to an undefined property#'
    - '#Call to an undefined method#'
        </div>

        <h4>7.3.3. SonarQube</h4>
        <div class="code-block">
# sonar-project.properties
sonar.projectKey=my-app
sonar.projectName=My Application
sonar.projectVersion=1.0.0
sonar.sources=app,resources,routes
sonar.tests=tests
sonar.php.coverage.reportPaths=coverage.xml
sonar.php.tests.reportPath=phpunit.report.xml
sonar.exclusions=vendor/**/*,node_modules/**/*,public/**/*
        </div>

        <div class="example">
            <h4>Integração com CI/CD</h4>
            <p>Adicione esse job ao pipeline GitHub Actions:</p>
            <div class="code-block">
quality:
  name: Code Quality
  needs: test
  runs-on: ubuntu-latest
  
  steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Importante para o SonarQube analisar histórico
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
    
    - name: Install dependencies
      run: composer install --no-interaction
    
    - name: Run PHP Code Sniffer
      run: ./vendor/bin/phpcs
    
    - name: Run PHPStan
      run: ./vendor/bin/phpstan analyse
    
    - name: Run SonarQube Scan
      uses: sonarsource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
            </div>
        </div>
    </section>
</section>

<section id="monitoramento">
    <h2>8. Monitoramento e Logs</h2>
    <p>O monitoramento eficaz é essencial para detectar e resolver problemas antes que afetem os usuários.</p>

    <section id="monitoramento-ferramentas">
        <h3>8.1. Ferramentas de Monitoramento</h3>
        <p>Recomendamos as seguintes ferramentas de monitoramento:</p>

        <table>
            <thead>
                <tr>
                    <th>Ferramenta</th>
                    <th>Propósito</th>
                    <th>Configuração</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Laravel Telescope</td>
                    <td>Debugging e monitoramento interno</td>
                    <td>Ativo apenas em ambientes de desenvolvimento e staging</td>
                </tr>
                <tr>
                    <td>New Relic</td>
                    <td>Monitoramento de performance da aplicação</td>
                    <td>Ativo em produção e staging</td>
                </tr>
                <tr>
                    <td>Prometheus + Grafana</td>
                    <td>Métricas de sistema e aplicação</td>
                    <td>Todos os ambientes</td>
                </tr>
                <tr>
                    <td>ELK Stack (Elasticsearch, Logstash, Kibana)</td>
                    <td>Agregação e análise de logs</td>
                    <td>Centralização de logs de todos os ambientes</td>
                </tr>
                <tr>
                    <td>Uptime Robot</td>
                    <td>Monitoramento de disponibilidade externa</td>
                    <td>Verificações a cada 5 minutos</td>
                </tr>
            </tbody>
        </table>

        <h4>8.1.1. Laravel Telescope</h4>
        <div class="code-block">
# Instalação
composer require laravel/telescope --dev

# Publicação e migração
php artisan telescope:install
php artisan migrate

# Configuração em app/Providers/TelescopeServiceProvider.php
public function register()
{
    $this->hideSensitiveRequestDetails();

    Telescope::filter(function (IncomingEntry $entry) {
        if ($this->app->environment('local')) {
            return true;
        }

        return $entry->isReportableException() ||
               $entry->isFailedRequest() ||
               $entry->isFailedJob() ||
               $entry->isScheduledTask() ||
               $entry->hasMonitoredTag();
    });
}

protected function gate()
{
    Gate::define('viewTelescope', function ($user) {
        return in_array($user->email, [
            '<EMAIL>',
            '<EMAIL>'
        ]);
    });
}
        </div>
    </section>

    <section id="monitoramento-alertas">
        <h3>8.2. Configuração de Alertas</h3>
        <p>Configure alertas para ser notificado proativamente sobre problemas:</p>

        <div class="best-practice">
            <h4>Estratégia de Alertas</h4>
            <ul>
                <li>Defina thresholds com base em dados históricos para evitar falsos positivos</li>
                <li>Estabeleça diferentes níveis de severidade (informacional, aviso, crítico)</li>
                <li>Configure canais de notificação apropriados para cada nível de severidade</li>
                <li>Implemente alertas para métricas chave de negócio, não apenas técnicas</li>
                <li>Documente procedimentos de resposta para cada tipo de alerta</li>
            </ul>
        </div>

        <h4>8.2.1. Alertas com Prometheus e Grafana</h4>
<p>Configuração de alertas utilizando Prometheus Alert Manager e Grafana:</p>

<div class="code-block">
# prometheus/alert.rules
groups:
- name: hosts
  rules:
  - alert: HighCPULoad
    expr: node_load1 > 1.5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Servidor com alta carga CPU"
      description: "{{ $labels.instance }} está com carga acima de 1.5 por 5 minutos"

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Servidor com uso de memória elevado"
      description: "{{ $labels.instance }} está usando mais de 85% de memória"
      
  - alert: LowDiskSpace
    expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} * 100 < 15
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Servidor com pouco espaço em disco"
      description: "{{ $labels.instance }} tem menos de 15% de espaço livre em disco"

- name: application
  rules:
  - alert: HighRequestLatency
    expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 0.5
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "Latência de resposta alta"
      description: "A aplicação está respondendo lentamente (>500ms)"

  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[1m]) / rate(http_requests_total[1m]) * 100 > 5
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Alta taxa de erros 5xx"
      description: "Mais de 5% das respostas são erros 5xx nos últimos minutos"

  - alert: DatabaseConnectionsHigh
    expr: mysql_global_status_threads_connected > 100
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "Muitas conexões ao banco de dados"
      description: "Mais de 100 conexões ativas ao MySQL"
</div>

<h4>8.2.2. Integrações com Canais de Notificação</h4>
<p>Configure diferentes canais de notificação para alertas:</p>

<div class="code-block">
# alertmanager/config.yml
global:
  resolve_timeout: 5m
  slack_api_url: 'https://hooks.slack.com/services/TXXXXXXX/BXXXXXXX/XXXXXXXXXX'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 3h
  receiver: 'team-emails'
  routes:
  - match:
      severity: critical
    receiver: 'team-pager'
    continue: true
  - match_re:
      service: ^(api|web|database)$
    receiver: 'dev-team-slack'

receivers:
- name: 'team-emails'
  email_configs:
  - to: '<EMAIL>'
    from: '<EMAIL>'
    smarthost: smtp.exemplo.com:587
    auth_username: 'alertmanager'
    auth_password: '{{ .GroupLabels.password }}'
    send_resolved: true

- name: 'team-pager'
  pagerduty_configs:
  - service_key: '<pagerduty-service-key>'
    send_resolved: true

- name: 'dev-team-slack'
  slack_configs:
  - channel: '#dev-alerts'
    send_resolved: true
    title: '[{{.Status | toUpper}}] {{ .GroupLabels.alertname }}'
    text: >-
      {{ range .Alerts }}
        *Alert:* {{ .Annotations.summary }}
        *Description:* {{ .Annotations.description }}
        *Details:*
        {{ range .Labels.SortedPairs }} • *{{ .Name }}:* `{{ .Value }}`
        {{ end }}
      {{ end }}
</div>
</section>

<section id="monitoramento-logs">
    <h3>8.3. Centralização de Logs</h3>
    <p>A centralização de logs facilita a identificação e resolução de problemas:</p>

    <h4>8.3.1. Configuração do ELK Stack</h4>
    <div class="code-block">
# Configuração do Filebeat para enviar logs ao Logstash
# /etc/filebeat/filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/www/production/storage/logs/*.log
  json.keys_under_root: true
  json.add_error_key: true
  json.message_key: message

output.logstash:
  hosts: ["logstash.internal:5044"]
    </div>

    <h4>8.3.2. Integração com Laravel</h4>
    <div class="code-block">
// Configuração do Laravel para formatar logs em JSON
// config/logging.php
'channels' => [
    'stack' => [
        'driver' => 'stack',
        'channels' => ['daily', 'slack'],
        'ignore_exceptions' => false,
    ],
    'daily' => [
        'driver' => 'daily',
        'path' => storage_path('logs/laravel.log'),
        'level' => env('LOG_LEVEL', 'debug'),
        'days' => 14,
        'tap' => [App\Logging\JsonFormatter::class],
    ],
    'slack' => [
        'driver' => 'slack',
        'url' => env('LOG_SLACK_WEBHOOK_URL'),
        'username' => env('APP_NAME', 'Laravel') . ' Log',
        'emoji' => ':boom:',
        'level' => 'error',
    ],
],

// App\Logging\JsonFormatter.php
namespace App\Logging;

use Monolog\Formatter\JsonFormatter as BaseJsonFormatter;

class JsonFormatter
{
    public function __invoke($logger)
    {
        foreach ($logger->getHandlers() as $handler) {
            $handler->setFormatter(new BaseJsonFormatter());
        }
    }
}
    </div>

    <div class="note">
        <p>Para uma documentação mais detalhada sobre as práticas de logging, consulte o <a href="ManualLogging.html" target="_blank">Manual de Logging</a> específico da aplicação.</p>
    </div>
</section>
</section>

<section id="seguranca">
    <h2>9. Segurança</h2>
    <p>Esta seção aborda configurações e práticas essenciais de segurança para o ambiente de produção.</p>

    <section id="seguranca-ssl">
        <h3>9.1. Configuração de SSL/TLS</h3>
        <p>Todas as comunicações devem ser protegidas com SSL/TLS:</p>

        <h4>9.1.1. Obtenção de Certificados com Let's Encrypt</h4>
        <div class="code-block">
# Instalação do Certbot
sudo apt update
sudo apt install -y certbot python3-certbot-nginx

# Obtenção do certificado
sudo certbot --nginx -d exemplo.com -d www.exemplo.com

# Renovação automática
sudo certbot renew --dry-run  # Testar a renovação
# Adicionar ao crontab
echo "0 3 * * * certbot renew --quiet" | sudo tee -a /etc/crontab
        </div>

        <h4>9.1.2. Configuração de SSL no Nginx</h4>
        <div class="code-block">
# Configurações de SSL recomendadas
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

# Otimizações SSL
ssl_session_timeout 1d;
ssl_session_cache shared:MozSSL:10m;  # cerca de 40000 sessões
ssl_session_tickets off;

# HSTS (descomente após testar)
# add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;
        </div>

        <div class="best-practice">
            <h4>Verificação da Configuração SSL</h4>
            <p>Após configurar SSL, verifique a qualidade da implementação com ferramentas como:</p>
            <ul>
                <li><a href="https://www.ssllabs.com/ssltest/" target="_blank">SSL Labs Server Test</a></li>
                <li><a href="https://securityheaders.com/" target="_blank">Security Headers</a></li>
                <li><a href="https://observatory.mozilla.org/" target="_blank">Mozilla Observatory</a></li>
            </ul>
            <p>O objetivo é obter uma classificação A ou A+ em todos os testes.</p>
        </div>
    </section>

    <section id="seguranca-firewall">
        <h3>9.2. Configuração de Firewall</h3>
        <p>O firewall é uma camada essencial de proteção para o servidor:</p>

        <h4>9.2.1. Configuração do UFW (Uncomplicated Firewall)</h4>
        <div class="code-block">
# Garantir que o UFW está habilitado
sudo ufw enable

# Política padrão: negar tudo
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Permitir SSH (altere a porta se você estiver usando uma porta não padrão)
sudo ufw allow 22/tcp

# Permitir HTTP e HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Se necessário, permitir acesso a outros serviços apenas de IPs específicos
# sudo ufw allow from ************* to any port 3306

# Verificar status
sudo ufw status verbose
        </div>

        <h4>9.2.2. Fail2ban para Proteção contra Força Bruta</h4>
        <div class="code-block">
# Instalação do Fail2ban
sudo apt install -y fail2ban

# Configuração básica - /etc/fail2ban/jail.local
[DEFAULT]
bantime = 1h
findtime = 10m
maxretry = 5

# Para SSH
[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

# Para proteção do site (tentativas de login)
[laravel-login]
enabled = true
port = http,https
filter = laravel-login
logpath = /var/www/production/storage/logs/laravel.log
maxretry = 5
        </div>

        <p>Crie um filtro personalizado para detectar tentativas de login malsucedidas:</p>

        <div class="code-block">
# /etc/fail2ban/filter.d/laravel-login.conf
[Definition]
failregex = ^.*Auth.WARNING: Login attempt failed for user .* from IP <HOST>.*$
ignoreregex =
        </div>
    </section>

    <section id="seguranca-secrets">
        <h3>9.3. Gerenciamento de Segredos</h3>
        <p>O gerenciamento seguro de segredos e credenciais é fundamental:</p>

        <div class="best-practice">
            <h4>Práticas de Gerenciamento de Segredos</h4>
            <ul>
                <li>Nunca armazene segredos diretamente no código-fonte ou repositório</li>
                <li>Utilize variáveis de ambiente para configuração de credenciais</li>
                <li>Considere usar um gerenciador de segredos como AWS Secrets Manager, HashiCorp Vault ou Laravel Vault</li>
                <li>Rotacione segredos e senhas regularmente</li>
                <li>Utilize permissões específicas para cada tipo de acesso</li>
                <li>Audite o acesso aos segredos</li>
            </ul>
        </div>

        <h4>9.3.1. Exemplo de Uso com Laravel Vault</h4>
        <div class="code-block">
# Instalação
composer require laravel/vault

# Publicação de configuração
php artisan vendor:publish --provider="Laravel\Vault\VaultServiceProvider"

# Configuração em config/vault.php
return [
    'default' => env('VAULT_DRIVER', 'file'),

    'drivers' => [
        'file' => [
            'driver' => 'file',
            'path' => env('VAULT_FILE_PATH', storage_path('vault')),
        ],
        'aws' => [
            'driver' => 'aws-secrets-manager',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
        ],
    ],
];

// Uso em código
use Laravel\Vault\Facades\Vault;

$apiKey = Vault::get('api-keys/payment-processor');
        </div>

        <div class="note">
            <p>Para obter mais informações sobre as práticas de segurança, consulte o <a href="ManualSeguranca.html" target="_blank">Manual de Segurança</a> da aplicação.</p>
        </div>
    </section>
</section>

<section id="troubleshooting">
    <h2>10. Troubleshooting</h2>
    <p>Esta seção fornece orientações para diagnosticar e resolver problemas comuns.</p>

    <section id="troubleshooting-comuns">
        <h3>10.1. Problemas Comuns e Soluções</h3>
        <table>
            <thead>
                <tr>
                    <th>Problema</th>
                    <th>Possíveis Causas</th>
                    <th>Soluções</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Erro 500 (Internal Server Error)</td>
                    <td>
                        <ul>
                            <li>Erro de configuração</li>
                            <li>Permissões de arquivo incorretas</li>
                            <li>Exceção não tratada</li>
                            <li>Problema no banco de dados</li>
                        </ul>
                    </td>
                    <td>
                        <ol>
                            <li>Verificar logs do Laravel em <code>storage/logs/laravel.log</code></li>
                            <li>Verificar logs do servidor web (<code>/var/log/nginx/error.log</code>)</li>
                            <li>Verificar permissões de arquivos e pastas</li>
                            <li>Verificar conexão com o banco de dados</li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td>Erro 503 (Service Unavailable)</td>
                    <td>
                        <ul>
                            <li>Aplicação em modo de manutenção</li>
                            <li>Servidor web não iniciado</li>
                            <li>Problema no PHP-FPM</li>
                        </ul>
                    </td>
                    <td>
                        <ol>
                            <li>Verificar se a aplicação está em modo de manutenção: <code>php artisan up</code></li>
                            <li>Verificar status do nginx/apache: <code>sudo systemctl status nginx</code></li>
                            <li>Verificar logs do PHP-FPM: <code>/var/log/php8.2-fpm.log</code></li>
                            <li>Reiniciar PHP-FPM: <code>sudo systemctl restart php8.2-fpm</code></li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td>Lentidão na aplicação</td>
                    <td>
                        <ul>
                            <li>Consultas SQL ineficientes</li>
                            <li>Cache não configurado</li>
                            <li>Alto uso de recursos do servidor</li>
                            <li>Problema com providers externos</li>
                        </ul>
                    </td>
                    <td>
                        <ol>
                            <li>Habilitar query logging: <code>DB::enableQueryLog()</code></li>
                            <li>Verificar uso de CPU e memória: <code>htop</code></li>
                            <li>Verificar configuração de cache</li>
                            <li>Usar New Relic ou Laravel Telescope para identificar gargalos</li>
                            <li>Verificar tempos de resposta de serviços externos</li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td>Falha em workers de fila</td>
                    <td>
                        <ul>
                            <li>Memória insuficiente</li>
                            <li>Timeout de processo</li>
                            <li>Erros não tratados</li>
                            <li>Problema com Redis/banco de dados</li>
                        </ul>
                    </td>
                    <td>
                        <ol>
                            <li>Verificar logs dos workers: <code>/var/www/production/storage/logs/worker.log</code></li>
                            <li>Verificar status do supervisor: <code>sudo supervisorctl status</code></li>
                            <li>Reiniciar workers: <code>php artisan queue:restart</code></li>
                            <li>Verificar conexão com Redis: <code>redis-cli ping</code></li>
                            <li>Considerar aumentar timeout ou memória alocada</li>
                        </ol>
                    </td>
                </tr>
            </tbody>
        </table>
    </section>

    <section id="troubleshooting-diagnostico">
        <h3>10.2. Ferramentas de Diagnóstico</h3>
        <p>Utilize estas ferramentas para diagnosticar problemas:</p>
        
        <ul>
            <li><strong>Artisan Tinker</strong>: Para interagir com a aplicação via console</li>
            <li><strong>Laravel Telescope</strong>: Para inspecionar requisições, queries, jobs e mais</li>
            <li><strong>Laravel Debugbar</strong>: Para debugging em desenvolvimento</li>
            <li><strong>New Relic APM</strong>: Para monitoramento de performance em produção</li>
            <li><strong>htop</strong>: Para monitorar uso de CPU e memória</li>
            <li><strong>mysqltuner</strong>: Para análise da configuração do MySQL</li>
            <li><strong>ab (Apache Benchmark)</strong>: Para testes de carga simples</li>
            <li><strong>tcpdump</strong>: Para análise de tráfego de rede</li>
        </ul>

        <div class="code-block">
# Exemplo de uso do Artisan Tinker
php artisan tinker
>>> App\Models\User::count()  # Verificar número de usuários
>>> DB::connection()->getPdo()  # Testar conexão com banco de dados
>>> Cache::get('key')  # Verificar valor em cache
>>> Log::info('Teste de log')  # Testar logging

# Exemplo de teste de carga com ab
ab -n 1000 -c 50 https://exemplo.com/api/endpoint
        </div>

        <h4>10.2.1. Debug com Headers de Resposta</h4>
        <p>Adicione headers de resposta para facilitar o diagnóstico em ambientes de homologação:</p>

        <div class="code-block">
// Em um middleware (apenas para homologação)
if (app()->environment('staging')) {
    $startTime = microtime(true);
    
    $response = $next($request);
    
    $response->headers->set('X-Response-Time', round((microtime(true) - $startTime) * 1000) . 'ms');
    $response->headers->set('X-Memory-Usage', round(memory_get_peak_usage() / 1048576, 2) . 'MB');
    $response->headers->set('X-DB-Queries', count(\DB::getQueryLog()));
    $response->headers->set('X-App-Version', config('app.version'));
    
    return $response;
}
        </div>
    </section>

    <section id="troubleshooting-performance">
        <h3>10.3. Diagnóstico de Performance</h3>
        <p>Passos para diagnosticar problemas de performance:</p>

        <div class="best-practice">
            <h4>Processo de Diagnóstico de Performance</h4>
            <ol>
                <li>Identifique se o problema é consistente ou intermitente</li>
                <li>Verifique se o problema é geral ou específico de algumas páginas/endpoints</li>
                <li>Analise a utilização de recursos do servidor durante os períodos de lentidão</li>
                <li>Examine os logs da aplicação e do servidor web</li>
                <li>Utilize ferramentas de profiling para identificar os gargalos exatos</li>
                <li>Verifique o tempo de resposta de serviços externos</li>
                <li>Analise o plano de execução de queries SQL lentas</li>
                <li>Teste a performance de configurações de cache</li>
            </ol>
        </div>

        <h4>10.3.1. Análise de Queries Lentas</h4>
        <div class="code-block">
# Habilitar log de queries lentas no MySQL
# Edite o arquivo /etc/mysql/my.cnf

[mysqld]
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 1  # Logar queries que levam mais de 1 segundo

# Analise o arquivo de log
sudo tail -f /var/log/mysql/mysql-slow.log

# Ou use o mysqldumpslow para analisar
mysqldumpslow -t 10 /var/log/mysql/mysql-slow.log
        </div>

        <h4>10.3.2. Verificação de Gargalos no Servidor</h4>
        <div class="code-block">
# Verificar uso de CPU e memória
htop

# Verificar I/O de disco
iostat -x 1

# Verificar conexões de rede
netstat -tunapl

# Verificar processos PHP consumindo mais recursos
ps aux --sort=-%cpu | grep php

# Verificar carga do servidor nos últimos 15 minutos
uptime
        </div>

        <div class="note">
            <p>Para informações mais detalhadas sobre a arquitetura da aplicação e padrões de implementação, consulte o <a href="ManualArquitetura.html" target="_blank">Manual de Arquitetura</a> e o <a href="ManualImplementação.html" target="_blank">Manual de Implementação</a>.</p>
        </div>
    </section>
</section>

<footer>
    <p>Manual de Deployment - Versão 1.0</p>
    <p>Última atualização: <span id="current-date"></span></p>
    <p>Este manual deve ser consultado em conjunto com os manuais de <a href="ManualArquitetura.html">Arquitetura</a>, <a href="ManualImplementação.html">Implementação</a>, <a href="ManualLogging.html">Logging</a> e <a href="ManualRespostasPadronizadas.html">Respostas Padronizadas</a>.</p>
    <script>
        document.getElementById('current-date').textContent = new Date().toLocaleDateString();
    </script>
</footer>
</body>
</html>