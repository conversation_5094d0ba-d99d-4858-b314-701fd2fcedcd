<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Arquitetura</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #2980b9;
            margin-top: 40px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        h3 {
            color: #3498db;
            margin-top: 25px;
        }

        .code-section {
            margin-bottom: 50px;
        }

        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', Courier, monospace;
            overflow-x: auto;
        }

        .example {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .example h4 {
            margin-top: 0;
            color: #2980b9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        table,
        th,
        td {
            border: 1px solid #ddd;
        }

        th {
            background-color: #f2f2f2;
            padding: 12px;
            text-align: left;
        }

        td {
            padding: 10px;
        }

        .diagram {
            background-color: #f8fafc;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }

        .note {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
            padding: 10px;
        }

        footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }

        #sumario {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }

        #sumario ul {
            list-style-type: none;
            padding-left: 20px;
        }

        #sumario ul li {
            margin-bottom: 8px;
        }

        #sumario a {
            color: #2980b9;
            text-decoration: none;
        }

        #sumario a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <header>
        <h1 style="color: white; border-bottom: none;">Manual de Arquitetura</h1>
        <p>Descrição da arquitetura do sistema e seus componentes</p>
    </header>

    <section id="sumario">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#visao-geral">2. Visão Geral da Arquitetura</a></li>
            <li><a href="#camadas">3. Arquitetura em Camadas</a></li>
            <li><a href="#componentes">4. Principais Componentes</a></li>
            <li><a href="#fluxo-dados">5. Fluxo de Dados</a></li>
            <li><a href="#seguranca">6. Segurança na Arquitetura</a></li>
            <li><a href="#integracoes">7. Integrações Externas</a></li>
            <li><a href="#escalabilidade">8. Escalabilidade e Performance</a></li>
            <li><a href="#decisoes">9. Decisões Arquiteturais</a></li>
        </ul>
    </section>

    <section id="introducao">
        <h2>1. Introdução</h2>
        <p>Este documento descreve a arquitetura do sistema, baseada no framework Laravel 12, detalhando os componentes,
            camadas, fluxos de dados e princípios arquiteturais adotados.</p>

        <p>A arquitetura foi projetada seguindo os princípios SOLID e padrões de design que promovem:</p>
        <ul>
            <li>Baixo acoplamento entre componentes</li>
            <li>Alta coesão dentro dos módulos</li>
            <li>Testabilidade e manutenibilidade</li>
            <li>Escalabilidade horizontal e vertical</li>
            <li>Segurança em múltiplas camadas</li>
        </ul>
    </section>

    <section id="visao-geral">
        <h2>2. Visão Geral da Arquitetura</h2>
        <p>O sistema segue uma arquitetura em camadas que separa claramente as responsabilidades entre diferentes
            componentes do software:</p>

        <div class="diagram">
            <img src="data:image/png;base64,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"
                alt="Arquitetura em Camadas" style="max-width:100%;">
        </div>

        <p>A arquitetura do sistema está estruturada nas seguintes camadas principais:</p>
        <ul>
            <li><strong>Camada de Apresentação</strong>: Responsável pela interação com o usuário e formatação das
                respostas</li>
            <li><strong>Camada de Aplicação</strong>: Orquestra as operações do sistema e implementa a lógica de negócio
            </li>
            <li><strong>Camada de Domínio</strong>: Contém as regras de negócio e entidades do domínio</li>
            <li><strong>Camada de Infraestrutura</strong>: Lida com interações com sistemas externos, persistência e
                operações técnicas</li>
        </ul>
    </section>

    <section id="camadas">
        <h2>3. Arquitetura em Camadas</h2>
        <p>Detalhamento de cada camada da arquitetura:</p>

        <h3>3.1. Camada de Apresentação</h3>
        <p>Esta camada é responsável pela interface com o usuário, seja através de endpoints API ou interfaces web.</p>
        <ul>
            <li><strong>Controllers</strong>: Recebem as requisições HTTP, delegam o processamento para a camada de
                aplicação e retornam respostas apropriadas.</li>
            <li><strong>Requests</strong>: Validam e normalizam os dados de entrada.</li>
            <li><strong>Responses</strong>: Formatam os dados de saída em formato padronizado JSON.</li>
            <li><strong>Middlewares</strong>: Processam requisições antes que cheguem aos controllers (autenticação,
                logs, etc).</li>
        </ul>

        <div class="code-block">
            namespace App\Http\Controllers;

            use App\Http\Requests\UserRequest;
            use App\Http\Responses\ApiResponse;
            use App\Services\UserService;

            class UserController extends Controller
            {
            protected $service;

            public function __construct(UserService $service)
            {
            $this->service = $service;
            }

            public function store(UserRequest $request, ApiResponse $response)
            {
            $user = $this->service->create($request->validated());
            return $response->created($user, 'Usuário criado com sucesso');
            }
            }
        </div>

        <h3>3.2. Camada de Aplicação (Services)</h3>
        <p>Esta camada contém a lógica de aplicação e orquestra as operações entre as diferentes partes do sistema.</p>
        <ul>
            <li><strong>Services</strong>: Implementam a lógica de negócio, orquestrando operações entre diferentes
                repositórios.</li>
            <li><strong>DTOs</strong> (Data Transfer Objects): Transportam dados entre as camadas do sistema.</li>
            <li><strong>Validação</strong>: Implementam regras de validação complexas a nível de negócio.</li>
            <li><strong>Transações</strong>: Gerenciam transações que envolvem múltiplas operações.</li>
        </ul>

        <div class="code-block">
            namespace App\Services;

            use App\Repositories\UserRepository;
            use App\Exceptions\UserException;
            use Illuminate\Support\Facades\DB;

            class UserService
            {
            protected $repository;

            public function __construct(UserRepository $repository)
            {
            $this->repository = $repository;
            }

            public function create(array $data)
            {
            DB::beginTransaction();
            try {
            $user = $this->repository->create($data);
            // Lógica adicional, como enviar email, atribuir perfil, etc.
            DB::commit();
            return $user;
            } catch (\Exception $e) {
            DB::rollBack();
            throw new UserException('Erro ao criar usuário: ' . $e->getMessage());
            }
            }
            }
        </div>

        <h3>3.3. Camada de Domínio</h3>
        <p>Esta camada contém as entidades de negócio e as regras que governam o domínio da aplicação.</p>
        <ul>
            <li><strong>Models</strong>: Representam as entidades de negócio no sistema.</li>
            <li><strong>Value Objects</strong>: Encapsulam valores com significado no domínio.</li>
            <li><strong>Regras de Negócio</strong>: Implementam as regras que são intrínsecas ao domínio.</li>
            <li><strong>Contratos (Interfaces)</strong>: Definem contratos para serviços e repositórios.</li>
        </ul>

        <div class="code-block">
            namespace App\Models;

            use Illuminate\Database\Eloquent\Model;
            use Illuminate\Database\Eloquent\SoftDeletes;

            class Order extends Model
            {
            use SoftDeletes;

            protected $fillable = ['customer_id', 'total', 'status'];

            protected $casts = [
            'total' => 'decimal:2',
            'created_at' => 'datetime',
            ];

            // Relacionamentos
            public function customer()
            {
            return $this->belongsTo(Customer::class);
            }

            public function items()
            {
            return $this->hasMany(OrderItem::class);
            }

            // Métodos de negócio
            public function canBeCancelled()
            {
            return in_array($this->status, ['pending', 'processing']);
            }

            public function cancel()
            {
            if (!$this->canBeCancelled()) {
            throw new \Exception('Pedido não pode ser cancelado');
            }

            $this->status = 'cancelled';
            $this->save();
            }
            }
        </div>

        <h3>3.4. Camada de Infraestrutura</h3>
        <p>Esta camada fornece suporte técnico para as outras camadas, lidando com aspectos como persistência,
            integração com sistemas externos, etc.</p>
        <ul>
            <li><strong>Repositories</strong>: Implementam o acesso aos dados persistentes.</li>
            <li><strong>External Services</strong>: Integram com APIs e serviços externos.</li>
            <li><strong>Logging</strong>: Implementam mecanismos de registro de logs.</li>
            <li><strong>Cache</strong>: Gerenciam o cache do sistema.</li>
            <li><strong>Queue</strong>: Gerenciam filas de processamento assíncrono.</li>
            <li><strong>File Storage</strong>: Gerenciam operações de armazenamento de arquivos.</li>
            <li><strong>Jobs</strong>: Implementam tarefas agendadas e processamento em background.</li>
        </ul>

        <div class="code-block">
            namespace App\Repositories;

            use App\Models\User;

            class UserRepository
            {
            protected $model;

            public function __construct(User $model)
            {
            $this->model = $model;
            }

            public function findById($id)
            {
            return $this->model->find($id);
            }

            public function create(array $data)
            {
            return $this->model->create($data);
            }

            public function update($id, array $data)
            {
            $entity = $this->findById($id);
            $entity->update($data);
            return $entity;
            }

            public function delete($id)
            {
            return $this->model->destroy($id);
            }

            public function findByEmail($email)
            {
            return $this->model->where('email', $email)->first();
            }
            }
        </div>

        <div class="example">
            <h4>Exemplo de External Service:</h4>
            <div class="code-block">
                namespace App\Services\External;

                use GuzzleHttp\Client;
                use App\Exceptions\PaymentException;

                class PaymentGatewayService
                {
                protected $client;
                protected $apiKey;

                public function __construct(Client $client)
                {
                $this->client = $client;
                $this->apiKey = config('services.payment.key');
                }

                public function processPayment($orderId, $amount, $cardToken)
                {
                try {
                $response = $this->client->post('https://api.payment.com/v1/charges', [
                'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json'
                ],
                'json' => [
                'amount' => $amount,
                'currency' => 'BRL',
                'source' => $cardToken,
                'description' => 'Pedido #' . $orderId
                ]
                ]);

                return json_decode($response->getBody()->getContents(), true);
                } catch (\Exception $e) {
                throw new PaymentException('Erro ao processar pagamento: ' . $e->getMessage());
                }
                }
                }
            </div>
        </div>
    </section>

    <section id="componentes">
        <h2>4. Principais Componentes</h2>

        <h3>4.1. Controllers</h3>
        <p>Os Controllers são responsáveis por receber as requisições HTTP, delegá-las para a camada de serviço
            apropriada e retornar as respostas. Seguem o princípio de manter controllers "magros", com mínima lógica.
        </p>

        <h3>4.2. Services</h3>
        <p>Os Services encapsulam a lógica de negócio da aplicação. Eles são responsáveis por orquestrar as operações
            entre diferentes repositórios e componentes, garantindo a consistência dos dados e implementando regras de
            negócio.</p>

        <h3>4.3. Repositories</h3>
        <p>Os Repositories abstraem a camada de persistência, fornecendo métodos para acesso e manipulação de dados.
            Isso desacopla a lógica de negócio do acesso a dados, facilitando testes e manutenção.</p>

        <h3>4.4. Models</h3>
        <p>Os Models representam as entidades do domínio e seus relacionamentos. No contexto do Laravel, eles também
            incluem métodos de consulta e definições de relacionamentos.</p>

        <h3>4.5. DTOs (Data Transfer Objects)</h3>
        <p>Os DTOs são objetos simples usados para transferir dados entre subsistemas da aplicação. Eles não contêm
            lógica de negócio, apenas dados e seus acessores.</p>

        <div class="code-block">
            namespace App\DTOs;

            class UserDTO
            {
            public $id;
            public $name;
            public $email;
            public $roles = [];

            public function __construct($id, $name, $email, array $roles = [])
            {
            $this->id = $id;
            $this->name = $name;
            $this->email = $email;
            $this->roles = $roles;
            }

            public static function fromModel($user)
            {
            return new self(
            $user->id,
            $user->name,
            $user->email,
            $user->roles->pluck('name')->toArray()
            );
            }
            }
        </div>

        <h3>4.6. Responses</h3>
        <p>As classes de Response padronizam o formato de respostas da API, garantindo consistência em toda a aplicação.
        </p>

        <div class="code-block">
            namespace App\Http\Responses;

            use Illuminate\Http\JsonResponse;

            class ApiResponse
            {
            public function success($data = null, $message = 'Operação realizada com sucesso', $statusCode = 200)
            {
            return new JsonResponse([
            'status' => 'success',
            'message' => $message,
            'data' => $data
            ], $statusCode);
            }

            public function error($message = 'Ocorreu um erro', $errors = null, $statusCode = 400)
            {
            return new JsonResponse([
            'status' => 'error',
            'message' => $message,
            'errors' => $errors
            ], $statusCode);
            }
            }
        </div>

        <h3>4.7. Exceptions</h3>
        <p>As Exceptions personalizadas ajudam a identificar e tratar erros de forma específica para cada contexto da
            aplicação.</p>
    </section>

    <section id="fluxo-dados">
        <h2>5. Fluxo de Dados</h2>

        <p>O fluxo de dados na arquitetura segue um padrão consistente para todas as operações:</p>

        <div class="diagram">
            <img src="diagrama-fluxo-dados.png" alt="Diagrama de Fluxo de Dados" style="max-width:100%;">
        </div>

        <ol>
            <li><strong>Request HTTP</strong> - O cliente (aplicação frontend, aplicativo móvel, etc.) envia uma
                requisição HTTP para um endpoint da API.</li>
            <li><strong>Middleware</strong> - A requisição passa por uma série de middlewares que podem realizar
                autenticação, logging, validação de token, etc.</li>
            <li><strong>Controller</strong> - O controller recebe a requisição, extrai os parâmetros necessários e
                delega o processamento para o service apropriado.</li>
            <li><strong>Validation</strong> - Os dados de entrada são validados através de form requests ou validadores.
            </li>
            <li><strong>Service</strong> - O service implementa a lógica de negócio, coordenando as operações entre
                diferentes repositories.</li>
            <li><strong>Repository</strong> - O repository executa as operações de persistência de dados, isolando a
                lógica de negócio do acesso a dados.</li>
            <li><strong>Model</strong> - Os models representam as entidades do domínio e suas regras de negócio
                inerentes.</li>
            <li><strong>Response</strong> - Os dados processados retornam pelo mesmo caminho, sendo formatados em um
                formato de resposta padronizado.</li>
        </ol>

        <h3>5.1. Exemplo de Fluxo Completo</h3>

        <div class="example">
            <h4>Fluxo de criação de um usuário:</h4>
            <ol>
                <li>O cliente envia uma requisição POST para <code>/api/users</code> com dados do usuário.</li>
                <li>Os middlewares de autenticação e autorização verificam se o requisitante tem permissão.</li>
                <li>O <code>UserController@store</code> recebe a requisição e a encaminha para validação.</li>
                <li>O <code>UserRequest</code> valida os dados de entrada conforme as regras definidas.</li>
                <li>O controller chama o método <code>create</code> do <code>UserService</code> com os dados validados.
                </li>
                <li>O service verifica regras de negócio adicionais (ex: verificar se o email já está em uso).</li>
                <li>O service chama o método <code>create</code> do <code>UserRepository</code>.</li>
                <li>O repository cria um novo registro no banco de dados.</li>
                <li>O service pode executar ações adicionais (enviar email de boas-vindas, atribuir perfil padrão).</li>
                <li>O controller recebe o resultado e o formata usando a classe de <code>ApiResponse</code>.</li>
                <li>A resposta HTTP é enviada ao cliente com o código 201 (Created) e os dados do usuário criado.</li>
            </ol>
        </div>

        <h3>5.2. Fluxos Assíncronos</h3>

        <p>Para operações que podem levar tempo ou que não precisam de retorno imediato, utilizamos processamento
            assíncrono:</p>

        <ol>
            <li>O controller recebe a requisição e a valida.</li>
            <li>O service dispara um job para execução em background.</li>
            <li>O job é colocado em uma fila para processamento posterior.</li>
            <li>O controller retorna imediatamente uma resposta de sucesso.</li>
            <li>O job é processado em background pelo worker de filas.</li>
            <li>O resultado do processamento pode ser armazenado ou notificado ao usuário posteriormente.</li>
        </ol>

        <div class="code-block">
            // Controller
            public function sendBulkEmails(BulkEmailRequest $request, ApiResponse $response)
            {
            $this->emailService->dispatchBulkEmailJob($request->validated());

            return $response->accepted(null, 'Os emails foram enfileirados para envio');
            }

            // Service
            public function dispatchBulkEmailJob(array $data)
            {
            ProcessBulkEmailJob::dispatch($data)
            ->onQueue('emails')
            ->delay(now()->addSeconds(10));
            }
        </div>
    </section>

    <section id="seguranca">
        <h2>6. Segurança na Arquitetura</h2>

        <p>A segurança é implementada em múltiplas camadas da arquitetura:</p>

        <h3>6.1. Autenticação e Autorização</h3>
        <ul>
            <li>Autenticação via JWT (JSON Web Tokens) para APIs</li>
            <li>Controle de acesso baseado em função (RBAC) através de políticas e gates</li>
            <li>Middleware de autenticação que verifica tokens e sessões</li>
            <li>Proteção CSRF para formulários web</li>
        </ul>

        <h3>6.2. Proteção de Dados</h3>
        <ul>
            <li>Criptografia de dados sensíveis em trânsito (HTTPS)</li>
            <li>Criptografia de dados sensíveis em repouso</li>
            <li>Hash seguro para senhas usando bcrypt</li>
            <li>Rate limiting para prevenir ataques de força bruta</li>
        </ul>

        <div class="code-block">
            // Middleware de taxa de requisições
            Route::middleware('throttle:60,1')->group(function () {
            Route::post('/login', 'AuthController@login');
            });

            // Criptografia de atributo sensível
            public function setCpfAttribute($value)
            {
            $this->attributes['cpf'] = encrypt($value);
            }

            public function getCpfAttribute($value)
            {
            return decrypt($value);
            }
        </div>

        <h3>6.3. Validação e Sanitização</h3>
        <ul>
            <li>Validação rigorosa de todos os dados de entrada</li>
            <li>Sanitização de dados para prevenir XSS e injeção SQL</li>
            <li>Validação em múltiplas camadas (frontend, controllers, services)</li>
        </ul>

        <h3>6.4. Logging e Auditoria</h3>
        <ul>
            <li>Logging de todas as ações sensíveis</li>
            <li>Trilha de auditoria para operações críticas</li>
            <li>Alertas para atividades suspeitas</li>
        </ul>
    </section>

    <section id="integracoes">
        <h2>7. Integrações Externas</h2>

        <p>A arquitetura permite a integração com sistemas externos através de interfaces bem definidas:</p>

        <h3>7.1. APIs de Terceiros</h3>
        <p>Para integrar com APIs de terceiros, utilizamos services específicos na camada de infraestrutura:</p>

        <div class="code-block">
            namespace App\Services\External;

            class PaymentGatewayService
            {
            protected $httpClient;
            protected $config;

            public function __construct(HttpClientInterface $httpClient, ConfigInterface $config)
            {
            $this->httpClient = $httpClient;
            $this->config = $config;
            }

            public function processPayment(PaymentDTO $payment)
            {
            try {
            $response = $this->httpClient->post(
            $this->config->get('payment_gateway.url'),
            [
            'headers' => $this->getAuthHeaders(),
            'json' => $payment->toArray()
            ]
            );

            return $this->parseResponse($response);
            } catch (Exception $e) {
            $this->logError($e);
            throw new PaymentProcessingException($e->getMessage());
            }
            }

            // Outros métodos...
            }
        </div>

        <h3>7.2. Webhooks</h3>
        <p>Para receber notificações de sistemas externos, implementamos endpoints de webhook:</p>

        <div class="code-block">
            class WebhookController extends Controller
            {
            protected $webhookService;

            public function __construct(WebhookService $webhookService)
            {
            $this->webhookService = $webhookService;
            }

            public function handlePaymentNotification(Request $request, ApiResponse $response)
            {
            $payload = $request->all();

            if (!$this->webhookService->validateSignature($payload, $request->header('X-Signature'))) {
            return $response->unauthorized('Assinatura inválida');
            }

            $this->webhookService->processPaymentNotification($payload);

            return $response->success(null, 'Notificação recebida com sucesso');
            }
            }
        </div>

        <h3>7.3. Filas e Mensageria</h3>
        <p>Para comunicação assíncrona com sistemas externos, utilizamos filas e sistemas de mensageria:</p>

        <ul>
            <li>RabbitMQ ou Apache Kafka para mensageria</li>
            <li>Redis ou Amazon SQS para filas</li>
            <li>Implementação de padrões como Circuit Breaker para lidar com falhas de sistemas externos</li>
        </ul>
    </section>

    <section id="escalabilidade">
        <h2>8. Escalabilidade e Performance</h2>

        <h3>8.1. Estratégias de Escalabilidade</h3>
        <ul>
            <li><strong>Escalabilidade Horizontal</strong>: Adição de mais instâncias da aplicação para distribuir carga
            </li>
            <li><strong>Escalabilidade Vertical</strong>: Aumento de recursos de cada instância</li>
            <li><strong>Microsserviços</strong>: Decomposição em serviços menores e especializados quando necessário
            </li>
            <li><strong>Distribuição Geográfica</strong>: CDN e replicação geográfica para melhorar latência</li>
        </ul>

        <h3>8.2. Cache</h3>
        <p>Implementamos múltiplas estratégias de cache para melhorar a performance:</p>

        <ul>
            <li>Cache de dados frequentemente acessados em Redis</li>
            <li>Cache de consultas complexas no nível do repositório</li>
            <li>Cache HTTP para respostas de API</li>
            <li>Cache de configuração e rotas em nível de aplicação</li>
        </ul>

        <div class="code-block">
            public function getUserProfile($userId)
            {
            $cacheKey = "user_profile_{$userId}";

            return $this->cache->remember($cacheKey, 3600, function () use ($userId) {
            return $this->userRepository->findWithRelations($userId);
            });
            }
        </div>

        <h3>8.3. Otimização de Banco de Dados</h3>
        <ul>
            <li>Índices apropriados para consultas frequentes</li>
            <li>Consultas eficientes com eager loading para evitar o problema N+1</li>
            <li>Paginação para conjuntos grandes de dados</li>
            <li>Replicação de leitura/escrita para distribuir carga</li>
        </ul>

        <h3>8.4. Processamento em Background</h3>
        <p>Operações pesadas são movidas para processamento em segundo plano:</p>

        <ul>
            <li>Processamento de relatórios</li>
            <li>Envio de emails em massa</li>
            <li>Sincronização com sistemas externos</li>
            <li>Processamento de uploads de arquivos grandes</li>
        </ul>
    </section>

    <section id="decisoes">
        <h2>9. Decisões Arquiteturais</h2>

        <p>As principais decisões arquiteturais tomadas e suas justificativas:</p>

        <h3>9.1. Por que Laravel?</h3>
        <p>O Laravel foi escolhido como framework base porque:</p>
        <ul>
            <li>Oferece um ecossistema completo de ferramentas para desenvolvimento web</li>
            <li>Alta produtividade com convenções claras e recursos prontos para uso</li>
            <li>Suporte robusto para APIs com recursos como API resources e throttling</li>
            <li>Facilidade de teste com ferramentas integradas</li>
            <li>Comunidade ativa e documentação excelente</li>
        </ul>

        <h3>9.2. Por que Arquitetura em Camadas?</h3>
        <p>A arquitetura em camadas foi adotada para:</p>
        <ul>
            <li>Separação clara de responsabilidades</li>
            <li>Facilidade de teste unitário e de integração</li>
            <li>Manutenibilidade a longo prazo</li>
            <li>Possibilidade de evolução independente de cada camada</li>
            <li>Reutilização de componentes</li>
        </ul>

        <h3>9.3. Por que Repositories e Services?</h3>
        <p>O padrão Repository + Service foi escolhido para:</p>
        <ul>
            <li>Abstrair o acesso a dados da lógica de negócio</li>
            <li>Facilitar a substituição da fonte de dados (ex: mudar de MySQL para MongoDB)</li>
            <li>Centralizar lógica de negócio em services para evitar duplicação</li>
            <li>Melhorar testabilidade com possibilidade de mock de repositories</li>
        </ul>

        <div class="note">
            <p><strong>Nota importante:</strong> Esta arquitetura é projetada para ser flexível. Nem todos os
                componentes precisam ser implementados para todas as funcionalidades. Para casos simples, pode-se omitir
                camadas como DTOs ou services específicos, dependendo da complexidade da operação.</p>
        </div>
    </section>

    <footer>
        <p>Manual de Arquitetura - Versão 1.0</p>
        <p>Última atualização: <span id="current-date"></span></p>
        <script>
            document.getElementById('current-date').textContent = new Date().toLocaleDateString();
        </script>
    </footer>
</body>

</html>