<?php

namespace App\Swagger;

use OpenApi\Analysis;
use Psr\Log\LoggerInterface;

/**
 * Processador personalizado para ignorar classes desconhecidas
 */
class SkipUnknownClassProcessor
{
    protected $logger;

    public function __construct(LoggerInterface $logger = null)
    {
        $this->logger = $logger;
    }

    /**
     * Método invocável que será chamado pelo OpenAPI durante o processamento
     */
    public function __invoke(Analysis $analysis)
    {
        // Filtra anotações com classes desconhecidas
        $toRemove = [];

        foreach ($analysis->annotations as $annotation) {
            if (property_exists($annotation, 'class') && !empty($annotation->class)) {
                if (!class_exists($annotation->class)) {
                    // Em vez de gerar erro, apenas registra e continua
                    if ($this->logger) {
                        $this->logger->debug('Ignorando classe desconhecida: ' . $annotation->class);
                    }

                    // Adiciona à lista de remoção
                    $toRemove[] = $annotation;
                }
            }
        }

        // Remove as anotações da análise
        foreach ($toRemove as $annotation) {
            $analysis->annotations->detach($annotation);
        }
    }
}
