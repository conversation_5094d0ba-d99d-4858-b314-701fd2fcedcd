<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Deployment - Laravel 12</title>
    <link rel="stylesheet" href="css/manual.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Deployment</h1>
        <p>Guia para implantação, configuração e gerenciamento de ambientes da aplicação Laravel 12</p>
        <div class="version">Versão 2.0 - Laravel 12.x</div>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#requisitos">Requisitos</a></li>
            <li><a href="#ambientes">Ambientes</a></li>
            <li><a href="#build">Build</a></li>
            <li><a href="#deployment">Deployment</a></li>
            <li><a href="#infra">Infraestrutura</a></li>
            <li><a href="#cicd">CI/CD</a></li>
            <li><a href="#monitoramento">Monitoramento</a></li>
            <li><a href="#seguranca">Segurança</a></li>
            <li><a href="#troubleshooting">Troubleshooting</a></li>
        </ul>
    </nav>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <p class="intro-text">Este manual tem como objetivo fornecer diretrizes completas para o processo de implantação
            da aplicação Laravel 12, desde o ambiente de desenvolvimento até a produção. O documento aborda todos os
            aspectos necessários para garantir uma implantação bem-sucedida, incluindo requisitos de sistema,
            configuração de ambiente, automação de processos, monitoramento e solução de problemas.</p>

        <div class="note">
            <p><strong>Nota:</strong> Este documento é complementar aos outros manuais da aplicação, como o Manual de
                Arquitetura, Manual de Implementação, Manual de Segurança e Manual de Logging. Recomenda-se a leitura
                desses documentos para uma compreensão mais completa do sistema.</p>
        </div>

        <h3>1.1. Público-alvo</h3>
        <p>Este manual destina-se a:</p>
        <ul>
            <li>DevOps Engineers</li>
            <li>SREs (Site Reliability Engineers)</li>
            <li>Administradores de sistemas</li>
            <li>Desenvolvedores envolvidos no processo de implantação</li>
            <li>Equipe de QA</li>
        </ul>

        <h3>1.2. Princípios de Deployment</h3>
        <p>Nossa estratégia de deployment é guiada pelos seguintes princípios:</p>
        <ul>
            <li><strong>Automação:</strong> Minimizar intervenção manual para reduzir erros humanos</li>
            <li><strong>Consistência:</strong> Garantir que todos os ambientes sejam o mais similares possíveis</li>
            <li><strong>Rastreabilidade:</strong> Manter histórico de todas as alterações e deployments</li>
            <li><strong>Reversibilidade:</strong> Permitir retorno rápido a versões anteriores em caso de problemas</li>
            <li><strong>Zero downtime:</strong> Realizar implantações sem interrupção de serviço</li>
            <li><strong>Segurança:</strong> Garantir a integridade e confidencialidade em todo o processo</li>
        </ul>

        <div class="key-points">
            <h3>Novidades no Laravel 12</h3>
            <ul>
                <li><strong>Suporte ao PHP 8.2+:</strong> Laravel 12 requer PHP 8.2 ou superior</li>
                <li><strong>Tipagem Estrita:</strong> Maior segurança com tipagem forte em todo o framework</li>
                <li><strong>Vite por Padrão:</strong> Substituição completa do Laravel Mix pelo Vite</li>
                <li><strong>Pest Integration:</strong> Melhor suporte para testes com Pest</li>
                <li><strong>Melhorias no Sistema de Filas:</strong> Novos recursos para processamento assíncrono</li>
                <li><strong>Otimizações de Performance:</strong> Melhorias significativas no desempenho do framework
                </li>
                <li><strong>Melhor Suporte para Containers:</strong> Ferramentas aprimoradas para deployment em
                    containers</li>
            </ul>
        </div>
    </section>

    <section id="requisitos" class="manual-section">
        <h2>2. Requisitos de Sistema</h2>
        <p>Esta seção detalha os requisitos necessários para a implantação da aplicação Laravel 12 em cada ambiente.</p>

        <section id="req-hardware" class="subsection">
            <h3>2.1. Requisitos de Hardware</h3>
            <p>Os requisitos de hardware variam de acordo com o ambiente e a carga esperada:</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Ambiente</th>
                            <th>CPU</th>
                            <th>Memória RAM</th>
                            <th>Armazenamento</th>
                            <th>Observações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Desenvolvimento</td>
                            <td>4+ cores</td>
                            <td>8+ GB</td>
                            <td>50+ GB SSD</td>
                            <td>Configuração mínima para ambiente local</td>
                        </tr>
                        <tr>
                            <td>Homologação</td>
                            <td>4+ cores</td>
                            <td>16+ GB</td>
                            <td>100+ GB SSD</td>
                            <td>Similar à produção para testes realistas</td>
                        </tr>
                        <tr>
                            <td>Produção (básico)</td>
                            <td>8+ cores</td>
                            <td>32+ GB</td>
                            <td>200+ GB SSD</td>
                            <td>Para tráfego moderado</td>
                        </tr>
                        <tr>
                            <td>Produção (escala)</td>
                            <td>16+ cores</td>
                            <td>64+ GB</td>
                            <td>500+ GB SSD</td>
                            <td>Para alto tráfego, distribuído em múltiplos servidores</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="note">
                <p>Para ambientes de produção, recomenda-se uma infraestrutura escalável horizontalmente, permitindo
                    adicionar mais recursos conforme necessário. Considere o uso de serviços em nuvem como AWS, Google
                    Cloud ou Azure para maior flexibilidade.</p>
            </div>
        </section>

        <section id="req-software" class="subsection">
            <h3>2.2. Requisitos de Software</h3>
            <p>Os seguintes componentes de software são necessários para executar a aplicação Laravel 12:</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Componente</th>
                            <th>Versão</th>
                            <th>Notas</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>PHP</td>
                            <td>8.2+</td>
                            <td>Com extensões: BCMath, Ctype, Fileinfo, JSON, Mbstring, OpenSSL, PDO, Tokenizer, XML, GD
                            </td>
                        </tr>
                        <tr>
                            <td>Nginx</td>
                            <td>1.22+</td>
                            <td>Alternativa: Apache 2.4+</td>
                        </tr>
                        <tr>
                            <td>MySQL</td>
                            <td>8.0+</td>
                            <td>Alternativas: MariaDB 10.6+, PostgreSQL 14+</td>
                        </tr>
                        <tr>
                            <td>Redis</td>
                            <td>7.0+</td>
                            <td>Para cache e filas</td>
                        </tr>
                        <tr>
                            <td>Node.js</td>
                            <td>18+</td>
                            <td>Para compilação de assets com Vite</td>
                        </tr>
                        <tr>
                            <td>Composer</td>
                            <td>2.5+</td>
                            <td>Gerenciador de dependências PHP</td>
                        </tr>
                        <tr>
                            <td>Git</td>
                            <td>2.34+</td>
                            <td>Para controle de versão</td>
                        </tr>
                        <tr>
                            <td>Supervisor</td>
                            <td>4.2+</td>
                            <td>Para gerenciamento de processos</td>
                        </tr>
                        <tr>
                            <td>Sistema Operacional</td>
                            <td>Ubuntu 22.04+ LTS</td>
                            <td>Alternativas: Debian 12+, Rocky Linux 9+, Amazon Linux 2023</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="req-rede" class="subsection">
            <h3>2.3. Requisitos de Rede</h3>
            <p>Configurações de rede necessárias para a aplicação funcionar corretamente:</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Serviço</th>
                            <th>Porta</th>
                            <th>Protocolo</th>
                            <th>Acesso</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>HTTP</td>
                            <td>80</td>
                            <td>TCP</td>
                            <td>Público (redirecionado para HTTPS)</td>
                        </tr>
                        <tr>
                            <td>HTTPS</td>
                            <td>443</td>
                            <td>TCP</td>
                            <td>Público</td>
                        </tr>
                        <tr>
                            <td>MySQL/MariaDB</td>
                            <td>3306</td>
                            <td>TCP</td>
                            <td>Privado (apenas servidores da aplicação)</td>
                        </tr>
                        <tr>
                            <td>PostgreSQL</td>
                            <td>5432</td>
                            <td>TCP</td>
                            <td>Privado (apenas servidores da aplicação)</td>
                        </tr>
                        <tr>
                            <td>Redis</td>
                            <td>6379</td>
                            <td>TCP</td>
                            <td>Privado (apenas servidores da aplicação)</td>
                        </tr>
                        <tr>
                            <td>SSH</td>
                            <td>22 (ou personalizado)</td>
                            <td>TCP</td>
                            <td>Privado (apenas IPs autorizados)</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="warning">
                <p><strong>Importante:</strong> Em ambientes de produção, configure sempre um firewall para limitar o
                    acesso aos serviços. Exponha publicamente apenas as portas estritamente necessárias (tipicamente
                    apenas 80 e 443).</p>
            </div>

            <h4>2.3.1. Integrações Externas</h4>
            <p>Certifique-se de que os servidores têm acesso às seguintes APIs e serviços externos:</p>
            <ul>
                <li>APIs de pagamento (Stripe, PayPal, etc.)</li>
                <li>Serviços de e-mail (SMTP)</li>
                <li>APIs de armazenamento (S3, etc.)</li>
                <li>Serviços de monitoramento</li>
                <li>CDNs</li>
            </ul>
        </section>
    </section>

    <section id="ambientes" class="manual-section">
        <h2>3. Ambientes de Implantação</h2>
        <p>A aplicação utiliza múltiplos ambientes para garantir qualidade e estabilidade antes da disponibilização para
            os usuários finais.</p>

        <section id="amb-desenvolvimento" class="subsection">
            <h3>3.1. Ambiente de Desenvolvimento</h3>
            <p>Ambiente utilizado pelos desenvolvedores para codificação e testes iniciais.</p>

            <div class="best-practice">
                <h4>Características do Ambiente de Desenvolvimento</h4>
                <ul>
                    <li>Configuração simplificada, geralmente local ou em containers</li>
                    <li>Debugging habilitado</li>
                    <li>Banco de dados com dados de teste</li>
                    <li>Serviços externos simulados (mocks) ou apontando para sandbox</li>
                    <li>Compilação de assets em tempo real (hot reload com Vite)</li>
                    <li>Otimizações desabilitadas para facilitar a depuração</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configuração do .env para Desenvolvimento</h4>
                <pre>
APP_NAME="Minha Aplicação"
APP_ENV=local
APP_KEY=base64:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=app_development
DB_USERNAME=root
DB_PASSWORD=secret

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# APIs em modo sandbox/teste
PAYMENT_API_MODE=sandbox</pre>
            </div>

            <h4>3.1.1. Setup do Ambiente de Desenvolvimento</h4>
            <div class="code-block">
                <h4>Configuração do Ambiente Local</h4>
                <pre>
# Clone do repositório
<NAME_EMAIL>:empresa/projeto.git
cd projeto

# Instalação de dependências
composer install
npm install

# Configuração do ambiente
cp .env.example .env
php artisan key:generate

# Criação e migração do banco de dados
php artisan migrate:fresh --seed

# Compilação de assets com Vite
npm run dev

# Iniciar servidor de desenvolvimento
php artisan serve</pre>
            </div>

            <div class="code-block">
                <h4>Configuração com Laravel Sail (Docker)</h4>
                <pre>
# Instalação do Laravel Sail
composer require laravel/sail --dev
php artisan sail:install

# Iniciar containers
./vendor/bin/sail up -d

# Executar comandos dentro do container
./vendor/bin/sail artisan migrate:fresh --seed
./vendor/bin/sail npm install
./vendor/bin/sail npm run dev</pre>
            </div>
        </section>

        <section id="amb-homologacao" class="subsection">
            <h3>3.2. Ambiente de Homologação</h3>
            <p>Ambiente intermediário que simula o ambiente de produção, utilizado para testes de integração, validação
                de funcionalidades e aprovação por stakeholders.</p>

            <div class="best-practice">
                <h4>Características do Ambiente de Homologação</h4>
                <ul>
                    <li>Configuração similar à produção</li>
                    <li>Dados anonimizados de produção ou dataset representativo</li>
                    <li>Conectado a versões de sandbox de serviços externos</li>
                    <li>Otimização de assets como em produção</li>
                    <li>Logging mais detalhado que em produção</li>
                    <li>Acesso restrito a usuários autorizados (equipe interna e clientes para UAT)</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configuração do .env para Homologação</h4>
                <pre>
APP_NAME="Minha Aplicação"
APP_ENV=staging
APP_KEY=base64:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
APP_DEBUG=false
APP_URL=https://staging.exemplo.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=staging-db.internal
DB_PORT=3306
DB_DATABASE=app_staging
DB_USERNAME=app_staging_user
DB_PASSWORD=${STAGING_DB_PASSWORD}

BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=staging-redis.internal
REDIS_PASSWORD=${STAGING_REDIS_PASSWORD}
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog.internal
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# APIs em modo sandbox/teste
PAYMENT_API_MODE=sandbox</pre>
            </div>

            <h4>3.2.1. Deploy para Homologação</h4>
            <div class="code-block">
                <h4>Script de Deploy para Homologação</h4>
                <pre>
#!/bin/bash

# Definir variáveis
DEPLOY_PATH="/var/www/staging"
REPO_URL="**************:empresa/projeto.git"
BRANCH="develop"

# 1. Atualizar código-fonte
cd $DEPLOY_PATH
git fetch --all
git checkout $BRANCH
git pull origin $BRANCH

# 2. Instalar dependências
composer install --no-dev --optimize-autoloader
npm ci
npm run build

# 3. Atualizar configurações
cp .env.staging .env
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 4. Executar migrações
php artisan migrate --force

# 5. Limpar cache
php artisan cache:clear

# 6. Reiniciar serviços
sudo supervisorctl restart app-staging-worker:*
sudo systemctl reload php8.2-fpm
sudo systemctl reload nginx</pre>
            </div>
        </section>

        <section id="amb-producao" class="subsection">
            <h3>3.3. Ambiente de Produção</h3>
            <p>Ambiente final, otimizado para performance, segurança e disponibilidade, onde os usuários acessam a
                aplicação.</p>

            <div class="best-practice">
                <h4>Características do Ambiente de Produção</h4>
                <ul>
                    <li>Alta disponibilidade e escalabilidade</li>
                    <li>Debug desabilitado</li>
                    <li>Máxima otimização de recursos</li>
                    <li>Cache configurado para melhor desempenho</li>
                    <li>Conexão com serviços externos em modo de produção</li>
                    <li>Monitoramento e alertas completos</li>
                    <li>Backups automáticos</li>
                    <li>Segurança reforçada</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configuração do .env para Produção</h4>
                <pre>
APP_NAME="Minha Aplicação"
APP_ENV=production
APP_KEY=base64:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
APP_DEBUG=false
APP_URL=https://exemplo.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=warning

DB_CONNECTION=mysql
DB_HOST=${PROD_DB_HOST}
DB_PORT=3306
DB_DATABASE=${PROD_DB_NAME}
DB_USERNAME=${PROD_DB_USER}
DB_PASSWORD=${PROD_DB_PASSWORD}

BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
FILESYSTEM_DISK=s3
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_URL=${PROD_REDIS_URL}

MAIL_MAILER=smtp
MAIL_HOST=${PROD_SMTP_HOST}
MAIL_PORT=${PROD_SMTP_PORT}
MAIL_USERNAME=${PROD_SMTP_USER}
MAIL_PASSWORD=${PROD_SMTP_PASSWORD}
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# APIs em modo de produção
PAYMENT_API_MODE=production</pre>
            </div>

            <h4>3.3.1. Configuração de Servidor Web para Produção</h4>
            <div class="code-block">
                <h4>Configuração do Nginx</h4>
                <pre>
# /etc/nginx/sites-available/exemplo.com.conf

server {
    listen 80;
    server_name exemplo.com www.exemplo.com;

    # Redirect to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name exemplo.com www.exemplo.com;

    ssl_certificate /etc/letsencrypt/live/exemplo.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/exemplo.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

    # SSL optimizations
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;

    # HSTS (optional - uncomment after testing)
    # add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

    # Security headers
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    root /var/www/production/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_intercept_errors on;
        fastcgi_buffer_size 16k;
        fastcgi_buffers 4 16k;
    }

    # Static files caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
        expires 7d;
        add_header Cache-Control "public, no-transform";
    }

    # Deny access to .htaccess files
    location ~ /\.ht {
        deny all;
    }

    # Deny access to hidden files and directories
    location ~ /\. {
        deny all;
    }
}</pre>
            </div>
        </section>

        <section id="amb-isolamento" class="subsection">
            <h3>3.4. Isolamento de Ambientes</h3>
            <p>Para garantir segurança, estabilidade e evitar contaminação de dados, os ambientes devem ser
                completamente isolados uns dos outros.</p>

            <div class="best-practice">
                <h4>Práticas de Isolamento</h4>
                <ul>
                    <li>Utilize servidores/instâncias separados para cada ambiente</li>
                    <li>Bancos de dados separados, preferencialmente em servidores diferentes</li>
                    <li>Controle de acesso específico para cada ambiente</li>
                    <li>Configurações e variáveis de ambiente distintas</li>
                    <li>Integração com serviços externos em modos compatíveis (sandbox para desenvolvimento e
                        homologação)</li>
                    <li>Firewalls e regras de rede separadas para cada ambiente</li>
                    <li>Subdomínios distintos para cada ambiente (ex: dev.exemplo.com, staging.exemplo.com, exemplo.com)
                    </li>
                    <li>Nunca copiar dados de produção para outros ambientes sem anonimização</li>
                </ul>
            </div>

            <div class="warning">
                <p><strong>Importante:</strong> Dados sensíveis de produção nunca devem estar disponíveis em ambientes
                    de desenvolvimento ou homologação sem o devido processo de anonimização.</p>
            </div>

            <h4>3.4.1. Anonimização de Dados</h4>
            <p>Quando necessário utilizar dados de produção em outros ambientes, utilize técnicas de anonimização:</p>

            <div class="code-block">
                <h4>Comando de Anonimização de Dados</h4>
                <pre>
# Script de exemplo para anonimizar dados (execute em staging, nunca em produção)
php artisan backup:anonymize-database --output=staging_db.sql

# Exemplo do que o comando acima poderia fazer:
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;

class AnonymizeDatabaseCommand extends Command
{
    protected $signature = 'backup:anonymize-database {--output=anonymized_db.sql}';
    protected $description = 'Cria um dump do banco com dados sensíveis anonimizados';

    public function handle(): void
    {
        $this->info('Anonimizando dados sensíveis...');
        $faker = Faker::create('pt_BR');

        // Atualiza dados sensíveis nas tabelas
        DB::table('users')->update([
            'email' => DB::raw("CONCAT('user_', id, '@example.com')"),
            'name' => DB::raw("CONCAT('User ', id)"),
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' // 'password'
        ]);

        DB::table('customers')->update([
            'phone' => DB::raw("CONCAT('+551199', LPAD(id, 7, '0'))"),
            'document' => DB::raw("LPAD(id, 11, '0')"),
            'address' => 'Rua de Exemplo, 123'
        ]);

        // Exporta banco anonimizado
        $output = $this->option('output');
        $this->info("Exportando para $output...");

        // Lógica de exportação do banco aqui...

        $this->info('Concluído!');
    }
}</pre>
            </div>
        </section>
    </section>

    <section id="build" class="manual-section">
        <h2>4. Processo de Build</h2>
        <p>O processo de build transforma o código fonte em artefatos prontos para implantação, incluindo compilação,
            otimização e empacotamento.</p>

        <section id="build-dependencies" class="subsection">
            <h3>4.1. Gerenciamento de Dependências</h3>
            <p>O gerenciamento correto de dependências é crucial para garantir builds consistentes e confiáveis.</p>

            <div class="best-practice">
                <h4>Boas Práticas para Dependências</h4>
                <ul>
                    <li>Especifique versões exatas nas dependências para evitar incompatibilidades inesperadas</li>
                    <li>Utilize lock files (composer.lock, package-lock.json) e mantenha-os no controle de versão</li>
                    <li>Revise regularmente as dependências em busca de vulnerabilidades</li>
                    <li>Mantenha um registro das dependências e suas licenças</li>
                    <li>Considere utilizar repositórios privados de pacotes para dependências críticas</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Instalação de Dependências</h4>
                <pre>
# Instalação de dependências PHP para produção
composer install --no-dev --optimize-autoloader

# Instalação de dependências JavaScript para produção
npm ci --production

# Verificação de vulnerabilidades
composer audit
npm audit</pre>
            </div>
            <h4>4.1.1. Otimização do Autoloader</h4>
            <p>Para melhorar a performance em ambientes de produção, o Laravel 12 oferece diversas otimizações para o
                autoloader do Composer:</p>

            <div class="code-block">
                <h4>Comandos de Otimização</h4>
                <pre>
# Otimização do autoloader do Composer
composer install --no-dev --optimize-autoloader

# Explicação das flags:
# --no-dev: Não instala pacotes de desenvolvimento
# --optimize-autoloader: Gera um mapa de classes para carregamento mais rápido
# --classmap-authoritative: Torna o autoloader mais rápido, mas requer reconstrução após qualquer alteração

# Otimização adicional para produção
composer dump-autoload --no-dev --optimize --classmap-authoritative</pre>
            </div>

            <div class="best-practice">
                <h4>Benefícios da Otimização do Autoloader</h4>
                <ul>
                    <li>Redução significativa no tempo de carregamento da aplicação</li>
                    <li>Menor uso de memória</li>
                    <li>Melhor desempenho em requisições consecutivas</li>
                    <li>Redução na carga do servidor em ambientes de alto tráfego</li>
                </ul>
            </div>
        </section>

        <section id="build-assets" class="subsection">
            <h3>4.2. Compilação de Assets</h3>
            <p>O Laravel 12 utiliza Vite como ferramenta padrão para compilação de assets, substituindo completamente o
                Laravel Mix.</p>

            <div class="code-block">
                <h4>Configuração do Vite</h4>
                <pre>
// vite.config.js
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: true,
        }),
    ],
    build: {
        // Configurações de build para produção
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: true,
            },
        },
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['vue', 'axios'],
                },
            },
        },
        chunkSizeWarningLimit: 1000,
    },
});</pre>
            </div>

            <div class="code-block">
                <h4>Compilação de Assets para Produção</h4>
                <pre>
# Compilação para desenvolvimento (com hot reload)
npm run dev

# Compilação para produção
npm run build

# Verificação de assets antes do build
npm run lint

# Compilação com análise de tamanho dos bundles
npm run build -- --analyze</pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Assets</h4>
                <ul>
                    <li>Divida seu código em chunks para melhor cache e carregamento</li>
                    <li>Utilize lazy loading para componentes grandes</li>
                    <li>Otimize imagens antes de incluí-las no build</li>
                    <li>Implemente estratégias de cache para assets estáticos</li>
                    <li>Considere o uso de CDN para distribuição de assets em produção</li>
                    <li>Monitore o tamanho dos bundles para evitar problemas de performance</li>
                </ul>
            </div>
        </section>

        <section id="build-cache" class="subsection">
            <h3>4.3. Otimização de Cache</h3>
            <p>O Laravel 12 oferece diversos comandos para otimizar o cache da aplicação, melhorando significativamente
                o desempenho em produção.</p>

            <div class="code-block">
                <h4>Comandos de Cache para Produção</h4>
                <pre>
# Limpar caches antes de reconstruí-los
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Construir caches otimizados
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Otimização adicional (opcional)
php artisan optimize

# Comando único para todas as otimizações
php artisan optimize:all</pre>
            </div>

            <div class="warning">
                <p><strong>Importante:</strong> Não utilize os comandos de cache em ambiente de desenvolvimento, pois
                    isso pode causar comportamentos inesperados quando você alterar arquivos de configuração, rotas ou
                    views.</p>
            </div>

            <div class="best-practice">
                <h4>Quando Reconstruir os Caches</h4>
                <ul>
                    <li>Após cada deployment</li>
                    <li>Após alterações em arquivos de configuração</li>
                    <li>Após alterações em rotas</li>
                    <li>Após atualizações do framework ou pacotes</li>
                </ul>
            </div>
        </section>

        <section id="build-testes" class="subsection">
            <h3>4.4. Execução de Testes</h3>
            <p>A execução de testes automatizados é uma parte crucial do processo de build, garantindo que o código
                esteja funcionando conforme esperado antes do deployment.</p>

            <div class="code-block">
                <h4>Execução de Testes com Pest</h4>
                <pre>
# Executar todos os testes
./vendor/bin/pest

# Executar testes com cobertura
./vendor/bin/pest --coverage

# Executar testes em paralelo para maior velocidade
./vendor/bin/pest --parallel

# Executar apenas testes específicos
./vendor/bin/pest --filter=UserTest

# Executar testes com relatório detalhado
./vendor/bin/pest --coverage --min=80</pre>
            </div>

            <div class="code-block">
                <h4>Integração de Testes no Processo de Build</h4>
                <pre>
#!/bin/bash

# Script de build com testes integrados

echo "Instalando dependências..."
composer install --no-dev --optimize-autoloader
npm ci

echo "Executando testes..."
./vendor/bin/pest --parallel

# Verificar se os testes passaram
if [ $? -ne 0 ]; then
    echo "Falha nos testes. Build interrompido."
    exit 1
fi

echo "Compilando assets..."
npm run build

echo "Otimizando aplicação..."
php artisan optimize:all

echo "Build concluído com sucesso!"</pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Testes</h4>
                <ul>
                    <li>Mantenha uma cobertura de testes adequada (recomendado: >80%)</li>
                    <li>Execute testes em um ambiente similar ao de produção</li>
                    <li>Inclua testes de integração e end-to-end além dos testes unitários</li>
                    <li>Automatize a execução de testes no pipeline CI/CD</li>
                    <li>Falhe o build se os testes não passarem</li>
                    <li>Mantenha os testes rápidos para feedback imediato</li>
                    <li>Utilize bancos de dados em memória para testes mais rápidos</li>
                </ul>
            </div>
        </section>

        <section id="build-artefatos" class="subsection">
            <h3>4.5. Geração de Artefatos</h3>
            <p>A geração de artefatos de build consiste em criar pacotes prontos para implantação, facilitando o
                processo de deployment e rollback.</p>

            <div class="code-block">
                <h4>Script de Geração de Artefatos</h4>
                <pre>
#!/bin/bash

# Definir variáveis
VERSION=$(git describe --tags --always)
BUILD_DIR="builds"
ARTIFACT_NAME="app-${VERSION}.tar.gz"

# Criar diretório de build
mkdir -p $BUILD_DIR

# Limpar arquivos temporários e desnecessários
rm -rf node_modules vendor *.log

# Instalar dependências de produção
composer install --no-dev --optimize-autoloader
npm ci --production
npm run build

# Otimizar aplicação
php artisan optimize:all

# Remover arquivos desnecessários para produção
rm -rf .git .github tests phpunit.xml .env.example

# Criar arquivo de artefato
tar -czf "${BUILD_DIR}/${ARTIFACT_NAME}" --exclude="builds" --exclude="storage/logs/*" --exclude="storage/app/*" --exclude=".env" .

# Restaurar ambiente de desenvolvimento
composer install
npm ci

echo "Artefato criado: ${BUILD_DIR}/${ARTIFACT_NAME}"</pre>
            </div>

            <div class="best-practice">
                <h4>Conteúdo do Artefato</h4>
                <p>Um artefato de build para Laravel 12 deve incluir:</p>
                <ul>
                    <li>Código-fonte da aplicação</li>
                    <li>Dependências PHP instaladas (diretório vendor)</li>
                    <li>Assets compilados (diretório public/build)</li>
                    <li>Arquivos de configuração (exceto .env)</li>
                    <li>Migrações de banco de dados</li>
                    <li>Caches pré-construídos (bootstrap/cache)</li>
                </ul>
            </div>

            <div class="warning">
                <p><strong>Importante:</strong> Nunca inclua arquivos sensíveis como .env, chaves privadas ou
                    credenciais nos artefatos de build. Esses arquivos devem ser gerenciados separadamente através de um
                    sistema seguro de gerenciamento de configurações.</p>
            </div>
        </section>
    </section>

    <section id="deployment" class="manual-section">
        <h2>5. Processo de Deployment</h2>
        <p>O processo de deployment consiste na implantação da aplicação nos diferentes ambientes, seguindo práticas que
            garantam estabilidade, segurança e facilidade de rollback.</p>

        <section id="deploy-estrategias" class="subsection">
            <h3>5.1. Estratégias de Deployment</h3>
            <p>Existem várias estratégias para realizar o deployment de aplicações Laravel, cada uma com suas vantagens
                e desvantagens.</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Estratégia</th>
                            <th>Descrição</th>
                            <th>Vantagens</th>
                            <th>Desvantagens</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Basic Deployment</td>
                            <td>Atualização direta do código no servidor</td>
                            <td>Simples, fácil de implementar</td>
                            <td>Downtime durante o deployment, difícil rollback</td>
                        </tr>
                        <tr>
                            <td>Rolling Deployment</td>
                            <td>Atualização gradual dos servidores</td>
                            <td>Reduz o impacto de problemas, permite testes em produção</td>
                            <td>Complexidade na gestão de múltiplas versões simultâneas</td>
                        </tr>
                        <tr>
                            <td>Blue-Green Deployment</td>
                            <td>Dois ambientes idênticos, um ativo e outro para a nova versão</td>
                            <td>Zero downtime, rollback instantâneo</td>
                            <td>Requer mais recursos, complexidade na sincronização de dados</td>
                        </tr>
                        <tr>
                            <td>Canary Deployment</td>
                            <td>Liberação gradual para um subconjunto de usuários</td>
                            <td>Detecção precoce de problemas, impacto limitado</td>
                            <td>Complexidade na configuração, necessidade de monitoramento avançado</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>5.1.1. Zero Downtime Deployment</h4>
            <p>Para aplicações Laravel 12 em produção, recomendamos a implementação de zero downtime deployment usando a
                estratégia Blue-Green ou Rolling Deployment.</p>

            <div class="code-block">
                <h4>Implementação de Blue-Green Deployment</h4>
                <pre>
#!/bin/bash

# Script de Blue-Green Deployment para Laravel 12

# Configurações
PRODUCTION_DIR="/var/www/production"
BLUE_DIR="${PRODUCTION_DIR}/blue"
GREEN_DIR="${PRODUCTION_DIR}/green"
CURRENT_LINK="${PRODUCTION_DIR}/current"
RELEASE_DIR="${PRODUCTION_DIR}/releases"
RELEASE_VERSION=$(date +"%Y%m%d%H%M%S")
NEW_RELEASE_DIR="${RELEASE_DIR}/${RELEASE_VERSION}"
ARTIFACT_PATH=$1

# Verificar se o artefato foi fornecido
if [ -z "$ARTIFACT_PATH" ]; then
    echo "Erro: Caminho do artefato não fornecido"
    echo "Uso: $0 caminho/para/artefato.tar.gz"
    exit 1
fi

# Criar diretório para a nova release
mkdir -p $NEW_RELEASE_DIR

# Extrair artefato
echo "Extraindo artefato para ${NEW_RELEASE_DIR}..."
tar -xzf $ARTIFACT_PATH -C $NEW_RELEASE_DIR

# Configurar permissões
echo "Configurando permissões..."
chmod -R 755 $NEW_RELEASE_DIR
chown -R www-data:www-data $NEW_RELEASE_DIR

# Criar diretórios de storage se não existirem
if [ ! -d "${PRODUCTION_DIR}/storage" ]; then
    echo "Criando diretório de storage compartilhado..."
    cp -r ${NEW_RELEASE_DIR}/storage ${PRODUCTION_DIR}/
    chmod -R 775 ${PRODUCTION_DIR}/storage
    chown -R www-data:www-data ${PRODUCTION_DIR}/storage
fi

# Remover storage da release e criar link simbólico
rm -rf ${NEW_RELEASE_DIR}/storage
ln -s ${PRODUCTION_DIR}/storage ${NEW_RELEASE_DIR}/storage

# Copiar arquivo .env para a nova release
cp ${PRODUCTION_DIR}/.env ${NEW_RELEASE_DIR}/.env

# Determinar qual ambiente está ativo (blue ou green)
if [ -L $CURRENT_LINK ] && [ "$(readlink $CURRENT_LINK)" == "$BLUE_DIR" ]; then
    INACTIVE_ENV="green"
    INACTIVE_DIR=$GREEN_DIR
else
    INACTIVE_ENV="blue"
    INACTIVE_DIR=$BLUE_DIR
fi

echo "Ambiente ativo atual: $(readlink $CURRENT_LINK)"
echo "Preparando ambiente $INACTIVE_ENV para a nova versão..."

# Remover conteúdo do ambiente inativo
rm -rf $INACTIVE_DIR
mkdir -p $INACTIVE_DIR

# Copiar nova release para o ambiente inativo
cp -a ${NEW_RELEASE_DIR}/. $INACTIVE_DIR/

# Executar migrações
echo "Executando migrações..."
cd $INACTIVE_DIR
php artisan migrate --force

# Limpar caches e otimizar
echo "Otimizando aplicação..."
php artisan optimize:all

# Verificar saúde da aplicação
echo "Verificando saúde da aplicação..."
php artisan app:health-check
if [ $? -ne 0 ]; then
    echo "Verificação de saúde falhou. Abortando deployment."
    exit 1
fi

# Alternar para o novo ambiente
echo "Alternando para o ambiente $INACTIVE_ENV..."
ln -sfn $INACTIVE_DIR $CURRENT_LINK

# Reiniciar serviços
echo "Reiniciando serviços..."
sudo systemctl reload php8.2-fpm
sudo systemctl reload nginx
sudo supervisorctl restart app-workers:*

# Limpar releases antigas (manter as 5 mais recentes)
echo "Limpando releases antigas..."
cd $RELEASE_DIR
ls -1t | tail -n +6 | xargs -I {} rm -rf {}

echo "Deployment concluído com sucesso!"
echo "Nova versão: $RELEASE_VERSION"
echo "Ambiente ativo: $INACTIVE_ENV"</pre>
            </div>
        </section>

        <section id="deploy-automacao" class="subsection">
            <h3>5.2. Automação de Deployment</h3>
            <p>A automação do processo de deployment é essencial para garantir consistência e reduzir erros humanos.</p>

            <div class="best-practice">
                <h4>Ferramentas de Automação</h4>
                <ul>
                    <li><strong>Laravel Forge:</strong> Plataforma gerenciada para provisionamento e deployment de
                        aplicações Laravel</li>
                    <li><strong>Laravel Envoyer:</strong> Ferramenta especializada em zero downtime deployment para
                        Laravel</li>
                    <li><strong>GitHub Actions:</strong> Integração direta com repositórios GitHub para CI/CD</li>
                    <li><strong>GitLab CI/CD:</strong> Pipeline integrado ao GitLab</li>
                    <li><strong>Jenkins:</strong> Servidor de automação open-source altamente configurável</li>
                    <li><strong>Ansible:</strong> Ferramenta de automação para configuração e deployment</li>
                    <li><strong>Docker + Kubernetes:</strong> Para deployments baseados em containers</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo de Workflow com GitHub Actions</h4>
                <pre>
# .github/workflows/deploy.yml
name: Deploy Laravel Application

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

jobs:
  build:
    name: Build Application
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, intl, bcmath, pdo_mysql
          coverage: none
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install PHP dependencies
        run: composer install --no-dev --optimize-autoloader
      
      - name: Install Node.js dependencies
        run: npm ci
      
      - name: Build assets
        run: npm run build
      
      - name: Run tests
        run: vendor/bin/pest
      
      - name: Create artifact
        run: |
          mkdir -p artifact
          tar -czf artifact/app.tar.gz --exclude=node_modules --exclude=tests --exclude=.git --exclude=artifact .
      
      - name: Upload artifact
        uses: actions/upload-artifact@v3
        with:
          name: app-artifact
          path: artifact/app.tar.gz
  
  deploy-staging:
    name: Deploy to Staging
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: staging
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v3
        with:
          name: app-artifact
          path: artifact
      
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      
      - name: Deploy to staging
        run: |
          scp -o StrictHostKeyChecking=no artifact/app.tar.gz ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/tmp/
          ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd /var/www/staging &&
            tar -xzf /tmp/app.tar.gz -C . &&
            composer install --no-dev --optimize-autoloader &&
            php artisan migrate --force &&
            php artisan optimize:all &&
            sudo systemctl reload php8.2-fpm &&
            sudo systemctl reload nginx &&
            rm /tmp/app.tar.gz
          '
  
  deploy-production:
    name: Deploy to Production
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    environment: production
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v3
        with:
          name: app-artifact
          path: artifact
      
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      
      - name: Deploy to production
        run: |
          scp -o StrictHostKeyChecking=no artifact/app.tar.gz ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/tmp/
          ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd /var/www/production &&
            bash deploy.sh /tmp/app.tar.gz
          '</pre>
            </div>
        </section>

        <section id="deploy-rollback" class="subsection">
            <h3>5.3. Estratégias de Rollback</h3>
            <p>Um plano de rollback eficiente é essencial para mitigar rapidamente problemas que possam surgir após um
                deployment.</p>

            <div class="best-practice">
                <h4>Boas Práticas para Rollback</h4>
                <ul>
                    <li>Mantenha versões anteriores da aplicação disponíveis para rollback rápido</li>
                    <li>Implemente migrações de banco de dados reversíveis</li>
                    <li>Documente procedimentos de rollback para cada tipo de alteração</li>
                    <li>Teste regularmente o processo de rollback</li>
                    <li>Automatize o processo de rollback quando possível</li>
                    <li>Mantenha backups de dados antes de alterações críticas</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Script de Rollback</h4>
                <pre>
#!/bin/bash

# Script de rollback para Laravel 12

# Configurações
PRODUCTION_DIR="/var/www/production"
RELEASES_DIR="${PRODUCTION_DIR}/releases"
CURRENT_LINK="${PRODUCTION_DIR}/current"

# Listar as últimas 5 releases
echo "Releases disponíveis para rollback:"
ls -lt $RELEASES_DIR | grep -v "total" | head -5

# Solicitar a versão para rollback
read -p "Digite o nome da release para rollback: " ROLLBACK_VERSION

# Verificar se a release existe
if [ ! -d "${RELEASES_DIR}/${ROLLBACK_VERSION}" ]; then
    echo "Erro: Release ${ROLLBACK_VERSION} não encontrada"
    exit 1
fi

# Verificar qual ambiente está ativo (blue ou green)
if [ -L $CURRENT_LINK ] && [ "$(readlink $CURRENT_LINK)" == "${PRODUCTION_DIR}/blue" ]; then
    ACTIVE_ENV="blue"
    INACTIVE_ENV="green"
    INACTIVE_DIR="${PRODUCTION_DIR}/green"
else
    ACTIVE_ENV="green"
    INACTIVE_ENV="blue"
    INACTIVE_DIR="${PRODUCTION_DIR}/blue"
fi

echo "Ambiente ativo atual: $ACTIVE_ENV"
echo "Preparando rollback para o ambiente $INACTIVE_ENV..."

# Limpar ambiente inativo
rm -rf $INACTIVE_DIR
mkdir -p $INACTIVE_DIR

# Copiar release anterior para o ambiente inativo
cp -a ${RELEASES_DIR}/${ROLLBACK_VERSION}/. $INACTIVE_DIR/

# Configurar storage e .env
rm -rf ${INACTIVE_DIR}/storage
ln -s ${PRODUCTION_DIR}/storage ${INACTIVE_DIR}/storage
cp ${PRODUCTION_DIR}/.env ${INACTIVE_DIR}/.env

# Executar migrações de rollback se necessário
read -p "Executar rollback de migrações? (s/n): " ROLLBACK_MIGRATIONS
if [ "$ROLLBACK_MIGRATIONS" = "s" ]; then
    echo "Executando rollback de migrações..."
    cd $INACTIVE_DIR
    php artisan migrate:rollback --step=1
fi

# Limpar caches e otimizar
echo "Otimizando aplicação..."
cd $INACTIVE_DIR
php artisan optimize:all

# Alternar para o ambiente com a versão anterior
echo "Alternando para o ambiente $INACTIVE_ENV com a versão ${ROLLBACK_VERSION}..."
ln -sfn $INACTIVE_DIR $CURRENT_LINK

# Reiniciar serviços
echo "Reiniciando serviços..."
sudo systemctl reload php8.2-fpm
sudo systemctl reload nginx
sudo supervisorctl restart app-workers:*

echo "Rollback concluído com sucesso!"
echo "Versão atual: $ROLLBACK_VERSION"
echo "Ambiente ativo: $INACTIVE_ENV"</pre>
            </div>
        </section>

        <section id="deploy-banco" class="subsection">
            <h3>5.4. Migrações de Banco de Dados</h3>
            <p>As migrações de banco de dados são uma parte crítica do processo de deployment e requerem atenção
                especial para evitar problemas.</p>

            <div class="best-practice">
                <h4>Boas Práticas para Migrações</h4>
                <ul>
                    <li>Sempre teste migrações em ambientes não-produtivos antes</li>
                    <li>Projete migrações para serem reversíveis (método down)</li>
                    <li>Evite alterações que bloqueiem tabelas por muito tempo</li>
                    <li>Para tabelas grandes, considere migrações em lotes ou em horários de baixo tráfego</li>
                    <li>Faça backup do banco de dados antes de migrações críticas</li>
                    <li>Documente alterações que não podem ser facilmente revertidas</li>
                    <li>Utilize transações para garantir atomicidade</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Migrações Seguras para Tabelas Grandes</h4>
                <pre>
// Exemplo de migração segura para tabelas grandes
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexToLargeTable extends Migration
{
    public function up(): void
    {
        // Para MySQL/MariaDB: adicionar índice sem bloquear a tabela
        DB::statement('ALTER TABLE large_table ADD INDEX idx_column (column) ALGORITHM=INPLACE, LOCK=NONE');
        
        // Alternativa para outros bancos ou operações mais complexas:
        // Adicionar em lotes para minimizar o impacto
        $chunkSize = 1000;
        $maxId = DB::table('large_table')->max('id');
        
        for ($i = 0; $i <= $maxId; $i += $chunkSize) {
            DB::table('large_table')
                ->where('id', '>=', $i)
                ->where('id', '<', $i + $chunkSize)
                ->update(['processed_column' => DB::raw('UPPER(original_column)')]);
                
            // Pequena pausa para não sobrecarregar o banco
            if ($i % ($chunkSize * 10) === 0) {
                sleep(1);
            }
        }
    }

    public function down(): void
    {
        // Remover o índice
        Schema::table('large_table', function (Blueprint $table) {
            $table->dropIndex('idx_column');
        });
        
        // Reverter a transformação em lotes
        $chunkSize = 1000;
        $maxId = DB::table('large_table')->max('id');
        
        for ($i = 0; $i <= $maxId; $i += $chunkSize) {
            DB::table('large_table')
                ->where('id', '>=', $i)
                ->where('id', '<', $i + $chunkSize)
                ->update(['processed_column' => DB::raw('LOWER(original_column)')]);
                
            if ($i % ($chunkSize * 10) === 0) {
                sleep(1);
            }
        }
    }
}</pre>
            </div>

            <div class="warning">
                <p><strong>Importante:</strong> Para aplicações de alto tráfego, considere ferramentas como Percona
                    Online Schema Change ou pt-online-schema-change para alterações em tabelas grandes sem bloqueios.
                </p>
            </div>
        </section>

        <section id="deploy-secrets" class="subsection">
            <h3>5.5. Gerenciamento de Secrets</h3>
            <p>O gerenciamento seguro de secrets (senhas, chaves de API, tokens) é fundamental para a segurança da
                aplicação.</p>

            <div class="best-practice">
                <h4>Boas Práticas para Secrets</h4>
                <ul>
                    <li>Nunca armazene secrets no controle de versão</li>
                    <li>Utilize variáveis de ambiente ou serviços de gerenciamento de secrets</li>
                    <li>Implemente rotação regular de secrets</li>
                    <li>Use diferentes secrets para cada ambiente</li>
                    <li>Limite o acesso aos secrets apenas a pessoas autorizadas</li>
                    <li>Audite o acesso e uso de secrets</li>
                </ul>
            </div>
            <div class="code-block">
                <h4>Gerenciamento de Secrets com Laravel 12</h4>
                <pre>
// Exemplo de uso do Laravel Vault para secrets
// Instalação: composer require laravel/vault

// Em config/services.php
return [
    'stripe' => [
        'key' => Vault::get('stripe/key'),
        'secret' => Vault::get('stripe/secret'),
    ],
];

// Configuração do Vault em config/vault.php
return [
    'default' => env('VAULT_DRIVER', 'file'),
    
    'drivers' => [
        'file' => [
            'driver' => 'file',
            'path' => storage_path('vault'),
        ],
        
        'aws' => [
            'driver' => 'aws-secrets-manager',
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'version' => 'latest',
            'prefix' => env('AWS_SECRETS_PREFIX', 'laravel/'),
        ],
        
        'hashicorp' => [
            'driver' => 'hashicorp',
            'address' => env('VAULT_ADDR'),
            'token' => env('VAULT_TOKEN'),
            'prefix' => env('VAULT_PREFIX', 'laravel/'),
        ],
    ],
];</pre>
            </div>

            <div class="code-block">
                <h4>Uso de Secrets em Deployment</h4>
                <pre>
#!/bin/bash

# Script para gerenciar secrets durante o deployment

# Configurações
APP_ENV=$1
SECRETS_DIR="/path/to/secrets/$APP_ENV"
TARGET_ENV="/var/www/$APP_ENV/.env"

# Verificar ambiente
if [ -z "$APP_ENV" ]; then
    echo "Erro: Ambiente não especificado"
    echo "Uso: $0 [production|staging|development]"
    exit 1
fi

# Verificar se o diretório de secrets existe
if [ ! -d "$SECRETS_DIR" ]; then
    echo "Erro: Diretório de secrets não encontrado: $SECRETS_DIR"
    exit 1
fi

# Criar arquivo .env base
cp /var/www/$APP_ENV/.env.example $TARGET_ENV

# Adicionar secrets ao arquivo .env
echo "Adicionando secrets ao arquivo .env..."
for file in $SECRETS_DIR/*; do
    if [ -f "$file" ]; then
        key=$(basename "$file")
        value=$(cat "$file")
        echo "$key=$value" >> $TARGET_ENV
    fi
done

# Definir permissões corretas
chmod 600 $TARGET_ENV
chown www-data:www-data $TARGET_ENV

echo "Secrets configurados com sucesso para ambiente $APP_ENV"</pre>
            </div>

            <div class="warning">
                <p><strong>Importante:</strong> Em ambientes de produção, considere o uso de soluções de gerenciamento
                    de secrets como HashiCorp Vault, AWS Secrets Manager ou Azure Key Vault em vez de armazenar secrets
                    em arquivos locais.</p>
            </div>
        </section>
    </section>

    <section id="infra" class="manual-section">
        <h2>6. Infraestrutura</h2>
        <p>Esta seção aborda as configurações de infraestrutura necessárias para suportar uma aplicação Laravel 12 em
            produção.</p>

        <section id="infra-servidores" class="subsection">
            <h3>6.1. Configuração de Servidores</h3>
            <p>A configuração adequada dos servidores é essencial para garantir desempenho, segurança e disponibilidade
                da aplicação.</p>

            <div class="best-practice">
                <h4>Recomendações para Servidores Web</h4>
                <ul>
                    <li>Use Nginx como servidor web principal devido ao seu desempenho e baixo consumo de recursos</li>
                    <li>Configure o PHP-FPM com ajustes específicos para a carga esperada</li>
                    <li>Implemente cache de opcode (OPcache) para melhorar o desempenho do PHP</li>
                    <li>Configure limites de memória e timeouts adequados para sua aplicação</li>
                    <li>Utilize HTTP/2 para melhor desempenho de carregamento</li>
                    <li>Implemente compressão gzip/brotli para reduzir o tamanho das respostas</li>
                    <li>Configure o servidor para lidar com picos de tráfego</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configuração Otimizada do PHP-FPM</h4>
                <pre>
; /etc/php/8.2/fpm/pool.d/www.conf

[www]
user = www-data
group = www-data

; Ajuste baseado na memória disponível e número de CPUs
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 15
pm.max_requests = 500

; Configurações de timeout
request_terminate_timeout = 60s
request_slowlog_timeout = 5s
slowlog = /var/log/php-fpm/slowlog.log

; Configurações de memória e upload
php_admin_value[memory_limit] = 256M
php_admin_value[upload_max_filesize] = 32M
php_admin_value[post_max_size] = 32M
php_admin_value[max_execution_time] = 60

; Configurações de log
php_admin_flag[log_errors] = on
php_admin_value[error_log] = /var/log/php-fpm/www-error.log</pre>
            </div>

            <div class="code-block">
                <h4>Configuração do OPcache</h4>
                <pre>
; /etc/php/8.2/fpm/conf.d/10-opcache.ini

[opcache]
opcache.enable=1
opcache.enable_cli=0
opcache.memory_consumption=128
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000
opcache.max_wasted_percentage=10
opcache.validate_timestamps=0
opcache.revalidate_freq=0
opcache.save_comments=1
opcache.fast_shutdown=1
opcache.enable_file_override=1
opcache.jit=1255
opcache.jit_buffer_size=64M</pre>
            </div>

            <div class="note">
                <p>Para ambientes de desenvolvimento, mantenha <code>opcache.validate_timestamps=1</code> e
                    <code>opcache.revalidate_freq=2</code> para que as alterações nos arquivos sejam detectadas
                    automaticamente.
                </p>
            </div>
        </section>

        <section id="infra-balanceamento" class="subsection">
            <h3>6.2. Balanceamento de Carga</h3>
            <p>Para aplicações de alta disponibilidade, o balanceamento de carga é essencial para distribuir o tráfego
                entre múltiplos servidores.</p>

            <div class="code-block">
                <h4>Configuração do Nginx como Load Balancer</h4>
                <pre>
# /etc/nginx/conf.d/load-balancer.conf

upstream laravel_app {
    # Algoritmo de balanceamento: least_conn, ip_hash, round_robin (padrão)
    least_conn;
    
    # Lista de servidores de aplicação
    server app1.internal:80 max_fails=3 fail_timeout=30s;
    server app2.internal:80 max_fails=3 fail_timeout=30s;
    server app3.internal:80 max_fails=3 fail_timeout=30s backup;
    
    # Manter conexões abertas
    keepalive 32;
}

server {
    listen 80;
    server_name exemplo.com www.exemplo.com;
    
    # Redirect to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name exemplo.com www.exemplo.com;
    
    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/exemplo.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/exemplo.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    
    # Proxy settings
    location / {
        proxy_pass http://laravel_app;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffering
        proxy_buffering on;
        proxy_buffer_size 16k;
        proxy_busy_buffers_size 24k;
        proxy_buffers 64 4k;
    }
    
    # Static files handling
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://laravel_app;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        expires 7d;
        add_header Cache-Control "public, no-transform";
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Considerações para Balanceamento de Carga</h4>
                <ul>
                    <li>Utilize verificações de saúde (health checks) para detectar servidores indisponíveis</li>
                    <li>Implemente sticky sessions se necessário para manter a sessão do usuário no mesmo servidor</li>
                    <li>Configure timeouts adequados para evitar problemas com requisições lentas</li>
                    <li>Considere o uso de serviços gerenciados como AWS ELB, Google Cloud Load Balancer ou Azure Load
                        Balancer</li>
                    <li>Implemente monitoramento para detectar problemas de balanceamento</li>
                    <li>Configure corretamente os cabeçalhos de proxy para preservar informações do cliente</li>
                </ul>
            </div>
        </section>

        <section id="infra-cache" class="subsection">
            <h3>6.3. Configuração de Cache</h3>
            <p>O uso eficiente de cache é fundamental para melhorar o desempenho da aplicação Laravel 12.</p>

            <div class="code-block">
                <h4>Configuração do Redis para Cache</h4>
                <pre>
# /etc/redis/redis.conf

# Configurações básicas
bind 127.0.0.1 ::1
protected-mode yes
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# Configurações de memória
maxmemory 1gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistência
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /var/lib/redis

# Configurações de segurança
requirepass ${REDIS_PASSWORD}

# Configurações de desempenho
appendonly no
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes
lua-time-limit 5000</pre>
            </div>

            <div class="code-block">
                <h4>Configuração de Cache no Laravel</h4>
                <pre>
// Em config/cache.php
return [
    'default' => env('CACHE_DRIVER', 'redis'),

    'stores' => [
        'redis' => [
            'driver' => 'redis',
            'connection' => 'cache',
            'lock_connection' => 'default',
        ],
        
        'redis_tags' => [
            'driver' => 'redis_tags',
            'connection' => 'cache',
        ],
        
        // Cache para dados frequentemente acessados
        'persistent' => [
            'driver' => 'redis',
            'connection' => 'persistent',
        ],
        
        // Cache para sessões
        'sessions' => [
            'driver' => 'redis',
            'connection' => 'sessions',
        ],
        
        // Cache para filas
        'queues' => [
            'driver' => 'redis',
            'connection' => 'queues',
        ],
    ],

    'prefix' => env('CACHE_PREFIX', 'laravel_cache'),
];

// Em config/database.php
'redis' => [
    'client' => env('REDIS_CLIENT', 'phpredis'),
    
    'options' => [
        'cluster' => env('REDIS_CLUSTER', 'redis'),
        'prefix' => env('REDIS_PREFIX', 'laravel_'),
    ],
    
    'default' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_DB', '0'),
        'read_timeout' => 60,
    ],
    
    'cache' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_CACHE_DB', '1'),
        'read_timeout' => 60,
    ],
    
    'sessions' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => '2',
        'read_timeout' => 60,
    ],
    
    'queues' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => '3',
        'read_timeout' => 60,
    ],
    
    'persistent' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => '4',
        'read_timeout' => 60,
    ],
],</pre>
            </div>

            <div class="best-practice">
                <h4>Estratégias de Cache</h4>
                <ul>
                    <li>Cache de consultas frequentes ao banco de dados</li>
                    <li>Cache de respostas de API</li>
                    <li>Cache de fragmentos de view</li>
                    <li>Cache de configurações e dados estáticos</li>
                    <li>Implementação de cache em camadas (hierárquico)</li>
                    <li>Uso de tags para invalidação seletiva de cache</li>
                    <li>Cache de rotas e configurações</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo de Implementação de Cache</h4>
                <pre>
// Em app/Services/ProductService.php
namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\Cache;

class ProductService
{
    public function getFeaturedProducts(int $limit = 10): array
    {
        // Cache por 1 hora com tag para invalidação seletiva
        return Cache::tags(['products', 'featured'])->remember('featured_products_' . $limit, 3600, function () use ($limit) {
            return Product::where('featured', true)
                ->with('categories')
                ->orderBy('popularity', 'desc')
                ->take($limit)
                ->get()
                ->toArray();
        });
    }
    
    public function getProductDetails(int $id): array
    {
        // Cache individual por produto
        return Cache::tags(['products', 'product:' . $id])->remember('product_' . $id, 3600, function () use ($id) {
            $product = Product::with(['categories', 'reviews', 'variants'])
                ->findOrFail($id);
                
            // Processar dados adicionais
            $product->load('relatedProducts');
            
            return $product->toArray();
        });
    }
    
    public function updateProduct(int $id, array $data): void
    {
        $product = Product::findOrFail($id);
        $product->update($data);
        
        // Invalidar cache relacionado ao produto
        Cache::tags(['product:' . $id])->flush();
        
        // Se o produto for destaque, invalidar também o cache de produtos destacados
        if ($product->featured) {
            Cache::tags(['featured'])->flush();
        }
    }
}</pre>
            </div>
        </section>

        <section id="infra-filas" class="subsection">
            <h3>6.4. Configuração de Filas</h3>
            <p>O sistema de filas do Laravel 12 permite processar tarefas demoradas de forma assíncrona, melhorando a
                experiência do usuário e a escalabilidade da aplicação.</p>

            <div class="code-block">
                <h4>Configuração de Filas</h4>
                <pre>
// Em config/queue.php
return [
    'default' => env('QUEUE_CONNECTION', 'redis'),

    'connections' => [
        'redis' => [
            'driver' => 'redis',
            'connection' => 'queues',
            'queue' => env('REDIS_QUEUE', 'default'),
            'retry_after' => 90,
            'block_for' => null,
            'after_commit' => false,
        ],
        
        // Fila para tarefas de alta prioridade
        'high' => [
            'driver' => 'redis',
            'connection' => 'queues',
            'queue' => 'high',
            'retry_after' => 60,
            'block_for' => null,
            'after_commit' => false,
        ],
        
        // Fila para tarefas de baixa prioridade
        'low' => [
            'driver' => 'redis',
            'connection' => 'queues',
            'queue' => 'low',
            'retry_after' => 120,
            'block_for' => null,
            'after_commit' => false,
        ],
        
        // Fila para processamento em lote
        'batch' => [
            'driver' => 'redis',
            'connection' => 'queues',
            'queue' => 'batch',
            'retry_after' => 300,
            'block_for' => null,
            'after_commit' => false,
        ],
    ],

    'failed' => [
        'driver' => env('QUEUE_FAILED_DRIVER', 'database-uuids'),
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'failed_jobs',
    ],
];</pre>
            </div>

            <div class="code-block">
                <h4>Configuração do Supervisor</h4>
                <pre>
; /etc/supervisor/conf.d/laravel-worker.conf

[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/production/current/artisan queue:work redis --queue=high,default,low --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/var/log/supervisor/worker.log
stopwaitsecs=3600
startsecs=10

[program:laravel-batch-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/production/current/artisan queue:work redis --queue=batch --tries=3 --max-time=7200
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/supervisor/batch-worker.log
stopwaitsecs=7200
startsecs=10</pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Filas</h4>
                <ul>
                    <li>Divida as filas por prioridade e tipo de tarefa</li>
                    <li>Configure o número adequado de workers com base na carga e recursos disponíveis</li>
                    <li>Implemente tratamento de falhas e retentativas</li>
                    <li>Monitore o tamanho das filas e o tempo de processamento</li>
                    <li>Configure alertas para filas que crescem além do esperado</li>
                    <li>Implemente jobs idempotentes para evitar problemas com processamento duplicado</li>
                    <li>Use batches para processar grandes volumes de dados</li>
                    <li>Configure timeouts adequados para evitar workers presos</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo de Job com Prioridade e Tratamento de Falhas</h4>
                <pre>
// Em app/Jobs/ProcessOrder.php
namespace App\Jobs;

use App\Models\Order;
use App\Services\PaymentService;
use App\Services\ShippingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Log;
use Throwable;

class ProcessOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $maxExceptions = 2;
    public int $timeout = 120;
    public int $backoff = 60;
    
    protected Order $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
        $this->onQueue('high'); // Define a fila de alta prioridade
    }
    
    public function middleware(): array
    {
        // Evita processamento simultâneo do mesmo pedido
        return [new WithoutOverlapping($this->order->id)];
    }

    public function handle(PaymentService $paymentService, ShippingService $shippingService): void
    {
        // Processar pagamento
        $paymentResult = $paymentService->processPayment($this->order);
        
        if (!$paymentResult->success) {
            Log::error('Falha no processamento do pagamento', [
                'order_id' => $this->order->id,
                'error' => $paymentResult->error
            ]);
            
            $this->fail($paymentResult->error);
            return;
        }
        
        // Atualizar status do pedido
        $this->order->update(['status' => 'paid']);
        
        // Processar envio
        $shippingResult = $shippingService->createShipment($this->order);
        
        if ($shippingResult->success) {
            $this->order->update([
                'status' => 'processing',
                'tracking_number' => $shippingResult->trackingNumber
            ]);
            
            // Notificar cliente
            NotifyCustomer::dispatch($this->order)->onQueue('default');
        }
    }
    
    public function failed(Throwable $exception): void
    {
        Log::error('Falha no processamento do pedido', [
            'order_id' => $this->order->id,
            'exception' => $exception->getMessage()
        ]);
        
        // Atualizar status do pedido
        $this->order->update(['status' => 'payment_failed']);
        
        // Notificar administrador
        NotifyAdmin::dispatch(
            'Falha no processamento do pedido #' . $this->order->id,
            $exception->getMessage()
        )->onQueue('low');
    }
}</pre>
            </div>
        </section>

        <section id="infra-storage" class="subsection">
            <h3>6.5. Armazenamento</h3>
            <p>A configuração adequada de armazenamento é essencial para aplicações que lidam com uploads de arquivos,
                geração de relatórios e outros tipos de dados.</p>

            <div class="code-block">
                <h4>Configuração de Armazenamento</h4>
                <pre>
// Em config/filesystems.php
return [
    'default' => env('FILESYSTEM_DISK', 'local'),

    'disks' => [
        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => false,
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
            'throw' => false,
        ],

        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
            'cache' => [
                'store' => 'redis',
                'expire' => 600,
                'prefix' => 's3_cache',
            ],
        ],
        
        // Disco para uploads de usuários
        'user_uploads' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_USER_UPLOADS_BUCKET'),
            'url' => env('AWS_USER_UPLOADS_URL'),
            'visibility' => 'private',
            'throw' => false,
        ],
        
        // Disco para backups
        'backups' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BACKUPS_BUCKET'),
            'visibility' => 'private',
            'throw' => false,
        ],
    ],

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],
];</pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Armazenamento</h4>
                <ul>
                    <li>Utilize serviços de armazenamento em nuvem (S3, Google Cloud Storage) para produção</li>
                    <li>Implemente políticas de ciclo de vida para arquivos temporários</li>
                    <li>Configure permissões adequadas para cada tipo de arquivo</li>
                    <li>Utilize URLs assinadas para acesso temporário a arquivos privados</li>
                    <li>Implemente validação de tipos de arquivo e limites de tamanho</li>
                    <li>Configure CDN para arquivos públicos frequentemente acessados</li>
                    <li>Implemente backup regular dos dados armazenados</li>
                    <li>Utilize discos separados para diferentes tipos de conteúdo</li>
                </ul>
            </div>
            <div class="code-block">
                <h4>Exemplo de Upload Seguro</h4>
                <pre>
// Em app/Http/Controllers/FileUploadController.php
namespace App\Http\Controllers;

use App\Http\Requests\FileUploadRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadController extends Controller
{
    public function store(FileUploadRequest $request): JsonResponse
    {
        // A validação já foi feita no FileUploadRequest
        $file = $request->file('file');
        
        // Gerar nome de arquivo seguro
        $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();
        
        // Determinar o diretório baseado no tipo de arquivo
        $fileType = $file->getMimeType();
        $directory = $this->getDirectoryForFileType($fileType);
        
        // Armazenar o arquivo
        $path = Storage::disk('user_uploads')->putFileAs(
            $directory,
            $file,
            $fileName
        );
        
        // Registrar o upload no banco de dados
        $upload = $request->user()->uploads()->create([
            'original_name' => $file->getClientOriginalName(),
            'file_name' => $fileName,
            'file_path' => $path,
            'file_size' => $file->getSize(),
            'mime_type' => $fileType,
            'disk' => 'user_uploads',
        ]);
        
        // Gerar URL temporária para acesso ao arquivo
        $url = Storage::disk('user_uploads')->temporaryUrl(
            $path,
            now()->addMinutes(30)
        );
        
        return response()->json([
            'success' => true,
            'file' => [
                'id' => $upload->id,
                'name' => $upload->original_name,
                'url' => $url,
                'size' => $upload->file_size,
                'type' => $upload->mime_type,
            ]
        ]);
    }
    
    private function getDirectoryForFileType(string $mimeType): string
    {
        // Organizar arquivos por tipo
        if (str_starts_with($mimeType, 'image/')) {
            return 'images/' . date('Y/m/d');
        } elseif (str_starts_with($mimeType, 'video/')) {
            return 'videos/' . date('Y/m/d');
        } elseif (str_starts_with($mimeType, 'application/pdf')) {
            return 'documents/pdf/' . date('Y/m/d');
        } elseif (str_starts_with($mimeType, 'application/')) {
            return 'documents/other/' . date('Y/m/d');
        }
        
        return 'misc/' . date('Y/m/d');
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Validação de Upload no FileUploadRequest</h4>
                <pre>
// Em app/Http/Requests/FileUploadRequest.php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FileUploadRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Ou implemente lógica de autorização
    }

    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'max:10240', // 10MB
                'mimes:jpeg,png,pdf,doc,docx,xls,xlsx,zip',
                'mimetypes:image/jpeg,image/png,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/zip',
            ],
        ];
    }
    
    public function messages(): array
    {
        return [
            'file.max' => 'O arquivo não pode ser maior que 10MB.',
            'file.mimes' => 'O formato do arquivo não é suportado.',
            'file.mimetypes' => 'O tipo de arquivo não é suportado.',
        ];
    }
}</pre>
            </div>
        </section>
    </section>

    <section id="cicd" class="manual-section">
        <h2>7. CI/CD (Integração e Entrega Contínuas)</h2>
        <p>A implementação de um pipeline de CI/CD eficiente é fundamental para automatizar o processo de teste, build e
            deployment da aplicação Laravel 12.</p>

        <section id="cicd-pipeline" class="subsection">
            <h3>7.1. Estrutura do Pipeline</h3>
            <p>Um pipeline de CI/CD completo para Laravel 12 deve incluir as seguintes etapas:</p>

            <div class="best-practice">
                <h4>Etapas do Pipeline</h4>
                <ul>
                    <li><strong>Checkout:</strong> Obtenção do código-fonte do repositório</li>
                    <li><strong>Instalação de Dependências:</strong> Composer e NPM</li>
                    <li><strong>Análise Estática:</strong> Verificação de qualidade de código</li>
                    <li><strong>Testes:</strong> Unitários, integração e end-to-end</li>
                    <li><strong>Build:</strong> Compilação de assets e otimização</li>
                    <li><strong>Empacotamento:</strong> Criação de artefatos de deployment</li>
                    <li><strong>Deployment:</strong> Implantação nos ambientes</li>
                    <li><strong>Verificação:</strong> Testes pós-deployment</li>
                    <li><strong>Notificação:</strong> Alertas sobre o resultado do pipeline</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Pipeline Completo com GitHub Actions</h4>
                <pre>
# .github/workflows/laravel-ci-cd.yml
name: Laravel CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

jobs:
  test:
    name: Test Application
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      redis:
        image: redis:7.0
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, intl, bcmath, pdo_mysql, redis
          coverage: pcov
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Get Composer Cache Directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      
      - name: Cache Composer dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-
      
      - name: Install PHP dependencies
        run: composer install --prefer-dist --no-progress
      
      - name: Install Node.js dependencies
        run: npm ci
      
      - name: Compile assets
        run: npm run build
      
      - name: Prepare Laravel Application
        run: |
          cp .env.example .env
          php artisan key:generate
          php artisan config:clear
      
      - name: Static Analysis
        run: |
          composer require --dev phpstan/phpstan
          ./vendor/bin/phpstan analyse app tests --level=5
      
      - name: Run Tests
        run: |
          php artisan test --coverage --min=80
          ./vendor/bin/pest --parallel
        env:
          DB_CONNECTION: mysql
          DB_HOST: 127.0.0.1
          DB_PORT: 3306
          DB_DATABASE: test_db
          DB_USERNAME: root
          DB_PASSWORD: password
          REDIS_HOST: 127.0.0.1
          REDIS_PORT: 6379
  
  build:
    name: Build Application
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'release'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, intl, bcmath
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install PHP dependencies
        run: composer install --no-dev --prefer-dist --optimize-autoloader
      
      - name: Install Node.js dependencies
        run: npm ci
      
      - name: Build assets
        run: npm run build
      
      - name: Optimize Laravel
        run: |
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          php artisan event:cache
      
      - name: Create artifact
        run: |
          mkdir -p artifact
          tar -czf artifact/app.tar.gz --exclude=node_modules --exclude=tests --exclude=.git --exclude=artifact .
      
      - name: Upload artifact
        uses: actions/upload-artifact@v3
        with:
          name: app-artifact
          path: artifact/app.tar.gz
          retention-days: 3
  
  deploy-staging:
    name: Deploy to Staging
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v3
        with:
          name: app-artifact
          path: artifact
      
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      
      - name: Deploy to staging
        run: |
          scp -o StrictHostKeyChecking=no artifact/app.tar.gz ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/tmp/
          ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd /var/www/staging &&
            bash deploy.sh /tmp/app.tar.gz
          '
      
      - name: Run post-deployment tests
        run: |
          sleep 30 # Wait for deployment to complete
          curl -s -o /dev/null -w "%{http_code}" https://staging.exemplo.com/api/health | grep 200
      
      - name: Notify on success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_COLOR: good
          SLACK_TITLE: Staging Deployment Successful
          SLACK_MESSAGE: 'Application deployed to staging environment successfully!'
      
      - name: Notify on failure
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_COLOR: danger
          SLACK_TITLE: Staging Deployment Failed
          SLACK_MESSAGE: 'Failed to deploy application to staging environment!'
  
  deploy-production:
    name: Deploy to Production
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    environment: production
    
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v3
        with:
          name: app-artifact
          path: artifact
      
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      
      - name: Deploy to production
        run: |
          scp -o StrictHostKeyChecking=no artifact/app.tar.gz ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/tmp/
          ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd /var/www/production &&
            bash deploy.sh /tmp/app.tar.gz
          '
      
      - name: Run post-deployment tests
        run: |
          sleep 30 # Wait for deployment to complete
          curl -s -o /dev/null -w "%{http_code}" https://exemplo.com/api/health | grep 200
      
      - name: Notify on success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_COLOR: good
          SLACK_TITLE: Production Deployment Successful
          SLACK_MESSAGE: 'Application deployed to production environment successfully!'
      
      - name: Notify on failure
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_COLOR: danger
          SLACK_TITLE: Production Deployment Failed
          SLACK_MESSAGE: 'Failed to deploy application to production environment!'</pre>
            </div>
        </section>

        <section id="cicd-ferramentas" class="subsection">
            <h3>7.2. Ferramentas de CI/CD</h3>
            <p>Existem diversas ferramentas que podem ser utilizadas para implementar CI/CD em projetos Laravel 12:</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Ferramenta</th>
                            <th>Vantagens</th>
                            <th>Desvantagens</th>
                            <th>Melhor para</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>GitHub Actions</td>
                            <td>Integração nativa com GitHub, configuração simples, executores gratuitos</td>
                            <td>Limitações nos minutos gratuitos, menos flexível que algumas alternativas</td>
                            <td>Projetos hospedados no GitHub, equipes pequenas a médias</td>
                        </tr>
                        <tr>
                            <td>GitLab CI/CD</td>
                            <td>Integração completa com GitLab, pipeline como código, runners auto-hospedados</td>
                            <td>Curva de aprendizado, pode ser complexo para configurações avançadas</td>
                            <td>Projetos hospedados no GitLab, DevOps completo</td>
                        </tr>
                        <tr>
                            <td>Jenkins</td>
                            <td>Altamente personalizável, grande ecossistema de plugins, auto-hospedado</td>
                            <td>Requer manutenção, configuração mais complexa, interface menos moderna</td>
                            <td>Grandes empresas, ambientes com requisitos específicos</td>
                        </tr>
                        <tr>
                            <td>CircleCI</td>
                            <td>Fácil configuração, bom paralelismo, cache eficiente</td>
                            <td>Pode ficar caro para projetos maiores, menos flexível que Jenkins</td>
                            <td>Startups, equipes focadas em velocidade de desenvolvimento</td>
                        </tr>
                        <tr>
                            <td>Laravel Forge + Envoyer</td>
                            <td>Específico para Laravel, fácil de usar, zero downtime deployment</td>
                            <td>Menos flexível para pipelines complexos, custo mensal</td>
                            <td>Equipes pequenas focadas exclusivamente em Laravel</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="best-practice">
                <h4>Critérios para Escolha da Ferramenta</h4>
                <ul>
                    <li>Tamanho e experiência da equipe</li>
                    <li>Complexidade do pipeline necessário</li>
                    <li>Orçamento disponível</li>
                    <li>Integração com outras ferramentas</li>
                    <li>Requisitos de segurança e conformidade</li>
                    <li>Necessidade de auto-hospedagem vs. serviço em nuvem</li>
                    <li>Tempo disponível para manutenção da infraestrutura de CI/CD</li>
                </ul>
            </div>
        </section>

        <section id="cicd-qualidade" class="subsection">
            <h3>7.3. Garantia de Qualidade</h3>
            <p>A integração de ferramentas de garantia de qualidade no pipeline CI/CD é essencial para manter a
                qualidade do código e prevenir regressões.</p>

            <div class="code-block">
                <h4>Configuração do PHPStan para Análise Estática</h4>
                <pre>
# phpstan.neon
parameters:
    level: 8
    paths:
        - app
        - tests
    excludePaths:
        - app/Console/Kernel.php
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    ignoreErrors:
        - '#PHPDoc tag @var#'
    reportUnmatchedIgnoredErrors: true</pre>
            </div>

            <div class="code-block">
                <h4>Configuração do PHP-CS-Fixer para Estilo de Código</h4>
                <pre>
# .php-cs-fixer.dist.php
$finder = PhpCsFixer\Finder::create()
    ->in([
        __DIR__ . '/app',
        __DIR__ . '/config',
        __DIR__ . '/database',
        __DIR__ . '/resources',
        __DIR__ . '/routes',
        __DIR__ . '/tests',
    ])
    ->name('*.php')
    ->notName('*.blade.php')
    ->ignoreDotFiles(true)
    ->ignoreVCS(true);

return (new PhpCsFixer\Config())
    ->setRules([
        '@PSR12' => true,
        'array_syntax' => ['syntax' => 'short'],
        'ordered_imports' => ['sort_algorithm' => 'alpha'],
        'no_unused_imports' => true,
        'not_operator_with_successor_space' => true,
        'trailing_comma_in_multiline' => true,
        'phpdoc_scalar' => true,
        'unary_operator_spaces' => true,
        'binary_operator_spaces' => true,
        'blank_line_before_statement' => [
            'statements' => ['break', 'continue', 'declare', 'return', 'throw', 'try'],
        ],
        'phpdoc_single_line_var_spacing' => true,
        'phpdoc_var_without_name' => true,
        'class_attributes_separation' => [
            'elements' => [
                'method' => 'one',
                'property' => 'one',
            ],
        ],
        'method_argument_space' => [
            'on_multiline' => 'ensure_fully_multiline',
            'keep_multiple_spaces_after_comma' => true,
        ],
        'single_trait_insert_per_statement' => true,
    ])
    ->setFinder($finder);</pre>
            </div>

            <div class="best-practice">
                <h4>Ferramentas de Qualidade para Laravel 12</h4>
                <ul>
                    <li><strong>PHPStan/Larastan:</strong> Análise estática de código</li>
                    <li><strong>PHP-CS-Fixer:</strong> Padronização de estilo de código</li>
                    <li><strong>Pest:</strong> Framework de testes moderno para PHP</li>
                    <li><strong>Laravel Pint:</strong> Formatador de código oficial do Laravel</li>
                    <li><strong>SonarQube:</strong> Análise contínua de qualidade de código</li>
                    <li><strong>Codecov/PHPUnit:</strong> Cobertura de testes</li>
                    <li><strong>Enlightn:</strong> Análise de performance, segurança e confiabilidade</li>
                    <li><strong>Rector:</strong> Refatoração automática de código</li>
                </ul>
            </div>
        </section>

        <section id="cicd-branching" class="subsection">
            <h3>7.4. Estratégia de Branching</h3>
            <p>Uma estratégia de branching bem definida é fundamental para o sucesso do CI/CD em projetos Laravel 12.
            </p>

            <div class="best-practice">
                <h4>GitFlow para Laravel 12</h4>
                <ul>
                    <li><strong>main:</strong> Código em produção, sempre estável</li>
                    <li><strong>develop:</strong> Código para a próxima release, integração contínua</li>
                    <li><strong>feature/*:</strong> Novas funcionalidades, ramificadas a partir de develop</li>
                    <li><strong>release/*:</strong> Preparação para release, testes finais e ajustes</li>
                    <li><strong>hotfix/*:</strong> Correções urgentes para produção, ramificadas a partir de main</li>
                    <li><strong>bugfix/*:</strong> Correções para a próxima release, ramificadas a partir de develop
                    </li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Fluxo de Trabalho com GitFlow</h4>
                <pre>
# Iniciar uma nova feature
git checkout develop
git pull origin develop
git checkout -b feature/nova-funcionalidade

# Trabalhar na feature e fazer commits
git add .
git commit -m "Implementa nova funcionalidade"

# Atualizar com as mudanças mais recentes de develop
git fetch origin
git rebase origin/develop

# Enviar a feature para revisão
git push origin feature/nova-funcionalidade

# Após aprovação do Pull Request, a feature é mesclada em develop
# Quando develop está pronto para release:
git checkout develop
git pull origin develop
git checkout -b release/1.2.0

# Ajustes finais na release
git add .
git commit -m "Ajusta versão para 1.2.0"

# Mesclar release em main e develop
git checkout main
git merge release/1.2.0 --no-ff
git tag -a v1.2.0 -m "Versão 1.2.0"
git push origin main --tags

git checkout develop
git merge release/1.2.0 --no-ff
git push origin develop

# Excluir branch de release
git branch -d release/1.2.0</pre>
            </div>

            <div class="best-practice">
                <h4>Alternativa: GitHub Flow</h4>
                <p>Para equipes menores ou projetos com ciclos de release mais rápidos, o GitHub Flow pode ser mais
                    adequado:</p>
                <ul>
                    <li><strong>main:</strong> Sempre pronto para deploy</li>
                    <li><strong>feature/*:</strong> Todas as alterações são feitas em branches de feature</li>
                    <li>Pull Requests para revisão de código</li>
                    <li>Após aprovação e testes, mesclar diretamente em main</li>
                    <li>Deploy automático a partir de main</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="monitoramento" class="manual-section">
        <h2>8. Monitoramento e Logging</h2>
        <p>O monitoramento adequado da aplicação Laravel 12 em produção é essencial para garantir disponibilidade,
            desempenho e segurança.</p>

        <section id="monitoramento-aplicacao" class="subsection">
            <h3>8.1. Monitoramento de Aplicação</h3>
            <p>O monitoramento da aplicação Laravel 12 deve abranger diversos aspectos para garantir uma visão completa
                do estado do sistema.</p>

            <div class="best-practice">
                <h4>Métricas Essenciais para Monitoramento</h4>
                <ul>
                    <li><strong>Tempo de resposta:</strong> Latência média, percentis (p95, p99)</li>
                    <li><strong>Taxa de requisições:</strong> Requisições por segundo, por endpoint</li>
                    <li><strong>Taxa de erros:</strong> Erros 4xx e 5xx por minuto</li>
                    <li><strong>Uso de recursos:</strong> CPU, memória, disco, conexões de banco de dados</li>
                    <li><strong>Filas:</strong> Tamanho, taxa de processamento, jobs com falha</li>
                    <li><strong>Cache:</strong> Taxa de acertos/erros, tempo de resposta</li>
                    <li><strong>Banco de dados:</strong> Tempo de consulta, conexões ativas, queries lentas</li>
                    <li><strong>Serviços externos:</strong> Disponibilidade, tempo de resposta</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Integração com Laravel Telescope</h4>
                <pre>
// Em config/telescope.php
return [
    'enabled' => env('TELESCOPE_ENABLED', true),
    
    'middleware' => [
        'web',
        Authorize::class,
    ],
    
    'storage' => [
        'database' => [
            'connection' => env('DB_CONNECTION', 'mysql'),
            'chunk' => 1000,
        ],
    ],
    
    'record_slow_queries' => true,
    'slow_query_threshold' => 100,
    
    'record_slow_requests' => true,
    'slow_request_threshold' => 1000,
    
    'watchers' => [
        Watchers\CacheWatcher::class => env('TELESCOPE_CACHE_WATCHER', true),
        Watchers\CommandWatcher::class => env('TELESCOPE_COMMAND_WATCHER', true),
        Watchers\DumpWatcher::class => env('TELESCOPE_DUMP_WATCHER', true),
        Watchers\EventWatcher::class => env('TELESCOPE_EVENT_WATCHER', true),
        Watchers\ExceptionWatcher::class => env('TELESCOPE_EXCEPTION_WATCHER', true),
        Watchers\JobWatcher::class => env('TELESCOPE_JOB_WATCHER', true),
        Watchers\LogWatcher::class => env('TELESCOPE_LOG_WATCHER', true),
        Watchers\MailWatcher::class => env('TELESCOPE_MAIL_WATCHER', true),
        Watchers\ModelWatcher::class => env('TELESCOPE_MODEL_WATCHER', true),
        Watchers\NotificationWatcher::class => env('TELESCOPE_NOTIFICATION_WATCHER', true),
        Watchers\QueryWatcher::class => [
            'enabled' => env('TELESCOPE_QUERY_WATCHER', true),
            'ignore_packages' => true,
            'ignore_paths' => [],
            'slow' => 100,
        ],
        Watchers\RedisWatcher::class => env('TELESCOPE_REDIS_WATCHER', true),
        Watchers\RequestWatcher::class => [
            'enabled' => env('TELESCOPE_REQUEST_WATCHER', true),
            'size_limit' => env('TELESCOPE_REQUEST_SIZE_LIMIT', 64),
        ],
        Watchers\ScheduleWatcher::class => env('TELESCOPE_SCHEDULE_WATCHER', true),
        Watchers\ViewWatcher::class => env('TELESCOPE_VIEW_WATCHER', true),
        Watchers\GateWatcher::class => [
            'enabled' => env('TELESCOPE_GATE_WATCHER', true),
            'ignore_abilities' => [],
            'ignore_packages' => true,
        ],
    ],
];</pre>
            </div>

            <div class="code-block">
                <h4>Integração com Prometheus e Grafana</h4>
                <pre>
// Instalação do pacote
// composer require spatie/laravel-prometheus

// Em app/Providers/AppServiceProvider.php
use Spatie\Prometheus\Collectors\MemoryCollector;
use Spatie\Prometheus\Collectors\QueueSizeCollector;
use Spatie\Prometheus\Facades\Prometheus;

public function boot(): void
{
    // Registrar coletores padrão
    Prometheus::addGauge('app_memory_usage', function (MemoryCollector $collector) {
        $collector->register();
    });
    
    Prometheus::addGauge('app_queue_size', function (QueueSizeCollector $collector) {
        $collector->connection('redis')->queue('default');
        $collector->connection('redis')->queue('high');
        $collector->connection('redis')->queue('low');
    });
    
    // Registrar coletores personalizados
    Prometheus::addCounter('app_jobs_processed_total', function ($counter) {
        $counter->help('The total number of processed jobs');
    });
    
    Prometheus::addHistogram('app_request_duration_seconds', function ($histogram) {
        $histogram
            ->help('The response time of requests in seconds')
            ->buckets([0.1, 0.25, 0.5, 0.75, 1, 2.5, 5, 7.5, 10]);
    });
    
    // Middleware para métricas de requisições HTTP
    $this->app->make(Router::class)->middlewareGroup('web', [
        // ... outros middlewares
        \App\Http\Middleware\TrackRequestDuration::class,
    ]);
}</pre>
            </div>

            <div class="code-block">
                <h4>Middleware para Métricas de Requisições</h4>
                <pre>
// Em app/Http/Middleware/TrackRequestDuration.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Spatie\Prometheus\Facades\Prometheus;
use Symfony\Component\HttpFoundation\Response;

class TrackRequestDuration
{
    public function handle(Request $request, Closure $next): mixed
    {
        // Marcar o início da requisição
        $startTime = microtime(true);
        
        // Processar a requisição
        $response = $next($request);
        
        // Calcular a duração
        $duration = microtime(true) - $startTime;
        
        // Registrar a duração no Prometheus
        Prometheus::getHistogram('app_request_duration_seconds')
            ->labels([
                'method' => $request->method(),
                'route' => $request->route() ? $request->route()->getName() ?? 'unnamed' : 'unknown',
                'status' => $response->getStatusCode(),
            ])
            ->observe($duration);
        
        // Incrementar contador de requisições
        Prometheus::getCounter('app_requests_total')
            ->labels([
                'method' => $request->method(),
                'status' => $response->getStatusCode(),
            ])
            ->inc();
        
        // Se for um erro, incrementar contador de erros
        if ($response->getStatusCode() >= 400) {
            Prometheus::getCounter('app_request_errors_total')
                ->labels([
                    'method' => $request->method(),
                    'status' => $response->getStatusCode(),
                ])
                ->inc();
        }
        
        return $response;
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Dashboard Grafana para Laravel</h4>
                <pre>
{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- Grafana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      },
      {
        "datasource": "Prometheus",
        "enable": true,
        "expr": "changes(app_deployments_total[1m]) > 0",
        "iconColor": "#5794F2",
        "name": "Deployments",
        "showIn": 0,
        "tags": ["deployment"],
        "titleFormat": "Deployment"
      }
    ]
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 0,
  "id": 1,
  "links": [],
  "panels": [
    {
      "aliasColors": {},
      "bars": false,
      "dashLength": 10,
      "dashes": false,
      "datasource": "Prometheus",
      "fill": 1,
      "fillGradient": 0,
      "gridPos": {
        "h": 8,
        "w": 12,
        "x": 0,
        "y": 0
      },
      "hiddenSeries": false,
      "id": 2,
      "legend": {
        "avg": false,
        "current": false,
        "max": false,
        "min": false,
        "show": true,
        "total": false,
        "values": false
      },
      "lines": true,
      "linewidth": 1,
      "nullPointMode": "null",
      "options": {
        "dataLinks": []
      },
      "percentage": false,
      "pointradius": 2,
      "points": false,
      "renderer": "flot",
      "seriesOverrides": [],
      "spaceLength": 10,
      "stack": false,
      "steppedLine": false,
      "targets": [
        {
          "expr": "histogram_quantile(0.95, sum(rate(app_request_duration_seconds_bucket[5m])) by (le, route))",
          "legendFormat": "p95 - {{route}}",
          "refId": "A"
        },
        {
          "expr": "histogram_quantile(0.50, sum(rate(app_request_duration_seconds_bucket[5m])) by (le, route))",
          "legendFormat": "p50 - {{route}}",
          "refId": "B"
        }
      ],
      "thresholds": [],
      "timeFrom": null,
      "timeRegions": [],
      "timeShift": null,
      "title": "Request Duration",
      "tooltip": {
        "shared": true,
        "sort": 0,
        "value_type": "individual"
      },
      "type": "graph",
      "xaxis": {
        "buckets": null,
        "mode": "time",
        "name": null,
        "show": true,
        "values": []
      },
      "yaxes": [
        {
          "format": "s",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        },
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        }
      ],
      "yaxis": {
        "align": false,
        "alignLevel": null
      }
    },
    {
      "aliasColors": {},
      "bars": false,
      "dashLength": 10,
      "dashes": false,
      "datasource": "Prometheus",
      "fill": 1,
      "fillGradient": 0,
      "gridPos": {
        "h": 8,
        "w": 12,
        "x": 12,
        "y": 0
      },
      "hiddenSeries": false,
      "id": 4,
      "legend": {
        "avg": false,
        "current": false,
        "max": false,
        "min": false,
        "show": true,
        "total": false,
        "values": false
      },
      "lines": true,
      "linewidth": 1,
      "nullPointMode": "null",
      "options": {
        "dataLinks": []
      },
      "percentage": false,
      "pointradius": 2,
      "points": false,
      "renderer": "flot",
      "seriesOverrides": [],
      "spaceLength": 10,
      "stack": false,
      "steppedLine": false,
      "targets": [
        {
          "expr": "sum(rate(app_requests_total[1m])) by (status)",
          "legendFormat": "{{status}}",
          "refId": "A"
        }
      ],
      "thresholds": [],
      "timeFrom": null,
      "timeRegions": [],
      "timeShift": null,
      "title": "Request Rate by Status",
      "tooltip": {
        "shared": true,
        "sort": 0,
        "value_type": "individual"
      },
      "type": "graph",
      "xaxis": {
        "buckets": null,
        "mode": "time",
        "name": null,
        "show": true,
        "values": []
      },
      "yaxes": [
        {
          "format": "reqps",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        },
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        }
      ],
      "yaxis": {
        "align": false,
        "alignLevel": null
      }
    }
  ],
  "refresh": "5s",
  "schemaVersion": 22,
  "style": "dark",
  "tags": ["laravel", "php"],
  "templating": {
    "list": []
  },
  "time": {
    "from": "now-1h",
    "to": "now"
  },
  "timepicker": {
    "refresh_intervals": [
      "5s",
      "10s",
      "30s",
      "1m",
      "5m",
      "15m",
      "30m",
      "1h",
      "2h",
      "1d"
    ]
  },
  "timezone": "",
  "title": "Laravel Application Metrics",
  "uid": "laravel-metrics",
  "version": 1
}</pre>
            </div>
        </section>

        <section id="monitoramento-logs" class="subsection">
            <h3>8.2. Configuração de Logging</h3>
            <p>Um sistema de logging bem configurado é essencial para diagnosticar problemas e monitorar o comportamento
                da aplicação em produção.</p>

            <div class="code-block">
                <h4>Configuração de Logging no Laravel 12</h4>
                <pre>
// Em config/logging.php
return [
    'default' => env('LOG_CHANNEL', 'stack'),

    'deprecations' => [
        'channel' => env('LOG_DEPRECATIONS_CHANNEL', 'null'),
        'trace' => false,
    ],

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['daily', 'slack'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
            'replace_placeholders' => true,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => env('APP_NAME', 'Laravel') . ' (' . env('APP_ENV') . ')',
            'emoji' => ':boom:',
            'level' => env('LOG_SLACK_LEVEL', 'critical'),
            'replace_placeholders' => true,
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => env('LOG_PAPERTRAIL_HANDLER', SyslogUdpHandler::class),
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
                'connectionString' => 'tls://'.env('PAPERTRAIL_URL').':'.env('PAPERTRAIL_PORT'),
            ],
            'processors' => [PsrLogMessageProcessor::class],
        ],

        'elasticsearch' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => \Monolog\Handler\ElasticsearchHandler::class,
            'handler_with' => [
                'client' => \Elasticsearch\ClientBuilder::create()
                    ->setHosts([env('ELASTICSEARCH_HOST', 'localhost:9200')])
                    ->build(),
                'options' => [
                    'index' => env('APP_NAME', 'laravel') . '-logs-' . strtolower(env('APP_ENV', 'production')),
                    'type' => '_doc',
                    'ignore_error' => false,
                ],
            ],
            'formatter' => \Monolog\Formatter\ElasticsearchFormatter::class,
            'formatter_with' => [
                'index' => env('APP_NAME', 'laravel') . '-logs-' . strtolower(env('APP_ENV', 'production')),
                'type' => '_doc',
            ],
        ],
    ],
];</pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Logging</h4>
                <ul>
                    <li>Use níveis de log apropriados (debug, info, warning, error, critical)</li>
                    <li>Inclua contexto suficiente em cada mensagem de log</li>
                    <li>Configure rotação de logs para evitar arquivos muito grandes</li>
                    <li>Centralize logs em um sistema como ELK Stack (Elasticsearch, Logstash, Kibana) ou Graylog</li>
                    <li>Configure alertas para erros críticos</li>
                    <li>Não registre informações sensíveis (senhas, tokens, dados pessoais)</li>
                    <li>Implemente logging estruturado (JSON) para facilitar a análise</li>
                    <li>Mantenha logs por um período adequado para auditoria e diagnóstico</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo de Logging Estruturado</h4>
                <pre>
// Em app/Providers/AppServiceProvider.php
use Illuminate\Support\Facades\Log;
use Monolog\Formatter\JsonFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

public function boot(): void
{
    // Configurar logging estruturado em JSON
    Log::extend('json', function ($app, array $config) {
        $handler = new StreamHandler($config['path'], $config['level'] ?? Logger::DEBUG);
        $handler->setFormatter(new JsonFormatter());
        
        return new Logger(
            $config['name'] ?? $app->make('config')->get('app.name'),
            [$handler]
        );
    });
    
    // Adicionar informações padrão a todos os logs
    Log::withContext([
        'environment' => app()->environment(),
        'app_version' => config('app.version'),
        'php_version' => PHP_VERSION,
    ]);
}</pre>
            </div>

            <div class="code-block">
                <h4>Exemplo de Uso de Logs na Aplicação</h4>
                <pre>
// Em um controller ou serviço
use Illuminate\Support\Facades\Log;

// Log simples
Log::info('Usuário logado com sucesso');

// Log com contexto
Log::info('Pedido criado', [
    'order_id' => $order->id,
    'customer_id' => $order->customer_id,
    'total' => $order->total,
    'items_count' => $order->items->count(),
]);

// Log de erro com exceção
try {
    // Código que pode gerar exceção
} catch (\Exception $e) {
    Log::error('Erro ao processar pagamento', [
        'order_id' => $order->id,
        'exception' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
    ]);
    
    // Relançar ou tratar a exceção
}

// Log com diferentes níveis
Log::debug('Informação detalhada para debugging');
Log::info('Informação geral sobre eventos do sistema');
Log::notice('Eventos normais mas significativos');
Log::warning('Ocorrências excepcionais que não são erros');
Log::error('Erros de runtime que não requerem ação imediata');
Log::critical('Falhas críticas que requerem atenção imediata');
Log::alert('Ação deve ser tomada imediatamente');
Log::emergency('Sistema está inutilizável');</pre>
            </div>
        </section>

        <section id="monitoramento-alertas" class="subsection">
            <h3>8.3. Sistema de Alertas</h3>
            <p>Um sistema de alertas eficiente permite que a equipe seja notificada rapidamente sobre problemas que
                requerem atenção.</p>

            <div class="best-practice">
                <h4>Tipos de Alertas</h4>
                <ul>
                    <li><strong>Alertas de Disponibilidade:</strong> Quando a aplicação ou componentes críticos estão
                        inacessíveis</li>
                    <li><strong>Alertas de Performance:</strong> Quando métricas de desempenho ultrapassam limiares
                        definidos</li>
                    <li><strong>Alertas de Erro:</strong> Quando ocorrem erros críticos ou em grande volume</li>
                    <li><strong>Alertas de Segurança:</strong> Quando são detectadas tentativas de acesso não autorizado
                    </li>
                    <li><strong>Alertas de Capacidade:</strong> Quando recursos estão próximos de se esgotar</li>
                    <li><strong>Alertas de Negócio:</strong> Quando métricas de negócio apresentam anomalias</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configuração de Alertas com Prometheus e Alertmanager</h4>
                <pre>
# /etc/prometheus/rules/laravel_alerts.yml
groups:
  - name: laravel_alerts
    rules:
      - alert: HighErrorRate
        expr: sum(rate(app_request_errors_total[5m])) / sum(rate(app_requests_total[5m])) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 5% for the last 5 minutes ({{ $value | printf \"%.2f\" }})"

      - alert: SlowResponses
        expr: histogram_quantile(0.95, sum(rate(app_request_duration_seconds_bucket[5m])) by (le)) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow response times detected"
          description: "95th percentile of response time is above 2 seconds ({{ $value | printf \"%.2f\" }}s)"

      - alert: HighQueueSize
        expr: sum(app_queue_size) > 1000
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Queue size is high"
          description: "Queue size is above 1000 jobs for more than 15 minutes ({{ $value }} jobs)"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Server memory usage is above 90% for more than 5 minutes ({{ $value | printf \"%.2f\" }}%)"

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 90% for more than 5 minutes ({{ $value | printf \"%.2f\" }}%)"

      - alert: DiskSpaceLow
        expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} < 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space"
          description: "Server has less than 10% free disk space ({{ $value | printf \"%.2f\" }}%)"</pre>
            </div>

            <div class="code-block">
                <h4>Configuração do Alertmanager</h4>
                <pre>
# /etc/alertmanager/alertmanager.yml
global:
  resolve_timeout: 5m
  slack_api_url: 'https://hooks.slack.com/services/XXXXXXXXX/XXXXXXXXX/XXXXXXXXXXXXXXXXXXXXXXXX'
  smtp_smarthost: 'smtp.example.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: 'alertmanager'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname', 'severity']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'team-emails'
  routes:
  - match:
      severity: critical
    receiver: 'team-pager'
    continue: true
  - match:
      severity: warning
    receiver: 'team-slack'

receivers:
- name: 'team-emails'
  email_configs:
  - to: '<EMAIL>'
    send_resolved: true

- name: 'team-slack'
  slack_configs:
  - channel: '#alerts'
    send_resolved: true
    title: '{{ .GroupLabels.alertname }}'
    text: >
      {{ range .Alerts }}
        *Alert:* {{ .Annotations.summary }}
        *Description:* {{ .Annotations.description }}
        *Severity:* {{ .Labels.severity }}
        *Started:* {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      {{ end }}

- name: 'team-pager'
  pagerduty_configs:
  - service_key: 'your-pagerduty-service-key'
    send_resolved: true</pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Alertas</h4>
                <ul>
                    <li>Configure alertas acionáveis que requerem intervenção humana</li>
                    <li>Evite alertas excessivos que podem causar fadiga de alerta</li>
                    <li>Defina níveis de severidade claros (info, warning, critical)</li>
                    <li>Implemente roteamento de alertas para as equipes corretas</li>
                    <li>Inclua informações suficientes para diagnóstico inicial</li>
                    <li>Configure alertas para diferentes canais (email, Slack, SMS, PagerDuty)</li>
                    <li>Revise e ajuste regularmente os limiares de alerta</li>
                    <li>Mantenha um registro de alertas para análise de tendências</li>
                </ul>
            </div>
        </section>

        <section id="monitoramento-health" class="subsection">
            <h3>8.4. Health Checks</h3>
            <p>Health checks são verificações automatizadas que monitoram a saúde da aplicação e seus componentes.</p>

            <div class="code-block">
                <h4>Implementação de Health Checks no Laravel 12</h4>
                <pre>
// Instalação do pacote
// composer require spatie/laravel-health

// Em config/health.php
return [
    'result_stores' => [
        Spatie\Health\ResultStores\EloquentHealthResultStore::class,
        Spatie\Health\ResultStores\CacheHealthResultStore::class,
    ],

    'notifications' => [
        'enabled' => true,
        'notifications' => [
            Spatie\Health\Notifications\CheckFailedNotification::class => ['mail', 'slack'],
        ],
        'notifiable' => Spatie\Health\Notifications\Notifiable::class,
        'throttle_notifications_for_minutes' => 60,
        'throttle_notifications_key' => 'health:throttled',
    ],

    'checks' => [
        // Verificações do sistema
        Spatie\Health\Checks\UsedDiskSpaceCheck::class => [
            'warning_threshold_percentage' => 70,
            'error_threshold_percentage' => 90,
        ],
        Spatie\Health\Checks\DatabaseCheck::class,
        Spatie\Health\Checks\CacheCheck::class,
        Spatie\Health\Checks\DebugModeCheck::class,
        Spatie\Health\Checks\EnvironmentCheck::class,
        Spatie\Health\Checks\OptimizedAppCheck::class,
        
        // Verificações de serviços externos
        Spatie\Health\Checks\RedisCheck::class,
        Spatie\Health\Checks\HorizonCheck::class,
        Spatie\Health\Checks\ScheduleCheck::class => [
            'signature' => Illuminate\Console\Scheduling\Schedule::class,
        ],
        
        // Verificações personalizadas
        App\Health\Checks\ExternalApiCheck::class => [
            'timeout_in_seconds' => 5,
            'services' => [
                'payment_gateway' => [
                    'url' => 'https://api.payment.example.com/health',
                    'method' => 'GET',
                ],
                'shipping_api' => [
                    'url' => 'https://api.shipping.example.com/status',
                    'method' => 'GET',
                ],
            ],
        ],
        App\Health\Checks\QueueCheck::class => [
            'max_queue_size' => 1000,
        ],
    ],
];</pre>
            </div>

            <div class="code-block">
                <h4>Implementação de Health Check Personalizado</h4>
                <pre>
// Em app/Health/Checks/ExternalApiCheck.php
namespace App\Health\Checks;

use Illuminate\Support\Facades\Http;
use Spatie\Health\Checks\Check;
use Spatie\Health\Checks\Result;

class ExternalApiCheck extends Check
{
    protected int $timeoutInSeconds = 3;
    protected array $services = [];

    public function timeoutInSeconds(int $seconds): self
    {
        $this->timeoutInSeconds = $seconds;
        
        return $this;
    }
    
    public function services(array $services): self
    {
        $this->services = $services;
        
        return $this;
    }

    public function run(): Result
    {
        $result = Result::make();
        
        if (empty($this->services)) {
            return $result->warning('No services configured for API health check');
        }
        
        $failedServices = [];
        
        foreach ($this->services as $name => $config) {
            try {
                $response = Http::timeout($this->timeoutInSeconds)
                    ->withHeaders($config['headers'] ?? [])
                    ->{strtolower($config['method'] ?? 'get')}($config['url']);
                
                if (!$response->successful()) {
                    $failedServices[] = "{$name} returned status {$response->status()}";
                }
            } catch (\Exception $exception) {
                $failedServices[] = "{$name} failed: {$exception->getMessage()}";
            }
        }
        
        if (empty($failedServices)) {
            return $result->ok('All external APIs are responding correctly');
        }
        
        return $result->failed('Some external APIs are not responding correctly: ' . implode(', ', $failedServices));
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Endpoint de Health Check</h4>
                <pre>
// Em routes/api.php
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toIso8601String(),
        'version' => config('app.version'),
        'environment' => app()->environment(),
    ]);
});

// Endpoint detalhado (protegido)
Route::get('/health/details', function () {
    // Verificar se a requisição é autorizada
    if (!request()->hasHeader('X-Health-Check-Token') || 
        request()->header('X-Health-Check-Token') !== config('app.health_check_token')) {
        return response()->json(['error' => 'Unauthorized'], 401);
    }
    
    // Executar verificações de saúde
    $healthCheckResults = app(\Spatie\Health\Health::class)->checkAll();
    
    return response()->json([
        'status' => $healthCheckResults->allChecksOk() ? 'ok' : 'failing',
        'results' => $healthCheckResults->toArray(),
        'timestamp' => now()->toIso8601String(),
        'version' => config('app.version'),
        'environment' => app()->environment(),
        'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . 'MB',
        'database' => [
            'connections' => DB::getConnections(),
            'slow_queries' => DB::getQueryLog(),
        ],
        'cache' => [
            'driver' => config('cache.default'),
            'status' => Cache::get('health_check_test', function () {
                Cache::put('health_check_test', true, 60);
                return 'created';
            }) === true ? 'ok' : 'failing',
        ],
    ]);
});</pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Health Checks</h4>
                <ul>
                    <li>Implemente verificações para todos os componentes críticos</li>
                    <li>Crie um endpoint público simples para verificações básicas</li>
                    <li>Proteja endpoints detalhados com autenticação</li>
                    <li>Configure verificações periódicas automatizadas</li>
                    <li>Integre health checks com sistemas de monitoramento</li>
                    <li>Defina timeouts adequados para evitar bloqueios</li>
                    <li>Inclua verificações de dependências externas</li>
                    <li>Implemente verificações de integridade do banco de dados</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="seguranca" class="manual-section">
        <h2>9. Segurança</h2>
        <p>A segurança é um aspecto crítico em qualquer aplicação Laravel 12, especialmente em ambientes de produção.
        </p>

        <section id="seguranca-configuracao" class="subsection">
            <h3>9.1. Configurações de Segurança</h3>
            <p>Configurações adequadas de segurança são essenciais para proteger a aplicação contra ameaças comuns.</p>

            <div class="best-practice">
                <h4>Configurações Essenciais de Segurança</h4>
                <ul>
                    <li>Mantenha o Laravel e todas as dependências atualizadas</li>
                    <li>Configure HTTPS em todos os ambientes</li>
                    <li>Utilize variáveis de ambiente para armazenar informações sensíveis</li>
                    <li>Configure cabeçalhos de segurança HTTP</li>
                    <li>Implemente proteção CSRF em todos os formulários</li>
                    <li>Configure políticas de segurança de conteúdo (CSP)</li>
                    <li>Desabilite funções perigosas do PHP</li>
                    <li>Implemente rate limiting em APIs e formulários de autenticação</li>
                    <li>Utilize autenticação de dois fatores (2FA) para acesso administrativo</li>
                    <li>Configure permissões de arquivo adequadas</li>
                    <li>Implemente validação de entrada rigorosa</li>
                    <li>Utilize prepared statements para consultas SQL</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configuração de PHP para Produção</h4>
                <pre>
; /etc/php/8.2/fpm/php.ini

; Ocultar informações de versão
expose_php = Off

; Limitar funções perigosas
disable_functions = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source,phpinfo

; Configurações de upload
file_uploads = On
upload_max_filesize = 10M
max_file_uploads = 20
post_max_size = 12M

; Configurações de sessão
session.use_strict_mode = On
session.use_only_cookies = On
session.cookie_secure = On
session.cookie_httponly = On
session.cookie_samesite = "Lax"
session.gc_maxlifetime = 7200
session.sid_length = 48
session.sid_bits_per_character = 6

; Configurações de erro
display_errors = Off
display_startup_errors = Off
log_errors = On
error_reporting = E_ALL
error_log = /var/log/php/error.log

; Configurações de segurança
allow_url_fopen = Off
allow_url_include = Off
variables_order = "GPCS"
memory_limit = 256M
max_execution_time = 60
max_input_time = 60
max_input_vars = 2000
</pre>
            </div>

            <div class="code-block">
                <h4>Configuração de Permissões de Arquivos</h4>
                <pre>
#!/bin/bash

# Script para configurar permissões adequadas em uma aplicação Laravel

APP_DIR="/var/www/production/current"
WEB_USER="www-data"
WEB_GROUP="www-data"

# Definir proprietário dos arquivos
chown -R $WEB_USER:$WEB_GROUP $APP_DIR

# Configurar permissões básicas
find $APP_DIR -type f -exec chmod 644 {} \;
find $APP_DIR -type d -exec chmod 755 {} \;

# Permissões especiais para diretórios que precisam de escrita
chmod -R 775 $APP_DIR/storage
chmod -R 775 $APP_DIR/bootstrap/cache

# Permissões para arquivos executáveis
chmod +x $APP_DIR/artisan

# Permissões para arquivos sensíveis
chmod 600 $APP_DIR/.env

echo "Permissões configuradas com sucesso!"
</pre>
            </div>

            <div class="best-practice">
                <h4>Verificações de Segurança Regulares</h4>
                <ul>
                    <li>Execute verificações de vulnerabilidades com ferramentas como Composer audit e npm audit</li>
                    <li>Realize testes de penetração periódicos</li>
                    <li>Monitore alertas de segurança do Laravel e pacotes utilizados</li>
                    <li>Verifique logs em busca de atividades suspeitas</li>
                    <li>Implemente ferramentas de detecção de intrusão</li>
                    <li>Mantenha um plano de resposta a incidentes atualizado</li>
                    <li>Realize auditorias de código com foco em segurança</li>
                </ul>
            </div>
        </section>

        <section id="seguranca-autenticacao" class="subsection">
            <h3>9.2. Autenticação e Autorização</h3>
            <p>A implementação adequada de autenticação e autorização é fundamental para proteger o acesso aos recursos
                da aplicação.</p>

            <div class="best-practice">
                <h4>Boas Práticas para Autenticação</h4>
                <ul>
                    <li>Utilize o sistema de autenticação nativo do Laravel</li>
                    <li>Implemente políticas de senha fortes (comprimento, complexidade, expiração)</li>
                    <li>Utilize autenticação de dois fatores (2FA) para contas sensíveis</li>
                    <li>Implemente bloqueio de conta após múltiplas tentativas falhas</li>
                    <li>Utilize tokens de acesso com tempo de expiração para APIs</li>
                    <li>Armazene senhas utilizando algoritmos de hash seguros (Bcrypt/Argon2)</li>
                    <li>Implemente logout automático após período de inatividade</li>
                    <li>Registre e monitore tentativas de login suspeitas</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configuração de Autenticação Segura</h4>
                <pre>
// Em config/auth.php
return [
    'defaults' => [
        'guard' => 'web',
        'passwords' => 'users',
    ],

    'guards' => [
        'web' => [
            'driver' => 'session',
            'provider' => 'users',
        ],

        'api' => [
            'driver' => 'sanctum',
            'provider' => 'users',
        ],

        'admin' => [
            'driver' => 'session',
            'provider' => 'admins',
        ],
    ],

    'providers' => [
        'users' => [
            'driver' => 'eloquent',
            'model' => App\Models\User::class,
        ],

        'admins' => [
            'driver' => 'eloquent',
            'model' => App\Models\Admin::class,
        ],
    ],

    'passwords' => [
        'users' => [
            'provider' => 'users',
            'table' => 'password_reset_tokens',
            'expire' => 60, // 60 minutos
            'throttle' => 60, // 1 tentativa por minuto
        ],
        
        'admins' => [
            'provider' => 'admins',
            'table' => 'admin_password_reset_tokens',
            'expire' => 30, // 30 minutos
            'throttle' => 60, // 1 tentativa por minuto
        ],
    ],

    'password_timeout' => 10800, // 3 horas
];
</pre>
            </div>

            <div class="code-block">
                <h4>Implementação de 2FA</h4>
                <pre>
// Instalação do pacote
// composer require bacon/bacon-qr-code pragmarx/google2fa-laravel

// Em app/Models/User.php
use Illuminate\Database\Eloquent\Casts\Attribute;

class User extends Authenticatable
{
    // ...
    
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];
    
    protected $casts = [
        'email_verified_at' => 'datetime',
        'two_factor_enabled' => 'boolean',
        'two_factor_recovery_codes' => 'array',
    ];
    
    // Método para gerar o segredo 2FA
    public function generateTwoFactorSecret(): void
    {
        $this->two_factor_secret = app('pragmarx.google2fa')->generateSecretKey();
        $this->two_factor_enabled = false;
        $this->save();
    }
    
    // Método para ativar 2FA
    public function enableTwoFactorAuth(): void
    {
        $this->two_factor_enabled = true;
        $this->two_factor_recovery_codes = $this->generateRecoveryCodes();
        $this->save();
    }
    
    // Método para desativar 2FA
    public function disableTwoFactorAuth(): void
    {
        $this->two_factor_enabled = false;
        $this->two_factor_secret = null;
        $this->two_factor_recovery_codes = null;
        $this->save();
    }
    
    // Método para gerar códigos de recuperação
    protected function generateRecoveryCodes(): array
    {
        $codes = [];
        
        for ($i = 0; $i < 8; $i++) {
            $codes[] = Str::random(10);
        }
        
        return $codes;
    }
    
    // Método para verificar código 2FA
    public function validateTwoFactorCode(string $code): bool
    {
        return app('pragmarx.google2fa')->verifyKey(
            $this->two_factor_secret,
            $code
        );
    }
}

// Em app/Http/Middleware/RequireTwoFactor.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RequireTwoFactor
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        if ($user && $user->two_factor_enabled && !session('two_factor_verified')) {
            return redirect()->route('two-factor.challenge');
        }
        
        return $next($request);
    }
}
</pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Autorização</h4>
                <ul>
                    <li>Utilize o sistema de Gates e Policies do Laravel</li>
                    <li>Implemente controle de acesso baseado em funções (RBAC)</li>
                    <li>Aplique o princípio do menor privilégio</li>
                    <li>Verifique permissões em múltiplas camadas (routes, controllers, views)</li>
                    <li>Registre e audite alterações em permissões</li>
                    <li>Teste exaustivamente as regras de autorização</li>
                    <li>Implemente verificações de autorização em APIs</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Implementação de RBAC</h4>
                <pre>
// Em database/migrations/create_roles_and_permissions_tables.php
Schema::create('roles', function (Blueprint $table) {
    $table->id();
    $table->string('name')->unique();
    $table->string('description')->nullable();
    $table->timestamps();
});

Schema::create('permissions', function (Blueprint $table) {
    $table->id();
    $table->string('name')->unique();
    $table->string('description')->nullable();
    $table->timestamps();
});

Schema::create('role_permission', function (Blueprint $table) {
    $table->foreignId('role_id')->constrained()->onDelete('cascade');
    $table->foreignId('permission_id')->constrained()->onDelete('cascade');
    $table->primary(['role_id', 'permission_id']);
});

Schema::create('role_user', function (Blueprint $table) {
    $table->foreignId('role_id')->constrained()->onDelete('cascade');
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->primary(['role_id', 'user_id']);
});

// Em app/Models/User.php
public function roles()
{
    return $this->belongsToMany(Role::class);
}

public function hasRole($role)
{
    if (is_string($role)) {
        return $this->roles->contains('name', $role);
    }
    
    return (bool) $role->intersect($this->roles)->count();
}

public function hasPermission($permission)
{
    return $this->roles->flatMap->permissions->contains('name', $permission);
}

// Em app/Providers/AuthServiceProvider.php
public function boot(): void
{
    // Definir gate para verificar permissões
    Gate::define('permission', function (User $user, $permission) {
        return $user->hasPermission($permission);
    });
    
    // Definir gates para recursos específicos
    Gate::define('view-dashboard', function (User $user) {
        return $user->hasPermission('view-dashboard');
    });
    
    Gate::define('manage-users', function (User $user) {
        return $user->hasPermission('manage-users');
    });
    
    // Registrar policies
    Gate::policy(Post::class, PostPolicy::class);
    Gate::policy(User::class, UserPolicy::class);
}
</pre>
            </div>
        </section>

        <section id="seguranca-api" class="subsection">
            <h3>9.3. Segurança de API</h3>
            <p>APIs requerem considerações especiais de segurança para proteger dados e funcionalidades expostas.</p>

            <div class="best-practice">
                <h4>Boas Práticas para APIs</h4>
                <ul>
                    <li>Utilize Laravel Sanctum ou Passport para autenticação de API</li>
                    <li>Implemente tokens com escopo limitado e expiração</li>
                    <li>Utilize HTTPS para todas as comunicações de API</li>
                    <li>Implemente rate limiting para prevenir abusos</li>
                    <li>Valide rigorosamente todas as entradas</li>
                    <li>Retorne códigos de status HTTP apropriados</li>
                    <li>Implemente logging de todas as chamadas de API</li>
                    <li>Considere o uso de JWT para APIs stateless</li>
                    <li>Implemente CORS corretamente</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configuração do Laravel Sanctum</h4>
                <pre>
// Instalação
// composer require laravel/sanctum

// Em config/sanctum.php
return [
    'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
        '%s%s',
        'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
        env('APP_URL') ? ','.parse_url(env('APP_URL'), PHP_URL_HOST) : ''
    ))),

    'guard' => ['web'],

    'expiration' => 60 * 24, // 24 horas

    'middleware' => [
        'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
        'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
    ],

    'prefix' => 'api/v1',
];

// Em app/Http/Kernel.php
protected $middlewareGroups = [
    // ...
    'api' => [
        \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
        \Illuminate\Routing\Middleware\ThrottleRequests::class.':api',
        \Illuminate\Routing\Middleware\SubstituteBindings::class,
    ],
];

// Em routes/api.php
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    
    Route::apiResource('posts', PostController::class);
    
    // Rotas que requerem permissões específicas
    Route::middleware('can:manage-users')->group(function () {
        Route::apiResource('users', UserController::class);
    });
});

// Criação de token com habilidades específicas
Route::post('/tokens/create', function (Request $request) {
    $token = $request->user()->createToken('api-token', ['read', 'create']);
    
    return ['token' => $token->plainTextToken];
});
</pre>
            </div>

            <div class="code-block">
                <h4>Implementação de Rate Limiting</h4>
                <pre>
// Em app/Providers/RouteServiceProvider.php
protected function configureRateLimiting(): void
{
    // Rate limiting para API geral
    RateLimiter::for('api', function (Request $request) {
        // Limite mais alto para usuários autenticados
        if ($request->user()) {
            return Limit::perMinute(60)->by($request->user()->id);
        }
        
        // Limite mais baixo para IPs não autenticados
        return Limit::perMinute(20)->by($request->ip());
    });
    
    // Rate limiting específico para endpoints sensíveis
    RateLimiter::for('api-sensitive', function (Request $request) {
        return Limit::perMinute(5)->by($request->user()?->id ?: $request->ip());
    });
    
    // Rate limiting para autenticação
    RateLimiter::for('auth', function (Request $request) {
        return Limit::perMinute(5)->by($request->ip());
    });
}

// Em routes/api.php
Route::middleware(['throttle:api-sensitive'])->group(function () {
    Route::post('/payments', [PaymentController::class, 'process']);
    Route::post('/password/reset', [PasswordResetController::class, 'reset']);
});
</pre>
            </div>

            <div class="code-block">
                <h4>Configuração de CORS</h4>
                <pre>
// Em config/cors.php
return [
    'paths' => ['api/*', 'sanctum/csrf-cookie'],
    
    'allowed_methods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    
    'allowed_origins' => [env('FRONTEND_URL', 'https://example.com')],
    
    'allowed_origins_patterns' => [],
    
    'allowed_headers' => ['Content-Type', 'X-Requested-With', 'Authorization'],
    
    'exposed_headers' => [],
    
    'max_age' => 60 * 60 * 24, // 24 horas
    
    'supports_credentials' => true,
];
</pre>
            </div>
        </section>

        <section id="seguranca-dados" class="subsection">
            <h3>9.4. Proteção de Dados</h3>
            <p>A proteção adequada dos dados é essencial para garantir a privacidade dos usuários e a conformidade com
                regulamentações.</p>

            <div class="best-practice">
                <h4>Boas Práticas para Proteção de Dados</h4>
                <ul>
                    <li>Utilize criptografia para dados sensíveis em repouso</li>
                    <li>Implemente mascaramento de dados sensíveis em logs</li>
                    <li>Utilize HTTPS para todas as comunicações</li>
                    <li>Implemente políticas de retenção de dados</li>
                    <li>Realize backups criptografados</li>
                    <li>Implemente controles de acesso granulares</li>
                    <li>Considere a pseudonimização de dados pessoais</li>
                    <li>Documente o fluxo de dados para conformidade com LGPD/GDPR</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Criptografia de Dados Sensíveis</h4>
                <pre>
// Em app/Models/User.php
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Crypt;

class User extends Authenticatable
{
    // ...
    
    protected function socialSecurityNumber(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? Crypt::decryptString($value) : null,
            set: fn ($value) => $value ? Crypt::encryptString($value) : null,
        );
    }
    
    protected function phoneNumber(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? Crypt::decryptString($value) : null,
            set: fn ($value) => $value ? Crypt::encryptString($value) : null,
        );
    }
    
    // Método para mascarar dados sensíveis em logs
    public function toArray()
    {
        $array = parent::toArray();
        
        // Mascarar dados sensíveis
        if (isset($array['email'])) {
            $array['email'] = $this->maskEmail($array['email']);
        }
        
        if (isset($array['social_security_number'])) {
            $array['social_security_number'] = '***-**-' . substr($array['social_security_number'], -4);
        }
        
        if (isset($array['phone_number'])) {
            $array['phone_number'] = '(***) ***-' . substr($array['phone_number'], -4);
        }
        
        return $array;
    }
    
    protected function maskEmail($email)
    {
        if (!$email) return null;
        
        $parts = explode('@', $email);
        if (count($parts) !== 2) return $email;
        
        $name = $parts[0];
        $domain = $parts[1];
        
        $maskedName = substr($name, 0, 2) . str_repeat('*', max(strlen($name) - 2, 0));
        
        return $maskedName . '@' . $domain;
    }
}
</pre>
            </div>

            <div class="code-block">
                <h4>Configuração de Backup Seguro</h4>
                <pre>
// Instalação
// composer require spatie/laravel-backup

// Em config/backup.php
return [
    'backup' => [
        'name' => env('APP_NAME', 'laravel-backup'),
        
        'source' => [
            'files' => [
                'include' => [
                    base_path(),
                ],
                'exclude' => [
                    base_path('vendor'),
                    base_path('node_modules'),
                    storage_path('logs'),
                ],
                'follow_links' => false,
                'ignore_unreadable_directories' => true,
            ],
            
            'databases' => [
                'mysql',
            ],
        ],
        
        'database_dump_compressor' => \Spatie\DbDumper\Compressors\GzipCompressor::class,
        
        'destination' => [
            'filename_prefix' => 'backup-',
            'disks' => [
                'backups',
                's3',
            ],
        ],
        
        'temporary_directory' => storage_path('app/backup-temp'),
        
        'password' => env('BACKUP_ENCRYPTION_PASSWORD'),
    ],
    
    // ... outras configurações
];

// Em app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    // Backup diário às 01:00
    $schedule->command('backup:clean')->daily()->at('01:00');
    $schedule->command('backup:run')->daily()->at('01:30');
    
    // Backup semanal completo
    $schedule->command('backup:run --only-db')->weeklyOn(7, '00:00');
}
</pre>
            </div>

            <div class="best-practice">
                <h4>Conformidade com LGPD/GDPR</h4>
                <ul>
                    <li>Implemente mecanismos para consentimento explícito</li>
                    <li>Forneça funcionalidade para exportação de dados do usuário</li>
                    <li>Implemente o "direito ao esquecimento" (exclusão de dados)</li>
                    <li>Mantenha registros de processamento de dados</li>
                    <li>Implemente notificação de violação de dados</li>
                    <li>Realize avaliações de impacto na proteção de dados</li>
                    <li>Designe um responsável pela proteção de dados</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Implementação de Exportação de Dados</h4>
                <pre>
// Em app/Http/Controllers/UserDataController.php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

class UserDataController extends Controller
{
    public function export(Request $request)
    {
        $user = Auth::user();
        
        // Coletar dados do usuário
        $userData = [
            'profile' => $user->toArray(),
            'posts' => $user->posts()->get()->toArray(),
            'comments' => $user->comments()->get()->toArray(),
            'orders' => $user->orders()->with('items')->get()->toArray(),
        ];
        
        // Criar arquivo JSON
        $jsonFile = storage_path('app/temp/' . $user->id . '_data.json');
        file_put_contents($jsonFile, json_encode($userData, JSON_PRETTY_PRINT));
        
        // Criar ZIP com arquivos do usuário
        $zipFile = storage_path('app/temp/' . $user->id . '_export.zip');
        $zip = new ZipArchive();
        $zip->open($zipFile, ZipArchive::CREATE | ZipArchive::OVERWRITE);
        
        // Adicionar JSON ao ZIP
        $zip->addFile($jsonFile, 'user_data.json');
        
        // Adicionar arquivos do usuário (uploads, etc.)
        $userFiles = Storage::disk('user_uploads')->files('users/' . $user->id);
        foreach ($userFiles as $file) {
            $zip->addFile(
                Storage::disk('user_uploads')->path($file),
                'uploads/' . basename($file)
            );
        }
        
        $zip->close();
        
        // Limpar arquivo JSON temporário
        unlink($jsonFile);
        
        // Retornar ZIP para download
        return response()->download($zipFile, 'user_data_export.zip')->deleteFileAfterSend();
    }
    
    public function delete(Request $request)
    {
        $user = Auth::user();
        
        // Verificar senha para confirmar
        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors(['password' => 'Senha incorreta']);
        }
        
        // Registrar solicitação de exclusão
        $user->deletion_requested_at = now();
        $user->save();
        
        // Agendar exclusão após período de reflexão (30 dias)
        DeleteUserData::dispatch($user)->delay(now()->addDays(30));
        
        // Logout
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('home')
            ->with('status', 'Sua conta foi marcada para exclusão e será removida em 30 dias.');
    }
}
</pre>
            </div>
        </section>

        <section id="seguranca-auditoria" class="subsection">
            <h3>9.5. Auditoria e Logging de Segurança</h3>
            <p>A auditoria e o logging adequados são essenciais para detectar, investigar e responder a incidentes de
                segurança.</p>

            <div class="best-practice">
                <h4>Boas Práticas para Auditoria</h4>
                <ul>
                    <li>Registre todas as ações sensíveis (login, alteração de permissões, etc.)</li>
                    <li>Implemente logging de alterações em dados críticos</li>
                    <li>Mantenha logs de segurança separados dos logs de aplicação</li>
                    <li>Proteja os logs contra manipulação</li>
                    <li>Implemente retenção adequada de logs</li>
                    <li>Utilize ferramentas de análise de logs para detecção de anomalias</li>
                    <li>Registre informações contextuais suficientes (IP, user-agent, etc.)</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Implementação de Auditoria</h4>
                <pre>
// Instalação
// composer require owen-it/laravel-auditing

// Em config/audit.php
return [
    'enabled' => env('AUDITING_ENABLED', true),
    
    'implementation' => OwenIt\Auditing\Models\Audit::class,
    
    'user' => [
        'morph_prefix' => 'user',
        'guards' => [
            'web',
            'api',
        ],
    ],
    
    'resolver' => [
        'user' => OwenIt\Auditing\Resolvers\UserResolver::class,
        'ip_address' => OwenIt\Auditing\Resolvers\IpAddressResolver::class,
        'user_agent' => OwenIt\Auditing\Resolvers\UserAgentResolver::class,
        'url' => OwenIt\Auditing\Resolvers\UrlResolver::class,
    ],
    
    'events' => [
        'created',
        'updated',
        'deleted',
        'restored',
    ],
    
    'strict' => false,
    
    'exclude' => [
        // Campos a serem excluídos da auditoria
        'password',
        'remember_token',
        'two_factor_secret',
    ],
    
    'threshold' => 0,
    
    'driver' => 'database',
    
    'drivers' => [
        'database' => [
            'table' => 'audits',
            'connection' => null,
        ],
    ],
];

// Em app/Models/User.php
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class User extends Authenticatable implements Auditable
{
    use AuditableTrait;
    
    // Campos a serem auditados
    protected $auditInclude = [
        'name',
        'email',
        'role_id',
        'is_active',
        'last_login_at',
    ];
    
    // Eventos personalizados para auditoria
    public function login()
    {
        $this->last_login_at = now();
        $this->save();
        
        // Registrar evento de login
        Activity::create([
            'user_id' => $this->id,
            'type' => 'login',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Logging de Segurança Centralizado</h4>
                <pre>
// Em app/Providers/AppServiceProvider.php
use Illuminate\Support\Facades\Log;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\JsonFormatter;

public function boot(): void
{
    // Configurar canal de log específico para segurança
    Log::extend('security', function ($app, array $config) {
        $handler = new RotatingFileHandler(
            storage_path('logs/security.log'),
            $config['days'] ?? 30,
            $config['level'] ?? 'debug'
        );
        
        $handler->setFormatter(new JsonFormatter());
        
        return new \Monolog\Logger(
            'security',
            [$handler]
        );
    });
}

// Em config/logging.php
'channels' => [
    // ... outros canais
    
    'security' => [
        'driver' => 'security',
        'days' => 90,
        'level' => 'info',
    ],
],

// Uso do log de segurança
use Illuminate\Support\Facades\Log;

// Em um controller ou middleware
Log::channel('security')->info('Tentativa de acesso a recurso restrito', [
    'user_id' => $user->id ?? null,
    'resource' => $request->path(),
    'ip' => $request->ip(),
    'user_agent' => $request->userAgent(),
    'method' => $request->method(),
]);</pre>
            </div>

            <div class="code-block">
                <h4>Middleware para Auditoria de Acesso</h4>
                <pre>
// Em app/Http/Middleware/AuditAccess.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\AccessLog;
use Symfony\Component\HttpFoundation\Response;

class AuditAccess
{
    public function handle(Request $request, Closure $next): Response
    {
        // Processar a requisição
        $response = $next($request);
        
        // Registrar acesso após a requisição ser processada
        $this->logAccess($request, $response);
        
        return $response;
    }
    
    protected function logAccess(Request $request, Response $response): void
    {
        // Ignorar rotas específicas (assets, etc.)
        if ($this->shouldIgnore($request)) {
            return;
        }
        
        // Registrar acesso
        AccessLog::create([
            'user_id' => $request->user()?->id,
            'path' => $request->path(),
            'method' => $request->method(),
            'status_code' => $response->getStatusCode(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
            'query_string' => $this->sanitizeQueryString($request->getQueryString()),
            'duration_ms' => defined('LARAVEL_START') ? round((microtime(true) - LARAVEL_START) * 1000) : null,
        ]);
    }
    
    protected function shouldIgnore(Request $request): bool
    {
        // Ignorar rotas de assets, health checks, etc.
        $ignorePaths = [
            '_debugbar',
            'favicon.ico',
            'robots.txt',
            'health',
            'css',
            'js',
            'images',
            'fonts',
        ];
        
        foreach ($ignorePaths as $path) {
            if (str_starts_with($request->path(), $path)) {
                return true;
            }
        }
        
        return false;
    }
    
    protected function sanitizeQueryString(?string $queryString): ?string
    {
        if (!$queryString) {
            return null;
        }
        
        // Lista de parâmetros sensíveis a serem mascarados
        $sensitiveParams = ['password', 'token', 'key', 'secret', 'card', 'cvv'];
        
        parse_str($queryString, $params);
        
        foreach ($sensitiveParams as $param) {
            if (isset($params[$param])) {
                $params[$param] = '********';
            }
        }
        
        return http_build_query($params);
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Monitoramento de Segurança</h4>
                <ul>
                    <li>Implemente alertas para atividades suspeitas (múltiplas falhas de login, acessos incomuns)</li>
                    <li>Utilize ferramentas de SIEM (Security Information and Event Management) para análise
                        centralizada</li>
                    <li>Configure dashboards para visualização de eventos de segurança</li>
                    <li>Realize análises periódicas de logs em busca de padrões suspeitos</li>
                    <li>Integre logs de segurança com sistemas de monitoramento</li>
                    <li>Implemente detecção de anomalias baseada em comportamento do usuário</li>
                </ul>
            </div>
        </section>

        <section id="seguranca-vulnerabilidades" class="subsection">
            <h3>9.6. Gerenciamento de Vulnerabilidades</h3>
            <p>O gerenciamento eficaz de vulnerabilidades é essencial para manter a aplicação Laravel 12 segura ao longo
                do tempo.</p>

            <div class="best-practice">
                <h4>Processo de Gerenciamento de Vulnerabilidades</h4>
                <ul>
                    <li>Realize verificações regulares de dependências com ferramentas automatizadas</li>
                    <li>Mantenha-se atualizado sobre boletins de segurança do Laravel e pacotes utilizados</li>
                    <li>Implemente um processo de atualização rápida para correções de segurança</li>
                    <li>Realize testes de penetração periódicos</li>
                    <li>Mantenha um programa de bug bounty ou divulgação responsável de vulnerabilidades</li>
                    <li>Documente e classifique vulnerabilidades encontradas</li>
                    <li>Priorize correções com base no risco e impacto</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Verificação Automatizada de Vulnerabilidades</h4>
                <pre>
#!/bin/bash

# Script para verificação de vulnerabilidades

echo "Iniciando verificação de vulnerabilidades..."

# Verificar dependências PHP
echo "Verificando dependências PHP..."
composer audit

# Verificar dependências JavaScript
echo "Verificando dependências JavaScript..."
npm audit

# Verificar vulnerabilidades de código com Enlightn
echo "Executando análise de código com Enlightn..."
php artisan enlightn

# Verificar vulnerabilidades conhecidas no Laravel
echo "Verificando vulnerabilidades conhecidas no Laravel..."
php artisan security:check

# Gerar relatório
echo "Gerando relatório de vulnerabilidades..."
php artisan security:report --output=security-report-$(date +%Y-%m-%d).html

echo "Verificação concluída!"</pre>
            </div>

            <div class="code-block">
                <h4>Integração com GitHub Actions para Verificação de Segurança</h4>
                <pre>
# .github/workflows/security-scan.yml
name: Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 0 * * 0'  # Executa todo domingo à meia-noite

jobs:
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, intl, bcmath, pdo_mysql
      
      - name: Install dependencies
        run: composer install --prefer-dist --no-progress
      
      - name: PHP Security Checker
        uses: symfonycorp/security-checker-action@v4
      
      - name: Composer Audit
        run: composer audit
      
      - name: NPM Audit
        run: npm audit --audit-level=moderate
      
      - name: Run Enlightn Security Checks
        run: |
          php artisan enlightn --details --ci --skip-env-specific
          php artisan enlightn:export --format=json --output=enlightn-report.json
      
      - name: Upload Security Report
        uses: actions/upload-artifact@v3
        with:
          name: security-reports
          path: |
            enlightn-report.json
            npm-audit.json
          retention-days: 7</pre>
            </div>

            <div class="best-practice">
                <h4>Resposta a Incidentes de Segurança</h4>
                <p>Tenha um plano de resposta a incidentes documentado que inclua:</p>
                <ul>
                    <li>Procedimentos para identificação e contenção de incidentes</li>
                    <li>Processo de comunicação interna e externa</li>
                    <li>Procedimentos de investigação e análise forense</li>
                    <li>Processo de recuperação e restauração de sistemas</li>
                    <li>Documentação e análise pós-incidente</li>
                    <li>Atualizações de segurança com base nas lições aprendidas</li>
                </ul>
            </div>
        </section>

        <section id="seguranca-headers" class="subsection">
            <h3>9.7. Cabeçalhos de Segurança HTTP</h3>
            <p>Os cabeçalhos de segurança HTTP são uma camada adicional de proteção que ajuda a mitigar vários tipos de
                ataques.</p>

            <div class="code-block">
                <h4>Middleware para Cabeçalhos de Segurança</h4>
                <pre>
// Em app/Http/Middleware/SecurityHeaders.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeaders
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        // Proteção contra clickjacking
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        
        // Proteção contra MIME-type sniffing
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        
        // Proteção contra XSS
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        
        // Política de referência
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        // Permissões
        $response->headers->set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), interest-cohort=()');
        
        // Content Security Policy
        $cspDirectives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://www.google-analytics.com",
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com",
            "img-src 'self' data: https://www.google-analytics.com",
            "font-src 'self' https://fonts.gstatic.com",
            "connect-src 'self' https://www.google-analytics.com",
            "frame-src 'self'",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'",
        ];
        
        // Apenas em produção, usar CSP em modo de relatório em desenvolvimento
        if (app()->environment('production')) {
            $response->headers->set('Content-Security-Policy', implode('; ', $cspDirectives));
        } else {
            $response->headers->set('Content-Security-Policy-Report-Only', implode('; ', $cspDirectives));
        }
        
        // HSTS (HTTP Strict Transport Security)
        if (app()->environment('production')) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }
        
        return $response;
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Registro do Middleware</h4>
                <pre>
// Em app/Http/Kernel.php
protected $middleware = [
    // ... outros middlewares
    \App\Http\Middleware\SecurityHeaders::class,
];</pre>
            </div>

            <div class="best-practice">
                <h4>Cabeçalhos de Segurança Essenciais</h4>
                <ul>
                    <li><strong>Content-Security-Policy (CSP):</strong> Controla quais recursos o navegador pode
                        carregar</li>
                    <li><strong>X-Frame-Options:</strong> Previne clickjacking controlando se a página pode ser
                        carregada em um iframe</li>
                    <li><strong>X-Content-Type-Options:</strong> Previne MIME-type sniffing</li>
                    <li><strong>X-XSS-Protection:</strong> Ativa filtros de XSS no navegador</li>
                    <li><strong>Strict-Transport-Security (HSTS):</strong> Força conexões HTTPS</li>
                    <li><strong>Referrer-Policy:</strong> Controla quais informações de referência são enviadas</li>
                    <li><strong>Permissions-Policy:</strong> Controla quais recursos o navegador pode usar</li>
                </ul>
            </div>

            <div class="note">
                <p><strong>Nota:</strong> Teste cuidadosamente os cabeçalhos de segurança antes de implementá-los em
                    produção, pois configurações muito restritivas podem quebrar funcionalidades da aplicação.</p>
            </div>
        </section>
    </section>

    <section id="troubleshooting" class="manual-section">
        <h2>10. Troubleshooting</h2>
        <p>Esta seção fornece orientações para identificar e resolver problemas comuns durante o deployment e operação
            de aplicações Laravel 12.</p>

        <section id="troubleshooting-diagnostico" class="subsection">
            <h3>10.1. Ferramentas de Diagnóstico</h3>
            <p>O Laravel 12 oferece diversas ferramentas para diagnosticar problemas em diferentes ambientes.</p>

            <div class="best-practice">
                <h4>Ferramentas de Diagnóstico Essenciais</h4>
                <ul>
                    <li><strong>Laravel Telescope:</strong> Monitoramento e debugging de requisições, queries, jobs,
                        etc.</li>
                    <li><strong>Laravel Debugbar:</strong> Barra de debug para ambiente de desenvolvimento</li>
                    <li><strong>Laravel Ignition:</strong> Página de erro avançada com sugestões de correção</li>
                    <li><strong>Laravel Horizon:</strong> Dashboard para monitoramento de filas Redis</li>
                    <li><strong>Laravel Pulse:</strong> Monitoramento de performance em tempo real</li>
                    <li><strong>Comandos artisan:</strong> Diversos comandos para diagnóstico e manutenção</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Comandos de Diagnóstico</h4>
                <pre>
# Verificar status da aplicação
php artisan about

# Listar rotas
php artisan route:list

# Verificar configuração de cache
php artisan config:show

# Verificar conexão com banco de dados
php artisan db:show

# Verificar jobs na fila
php artisan queue:monitor

# Verificar status dos serviços
php artisan health:check

# Limpar caches
php artisan optimize:clear

# Verificar problemas de segurança
php artisan security:check

# Verificar problemas de performance
php artisan optimize:check</pre>
            </div>

            <div class="code-block">
                <h4>Implementação de Comando de Diagnóstico Personalizado</h4>
                <pre>
// Em app/Console/Commands/DiagnoseSystem.php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class DiagnoseSystem extends Command
{
    protected $signature = 'app:diagnose {--verbose : Mostrar informações detalhadas}';
    protected $description = 'Executa diagnóstico completo do sistema';

    public function handle(): void
    {
        $this->info('Iniciando diagnóstico do sistema...');
        
        // Verificar ambiente
        $this->info('Ambiente: ' . app()->environment());
        
        // Verificar versões
        $this->checkVersions();
        
        // Verificar banco de dados
        $this->checkDatabase();
        
        // Verificar Redis
        $this->checkRedis();
        
        // Verificar sistema de arquivos
        $this->checkFilesystem();
        
        // Verificar cache
        $this->checkCache();
        
        // Verificar filas
        $this->checkQueues();
        
        // Verificar sessões
        $this->checkSessions();
        
        // Verificar permissões
        $this->checkPermissions();
        
        $this->info('Diagnóstico concluído!');
    }
    
    protected function checkVersions(): void
    {
        $this->comment('Verificando versões...');
        $this->line('PHP: ' . PHP_VERSION);
        $this->line('Laravel: ' . app()->version());
        $this->line('Composer: ' . shell_exec('composer --version'));
        $this->line('Node: ' . trim(shell_exec('node --version')));
        $this->line('NPM: ' . trim(shell_exec('npm --version')));
    }
    
    protected function checkDatabase(): void
    {
        $this->comment('Verificando banco de dados...');
        
        try {
            // Testar conexão
            DB::connection()->getPdo();
            $this->line('Conexão: ' . DB::connection()->getName() . ' ✓');
            
            // Verificar versão
            $version = DB::select('SELECT version()')[0]->{'version()'};
            $this->line('Versão: ' . $version);
            
            // Verificar migrações
            $migrations = DB::table('migrations')->count();
            $this->line('Migrações aplicadas: ' . $migrations);
            
            // Verificar tabelas
            if ($this->option('verbose')) {
                $tables = DB::select('SHOW TABLES');
                $tableCount = count($tables);
                $this->line('Total de tabelas: ' . $tableCount);
                
                // Verificar tamanho do banco
                $dbSize = DB::select('SELECT SUM(data_length + index_length) / 1024 / 1024 AS size_mb FROM information_schema.TABLES WHERE table_schema = ?', [DB::connection()->getDatabaseName()]);
                $this->line('Tamanho do banco: ' . round($dbSize[0]->size_mb, 2) . ' MB');
            }
        } catch (\Exception $e) {
            $this->error('Erro na conexão com banco de dados: ' . $e->getMessage());
        }
    }
    
    protected function checkRedis(): void
    {
        $this->comment('Verificando Redis...');
        
        try {
            $ping = Redis::connection()->ping();
            $this->line('Conexão: ' . ($ping ? '✓' : '✗'));
            
            if ($this->option('verbose')) {
                $info = Redis::connection()->info();
                $this->line('Versão: ' . $info['redis_version']);
                $this->line('Memória usada: ' . $this->formatBytes($info['used_memory']));
                $this->line('Clientes conectados: ' . $info['connected_clients']);
                $this->line('Tempo de atividade: ' . $this->formatUptime($info['uptime_in_seconds']));
            }
        } catch (\Exception $e) {
            $this->error('Erro na conexão com Redis: ' . $e->getMessage());
        }
    }
    
    protected function checkFilesystem(): void
    {
        $this->comment('Verificando sistema de arquivos...');
        
        // Verificar discos configurados
        $disks = config('filesystems.disks');
        $this->line('Discos configurados: ' . implode(', ', array_keys($disks)));
        
        // Verificar permissões de diretórios críticos
        $this->checkDirectoryPermissions(storage_path(), 'storage');
        $this->checkDirectoryPermissions(storage_path('app'), 'storage/app');
        $this->checkDirectoryPermissions(storage_path('logs'), 'storage/logs');
        $this->checkDirectoryPermissions(storage_path('framework'), 'storage/framework');
        $this->checkDirectoryPermissions(base_path('bootstrap/cache'), 'bootstrap/cache');
        
        // Verificar espaço em disco
        $this->checkDiskSpace();
    }
    
    protected function checkDirectoryPermissions(string $path, string $name): void
    {
        if (is_dir($path)) {
            $writable = is_writable($path);
            $this->line($name . ' é gravável: ' . ($writable ? '✓' : '✗'));
            
            if (!$writable) {
                $this->warn('O diretório ' . $name . ' não tem permissão de escrita!');
            }
        } else {
            $this->error('Diretório ' . $name . ' não existe!');
        }
    }
    
    protected function checkDiskSpace(): void
    {
        $totalSpace = disk_total_space(base_path());
        $freeSpace = disk_free_space(base_path());
        $usedSpace = $totalSpace - $freeSpace;
        $usedPercent = round(($usedSpace / $totalSpace) * 100, 2);
        
        $this->line('Espaço total: ' . $this->formatBytes($totalSpace));
        $this->line('Espaço livre: ' . $this->formatBytes($freeSpace));
        $this->line('Espaço usado: ' . $this->formatBytes($usedSpace) . ' (' . $usedPercent . '%)');
        
        if ($usedPercent > 90) {
            $this->error('Alerta: Espaço em disco está quase cheio!');
        } elseif ($usedPercent > 80) {
            $this->warn('Atenção: Espaço em disco está ficando baixo.');
        }
    }
    
    protected function checkCache(): void
    {
        $this->comment('Verificando cache...');
        
        $driver = config('cache.default');
        $this->line('Driver: ' . $driver);
        
        try {
            $testKey = 'diagnose_test_' . time();
            Cache::put($testKey, true, 60);
            $testResult = Cache::get($testKey);
            Cache::forget($testKey);
            
            $this->line('Teste de cache: ' . ($testResult === true ? '✓' : '✗'));
        } catch (\Exception $e) {
            $this->error('Erro no teste de cache: ' . $e->getMessage());
        }
    }
    
    protected function checkQueues(): void
    {
        $this->comment('Verificando filas...');
        
        $driver = config('queue.default');
        $this->line('Driver: ' . $driver);
        
        if ($driver === 'redis') {
            try {
                $queues = ['default', 'high', 'low', 'emails'];
                
                foreach ($queues as $queue) {
                    $size = Redis::connection('queues')->command('LLEN', ["queues:{$queue}"]);
                    $this->line("Fila '{$queue}': {$size} jobs");
                    
                    if ($size > 1000) {
                        $this->warn("Atenção: Fila '{$queue}' tem muitos jobs pendentes!");
                    }
                }
                
                // Verificar jobs com falha
                $failedCount = DB::table('failed_jobs')->count();
                $this->line('Jobs com falha: ' . $failedCount);
                
                if ($failedCount > 0) {
                    $this->warn('Existem jobs com falha que precisam de atenção!');
                }
            } catch (\Exception $e) {
                $this->error('Erro ao verificar filas: ' . $e->getMessage());
            }
        }
    }
    
    protected function checkSessions(): void
    {
        $this->comment('Verificando sessões...');
        
        $driver = config('session.driver');
        $this->line('Driver: ' . $driver);
        
        if ($driver === 'file') {
            $path = config('session.files');
            $files = glob($path . '/*');
            $count = count($files);
            
            $this->line('Sessões ativas: ' . $count);
            
            if ($count > 10000) {
                $this->warn('Atenção: Muitas sessões ativas!');
            }
        } elseif ($driver === 'redis') {
            try {
                $prefix = config('session.prefix', 'laravel_session:');
                $keys = Redis::connection(config('session.connection'))->command('KEYS', ["{$prefix}*"]);
                $count = count($keys);
                
                $this->line('Sessões ativas: ' . $count);
            } catch (\Exception $e) {
                $this->error('Erro ao verificar sessões: ' . $e->getMessage());
            }
        }
    }
    
    protected function checkPermissions(): void
    {
        $this->comment('Verificando permissões de arquivos...');
        
        $webUser = posix_getpwuid(posix_geteuid())['name'];
        $this->line('Usuário web: ' . $webUser);
        
        $criticalFiles = [
            base_path('.env'),
            base_path('artisan'),
            public_path('index.php'),
        ];
        
        foreach ($criticalFiles as $file) {
            if (file_exists($file)) {
                $perms = substr(sprintf('%o', fileperms($file)), -4);
                $this->line(basename($file) . ': ' . $perms);
                
                if ($file === base_path('.env') && $perms !== '0600') {
                    $this->error('Arquivo .env deve ter permissão 0600!');
                }
            } else {
                $this->error('Arquivo ' . basename($file) . ' não encontrado!');
            }
        }
    }
    
    protected function formatBytes($bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, $precision) . ' ' . $units[$pow];
    }
    
    protected function formatUptime($seconds): string
    {
        $days = floor($seconds / 86400);
        $seconds %= 86400;
        
        $hours = floor($seconds / 3600);
        $seconds %= 3600;
        
        $minutes = floor($seconds / 60);
        $seconds %= 60;
        
        $parts = [];
        if ($days > 0) $parts[] = $days . 'd';
        if ($hours > 0) $parts[] = $hours . 'h';
        if ($minutes > 0) $parts[] = $minutes . 'm';
        if ($seconds > 0 || count($parts) === 0) $parts[] = $seconds . 's';
        
        return implode(' ', $parts);
    }
}</pre>
            </div>
        </section>

        <section id="troubleshooting-problemas" class="subsection">
            <h3>10.2. Problemas Comuns e Soluções</h3>
            <p>Esta seção aborda problemas frequentes encontrados durante o deployment e operação de aplicações Laravel
                12 e suas respectivas soluções.</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Problema</th>
                            <th>Possíveis Causas</th>
                            <th>Soluções</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Página em branco após deployment</td>
                            <td>
                                <ul>
                                    <li>Erro de permissão</li>
                                    <li>Cache desatualizado</li>
                                    <li>Erro de configuração</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Verificar logs de erro (storage/logs, logs do servidor web)</li>
                                    <li>Corrigir permissões: <code>chmod -R 755 storage bootstrap/cache</code></li>
                                    <li>Limpar caches: <code>php artisan optimize:clear</code></li>
                                    <li>Verificar configuração do servidor web</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Erro 500 após deployment</td>
                            <td>
                                <ul>
                                    <li>Erro na aplicação</li>
                                    <li>Dependências incompatíveis</li>
                                    <li>Configuração incorreta</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Verificar logs de erro</li>
                                    <li>Habilitar temporariamente <code>APP_DEBUG=true</code> para ver detalhes do erro
                                    </li>
                                    <li>Verificar se todas as dependências foram instaladas</li>
                                    <li>Verificar se o arquivo .env existe e está configurado corretamente</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Erro de conexão com banco de dados</td>
                            <td>
                                <ul>
                                    <li>Credenciais incorretas</li>
                                    <li>Banco de dados inacessível</li>
                                    <li>Firewall bloqueando conexão</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Verificar credenciais no arquivo .env</li>
                                    <li>Testar conexão manualmente: <code>mysql -u user -p -h host</code></li>
                                    <li>Verificar regras de firewall</li>
                                    <li>Verificar se o serviço de banco de dados está em execução</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Assets (CSS/JS) não carregam</td>
                            <td>
                                <ul>
                                    <li>Assets não compilados</li>
                                    <li>Caminho incorreto</li>
                                    <li>Problema de permissão</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Verificar se os assets foram compilados: <code>npm run build</code></li>
                                    <li>Verificar se o diretório public/build existe e tem os arquivos</li>
                                    <li>Verificar se APP_URL está configurado corretamente</li>
                                    <li>Verificar se o servidor web está configurado para servir arquivos estáticos</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Filas não processam jobs</td>
                            <td>
                                <ul>
                                    <li>Workers não estão em execução</li>
                                    <li>Configuração incorreta</li>
                                    <li>Redis inacessível</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Verificar se os workers estão em execução: <code>supervisorctl status</code>
                                    </li>
                                    <li>Iniciar workers: <code>supervisorctl start laravel-worker:*</code></li>
                                    <li>Verificar configuração de filas no .env</li>
                                    <li>Verificar conexão com Redis</li>
                                    <li>Verificar logs de falhas: <code>php artisan queue:failed</code></li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Migrações falham</td>
                            <td>
                                <ul>
                                    <li>Incompatibilidade de esquema</li>
                                    <li>Permissões insuficientes</li>
                                    <li>Erros de sintaxe</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Executar migrações com flag --pretend para verificar SQL:
                                        <code>php artisan migrate --pretend</code>
                                    </li>
                                    <li>Verificar permissões do usuário do banco de dados</li>
                                    <li>Verificar logs de erro</li>
                                    <li>Considerar migrações manuais para alterações complexas</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Sessões não persistem</td>
                            <td>
                                <ul>
                                    <li>Driver de sessão mal configurado</li>
                                    <li>Permissões de diretório</li>
                                    <li>Load balancer sem sticky sessions</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Verificar configuração de sessão no .env e config/session.php</li>
                                    <li>Para file driver: verificar permissões do diretório storage/framework/sessions
                                    </li>
                                    <li>Para Redis: verificar conexão</li>
                                    <li>Em ambientes com múltiplos servidores: configurar sticky sessions ou usar driver
                                        centralizado</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Desempenho lento</td>
                            <td>
                                <ul>
                                    <li>Cache não configurado</li>
                                    <li>Queries ineficientes</li>
                                    <li>Recursos de servidor insuficientes</li>
                                    <li>OPcache não otimizado</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Verificar configuração de cache</li>
                                    <li>Otimizar queries com índices e eager loading</li>
                                    <li>Utilizar ferramentas de profiling como Laravel Telescope ou Debugbar</li>
                                    <li>Verificar configuração do OPcache</li>
                                    <li>Considerar CDN para assets estáticos</li>
                                </ul>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="code-block">
                <h4>Script de Verificação de Problemas Comuns</h4>
                <pre>
#!/bin/bash

# Script para verificar problemas comuns em aplicações Laravel

echo "Iniciando verificação de problemas comuns..."

# Verificar permissões
echo "Verificando permissões..."
if [ ! -w "storage" ] || [ ! -w "bootstrap/cache" ]; then
    echo "ERRO: Diretórios storage ou bootstrap/cache não têm permissão de escrita"
    echo "Solução: chmod -R 775 storage bootstrap/cache"
    echo "         chown -R www-data:www-data storage bootstrap/cache"
else
    echo "Permissões OK"
fi

# Verificar arquivo .env
echo "Verificando arquivo .env..."
if [ ! -f ".env" ]; then
    echo "ERRO: Arquivo .env não encontrado"
    echo "Solução: cp .env.example .env && php artisan key:generate"
else
    echo "Arquivo .env encontrado"
    
    # Verificar APP_KEY
    if grep -q "APP_KEY=base64:" .env; then
        echo "APP_KEY configurada"
    else
        echo "ERRO: APP_KEY não configurada"
        echo "Solução: php artisan key:generate"
    fi
fi

# Verificar conexão com banco de dados
echo "Verificando conexão com banco de dados..."
DB_CONNECTION=$(grep DB_CONNECTION .env | cut -d '=' -f2)
DB_HOST=$(grep DB_HOST .env | cut -d '=' -f2)
DB_PORT=$(grep DB_PORT .env | cut -d '=' -f2)

if php artisan db:show > /dev/null 2>&1; then
    echo "Conexão com banco de dados OK"
else
    echo "ERRO: Não foi possível conectar ao banco de dados"
    echo "Verifique as configurações de banco de dados no arquivo .env"
    echo "Verifique se o serviço de banco de dados está em execução"
fi

# Verificar compilação de assets
echo "Verificando assets..."
if [ -d "public/build" ]; then
    echo "Diretório public/build encontrado"
else
    echo "AVISO: Diretório public/build não encontrado"
    echo "Solução: npm install && npm run build"
fi

# Verificar cache
echo "Verificando cache..."
if php artisan cache:get test_key > /dev/null 2>&1; then
    echo "Cache funcionando"
else
    echo "AVISO: Possível problema com cache"
    echo "Verifique a configuração de cache no arquivo .env"
    echo "Solução: php artisan cache:clear"
fi

# Verificar workers
echo "Verificando workers..."
if command -v supervisorctl &> /dev/null; then
    if supervisorctl status | grep -q "laravel-worker"; then
        echo "Workers configurados com Supervisor"
    else
        echo "AVISO: Workers não encontrados no Supervisor"
        echo "Verifique a configuração do Supervisor"
    fi
else
    echo "Supervisor não encontrado. Verifique se os workers estão em execução."
fi

# Verificar logs de erro
echo "Verificando logs de erro recentes..."
if [ -f "storage/logs/laravel.log" ]; then
    ERROR_COUNT=$(grep -c "\[ERROR\]" storage/logs/laravel.log)
    if [ $ERROR_COUNT -gt 0 ]; then
        echo "AVISO: $ERROR_COUNT erros encontrados no log"
        echo "Verifique o arquivo storage/logs/laravel.log para mais detalhes"
    else
        echo "Nenhum erro recente encontrado nos logs"
    fi
else
    echo "Arquivo de log não encontrado"
fi

echo "Verificação concluída!"</pre>
            </div>
        </section>

        <section id="troubleshooting-performance" class="subsection">
            <h3>10.3. Otimização de Performance</h3>
            <p>Esta seção aborda técnicas para identificar e resolver problemas de performance em aplicações Laravel 12.
            </p>

            <div class="best-practice">
                <h4>Ferramentas de Profiling</h4>
                <ul>
                    <li><strong>Laravel Telescope:</strong> Monitoramento detalhado de requisições, queries, cache, etc.
                    </li>
                    <li><strong>Laravel Debugbar:</strong> Barra de debug com informações de performance</li>
                    <li><strong>Blackfire.io:</strong> Profiling avançado de aplicações PHP</li>
                    <li><strong>New Relic:</strong> Monitoramento de performance em produção</li>
                    <li><strong>Laravel Pulse:</strong> Monitoramento em tempo real de métricas de performance</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Identificação de Queries Lentas</h4>
                <pre>
// Em config/database.php
'mysql' => [
    // ...
    'options' => extension_loaded('pdo_mysql') ? array_filter([
        // ...
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
    ]) : [],
    'dump' => [
        'enabled' => env('DB_DUMP_ENABLED', false),
        'threshold' => env('DB_DUMP_THRESHOLD', 100), // em milissegundos
    ],
],

// Em app/Providers/AppServiceProvider.php
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

public function boot(): void
{
    if (app()->environment('local', 'staging')) {
        DB::listen(function (QueryExecuted $query) {
            $threshold = config('database.connections.mysql.dump.threshold', 100);
            
            if ($query->time > $threshold) {
                $sql = $query->sql;
                $bindings = $query->bindings;
                $time = $query->time;
                
                $sql = str_replace(['?'], array_map(function ($binding) {
                    return is_numeric($binding) ? $binding : "'{$binding}'";
                }, $bindings), $sql);
                
                Log::channel('queries')->warning("Slow query detected: {$sql} ({$time}ms)");
            }
        });
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Otimização de Queries com Eager Loading</h4>
                <pre>
// Exemplo de N+1 problem
// Ruim - gera N+1 queries
$posts = Post::all();
foreach ($posts as $post) {
    echo $post->user->name; // Uma query adicional para cada post
}

// Bom - apenas 2 queries
$posts = Post::with('user')->get();
foreach ($posts as $post) {
    echo $post->user->name; // Dados já carregados
}

// Carregamento de múltiplas relações
$posts = Post::with(['user', 'comments', 'tags'])->get();

// Carregamento de relações aninhadas
$posts = Post::with(['comments.user', 'tags'])->get();

// Carregamento condicional
$posts = Post::with(['comments' => function ($query) {
    $query->where('approved', true)->latest();
}])->get();</pre>
            </div>

            <div class="best-practice">
                <h4>Técnicas de Otimização de Performance</h4>
                <ul>
                    <li><strong>Caching:</strong> Implementar cache para consultas frequentes, views e dados estáticos
                    </li>
                    <li><strong>Indexação:</strong> Adicionar índices apropriados nas tabelas do banco de dados</li>
                    <li><strong>Eager Loading:</strong> Evitar o problema N+1 em consultas relacionadas</li>
                    <li><strong>Paginação:</strong> Utilizar paginação para conjuntos grandes de dados</li>
                    <li><strong>Chunking:</strong> Processar grandes conjuntos de dados em lotes</li>
                    <li><strong>Queues:</strong> Mover processamento pesado para jobs em background</li>
                    <li><strong>Otimização de Assets:</strong> Minificar e combinar CSS/JS, otimizar imagens</li>
                    <li><strong>CDN:</strong> Utilizar CDN para assets estáticos</li>
                    <li><strong>OPcache:</strong> Configurar corretamente o OPcache do PHP</li>
                    <li><strong>Database Connection Pooling:</strong> Reutilizar conexões de banco de dados</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Implementação de Cache Eficiente</h4>
                <pre>
// Cache de consultas frequentes
public function getPopularPosts($limit = 10)
{
    return Cache::remember('popular_posts_' . $limit, now()->addHours(3), function () use ($limit) {
        return Post::withCount('views')
            ->orderByDesc('views_count')
            ->limit($limit)
            ->get();
    });
}

// Cache de fragmentos de view
@php
$cacheKey = 'user_profile_' . $user->id . '_' . $user->updated_at->timestamp;
@endphp

@cache($cacheKey, 60*24)
    <div class="user-profile">
        <h2>{{ $user->name }}</h2>
        <div class="stats">
            <span>Posts: {{ $user->posts_count }}</span>
            <span>Followers: {{ $user->followers_count }}</span>
        </div>
        <!-- Conteúdo que raramente muda -->
    </div>
@endcache

// Invalidação seletiva de cache com tags
Cache::tags(['posts', 'user:' . $user->id])->put('user_posts_' . $user->id, $posts, 3600);

// Quando um post é atualizado
Cache::tags(['posts', 'post:' . $post->id])->flush();

// Quando todos os posts de um usuário precisam ser invalidados
Cache::tags(['posts', 'user:' . $user->id])->flush();</pre>
            </div>

            <div class="code-block">
                <h4>Processamento em Lotes para Grandes Conjuntos de Dados</h4>
                <pre>
// Processamento em lotes com chunk
User::where('active', false)
    ->chunk(100, function ($users) {
        foreach ($users as $user) {
            // Processar cada usuário
        }
    });

// Processamento em lotes com cursor (menor uso de memória)
foreach (User::where('active', false)->cursor() as $user) {
    // Processar cada usuário
}

// Processamento em lotes com LazyCollection (Laravel 12)
User::where('active', false)
    ->lazyById(100)
    ->each(function ($user) {
        // Processar cada usuário
    });

// Processamento em lotes com jobs
User::where('active', false)
    ->chunk(100, function ($users) {
        ProcessUsersBatch::dispatch($users);
    });</pre>
            </div>
        </section>

        <section id="troubleshooting-rollback" class="subsection">
            <h3>10.4. Procedimentos de Rollback</h3>
            <p>Esta seção detalha procedimentos para realizar rollback em diferentes cenários de falha.</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Cenário</th>
                            <th>Procedimento de Rollback</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Falha em deployment de código</td>
                            <td>
                                <ol>
                                    <li>Executar script de rollback para retornar à versão anterior:
                                        <code>cd /var/www/production && bash rollback.sh</code>
                                    </li>
                                    <li>Verificar logs para identificar a causa da falha</li>
                                    <li>Notificar a equipe de desenvolvimento</li>
                                </ol>
                            </td>
                        </tr>
                        <tr>
                            <td>Falha em migração de banco de dados</td>
                            <td>
                                <ol>
                                    <li>Executar rollback da migração:
                                        <code>php artisan migrate:rollback --step=1</code>
                                    </li>
                                    <li>Se necessário, restaurar backup do banco de dados:
                                        <code>mysql -u user -p database < backup.sql</code>
                                    </li>
                                    <li>Retornar ao código compatível com o esquema anterior</li>
                                </ol>
                            </td>
                        </tr>
                        <tr>
                            <td>Falha em atualização de dependências</td>
                            <td>
                                <ol>
                                    <li>Restaurar arquivo composer.lock anterior:
                                        <code>git checkout HEAD~1 composer.lock</code>
                                    </li>
                                    <li>Reinstalar dependências:
                                        <code>composer install --no-scripts</code>
                                    </li>
                                    <li>Executar scripts pós-instalação:
                                        <code>composer run-script post-install-cmd</code>
                                    </li>
                                </ol>
                            </td>
                        </tr>
                        <tr>
                            <td>Falha em configuração de servidor</td>
                            <td>
                                <ol>
                                    <li>Restaurar arquivos de configuração anteriores:
                                        <code>cp /etc/nginx/sites-available/example.com.conf.bak /etc/nginx/sites-available/example.com.conf</code>
                                    </li>
                                    <li>Reiniciar serviços afetados:
                                        <code>systemctl restart nginx php8.2-fpm</code>
                                    </li>
                                    <li>Verificar logs para identificar a causa da falha</li>
                                </ol>
                            </td>
                        </tr>
                        <tr>
                            <td>Falha em atualização de assets</td>
                            <td>
                                <ol>
                                    <li>Restaurar assets anteriores:
                                        <code>cp -r /var/www/production/releases/previous/public/build /var/www/production/current/public/</code>
                                    </li>
                                    <li>Limpar cache do navegador ou invalidar cache de CDN</li>
                                    <li>Verificar erros de compilação nos logs</li>
                                </ol>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="code-block">
                <h4>Script de Rollback Completo</h4>
                <pre>
#!/bin/bash

# Script de rollback completo para Laravel 12

# Configurações
PRODUCTION_DIR="/var/www/production"
RELEASES_DIR="${PRODUCTION_DIR}/releases"
CURRENT_LINK="${PRODUCTION_DIR}/current"
BACKUP_DIR="${PRODUCTION_DIR}/backups"
DB_BACKUP="${BACKUP_DIR}/db_before_deploy_$(date +%Y%m%d).sql"

# Funções
function log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

function rollback_code() {
    log_message "Iniciando rollback de código..."
    
    # Listar as últimas 5 releases
    log_message "Releases disponíveis:"
    ls -lt $RELEASES_DIR | grep -v "total" | head -5
    
    # Obter a release anterior
    CURRENT_RELEASE=$(basename $(readlink $CURRENT_LINK))
    PREVIOUS_RELEASE=$(ls -t $RELEASES_DIR | grep -v $CURRENT_RELEASE | head -1)
    
    if [ -z "$PREVIOUS_RELEASE" ]; then
        log_message "ERRO: Não foi possível encontrar uma release anterior"
        return 1
    fi
    
    log_message "Rollback para release: $PREVIOUS_RELEASE"
    
    # Atualizar link simbólico
    ln -sfn "${RELEASES_DIR}/${PREVIOUS_RELEASE}" $CURRENT_LINK
    
    log_message "Rollback de código concluído"
    return 0
}

function rollback_database() {
    log_message "Iniciando rollback de banco de dados..."
    
    if [ ! -f "$DB_BACKUP" ]; then
        log_message "ERRO: Backup de banco de dados não encontrado: $DB_BACKUP"
        return 1
    fi
    
    # Obter credenciais do banco de dados do arquivo .env
    source <(grep -v '^#' ${CURRENT_LINK}/.env | sed 's/^/export /')
    
    log_message "Restaurando backup do banco de dados..."
    mysql -u"$DB_USERNAME" -p"$DB_PASSWORD" -h"$DB_HOST" "$DB_DATABASE" < "$DB_BACKUP"
    
    if [ $? -ne 0 ]; then
        log_message "ERRO: Falha ao restaurar backup do banco de dados"
        return 1
    fi
    
    log_message "Rollback de banco de dados concluído"
    return 0
}

function rollback_assets() {
    log_message "Iniciando rollback de assets..."
    
    CURRENT_RELEASE=$(basename $(readlink $CURRENT_LINK))
    PREVIOUS_RELEASE=$(ls -t $RELEASES_DIR | grep -v $CURRENT_RELEASE | head -1)
    
    if [ -d "${RELEASES_DIR}/${PREVIOUS_RELEASE}/public/build" ]; then
        log_message "Restaurando assets da release anterior..."
        cp -r "${RELEASES_DIR}/${PREVIOUS_RELEASE}/public/build" "${CURRENT_LINK}/public/"
        log_message "Assets restaurados com sucesso"
    else
        log_message "AVISO: Diretório de assets não encontrado na release anterior"
    fi
    
    return 0
}

function restart_services() {
    log_message "Reiniciando serviços..."
    
    systemctl reload php8.2-fpm
    systemctl reload nginx
    supervisorctl restart laravel-worker:*
    
    log_message "Serviços reiniciados"
}

function verify_application() {
    log_message "Verificando aplicação após rollback..."
    
    # Verificar se a aplicação está respondendo
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://exemplo.com/api/health)
    
    if [ "$HTTP_STATUS" == "200" ]; then
        log_message "Aplicação está respondendo normalmente (HTTP 200)"
        return 0
    else
        log_message "AVISO: Aplicação retornou status HTTP $HTTP_STATUS"
        return 1
    fi
}

# Menu de rollback
echo "=== Script de Rollback Laravel 12 ==="
echo "1) Rollback completo (código, banco de dados e assets)"
echo "2) Rollback apenas de código"
echo "3) Rollback apenas de banco de dados"
echo "4) Rollback apenas de assets"
echo "5) Sair"

read -p "Escolha uma opção: " option

case $option in
    1)
        log_message "Iniciando rollback completo..."
        rollback_code
        rollback_database
        rollback_assets
        restart_services
        verify_application
        ;;
    2)
        rollback_code
        restart_services
        verify_application
        ;;
    3)
        rollback_database
        restart_services
        verify_application
        ;;
    4)
        rollback_assets
        restart_services
        verify_application
        ;;
    5)
        log_message "Operação cancelada pelo usuário"
        exit 0
        ;;
    *)
        log_message "Opção inválida"
        exit 1
        ;;
esac

log_message "Processo de rollback concluído"</pre>
            </div>

            <div class="warning">
                <p><strong>Importante:</strong> Sempre teste os procedimentos de rollback em ambientes de homologação
                    antes de aplicá-los em produção. Mantenha backups atualizados e documentação detalhada dos
                    procedimentos para cada tipo de rollback.</p>
            </div>
        </section>

        <section id="troubleshooting-logs" class="subsection">
            <h3>10.5. Análise de Logs</h3>
            <p>A análise eficiente de logs é fundamental para identificar e resolver problemas em aplicações Laravel 12.
            </p>

            <div class="best-practice">
                <h4>Ferramentas para Análise de Logs</h4>
                <ul>
                    <li><strong>ELK Stack:</strong> Elasticsearch, Logstash e Kibana para centralização e visualização
                        de logs</li>
                    <li><strong>Graylog:</strong> Plataforma de gerenciamento de logs centralizada</li>
                    <li><strong>Papertrail:</strong> Serviço de gerenciamento de logs em nuvem</li>
                    <li><strong>Laravel Telescope:</strong> Interface para visualização de logs e eventos da aplicação
                    </li>
                    <li><strong>Grep/Awk/Sed:</strong> Ferramentas de linha de comando para análise rápida de logs</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Comandos Úteis para Análise de Logs</h4>
                <pre>
# Visualizar últimas 100 linhas do log
tail -n 100 storage/logs/laravel.log

# Filtrar erros no log
grep -i "error" storage/logs/laravel.log

# Filtrar erros de um período específico
grep "2023-10-15" storage/logs/laravel.log | grep -i "error"

# Contar ocorrências de erros por tipo
grep -i "error" storage/logs/laravel.log | awk -F'[][]' '{print $3}' | sort | uniq -c | sort -nr

# Encontrar requisições lentas (mais de 1 segundo)
grep "Request completed in" storage/logs/laravel.log | awk '{if ($NF > 1000) print $0}'

# Analisar logs de exceções
grep -A 10 "Exception" storage/logs/laravel.log

# Extrair e contar URLs com erro 500
grep "500" storage/logs/laravel.log | grep -o 'GET [^ ]*\|POST [^ ]*' | sort | uniq -c | sort -nr</pre>
            </div>

            <div class="code-block">
                <h4>Script para Análise de Logs</h4>
                <pre>
#!/bin/bash

# Script para análise de logs do Laravel

LOG_FILE="storage/logs/laravel.log"
DAYS_AGO=7
OUTPUT_DIR="log-analysis"

# Verificar se o arquivo de log existe
if [ ! -f "$LOG_FILE" ]; then
    echo "Arquivo de log não encontrado: $LOG_FILE"
    exit 1
fi

# Criar diretório para resultados
mkdir -p "$OUTPUT_DIR"

# Definir data para filtro
FILTER_DATE=$(date -d "$DAYS_AGO days ago" +"%Y-%m-%d")

echo "Analisando logs a partir de $FILTER_DATE..."

# Extrair e contar erros por tipo
echo "Extraindo estatísticas de erros..."
grep -i "error\|exception\|critical" "$LOG_FILE" | grep -A 1 -E "$FILTER_DATE" | \
    awk -F'[][]' '{print $3}' | sort | uniq -c | sort -nr > "$OUTPUT_DIR/errors_by_type.txt"

# Extrair e contar URLs com erro
echo "Extraindo URLs com erro..."
grep -E "404|500" "$LOG_FILE" | grep -E "$FILTER_DATE" | \
    grep -o 'GET [^ ]*\|POST [^ ]*' | sort | uniq -c | sort -nr > "$OUTPUT_DIR/error_urls.txt"

# Extrair requisições lentas (mais de 1 segundo)
echo "Extraindo requisições lentas..."
grep "Request completed in" "$LOG_FILE" | grep -E "$FILTER_DATE" | \
    awk '{if ($NF > 1000) print $0}' | sort -k $NF -nr > "$OUTPUT_DIR/slow_requests.txt"

# Extrair erros de banco de dados
echo "Extraindo erros de banco de dados..."
grep -i "sql\|query\|database" "$LOG_FILE" | grep -i "error\|exception" | \
    grep -E "$FILTER_DATE" > "$OUTPUT_DIR/database_errors.txt"

# Extrair erros de autenticação
echo "Extraindo erros de autenticação..."
grep -i "auth\|login\|authentication" "$LOG_FILE" | grep -i "fail\|error\|invalid" | \
    grep -E "$FILTER_DATE" > "$OUTPUT_DIR/auth_errors.txt"

# Gerar resumo
echo "Gerando resumo..."
{
    echo "=== Resumo da Análise de Logs ==="
    echo "Período: $FILTER_DATE até hoje"
    echo ""
    echo "Total de erros: $(grep -i "error\|exception\|critical" "$LOG_FILE" | grep -E "$FILTER_DATE" | wc -l)"
    echo "Total de requisições lentas: $(grep "Request completed in" "$LOG_FILE" | grep -E "$FILTER_DATE" | awk '{if ($NF > 1000) print $0}' | wc -l)"
    echo "Erros 500: $(grep "500" "$LOG_FILE" | grep -E "$FILTER_DATE" | wc -l)"
    echo "Erros 404: $(grep "404" "$LOG_FILE" | grep -E "$FILTER_DATE" | wc -l)"
    echo ""
    echo "Top 5 tipos de erro:"
    head -5 "$OUTPUT_DIR/errors_by_type.txt"
    echo ""
    echo "Top 5 URLs com erro:"
    head -5 "$OUTPUT_DIR/error_urls.txt"
} > "$OUTPUT_DIR/summary.txt"

echo "Análise concluída! Resultados disponíveis no diretório $OUTPUT_DIR"</pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Análise de Logs</h4>
                <ul>
                    <li>Centralize logs em um único sistema para facilitar a análise</li>
                    <li>Utilize formatos estruturados (JSON) para facilitar o parsing</li>
                    <li>Estabeleça níveis de severidade consistentes</li>
                    <li>Configure alertas para padrões críticos nos logs</li>
                    <li>Mantenha rotação de logs para evitar arquivos muito grandes</li>
                    <li>Inclua contexto suficiente em cada entrada de log</li>
                    <li>Utilize IDs de correlação para rastrear requisições através de múltiplos serviços</li>
                    <li>Analise logs regularmente, não apenas quando ocorrem problemas</li>
                    <li>Automatize a análise de logs para identificar tendências</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configuração do ELK Stack para Laravel</h4>
                <pre>
# docker-compose.yml para ELK Stack
version: '3.7'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.10.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - elk

  logstash:
    image: docker.elastic.co/logstash/logstash:8.10.0
    container_name: logstash
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
      - ./storage/logs:/var/log/laravel
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
      - "5000:5000/udp"
      - "9600:9600"
    environment:
      LS_JAVA_OPTS: "-Xmx256m -Xms256m"
    networks:
      - elk
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.10.0
    container_name: kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_URL: http://elasticsearch:9200
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    networks:
      - elk
    depends_on:
      - elasticsearch

networks:
  elk:
    driver: bridge

volumes:
  elasticsearch-data:
    driver: local</pre>
            </div>

            <div class="code-block">
                <h4>Configuração do Logstash para Laravel</h4>
                <pre>
# logstash/pipeline/laravel.conf
input {
  file {
    path => "/var/log/laravel/laravel.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => multiline {
      pattern => "^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]"
      negate => true
      what => "previous"
    }
  }
}

filter {
  grok {
    match => { "message" => "\[%{TIMESTAMP_ISO8601:timestamp}\] %{WORD:env}\.%{LOGLEVEL:log_level}: %{GREEDYDATA:message}" }
    overwrite => [ "message" ]
  }

  date {
    match => [ "timestamp", "yyyy-MM-dd HH:mm:ss" ]
    target => "@timestamp"
  }

  if [message] =~ /exception|error|critical/i {
    mutate {
      add_tag => ["error"]
    }
  }

  if [message] =~ /warning|notice/i {
    mutate {
      add_tag => ["warning"]
    }
  }

  # Extrair informações de requisição HTTP
  grok {
    match => { "message" => "Request URL: %{URIPROTO:http_protocol}://%{IPORHOST:http_host}%{URIPATHPARAM:http_path}" }
  }

  # Extrair tempo de resposta
  grok {
    match => { "message" => "Request completed in %{NUMBER:response_time:float}ms" }
  }

  # Extrair informações de exceção
  if [message] =~ /exception/i {
    grok {
      match => { "message" => "(?m)%{GREEDYDATA:exception_type}: %{GREEDYDATA:exception_message}[\r\n]?.*?in %{GREEDYDATA:exception_file} on line %{NUMBER:exception_line:int}" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "laravel-logs-%{+YYYY.MM.dd}"
  }
  stdout {
    codec => rubydebug
  }
}</pre>
            </div>
        </section>
    </section>

    <footer class="manual-footer">
        <p>Manual de Deployment - Laravel 12</p>
        <p>Última atualização: Outubro 2023</p>
        <p>© 2023 Equipe de Desenvolvimento</p>
    </footer>
</body>

</html>