{"openapi": "3.0.0", "info": {"title": "Sistema de Backend API", "description": "API para sistema de backend com autenticação JWT", "contact": {"name": "Equipe de Suporte", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://my-default-host.com", "description": "API Server"}], "paths": {"/api/auth/login": {"post": {"tags": ["Autenticação"], "summary": "Autenticar usuá<PERSON>", "operationId": "d7cbb3ac96888b0e21dcf8ee1845fe40", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "senha123"}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON>gin bem-sucedido", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"token": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}, "user": {"$ref": "#/components/schemas/UserModel"}}, "type": "object"}, "message": {"type": "string", "example": "Login realizado com sucesso"}}, "type": "object"}}}}, "401": {"description": "Credenciais inválidas", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Credenciais inválidas"}, "errors": {"type": "object", "nullable": true}}, "type": "object"}}}}, "422": {"description": "Erro de validação", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Erro de validação"}, "errors": {"type": "object", "example": {"email": ["O campo email é obrigatório."]}}}, "type": "object"}}}}}}}, "/api/auth/register": {"post": {"tags": ["Autenticação"], "summary": "Registrar novo usuário", "operationId": "31cfdf7ad0b73495ed2b7bb54e9d8a54", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name", "email", "password", "password_confirmation"], "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "senha123"}, "password_confirmation": {"type": "string", "format": "password", "example": "senha123"}}, "type": "object"}}}}, "responses": {"201": {"description": "Usuário registrado com sucesso", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"token": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}, "user": {"$ref": "#/components/schemas/UserModel"}}, "type": "object"}, "message": {"type": "string", "example": "Usuário registrado com sucesso"}}, "type": "object"}}}}, "422": {"description": "Erro de validação", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Erro de validação"}, "errors": {"type": "object", "example": {"email": ["O email já está em uso."]}}}, "type": "object"}}}}}}}, "/api/auth/logout": {"post": {"tags": ["Autenticação"], "summary": "<PERSON><PERSON><PERSON>", "operationId": "7bdaf2cc85f771edf0ea8b95bb8dbac1", "responses": {"200": {"description": "Logout realizado com sucesso", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "null"}, "message": {"type": "string", "example": "Logout realizado com sucesso"}}, "type": "object"}}}}, "401": {"description": "Não autenticado", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Não autenticado"}, "errors": {"type": "object", "nullable": true}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/auth/me": {"get": {"tags": ["Autenticação"], "summary": "Obter dados do usuário autenticado", "operationId": "01da7495e0234cedd5dd80663a0b1317", "responses": {"200": {"description": "Dados do usuário", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/UserModel"}, "message": {"type": "string", "example": "Dados do usuário"}}, "type": "object"}}}}, "401": {"description": "Não autenticado", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Não autenticado"}, "errors": {"type": "object", "nullable": true}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/examples": {"get": {"tags": ["Exemplos"], "summary": "Lista todos os exemplos", "operationId": "3528974bf64602254a5ef01dd275e826", "responses": {"200": {"description": "Lista de exemplos", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ExampleModel"}}, "message": {"type": "string", "example": "Exemplos listados com sucesso"}}, "type": "object"}}}}}}}, "/api/examples/{id}": {"get": {"tags": ["Exemplos"], "summary": "Obtém um exemplo específico", "operationId": "fad36f5a24a2bb09890f608fecb76efb", "parameters": [{"name": "id", "in": "path", "description": "ID do exemplo", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Exemplo encontrado", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/ExampleModel"}, "message": {"type": "string", "example": "Exemplo encontrado com sucesso"}}, "type": "object"}}}}}}}}, "components": {"schemas": {"SuccessResponse": {"title": "Success Response", "description": "Resposta de sucesso padrão", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Operação realizada com sucesso"}, "data": {"type": "object", "nullable": true}}, "type": "object"}, "TokenPair": {"title": "Token Pair", "description": "Par de tokens de acesso e refresh", "properties": {"access_token": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}, "token_type": {"type": "string", "example": "bearer"}, "expires_in": {"type": "integer", "example": 3600}, "refresh_token": {"type": "string", "example": "eyJ1c2VyX2lkIjoxLCJhY2Nlc3NfdG9rZW4i..."}}, "type": "object"}, "Session": {"title": "Session", "description": "Informações de sessão ativa", "properties": {"device": {"type": "string", "example": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)..."}, "ip": {"type": "string", "example": "***********"}, "created_at": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00.000000Z"}, "expires_at": {"type": "string", "format": "date-time", "example": "2023-01-01T01:00:00.000000Z"}, "last_activity": {"type": "string", "format": "date-time", "example": "2023-01-01T00:30:00.000000Z"}, "platform": {"type": "string", "example": "windows"}}, "type": "object"}, "User": {"title": "User", "description": "<PERSON><PERSON>", "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "created_at": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00.000000Z"}}, "type": "object"}, "Error": {"title": "Erro", "description": "Resposta de erro pad<PERSON>", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Mensagem de erro"}, "errors": {"type": "object", "nullable": true}}, "type": "object"}, "UserModel": {"title": "User Model", "description": "Modelo completo de usuário", "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "email_verified_at": {"type": "string", "format": "date-time", "nullable": true}, "created_at": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00.000000Z"}}, "type": "object"}, "ExampleModel": {"title": "Example Model", "description": "Modelo de exemplo", "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "title": {"type": "string", "example": "Título do exemplo"}, "description": {"type": "string", "example": "Descrição do exemplo"}, "created_at": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2023-01-01T00:00:00.000000Z"}}, "type": "object"}}, "responses": {"BadRequest": {"description": "Requisição inválida", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Requisição inválida"}, "errors": {"type": "object", "nullable": true}}, "type": "object"}}}}, "Unauthorized": {"description": "Não autorizado", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Não autorizado"}, "errors": {"type": "object", "nullable": true}}, "type": "object"}}}}, "Forbidden": {"description": "<PERSON><PERSON> proibido", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> proibido"}, "errors": {"type": "object", "nullable": true}}, "type": "object"}}}}, "NotFound": {"description": "Recurso não encontrado", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Recurso não encontrado"}, "errors": {"type": "object", "nullable": true}}, "type": "object"}}}}, "ValidationError": {"description": "Erro de validação", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Erro de validação"}, "errors": {"type": "object", "example": {"email": ["O campo email é obrigatório."]}}}, "type": "object"}}}}, "ServerError": {"description": "Erro interno do servidor", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Erro interno do servidor"}, "errors": {"type": "object", "nullable": true}}, "type": "object"}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Autenticação", "description": "Endpoints para autenticação de usuários"}, {"name": "Exemplos", "description": "Operações de exemplo"}]}