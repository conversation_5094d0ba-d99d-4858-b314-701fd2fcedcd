<?php

namespace Tests\Unit\Services;

use App\Models\UserModel;
use App\Services\JwtService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Mockery;
use Illuminate\Contracts\Cache\Repository;
use Illuminate\Http\Request;

class JwtServiceTest extends TestCase
{
    protected $jwtService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->jwtService = new JwtService();
    }

    #[Test]
    public function it_can_get_active_sessions()
    {
        // Mock para Cache
        Cache::shouldReceive('get')
            ->with('user_tokens:1', [])
            ->andReturn([
                'token1' => [
                    'token' => 'token1-value',
                    'device' => 'Chrome Browser',
                    'ip' => '***********',
                    'created_at' => Carbon::now()->subHour()->toIso8601String(),
                    'expires_at' => Carbon::now()->addHour()->toIso8601String(),
                ],
                'token2' => [
                    'token' => 'token2-value',
                    'device' => 'Firefox Browser',
                    'ip' => '***********',
                    'created_at' => Carbon::now()->subHours(2)->toIso8601String(),
                    'expires_at' => Carbon::now()->addHour()->toIso8601String(),
                ]
            ]);

        // Execute
        $sessions = $this->jwtService->getActiveSessions(1);

        // Assert
        $this->assertIsArray($sessions);
        $this->assertCount(2, $sessions);
        $this->assertEquals('Chrome Browser', $sessions[0]['device']);
        $this->assertEquals('Firefox Browser', $sessions[1]['device']);
        $this->assertArrayNotHasKey('token', $sessions[0]);
        $this->assertArrayNotHasKey('token', $sessions[1]);
    }

    #[Test]
    public function it_should_check_if_token_is_blacklisted()
    {
        // Configurar JWTAuth
        $mockBlacklist = Mockery::mock('stdClass');
        $mockBlacklist->shouldReceive('has')
            ->with('test-token')
            ->andReturn(true);

        JWTAuth::shouldReceive('getBlacklist')
            ->andReturn($mockBlacklist);

        // Execute
        $result = $this->jwtService->isTokenBlacklisted('test-token');

        // Assert
        $this->assertTrue($result);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    #[Test]
    public function it_can_invalidate_token()
    {
        // 1. Mock de Request com expectativas específicas
        $mockRequest = Mockery::mock(Request::class);
        $mockRequest->shouldReceive('userAgent')->andReturn('Test-Browser');
        $mockRequest->shouldReceive('ip')->andReturn('127.0.0.1');
        $mockRequest->shouldReceive('setUserResolver')->withAnyArgs()->andReturnSelf();
        $this->app->instance('request', $mockRequest);

        // 2. Mock para JWTAuth
        JWTAuth::shouldReceive('setToken')
            ->with('test-token')
            ->andReturnSelf();
        JWTAuth::shouldReceive('invalidate')
            ->andReturn(true);

        // 3. Mock para Cache
        Cache::shouldReceive('get')
            ->with('user_tokens:1', [])
            ->andReturn([
                'test-token' => [
                    'token' => 'test-token',
                    'device' => 'Test-Browser',
                    'ip' => '127.0.0.1',
                    'created_at' => Carbon::now()->toIso8601String(),
                    'expires_at' => Carbon::now()->addHour()->toIso8601String(),
                ]
            ]);
        Cache::shouldReceive('put')
            ->withAnyArgs()
            ->andReturn(true);

        // Execute
        $result = $this->jwtService->invalidateToken(1, 'test-token');

        // Assert
        $this->assertTrue($result);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    #[Test]
    public function it_can_invalidate_all_tokens()
    {
        // 1. Mock para cache com driver específico
        $mockRepository = Mockery::mock(Repository::class);
        $mockRepository->shouldReceive('get')
            ->withAnyArgs()
            ->andReturn(null);
        $mockRepository->shouldReceive('put')
            ->withAnyArgs()
            ->andReturn(true);

        Cache::shouldReceive('driver')
            ->andReturn($mockRepository);

        // 2. Resto do mock para Cache
        Cache::shouldReceive('get')
            ->with('user_tokens:1', [])
            ->andReturn([
                'token1' => [
                    'token' => 'token1',
                    'device' => 'Device 1',
                    'ip' => '127.0.0.1',
                ],
                'token2' => [
                    'token' => 'token2',
                    'device' => 'Device 2',
                    'ip' => '*********',
                ]
            ]);

        Cache::shouldReceive('forget')
            ->with('user_tokens:1')
            ->andReturn(true);

        // 3. Mock para JWTAuth
        JWTAuth::shouldReceive('setToken')
            ->with(Mockery::anyOf('token1', 'token2'))
            ->andReturnSelf();

        JWTAuth::shouldReceive('invalidate')
            ->andReturn(true);

        // Execute
        $result = $this->jwtService->invalidateAllTokens(1);

        // Assert
        $this->assertTrue($result);
    }

    #[Test]
    public function it_can_generate_refresh_token()
    {
        // Mock para Config
        Config::shouldReceive('get')
            ->with('jwt.refresh_ttl', 20160)
            ->andReturn(20160);

        // Mock para Cache
        Cache::shouldReceive('put')
            ->once()
            ->withArgs(function ($key, $payload, $expiresAt) {
                return strpos($key, 'refresh_token:') === 0;
            })
            ->andReturn(true);

        // Execute
        $refreshToken = $this->jwtService->generateRefreshToken(1, 'test-token');

        // Assert token format and structure
        $this->assertNotEmpty($refreshToken);
        $decoded = json_decode(base64_decode($refreshToken), true);
        $this->assertEquals(1, $decoded['user_id']);
        $this->assertEquals('test-token', $decoded['access_token']);
    }

    #[Test]
    public function it_can_decode_refresh_token()
    {
        // Criar payload de teste
        $payload = [
            'user_id' => 1,
            'access_token' => 'test-token',
            'token_id' => 'test-id',
            'expires_at' => Carbon::now()->addHour()->toIso8601String()
        ];
        $refreshToken = base64_encode(json_encode($payload));

        // Mock para Cache::get
        Cache::shouldReceive('get')
            ->with('refresh_token:test-id')
            ->andReturn($payload);

        // Usar reflection para acessar método privado
        $reflection = new \ReflectionClass($this->jwtService);
        $method = $reflection->getMethod('decodeRefreshToken');
        $method->setAccessible(true);

        // Execute
        $result = $method->invokeArgs($this->jwtService, [$refreshToken]);

        // Assert
        $this->assertEquals(1, $result['user_id']);
        $this->assertEquals('test-token', $result['access_token']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
