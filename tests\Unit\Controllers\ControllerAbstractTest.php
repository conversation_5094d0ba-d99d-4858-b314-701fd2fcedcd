<?php

namespace Tests\Unit\Controllers;

use App\Controllers\ControllerAbstract;
use App\Responses\ResponseInterface;
use App\Services\ServiceAbstract;
use Illuminate\Http\JsonResponse;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Mockery;

class ControllerAbstractTest extends TestCase
{
    protected $controller;
    protected $mockService;
    protected $mockResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockService = Mockery::mock(ServiceAbstract::class);
        $this->mockResponse = Mockery::mock(ResponseInterface::class);

        // Criar uma implementação concreta para testes
        $this->controller = new class($this->mockService, $this->mockResponse) extends ControllerAbstract {
            // Método para expor métodos protegidos para teste
            public function getResponse()
            {
                return $this->response();
            }

            public function getService()
            {
                return $this->service;
            }

            // Método exemplo para testar a execução de serviços
            public function executeServiceMethod($param)
            {
                try {
                    return $this->service->doSomething($param);
                } catch (\Exception $e) {
                    // Agora tratamos a exceção e retornamos uma resposta de erro
                    return $this->response()->serverError('Erro interno do servidor');
                }
            }
        };
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    #[Test]
    public function it_has_a_service_instance()
    {
        $this->assertSame($this->mockService, $this->controller->getService());
    }

    #[Test]
    public function it_has_a_response_instance()
    {
        $this->assertSame($this->mockResponse, $this->controller->getResponse());
    }

    #[Test]
    public function it_can_return_ok_response()
    {
        $data = ['test' => true];
        $message = 'Sucesso';
        $expectedResponse = new JsonResponse(['data' => $data, 'message' => $message], 200);

        $this->mockResponse->shouldReceive('ok')
            ->once()
            ->with($data, $message)
            ->andReturn($expectedResponse);

        $result = $this->controller->getResponse()->ok($data, $message);

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(200, $result->getStatusCode());
        $this->assertEquals(json_encode(['data' => $data, 'message' => $message]), $result->getContent());
    }

    #[Test]
    public function it_can_return_error_response()
    {
        $message = 'Erro do servidor';
        $expectedResponse = new JsonResponse(['message' => $message], 500);

        $this->mockResponse->shouldReceive('serverError')
            ->once()
            ->with($message)
            ->andReturn($expectedResponse);

        $result = $this->controller->getResponse()->serverError($message);

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(500, $result->getStatusCode());
        $this->assertEquals(json_encode(['message' => $message]), $result->getContent());
    }

    #[Test]
    public function it_delegates_calls_to_service()
    {
        $param = 'test-parameter';
        $expectedResult = 'result-from-service';

        // Mock do método no serviço
        $this->mockService->shouldReceive('doSomething')
            ->once()
            ->with($param)
            ->andReturn($expectedResult);

        // Chamada ao método do controller
        $result = $this->controller->executeServiceMethod($param);

        // Verificação
        $this->assertEquals($expectedResult, $result);
    }

    #[Test]
    public function it_handles_service_exceptions_gracefully()
    {
        $param = 'bad-parameter';
        $exceptionMessage = 'Erro no serviço';

        // Mock do método no serviço para lançar exceção
        $this->mockService->shouldReceive('doSomething')
            ->once()
            ->with($param)
            ->andThrow(new \Exception($exceptionMessage));

        // Mock da resposta de erro
        $errorResponse = new JsonResponse(['message' => 'Erro interno do servidor'], 500);
        $this->mockResponse->shouldReceive('serverError')
            ->once()
            ->with('Erro interno do servidor')
            ->andReturn($errorResponse);

        // Executar o método e verificar a resposta
        $result = $this->controller->executeServiceMethod($param);

        // Verificar se a resposta é a esperada
        $this->assertSame($errorResponse, $result);
    }
}
