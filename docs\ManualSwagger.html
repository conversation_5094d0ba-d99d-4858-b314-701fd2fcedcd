<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Swagger (OpenAPI) para Laravel</title>
    <link rel="stylesheet" href="css/manual.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Swagger (OpenAPI) para Laravel</h1>
        <p>Guia completo para documentação de APIs RESTful usando Swagger/OpenAPI</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#instalacao">Instalação</a></li>
            <li><a href="#anotacoes-basicas">Anotações Básicas</a></li>
            <li><a href="#documentando-endpoints">Endpoints</a></li>
            <li><a href="#modelos">Modelos</a></li>
            <li><a href="#autenticacao">Autenticação</a></li>
            <li><a href="#exemplos">Exemplos</a></li>
            <li><a href="#interface">Interface UI</a></li>
            <li><a href="#automacao">Automação</a></li>
            <li><a href="#boas-praticas">Boas Práticas</a></li>
            <li><a href="#conclusao">Conclusão</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução ao Swagger/OpenAPI</a></li>
            <li><a href="#instalacao">2. Instalação e Configuração</a></li>
            <li><a href="#anotacoes-basicas">3. Anotações Básicas</a></li>
            <li><a href="#documentando-endpoints">4. Documentando Endpoints</a>
                <ul>
                    <li><a href="#endpoints-get">4.1. Endpoints GET</a></li>
                    <li><a href="#endpoints-post">4.2. Endpoints POST</a></li>
                    <li><a href="#endpoints-microsservicos">4.3. Endpoints em Microsserviços</a></li>
                </ul>
            </li>
            <li><a href="#modelos">5. Documentando Modelos e Esquemas</a>
                <ul>
                    <li><a href="#modelos-entidades">5.1. Modelos de Entidades</a></li>
                    <li><a href="#modelos-respostas">5.2. Esquemas de Respostas</a></li>
                </ul>
            </li>
            <li><a href="#autenticacao">6. Documentando Autenticação</a>
                <ul>
                    <li><a href="#autenticacao-esquemas">6.1. Esquemas de Segurança</a></li>
                    <li><a href="#autenticacao-endpoints">6.2. Endpoints de Autenticação</a></li>
                </ul>
            </li>
            <li><a href="#exemplos">7. Exemplos de Requisições e Respostas</a></li>
            <li><a href="#interface">8. Interface UI do Swagger</a></li>
            <li><a href="#automacao">9. Automação da Documentação</a></li>
            <li><a href="#boas-praticas">10. Boas Práticas</a></li>
            <li><a href="#conclusao">11. Conclusão</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução ao Swagger/OpenAPI</h2>
        <p class="intro-text">Swagger (agora oficialmente chamado OpenAPI) é uma especificação para descrever, produzir,
            consumir e
            visualizar APIs REST. A documentação de API é crucial para qualquer projeto, especialmente em
            arquiteturas orientadas a serviços ou microsserviços como a nossa.</p>

        <div class="key-points">
            <h3>Por que documentar APIs?</h3>
            <ul>
                <li>Facilita o entendimento da API para novos desenvolvedores</li>
                <li>Serve como contrato entre front-end e back-end</li>
                <li>Permite testes interativos de endpoints</li>
                <li>Pode gerar automaticamente SDKs e clientes</li>
                <li>Melhora a manutenibilidade da API</li>
                <li>Reduz o tempo de integração entre microsserviços</li>
            </ul>
        </div>

        <p>O Swagger/OpenAPI consiste em:</p>
        <ul>
            <li><strong>Especificação OpenAPI:</strong> Um formato padrão para descrever APIs REST</li>
            <li><strong>Swagger UI:</strong> Uma interface visual para explorar e testar sua API</li>
            <li><strong>Swagger Editor:</strong> Um editor para criar e validar documentação OpenAPI</li>
            <li><strong>Swagger Codegen:</strong> Ferramenta para gerar clientes e servidores a partir das
                especificações</li>
        </ul>

        <div class="note">
            <p><strong>Nota:</strong> Este documento é complementar aos Manuais de Arquitetura, Implementação e
                Segurança.
                As diretrizes aqui apresentadas devem ser seguidas por todos os microsserviços da aplicação.</p>
        </div>
    </section>

    <section id="instalacao" class="manual-section">
        <h2>2. Instalação e Configuração</h2>
        <p>Para Laravel, usamos o pacote <code>darkaonline/l5-swagger</code>, que é baseado no
            <code>swagger-php</code> e fornece integração com o framework.
        </p>

        <section id="instalacao-passos" class="subsection">
            <h3>2.1. Passo a Passo</h3>

            <h4>Passo 1: Instalação</h4>
            <div class="code-block">
                <pre>
composer require darkaonline/l5-swagger</pre>
            </div>

            <h4>Passo 2: Publicar configuração</h4>
            <div class="code-block">
                <pre>
php artisan vendor:publish --provider "L5Swagger\L5SwaggerServiceProvider"</pre>
            </div>

            <p>Isso irá publicar:</p>
            <ul>
                <li>Arquivo de configuração: <code>config/l5-swagger.php</code></li>
                <li>Views para o Swagger UI</li>
            </ul>

            <h4>Passo 3: Configuração inicial</h4>
            <p>Edite o arquivo <code>config/l5-swagger.php</code> conforme necessário. Algumas configurações
                importantes:</p>

            <div class="code-block">
                <pre>
'documentations' => [
    'default' => [
        'api' => [
            'title' => 'API Documentation',
        ],
        'routes' => [
            'api' => 'api/documentation',
        ],
        'paths' => [
            'use_absolute_path' => env('L5_SWAGGER_USE_ABSOLUTE_PATH', true),
            'docs_json' => 'api-docs.json',
            'docs_yaml' => 'api-docs.yaml',
            'format_to_use_for_docs' => env('L5_FORMAT_TO_USE_FOR_DOCS', 'json'),
            'annotations' => [
                base_path('app'),
            ],
        ],
    ],
],</pre>
            </div>
        </section>

        <section id="instalacao-configuracao" class="subsection">
            <h3>2.2. Configuração para Arquitetura Modular</h3>
            <p>Para nossa arquitetura modular baseada em microsserviços, é importante configurar o Swagger para
                reconhecer todos os diretórios relevantes:</p>

            <div class="code-block">
                <pre>
'annotations' => [
    base_path('app/Http/Controllers'),
    base_path('app/Http/Sistema'),
    base_path('app/Models'),
    base_path('app/Http/Resources'),
    base_path('app/Http/Requests'),
],</pre>
            </div>

            <div class="best-practice">
                <h4>Configuração Recomendada</h4>
                <p>Para nosso projeto, recomendamos as seguintes configurações adicionais:</p>
                <pre>
'api' => [
    'title' => 'API do Sistema de Gestão',
    'description' => 'Documentação da API para o sistema de gestão com arquitetura modular baseada em microsserviços',
    'version' => '1.0.0',
    'contact' => [
        'name' => 'Equipe de Desenvolvimento',
        'email' => '<EMAIL>',
    ],
],

'security' => [
    'bearer_token' => [
        'type' => 'http',
        'scheme' => 'bearer',
        'bearerFormat' => 'JWT',
    ],
],</pre>
            </div>

            <div class="warning-box">
                <div class="warning-icon">⚠️</div>
                <div class="warning-content">
                    <p>Certifique-se de incluir todos os diretórios que contêm suas controllers, models, requests,
                        resources, etc. no array <code>annotations</code>, especialmente os diretórios de
                        microsserviços.</p>
                </div>
            </div>
        </section>
    </section>

    <section id="anotacoes-basicas" class="manual-section">
        <h2>3. Anotações Básicas</h2>
        <p>O Swagger usa anotações PHPDoc para gerar a documentação. Vamos começar com as anotações mais
            básicas.</p>

        <section id="anotacoes-configuracao" class="subsection">
            <h3>3.1. Configuração Geral da API</h3>
            <p>Crie um arquivo <code>app/Http/Controllers/Controller.php</code> ou outro arquivo para armazenar a
                configuração geral:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Info(
 *     title="API do Sistema de Gestão",
 *     version="1.0.0",
 *     description="API para o sistema de gestão com arquitetura modular baseada em microsserviços",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="Equipe de Desenvolvimento"
 *     ),
 *     @OA\License(
 *         name="Proprietário",
 *         url="https://www.empresa.com.br/termos"
 *     )
 * )
 * 
 * @OA\Server(
 *     description="Servidor de Desenvolvimento",
 *     url=L5_SWAGGER_CONST_HOST
 * )
 * 
 * @OA\Server(
 *     description="Servidor de Produção",
 *     url="https://api.empresa.com.br"
 * )
 * 
 * @OA\SecurityScheme(
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     securityScheme="bearerAuth"
 * )
 */</pre>
            </div>

            <div class="note">
                <p>O uso de <code>L5_SWAGGER_CONST_HOST</code> permite configurar o URL base da API através do
                    arquivo .env. Configure-o como <code>L5_SWAGGER_CONST_HOST=http://localhost:8000/api</code> para
                    desenvolvimento local.</p>
            </div>
        </section>

        <section id="anotacoes-estrutura" class="subsection">
            <h3>3.2. Estrutura das Anotações</h3>
            <p>As anotações do Swagger seguem uma estrutura hierárquica:</p>

            <ul>
                <li><strong>@OA\Info</strong> - Informações gerais sobre a API</li>
                <li><strong>@OA\Server</strong> - Servidores onde a API está disponível</li>
                <li><strong>@OA\SecurityScheme</strong> - Esquemas de segurança (autenticação)</li>
                <li><strong>@OA\Tag</strong> - Tags para agrupar endpoints</li>
                <li><strong>@OA\Schema</strong> - Definição de modelos/esquemas</li>
                <li><strong>@OA\Get, @OA\Post, etc.</strong> - Operações HTTP</li>
                <li><strong>@OA\Parameter</strong> - Parâmetros de requisição</li>
                <li><strong>@OA\RequestBody</strong> - Corpo da requisição</li>
                <li><strong>@OA\Response</strong> - Respostas possíveis</li>
                <li><strong>@OA\Property</strong> - Propriedades de objetos</li>
            </ul>

            <div class="best-practice">
                <h4>Definição de Tags</h4>
                <p>Para nossa arquitetura modular, é recomendável definir tags para cada microsserviço:</p>
                <pre>
/**
 * @OA\Tag(
 *     name="Autenticação",
 *     description="Endpoints para autenticação e gerenciamento de tokens"
 * )
 * 
 * @OA\Tag(
 *     name="Usuários",
 *     description="Operações relacionadas a usuários"
 * )
 * 
 * @OA\Tag(
 *     name="Financeiro - Transações",
 *     description="Gerenciamento de transações financeiras"
 * )
 * 
 * @OA\Tag(
 *     name="Produtos",
 *     description="Gerenciamento de produtos e estoque"
 * )
 */</pre>
            </div>
        </section>
    </section>

    <section id="documentando-endpoints" class="manual-section">
        <h2>4. Documentando Endpoints</h2>
        <p>Agora vamos documentar endpoints específicos em nossos controllers, seguindo nossa arquitetura
            modular.</p>

        <section id="endpoints-get" class="subsection">
            <h3>4.1. Endpoints GET</h3>
            <p>Exemplo de documentação para um endpoint GET:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Get(
 *     path="/api/usuarios",
 *     operationId="listarUsuarios",
 *     tags={"Usuários"},
 *     summary="Lista todos os usuários",
 *     description="Retorna uma lista paginada de todos os usuários do sistema",
 *     @OA\Parameter(
 *         name="page",
 *         in="query",
 *         description="Número da página",
 *         required=false,
 *         @OA\Schema(type="integer", default=1)
 *     ),
 *     @OA\Parameter(
 *         name="per_page",
 *         in="query",
 *         description="Resultados por página",
 *         required=false,
 *         @OA\Schema(type="integer", default=15)
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Listagem de usuários bem-sucedida",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(property="data", type="array", 
 *                 @OA\Items(ref="#/components/schemas/User")
 *             ),
 *             @OA\Property(property="meta", type="object", 
 *                 @OA\Property(property="current_page", type="integer", example=1),
 *                 @OA\Property(property="last_page", type="integer", example=5),
 *                 @OA\Property(property="per_page", type="integer", example=15),
 *                 @OA\Property(property="total", type="integer", example=75)
 *             ),
 *             @OA\Property(property="links", type="object")
 *         )
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Não autorizado",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="Unauthenticated.")
 *         )
 *     ),
 *     security={{"bearerAuth": {}}}
 * )
 */
public function index()
{
    // Código do controller
}</pre>
            </div>

            <div class="note">
                <p>O <code>operationId</code> deve ser único em toda a API. Recomendamos usar o formato
                    <code>verbo + Entidade</code>, como <code>listarUsuarios</code>, <code>obterProduto</code>, etc.
                </p>
            </div>
        </section>

        <section id="endpoints-post" class="subsection">
            <h3>4.2. Endpoints POST</h3>
            <p>Exemplo de documentação para um endpoint POST:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Post(
 *     path="/api/usuarios",
 *     operationId="criarUsuario",
 *     tags={"Usuários"},
 *     summary="Criar um novo usuário",
 *     description="Cria um novo usuário no sistema",
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\JsonContent(
 *             required={"name","email","password"},
 *             @OA\Property(property="name", type="string", example="João Silva"),
 *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *             @OA\Property(property="password", type="string", format="password", example="senha123"),
 *             @OA\Property(property="password_confirmation", type="string", format="password", example="senha123"),
 *         )
 *     ),
 *     @OA\Response(
 *         response=201,
 *         description="Usuário criado com sucesso",
 *         @OA\JsonContent(ref="#/components/schemas/User")
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Erro de validação",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="The given data was invalid."),
 *             @OA\Property(
 *                 property="errors",
 *                 type="object",
 *                 @OA\Property(property="email", type="array", @OA\Items(type="string", example="O campo email já está sendo utilizado."))
 *             )
 *         )
 *     ),
 *     security={{"bearerAuth": {}}}
 * )
 */
public function store(Request $request)
{
    // Código do controller
}</pre>
            </div>

            <div class="best-practice">
                <h4>Documentação de Códigos de Status</h4>
                <p>Documente todos os possíveis códigos de status que seu endpoint pode retornar:</p>
                <ul>
                    <li><strong>200</strong> - OK (Sucesso)</li>
                    <li><strong>201</strong> - Created (Recurso criado)</li>
                    <li><strong>204</strong> - No Content (Sucesso sem conteúdo)</li>
                    <li><strong>400</strong> - Bad Request (Requisição inválida)</li>
                    <li><strong>401</strong> - Unauthorized (Não autenticado)</li>
                    <li><strong>403</strong> - Forbidden (Não autorizado)</li>
                    <li><strong>404</strong> - Not Found (Recurso não encontrado)</li>
                    <li><strong>422</strong> - Unprocessable Entity (Erro de validação)</li>
                    <li><strong>500</strong> - Internal Server Error (Erro interno)</li>
                </ul>
            </div>
        </section>

        <section id="endpoints-microsservicos" class="subsection">
            <h3>4.3. Endpoints em Microsserviços</h3>
            <p>Para nossa arquitetura modular, é importante organizar os endpoints por microsserviço:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Post(
 *     path="/api/financeiro/transacoes",
 *     operationId="criarTransacao",
 *     tags={"Financeiro - Transações"},
 *     summary="Criar uma nova transação financeira",
 *     description="Registra uma nova transação financeira no sistema",
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\JsonContent(
 *             required={"tipo", "valor", "conta_id"},
 *             @OA\Property(property="tipo", type="string", enum={"entrada", "saida"}, example="entrada"),
 *             @OA\Property(property="valor", type="number", format="float", example=150.75),
 *             @OA\Property(property="conta_id", type="integer", example=1),
 *             @OA\Property(property="categoria_id", type="integer", example=3),
 *             @OA\Property(property="descricao", type="string", example="Pagamento de cliente")
 *         )
 *     ),
 *     @OA\Response(
 *         response=201,
 *         description="Transação criada com sucesso",
 *         @OA\JsonContent(ref="#/components/schemas/Transacao")
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Erro de validação",
 *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
 *     ),
 *     security={{"bearerAuth": {}}}
 * )
 */</pre>
            </div>

            <div class="tip-box">
                <div class="tip-icon">💡</div>
                <div class="tip-content">
                    <p>Use o prefixo do microsserviço nas tags (ex: "Financeiro - Transações") para organizar melhor
                        a documentação na interface do Swagger.</p>
                </div>
            </div>

            <p>Exemplo de documentação para um endpoint PUT:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Put(
 *     path="/api/produtos/{id}",
 *     operationId="atualizarProduto",
 *     tags={"Produtos"},
 *     summary="Atualizar um produto existente",
 *     description="Atualiza os dados de um produto existente no sistema",
 *     @OA\Parameter(
 *         name="id",
 *         in="path",
 *         required=true,
 *         description="ID do produto",
 *         @OA\Schema(type="integer")
 *     ),
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\JsonContent(
 *             @OA\Property(property="nome", type="string", example="Produto Atualizado"),
 *             @OA\Property(property="descricao", type="string", example="Nova descrição do produto"),
 *             @OA\Property(property="preco", type="number", format="float", example=99.90),
 *             @OA\Property(property="categoria_id", type="integer", example=2)
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Produto atualizado com sucesso",
 *         @OA\JsonContent(ref="#/components/schemas/Produto")
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Produto não encontrado",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="Produto não encontrado")
 *         )
 *     ),
 *     security={{"bearerAuth": {}}}
 * )
 */</pre>
            </div>
        </section>
    </section>

    <section id="modelos" class="manual-section">
        <h2>5. Documentando Modelos e Esquemas</h2>
        <p>É uma boa prática definir modelos (schemas) reutilizáveis para suas entidades de dados. Isso evita
            duplicação e facilita a manutenção.</p>

        <section id="modelos-entidades" class="subsection">
            <h3>5.1. Modelos de Entidades</h3>
            <p>Organize seus modelos de acordo com a estrutura de microsserviços:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Schema(
 *     schema="User",
 *     title="Usuário",
 *     description="Modelo de usuário do sistema",
 *     required={"id", "name", "email"},
 *     @OA\Property(property="id", type="integer", format="int64", example=1),
 *     @OA\Property(property="name", type="string", example="João Silva"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="email_verified_at", type="string", format="date-time", nullable=true),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time")
 * )
 */
class User extends Model
{
    // Código do modelo
}</pre>
            </div>

            <div class="code-block">
                <pre>
/**
 * @OA\Schema(
 *     schema="Transacao",
 *     title="Transação Financeira",
 *     description="Modelo de transação financeira",
 *     required={"id", "tipo", "valor", "conta_id"},
 *     @OA\Property(property="id", type="integer", format="int64", example=1),
 *     @OA\Property(property="tipo", type="string", enum={"entrada", "saida"}, example="entrada"),
 *     @OA\Property(property="valor", type="number", format="float", example=150.75),
 *     @OA\Property(property="conta_id", type="integer", example=1),
 *     @OA\Property(property="categoria_id", type="integer", nullable=true, example=3),
 *     @OA\Property(property="descricao", type="string", nullable=true, example="Pagamento de cliente"),
 *     @OA\Property(property="data_transacao", type="string", format="date-time"),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time")
 * )
 */
class Transacao extends Model
{
    // Código do modelo
}</pre>
            </div>

            <div class="best-practice">
                <h4>Tipos de Dados Comuns</h4>
                <ul>
                    <li><strong>integer</strong> - Número inteiro</li>
                    <li><strong>number</strong> - Número decimal (use format="float" ou format="double")</li>
                    <li><strong>string</strong> - Texto</li>
                    <li><strong>boolean</strong> - Valor booleano</li>
                    <li><strong>array</strong> - Array (use @OA\Items para definir o tipo dos itens)</li>
                    <li><strong>object</strong> - Objeto (use @OA\Property para definir as propriedades)</li>
                </ul>
                <p>Formatos especiais para strings:</p>
                <ul>
                    <li><strong>date</strong> - Data (YYYY-MM-DD)</li>
                    <li><strong>date-time</strong> - Data e hora (YYYY-MM-DDThh:mm:ssZ)</li>
                    <li><strong>password</strong> - Senha (será mascarada na UI)</li>
                    <li><strong>email</strong> - Endereço de e-mail</li>
                    <li><strong>uuid</strong> - UUID</li>
                    <li><strong>uri</strong> - URI</li>
                </ul>
            </div>
        </section>

        <section id="modelos-respostas" class="subsection">
            <h3>5.2. Esquemas de Respostas</h3>
            <p>Defina esquemas para respostas comuns em toda a API:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Schema(
 *     schema="ValidationError",
 *     title="Erro de Validação",
 *     description="Formato padrão para erros de validação",
 *     @OA\Property(property="message", type="string", example="Os dados fornecidos são inválidos."),
 *     @OA\Property(
 *         property="errors",
 *         type="object",
 *         @OA\Property(
 *             property="campo",
 *             type="array",
 *             @OA\Items(type="string", example="O campo é obrigatório.")
 *         )
 *     )
 * )
 */</pre>
            </div>

            <div class="code-block">
                <pre>
/**
 * @OA\Schema(
 *     schema="ApiResponse",
 *     title="Resposta Padrão da API",
 *     description="Formato padrão para respostas da API",
 *     @OA\Property(property="success", type="boolean", example=true),
 *     @OA\Property(property="message", type="string", example="Operação realizada com sucesso"),
 *     @OA\Property(property="data", type="object", nullable=true),
 *     @OA\Property(property="api_version", type="string", example="1.0"),
 *     @OA\Property(property="timestamp", type="string", format="date-time")
 * )
 */</pre>
            </div>
        </section>
    </section>

    <section id="autenticacao" class="manual-section">
        <h2>6. Documentando Autenticação</h2>
        <p>A documentação adequada da autenticação é crucial para que outros desenvolvedores entendam como
            acessar sua API.</p>

        <section id="autenticacao-esquemas" class="subsection">
            <h3>6.1. Esquemas de Segurança</h3>
            <p>Defina os esquemas de segurança utilizados na API:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\SecurityScheme(
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     securityScheme="bearerAuth"
 * )
 * 
 * @OA\SecurityScheme(
 *     type="apiKey",
 *     in="header",
 *     name="X-API-KEY",
 *     securityScheme="apiKeyAuth"
 * )
 */</pre>
            </div>
        </section>

        <section id="autenticacao-endpoints" class="subsection">
            <h3>6.2. Endpoints de Autenticação</h3>
            <p>Documente os endpoints de autenticação de forma clara:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Post(
 *     path="/api/auth/login",
 *     operationId="login",
 *     tags={"Autenticação"},
 *     summary="Login de usuário",
 *     description="Retorna um token JWT para autenticação",
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\JsonContent(
 *             required={"email","password"},
 *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *             @OA\Property(property="password", type="string", format="password", example="senhasegura123"),
 *             @OA\Property(property="device_name", type="string", example="iPhone de João")
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Login bem-sucedido",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(property="message", type="string", example="Login realizado com sucesso"),
 *             @OA\Property(
 *                 property="data",
 *                 type="object",
 *                 @OA\Property(property="token", type="string", example="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."),
 *                 @OA\Property(property="token_type", type="string", example="bearer"),
 *                 @OA\Property(property="expires_in", type="integer", example=3600),
 *                 @OA\Property(
 *                     property="user",
 *                     type="object",
 *                     @OA\Property(property="id", type="integer", example=1),
 *                     @OA\Property(property="name", type="string", example="João Silva"),
 *                     @OA\Property(property="email", type="string", example="<EMAIL>")
 *                 )
 *             ),
 *             @OA\Property(property="api_version", type="string", example="1.0"),
 *             @OA\Property(property="timestamp", type="string", format="date-time")
 *         )
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Credenciais inválidas",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=false),
 *             @OA\Property(property="message", type="string", example="As credenciais fornecidas estão incorretas"),
 *             @OA\Property(property="api_version", type="string", example="1.0"),
 *             @OA\Property(property="timestamp", type="string", format="date-time")
 *         )
 *     )
 * )
 */</pre>
            </div>

            <div class="code-block">
                <pre>
/**
 * @OA\Post(
 *     path="/api/auth/logout",
 *     operationId="logout",
 *     tags={"Autenticação"},
 *     summary="Logout de usuário",
 *     description="Revoga o token atual do usuário",
 *     @OA\Response(
 *         response=200,
 *         description="Logout bem-sucedido",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(property="message", type="string", example="Logout realizado com sucesso"),
 *             @OA\Property(property="api_version", type="string", example="1.0"),
 *             @OA\Property(property="timestamp", type="string", format="date-time")
 *         )
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Não autorizado",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="Unauthenticated.")
 *         )
 *     ),
 *     security={{"bearerAuth": {}}}
 * )
 */</pre>
            </div>

            <div class="warning-box">
                <div class="warning-icon">⚠️</div>
                <div class="warning-content">
                    <p>Evite expor informações sensíveis nos exemplos de resposta, como tokens reais ou senhas.</p>
                </div>
            </div>
        </section>
    </section>

    <section id="exemplos" class="manual-section">
        <h2>7. Exemplos de Requisições e Respostas</h2>
        <p>Fornecer exemplos detalhados de requisições e respostas melhora significativamente a experiência dos
            desenvolvedores que utilizam sua API.</p>

        <section id="exemplos-microsservicos" class="subsection">
            <h3>7.1. Exemplos para Microsserviços</h3>
            <p>Forneça exemplos específicos para cada microsserviço:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Get(
 *     path="/api/produtos",
 *     operationId="listarProdutos",
 *     tags={"Produtos"},
 *     summary="Lista produtos com filtros",
 *     @OA\Parameter(name="categoria_id", in="query", @OA\Schema(type="integer")),
 *     @OA\Parameter(name="preco_min", in="query", @OA\Schema(type="number")),
 *     @OA\Parameter(name="preco_max", in="query", @OA\Schema(type="number")),
 *     @OA\Parameter(name="em_estoque", in="query", @OA\Schema(type="boolean")),
 *     @OA\Response(
 *         response=200,
 *         description="Lista de produtos",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(property="message", type="string", example="Produtos listados com sucesso"),
 *             @OA\Property(
 *                 property="data",
 *                 type="array",
 *                 @OA\Items(ref="#/components/schemas/Produto")
 *             ),
 *             @OA\Property(
 *                 property="meta",
 *                 type="object",
 *                 @OA\Property(property="current_page", type="integer", example=1),
 *                 @OA\Property(property="last_page", type="integer", example=3),
 *                 @OA\Property(property="per_page", type="integer", example=15),
 *                 @OA\Property(property="total", type="integer", example=45)
 *             ),
 *             @OA\Property(property="api_version", type="string", example="1.0"),
 *             @OA\Property(property="timestamp", type="string", format="date-time")
 *         )
 *     ),
 *     security={{"bearerAuth": {}}}
 * )
 */</pre>
            </div>
        </section>

        <section id="exemplos-multiplos" class="subsection">
            <h3>7.2. Exemplos Múltiplos para Diferentes Cenários</h3>
            <p>Forneça exemplos para diferentes cenários de uso:</p>

            <div class="code-block">
                <pre>
/**
 * @OA\Get(
 *     path="/api/pedidos/{id}",
 *     operationId="obterPedido",
 *     tags={"Pedidos"},
 *     summary="Obtém detalhes de um pedido",
 *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
 *     @OA\Response(
 *         response=200,
 *         description="Detalhes do pedido",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(property="message", type="string", example="Pedido encontrado com sucesso"),
 *             @OA\Property(property="data", ref="#/components/schemas/Pedido"),
 *             @OA\Property(property="api_version", type="string", example="1.0"),
 *             @OA\Property(property="timestamp", type="string", format="date-time")
 *         ),
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Examples(
 *                 example="pedido_pendente",
 *                 summary="Pedido pendente",
 *                 value={
 *                     "success": true,
 *                     "message": "Pedido encontrado com sucesso",
 *                     "data": {
 *                         "id": 1,
 *                         "cliente_id": 5,
 *                         "status": "pendente",
 *                         "valor_total": 299.90,
 *                         "data_pedido": "2023-06-15T14:30:00Z",
 *                         "itens": {
 *                             {"produto_id": 123, "quantidade": 2, "valor_unitario": 149.95}
 *                         }
 *                     },
 *                     "api_version": "1.0",
 *                     "timestamp": "2023-06-15T14:35:12Z"
 *                 }
 *             ),
 *             @OA\Examples(
 *                 example="pedido_entregue",
 *                 summary="Pedido entregue",
 *                 value={
 *                     "success": true,
 *                     "message": "Pedido encontrado com sucesso",
 *                     "data": {
 *                         "id": 2,
 *                         "cliente_id": 8,
 *                         "status": "entregue",
 *                         "valor_total": 599.80,
 *                         "data_pedido": "2023-06-10T09:15:00Z",
 *                         "data_entrega": "2023-06-12T14:20:00Z",
 *                         "itens": {
 *                             {"produto_id": 456, "quantidade": 1, "valor_unitario": 599.80}
 *                         }
 *                     },
 *                     "api_version": "1.0",
 *                     "timestamp": "2023-06-15T14:35:12Z"
 *                 }
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Pedido não encontrado",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=false),
 *             @OA\Property(property="message", type="string", example="Pedido não encontrado"),
 *             @OA\Property(property="api_version", type="string", example="1.0"),
 *             @OA\Property(property="timestamp", type="string", format="date-time")
 *         )
 *     ),
 *     security={{"bearerAuth": {}}}
 * )
 */</pre>
            </div>

            <div class="best-practice">
                <h4>Dicas para Exemplos Eficazes</h4>
                <ul>
                    <li>Forneça exemplos realistas com dados que façam sentido</li>
                    <li>Inclua exemplos para diferentes cenários (sucesso, erro, casos especiais)</li>
                    <li>Use nomes descritivos para os exemplos</li>
                    <li>Mantenha consistência no formato dos exemplos em toda a API</li>
                    <li>Inclua todos os campos que podem aparecer na resposta</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="interface" class="manual-section">
        <h2>8. Interface UI do Swagger</h2>
        <p>O Swagger UI fornece uma interface interativa para explorar e testar sua API.</p>

        <section id="interface-acesso" class="subsection">
            <h3>8.1. Acessando o Swagger UI</h3>
            <p>Após configurar o Swagger, você pode acessar a interface UI através da URL:</p>

            <div class="code-block">
                <pre>http://seu-site.com/api/documentation</pre>
            </div>

            <p>Para gerar a documentação, execute:</p>

            <div class="code-block">
                <pre>php artisan l5-swagger:generate</pre>
            </div>

            <div class="note">
                <p>Por padrão, o Swagger UI está disponível apenas em ambientes não-produção. Para alterar isso,
                    configure a opção <code>routes.middleware</code> no arquivo <code>config/l5-swagger.php</code>.</p>
            </div>
        </section>

        <section id="interface-personalizacao" class="subsection">
            <h3>8.2. Personalização da Interface</h3>
            <p>Você pode personalizar a aparência e comportamento do Swagger UI:</p>

            <div class="code-block">
                <pre>
// Em config/l5-swagger.php
'defaults' => [
    'routes' => [
        // ...
    ],
    'paths' => [
        // ...
    ],
    'security' => [
        // ...
    ],
    'ui' => [
        'display' => [
            'doc_expansion' => 'none',
            'filter' => true,
        ],
        'authorization' => [
            'persist_authorization' => true,
        ],
    ],
],</pre>
            </div>

            <p>Para personalizar ainda mais, você pode publicar e modificar as views:</p>

            <div class="code-block">
                <pre>php artisan vendor:publish --tag=l5-swagger-views</pre>
            </div>

            <div class="best-practice">
                <h4>Opções de Personalização Recomendadas</h4>
                <ul>
                    <li><strong>doc_expansion</strong>: 'none' (todos fechados), 'list' (tags expandidas), 'full' (tudo
                        expandido)</li>
                    <li><strong>filter</strong>: true (habilita campo de busca)</li>
                    <li><strong>persist_authorization</strong>: true (mantém tokens entre recarregamentos)</li>
                    <li><strong>try_it_out_enabled</strong>: true (habilita o botão "Try it out" por padrão)</li>
                    <li><strong>operations_sort</strong>: 'alpha' (ordena operações alfabeticamente)</li>
                    <li><strong>tags_sort</strong>: 'alpha' (ordena tags alfabeticamente)</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="automacao" class="manual-section">
        <h2>9. Automação da Documentação</h2>
        <p>Automatizar a geração da documentação garante que ela esteja sempre atualizada.</p>

        <section id="automacao-ci" class="subsection">
            <h3>9.1. Integração com CI/CD</h3>
            <p>Integre a geração da documentação ao seu pipeline de CI/CD:</p>

            <div class="code-block">
                <pre>
# Exemplo para GitHub Actions
name: Generate API Documentation

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  generate-docs:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo
        coverage: none
        
    - name: Install Dependencies
      run: composer install --prefer-dist --no-progress
      
    - name: Generate Swagger Documentation
      run: php artisan l5-swagger:generate
      
    - name: Commit and Push Documentation
      uses: stefanzweifel/git-auto-commit-action@v4
      with:
        commit_message: "docs: update API documentation"
        file_pattern: public/docs/*</pre>
            </div>
        </section>

        <section id="automacao-hooks" class="subsection">
            <h3>9.2. Git Hooks</h3>
            <p>Use Git hooks para gerar a documentação automaticamente antes de commits ou pushes:</p>

            <div class="code-block">
                <pre>
# Em .git/hooks/pre-commit ou usando husky
#!/bin/sh
echo "Generating API documentation..."
php artisan l5-swagger:generate
git add public/docs/</pre>
            </div>
        </section>

        <section id="automacao-comandos" class="subsection">
            <h3>9.3. Comandos Personalizados</h3>
            <p>Crie comandos personalizados para facilitar a geração da documentação:</p>

            <div class="code-block">
                <pre>
// Em app/Console/Commands/GenerateApiDocs.php
namespace App\Console\Commands;

use Illuminate\Console\Command;

class GenerateApiDocs extends Command
{
    protected $signature = 'api:docs {--format=json}';
    protected $description = 'Generate API documentation';

    public function handle()
    {
        $format = $this->option('format');
        
        // Definir formato no .env temporariamente
        $this->setEnvironmentValue('L5_FORMAT_TO_USE_FOR_DOCS', $format);
        
        $this->info("Generating API documentation in {$format} format...");
        $this->call('l5-swagger:generate');
        
        $this->info('API documentation generated successfully!');
        $this->info('You can access it at: ' . config('app.url') . '/api/documentation');
        
        return Command::SUCCESS;
    }
    
    protected function setEnvironmentValue($key, $value)
    {
        // Implementação para alterar valor no .env temporariamente
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Automação</h4>
                <ul>
                    <li>Gere a documentação como parte do processo de build</li>
                    <li>Verifique a validade da documentação gerada</li>
                    <li>Mantenha versões anteriores da documentação para referência</li>
                    <li>Automatize a verificação de quebras de contrato da API</li>
                    <li>Considere usar ferramentas como Spectral para linting da documentação OpenAPI</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="boas-praticas" class="manual-section">
        <h2>10. Boas Práticas</h2>
        <p>Seguir boas práticas garante uma documentação de API de alta qualidade e útil.</p>

        <section id="boas-praticas-organizacao" class="subsection">
            <h3>10.1. Organização</h3>
            <ul>
                <li><strong>Agrupe endpoints por microsserviço</strong> usando tags consistentes</li>
                <li><strong>Use nomes descritivos</strong> para operações e parâmetros</li>
                <li><strong>Mantenha a documentação próxima ao código</strong> que ela descreve</li>
                <li><strong>Defina modelos reutilizáveis</strong> para evitar duplicação</li>
                <li><strong>Organize as tags</strong> em uma ordem lógica</li>
            </ul>

            <div class="code-block">
                <pre>
/**
 * @OA\Tag(
 *     name="1. Autenticação",
 *     description="Endpoints para autenticação e gerenciamento de tokens"
 * )
 * @OA\Tag(
 *     name="2. Usuários",
 *     description="Operações relacionadas a usuários"
 * )
 * @OA\Tag(
 *     name="3. Produtos",
 *     description="Gerenciamento de produtos"
 * )
 */</pre>
            </div>
        </section>

        <section id="boas-praticas-descricoes" class="subsection">
            <h3>10.2. Descrições Claras</h3>
            <ul>
                <li><strong>Forneça descrições detalhadas</strong> para endpoints, parâmetros e respostas</li>
                <li><strong>Explique casos de uso</strong> e comportamentos esperados</li>
                <li><strong>Documente limitações e restrições</strong> (ex: rate limits, tamanhos máximos)</li>
                <li><strong>Use linguagem consistente</strong> em toda a documentação</li>
                <li><strong>Inclua exemplos realistas</strong> para facilitar o entendimento</li>
            </ul>

            <div class="code-block">
                <pre>
/**
 * @OA\Post(
 *     path="/api/usuarios",
 *     summary="Criar um novo usuário",
 *     description="Cria um novo usuário no sistema. O email deve ser único e a senha deve atender aos requisitos de segurança (mínimo 8 caracteres, incluindo letras maiúsculas, minúsculas, números e símbolos). Após a criação, um email de verificação será enviado para o endereço fornecido.",
 *     // ...
 * )
 */</pre>
            </div>
        </section>

        <section id="boas-praticas-erros" class="subsection">
            <h3>10.3. Documentação de Erros</h3>
            <ul>
                <li><strong>Documente todos os possíveis códigos de erro</strong> para cada endpoint</li>
                <li><strong>Forneça exemplos de respostas de erro</strong> com mensagens claras</li>
                <li><strong>Use códigos de status HTTP apropriados</strong> e consistentes</li>
                <li><strong>Explique como resolver erros comuns</strong> quando aplicável</li>
            </ul>

            <div class="code-block">
                <pre>
/**
 * @OA\Response(
 *     response=400,
 *     description="Requisição inválida",
 *     @OA\JsonContent(
 *         @OA\Property(property="success", type="boolean", example=false),
 *         @OA\Property(property="message", type="string", example="Parâmetros inválidos"),
 *         @OA\Property(
 *             property="errors",
 *             type="object",
 *             @OA\Property(
 *                 property="campo",
 *                 type="array",
 *                 @OA\Items(type="string", example="O campo deve ser um número inteiro positivo.")
 *             )
 *         ),
 *         @OA\Property(property="api_version", type="string", example="1.0"),
 *         @OA\Property(property="timestamp", type="string", format="date-time")
 *     )
 * )
 */</pre>
            </div>
        </section>

        <section id="boas-praticas-versionamento" class="subsection">
            <h3>10.4. Versionamento</h3>
            <ul>
                <li><strong>Documente claramente a versão da API</strong> que está sendo descrita</li>
                <li><strong>Mantenha documentação para versões anteriores</strong> quando aplicável</li>
                <li><strong>Indique endpoints obsoletos</strong> e suas alternativas</li>
                <li><strong>Use tags de versão</strong> para diferenciar endpoints de diferentes versões</li>
            </ul>

            <div class="code-block">
                <pre>
/**
 * @OA\Get(
 *     path="/api/v1/produtos",
 *     operationId="listarProdutosV1",
 *     tags={"Produtos (v1)"},
 *     // ...
 * )
 * 
 * @OA\Get(
 *     path="/api/v2/produtos",
 *     operationId="listarProdutosV2",
 *     tags={"Produtos (v2)"},
 *     // ...
 * )
 */</pre>
            </div>

            <div class="best-practice">
                <h4>Checklist de Qualidade da Documentação</h4>
                <ul>
                    <li>Todos os endpoints estão documentados?</li>
                    <li>Todos os parâmetros têm descrições claras?</li>
                    <li>Todos os possíveis códigos de status estão documentados?</li>
                    <li>Os exemplos são realistas e úteis?</li>
                    <li>A documentação está organizada logicamente?</li>
                    <li>Os modelos são reutilizados adequadamente?</li>
                    <li>A autenticação está claramente explicada?</li>
                    <li>As limitações e restrições estão documentadas?</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="conclusao" class="manual-section">
        <h2>11. Conclusão</h2>
        <p>Uma documentação de API bem elaborada é essencial para o sucesso de qualquer projeto, especialmente em uma
            arquitetura modular baseada em microsserviços. O Swagger/OpenAPI fornece uma estrutura robusta para criar
            documentação interativa e útil.</p>

        <p>Seguindo as diretrizes deste manual, você poderá:</p>
        <ul>
            <li>Criar documentação clara e consistente para todos os microsserviços</li>
            <li>Facilitar a integração entre diferentes partes do sistema</li>
            <li>Reduzir o tempo de onboarding para novos desenvolvedores</li>
            <li>Melhorar a qualidade geral da API através de um design mais cuidadoso</li>
            <li>Permitir testes interativos que aceleram o desenvolvimento e debugging</li>
        </ul>

        <p>Lembre-se de que a documentação da API é um documento vivo que deve evoluir junto com o código. Mantenha-a
            atualizada e use as ferramentas de automação para garantir que ela sempre reflita o estado atual da API.</p>

        <div class="note">
            <p><strong>Dica final:</strong> Considere a documentação da API como parte integrante do processo de
                desenvolvimento, não como uma tarefa adicional a ser feita depois. Documentar enquanto desenvolve leva a
                APIs mais bem projetadas e mais fáceis de usar.</p>
        </div>
    </section>

    <footer class="manual-footer">
        <p>Manual de Swagger (OpenAPI) para Laravel | Última atualização: Junho 2024</p>
    </footer>
</body>

</html>