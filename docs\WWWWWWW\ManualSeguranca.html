<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Segurança</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #2980b9;
            margin-top: 40px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        h3 {
            color: #3498db;
            margin-top: 25px;
        }

        .code-section {
            margin-bottom: 50px;
        }

        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', Courier, monospace;
            overflow-x: auto;
        }

        .example {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .example h4 {
            margin-top: 0;
            color: #2980b9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        table,
        th,
        td {
            border: 1px solid #ddd;
        }

        th {
            background-color: #f2f2f2;
            padding: 12px;
            text-align: left;
        }

        td {
            padding: 10px;
        }

        .best-practice {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }

        .bad-practice {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }

        .note {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
            padding: 10px;
        }

        footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }

        #sumario {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }

        #sumario ul {
            list-style-type: none;
            padding-left: 20px;
        }

        #sumario ul li {
            margin-bottom: 8px;
        }

        #sumario a {
            color: #2980b9;
            text-decoration: none;
        }

        #sumario a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <header>
        <h1 style="color: white; border-bottom: none;">Manual de Segurança</h1>
        <p>Guia para implementação de práticas de segurança na aplicação</p>
    </header>

    <section id="sumario">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#autenticacao">2. Autenticação</a>
                <ul>
                    <li><a href="#auth-password">2.1. Autenticação por Senha</a></li>
                    <li><a href="#auth-2fa">2.2. Autenticação de Dois Fatores (2FA)</a></li>
                    <li><a href="#auth-sanctum">2.3. Autenticação de API com Sanctum</a></li>
                    <li><a href="#auth-social">2.4. Autenticação com Provedores Sociais</a></li>
                </ul>
            </li>
            <li><a href="#autorizacao">3. Autorização</a>
                <ul>
                    <li><a href="#auth-gates">3.1. Gates e Policies</a></li>
                    <li><a href="#auth-roles">3.2. Controle de Acesso Baseado em Papéis (RBAC)</a></li>
                    <li><a href="#auth-middleware">3.3. Middleware de Autorização</a></li>
                </ul>
            </li>
            <li><a href="#protecao-dados">4. Proteção de Dados</a>
                <ul>
                    <li><a href="#dados-criptografia">4.1. Criptografia e Hashing</a></li>
                    <li><a href="#dados-sensiveis">4.2. Tratamento de Dados Sensíveis</a></li>
                    <li><a href="#dados-validacao">4.3. Validação e Sanitização de Entrada</a></li>
                </ul>
            </li>
            <li><a href="#csrf">5. Proteção CSRF</a></li>
            <li><a href="#xss">6. Prevenção XSS</a></li>
            <li><a href="#sql-injection">7. Prevenção SQL Injection</a></li>
            <li><a href="#headers">8. Cabeçalhos de Segurança HTTP</a></li>
            <li><a href="#rate-limiting">9. Rate Limiting</a></li>
            <li><a href="#seguranca-api">10. Segurança em APIs</a></li>
            <li><a href="#dependencias">11. Segurança de Dependências</a></li>
            <li><a href="#checklist">12. Checklist de Segurança</a></li>
        </ul>
    </section>

    <section id="introducao">
        <h2>1. Introdução</h2>
        <p>A segurança é um aspecto crítico de qualquer aplicação web moderna. Este manual estabelece os padrões e
            práticas recomendadas para implementação de segurança em nossa aplicação, protegendo dados sensíveis,
            prevenindo vulnerabilidades comuns e garantindo uma experiência segura para os usuários.</p>

        <p>As diretrizes aqui apresentadas são baseadas em práticas estabelecidas pela indústria, incluindo as
            recomendações da OWASP (Open Web Application Security Project) e adequadas às especificidades do framework
            Laravel.</p>

        <div class="note">
            <p><strong>Nota:</strong> Este documento é complementar aos Manuais de Arquitetura, Implementação e Logging.
                As diretrizes aqui apresentadas devem ser seguidas por todos os componentes da aplicação.</p>
        </div>

        <h3>Princípios de Segurança</h3>
        <ul>
            <li><strong>Defesa em Profundidade:</strong> Implementar múltiplas camadas de segurança</li>
            <li><strong>Privilégio Mínimo:</strong> Conceder apenas as permissões estritamente necessárias</li>
            <li><strong>Validação de Entrada:</strong> Nunca confiar em dados fornecidos pelo usuário</li>
            <li><strong>Codificação de Saída:</strong> Sempre sanitizar dados antes de exibi-los</li>
            <li><strong>Falha Segura:</strong> Em caso de erro, falhar de forma segura e sem revelar informações
                sensíveis</li>
            <li><strong>Segurança por Design:</strong> Considerar aspectos de segurança desde o início do projeto</li>
        </ul>
    </section>

    <section id="autenticacao">
        <h2>2. Autenticação</h2>
        <p>A autenticação é o processo de verificar a identidade de um usuário. Uma implementação robusta é essencial
            para proteger recursos e dados sensíveis.</p>

        <section id="auth-password">
            <h3>2.1. Autenticação por Senha</h3>
            <p>A autenticação baseada em senha deve seguir estas diretrizes:</p>

            <div class="best-practice">
                <h4>Requisitos para Senhas</h4>
                <ul>
                    <li>Comprimento mínimo de 8 caracteres</li>
                    <li>Combinação de letras maiúsculas, minúsculas, números e símbolos</li>
                    <li>Verificação contra senhas comuns e vazadas</li>
                    <li>Não permitir senhas que contenham informações pessoais do usuário</li>
                    <li>Armazenamento utilizando hashing seguro (bcrypt ou Argon2)</li>
                </ul>
            </div>

            <div class="code-block">
                // Validação de senha no formulário de registro
                public function rules()
                {
                return [
                'email' => 'required|string|email|max:255|unique:users',
                'password' => [
                'required',
                'string',
                'min:8', // Mínimo de 8 caracteres
                'confirmed', // Confirmação de senha
                'regex:/[a-z]/', // Pelo menos uma letra minúscula
                'regex:/[A-Z]/', // Pelo menos uma letra maiúscula
                'regex:/[0-9]/', // Pelo menos um número
                'regex:/[@$!%*#?&]/', // Pelo menos um caractere especial
                Password::defaults() // Regras padrão do Laravel (inclui verificação de exposição)
                ],
                ];
                }

                // Implementação do login
                public function authenticate(Request $request)
                {
                $credentials = $request->validate([
                'email' => ['required', 'email'],
                'password' => ['required'],
                ]);

                if (Auth::attempt($credentials, $request->boolean('remember'))) {
                $request->session()->regenerate();

                // Registrar evento de login bem-sucedido
                activity()
                ->causedBy(Auth::user())
                ->log('login');

                return redirect()->intended('dashboard');
                }

                // Registrar tentativa falha (mas sem revelar se o usuário existe)
                Log::warning('Falha na tentativa de login', [
                'email' => $request->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
                ]);

                return back()->withErrors([
                'email' => 'As credenciais fornecidas não correspondem aos nossos registros.',
                ])->onlyInput('email');
                }
            </div>

            <h4>Recuperação de Senha</h4>
            <p>O processo de recuperação de senha deve ser seguro e não revelar informações sensíveis:</p>
            <ul>
                <li>Utilizar tokens de uso único com expiração (padrão do Laravel)</li>
                <li>Limitar número de tentativas de recuperação</li>
                <li>Não revelar se o e-mail existe na base de dados</li>
                <li>Enviar notificação ao usuário quando a senha for alterada</li>
            </ul>

            <div class="code-block">
                // Utilização do sistema padrão de reset de senha do Laravel
                use Illuminate\Auth\Notifications\ResetPassword;
                use Illuminate\Support\Facades\Password;

                // No controller
                public function sendResetLinkEmail(Request $request)
                {
                $request->validate(['email' => 'required|email']);

                // Enviar link de reset independentemente de encontrar o usuário
                $status = Password::sendResetLink(
                $request->only('email')
                );

                // Logar a tentativa de recuperação
                Log::info('Solicitação de recuperação de senha', [
                'email' => $request->email,
                'ip' => $request->ip(),
                'success' => $status === Password::RESET_LINK_SENT
                ]);

                return $status === Password::RESET_LINK_SENT
                ? back()->with(['status' => __($status)])
                : back()->withErrors(['email' => __($status)]);
                }
            </div>
        </section>

        <section id="auth-2fa">
            <h3>2.2. Autenticação de Dois Fatores (2FA)</h3>
            <p>A autenticação de dois fatores adiciona uma camada extra de segurança, exigindo que o usuário forneça
                duas formas diferentes de identificação:</p>

            <div class="code-block">
                // Implementação de 2FA com Laravel Fortify
                use Laravel\Fortify\Features;

                // Em config/fortify.php
                'features' => [
                Features::registration(),
                Features::resetPasswords(),
                Features::emailVerification(),
                Features::updateProfileInformation(),
                Features::updatePasswords(),
                Features::twoFactorAuthentication([
                'confirm' => true,
                'confirmPassword' => true,
                ]),
                ],

                // Em app/Providers/FortifyServiceProvider.php
                use App\Actions\Fortify\EnableTwoFactorAuthentication;
                use App\Actions\Fortify\DisableTwoFactorAuthentication;

                public function boot()
                {
                Fortify::createUsersUsing(CreateNewUser::class);
                Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
                Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
                Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

                Fortify::registerView(function () {
                return view('auth.register');
                });

                Fortify::loginView(function () {
                return view('auth.login');
                });

                Fortify::twoFactorChallengeView(function () {
                return view('auth.two-factor-challenge');
                });
                }
            </div>

            <div class="note">
                <p>É recomendado implementar 2FA usando o algoritmo TOTP (Time-based One-time Password) com aplicativos
                    como Google Authenticator, Authy ou Microsoft Authenticator. Também é importante fornecer códigos de
                    recuperação para que os usuários não percam o acesso às suas contas.</p>
            </div>
        </section>

        <section id="auth-sanctum">
            <h3>2.3. Autenticação de API com Sanctum</h3>
            <p>Para APIs RESTful ou aplicativos SPA, use Laravel Sanctum para autenticação baseada em token:</p>

            <div class="code-block">
                // Autenticação para SPA e APIs móveis
                use Laravel\Sanctum\HasApiTokens;

                class User extends Authenticatable
                {
                use HasApiTokens, Notifiable;

                // ...
                }

                // No controller de login da API
                public function login(Request $request)
                {
                $request->validate([
                'email' => 'required|email',
                'password' => 'required',
                'device_name' => 'required',
                ]);

                $user = User::where('email', $request->email)->first();

                if (! $user || ! Hash::check($request->password, $user->password)) {
                Log::warning('Falha na tentativa de login via API', [
                'email' => $request->email,
                'ip' => $request->ip(),
                'device_name' => $request->device_name
                ]);

                return response()->json([
                'status' => 'error',
                'message' => 'Credenciais inválidas'
                ], 401);
                }

                // Criar token com escopos apropriados baseados nas permissões do usuário
                $token = $user->createToken($request->device_name, $user->getPermissionScopes());

                Log::info('Login via API realizado com sucesso', [
                'user_id' => $user->id,
                'email' => $user->email,
                'ip' => $request->ip(),
                'device_name' => $request->device_name
                ]);

                return response()->json([
                'status' => 'success',
                'message' => 'Autenticação realizada com sucesso',
                'data' => [
                'token' => $token->plainTextToken
                ]
                ]);
                }
            </div>

            <h4>Proteção de Rotas com Sanctum</h4>

            <div class="code-block">
                // Em routes/api.php
                Route::middleware('auth:sanctum')->group(function () {
                Route::get('/user', function (Request $request) {
                return $request->user();
                });

                // Rotas que exigem um escopo específico
                Route::middleware(['ability:manage-accounts'])->group(function () {
                Route::apiResource('accounts', AccountController::class);
                });
                });

                // Revogação de token ao fazer logout
                public function logout(Request $request)
                {
                // Revogar token atual
                $request->user()->currentAccessToken()->delete();

                // OU revogar todos os tokens (logout em todos os dispositivos)
                // $request->user()->tokens()->delete();

                return response()->json([
                'status' => 'success',
                'message' => 'Logout realizado com sucesso'
                ]);
                }
            </div>
        </section>

        <section id="auth-social">
            <h3>2.4. Autenticação com Provedores Sociais</h3>
            <p>A autenticação social pode ser implementada utilizando Laravel Socialite:</p>

            <div class="code-block">
                // No config/services.php
                'github' => [
                'client_id' => env('GITHUB_CLIENT_ID'),
                'client_secret' => env('GITHUB_CLIENT_SECRET'),
                'redirect' => env('GITHUB_CALLBACK_URL'),
                ],

                'google' => [
                'client_id' => env('GOOGLE_CLIENT_ID'),
                'client_secret' => env('GOOGLE_CLIENT_SECRET'),
                'redirect' => env('GOOGLE_CALLBACK_URL'),
                ],

                // No controller de autenticação social
                public function redirectToProvider($provider)
                {
                if (!in_array($provider, ['github', 'google', 'facebook'])) {
                abort(404);
                }

                return Socialite::driver($provider)->redirect();
                }

                public function handleProviderCallback($provider)
                {
                try {
                $socialUser = Socialite::driver($provider)->user();
                } catch (\Exception $e) {
                Log::error("Erro na autenticação social com {$provider}", [
                'error' => $e->getMessage()
                ]);

                return redirect()->route('login')
                ->withErrors(['error' => 'Ocorreu um erro na autenticação. Por favor, tente novamente.']);
                }

                // Buscar usuário pelo ID social ou email
                $user = User::where("{$provider}_id", $socialUser->getId())
                ->orWhere('email', $socialUser->getEmail())
                ->first();

                // Se o usuário não existir, crie um novo
                if (!$user) {
                $user = User::create([
                'name' => $socialUser->getName(),
                'email' => $socialUser->getEmail(),
                "{$provider}_id" => $socialUser->getId(),
                'password' => Hash::make(Str::random(16)), // Senha aleatória forte
                'email_verified_at' => now(),
                ]);
                } else if (!$user->{"{$provider}_id"}) {
                // Atualizar ID social se o usuário foi encontrado apenas pelo email
                $user->{"{$provider}_id"} = $socialUser->getId();
                $user->save();
                }

                // Autenticar o usuário
                Auth::login($user, true);

                Log::info("Login via {$provider} realizado com sucesso", [
                'user_id' => $user->id,
                'email' => $user->email
                ]);

                return redirect()->intended('/dashboard');
                }
            </div>

            <div class="warning">
                <p>Ao implementar autenticação social, sempre verifique a integridade do e-mail e outros dados
                    retornados pelo provedor. Em alguns casos, pode ser necessário solicitar permissões adicionais para
                    obter o e-mail verificado do usuário.</p>
            </div>
        </section>
    </section>

    <section id="autorizacao">
        <h2>3. Autorização</h2>
        <p>A autorização determina o que um usuário autenticado pode fazer dentro do sistema. O Laravel fornece vários
            mecanismos para implementar controle de acesso granular.</p>

        <section id="auth-gates">
            <h3>3.1. Gates e Policies</h3>
            <p>Gates e Policies permitem definir regras claras de autorização para diferentes partes da aplicação:</p>

            <div class="code-block">
                // Definindo Gates em AuthServiceProvider
                public function boot()
                {
                $this->registerPolicies();

                // Gate simples
                Gate::define('view-dashboard', function (User $user) {
                return $user->role === 'admin' || $user->role === 'manager';
                });

                // Gate com parâmetros adicionais
                Gate::define('update-post', function (User $user, Post $post) {
                return $user->id === $post->user_id || $user->hasRole('editor');
                });
                }

                // Usando Gates em controllers
                public function show()
                {
                if (Gate::denies('view-dashboard')) {
                abort(403, 'Acesso não autorizado.');
                }

                // OU
                $this->authorize('view-dashboard');

                return view('dashboard');
                }
            </div>

            <p>Para models específicos, as Policies fornecem uma abordagem orientada a objetos para autorização:</p>

            <div class="code-block">
                // Criar uma Policy
                php artisan make:policy PostPolicy --model=Post

                // Em app/Policies/PostPolicy.php
                public function update(User $user, Post $post)
                {
                return $user->id === $post->user_id || $user->hasPermissionTo('edit posts');
                }

                public function delete(User $user, Post $post)
                {
                return $user->id === $post->user_id || $user->hasPermissionTo('delete posts');
                }

                // Em controller
                public function edit(Post $post)
                {
                $this->authorize('update', $post);

                return view('posts.edit', compact('post'));
                }

                // Em blade templates
                @can('update', $post)
                <a href="{{ route('posts.edit', $post) }}">Editar</a>
                @endcan
            </div>
        </section>

        <section id="auth-roles">
            <h3>3.2. Controle de Acesso Baseado em Papéis (RBAC)</h3>
            <p>O RBAC permite definir papéis (roles) com conjuntos específicos de permissões:</p>

            <div class="best-practice">
                <h4>Implementação com Spatie Permission Package</h4>
                <p>Recomendamos o uso do pacote <code>spatie/laravel-permission</code> para implementar RBAC de forma
                    robusta.</p>
            </div>

            <div class="code-block">
                // Instalação
                composer require spatie/laravel-permission

                // Publicar migrações
                php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
                php artisan migrate

                // No modelo User
                use Spatie\Permission\Traits\HasRoles;

                class User extends Authenticatable
                {
                use HasRoles;

                // ...
                }

                // Criando papéis e permissões
                // Em um seeder ou durante o bootstrap da aplicação:
                $adminRole = Role::create(['name' => 'admin']);
                $editorRole = Role::create(['name' => 'editor']);
                $userRole = Role::create(['name' => 'user']);

                Permission::create(['name' => 'create posts']);
                Permission::create(['name' => 'edit posts']);
                Permission::create(['name' => 'delete posts']);
                Permission::create(['name' => 'publish posts']);
                Permission::create(['name' => 'manage users']);

                // Atribuir permissões aos papéis
                $adminRole->givePermissionTo(Permission::all());
                $editorRole->givePermissionTo(['create posts', 'edit posts', 'delete posts', 'publish posts']);
                $userRole->givePermissionTo(['create posts', 'edit posts']);

                // Uso em código
                if ($user->hasRole('admin')) {
                // Lógica para administradores
                }

                if ($user->can('delete posts')) {
                // Lógica para usuários com permissão para excluir posts
                }

                // Uso em middleware
                Route::group(['middleware' => ['role:admin']], function () {
                Route::get('/admin/dashboard', [AdminController::class, 'dashboard']);
                Route::resource('/admin/users', UserController::class);
                });

                Route::group(['middleware' => ['permission:edit posts']], function () {
                Route::get('/posts/{post}/edit', [PostController::class, 'edit']);
                Route::put('/posts/{post}', [PostController::class, 'update']);
                });

                // Uso em blade templates
                @role('admin')
                <li><a href="{{ route('admin.settings') }}">Configurações do Sistema</a></li>
                @endrole

                @can('publish posts')
                <button type="button" class="btn btn-publish">Publicar</button>
                @endcan
            </div>
        </section>

        <section id="auth-middleware">
            <h3>3.3. Middleware de Autorização</h3>
            <p>Os middlewares de autorização permitem proteger rotas com base em regras de negócio específicas:</p>

            <div class="code-block">
                // Criando um middleware personalizado
                php artisan make:middleware CheckSubscriptionStatus

                // Em app/Http/Middleware/CheckSubscriptionStatus.php
                public function handle($request, Closure $next)
                {
                if (auth()->check()) {
                $user = auth()->user();

                if ($user->subscription_status !== 'active') {
                // Registrar tentativa de acesso
                Log::warning('Usuário com assinatura inativa tentando acessar recurso premium', [
                'user_id' => $user->id,
                'subscription_status' => $user->subscription_status,
                'route' => $request->path()
                ]);

                if ($request->expectsJson()) {
                return response()->json([
                'status' => 'error',
                'message' => 'Assinatura inativa',
                'code' => 'SUBSCRIPTION_INACTIVE'
                ], 403);
                }

                return redirect()->route('subscription.expired');
                }
                }

                return $next($request);
                }

                // Registrando o middleware em app/Http/Kernel.php
                protected $routeMiddleware = [
                // ...
                'subscription.active' => \App\Http\Middleware\CheckSubscriptionStatus::class,
                ];

                // Usando o middleware
                Route::middleware(['auth', 'subscription.active'])->group(function () {
                Route::get('/premium-content', [ContentController::class, 'premium']);
                });
            </div>

            <div class="note">
                <p>Combine vários middlewares para criar regras de acesso complexas e granulares. Sempre registre
                    tentativas de acesso não autorizado para detecção de potenciais abusos.</p>
            </div>
        </section>
    </section>

    <section id="protecao-dados">
        <h2>4. Proteção de Dados</h2>
        <p>A proteção adequada dos dados é fundamental para garantir a confidencialidade e integridade das informações
            sensíveis.</p>

        <section id="dados-criptografia">
            <h3>4.1. Criptografia e Hashing</h3>
            <p>Laravel fornece ferramentas para criptografar e fazer hash de dados sensíveis:</p>

            <div class="code-block">
                // Criptografar dados sensíveis (reversível)
                use Illuminate\Support\Facades\Crypt;

                // Criptografar
                $encryptedValue = Crypt::encrypt($sensitiveData);

                // Descriptografar
                try {
                $decryptedValue = Crypt::decrypt($encryptedValue);
                } catch (DecryptException $e) {
                // Tratar erro de descriptografia
                }

                // Hash de senhas (irreversível)
                use Illuminate\Support\Facades\Hash;

                $hashedPassword = Hash::make($password);

                // Verificar senha
                if (Hash::check($inputPassword, $user->password)) {
                // Senha válida
                }

                // Rehash caso necessário (se o algoritmo de hash foi atualizado)
                if (Hash::needsRehash($user->password)) {
                $user->password = Hash::make($inputPassword);
                $user->save();
                }
            </div>

            <div class="best-practice">
                <h4>Criptografia de Atributos no Modelo</h4>
                <p>Automatize a criptografia de campos sensíveis nos modelos Eloquent:</p>
                <div class="code-block">
                    // Em um modelo Eloquent
                    use App\Traits\HasEncryptedAttributes;

                    class Patient extends Model
                    {
                    use HasEncryptedAttributes;

                    /**
                    * Atributos que devem ser criptografados
                    */
                    protected $encrypted = [
                    'social_security_number',
                    'medical_record',
                    'insurance_data'
                    ];
                    }

                    // Implementação da trait HasEncryptedAttributes
                    namespace App\Traits;

                    use Illuminate\Support\Facades\Crypt;

                    trait HasEncryptedAttributes
                    {
                    /**
                    * Criptografa atributos antes de salvar no banco
                    */
                    public function setAttribute($key, $value)
                    {
                    if ($value !== null && in_array($key, $this->encrypted ?? [])) {
                    $value = Crypt::encrypt($value);
                    }

                    return parent::setAttribute($key, $value);
                    }

                    /**
                    * Descriptografa atributos ao acessá-los
                    */
                    public function getAttribute($key)
                    {
                    $value = parent::getAttribute($key);

                    if ($value !== null && in_array($key, $this->encrypted ?? [])) {
                    try {
                    return Crypt::decrypt($value);
                    } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
                    Log::warning("Falha ao descriptografar atributo: {$key}", [
                    'model' => get_class($this),
                    'id' => $this->getKey(),
                    'error' => $e->getMessage()
                    ]);

                    return null;
                    }
                    }

                    return $value;
                    }

                    /**
                    * Descritografa atributos ao converter para array/JSON
                    */
                    public function toArray()
                    {
                    $array = parent::toArray();

                    foreach ($this->encrypted ?? [] as $key) {
                    if (isset($array[$key])) {
                    try {
                    $array[$key] = Crypt::decrypt($array[$key]);
                    } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
                    $array[$key] = null;
                    }
                    }
                    }

                    return $array;
                    }
                    }
                </div>
        </section>

        <section id="dados-sensiveis">
            <h3>4.2. Tratamento de Dados Sensíveis</h3>
            <p>Os dados sensíveis requerem cuidados especiais em todas as etapas do processamento:</p>

            <div class="best-practice">
                <h4>Diretrizes para Dados Sensíveis</h4>
                <ul>
                    <li>Colete apenas os dados estritamente necessários (princípio da minimização)</li>
                    <li>Defina períodos de retenção apropriados e implemente rotinas de exclusão de dados obsoletos</li>
                    <li>Implemente controles de acesso granulares para dados sensíveis</li>
                    <li>Criptografe dados sensíveis em trânsito e em repouso</li>
                    <li>Mascare ou truncate dados sensíveis em logs e outputs de depuração</li>
                    <li>Utilize campos protegidos em modelos para evitar atribuição em massa</li>
                </ul>
            </div>

            <div class="code-block">
                // Proteção contra atribuição em massa em modelos
                class User extends Authenticatable
                {
                /**
                * Atributos que são atribuíveis em massa.
                */
                protected $fillable = [
                'name',
                'email',
                ];

                /**
                * Atributos que devem ser ocultos para arrays.
                */
                protected $hidden = [
                'password',
                'remember_token',
                'api_token',
                'social_security_number',
                ];

                /**
                * Atributos que devem ter tipo nativo.
                */
                protected $casts = [
                'email_verified_at' => 'datetime',
                'password' => 'hashed',
                ];
                }
            </div>

            <h4>Mascaramento de Dados em Logs</h4>

            <div class="code-block">
                // Helper para mascarar dados sensíveis em logs e saídas
                class SensitiveDataMasker
                {
                public static function maskCreditCard($number)
                {
                if (!$number) return null;

                // Manter apenas os primeiros e últimos 4 dígitos
                $length = strlen($number);
                if ($length <= 8) return str_repeat('*', $length); return substr($number, 0, 4) . str_repeat('*',
                    $length - 8) . substr($number, -4); } public static function maskEmail($email) { if (!$email ||
                    !strpos($email, '@' )) return $email; [$username, $domain]=explode('@', $email);
                    $maskedUsername=substr($username, 0, 2) . str_repeat('*', max(1, strlen($username) - 2)); return
                    $maskedUsername . '@' . $domain; } public static function maskDocument($document) { if (!$document)
                    return null; // Remove caracteres não numéricos $document=preg_replace('/\D/', '' , $document);
                    $length=strlen($document); // Manter os dois primeiros e últimos dígitos if ($length <=4) return
                    str_repeat('*', $length); return substr($document, 0, 2) . str_repeat('*', $length - 4) .
                    substr($document, -2); } } // Uso Log::info('Processando pagamento', [ 'user_id'=> $user->id,
                    'email' => SensitiveDataMasker::maskEmail($user->email),
                    'card' => SensitiveDataMasker::maskCreditCard($payment->card_number)
                    ]);
            </div>
        </section>

        <section id="dados-validacao">
            <h3>4.3. Validação e Sanitização de Entrada</h3>
            <p>Todo dado fornecido pelo usuário deve ser validado e sanitizado adequadamente:</p>

            <div class="warning">
                <p>Nunca confie em dados fornecidos pelo usuário. Sempre valide e sanitize toda entrada,
                    independentemente da sua origem.</p>
            </div>

            <div class="code-block">
                // Validação em um request personalizado
                class StoreUserRequest extends FormRequest
                {
                public function authorize()
                {
                return true; // ou lógica de autorização
                }

                public function rules()
                {
                return [
                'name' => ['required', 'string', 'max:255'],
                'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
                'document' => ['required', new DocumentRule],
                'phone' => ['required', 'string', 'min:10', 'max:15'],
                'birthdate' => ['required', 'date', 'before:today'],
                'profile_image' => ['nullable', 'image', 'max:2048'], // Max 2MB
                'address.street' => ['required', 'string', 'max:255'],
                'address.city' => ['required', 'string', 'max:100'],
                'address.state' => ['required', 'string', 'size:2'],
                'address.zip' => ['required', 'string', 'max:10'],
                ];
                }

                // Sanitização antes da validação
                protected function prepareForValidation()
                {
                if ($this->has('document')) {
                $this->merge([
                'document' => preg_replace('/\D/', '', $this->document)
                ]);
                }

                if ($this->has('phone')) {
                $this->merge([
                'phone' => preg_replace('/\D/', '', $this->phone)
                ]);
                }
                }
                }

                // Validação de regra personalizada para documentos (CPF/CNPJ)
                class DocumentRule implements Rule
                {
                public function passes($attribute, $value)
                {
                // Remove caracteres não numéricos
                $value = preg_replace('/\D/', '', $value);

                // Verifica se é CPF (11 dígitos)
                if (strlen($value) === 11) {
                return $this->validateCpf($value);
                }

                // Verifica se é CNPJ (14 dígitos)
                if (strlen($value) === 14) {
                return $this->validateCnpj($value);
                }

                return false;
                }

                public function message()
                {
                return 'O documento informado não é válido.';
                }

                private function validateCpf($cpf)
                {
                // Implementação da validação de CPF
                // ...
                }

                private function validateCnpj($cnpj)
                {
                // Implementação da validação de CNPJ
                // ...
                }
                }

                // Sanitização para saída HTML
                class SanitizationHelper
                {
                public static function sanitizeHtml($input)
                {
                // Exemplo usando HTMLPurifier
                $config = \HTMLPurifier_Config::createDefault();
                $config->set('HTML.Allowed', 'p,b,i,strong,em,br,ul,ol,li,a[href]');
                $purifier = new \HTMLPurifier($config);

                return $purifier->purify($input);
                }

                public static function toPlainText($input)
                {
                // Converter HTML para texto puro
                return htmlspecialchars(strip_tags($input));
                }
                }

                // Uso no controller
                public function store(StoreUserRequest $request)
                {
                // Dados já validados e sanitizados pelo FormRequest
                $validatedData = $request->validated();

                // Sanitizar conteúdo HTML, se necessário
                if (isset($validatedData['bio'])) {
                $validatedData['bio'] = SanitizationHelper::sanitizeHtml($validatedData['bio']);
                }

                $user = User::create($validatedData);

                return redirect()->route('users.show', $user)->with('success', 'Usuário criado com sucesso!');
                }
            </div>
        </section>
    </section>

    <section id="csrf">
        <h2>5. Proteção CSRF</h2>
        <p>O Laravel fornece proteção automática contra ataques CSRF (Cross-Site Request Forgery), que deve ser mantida
            ativa em todas as aplicações:</p>

        <div class="code-block">
            // Middleware de proteção CSRF é registrado por padrão em app/Http/Kernel.php
            protected $middlewareGroups = [
            'web' => [
            // ...
            \App\Http\Middleware\VerifyCsrfToken::class,
            // ...
            ],
            ];

            // Em formulários Blade
            <form method="POST" action="/profile">
                @csrf
                <!-- Campos do formulário -->
            </form>

            // Em requisições AJAX (utilizando JavaScript)
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            fetch('/api/update-profile', {
            method: 'POST',
            headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': token
            },
            body: JSON.stringify({
            name: 'Nome Atualizado'
            })
            });

            // Alternativa usando o axios (configuração inicial)
            window.axios.defaults.headers.common['X-CSRF-TOKEN'] =
            document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        </div>

        <div class="note">
            <p>Em APIs que usam autenticação por token (como APIs RESTful), a proteção CSRF pode ser desativada para
                essas rotas específicas, já que os tokens de API fornecem proteção contra esses tipos de ataques.</p>
        </div>

        <div class="code-block">
            // Exceções para a proteção CSRF em VerifyCsrfToken.php
            protected $except = [
            'api/*', // Não aplicar proteção CSRF para rotas de API autenticadas por token
            'webhook/*', // Webhooks externos não podem incluir tokens CSRF
            ];
        </div>

        <div class="best-practice">
            <h4>Boas Práticas para CSRF</h4>
            <ul>
                <li>Sempre use o middleware CSRF para rotas web que processam formulários</li>
                <li>Inclua o token CSRF em todos os formulários e requisições AJAX</li>
                <li>Use tokens CSRF com tempo de vida limitado para transações críticas</li>
                <li>Ao usar SPA com APIs, considere abordagens como cookies de SameSite="strict"</li>
                <li>Evite tornar dados sensíveis acessíveis por requisições GET</li>
            </ul>
        </div>
    </section>

    <section id="xss">
        <h2>6. Prevenção XSS</h2>
        <p>O Cross-Site Scripting (XSS) é uma vulnerabilidade que permite que atacantes injetem scripts maliciosos no
            conteúdo exibido para outros usuários:</p>

        <div class="best-practice">
            <h4>Prevenção de XSS</h4>
            <ul>
                <li>Use sempre <code>{{ $variable }}</code> em vez de <code>{!! $variable !!}</code> em templates Blade
                    (escape automático)</li>
                <li>Sanitize conteúdo HTML quando necessário permitir HTML seguro</li>
                <li>Implemente cabeçalhos de segurança adequados, como Content-Security-Policy</li>
                <li>Valide e sanitize todas as entradas do usuário</li>
                <li>Use atributos de cookie seguro como HttpOnly e SameSite</li>
            </ul>
        </div>

        <div class="code-block">
            // Exemplo de sanitização de HTML para permitir tags específicas
            use Illuminate\Support\Facades\Blade;

            Blade::directive('safeHtml', function ($expression) {
            return "
            <?php echo app('purifier')->purify($expression); ?>";
            });

            // No template:
            <div class="user-content">
                @safeHtml($post->content)
            </div>

            // Exemplo de implementação do HTMLPurifier
            // Primeiro, instalar via composer: composer require mews/purifier
            // ServiceProvider
            namespace App\Providers;

            use HTMLPurifier;
            use HTMLPurifier_Config;
            use Illuminate\Support\ServiceProvider;

            class HtmlPurifierServiceProvider extends ServiceProvider
            {
            public function register()
            {
            $this->app->singleton('purifier', function ($app) {
            $config = HTMLPurifier_Config::createDefault();

            // Tags e atributos permitidos
            $config->set('HTML.Allowed',
            'p,b,i,strong,em,br,ul,ol,li,a[href|title|target],img[src|alt|width|height],h1,h2,h3,h4,h5,blockquote,table,tr,td,th,thead,tbody');

            // Prevenir ataques CSS via attributes style
            $config->set('CSS.AllowedProperties', '');

            // Configurar target "_blank" para noopener e noreferrer automaticamente
            $config->set('HTML.TargetBlank', true);

            // Outras configurações...

            return new HTMLPurifier($config);
            });
            }
            }
        </div>

        <h3>6.1. Cabeçalhos de Segurança para XSS</h3>
        <p>Implemente cabeçalhos HTTP de segurança para mitigar ataques XSS:</p>

        <div class="code-block">
            // Middleware personalizado para cabeçalhos de segurança
            namespace App\Http\Middleware;

            use Closure;

            class SecurityHeaders
            {
            public function handle($request, Closure $next)
            {
            $response = $next($request);

            // Content Security Policy (CSP)
            $response->headers->set(
            'Content-Security-Policy',
            "default-src 'self'; " .
            "script-src 'self' https://cdn.jsdelivr.net https://www.google-analytics.com; " .
            "style-src 'self' https://cdn.jsdelivr.net 'unsafe-inline'; " .
            "img-src 'self' data: https://cdn.jsdelivr.net https://www.google-analytics.com; " .
            "font-src 'self' https://cdn.jsdelivr.net; " .
            "connect-src 'self' https://api.example.com https://www.google-analytics.com; " .
            "frame-src 'none'; " .
            "object-src 'none'; " .
            "base-uri 'self';"
            );

            // Prevenir MIME type sniffing
            $response->headers->set('X-Content-Type-Options', 'nosniff');

            // Habilitar proteção XSS do navegador
            $response->headers->set('X-XSS-Protection', '1; mode=block');

            // Evitar que o site seja carregado em iframe (clickjacking)
            $response->headers->set('X-Frame-Options', 'SAMEORIGIN');

            // Evitar vazamento de informações via Referer header
            $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

            // Definir tipo de conteúdo e encoding
            $response->headers->set('X-Permitted-Cross-Domain-Policies', 'none');

            return $response;
            }
            }

            // Registrar o middleware em app/Http/Kernel.php
            protected $middlewareGroups = [
            'web' => [
            // ...
            \App\Http\Middleware\SecurityHeaders::class,
            ],
            ];
        </div>
    </section>

    <section id="sql-injection">
        <h2>7. Prevenção SQL Injection</h2>
        <p>O Laravel oferece proteção automática contra SQL Injection através do ORM Eloquent e do Query Builder, mas é
            importante seguir as melhores práticas:</p>

        <div class="bad-practice">
            <h4>Práticas a Evitar</h4>
            <div class="code-block">
                // NUNCA faça isso - vulnerável a SQL injection
                $userId = $request->input('user_id');
                $results = DB::select(DB::raw("SELECT * FROM users WHERE id = $userId"));

                // NUNCA faça isso - ainda vulnerável
                $searchTerm = $request->input('search');
                $users = DB::table('users')
                ->whereRaw("name LIKE '%$searchTerm%'")
                ->get();
            </div>
        </div>

        <div class="best-practice">
            <h4>Práticas Recomendadas</h4>
            <div class="code-block">
                // Use Eloquent sempre que possível
                $user = User::find($request->input('user_id'));

                // Use Query Builder com parâmetros bind
                $searchTerm = $request->input('search');
                $users = DB::table('users')
                ->where('name', 'LIKE', "%{$searchTerm}%")
                ->get();

                // Se precisar usar raw queries, use binding de parâmetros
                $userId = $request->input('user_id');
                $results = DB::select('SELECT * FROM users WHERE id = ?', [$userId]);

                // Para queries raw complexas, use named bindings
                $results = DB::select(
                'SELECT * FROM users WHERE email = :email OR username = :username',
                ['email' => $email, 'username' => $username]
                );
            </div>
        </div>

        <div class="note">
            <p>Sempre que possível, use o ORM Eloquent ou Query Builder do Laravel, que aplicam proteções automáticas
                contra SQL injection. Se precisar escrever SQL bruto, sempre use parâmetros bind para valores dinâmicos.
            </p>
        </div>

        <h3>7.1. Proteção em Ordenações e Filtros Dinâmicos</h3>
        <p>Ordenações e filtros dinâmicos controlados pelo usuário requerem cuidados adicionais:</p>

        <div class="code-block">
            // Ordenação segura
            public function index(Request $request)
            {
            $allowedColumns = ['name', 'created_at', 'email', 'status'];
            $column = in_array($request->input('sort'), $allowedColumns)
            ? $request->input('sort')
            : 'created_at';

            $direction = in_array($request->input('direction'), ['asc', 'desc'])
            ? $request->input('direction')
            : 'desc';

            return User::orderBy($column, $direction)->paginate(20);
            }

            // Filtros dinâmicos seguros
            public function index(Request $request)
            {
            $query = Product::query();

            // Lista de filtros permitidos
            $allowedFilters = [
            'category_id' => '=',
            'price_min' => '>=',
            'price_max' => '<=', 'status'=> '=',
                'name' => 'LIKE',
                ];

                foreach ($allowedFilters as $param => $operator) {
                if ($request->has($param) && $request->filled($param)) {
                $value = $request->input($param);

                // Para operador LIKE, ajustar o valor
                if ($operator === 'LIKE') {
                $value = '%' . $value . '%';
                }

                // Mapear para o campo correto no banco
                $field = $param;
                if ($param === 'price_min' || $param === 'price_max') {
                $field = 'price';
                }

                $query->where($field, $operator, $value);
                }
                }

                return $query->paginate(20);
                }
        </div>
    </section>

    <section id="headers">
        <h2>8. Cabeçalhos de Segurança HTTP</h2>
        <p>Os cabeçalhos HTTP de segurança ajudam a proteger sua aplicação contra diversos tipos de ataques:</p>

        <table>
            <thead>
                <tr>
                    <th>Cabeçalho</th>
                    <th>Descrição</th>
                    <th>Exemplo</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Content-Security-Policy</td>
                    <td>Define fontes confiáveis para recursos</td>
                    <td><code>default-src 'self'; script-src 'self' https://cdn.jsdelivr.net;</code></td>
                </tr>
                <tr>
                    <td>X-Content-Type-Options</td>
                    <td>Previne MIME type sniffing</td>
                    <td><code>nosniff</code></td>
                </tr>
                <tr>
                    <td>X-XSS-Protection</td>
                    <td>Habilita filtros XSS em browsers antigos</td>
                    <td><code>1; mode=block</code></td>
                </tr>
                <tr>
                    <td>X-Frame-Options</td>
                    <td>Previne clickjacking</td>
                    <td><code>SAMEORIGIN</code></td>
                </tr>
                <tr>
                    <td>Referrer-Policy</td>
                    <td>Controla informações de referência enviadas</td>
                    <td><code>strict-origin-when-cross-origin</code></td>
                </tr>
                <tr>
                    <td>Strict-Transport-Security (HSTS)</td>
                    <td>Força conexões HTTPS</td>
                    <td><code>max-age=31536000; includeSubDomains</code></td>
                </tr>
                <tr>
                    <td>Permissions-Policy</td>
                    <td>Controla quais recursos o navegador pode usar</td>
                    <td><code>camera=(), microphone=(), geolocation=(self)</code></td>
                </tr>
            </tbody>
        </table>

        <div class="code-block">
            // Middleware para configuração do HSTS
            namespace App\Http\Middleware;

            use Closure;

            class HttpsProtocol
            {
            public function handle($request, Closure $next)
            {
            $response = $next($request);

            if (!app()->environment('local')) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
            }

            return $response;
            }
            }

            // Adicione ao Kernel como middleware global
            protected $middleware = [
            // ...
            \App\Http\Middleware\HttpsProtocol::class,
            ];
        </div>

        <div class="note">
            <p>Recomendamos utilizar pacotes como <code>bepsvpt/secure-headers</code> para facilitar a implementação e
                manutenção desses cabeçalhos de segurança.</p>
        </div>
    </section>

    <section id="rate-limiting">
        <h2>9. Rate Limiting</h2>
        <p>O rate limiting protege sua aplicação contra ataques de força bruta, scraping, e sobrecarga do servidor:</p>

        <div class="code-block">
            // Configuração de rate limiting no Laravel
            namespace App\Providers;

            use Illuminate\Cache\RateLimiting\Limit;
            use Illuminate\Http\Request;
            use Illuminate\Support\Facades\RateLimiter;
            use Illuminate\Support\ServiceProvider;

            class RouteServiceProvider extends ServiceProvider
            {
            // ...

            protected function configureRateLimiting()
            {
            // Limite global para todas as requisições API
            RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->ip());
            });

            // Limite para tentativas de login
            RateLimiter::for('login', function (Request $request) {
            // Identificação mais precisa combinando IP e email
            $key = $request->ip() . '|' . $request->input('email');

            return Limit::perMinute(5)->by($key);
            });

            // Limite para redefinição de senha
            RateLimiter::for('password-reset', function (Request $request) {
            return Limit::perHour(3)->by($request->ip());
            });

            // Limite para requisições à API específica por usuário autenticado
            RateLimiter::for('api-user', function (Request $request) {
            $user = $request->user();

            // Taxa diferente baseada no tipo de usuário
            if ($user && $user->isPremium()) {
            return Limit::perMinute(120)->by($user->id);
            }

            return $user
            ? Limit::perMinute(60)->by($user->id)
            : Limit::perMinute(30)->by($request->ip());
            });
            }
            }

            // Aplicando o limitador em rotas
            Route::middleware(['auth:sanctum', 'throttle:api-user'])
            ->prefix('api')
            ->group(function () {
            Route::get('/user-data', [UserDataController::class, 'index']);
            });

            // Limitadores em rotas de autenticação
            Route::post('/login', [AuthController::class, 'login'])
            ->middleware('throttle:login');

            Route::post('/forgot-password', [PasswordResetController::class, 'sendResetLink'])
            ->middleware('throttle:password-reset');
        </div>

        <h3>9.1. Respostas para Rate Limiting</h3>
        <p>Personalize as respostas para quando o limite for excedido:</p>

        <div class="code-block">
            // Em App\Exceptions\Handler.php
            public function register()
            {
            $this->renderable(function (ThrottleRequestsException $e, $request) {
            if ($request->expectsJson()) {
            return response()->json([
            'status' => 'error',
            'message' => 'Você excedeu o limite de requisições permitidas. Por favor, tente novamente mais tarde.',
            'code' => 'RATE_LIMIT_EXCEEDED'
            ], 429);
            }
            });
            }

            // Em um middleware personalizado
            public function handle($request, Closure $next)
            {
            // Tenta executar a requisição com o middleware de throttle
            try {
            return app(ThrottleRequests::class)
            ->handle($request, $next, 'api', 60);
            } catch (ThrottleRequestsException $exception) {
            // Registrar tentativa de abuso
            Log::warning('Rate limit excedido', [
            'ip' => $request->ip(),
            'user_id' => $request->user()->id ?? null,
            'route' => $request->path(),
            'headers' => $request->headers->all()
            ]);

            return response()->json([
            'status' => 'error',
            'message' => 'Limite de requisições excedido',
            'retry_after' => $exception->getHeaders()['Retry-After'] ?? null,
            ], 429);
            }
            }
        </div>
    </section>

    <section id="seguranca-api">
        <h2>10. Segurança em APIs</h2>
        <p>As APIs requerem considerações especiais de segurança para proteger dados e serviços:</p>

        <div class="best-practice">
            <h4>Melhores Práticas para APIs</h4>
            <ul>
                <li>Use HTTPS para todas as comunicações</li>
                <li>Implemente autenticação robusta (OAuth 2.0, JWT, ou tokens de API)</li>
                <li>Defina escopos precisos para autorizações (principle of least privilege)</li>
                <li>Aplique rate limiting e throttling para prevenir abusos</li>
                <li>Use cabeçalhos HTTP de segurança apropriados</li>
                <li>Implemente validação rigorosa de entradas</li>
                <li>Retorne códigos de status HTTP apropriados</li>
                <li>Registre todas as chamadas de API para auditoria</li>
            </ul>
        </div>

        <h3>10.1. Autenticação Stateless com JWT</h3>
        <p>Para APIs stateless, JSON Web Tokens (JWT) podem ser uma opção de autenticação adequada:</p>

        <div class="code-block">
            // Usando o pacote tymon/jwt-auth
            // Instalação: composer require tymon/jwt-auth

            // Configuração do provider no config/app.php
            'providers' => [
            ...
            Tymon\JWTAuth\Providers\LaravelServiceProvider::class,
            ],

            'aliases' => [
            ...
            'JWTAuth' => Tymon\JWTAuth\Facades\JWTAuth::class,
            'JWTFactory' => Tymon\JWTAuth\Facades\JWTFactory::class,
            ],

            // Publicar configuração
            // php artisan vendor:publish --provider="Tymon\JWTAuth\Providers\LaravelServiceProvider"

            // Gerar chave secreta
            // php artisan jwt:secret

            // Implementar no modelo User
            use Tymon\JWTAuth\Contracts\JWTSubject;

            class User extends Authenticatable implements JWTSubject
            {
            // ...

            /**
            * Get the identifier that will be stored in the subject claim of the JWT.
            *
            * @return mixed
            */
            public function getJWTIdentifier()
            {
            return $this->getKey();
            }

            /**
            * Return a key value array, containing any custom claims to be added to the JWT.
            *
            * @return array
            */
            public function getJWTCustomClaims()
            {
            return [
            'user_type' => $this->type,
            'permissions' => $this->getAllPermissions()->pluck('name'),
            ];
            }
            }

            // Controller de Autenticação
            namespace App\Http\Controllers\Api;

            use App\Http\Controllers\Controller;
            use Illuminate\Http\Request;
            use Illuminate\Support\Facades\Auth;

            class AuthController extends Controller
            {
            /**
            * Criar um novo AuthController instance.
            *
            * @return void
            */
            public function __construct()
            {
            $this->middleware('auth:api', ['except' => ['login']]);
            }

            /**
            * Obter token JWT via credenciais.
            *
            * @return \Illuminate\Http\JsonResponse
            */
            public function login(Request $request)
            {
            $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
            ]);

            if (! $token = Auth::guard('api')->attempt($credentials)) {
            Log::warning('Falha na tentativa de login API', [
            'email' => $request->email,
            'ip' => $request->ip()
            ]);

            return response()->json([
            'status' => 'error',
            'message' => 'Credenciais inválidas'
            ], 401);
            }

            Log::info('Login via API realizado', [
            'user_id' => Auth::guard('api')->user()->id,
            'ip' => $request->ip()
            ]);

            return $this->respondWithToken($token);
            }

            /**
            * Obter o usuário autenticado.
            *
            * @return \Illuminate\Http\JsonResponse
            */
            public function me()
            {
            return response()->json([
            'status' => 'success',
            'data' => Auth::guard('api')->user()
            ]);
            }

            /**
            * Deslogar usuário (invalidar o token).
            *
            * @return \Illuminate\Http\JsonResponse
            */
            public function logout()
            {
            Auth::guard('api')->logout();

            return response()->json([
            'status' => 'success',
            'message' => 'Logout realizado com sucesso'
            ]);
            }

            /**
            * Atualizar token.
            *
            * @return \Illuminate\Http\JsonResponse
            */
            public function refresh()
            {
            return $this->respondWithToken(Auth::guard('api')->refresh());
            }

            /**
            * Formatação padronizada para a resposta de token.
            *
            * @param string $token
            *
            * @return \Illuminate\Http\JsonResponse
            */
            protected function respondWithToken($token)
            {
            return response()->json([
            'status' => 'success',
            'data' => [
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => Auth::guard('api')->factory()->getTTL() * 60
            ]
            ]);
            }
            }
        </div>

        <div class="best-practice">
            <h4>Segurança com JWT</h4>
            <ul>
                <li>Defina tempos de expiração curtos para tokens (15-60 minutos)</li>
                <li>Implemente refresh tokens para melhor experiência de usuário</li>
                <li>Armazene a chave secreta JWT de forma segura e nunca a exponha</li>
                <li>Use algoritmos de assinatura seguros como RS256 (assimétrico) em vez de HS256 quando apropriado</li>
                <li>Inclua apenas as claims necessárias no payload para minimizar o tamanho do token</li>
                <li>Implemente uma lista de bloqueio para tokens revogados</li>
            </ul>
        </div>

        <div class="code-block">
            // Implementação de uma blacklist para tokens JWT
            // Em um middleware personalizado:
            namespace App\Http\Middleware;

            use Closure;
            use Illuminate\Support\Facades\Cache;
            use Tymon\JWTAuth\Facades\JWTAuth;
            use Tymon\JWTAuth\Exceptions\TokenExpiredException;
            use Tymon\JWTAuth\Exceptions\TokenInvalidException;

            class CheckBlacklistedToken
            {
            public function handle($request, Closure $next)
            {
            try {
            $token = JWTAuth::parseToken();
            $payload = $token->getPayload();
            $jti = $payload->get('jti'); // ID único do token

            // Verificar se o token está na blacklist
            if (Cache::has('blacklist_token_' . $jti)) {
            return response()->json([
            'status' => 'error',
            'message' => 'Token revogado',
            'code' => 'TOKEN_BLACKLISTED'
            ], 401);
            }

            } catch (TokenExpiredException $e) {
            return response()->json([
            'status' => 'error',
            'message' => 'Token expirado',
            'code' => 'TOKEN_EXPIRED'
            ], 401);
            } catch (TokenInvalidException $e) {
            return response()->json([
            'status' => 'error',
            'message' => 'Token inválido',
            'code' => 'TOKEN_INVALID'
            ], 401);
            } catch (\Exception $e) {
            return response()->json([
            'status' => 'error',
            'message' => 'Autorização não encontrada',
            'code' => 'AUTH_NOT_FOUND'
            ], 401);
            }

            return $next($request);
            }
            }

            // No controller de logout
            public function logout()
            {
            $token = JWTAuth::getToken();
            $payload = JWTAuth::getPayload($token);
            $jti = $payload->get('jti');

            // Adicionar o token à blacklist pelo tempo que resta de sua validade
            $exp = $payload->get('exp') - time();
            Cache::put('blacklist_token_' . $jti, true, $exp);

            Auth::guard('api')->logout();

            return response()->json([
            'status' => 'success',
            'message' => 'Logout realizado com sucesso'
            ]);
            }
        </div>
    </section>

    <section id="dependencias">
        <h2>11. Segurança de Dependências</h2>
        <p>As dependências de terceiros podem introduzir vulnerabilidades na sua aplicação:</p>

        <div class="best-practice">
            <h4>Gerenciamento Seguro de Dependências</h4>
            <ul>
                <li>Mantenha todas as dependências atualizadas regularmente</li>
                <li>Utilize ferramentas de análise de vulnerabilidades como GitHub Dependabot, Snyk ou npm audit</li>
                <li>Verifique a reputação e manutenção ativa dos pacotes antes de adicioná-los</li>
                <li>Bloqueie versões específicas de pacotes em vez de usar intervalos amplos</li>
                <li>Estabeleça um processo para verificação e atualização regular de dependências</li>
            </ul>
        </div>

        <div class="code-block">
            // No composer.json, evite intervalos amplos de versões
            {
            "require": {
            "php": "^8.1",
            "laravel/framework": "^10.0",
            "specific-package/name": "1.2.3",
            "other-package/name": "4.5.6"
            }
            }

            // Script para verificação de vulnerabilidades como parte do CI/CD
            {
            "scripts": {
            "security-check": [
            "@php vendor/bin/security-checker security:check",
            "npm audit"
            ]
            }
            }
        </div>

        <h3>11.1. Auditorias de Segurança</h3>
        <p>Realize auditorias de segurança periódicas na sua aplicação:</p>

        <ul>
            <li><strong>Auditorias automatizadas:</strong> Use ferramentas como OWASP ZAP, Burp Suite ou outras para
                análise automatizada</li>
            <li><strong>Code reviews:</strong> Estabeleça processos de revisão de código com foco em segurança</li>
            <li><strong>Testes de penetração:</strong> Contrate serviços especializados para testes periódicos</li>
            <li><strong>Bug bounty:</strong> Considere programas de recompensa para descoberta de vulnerabilidades</li>
        </ul>

        <div class="code-block">
            // Exemplo de integração com GitHub Actions para verificação de dependências
            name: Security Checks

            on:
            push:
            branches: [ main ]
            pull_request:
            branches: [ main ]
            schedule:
            - cron: '0 0 * * 0' # Executa semanalmente

            jobs:
            security-check:
            runs-on: ubuntu-latest
            steps:
            - uses: actions/checkout@v3

            - name: Setup PHP
            uses: shivammathur/setup-php@v2
            with:
            php-version: '8.1'

            - name: Install Dependencies
            run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist

            - name: Security Check
            uses: symfonycorp/security-checker-action@v4

            - name: NPM Audit
            run: npm audit
        </div>
    </section>

    <section id="checklist">
        <h2>12. Checklist de Segurança</h2>
        <p>Antes de implantar sua aplicação em produção, verifique estes pontos essenciais de segurança:</p>

        <div class="best-practice">
            <h4>Checklist de Implantação</h4>
            <form>
                <ul style="list-style-type: none;">
                    <li><input type="checkbox"> Todas as senhas são armazenadas com hashing seguro (bcrypt ou argon2)
                    </li>
                    <li><input type="checkbox"> A proteção CSRF está ativa em todos os formulários e requisições não-API
                    </li>
                    <li><input type="checkbox"> Todos os dados de entrada são validados e sanitizados</li>
                    <li><input type="checkbox"> Os dados sensíveis estão criptografados em repouso</li>
                    <li><input type="checkbox"> HTTPS/TLS está configurado corretamente com certificados válidos</li>
                    <li><input type="checkbox"> Cabeçalhos de segurança HTTP estão implementados</li>
                    <li><input type="checkbox"> A aplicação não revela informações sensíveis em erros</li>
                    <li><input type="checkbox"> A autenticação inclui proteções contra força bruta (rate limiting)</li>
                    <li><input type="checkbox"> As políticas de autorização estão implementadas e testadas</li>
                    <li><input type="checkbox"> As dependências foram verificadas quanto a vulnerabilidades conhecidas
                    </li>
                    <li><input type="checkbox"> Sessões e cookies estão configurados com atributos de segurança</li>
                    <li><input type="checkbox"> Logs de segurança e auditoria estão implementados</li>
                    <li><input type="checkbox"> Backups estão configurados e testados</li>
                    <li><input type="checkbox"> Plano de resposta a incidentes está documentado</li>
                    <li><input type="checkbox"> Características de debug estão desativadas em produção</li>
                    <li><input type="checkbox"> Variáveis de ambiente estão configuradas corretamente</li>
                    <li><input type="checkbox"> Permissões de arquivos e pastas estão configuradas corretamente</li>
                    <li><input type="checkbox"> PHP settings recomendados para produção estão configurados</li>
                    <li><input type="checkbox"> Funcionalidades desnecessárias/perigosas estão desativadas (.env
                        acessível, etc)</li>
                    <li><input type="checkbox"> Implementado monitoramento e alertas para atividades suspeitas</li>
                </ul>
            </form>
        </div>

        <h3>12.1. Recursos Adicionais</h3>
        <p>Recomendamos os seguintes recursos para aprofundamento em práticas de segurança:</p>

        <ul>
            <li><a href="https://owasp.org/www-project-top-ten/" target="_blank">OWASP Top 10</a> - Lista dos riscos de
                segurança mais críticos em aplicações web</li>
            <li><a href="https://cheatsheetseries.owasp.org/" target="_blank">OWASP Cheat Sheet Series</a> - Guias
                práticos para implementar segurança</li>
            <li><a href="https://laravel.com/docs/security" target="_blank">Documentação de Segurança do Laravel</a> -
                Guia oficial de segurança do framework</li>
            <li><a href="https://securityheaders.com/" target="_blank">Security Headers</a> - Ferramenta para verificar
                cabeçalhos de segurança HTTP</li>
            <li><a href="https://observatory.mozilla.org/" target="_blank">Mozilla Observatory</a> - Avaliação
                abrangente de segurança para sites</li>
        </ul>
    </section>

    <footer>
        <p>Manual de Segurança - Versão 1.0</p>
        <p>Última atualização: <span id="current-date"></span></p>
        <script>
            document.getElementById('current-date').textContent = new Date().toLocaleDateString();
        </script>
    </footer>
</body>

</html>