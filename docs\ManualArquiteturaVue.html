<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Manual de Arquitetura e Visão Geral</title>
  <link rel="stylesheet" href="css/manual.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
  <header class="manual-header">
    <h1>Manual de Arquitetura e Visão Geral</h1>
    <p>Estrutura completa do projeto Vue.js com Laravel</p>
  </header>

  <nav class="manual-nav">
    <ul>
      <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
      <li><a href="#introducao">Introdução</a></li>
      <li><a href="#stack-tecnologico">Stack Tecnológico</a></li>
      <li><a href="#arquitetura">Arquitetura</a></li>
      <li><a href="#estrutura-pastas">Estrutura de Pastas</a></li>
      <li><a href="#fluxo-dados">Fluxo de Dados</a></li>
      <li><a href="#comunicacao">Comunicação</a></li>
      <li><a href="#padroes">Padrões</a></li>
      <li><a href="#decisoes">Decisões</a></li>
      <li><a href="#conclusao">Conclusão</a></li>
    </ul>
  </nav>

  <section id="sumario" class="manual-section">
    <h2>Sumário</h2>
    <ul>
      <li><a href="#introducao">1. Introdução</a></li>
      <li><a href="#stack-tecnologico">2. Stack Tecnológico</a>
        <ul>
          <li><a href="#frontend">2.1. Frontend</a></li>
          <li><a href="#backend">2.2. Backend</a></li>
          <li><a href="#ferramentas">2.3. Ferramentas de Desenvolvimento</a></li>
        </ul>
      </li>
      <li><a href="#arquitetura">3. Arquitetura do Sistema</a>
        <ul>
          <li><a href="#arquitetura-frontend">3.1. Arquitetura Frontend</a></li>
          <li><a href="#arquitetura-backend">3.2. Arquitetura Backend</a></li>
          <li><a href="#integracao">3.3. Integração Frontend-Backend</a></li>
        </ul>
      </li>
      <li><a href="#estrutura-pastas">4. Estrutura de Pastas</a>
        <ul>
          <li><a href="#estrutura-frontend">4.1. Estrutura do Frontend</a></li>
          <li><a href="#estrutura-backend">4.2. Estrutura do Backend</a></li>
        </ul>
      </li>
      <li><a href="#fluxo-dados">5. Fluxo de Dados</a>
        <ul>
          <li><a href="#fluxo-frontend">5.1. Fluxo no Frontend</a></li>
          <li><a href="#fluxo-backend">5.2. Fluxo no Backend</a></li>
          <li><a href="#fluxo-completo">5.3. Fluxo Completo de uma Requisição</a></li>
        </ul>
      </li>
      <li><a href="#comunicacao">6. Comunicação entre Componentes</a>
        <ul>
          <li><a href="#props-eventos">6.1. Props e Eventos</a></li>
          <li><a href="#provide-inject">6.2. Provide/Inject</a></li>
          <li><a href="#store">6.3. Store Centralizada</a></li>
        </ul>
      </li>
      <li><a href="#padroes">7. Padrões e Convenções</a>
        <ul>
          <li><a href="#nomenclatura">7.1. Nomenclatura</a></li>
          <li><a href="#estilo-codigo">7.2. Estilo de Código</a></li>
          <li><a href="#organizacao">7.3. Organização de Arquivos</a></li>
        </ul>
      </li>
      <li><a href="#decisoes">8. Decisões Arquiteturais</a>
        <ul>
          <li><a href="#vue3">8.1. Por que Vue 3 + Composition API</a></li>
          <li><a href="#pinia">8.2. Por que Pinia em vez de Vuex</a></li>
          <li><a href="#bootstrap">8.3. Por que Bootstrap-Vue</a></li>
        </ul>
      </li>
      <li><a href="#conclusao">9. Conclusão</a></li>
    </ul>
  </section>

  <section id="introducao" class="manual-section">
    <h2>1. Introdução</h2>

    <div class="intro-text">
      <p>Este manual fornece uma visão abrangente da arquitetura do nosso sistema, que consiste em um frontend Vue.js 3
        integrado com um backend Laravel. O documento destina-se a desenvolvedores que precisam entender a estrutura
        geral do projeto, seus componentes principais e como eles interagem entre si.</p>

      <p>A arquitetura foi projetada com foco em manutenibilidade, escalabilidade e desempenho, seguindo as melhores
        práticas de desenvolvimento moderno de aplicações web.</p>
    </div>

    <div class="key-points">
      <h3>Pontos-chave da Arquitetura</h3>
      <ul>
        <li>Aplicação SPA (Single Page Application) com Vue.js 3 e Composition API</li>
        <li>API RESTful com Laravel para o backend</li>
        <li>Gerenciamento de estado centralizado com Pinia</li>
        <li>Interface de usuário responsiva com Bootstrap-Vue 3</li>
        <li>Comunicação cliente-servidor via Axios</li>
        <li>Roteamento no frontend com Vue Router</li>
        <li>Estrutura modular e componentizada</li>
      </ul>
    </div>
  </section>

  <section id="stack-tecnologico" class="manual-section">
    <h2>2. Stack Tecnológico</h2>
    <p>Nossa aplicação utiliza um conjunto moderno de tecnologias para frontend e backend, selecionadas para
      proporcionar a melhor experiência de desenvolvimento e desempenho.</p>

    <div id="frontend" class="subsection">
      <h3>2.1. Frontend</h3>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Tecnologia</th>
              <th>Versão</th>
              <th>Propósito</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Vue.js</td>
              <td>3.x</td>
              <td>Framework JavaScript principal para construção da interface</td>
            </tr>
            <tr>
              <td>Vite</td>
              <td>4.x</td>
              <td>Ferramenta de build e servidor de desenvolvimento</td>
            </tr>
            <tr>
              <td>Pinia</td>
              <td>2.x</td>
              <td>Gerenciamento de estado centralizado</td>
            </tr>
            <tr>
              <td>Vue Router</td>
              <td>4.x</td>
              <td>Roteamento e navegação</td>
            </tr>
            <tr>
              <td>Bootstrap</td>
              <td>5.x</td>
              <td>Framework CSS para layout e componentes</td>
            </tr>
            <tr>
              <td>Bootstrap-Vue</td>
              <td>3.x</td>
              <td>Integração do Bootstrap com Vue.js</td>
            </tr>
            <tr>
              <td>Axios</td>
              <td>1.x</td>
              <td>Cliente HTTP para comunicação com a API</td>
            </tr>
            <tr>
              <td>Vitest</td>
              <td>0.x</td>
              <td>Framework de testes para Vue.js</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div id="backend" class="subsection">
      <h3>2.2. Backend</h3>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Tecnologia</th>
              <th>Versão</th>
              <th>Propósito</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Laravel</td>
              <td>10.x</td>
              <td>Framework PHP para desenvolvimento da API</td>
            </tr>
            <tr>
              <td>MySQL</td>
              <td>8.x</td>
              <td>Sistema de gerenciamento de banco de dados</td>
            </tr>
            <tr>
              <td>Eloquent ORM</td>
              <td>10.x</td>
              <td>ORM para interação com o banco de dados</td>
            </tr>
            <tr>
              <td>Laravel Sanctum</td>
              <td>3.x</td>
              <td>Autenticação e proteção de API</td>
            </tr>
            <tr>
              <td>PHPUnit</td>
              <td>10.x</td>
              <td>Framework de testes para PHP</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div id="ferramentas" class="subsection">
      <h3>2.3. Ferramentas de Desenvolvimento</h3>

      <div class="tools-list">
        <ul>
          <li>
            <strong>Git</strong>
            <p>Sistema de controle de versão para gerenciamento de código fonte.</p>
          </li>
          <li>
            <strong>ESLint</strong>
            <p>Ferramenta de linting para JavaScript/Vue para garantir qualidade e consistência do código.</p>
          </li>
          <li>
            <strong>Prettier</strong>
            <p>Formatador de código para manter consistência de estilo.</p>
          </li>
          <li>
            <strong>VS Code</strong>
            <p>Editor de código recomendado com extensões para Vue.js e Laravel.</p>
            <pre>Extensões recomendadas:
- Vue Language Features (Volar)
- ESLint
- Prettier
- Laravel Blade Snippets
- PHP Intelephense</pre>
          </li>
          <li>
            <strong>Chrome DevTools</strong>
            <p>Com a extensão Vue.js devtools para depuração do frontend.</p>
          </li>
        </ul>
      </div>
    </div>
  </section>

  <section id="arquitetura" class="manual-section">
    <h2>3. Arquitetura do Sistema</h2>
    <p>Nossa aplicação segue uma arquitetura cliente-servidor clara, com separação entre frontend e backend,
      comunicando-se via API RESTful.</p>

    <div id="arquitetura-frontend" class="subsection">
      <h3>3.1. Arquitetura Frontend</h3>

      <div class="architecture-diagram">
        <div class="arch-layer">
          <h4>Camada de UI</h4>
          <p>Views, Componentes, Layouts, Diretivas, Componentes UI</p>
        </div>
        <div class="arch-layer">
          <h4>Camada de Lógica</h4>
          <p>Composables, Utilitários, Validadores</p>
        </div>
        <div class="arch-layer">
          <h4>Camada de Estado</h4>
          <p>Pinia Stores, Estado Local, Persistência</p>
        </div>
        <div class="arch-layer">
          <h4>Camada de Integração</h4>
          <p>API Services, Interceptors, Adaptadores</p>
        </div>
      </div>

      <p>O frontend segue uma arquitetura em camadas que separa claramente as responsabilidades:</p>
      <ul>
        <li><strong>Camada de UI</strong>: Responsável pela apresentação visual e interação com o usuário</li>
        <li><strong>Camada de Lógica</strong>: Contém a lógica de negócio do frontend e funcionalidades reutilizáveis
        </li>
        <li><strong>Camada de Estado</strong>: Gerencia o estado da aplicação e sua persistência</li>
        <li><strong>Camada de Integração</strong>: Lida com a comunicação com o backend e serviços externos</li>
      </ul>

      <div class="best-practice">
        <h4>Princípios Arquiteturais do Frontend</h4>
        <ul>
          <li><strong>Componentização</strong>: Dividir a interface em componentes reutilizáveis</li>
          <li><strong>Composição</strong>: Usar a Composition API para compartilhar lógica entre componentes</li>
          <li><strong>Unidirecionalidade</strong>: Fluxo de dados em uma única direção (props para baixo, eventos para
            cima)</li>
          <li><strong>Estado Centralizado</strong>: Gerenciar estado compartilhado em stores Pinia</li>
          <li><strong>Separação de Responsabilidades</strong>: Cada componente e módulo tem uma responsabilidade clara
          </li>
        </ul>
      </div>
    </div>

    <div id="arquitetura-backend" class="subsection">
      <h3>3.2. Arquitetura Backend</h3>

      <div class="architecture-diagram">
        <div class="arch-layer">
          <h4>Camada de Roteamento e Controladores</h4>
          <p>Routes, Controllers, Middleware, Requests</p>
        </div>
        <div class="arch-layer">
          <h4>Camada de Serviços</h4>
          <p>Services, Repositories, Helpers</p>
        </div>
        <div class="arch-layer">
          <h4>Camada de Modelo</h4>
          <p>Models, Relationships, Eloquent</p>
        </div>
        <div class="arch-layer">
          <h4>Camada de Persistência</h4>
          <p>Database, Migrations, Seeders</p>
        </div>
      </div>

      <p>O backend segue a arquitetura MVC do Laravel, com algumas adaptações:</p>
      <ul>
        <li><strong>Camada de Roteamento e Controladores</strong>: Define endpoints da API e processa requisições</li>
        <li><strong>Camada de Serviços</strong>: Contém a lógica de negócio principal</li>
        <li><strong>Camada de Modelo</strong>: Define a estrutura de dados e relacionamentos</li>
        <li><strong>Camada de Persistência</strong>: Gerencia o armazenamento e recuperação de dados</li>
      </ul>

      <div class="best-practice">
        <h4>Princípios Arquiteturais do Backend</h4>
        <ul>
          <li><strong>API RESTful</strong>: Seguir princípios REST para design de API</li>
          <li><strong>Validação de Entrada</strong>: Validar todas as entradas de usuário com Form Requests</li>
          <li><strong>Autorização Explícita</strong>: Usar Policies para controle de acesso</li>
          <li><strong>Camada de Serviço</strong>: Encapsular lógica de negócio em classes de serviço</li>
          <li><strong>Eloquent ORM</strong>: Utilizar o ORM para interações com o banco de dados</li>
        </ul>
      </div>
    </div>

    <div id="integracao" class="subsection">
      <h3>3.3. Integração Frontend-Backend</h3>

      <div class="diagram" align="center">
        <svg width="700" height="300" xmlns="http://www.w3.org/2000/svg">
          <!-- Frontend Box -->
          <rect x="50" y="50" width="250" height="200" rx="10" ry="10" fill="#42b883" stroke="#2c9c6a"
            stroke-width="2" />
          <text x="175" y="80" font-family="Arial" font-size="18" text-anchor="middle" fill="white"
            font-weight="bold">Frontend (Vue.js)</text>

          <!-- Backend Box -->
          <rect x="400" y="50" width="250" height="200" rx="10" ry="10" fill="#f05340" stroke="#cc4f3a"
            stroke-width="2" />
          <text x="525" y="80" font-family="Arial" font-size="18" text-anchor="middle" fill="white"
            font-weight="bold">Backend (Laravel)</text>

          <!-- Frontend Components -->
          <rect x="70" y="100" width="210" height="30" rx="5" ry="5" fill="#2c9c6a" stroke="white" stroke-width="1" />
          <text x="175" y="120" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Componentes
            Vue</text>

          <rect x="70" y="140" width="210" height="30" rx="5" ry="5" fill="#2c9c6a" stroke="white" stroke-width="1" />
          <text x="175" y="160" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Pinia Store</text>

          <rect x="70" y="180" width="210" height="30" rx="5" ry="5" fill="#2c9c6a" stroke="white" stroke-width="1" />
          <text x="175" y="200" font-family="Arial" font-size="14" text-anchor="middle" fill="white">API Services
            (Axios)</text>

          <!-- Backend Components -->
          <rect x="420" y="100" width="210" height="30" rx="5" ry="5" fill="#cc4f3a" stroke="white" stroke-width="1" />
          <text x="525" y="120" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Controllers</text>

          <rect x="420" y="140" width="210" height="30" rx="5" ry="5" fill="#cc4f3a" stroke="white" stroke-width="1" />
          <text x="525" y="160" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Services</text>

          <rect x="420" y="180" width="210" height="30" rx="5" ry="5" fill="#cc4f3a" stroke="white" stroke-width="1" />
          <text x="525" y="200" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Models</text>

          <!-- Connection Arrows -->
          <line x1="300" y1="125" x2="400" y2="125" stroke="#333" stroke-width="2" />
          <polygon points="400,125 390,120 390,130" fill="#333" />

          <line x1="400" y1="175" x2="300" y2="175" stroke="#333" stroke-width="2" />
          <polygon points="300,175 310,170 310,180" fill="#333" />

          <!-- Labels -->
          <text x="350" y="115" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">HTTP Request</text>
          <text x="350" y="195" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">JSON Response</text>
        </svg>
      </div>

      <p>A integração entre frontend e backend é realizada através de uma API RESTful:</p>
      <ul>
        <li>O frontend se comunica com o backend através de requisições HTTP usando Axios</li>
        <li>O backend processa as requisições e retorna respostas em formato JSON</li>
        <li>A autenticação é realizada via tokens JWT gerenciados pelo Laravel Sanctum</li>
        <li>Os serviços de API no frontend encapsulam a lógica de comunicação com o backend</li>
        <li>Interceptors do Axios tratam erros comuns e gerenciam tokens de autenticação</li>
      </ul>

      <div class="code-block">
        <h4>Exemplo de Serviço de API no Frontend</h4>
        <pre><code>// src/services/api/userService.js
import http from '@/services/http';

const resource = 'usuarios';

export default {
  /**
   * Busca lista de usuários com paginação
   * @param {Object} params Parâmetros de busca
   * @returns {Promise} Promise com os usuários
   */
  getUsers(params = {}) {
    return http.get(`/${resource}`, { params })
      .then(response => ({
        data: response.data.data,
        meta: response.data.meta
      }));
  },
  
  /**
   * Busca usuário pelo ID
   * @param {Number} id ID do usuário
   * @returns {Promise} Promise com os dados do usuário
   */
  getUser(id) {
    return http.get(`/${resource}/${id}`)
      .then(response => response.data.data);
  },
  
  /**
   * Cria um novo usuário
   * @param {Object} userData Dados do usuário
   * @returns {Promise} Promise com o usuário criado
   */
  createUser(userData) {
    return http.post(`/${resource}`, userData)
      .then(response => response.data.data);
  },
  
  /**
   * Atualiza um usuário existente
   * @param {Number} id ID do usuário
   * @param {Object} userData Dados do usuário
   * @returns {Promise} Promise com o usuário atualizado
   */
  updateUser(id, userData) {
    return http.put(`/${resource}/${id}`, userData)
      .then(response => response.data.data);
  },
  
  /**
   * Exclui um usuário
   * @param {Number} id ID do usuário
   * @returns {Promise} Promise vazia em caso de sucesso
   */
  deleteUser(id) {
    return http.delete(`/${resource}/${id}`);
  }
};</code></pre>
      </div>

      <div class="code-block">
        <h4>Exemplo de Controller no Backend</h4>
        <pre><code>// app/Http/Controllers/UsuarioController.php
namespace App\Http\Controllers;

use App\Http\Requests\UsuarioRequest;
use App\Models\Usuario;
use App\Services\UsuarioService;
use Illuminate\Http\Request;

class UsuarioController extends Controller
{
    protected $usuarioService;
    
    public function __construct(UsuarioService $usuarioService)
    {
        $this->usuarioService = $usuarioService;
    }
    
    /**
     * Lista todos os usuários com paginação
     */
    public function index(Request $request)
    {
        $usuarios = $this->usuarioService->listarUsuarios(
            $request->query('page', 1),
            $request->query('perPage', 15)
        );
        
        return response()->json($usuarios);
    }
    
    /**
     * Busca um usuário específico
     */
    public function show($id)
    {
        $usuario = $this->usuarioService->buscarUsuario($id);
        
        return response()->json([
            'data' => $usuario
        ]);
    }
    
    /**
     * Cria um novo usuário
     */
    public function store(UsuarioRequest $request)
    {
        $usuario = $this->usuarioService->criarUsuario($request->validated());
        
        return response()->json([
            'data' => $usuario,
            'message' => 'Usuário criado com sucesso'
        ], 201);
    }
    
    /**
     * Atualiza um usuário existente
     */
    public function update(UsuarioRequest $request, $id)
    {
        $usuario = $this->usuarioService->atualizarUsuario($id, $request->validated());
        
        return response()->json([
            'data' => $usuario,
            'message' => 'Usuário atualizado com sucesso'
        ]);
    }
    
    /**
     * Remove um usuário
     */
    public function destroy($id)
    {
        $this->usuarioService->excluirUsuario($id);
        
        return response()->json([
            'message' => 'Usuário excluído com sucesso'
        ]);
    }
}</code></pre>
      </div>
    </div>
  </section>

  <section id="estrutura-pastas" class="manual-section">
    <h2>4. Estrutura de Pastas</h2>
    <p>A organização de arquivos e pastas segue uma estrutura lógica que reflete a arquitetura do sistema.</p>

    <div id="estrutura-frontend" class="subsection">
      <h3>4.1. Estrutura do Frontend</h3>

      <div class="code-block">
        <pre><code>src/
├── assets/              # Arquivos estáticos (imagens, fonts, etc)
├── components/          # Componentes Vue reutilizáveis
│   ├── common/          # Componentes genéricos usados em toda a aplicação
│   ├── forms/           # Componentes relacionados a formulários
│   ├── layout/          # Componentes de layout (header, footer, etc)
│   └── ui/              # Componentes de UI básicos (botões, cards, etc)
├── composables/         # Funções composables reutilizáveis
├── config/              # Configurações da aplicação
├── directives/          # Diretivas Vue personalizadas
├── layouts/             # Layouts de página
├── plugins/             # Plugins Vue
├── router/              # Configuração de rotas
│   ├── index.js         # Exportação principal do router
│   └── routes/          # Definições de rotas por módulo
├── services/            # Serviços para integração com API
│   ├── api/             # Clients de API para cada recurso
│   ├── http.js          # Cliente HTTP configurado
│   └── interceptors/    # Interceptors para requisições e respostas
├── stores/              # Pinia stores
│   ├── modules/         # Stores por módulo
│   └── index.js         # Exportação principal das stores
├── utils/               # Funções utilitárias
├── validators/          # Validadores de formulário
├── views/               # Componentes de página
│   ├── auth/            # Páginas de autenticação
│   ├── dashboard/       # Páginas de dashboard
│   ├── users/           # Páginas relacionadas a usuários
│   └── errors/          # Páginas de erro (404, 500, etc)
├── App.vue              # Componente raiz
├── main.js              # Ponto de entrada da aplicação
└── env.js               # Configurações de ambiente</code></pre>
      </div>

      <div class="alerts-section">
        <h4>Organização de Componentes</h4>
        <p>Os componentes são organizados por tipo e funcionalidade:</p>
        <ul>
          <li><strong>components/common/</strong>: Componentes genéricos como DataTable, Modal, etc.</li>
          <li><strong>components/forms/</strong>: Componentes de formulário como Input, Select, etc.</li>
          <li><strong>components/layout/</strong>: Componentes estruturais como Header, Sidebar, etc.</li>
          <li><strong>components/ui/</strong>: Componentes básicos de UI como Button, Card, etc.</li>
        </ul>
        <p>Esta organização facilita a localização e reutilização de componentes em todo o projeto.</p>
      </div>
    </div>

    <div id="estrutura-backend" class="subsection">
      <h3>4.2. Estrutura do Backend</h3>

      <div class="code-block">
        <pre><code>nosso-projeto/
├── app/ # Código da aplicação
│ ├── Console/ # Comandos artisan
│ ├── Contracts/ # Interfaces
│ ├── DTOs/ # Data Transfer Objects
│ ├── Exceptions/ # Exceções customizadas
│ ├── Http/ # Camada de Apresentação
│ │ ├── Controllers/ # Controllers gerais
│ │ ├── Middleware/ # Middlewares
│ │ ├── Requests/ # Form Requests gerais
│ │ ├── Resources/ # API Resources
│ │ └── Sistema/ # Organização por sistemas
│ │   ├── Micro1/ # Microsserviço 1
│ │   │   ├── Controllers/ # Controllers específicos do microsserviço
│ │   │   ├── Repositories/ # Repositórios específicos do microsserviço
│ │   │   ├── Services/ # Serviços específicos do microsserviço
│ │   │   ├── Requests/ # Form Requests específicos do microsserviço
│ │   │   └── Models/ # Models específicos do microsserviço (se houver)
│ │   ├── Micro2/ # Microsserviço 2
│ │   └── ... # Outros microsserviços
│ ├── Models/ # Modelos Eloquent gerais
│ ├── Policies/ # Políticas de autorização
│ ├── Repositories/ # Repositórios base
│ │ ├── Contracts/ # Interfaces de repositórios
│ │ └── Eloquent/ # Implementações Eloquent
│ ├── Responses/ # Classes de resposta
│ ├── Services/ # Serviços base
│ ├── ValueObjects/ # Objetos de valor
│ └── Providers/ # Service providers
├── bootstrap/ # Arquivos de inicialização
├── config/ # Configurações
├── database/ # Migrations, seeders
├── docs/ # Documentação do projeto
├── public/ # Assets públicos
├── resources/ # Views, assets não compilados
├── routes/ # Definição de rotas
│ ├── api.php # Rotas da API
│ ├── web.php # Rotas da Web
│ └── console.php # Comandos personalizados
├── storage/ # Armazenamento (logs, cache)
└── tests/ # Testes
├── Feature/ # Testes de feature
└── Unit/ # Testes unitários</code></pre>
      </div>

      <div class="alerts-section">
        <h4>Organização de Controladores e Serviços</h4>
        <p>O backend segue o padrão de Service Layer para separar a lógica de negócio dos controladores:</p>
        <ul>
          <li><strong>Controllers/</strong>: Responsáveis apenas por receber requisições, validar entradas e retornar
            respostas</li>
          <li><strong>Services/</strong>: Contêm a lógica de negócio e orquestram operações complexas</li>
          <li><strong>Repositories/</strong>: Encapsulam a lógica de acesso a dados e operações no banco</li>
          <li><strong>Models/</strong>: Definem a estrutura de dados e relacionamentos</li>
        </ul>
        <p>Esta separação facilita a manutenção e testabilidade do código.</p>
      </div>

      <div class="best-practice">
        <h4>Convenções de Nomenclatura no Backend</h4>
        <ul>
          <li><strong>Controllers</strong>: Singular, sufixo "Controller" (ex: UsuarioController)</li>
          <li><strong>Models</strong>: Singular, primeira letra maiúscula (ex: Usuario)</li>
          <li><strong>Services</strong>: Singular, sufixo "Service" (ex: UsuarioService)</li>
          <li><strong>Repositories</strong>: Singular, sufixo "Repository" (ex: UsuarioRepository)</li>
          <li><strong>Migrations</strong>: Data e descrição da alteração (ex: 2023_01_15_create_usuarios_table)</li>
          <li><strong>Requests</strong>: Singular, sufixo "Request" (ex: UsuarioRequest)</li>
        </ul>
      </div>
    </div>
  </section>
  <section id="fluxo-dados" class="manual-section">
    <h2>5. Fluxo de Dados</h2>
    <p>Entender o fluxo de dados na aplicação é essencial para compreender como as diferentes partes do sistema
      interagem.</p>

    <div id="fluxo-frontend" class="subsection">
      <h3>5.1. Fluxo no Frontend</h3>

      <div class="architecture-diagram">
        <div class="arch-layer">
          <h4>Componente Vue (View)</h4>
          <p>Renderiza a interface e captura interações do usuário</p>
        </div>
        <div class="arch-layer">
          <h4>Ações do Componente</h4>
          <p>Métodos que respondem a eventos do usuário</p>
        </div>
        <div class="arch-layer">
          <h4>Store Pinia (Estado)</h4>
          <p>Armazena e gerencia o estado da aplicação</p>
        </div>
        <div class="arch-layer">
          <h4>Serviço de API</h4>
          <p>Comunica-se com o backend via HTTP</p>
        </div>
      </div>

      <p>O fluxo de dados no frontend segue um padrão unidirecional:</p>
      <ol>
        <li>O usuário interage com a interface (componente Vue)</li>
        <li>O componente dispara uma ação (método) em resposta à interação</li>
        <li>A ação pode atualizar o estado local ou chamar uma action na store Pinia</li>
        <li>A store Pinia executa a lógica necessária e pode chamar serviços de API</li>
        <li>O serviço de API faz requisições HTTP para o backend</li>
        <li>Quando a resposta é recebida, a store atualiza seu estado</li>
        <li>Os componentes que dependem desse estado são automaticamente atualizados</li>
      </ol>

      <div class="code-block">
        <h4>Exemplo de Fluxo de Dados em um Componente</h4>
        <pre><code>// src/views/usuarios/UsuariosView.vue
&lt;template&gt;
  &lt;div class="usuarios-view"&gt;
    &lt;h1&gt;Gerenciamento de Usuários&lt;/h1&gt;
    
    &lt;!-- Interface de usuário que exibe dados e captura interações --&gt;
    &lt;data-table 
      :items="usuarios" 
      :loading="isLoading"
      @edit="editarUsuario"
      @delete="confirmarExclusao"
    /&gt;
    
    &lt;!-- Modal de confirmação de exclusão --&gt;
    &lt;confirm-modal 
      v-model="showDeleteModal"
      @confirm="excluirUsuario"
    /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, computed } from 'vue';
import { useUsuarioStore } from '@/stores/modules/usuarioStore';
import DataTable from '@/components/common/DataTable.vue';
import ConfirmModal from '@/components/common/ConfirmModal.vue';

// Store de usuários
const usuarioStore = useUsuarioStore();

// Estado local do componente
const showDeleteModal = ref(false);
const usuarioParaExcluir = ref(null);

// Dados computados da store
const usuarios = computed(() => usuarioStore.usuarios);
const isLoading = computed(() => usuarioStore.loading);

// Ações do componente
function editarUsuario(usuario) {
  // Navegar para a página de edição
  router.push(`/usuarios/${usuario.id}/editar`);
}

function confirmarExclusao(usuario) {
  usuarioParaExcluir.value = usuario;
  showDeleteModal.value = true;
}

async function excluirUsuario() {
  if (usuarioParaExcluir.value) {
    // Chamar action da store
    await usuarioStore.excluirUsuario(usuarioParaExcluir.value.id);
    showDeleteModal.value = false;
    usuarioParaExcluir.value = null;
  }
}

// Carregar dados ao montar o componente
onMounted(() => {
  usuarioStore.carregarUsuarios();
});
&lt;/script&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Exemplo de Store Pinia</h4>
        <pre><code>// src/stores/modules/usuarioStore.js
import { defineStore } from 'pinia';
import userService from '@/services/api/userService';

export const useUsuarioStore = defineStore('usuario', {
  state: () => ({
    usuarios: [],
    usuarioAtual: null,
    loading: false,
    error: null
  }),
  
  getters: {
    // Getters para acessar e filtrar dados
    usuariosAtivos: (state) => state.usuarios.filter(u => u.ativo),
  },
  
  actions: {
    // Ações para modificar o estado
    async carregarUsuarios() {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await userService.getUsers();
        this.usuarios = response.data;
      } catch (error) {
        this.error = error.message || 'Erro ao carregar usuários';
        console.error('Erro ao carregar usuários:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async carregarUsuario(id) {
      this.loading = true;
      this.error = null;
      
      try {
        const usuario = await userService.getUser(id);
        this.usuarioAtual = usuario;
        return usuario;
      } catch (error) {
        this.error = error.message || 'Erro ao carregar usuário';
        console.error(`Erro ao carregar usuário ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    async salvarUsuario(dadosUsuario) {
      this.loading = true;
      this.error = null;
      
      try {
        let usuario;
        
        if (dadosUsuario.id) {
          // Atualizar usuário existente
          usuario = await userService.updateUser(dadosUsuario.id, dadosUsuario);
          
          // Atualizar na lista de usuários
          const index = this.usuarios.findIndex(u => u.id === usuario.id);
          if (index !== -1) {
            this.usuarios[index] = usuario;
          }
        } else {
          // Criar novo usuário
          usuario = await userService.createUser(dadosUsuario);
          this.usuarios.push(usuario);
        }
        
        return usuario;
      } catch (error) {
        this.error = error.message || 'Erro ao salvar usuário';
        console.error('Erro ao salvar usuário:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    async excluirUsuario(id) {
      this.loading = true;
      this.error = null;
      
      try {
        await userService.deleteUser(id);
        
        // Remover da lista de usuários
        this.usuarios = this.usuarios.filter(u => u.id !== id);
        
        // Limpar usuário atual se for o mesmo
        if (this.usuarioAtual && this.usuarioAtual.id === id) {
          this.usuarioAtual = null;
        }
      } catch (error) {
        this.error = error.message || 'Erro ao excluir usuário';
        console.error(`Erro ao excluir usuário ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    }
  }
});</code></pre>
      </div>
    </div>

    <div id="fluxo-backend" class="subsection">
      <h3>5.2. Fluxo no Backend</h3>

      <div class="architecture-diagram">
        <div class="arch-layer">
          <h4>Rota</h4>
          <p>Define o endpoint da API</p>
        </div>
        <div class="arch-layer">
          <h4>Middleware</h4>
          <p>Processa a requisição antes do controlador</p>
        </div>
        <div class="arch-layer">
          <h4>Controller</h4>
          <p>Recebe a requisição e coordena a resposta</p>
        </div>
        <div class="arch-layer">
          <h4>Service</h4>
          <p>Contém a lógica de negócio</p>
        </div>
        <div class="arch-layer">
          <h4>Repository</h4>
          <p>Acessa e manipula os dados</p>
        </div>
        <div class="arch-layer">
          <h4>Model</h4>
          <p>Representa a estrutura de dados</p>
        </div>
      </div>

      <p>O fluxo de dados no backend segue um padrão de camadas:</p>
      <ol>
        <li>A requisição HTTP chega a uma rota definida em routes/api.php</li>
        <li>Os middlewares processam a requisição (autenticação, validação, etc.)</li>
        <li>O controlador recebe a requisição e extrai os dados necessários</li>
        <li>O controlador chama um método no serviço apropriado</li>
        <li>O serviço executa a lógica de negócio e utiliza repositórios para acessar dados</li>
        <li>O repositório interage com os modelos Eloquent para operações no banco de dados</li>
        <li>O resultado é retornado através da cadeia: Modelo → Repositório → Serviço → Controlador</li>
        <li>O controlador formata a resposta e a retorna como JSON</li>
      </ol>

      <div class="code-block">
        <h4>Exemplo de Rota</h4>
        <pre><code>// routes/api.php
Route::middleware(['auth:sanctum'])->group(function () {
    Route::apiResource('usuarios', UsuarioController::class);
});</code></pre>
      </div>

      <div class="code-block">
        <h4>Exemplo de Service</h4>
        <pre><code>// app/Services/UsuarioService.php
namespace App\Services;

use App\Repositories\UsuarioRepository;
use Illuminate\Pagination\LengthAwarePaginator;

class UsuarioService
{
    protected $usuarioRepository;
    
    public function __construct(UsuarioRepository $usuarioRepository)
    {
        $this->usuarioRepository = $usuarioRepository;
    }
    
    /**
     * Lista usuários com paginação
     */
    public function listarUsuarios(int $page = 1, int $perPage = 15): array
    {
        $usuarios = $this->usuarioRepository->paginate($perPage, $page);
        
        return [
            'data' => $usuarios->items(),
            'meta' => [
                'current_page' => $usuarios->currentPage(),
                'last_page' => $usuarios->lastPage(),
                'per_page' => $usuarios->perPage(),
                'total' => $usuarios->total()
            ]
        ];
    }
    
    /**
     * Busca um usuário pelo ID
     */
    public function buscarUsuario(int $id)
    {
        return $this->usuarioRepository->findOrFail($id);
    }
    
    /**
     * Cria um novo usuário
     */
    public function criarUsuario(array $dados)
    {
        // Lógica de negócio antes de criar o usuário
        if (!isset($dados['ativo'])) {
            $dados['ativo'] = true;
        }
        
        return $this->usuarioRepository->create($dados);
    }
    
    /**
     * Atualiza um usuário existente
     */
    public function atualizarUsuario(int $id, array $dados)
    {
        $usuario = $this->usuarioRepository->findOrFail($id);
        
        // Lógica de negócio antes de atualizar o usuário
        
        return $this->usuarioRepository->update($usuario, $dados);
    }
    
    /**
     * Exclui um usuário
     */
    public function excluirUsuario(int $id): bool
    {
        $usuario = $this->usuarioRepository->findOrFail($id);
        
        // Lógica de negócio antes de excluir o usuário
        // Ex: verificar se o usuário pode ser excluído
        
        return $this->usuarioRepository->delete($usuario);
    }
}</code></pre>
      </div>

      <div class="code-block">
        <h4>Exemplo de Repository</h4>
        <pre><code>// app/Repositories/UsuarioRepository.php
namespace App\Repositories;

use App\Models\Usuario;
use Illuminate\Pagination\LengthAwarePaginator;

class UsuarioRepository
{
    /**
     * Busca todos os usuários com paginação
     */
    public function paginate(int $perPage = 15, int $page = 1): LengthAwarePaginator
    {
        return Usuario::query()
            ->orderBy('nome')
            ->paginate($perPage, ['*'], 'page', $page);
    }
    
    /**
     * Busca um usuário pelo ID
     */
    public function findOrFail(int $id): Usuario
    {
        return Usuario::findOrFail($id);
    }
    
    /**
     * Cria um novo usuário
     */
    public function create(array $dados): Usuario
    {
        return Usuario::create($dados);
    }
    
    /**
     * Atualiza um usuário existente
     */
    public function update(Usuario $usuario, array $dados): Usuario
    {
        $usuario->update($dados);
        return $usuario->fresh();
    }
    
    /**
     * Exclui um usuário
     */
    public function delete(Usuario $usuario): bool
    {
        return $usuario->delete();
    }
}</code></pre>
      </div>
    </div>

    <div id="fluxo-completo" class="subsection">
      <h3>5.3. Fluxo Completo de uma Requisição</h3>

      <div class="diagram" align="center">
        <svg width="700" height="500" xmlns="http://www.w3.org/2000/svg">
          <!-- User -->
          <circle cx="100" cy="50" r="30" fill="#f8f9fa" stroke="#333" stroke-width="2" />
          <text x="100" y="55" font-family="Arial" font-size="14" text-anchor="middle">Usuário</text>

          <!-- Vue Component -->
          <rect x="50" y="120" width="100" height="50" rx="5" ry="5" fill="#42b883" stroke="#2c9c6a" stroke-width="2" />
          <text x="100" y="150" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Componente</text>

          <!-- Pinia Store -->
          <rect x="50" y="210" width="100" height="50" rx="5" ry="5" fill="#42b883" stroke="#2c9c6a" stroke-width="2" />
          <text x="100" y="240" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Store
            Pinia</text>

          <!-- API Service -->
          <rect x="50" y="300" width="100" height="50" rx="5" ry="5" fill="#42b883" stroke="#2c9c6a" stroke-width="2" />
          <text x="100" y="330" font-family="Arial" font-size="14" text-anchor="middle" fill="white">API
            Service</text>

          <!-- HTTP Request -->
          <rect x="200" y="300" width="100" height="50" rx="5" ry="5" fill="#6c757d" stroke="#495057"
            stroke-width="2" />
          <text x="250" y="330" font-family="Arial" font-size="14" text-anchor="middle" fill="white">HTTP</text>

          <!-- Laravel Route -->
          <rect x="350" y="300" width="100" height="50" rx="5" ry="5" fill="#f05340" stroke="#cc4f3a"
            stroke-width="2" />
          <text x="400" y="330" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Rota</text>

          <!-- Laravel Controller -->
          <rect x="500" y="300" width="100" height="50" rx="5" ry="5" fill="#f05340" stroke="#cc4f3a"
            stroke-width="2" />
          <text x="550" y="330" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Controller</text>

          <!-- Laravel Service -->
          <rect x="500" y="210" width="100" height="50" rx="5" ry="5" fill="#f05340" stroke="#cc4f3a"
            stroke-width="2" />
          <text x="550" y="240" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Service</text>

          <!-- Laravel Repository -->
          <rect x="500" y="120" width="100" height="50" rx="5" ry="5" fill="#f05340" stroke="#cc4f3a"
            stroke-width="2" />
          <text x="550" y="150" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Repository</text>

          <!-- Database -->
          <rect x="350" y="120" width="100" height="50" rx="5" ry="5" fill="#0275d8" stroke="#0269c2"
            stroke-width="2" />
          <text x="400" y="150" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Database</text>

          <!-- Arrows -->
          <!-- User to Component -->
          <line x1="100" y1="80" x2="100" y2="120" stroke="#333" stroke-width="2" />
          <polygon points="100,120 95,110 105,110" fill="#333" />

          <!-- Component to Store -->
          <line x1="100" y1="170" x2="100" y2="210" stroke="#333" stroke-width="2" />
          <polygon points="100,210 95,200 105,200" fill="#333" />

          <!-- Store to API Service -->
          <line x1="100" y1="260" x2="100" y2="300" stroke="#333" stroke-width="2" />
          <polygon points="100,300 95,290 105,290" fill="#333" />

          <!-- API Service to HTTP -->
          <line x1="150" y1="325" x2="200" y2="325" stroke="#333" stroke-width="2" />
          <polygon points="200,325 190,320 190,330" fill="#333" />

          <!-- HTTP to Route -->
          <line x1="300" y1="325" x2="350" y2="325" stroke="#333" stroke-width="2" />
          <polygon points="350,325 340,320 340,330" fill="#333" />

          <!-- Route to Controller -->
          <line x1="450" y1="325" x2="500" y2="325" stroke="#333" stroke-width="2" />
          <polygon points="500,325 490,320 490,330" fill="#333" />

          <!-- Controller to Service -->
          <line x1="550" y1="300" x2="550" y2="260" stroke="#333" stroke-width="2" />
          <polygon points="550,260 545,270 555,270" fill="#333" />

          <!-- Service to Repository -->
          <line x1="550" y1="210" x2="550" y2="170" stroke="#333" stroke-width="2" />
          <polygon points="550,170 545,180 555,180" fill="#333" />

          <!-- Repository to Database -->
          <line x1="500" y1="145" x2="450" y2="145" stroke="#333" stroke-width="2" />
          <polygon points="450,145 460,140 460,150" fill="#333" />

          <!-- Database to Repository -->
          <line x1="450" y1="125" x2="500" y2="125" stroke="#333" stroke-width="2" />
          <polygon points="500,125 490,120 490,130" fill="#333" />

          <!-- Repository to Service -->
          <line x1="530" y1="170" x2="530" y2="210" stroke="#333" stroke-width="2" />
          <polygon points="530,210 525,200 535,200" fill="#333" />

          <!-- Service to Controller -->
          <line x1="530" y1="260" x2="530" y2="300" stroke="#333" stroke-width="2" />
          <polygon points="530,300 525,290 535,290" fill="#333" />

          <!-- Controller to Route -->
          <line x1="500" y1="315" x2="450" y2="315" stroke="#333" stroke-width="2" />
          <polygon points="450,315 460,310 460,320" fill="#333" />

          <!-- Route to HTTP -->
          <line x1="350" y1="315" x2="300" y2="315" stroke="#333" stroke-width="2" />
          <polygon points="300,315 310,310 310,320" fill="#333" />

          <!-- HTTP to API Service -->
          <line x1="200" y1="315" x2="150" y2="315" stroke="#333" stroke-width="2" />
          <polygon points="150,315 160,310 160,320" fill="#333" />

          <!-- API Service to Store -->
          <line x1="80" y1="300" x2="80" y2="260" stroke="#333" stroke-width="2" />
          <polygon points="80,260 75,270 85,270" fill="#333" />

          <!-- Store to Component -->
          <line x1="80" y1="210" x2="80" y2="170" stroke="#333" stroke-width="2" />
          <polygon points="80,170 75,180 85,180" fill="#333" />

          <!-- Component to User -->
          <line x1="80" y1="120" x2="80" y2="80" stroke="#333" stroke-width="2" />
          <polygon points="80,80 75,90 85,90" fill="#333" />

          <!-- Labels -->
          <text x="110" y="100" font-family="Arial" font-size="12" text-anchor="start">1. Interação</text>
          <text x="110" y="190" font-family="Arial" font-size="12" text-anchor="start">2. Dispatch</text>
          <text x="110" y="280" font-family="Arial" font-size="12" text-anchor="start">3. API Call</text>
          <text x="175" y="345" font-family="Arial" font-size="12" text-anchor="middle">4. Request</text>
          <text x="325" y="345" font-family="Arial" font-size="12" text-anchor="middle">5. Route</text>
          <text x="475" y="345" font-family="Arial" font-size="12" text-anchor="middle">6. Handle</text>
          <text x="570" y="280" font-family="Arial" font-size="12" text-anchor="start">7. Service</text>
          <text x="570" y="190" font-family="Arial" font-size="12" text-anchor="start">8. Repository</text>
          <text x="400" y="110" font-family="Arial" font-size="12" text-anchor="middle">9. Query</text>
          <text x="400" y="170" font-family="Arial" font-size="12" text-anchor="middle">10. Data</text>
          <text x="475" y="295" font-family="Arial" font-size="12" text-anchor="middle">13. Response</text>
          <text x="325" y="295" font-family="Arial" font-size="12" text-anchor="middle">14. JSON</text>
          <text x="175" y="295" font-family="Arial" font-size="12" text-anchor="middle">15. Parse</text>
          <text x="60" y="280" font-family="Arial" font-size="12" text-anchor="end">16. Update</text>
          <text x="60" y="190" font-family="Arial" font-size="12" text-anchor="end">17. Render</text>
        </svg>
      </div>

      <p>O diagrama acima ilustra o fluxo completo de uma requisição típica em nossa aplicação:</p>
      <ol>
        <li>O usuário interage com a interface (clica em um botão, preenche um formulário, etc.)</li>
        <li>O componente Vue dispara uma ação em resposta à interação</li>
        <li>A ação chama um método na store Pinia</li>
        <li>A store Pinia chama um método no serviço de API</li>
        <li>O serviço de API faz uma requisição HTTP para o backend</li>
        <li>A requisição é roteada para o controlador apropriado no Laravel</li>
        <li>O controlador valida a entrada e chama um método no serviço</li>
        <li>O serviço executa a lógica de negócio e chama métodos no repositório</li>
        <li>O repositório interage com o modelo Eloquent para acessar o banco de dados</li>
        <li>O banco de dados retorna os dados solicitados</li>
        <li>Os dados são processados pelo repositório e retornados ao serviço</li>
        <li>O serviço processa os dados e os retorna ao controlador</li>
        <li>O controlador formata a resposta e a retorna como JSON</li>
        <li>O frontend recebe a resposta JSON</li>
        <li>O serviço de API processa a resposta e a retorna à store</li>
        <li>A store atualiza seu estado com os novos dados</li>
        <li>Os componentes que dependem desse estado são automaticamente atualizados</li>
        <li>A interface é atualizada para refletir os novos dados</li>
      </ol>

      <div class="best-practice">
        <h4>Benefícios deste Fluxo de Dados</h4>
        <ul>
          <li><strong>Previsibilidade</strong>: O fluxo unidirecional torna o comportamento da aplicação mais previsível
          </li>
          <li><strong>Manutenibilidade</strong>: A separação clara de responsabilidades facilita a manutenção</li>
          <li><strong>Testabilidade</strong>: Cada camada pode ser testada isoladamente</li>
          <li><strong>Escalabilidade</strong>: A arquitetura modular permite que a aplicação cresça de forma organizada
          </li>
          <li><strong>Reusabilidade</strong>: Componentes e lógica podem ser reutilizados em diferentes partes da
            aplicação</li>
        </ul>
      </div>
    </div>
  </section>

  <section id="comunicacao" class="manual-section">
    <h2>6. Comunicação entre Componentes</h2>
    <p>A comunicação eficiente entre componentes é essencial para uma aplicação Vue.js bem estruturada. Nossa
      arquitetura utiliza diferentes métodos de comunicação dependendo do contexto e da relação entre os
      componentes.</p>

    <div id="props-eventos" class="subsection">
      <h3>6.1. Props e Eventos</h3>

      <p>O método mais básico e recomendado para comunicação entre componentes pai e filho é através de props (de
        cima para baixo) e eventos personalizados (de baixo para cima).</p>

      <div class="diagram" align="center">
        <svg width="500" height="250" xmlns="http://www.w3.org/2000/svg">
          <!-- Parent Component -->
          <rect x="100" y="50" width="300" height="80" rx="8" ry="8" fill="#42b883" stroke="#2c9c6a" stroke-width="2" />
          <text x="250" y="90" font-family="Arial" font-size="16" text-anchor="middle" fill="white">Componente
            Pai</text>

          <!-- Child Component -->
          <rect x="150" y="170" width="200" height="60" rx="8" ry="8" fill="#64b5f6" stroke="#1976d2"
            stroke-width="2" />
          <text x="250" y="205" font-family="Arial" font-size="16" text-anchor="middle" fill="white">Componente
            Filho</text>

          <!-- Props Arrow -->
          <line x1="200" y1="130" x2="200" y2="170" stroke="#2c9c6a" stroke-width="2" />
          <polygon points="200,170 195,160 205,160" fill="#2c9c6a" />
          <text x="180" y="150" font-family="Arial" font-size="14" text-anchor="end" fill="#2c9c6a">Props</text>

          <!-- Events Arrow -->
          <line x1="300" y1="170" x2="300" y2="130" stroke="#1976d2" stroke-width="2" />
          <polygon points="300,130 295,140 305,140" fill="#1976d2" />
          <text x="320" y="150" font-family="Arial" font-size="14" text-anchor="start" fill="#1976d2">Eventos</text>
        </svg>
      </div>

      <div class="code-block">
        <h4>Exemplo de Comunicação com Props e Eventos</h4>
        <pre><code>// Componente Pai: UserForm.vue
&lt;template&gt;
  &lt;div class="user-form"&gt;
    &lt;h2&gt;Formulário de Usuário&lt;/h2&gt;
    
    &lt;!-- Passando props para o componente filho --&gt;
    &lt;form-input
      label="Nome"
      :value="usuario.nome"
      :error="errors.nome"
      @input="usuario.nome = $event"
    /&gt;
    
    &lt;form-input
      label="Email"
      type="email"
      :value="usuario.email"
      :error="errors.email"
      @input="usuario.email = $event"
    /&gt;
    
    &lt;button @click.prevent="salvar"&gt;Salvar&lt;/button&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';
import FormInput from '@/components/forms/FormInput.vue';

const usuario = ref({
  nome: '',
  email: ''
});

const errors = ref({
  nome: '',
  email: ''
});

function salvar() {
  // Lógica para salvar o usuário
  console.log('Salvando usuário:', usuario.value);
}
&lt;/script&gt;

// Componente Filho: FormInput.vue
&lt;template&gt;
  &lt;div class="form-group"&gt;
    &lt;label v-if="label"&gt;{{ label }}&lt;/label&gt;
    &lt;input
      :type="type"
      :value="value"
      @input="$emit('input', $event.target.value)"
      class="form-control"
      :class="{ 'is-invalid': error }"
    /&gt;
    &lt;div v-if="error" class="invalid-feedback"&gt;{{ error }}&lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
// Definindo props
const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  value: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  error: {
    type: String,
    default: ''
  }
});

// Definindo eventos que este componente pode emitir
const emit = defineEmits(['input']);
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Boas Práticas para Props e Eventos</h4>
        <ul>
          <li><strong>Props Validadas</strong>: Sempre defina tipos e valores padrão para props</li>
          <li><strong>Props Imutáveis</strong>: Nunca modifique props diretamente no componente filho</li>
          <li><strong>Nomes Descritivos</strong>: Use nomes claros para props e eventos</li>
          <li><strong>Eventos Específicos</strong>: Emita eventos com nomes específicos e dados relevantes</li>
          <li><strong>v-model Personalizado</strong>: Use modelValue e update:modelValue para componentes com
            v-model</li>
        </ul>
      </div>
    </div>

    <div id="provide-inject" class="subsection">
      <h3>6.2. Provide/Inject</h3>

      <p>Para comunicação entre componentes distantes na árvore de componentes, o Vue oferece o mecanismo
        provide/inject, que permite que um componente ancestral forneça dados que podem ser injetados em qualquer
        componente descendente.</p>

      <div class="diagram" align="center">
        <svg width="500" height="350" xmlns="http://www.w3.org/2000/svg">
          <!-- Root Component -->
          <rect x="150" y="30" width="200" height="60" rx="8" ry="8" fill="#42b883" stroke="#2c9c6a" stroke-width="2" />
          <text x="250" y="65" font-family="Arial" font-size="16" text-anchor="middle" fill="white">Componente
            Raiz</text>
          <text x="250" y="85" font-family="Arial" font-size="12" text-anchor="middle" fill="white">provide()</text>

          <!-- Level 1 Component -->
          <rect x="150" y="130" width="200" height="40" rx="8" ry="8" fill="#64b5f6" stroke="#1976d2"
            stroke-width="2" />
          <text x="250" y="155" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Componente
            Nível 1</text>

          <!-- Level 2 Components -->
          <rect x="80" y="210" width="150" height="40" rx="8" ry="8" fill="#64b5f6" stroke="#1976d2" stroke-width="2" />
          <text x="155" y="235" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Componente
            Nível 2A</text>

          <rect x="270" y="210" width="150" height="40" rx="8" ry="8" fill="#64b5f6" stroke="#1976d2"
            stroke-width="2" />
          <text x="345" y="235" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Componente
            Nível 2B</text>

          <!-- Level 3 Component -->
          <rect x="80" y="290" width="150" height="40" rx="8" ry="8" fill="#ff7043" stroke="#e64a19" stroke-width="2" />
          <text x="155" y="315" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Componente
            Nível 3</text>
          <text x="155" y="330" font-family="Arial" font-size="12" text-anchor="middle" fill="white">inject()</text>

          <!-- Connection Lines -->
          <line x1="250" y1="90" x2="250" y2="130" stroke="#333" stroke-width="1" />
          <line x1="250" y1="170" x2="250" y2="190" stroke="#333" stroke-width="1" />
          <line x1="250" y1="190" x2="155" y2="210" stroke="#333" stroke-width="1" />
          <line x1="250" y1="190" x2="345" y2="210" stroke="#333" stroke-width="1" />
          <line x1="155" y1="250" x2="155" y2="290" stroke="#333" stroke-width="1" />

          <!-- Provide/Inject Arrow -->
          <line x1="180" y1="90" x2="120" y2="290" stroke="#e64a19" stroke-width="2" stroke-dasharray="5,5" />
          <polygon points="120,290 118,280 128,285" fill="#e64a19" />
          <text x="130" y="180" font-family="Arial" font-size="14" text-anchor="middle" fill="#e64a19"
            transform="rotate(-45, 130, 180)">provide/inject</text>
        </svg>
      </div>

      <div class="code-block">
        <h4>Exemplo de Provide/Inject</h4>
        <pre><code>// Componente Raiz: App.vue
&lt;template&gt;
  &lt;div class="app"&gt;
    &lt;app-header /&gt;
    &lt;main&gt;
      &lt;router-view /&gt;
    &lt;/main&gt;
    &lt;app-footer /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { provide, ref } from 'vue';
import AppHeader from '@/components/layout/AppHeader.vue';
import AppFooter from '@/components/layout/AppFooter.vue';

// Criando dados para fornecer aos componentes descendentes
const theme = ref('light');
const appName = 'Sistema de Gestão';

// Fornecendo dados e funções para componentes descendentes
provide('theme', theme);
provide('appName', appName);
provide('toggleTheme', () => {
  theme.value = theme.value === 'light' ? 'dark' : 'light';
});
&lt;/script&gt;

// Componente Descendente: ThemeSwitcher.vue (pode estar aninhado em qualquer nível)
&lt;template&gt;
  &lt;div class="theme-switcher"&gt;
    &lt;span&gt;Tema atual: {{ theme }}&lt;/span&gt;
    &lt;button @click="toggleTheme" class="btn btn-sm"&gt;
      Alternar Tema
    &lt;/button&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { inject } from 'vue';

// Injetando dados e funções fornecidos por um componente ancestral
const theme = inject('theme');
const appName = inject('appName');
const toggleTheme = inject('toggleTheme');
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Quando Usar Provide/Inject</h4>
        <ul>
          <li><strong>Temas e Configurações</strong>: Para fornecer configurações globais como tema, idioma, etc.
          </li>
          <li><strong>Contexto de Componentes</strong>: Para componentes que precisam compartilhar contexto (ex:
            formulários)</li>
          <li><strong>Evitar "Prop Drilling"</strong>: Quando precisar passar props através de muitos níveis de
            componentes</li>
        </ul>
      </div>

      <div class="alerts-section">
        <h4>Cuidados com Provide/Inject</h4>
        <ul>
          <li>Use com moderação, pois pode tornar o fluxo de dados menos explícito</li>
          <li>Documente bem os valores fornecidos para facilitar a manutenção</li>
          <li>Considere usar TypeScript para garantir tipagem segura</li>
          <li>Para estados complexos ou globais, prefira usar Pinia</li>
        </ul>
      </div>
    </div>

    <div id="store" class="subsection">
      <h3>6.3. Store Centralizada</h3>

      <p>Para gerenciamento de estado global e comunicação entre componentes não relacionados, utilizamos o Pinia
        como solução de gerenciamento de estado.</p>

      <div class="diagram" align="center">
        <svg width="600" height="300" xmlns="http://www.w3.org/2000/svg">
          <!-- Pinia Store -->
          <rect x="200" y="30" width="200" height="80" rx="8" ry="8" fill="#ffd54f" stroke="#ffa000" stroke-width="2" />
          <text x="300" y="65" font-family="Arial" font-size="18" text-anchor="middle" fill="#333">Pinia
            Store</text>
          <text x="300" y="90" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">Estado
            Centralizado</text>

          <!-- Components -->
          <rect x="50" y="170" width="150" height="60" rx="8" ry="8" fill="#42b883" stroke="#2c9c6a" stroke-width="2" />
          <text x="125" y="205" font-family="Arial" font-size="16" text-anchor="middle" fill="white">Componente
            A</text>

          <rect x="225" y="170" width="150" height="60" rx="8" ry="8" fill="#42b883" stroke="#2c9c6a"
            stroke-width="2" />
          <text x="300" y="205" font-family="Arial" font-size="16" text-anchor="middle" fill="white">Componente
            B</text>

          <rect x="400" y="170" width="150" height="60" rx="8" ry="8" fill="#42b883" stroke="#2c9c6a"
            stroke-width="2" />
          <text x="475" y="205" font-family="Arial" font-size="16" text-anchor="middle" fill="white">Componente
            C</text>

          <!-- Arrows -->
          <line x1="125" y1="170" x2="250" y2="110" stroke="#2c9c6a" stroke-width="2" />
          <polygon points="250,110 240,115 245,105" fill="#2c9c6a" />
          <text x="170" y="130" font-family="Arial" font-size="14" text-anchor="middle" fill="#2c9c6a">dispatch</text>

          <line x1="300" y1="170" x2="300" y2="110" stroke="#2c9c6a" stroke-width="2" />
          <polygon points="300,110 295,120 305,120" fill="#2c9c6a" />
          <text x="320" y="140" font-family="Arial" font-size="14" text-anchor="start" fill="#2c9c6a">dispatch</text>

          <line x1="475" y1="170" x2="350" y2="110" stroke="#2c9c6a" stroke-width="2" />
          <polygon points="350,110 355,115 360,105" fill="#2c9c6a" />
          <text x="430" y="130" font-family="Arial" font-size="14" text-anchor="middle" fill="#2c9c6a">dispatch</text>

          <line x1="250" y1="90" x2="125" y2="170" stroke="#ffa000" stroke-width="2" stroke-dasharray="5,3" />
          <polygon points="125,170 130,160 135,170" fill="#ffa000" />
          <text x="170" y="150" font-family="Arial" font-size="14" text-anchor="middle" fill="#ffa000">state</text>

          <line x1="300" y1="110" x2="300" y2="170" stroke="#ffa000" stroke-width="2" stroke-dasharray="5,3" />
          <polygon points="300,170 295,160 305,160" fill="#ffa000" />
          <text x="280" y="140" font-family="Arial" font-size="14" text-anchor="end" fill="#ffa000">state</text>

          <line x1="350" y1="90" x2="475" y2="170" stroke="#ffa000" stroke-width="2" stroke-dasharray="5,3" />
          <polygon points="475,170 465,165 470,155" fill="#ffa000" />
          <text x="430" y="150" font-family="Arial" font-size="14" text-anchor="middle" fill="#ffa000">state</text>
        </svg>
      </div>

      <div class="code-block">
        <h4>Exemplo de Store Pinia</h4>
        <pre><code>// src/stores/modules/notificacaoStore.js
import { defineStore } from 'pinia';

export const useNotificacaoStore = defineStore('notificacao', {
  state: () => ({
    notificacoes: [],
    naoLidas: 0
  }),
  
  getters: {
    temNotificacoesNaoLidas: (state) => state.naoLidas > 0,
    
    notificacoesOrdenadas: (state) => {
      return [...state.notificacoes].sort((a, b) => {
        return new Date(b.data) - new Date(a.data);
      });
    }
  },
  
  actions: {
    adicionarNotificacao(notificacao) {
      this.notificacoes.push({
        id: Date.now(),
        lida: false,
        data: new Date().toISOString(),
        ...notificacao
      });
      this.naoLidas++;
    },
    
    marcarComoLida(id) {
      const notificacao = this.notificacoes.find(n => n.id === id);
      if (notificacao && !notificacao.lida) {
        notificacao.lida = true;
        this.naoLidas--;
      }
    },
    
    marcarTodasComoLidas() {
      this.notificacoes.forEach(n => {
        n.lida = true;
      });
      this.naoLidas = 0;
    },
    
    removerNotificacao(id) {
      const index = this.notificacoes.findIndex(n => n.id === id);
      if (index !== -1) {
        const notificacao = this.notificacoes[index];
        if (!notificacao.lida) {
          this.naoLidas--;
        }
        this.notificacoes.splice(index, 1);
      }
    }
  }
});</code></pre>
      </div>

      <div class="code-block">
        <h4>Uso da Store em Componentes</h4>
        <pre><code>// Componente A: NotificacaoButton.vue
&lt;template&gt;
  &lt;div class="notificacao-button"&gt;
    &lt;button class="btn btn-icon" @click="toggleMenu"&gt;
      &lt;i class="fas fa-bell"&gt;&lt;/i&gt;
      &lt;span v-if="temNotificacoes" class="badge"&gt;{{ naoLidas }}&lt;/span&gt;
    &lt;/button&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';
import { useNotificacaoStore } from '@/stores/modules/notificacaoStore';

const notificacaoStore = useNotificacaoStore();

// Dados computados da store
const naoLidas = computed(() => notificacaoStore.naoLidas);
const temNotificacoes = computed(() => notificacaoStore.temNotificacoesNaoLidas);

function toggleMenu() {
  // Lógica para abrir/fechar menu de notificações
}
&lt;/script&gt;

// Componente B: NotificacaoList.vue
&lt;template&gt;
  &lt;div class="notificacao-list"&gt;
    &lt;div class="header"&gt;
      &lt;h3&gt;Notificações&lt;/h3&gt;
      &lt;button v-if="temNotificacoes" @click="marcarTodasComoLidas"&gt;
        Marcar todas como lidas
      &lt;/button&gt;
    &lt;/div&gt;
    
    &lt;div v-if="notificacoes.length === 0" class="empty-state"&gt;
      Nenhuma notificação
    &lt;/div&gt;
    
    &lt;ul v-else class="notificacao-items"&gt;
      &lt;li 
        v-for="notificacao in notificacoes" 
        :key="notificacao.id"
        :class="{ 'nao-lida': !notificacao.lida }"
      &gt;
        &lt;div class="content" @click="marcarComoLida(notificacao.id)"&gt;
          &lt;h4&gt;{{ notificacao.titulo }}&lt;/h4&gt;
          &lt;p&gt;{{ notificacao.mensagem }}&lt;/p&gt;
          &lt;span class="data"&gt;{{ formatarData(notificacao.data) }}&lt;/span&gt;
        &lt;/div&gt;
        &lt;button @click="removerNotificacao(notificacao.id)" class="btn-remove"&gt;
          &lt;i class="fas fa-times"&gt;&lt;/i&gt;
        &lt;/button&gt;
      &lt;/li&gt;
    &lt;/ul&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';
import { useNotificacaoStore } from '@/stores/modules/notificacaoStore';

const notificacaoStore = useNotificacaoStore();

// Dados computados da store
const notificacoes = computed(() => notificacaoStore.notificacoesOrdenadas);
const temNotificacoes = computed(() => notificacaoStore.temNotificacoesNaoLidas);

// Métodos que chamam actions da store
function marcarComoLida(id) {
  notificacaoStore.marcarComoLida(id);
}

function marcarTodasComoLidas() {
  notificacaoStore.marcarTodasComoLidas();
}

function removerNotificacao(id) {
  notificacaoStore.removerNotificacao(id);
}

// Utilitário para formatação de data
function formatarData(dataString) {
  // Lógica para formatar a data
  return new Date(dataString).toLocaleString();
}
&lt;/script&gt;

// Componente C: NotificacaoCreator.vue
&lt;template&gt;
  &lt;div class="notificacao-creator"&gt;
    &lt;button @click="criarNotificacaoTeste" class="btn btn-primary"&gt;
      Criar Notificação de Teste
    &lt;/button&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { useNotificacaoStore } from '@/stores/modules/notificacaoStore';

const notificacaoStore = useNotificacaoStore();

function criarNotificacaoTeste() {
  notificacaoStore.adicionarNotificacao({
    titulo: 'Notificação de Teste',
    mensagem: 'Esta é uma notificação de teste criada em ' + new Date().toLocaleTimeString(),
    tipo: 'info'
  });
}
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Boas Práticas para Stores Pinia</h4>
        <ul>
          <li><strong>Modularização</strong>: Divida as stores por domínio ou funcionalidade</li>
          <li><strong>Getters para Derivação</strong>: Use getters para dados derivados ou filtrados</li>
          <li><strong>Actions para Assincronicidade</strong>: Encapsule operações assíncronas em actions</li>
          <li><strong>Composição</strong>: Use composição de stores para reutilizar funcionalidades</li>
          <li><strong>Persistência</strong>: Configure persistência para dados que precisam sobreviver a recargas
          </li>
        </ul>
      </div>
    </div>
  </section>

  <section id="padroes" class="manual-section">
    <h2>7. Padrões e Convenções</h2>
    <p>Para manter a consistência e qualidade do código, adotamos um conjunto de padrões e convenções em todo o
      projeto.</p>

    <div id="nomenclatura" class="subsection">
      <h3>7.1. Nomenclatura</h3>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Elemento</th>
              <th>Convenção</th>
              <th>Exemplos</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Componentes Vue</td>
              <td>PascalCase</td>
              <td>UserProfile.vue, DataTable.vue</td>
            </tr>
            <tr>
              <td>Instâncias de componentes</td>
              <td>kebab-case</td>
              <td>&lt;user-profile&gt;, &lt;data-table&gt;</td>
            </tr>
            <tr>
              <td>Props</td>
              <td>camelCase</td>
              <td>userId, tableData</td>
            </tr>
            <tr>
              <td>Eventos</td>
              <td>kebab-case</td>
              <td>@item-selected, @form-submit</td>
            </tr>
            <tr>
              <td>Stores Pinia</td>
              <td>camelCase com prefixo "use"</td>
              <td>useUserStore, useAuthStore</td>
            </tr>
            <tr>
              <td>Composables</td>
              <td>camelCase com prefixo "use"</td>
              <td>useFormValidation, useDebounce</td>
            </tr>
            <tr>
              <td>Arquivos JavaScript</td>
              <td>camelCase</td>
              <td>formUtils.js, dateHelpers.js</td>
            </tr>
            <tr>
              <td>Constantes</td>
              <td>SNAKE_CASE maiúsculo</td>
              <td>API_URL, MAX_ITEMS</td>
            </tr>
            <tr>
              <td>Classes CSS</td>
              <td>kebab-case</td>
              <td>.user-card, .data-table</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="best-practice">
        <h4>Convenções para Nomes de Arquivos</h4>
        <ul>
          <li><strong>Componentes</strong>: PascalCase (ex: UserProfile.vue)</li>
          <li><strong>Views</strong>: PascalCase com sufixo "View" (ex: UsersView.vue)</li>
          <li><strong>Layouts</strong>: PascalCase com sufixo "Layout" (ex: DefaultLayout.vue)</li>
          <li><strong>Stores</strong>: camelCase com sufixo "Store" (ex: userStore.js)</li>
          <li><strong>Serviços</strong>: camelCase com sufixo "Service" (ex: userService.js)</li>
          <li><strong>Utilitários</strong>: camelCase descritivo (ex: dateUtils.js)</li>
        </ul>
      </div>
    </div>

    <div id="estilo-codigo" class="subsection">
      <h3>7.2. Estilo de Código</h3>

      <p>Seguimos as convenções de estilo recomendadas pela comunidade Vue.js e configuramos ferramentas de linting
        para garantir a consistência.</p>

      <div class="code-block">
        <h4>Configuração ESLint</h4>
        <pre><code>// .eslintrc.js
module.exports = {
  root: true,
  env: {
    node: true,
    'vue/setup-compiler-macros': true
  },
  extends: [
    'plugin:vue/vue3-recommended',
    'eslint:recommended',
    '@vue/eslint-config-prettier'
  ],
  parserOptions: {
    ecmaVersion: 2022
  },
  rules: {
    'vue/multi-word-component-names': 'error',
    'vue/require-default-prop': 'error',
    'vue/no-unused-vars': 'error',
    'vue/script-setup-uses-vars': 'error',
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
  }
}</code></pre>
      </div>

      <div class="code-block">
        <h4>Configuração Prettier</h4>
        <pre><code>// .prettierrc.js
module.exports = {
  semi: true,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'es5',
  printWidth: 100,
  endOfLine: 'lf'
}</code></pre>
      </div>

      <div class="best-practice">
        <h4>Padrões de Estilo de Código</h4>
        <ul>
          <li><strong>Indentação</strong>: 2 espaços</li>
          <li><strong>Aspas</strong>: Simples para strings</li>
          <li><strong>Ponto-e-vírgula</strong>: Sempre usar</li>
          <li><strong>Comprimento máximo de linha</strong>: 100 caracteres</li>
          <li><strong>Ordem em componentes Vue</strong>: name, components, props, data, computed, watch, lifecycle
            hooks, methods</li>
          <li><strong>Composition API</strong>: Agrupar código por funcionalidade, não por opção de API</li>
          <li><strong>Comentários</strong>: Usar JSDoc para funções públicas e complexas</li>
        </ul>
      </div>

      <div class="code-block">
        <h4>Exemplo de Estilo de Código em Componente Vue</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="user-card"&gt;
    &lt;div v-if="loading" class="loading-spinner"&gt;
      &lt;spinner-icon /&gt;
    &lt;/div&gt;
    
    &lt;template v-else&gt;
      &lt;div class="user-header"&gt;
        &lt;avatar :src="user.avatarUrl" :alt="user.name" /&gt;
        &lt;h3&gt;{{ user.name }}&lt;/h3&gt;
      &lt;/div&gt;
      
      &lt;div class="user-details"&gt;
        &lt;p&gt;&lt;strong&gt;Email:&lt;/strong&gt; {{ user.email }}&lt;/p&gt;
        &lt;p&gt;&lt;strong&gt;Cargo:&lt;/strong&gt; {{ user.role }}&lt;/p&gt;
        &lt;p&gt;&lt;strong&gt;Departamento:&lt;/strong&gt; {{ user.department }}&lt;/p&gt;
      &lt;/div&gt;
      
      &lt;div class="user-actions"&gt;
        &lt;button 
          class="btn btn-primary" 
          @click="$emit('edit', user.id)"
        &gt;
          Editar
        &lt;/button&gt;
        &lt;button 
          class="btn btn-danger" 
          @click="$emit('delete', user.id)"
        &gt;
          Excluir
        &lt;/button&gt;
      &lt;/div&gt;
    &lt;/template&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';
import Avatar from '@/components/ui/Avatar.vue';
import SpinnerIcon from '@/components/ui/SpinnerIcon.vue';

/**
 * Props do componente
 */
const props = defineProps({
  /**
   * Dados do usuário
   */
  user: {
    type: Object,
    required: true,
    validator: (user) => {
      return user.id && user.name && user.email;
    }
  },
  /**
   * Estado de carregamento
   */
  loading: {
    type: Boolean,
    default: false
  }
});

/**
 * Eventos emitidos pelo componente
 */
const emit = defineEmits(['edit', 'delete']);

/**
 * Nome completo formatado do usuário
 */
const formattedName = computed(() => {
  return props.user.name.toUpperCase();
});
&lt;/script&gt;

&lt;style scoped&gt;
.user-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.user-header h3 {
  margin: 0 0 0 12px;
  font-size: 18px;
}

.user-details {
  margin-bottom: 16px;
}

.user-details p {
  margin: 8px 0;
}

.user-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  padding: 24px;
}
&lt;/style&gt;</code></pre>
      </div>
    </div>

    <div id="organizacao" class="subsection">
      <h3>7.3. Organização de Arquivos</h3>

      <p>A organização adequada de arquivos é essencial para a manutenibilidade do projeto. Seguimos uma estrutura
        modular e orientada a funcionalidades.</p>

      <div class="best-practice">
        <h4>Princípios de Organização</h4>
        <ul>
          <li><strong>Coesão</strong>: Arquivos relacionados devem estar próximos</li>
          <li><strong>Modularidade</strong>: Organização por funcionalidade ou domínio</li>
          <li><strong>Previsibilidade</strong>: Estrutura consistente em todo o projeto</li>
          <li><strong>Escalabilidade</strong>: Facilidade para adicionar novos recursos</li>
        </ul>
      </div>

      <div class="code-block">
        <h4>Exemplo de Organização de Módulo</h4>
        <pre><code>src/modules/usuarios/
├── components/              # Componentes específicos do módulo
│   ├── UserForm.vue         # Formulário de usuário
│   ├── UserList.vue         # Lista de usuários
│   └── UserFilters.vue      # Filtros para a lista de usuários
├── composables/             # Composables específicos do módulo
│   └── useUserValidation.js # Validação de usuários
├── views/                   # Páginas do módulo
│   ├── UsersView.vue        # Página principal de usuários
│   ├── UserCreateView.vue   # Página de criação de usuário
│   └── UserEditView.vue     # Página de edição de usuário
├── services/                # Serviços específicos do módulo
│   └── userService.js       # Serviço de API para usuários
└── store/                   # Store específica do módulo
    └── userStore.js         # Store Pinia para usuários</code></pre>
      </div>

      <div class="alerts-section">
        <h4>Organização de Componentes Reutilizáveis</h4>
        <p>Componentes reutilizáveis são organizados por tipo e complexidade:</p>
        <ul>
          <li><strong>components/ui/</strong>: Componentes básicos de UI (Button, Card, etc.)</li>
          <li><strong>components/forms/</strong>: Componentes de formulário (Input, Select, etc.)</li>
          <li><strong>components/data/</strong>: Componentes de visualização de dados (Table, Chart, etc.)</li>
          <li><strong>components/layout/</strong>: Componentes de layout (Header, Sidebar, etc.)</li>
          <li><strong>components/common/</strong>: Componentes complexos reutilizáveis (Modal, Notification, etc.)
          </li>
        </ul>
      </div>

      <div class="best-practice">
        <h4>Importações e Aliases</h4>
        <p>Utilizamos aliases para simplificar as importações e evitar caminhos relativos complexos:</p>
        <pre><code>// vite.config.js
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@composables': path.resolve(__dirname, './src/composables'),
      '@views': path.resolve(__dirname, './src/views'),
      '@stores': path.resolve(__dirname, './src/stores'),
      '@services': path.resolve(__dirname, './src/services'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@assets': path.resolve(__dirname, './src/assets'),
    }
  }
});</code></pre>
        <p>Exemplo de uso:</p>
        <pre><code>// Antes
import Button from '../../../components/ui/Button.vue';
import { useUserStore } from '../../../stores/modules/userStore';

// Depois
import Button from '@components/ui/Button.vue';
import { useUserStore } from '@stores/modules/userStore';</code></pre>
      </div>
    </div>
  </section>

  <section id="decisoes" class="manual-section">
    <h2>8. Decisões Arquiteturais</h2>
    <p>Esta seção explica as principais decisões arquiteturais tomadas no projeto e suas justificativas.</p>

    <div id="vue3" class="subsection">
      <h3>8.1. Por que Vue 3 + Composition API</h3>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Característica</th>
              <th>Benefício</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Composition API</td>
              <td>Melhor organização de código, reutilização de lógica e tipagem</td>
            </tr>
            <tr>
              <td>Script Setup</td>
              <td>Sintaxe mais concisa e menos boilerplate</td>
            </tr>
            <tr>
              <td>Teleport</td>
              <td>Renderização de conteúdo em qualquer parte do DOM</td>
            </tr>
            <tr>
              <td>Fragments</td>
              <td>Múltiplos elementos raiz em componentes</td>
            </tr>
            <tr>
              <td>Suspense</td>
              <td>Melhor gerenciamento de estados de carregamento</td>
            </tr>
            <tr>
              <td>Melhor suporte a TypeScript</td>
              <td>Tipagem mais precisa e melhor experiência de desenvolvimento</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="code-block">
        <h4>Exemplo de Composition API vs Options API</h4>
        <pre><code>// Options API (Vue 2)
export default {
  props: {
    initialCounter: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      counter: this.initialCounter
    }
  },
  computed: {
    doubleCounter() {
      return this.counter * 2
    }
  },
  methods: {
    increment() {
      this.counter++
    }
  },
  watch: {
    counter(newValue) {
      console.log(`Counter changed to: ${newValue}`)
    }
  }
}

// Composition API (Vue 3)
import { ref, computed, watch } from 'vue'

export default {
  props: {
    initialCounter: {
      type: Number,
      default: 0
    }
  },
  setup(props) {
    // Estado
    const counter = ref(props.initialCounter)
    
    // Computados
    const doubleCounter = computed(() => counter.value * 2)
    
    // Métodos
    function increment() {
      counter.value++
    }
    
    // Watchers
    watch(counter, (newValue) => {
      console.log(`Counter changed to: ${newValue}`)
    })
    
    // Expor para o template
    return {
      counter,
      doubleCounter,
      increment
    }
  }
}

// Script Setup (Vue 3 - mais conciso)
&lt;script setup&gt;
import { ref, computed, watch } from 'vue'

const props = defineProps({
  initialCounter: {
    type: Number,
    default: 0
  }
})

// Estado
const counter = ref(props.initialCounter)

// Computados
const doubleCounter = computed(() => counter.value * 2)

// Métodos
function increment() {
  counter.value++
}

// Watchers
watch(counter, (newValue) => {
  console.log(`Counter changed to: ${newValue}`)
})
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Vantagens da Composition API</h4>
        <ul>
          <li><strong>Melhor organização de código</strong>: Agrupamento por funcionalidade em vez de por opções
          </li>
          <li><strong>Reutilização de lógica</strong>: Composables permitem compartilhar lógica entre componentes
          </li>
          <li><strong>Melhor tipagem</strong>: Suporte nativo a TypeScript sem necessidade de decoradores</li>
          <li><strong>Melhor performance</strong>: Tree-shaking mais eficiente e menor overhead</li>
          <li><strong>Melhor IDE support</strong>: Autocompletion e verificação de tipos mais precisa</li>
        </ul>
      </div>
    </div>

    <div id="pinia" class="subsection">
      <h3>8.2. Por que Pinia em vez de Vuex</h3>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Característica</th>
              <th>Vuex 4</th>
              <th>Pinia</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>API</td>
              <td>Baseada em objetos e strings</td>
              <td>Baseada em funções e tipagem</td>
            </tr>
            <tr>
              <td>Modularização</td>
              <td>Módulos aninhados</td>
              <td>Stores independentes</td>
            </tr>
            <tr>
              <td>Boilerplate</td>
              <td>Mais verboso</td>
              <td>Mais conciso</td>
            </tr>
            <tr>
              <td>TypeScript</td>
              <td>Suporte limitado</td>
              <td>Suporte completo</td>
            </tr>
            <tr>
              <td>DevTools</td>
              <td>Integração básica</td>
              <td>Integração avançada</td>
            </tr>
            <tr>
              <td>Mutações</td>
              <td>Obrigatórias</td>
              <td>Não necessárias</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="code-block">
        <h4>Exemplo de Vuex vs Pinia</h4>
        <pre><code>// Vuex 4
// store/modules/counter.js
export default {
  namespaced: true,
  state: () => ({
    count: 0
  }),
  getters: {
    doubleCount: (state) => state.count * 2
  },
  mutations: {
    INCREMENT: (state) => {
      state.count++
    },
    SET_COUNT: (state, payload) => {
      state.count = payload
    }
  },
  actions: {
    increment({ commit }) {
      commit('INCREMENT')
    },
    setCount({ commit }, payload) {
      commit('SET_COUNT', payload)
    }
  }
}

// Uso do Vuex
import { useStore } from 'vuex'
import { computed } from 'vue'

export default {
  setup() {
    const store = useStore()
    
    return {
      // Getters
      count: computed(() => store.state.counter.count),
      doubleCount: computed(() => store.getters['counter/doubleCount']),
      
      // Actions
      increment: () => store.dispatch('counter/increment'),
      setCount: (value) => store.dispatch('counter/setCount', value)
    }
  }
}

// Pinia
// stores/counterStore.js
import { defineStore } from 'pinia'

export const useCounterStore = defineStore('counter', {
  state: () => ({
    count: 0
  }),
  getters: {
    doubleCount: (state) => state.count * 2
  },
  actions: {
    increment() {
      this.count++
    },
    setCount(value) {
      this.count = value
    }
  }
})

// Uso do Pinia
import { useCounterStore } from '@/stores/counterStore'
import { storeToRefs } from 'pinia'

export default {
  setup() {
    const counterStore = useCounterStore()
    
    // Desestruturação reativa com storeToRefs
    const { count, doubleCount } = storeToRefs(counterStore)
    
    return {
      // Estado e getters reativos
      count,
      doubleCount,
      
      // Actions
      increment: counterStore.increment,
      setCount: counterStore.setCount
    }
  }
}</code></pre>
      </div>

      <div class="best-practice">
        <h4>Vantagens do Pinia</h4>
        <ul>
          <li><strong>API mais simples</strong>: Sem necessidade de mutations, namespaces ou módulos aninhados
          </li>
          <li><strong>Melhor tipagem</strong>: Suporte completo a TypeScript com inferência automática</li>
          <li><strong>Modularidade</strong>: Stores independentes facilitam a organização e o code-splitting</li>
          <li><strong>Menos boilerplate</strong>: Sintaxe mais concisa e direta</li>
          <li><strong>Melhor DevTools</strong>: Integração avançada com Vue DevTools</li>
          <li><strong>Composição</strong>: Facilidade para compor e reutilizar lógica entre stores</li>
        </ul>
      </div>
    </div>

    <div id="bootstrap" class="subsection">
      <h3>8.3. Por que Bootstrap-Vue</h3>

      <p>Escolhemos o Bootstrap-Vue como nossa biblioteca de componentes UI pelos seguintes motivos:</p>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Característica</th>
              <th>Benefício</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Componentes prontos</td>
              <td>Ampla biblioteca de componentes UI testados e acessíveis</td>
            </tr>
            <tr>
              <td>Integração com Vue</td>
              <td>Componentes nativos Vue com props e eventos</td>
            </tr>
            <tr>
              <td>Responsividade</td>
              <td>Sistema de grid e utilitários responsivos</td>
            </tr>
            <tr>
              <td>Customização</td>
              <td>Fácil personalização via variáveis SCSS</td>
            </tr>
            <tr>
              <td>Documentação</td>
              <td>Documentação completa e exemplos</td>
            </tr>
            <tr>
              <td>Comunidade</td>
              <td>Grande comunidade e suporte</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="code-block">
        <h4>Exemplo de Uso do Bootstrap-Vue</h4>
        <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;b-card title="Formulário de Usuário" class="mb-4"&gt;
      &lt;b-form @submit.prevent="onSubmit"&gt;
        &lt;b-form-group
          label="Nome"
          label-for="input-name"
          :invalid-feedback="errors.name"
          :state="!errors.name"
        &gt;
          &lt;b-form-input
            id="input-name"
            v-model="form.name"
            :state="!errors.name"
            trim
          &gt;&lt;/b-form-input&gt;
        &lt;/b-form-group&gt;
        
        &lt;b-form-group
          label="Email"
          label-for="input-email"
          :invalid-feedback="errors.email"
          :state="!errors.email"
        &gt;
          &lt;b-form-input
            id="input-email"
            v-model="form.email"
            type="email"
            :state="!errors.email"
            trim
          &gt;&lt;/b-form-input&gt;
        &lt;/b-form-group&gt;
        
        &lt;b-form-group label="Departamento" label-for="input-department"&gt;
          &lt;b-form-select
            id="input-department"
            v-model="form.department"
            :options="departmentOptions"
          &gt;&lt;/b-form-select&gt;
        &lt;/b-form-group&gt;
        
        &lt;b-button type="submit" variant="primary" :disabled="isSubmitting"&gt;
          &lt;b-spinner small v-if="isSubmitting"&gt;&lt;/b-spinner&gt;
          {{ isSubmitting ? 'Salvando...' : 'Salvar' }}
        &lt;/b-button&gt;
      &lt;/b-form&gt;
    &lt;/b-card&gt;
    
    &lt;b-alert
      v-model="showSuccessAlert"
      variant="success"
      dismissible
      fade
    &gt;
      Usuário salvo com sucesso!
    &lt;/b-alert&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, reactive } from 'vue';

const form = reactive({
  name: '',
  email: '',
  department: null
});

const errors = reactive({
  name: '',
  email: ''
});

const isSubmitting = ref(false);
const showSuccessAlert = ref(false);

const departmentOptions = [
  { value: null, text: 'Selecione um departamento' },
  { value: 'ti', text: 'Tecnologia da Informação' },
  { value: 'rh', text: 'Recursos Humanos' },
  { value: 'financeiro', text: 'Financeiro' },
  { value: 'marketing', text: 'Marketing' }
];

const validate = () => {
  let isValid = true;
  
  // Validar nome
  if (!form.name) {
    errors.name = 'O nome é obrigatório';
    isValid = false;
  } else {
    errors.name = '';
  }
  
  // Validar email
  if (!form.email) {
    errors.email = 'O email é obrigatório';
    isValid = false;
  } else if (!/\S+@\S+\.\S+/.test(form.email)) {
    errors.email = 'Email inválido';
    isValid = false;
  } else {
    errors.email = '';
  }
  
  return isValid;
};

const onSubmit = async () => {
  if (!validate()) return;
  
  isSubmitting.value = true;
  
  try {
    // Simulação de chamada de API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Limpar formulário
    form.name = '';
    form.email = '';
    form.department = null;
    
    // Mostrar alerta de sucesso
    showSuccessAlert.value = true;
  } catch (error) {
    console.error('Erro ao salvar usuário:', error);
  } finally {
    isSubmitting.value = false;
  }
};
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Customização do Bootstrap</h4>
        <p>Personalizamos o Bootstrap para se adequar à identidade visual do projeto:</p>
        <pre><code>// src/assets/scss/_variables.scss
$primary: #3490dc;
$secondary: #6c757d;
$success: #38c172;
$info: #6cb2eb;
$warning: #ffed4a;
$danger: #e3342f;
$light: #f8f9fa;
$dark: #343a40;

$border-radius: 0.25rem;
$border-radius-lg: 0.3rem;
$border-radius-sm: 0.2rem;

$font-family-sans-serif: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-size-base: 0.9rem;
$line-height-base: 1.6;

// src/assets/scss/main.scss
@import 'variables';
@import 'bootstrap/scss/bootstrap';

// Customizações adicionais
.btn {
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}</code></pre>
      </div>

      <div class="alerts-section">
        <h4>Alternativas Consideradas</h4>
        <p>Antes de escolher o Bootstrap-Vue, consideramos outras bibliotecas de componentes:</p>
        <ul>
          <li><strong>Vuetify</strong>: Mais completo, mas com design Material que não se alinhava com nossa
            identidade visual</li>
          <li><strong>Quasar</strong>: Excelente para aplicações móveis, mas mais complexo para nosso caso de uso
          </li>
          <li><strong>PrimeVue</strong>: Boa biblioteca, mas com menos recursos de acessibilidade</li>
          <li><strong>Tailwind CSS</strong>: Considerado para uma abordagem utility-first, mas optamos por
            componentes prontos para agilizar o desenvolvimento</li>
        </ul>
      </div>
    </div>
  </section>

  <section id="conclusao" class="manual-section">
    <h2>9. Conclusão</h2>

    <div class="conclusion">
      <h3>Resumo da Arquitetura</h3>
      <p>Nossa arquitetura foi projetada para ser modular, escalável e manutenível, com separação clara de
        responsabilidades entre frontend e backend. O frontend Vue.js 3 com Composition API e Pinia oferece uma
        experiência de desenvolvimento moderna e produtiva, enquanto o backend Laravel fornece uma API robusta e
        segura.</p>

      <p>Os principais pontos da arquitetura incluem:</p>
      <ul>
        <li>Aplicação SPA com Vue.js 3 e Composition API</li>
        <li>Gerenciamento de estado centralizado com Pinia</li>
        <li>Componentes UI com Bootstrap-Vue</li>
        <li>API RESTful com Laravel</li>
        <li>Comunicação cliente-servidor via Axios</li>
        <li>Estrutura modular e orientada a funcionalidades</li>
      </ul>
    </div>

    <div class="best-practice">
      <h4>Próximos Passos</h4>
      <ul>
        <li>Consulte o <strong>Manual de Configuração de Ambiente</strong> para configurar seu ambiente de
          desenvolvimento</li>
        <li>Explore o <strong>Manual de Componentes e Padrões UI</strong> para entender os componentes disponíveis
        </li>
        <li>Veja o <strong>Manual de Gerenciamento de Estado com Pinia</strong> para aprender a trabalhar com o
          estado da aplicação</li>
        <li>Consulte o <strong>Manual de Integração com API</strong> para entender como se comunicar com o backend
        </li>
      </ul>
    </div>

    <div class="checklist-container">
      <div class="checklist-group">
        <h3>Checklist para Novos Desenvolvedores</h3>
        <ul class="checklist">
          <li>
            <input type="checkbox" id="check1">
            <label for="check1">Configurar ambiente de desenvolvimento</label>
          </li>
          <li>
            <input type="checkbox" id="check2">
            <label for="check2">Estudar a estrutura de pastas do projeto</label>
          </li>
          <li>
            <input type="checkbox" id="check3">
            <label for="check3">Revisar os padrões de código e convenções</label>
          </li>
          <li>
            <input type="checkbox" id="check4">
            <label for="check4">Entender o fluxo de dados da aplicação</label>
          </li>
          <li>
            <input type="checkbox" id="check5">
            <label for="check5">Familiarizar-se com os componentes principais</label>
          </li>
          <li>
            <input type="checkbox" id="check6">
            <label for="check6">Aprender a usar as stores Pinia</label>
          </li>
          <li>
            <input type="checkbox" id="check7">
            <label for="check7">Entender a comunicação com a API</label>
          </li>
          <li>
            <input type="checkbox" id="check8">
            <label for="check8">Executar os testes automatizados</label>
          </li>
        </ul>
      </div>

      <div class="checklist-group">
        <h3>Recursos Adicionais</h3>
        <ul>
          <li><a href="https://vuejs.org/guide/introduction.html" target="_blank">Documentação oficial do Vue.js
              3</a></li>
          <li><a href="https://pinia.vuejs.org/" target="_blank">Documentação do Pinia</a></li>
          <li><a href="https://bootstrap-vue.org/" target="_blank">Documentação do Bootstrap-Vue</a></li>
          <li><a href="https://laravel.com/docs/10.x" target="_blank">Documentação do Laravel 10</a></li>
          <li><a href="https://axios-http.com/docs/intro" target="_blank">Documentação do Axios</a></li>
          <li><a href="https://router.vuejs.org/" target="_blank">Documentação do Vue Router</a></li>
          <li><a href="https://vitest.dev/" target="_blank">Documentação do Vitest</a></li>
        </ul>
      </div>
    </div>

    <div class="final-notes">
      <h3>Considerações Finais</h3>
      <p>Esta arquitetura foi projetada para equilibrar produtividade, manutenibilidade e desempenho. À medida que
        o projeto evolui, a arquitetura também pode evoluir para atender a novos requisitos e desafios.</p>

      <p>Lembre-se de que as melhores práticas e padrões descritos neste manual são diretrizes, não regras
        rígidas. Use seu julgamento e experiência para adaptar essas práticas quando necessário, sempre mantendo a
        consistência e a qualidade do código.</p>

      <p>Se tiver dúvidas ou sugestões sobre a arquitetura, consulte a equipe de desenvolvimento ou contribua para
        a evolução deste manual.</p>
    </div>
  </section>

  <footer class="manual-footer">
    <p>&copy; 2023 - Sistema de Gestão - Todos os direitos reservados</p>
    <p>Versão 1.0 - Última atualização: Outubro/2023</p>
  </footer>
</body>

</html>