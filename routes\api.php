<?php

use App\Controllers\AuthController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;

Route::group([
    'middleware' => 'api',
    'prefix' => 'auth',
    'controller' => AuthController::class,
], function () {
    // Rotas públicas
    Route::post('/register', 'register')->name('register');
    Route::post('/login', 'login')->name('login');

    // Rotas protegidas
    Route::middleware('jwt.verify')->group(function () {
        Route::post('/logout', 'logout')->name('logout');
        Route::get('/me', 'me')->name('me');
    });

    // Rota de refresh não precisa de autenticação, apenas do refresh token
    Route::post('/refresh', 'refresh')->name('refresh');
});

// Rota de teste para consultas SQL
Route::get('/sql-test', function () {
    // Habilitar o log de consultas
    DB::enableQueryLog();

    // Executar algumas consultas SQL
    $users = DB::table('users')->limit(5)->get();
    $usersCount = DB::table('users')->count();

    // Outras consultas para teste
    DB::table('users')->where('id', '>', 10)->count();
    DB::table('users')->where('email', 'like', '%gmail%')->get();

    // Obter o log de consultas
    $queries = DB::getQueryLog();

    // Formatar as consultas para exibição
    $formattedQueries = [];
    foreach ($queries as $query) {
        // Substituir parâmetros no SQL
        $sql = $query['query'];
        $bindings = $query['bindings'];

        foreach ($bindings as $binding) {
            $value = is_numeric($binding) ? $binding : "'" . $binding . "'";
            $sql = preg_replace('/\?/', $value, $sql, 1);
        }

        $formattedQueries[] = [
            'sql' => $sql,
            'time' => $query['time'] / 1000 . ' segundos',
        ];
    }

    // Retornar os resultados em formato JSON
    return response()->json([
        'success' => true,
        'data' => [
            'users_count' => $usersCount,
            'users' => $users,
            'queries' => $formattedQueries,
        ],
    ]);
});
