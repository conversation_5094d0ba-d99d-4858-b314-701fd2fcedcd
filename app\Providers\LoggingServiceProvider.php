<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Log\LogManager;
use Illuminate\Cache\RateLimiter;
use Monolog\Level;
use Monolog\Processor\WebProcessor;
use Monolog\Processor\IntrospectionProcessor;
use Monolog\Processor\MemoryUsageProcessor;
use Monolog\Processor\GitProcessor;
use Monolog\Handler\RotatingFileHandler;
use App\Mail\ExceptionOccurred;
use Illuminate\Support\Facades\Mail;

class LoggingServiceProvider extends ServiceProvider
{
    // Usando Monolog\Level em vez das constantes depreciadas
    protected $criticalLevels = [
        Level::Emergency->value,
        Level::Alert->value,
        Level::Critical->value
    ];

    public function boot(): void
    {
        $this->app->afterResolving(LogManager::class, function (LogManager $logManager) {
            // Configurar listener para todos os logs
            $logManager->listen(function ($level, $message, $context) {
                // <PERSON><PERSON> o contexto
                $enrichedContext = $this->enrichContext($context);

                // Converte o nível para o valor numérico do Monolog
                $levelValue = $this->getLevelValue($level);

                // Verifica se é um erro crítico
                if (in_array($levelValue, $this->criticalLevels)) {
                    $this->handleCriticalError($level, $message, $enrichedContext);
                }

                return [$level, $message, $enrichedContext];
            });

            $this->configureChannels($logManager);
        });
    }

    /**
     * Converte o nível de log do Laravel para o valor numérico do Monolog
     */
    protected function getLevelValue(string $level): int
    {
        return match (strtolower($level)) {
            'emergency' => Level::Emergency->value,
            'alert'     => Level::Alert->value,
            'critical'  => Level::Critical->value,
            'error'     => Level::Error->value,
            'warning'   => Level::Warning->value,
            'notice'    => Level::Notice->value,
            'info'      => Level::Info->value,
            'debug'     => Level::Debug->value,
            default     => Level::Debug->value,
        };
    }

    protected function handleCriticalError($level, $message, array $context): void
    {
        $limiter = app(RateLimiter::class);
        $key = 'critical_error_notification';

        // Limita a 1 notificação a cada 5 minutos
        if (!$limiter->tooManyAttempts($key, 1)) {
            try {
                // Envia email com os detalhes do erro
                Mail::to(config('mail.admin_address'))
                    ->queue(new ExceptionOccurred(
                        new \Exception($message),
                        request()->fullUrl(),
                        'Critical Error'
                    ));

                // Registra a tentativa com TTL de 5 minutos
                $limiter->hit($key, 300);
            } catch (\Throwable $e) {
                logger()->error('Falha ao enviar notificação de erro crítico', [
                    'error' => $e->getMessage(),
                    'original_message' => $message,
                    'context' => $context
                ]);
            }
        }
    }

    protected function enrichContext(array $context): array
    {
        $context['environment'] = app()->environment();
        $context['memory_usage'] = memory_get_usage(true);
        $context['execution_time'] = microtime(true) - LARAVEL_START;
        $context['server'] = gethostname();
        $context['timestamp'] = now()->toIso8601String();

        if (request()) {
            $context['request'] = $this->getRequestContext();
        }

        if (auth()->check()) {
            $context['user'] = $this->getUserContext();
        }

        return $context;
    }

    protected function getRequestContext(): array
    {
        $requestId = request()->header('X-Request-ID') ?? Str::uuid()->toString();

        return [
            'id' => $requestId,
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'headers' => collect(request()->headers->all())
                ->except(['authorization', 'cookie'])
                ->toArray(),
            'input' => request()->except(['password', 'password_confirmation']),
        ];
    }


    protected function getUserContext(): array
    {
        $user = auth()->user();
        return [
            'id' => $user->id,
            'email' => $user->email,
            'type' => class_basename($user),
            'last_activity' => now()->toIso8601String(),
        ];
    }

    protected function configureChannels(LogManager $logManager): void
    {
        foreach ($logManager->getChannels() as $channel) {
            $logger = $channel->getLogger();

            if (method_exists($logger, 'pushProcessor')) {
                $this->addProcessors($logger);
                $this->configureRotation($logger);
            }
        }
    }

    protected function addProcessors($logger): void
    {
        $logger->pushProcessor(new WebProcessor());
        $logger->pushProcessor(new IntrospectionProcessor(
            Level::Debug,
            ['Illuminate\\', 'App\\']
        ));
        $logger->pushProcessor(new MemoryUsageProcessor());

        // Verifica se o GitProcessor está disponível
        if (class_exists(GitProcessor::class)) {
            $logger->pushProcessor(new GitProcessor());
        }

        // Adiciona processor customizado para enriquecer logs
        $logger->pushProcessor(function ($record) {
            $record['extra']['process_id'] = getmypid();
            $record['extra']['memory_peak'] = memory_get_peak_usage(true);
            return $record;
        });
    }

    protected function configureRotation($logger): void
    {
        foreach ($logger->getHandlers() as $handler) {
            if ($handler instanceof RotatingFileHandler) {
                $handler->setFilenameFormat('{filename}-{date}', 'Y-m-d');
            }
        }
    }
}
