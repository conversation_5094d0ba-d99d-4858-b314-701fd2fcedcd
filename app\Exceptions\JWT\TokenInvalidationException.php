<?php

namespace App\Exceptions\JWT;

class TokenInvalidationException extends \Exception
{
    protected $userId;
    protected $token;

    public function __construct(string $message = "Erro ao invalidar token", int $userId = 0, string $token = '', \Throwable $previous = null)
    {
        $this->userId = $userId;
        $this->token = $token;
        parent::__construct($message, 0, $previous);
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getTokenPreview(): string
    {
        return substr($this->token, 0, 10) . '...';
    }

    public function getContext(): array
    {
        return [
            'user_id' => $this->userId,
            'token' => $this->getTokenPreview(),
        ];
    }
}
