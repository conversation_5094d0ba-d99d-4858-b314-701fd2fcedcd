<?php

namespace App\Http\Sistema\Micro1\Repositories;

use App\Models\UserModel;
use App\Repositories\RepositoryAbstract;

class ExampleRepository extends RepositoryAbstract
{
    /**
     * Construtor
     *
     * @param UserModel $model
     */
    public function __construct(UserModel $model)
    {
        parent::__construct($model);
    }

    /**
     * Encontra um usuário pelo email
     *
     * @param string $email
     * @return UserModel|null
     */
    public function findByEmail(string $email): ?UserModel
    {
        return $this->model->where('email', $email)->first();
    }
}
