<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Controllers\AuthController;

Route::group([
    'middleware' => 'api',
    'prefix' => 'auth',
    'controller' => AuthController::class,
], function () {
    // Rotas públicas
    Route::post('/register', 'register')->name('register');
    Route::post('/login', 'login')->name('login');
    Route::post('/refresh', 'refresh')->name('refresh');

    Route::get('/health', function () {
        return response()->json([
            'status' => 'healthy',
            'timestamp' => now()->toIso8601String(),
            'version' => config('app.version'),
            'environment' => app()->environment(),
        ]);
    });

    // Rotas protegidas
    Route::middleware('auth:api')->group(function () {
        Route::post('/logout', 'logout')->name('logout');
        Route::post('/logout-all', 'logoutAll')->name('logout.all');
        Route::get('/me', 'me')->name('me');
        Route::get('/sessions', 'sessions')->name('sessions');
    });
});
