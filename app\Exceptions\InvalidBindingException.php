<?php

namespace App\Exceptions;

use Exception;
use Throwable;
use Psr\SimpleCache\InvalidArgumentException;

/**
 * Exceção lançada quando ocorre um erro ao registrar bindings
 */
class InvalidBindingException extends Exception implements InvalidArgumentException
{
    /**
     * Contexto adicional do erro
     * @var array
     */
    protected array $context;

    /**
     * Construtor
     *
     * @param string $message Mensagem de erro
     * @param int $code Código de erro
     * @param Throwable|InvalidArgumentException|null $previous Exceção anterior
     * @param array $context Contexto adicional
     */
    public function __construct(
        string $message = "",
        int $code = 0,
        $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * Obtém o contexto do erro
     *
     * @return array
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Cria uma exceção para interface não implementada
     *
     * @param string $concrete Classe concreta
     * @param string $abstract Interface esperada
     * @return self
     */
    public static function forMissingInterface(
        string $concrete,
        string $abstract
    ): self {
        return new self(
            "Classe {$concrete} deve implementar {$abstract}",
            500,
            null,
            ['concrete' => $concrete, 'abstract' => $abstract]
        );
    }

    /**
     * Cria uma exceção para classe não encontrada
     *
     * @param string $className Nome da classe
     * @return self
     */
    public static function forClassNotFound(string $className): self
    {
        return new self(
            "Classe {$className} não encontrada",
            404,
            null,
            ['class' => $className]
        );
    }

    /**
     * Cria uma exceção para interface não encontrada
     *
     * @param string $interfaceName Nome da interface
     * @return self
     */
    public static function forInterfaceNotFound(string $interfaceName): self
    {
        return new self(
            "Interface {$interfaceName} não encontrada",
            404,
            null,
            ['interface' => $interfaceName]
        );
    }
}
