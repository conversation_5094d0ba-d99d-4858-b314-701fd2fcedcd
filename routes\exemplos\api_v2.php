<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Controllers\AuthController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Rotas da API com versionamento e middlewares apropriados
|
*/

// Rota de Health Check
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now()->toIso8601String(),
        'version' => config('app.version', '1.0'),
        'environment' => app()->environment(),
        'database' => DB::connection()->getPdo() ? 'connected' : 'failed',
        'cache' => Cache::store()->getStore() instanceof \Illuminate\Cache\Repository ? 'connected' : 'failed',
        'request_id' => request()->header('X-Request-ID'),
    ]);
});

// Grupo principal da API v1
Route::prefix('v1')->group(function () {

    // Rotas de Autenticação
    Route::group([
        'prefix' => 'auth',
        'controller' => AuthController::class,
    ], function () {
        // Rotas públicas com throttling
        Route::middleware(['api-throttle'])->group(function () {
            Route::post('/register', 'register')->name('auth.register');
            Route::post('/login', 'login')->name('auth.login');
        });

        // Rotas protegidas
        Route::middleware(['auth.api'])->group(function () {
            Route::post('/logout', 'logout')->name('auth.logout');
            Route::post('/refresh', 'refresh')->name('auth.refresh');
            Route::get('/me', 'me')->name('auth.me');
        });
    });

    // Exemplo de rotas com cache
    Route::middleware(['cache.api:30'])->group(function () {
        Route::get('/public-data', function () {
            // Dados que podem ser cacheados
            $data = [
                'message' => 'Estes dados são cacheados por 30 minutos',
                'timestamp' => now()->toIso8601String(),
            ];

            return response()->json($data);
        });
    });
});

// Fallback para rotas não encontradas
Route::fallback(function () {
    return response()->json([
        'status' => 'error',
        'message' => 'Route not found.',
        'code' => 404,
        'timestamp' => now()->toIso8601String(),
        'api_version' => config('app.api_version', '1.0'),
        'request_id' => request()->header('X-Request-ID'),
    ], 404);
});
