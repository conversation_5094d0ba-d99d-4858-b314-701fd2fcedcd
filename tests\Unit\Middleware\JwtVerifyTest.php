<?php

namespace Tests\Unit\Middleware;

use App\Middleware\JwtVerify;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Mockery;
use PHPOpenSourceSaver\JWTAuth\Exceptions\TokenExpiredException;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class JwtVerifyTest extends TestCase
{
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new JwtVerify();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    #[Test]
    public function it_allows_valid_token()
    {
        // Mock para JWTAuth
        JWTAuth::shouldReceive('parseToken')
            ->once()
            ->andReturnSelf();

        JWTAuth::shouldReceive('authenticate')
            ->once()
            ->andReturn((object)['id' => 1]);

        // Criar requisição
        $request = new Request();

        // Criar callback next
        $next = function ($req) {
            return new Response('OK');
        };

        $response = $this->middleware->handle($request, $next);
        $this->assertEquals('OK', $response->getContent());
    }

    #[Test]
    public function it_returns_error_for_expired_token()
    {
        // Mock para JWTAuth
        JWTAuth::shouldReceive('parseToken')
            ->once()
            ->andReturnSelf();

        JWTAuth::shouldReceive('authenticate')
            ->once()
            ->andThrow(new TokenExpiredException('Token has expired'));

        // Criar requisição
        $request = new Request();

        // Criar callback next
        $next = function ($req) {
            return new Response('OK');
        };

        $response = $this->middleware->handle($request, $next);
        $responseData = json_decode($response->getContent(), true);

        $this->assertEquals(401, $response->getStatusCode());
        $this->assertEquals('error', $responseData['status']);
        $this->assertEquals('Token has expired', $responseData['message']);
        $this->assertTrue($responseData['should_refresh']);
    }
}
