<?php

namespace Tests\Feature;

use App\Models\UserModel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function user_can_register()
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ];

        $response = $this->postJson('/api/auth/register', $userData);

        // Primeiro, vamos verificar o status e imprimir a resposta para debug
        if ($response->getStatusCode() != 201) {
            echo "Resposta: " . $response->getContent() . "\n";
        }

        // Ajuste a estrutura esperada com base na resposta real da sua API
        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'user' => [
                        'name',
                        'email'
                    ],
                    'authorization' => [
                        'token',
                        'type'
                    ]
                ],
                'message'
            ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>'
        ]);
    }

    #[Test]
    public function user_can_login()
    {
        // Criar usuário para teste
        $user = UserModel::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123')
        ]);

        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // Ajuste a estrutura esperada com base na resposta real da API
        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'code',
                'timestamp',
                'api_version',
                'request_id',
                'data' => [
                    'authorization' => [
                        'token',
                        'refresh_token',
                        'type',
                        'expires_in'
                    ]
                ]
            ]);
    }

    #[Test]
    public function it_rejects_invalid_credentials()
    {
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);

        $response->assertStatus(401);
    }

    #[Test]
    public function it_requires_valid_email_for_registration()
    {
        $response = $this->postJson('/api/auth/register', [
            'name' => 'Test User',
            'email' => 'invalid-email',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ]);

        $response->assertStatus(400)
            ->assertJsonFragment(['email' => ['The email field must be a valid email address.']]);
    }



    /*
    #[Test]
    public function debug_login_response_structure()
    {
        // Criar usuário para teste
        $user = UserModel::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123')
        ]);

        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // Imprime a resposta completa
        echo $response->getContent();

        // Sempre passa para que possamos ver a saída
        $this->assertTrue(true);
    }
        */
}
