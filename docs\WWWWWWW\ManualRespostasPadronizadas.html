<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Respostas Padronizadas</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #2980b9;
            margin-top: 40px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        h3 {
            color: #3498db;
            margin-top: 25px;
        }

        .code-section {
            margin-bottom: 50px;
        }

        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', Courier, monospace;
            overflow-x: auto;
        }

        .example {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .example h4 {
            margin-top: 0;
            color: #2980b9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        table,
        th,
        td {
            border: 1px solid #ddd;
        }

        th {
            background-color: #f2f2f2;
            padding: 12px;
            text-align: left;
        }

        td {
            padding: 10px;
        }

        .best-practice {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }

        .bad-practice {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }

        .note {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
            padding: 10px;
        }

        footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }

        #sumario {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }

        #sumario ul {
            list-style-type: none;
            padding-left: 20px;
        }

        #sumario ul li {
            margin-bottom: 8px;
        }

        #sumario a {
            color: #2980b9;
            text-decoration: none;
        }

        #sumario a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <header>
        <h1 style="color: white; border-bottom: none;">Manual de Respostas Padronizadas</h1>
        <p>Guia para formatação padronizada de respostas da API</p>
    </header>

    <section id="sumario">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#estrutura">2. Estrutura de Respostas</a>
                <ul>
                    <li><a href="#estrutura-sucesso">2.1. Respostas de Sucesso</a></li>
                    <li><a href="#estrutura-erro">2.2. Respostas de Erro</a></li>
                    <li><a href="#estrutura-paginacao">2.3. Respostas com Paginação</a></li>
                </ul>
            </li>
            <li><a href="#codigos-http">3. Códigos HTTP</a></li>
            <li><a href="#implementacao">4. Implementação</a>
                <ul>
                    <li><a href="#resp-class">4.1. Classe ApiResponse</a></li>
                    <li><a href="#resp-controller">4.2. Uso em Controllers</a></li>
                    <li><a href="#resp-resources">4.3. Integração com API Resources</a></li>
                    <li><a href="#resp-exception">4.4. Integração com Exception Handler</a></li>
                </ul>
            </li>
            <li><a href="#casos-uso">5. Casos de Uso Comuns</a>
                <ul>
                    <li><a href="#caso-listagem">5.1. Listagem de Recursos</a></li>
                    <li><a href="#caso-detalhes">5.2. Detalhes de um Recurso</a></li>
                    <li><a href="#caso-criacao">5.3. Criação de Recurso</a></li>
                    <li><a href="#caso-atualizacao">5.4. Atualização de Recurso</a></li>
                    <li><a href="#caso-remocao">5.5. Remoção de Recurso</a></li>
                    <li><a href="#caso-validacao">5.6. Erros de Validação</a></li>
                    <li><a href="#caso-autenticacao">5.7. Erros de Autenticação e Autorização</a></li>
                </ul>
            </li>
            <li><a href="#boas-praticas">6. Boas Práticas</a></li>
            <li><a href="#internacionalizacao">7. Internacionalização</a></li>
            <li><a href="#testes">8. Teste de Respostas</a></li>
            <li><a href="#documentacao">9. Documentação da API</a></li>
        </ul>
    </section>

    <section id="introducao">
        <h2>1. Introdução</h2>
        <p>Este manual define o padrão de formatação para todas as respostas HTTP geradas pela API do sistema. A
            padronização de respostas é essencial para garantir uma experiência consistente para os consumidores da API,
            facilitando a integração e o tratamento de erros.</p>

        <p>Ao seguir estas diretrizes, garantimos que:</p>
        <ul>
            <li>Todas as respostas seguem uma estrutura previsível</li>
            <li>Os códigos HTTP são usados de forma consistente e correta</li>
            <li>As mensagens de erro são informativas e úteis</li>
            <li>O formato de dados é padronizado em toda a aplicação</li>
        </ul>

        <div class="note">
            <p><strong>Nota:</strong> Este documento é complementar ao Manual de Arquitetura e ao Manual de
                Implementação. As diretrizes aqui apresentadas devem ser seguidas por todos os endpoints da API.</p>
        </div>
    </section>

    <section id="estrutura">
        <h2>2. Estrutura de Respostas</h2>
        <p>Todas as respostas da API devem seguir uma estrutura comum que facilite o consumo e a interpretação pelos
            clientes.</p>

        <section id="estrutura-sucesso">
            <h3>2.1. Respostas de Sucesso</h3>
            <p>As respostas de sucesso devem seguir a estrutura abaixo:</p>

            <div class="code-block">
                {
                "status": "success",
                "message": "Mensagem descritiva do resultado da operação",
                "data": {
                // Dados retornados pela API
                }
                }
            </div>

            <div class="example">
                <h4>Exemplo de Resposta de Sucesso</h4>
                <div class="code-block">
                    {
                    "status": "success",
                    "message": "Produto recuperado com sucesso",
                    "data": {
                    "id": 1,
                    "name": "Smartphone XYZ",
                    "price": 1299.99,
                    "description": "Um smartphone incrível com recursos avançados",
                    "category": {
                    "id": 3,
                    "name": "Eletrônicos"
                    },
                    "created_at": "2023-06-15T14:30:45.000000Z",
                    "updated_at": "2023-06-15T14:30:45.000000Z"
                    }
                    }
                </div>
            </div>
        </section>

        <section id="estrutura-erro">
            <h3>2.2. Respostas de Erro</h3>
            <p>As respostas de erro devem seguir a estrutura abaixo:</p>

            <div class="code-block">
                {
                "status": "error",
                "message": "Mensagem descritiva do erro",
                "errors": {
                // Detalhes do erro (opcional)
                },
                "code": "ERROR_CODE" // Código de erro interno (opcional)
                }
            </div>

            <div class="example">
                <h4>Exemplo de Resposta de Erro de Validação</h4>
                <div class="code-block">
                    {
                    "status": "error",
                    "message": "Erros de validação foram encontrados",
                    "errors": {
                    "name": [
                    "O campo nome é obrigatório"
                    ],
                    "price": [
                    "O preço deve ser um valor numérico",
                    "O preço deve ser maior que zero"
                    ]
                    }
                    }
                </div>
            </div>

            <div class="example">
                <h4>Exemplo de Resposta de Erro de Recurso Não Encontrado</h4>
                <div class="code-block">
                    {
                    "status": "error",
                    "message": "Produto não encontrado",
                    "code": "RESOURCE_NOT_FOUND"
                    }
                </div>
            </div>
        </section>

        <section id="estrutura-paginacao">
            <h3>2.3. Respostas com Paginação</h3>
            <p>Quando retornar coleções paginadas, a estrutura deve incluir metadados de paginação:</p>

            <div class="code-block">
                {
                "status": "success",
                "message": "Recursos recuperados com sucesso",
                "data": {
                "data": [
                // Array de itens da página atual
                ],
                "pagination": {
                "total": 100,
                "count": 15,
                "per_page": 15,
                "current_page": 1,
                "total_pages": 7,
                "links": {
                "next": "https://api.exemplo.com/recursos?page=2",
                "previous": null
                }
                }
                }
                }
            </div>

            <div class="note">
                <p>Ao usar a paginação nativa do Laravel, o campo <code>data.pagination</code> deve ser construído a
                    partir das informações da instância <code>LengthAwarePaginator</code>.</p>
            </div>
        </section>
    </section>

    <section id="codigos-http">
        <h2>3. Códigos HTTP</h2>
        <p>Os códigos HTTP devem ser usados de forma consistente para indicar o resultado da operação:</p>

        <table>
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Descrição</th>
                    <th>Uso</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>200 OK</td>
                    <td>Sucesso</td>
                    <td>Para requisições GET, PUT ou PATCH bem-sucedidas</td>
                </tr>
                <tr>
                    <td>201 Created</td>
                    <td>Criado</td>
                    <td>Quando um recurso é criado com sucesso (POST)</td>
                </tr>
                <tr>
                    <td>204 No Content</td>
                    <td>Sem conteúdo</td>
                    <td>Para requisições bem-sucedidas que não retornam dados (DELETE)</td>
                </tr>
                <tr>
                    <td>400 Bad Request</td>
                    <td>Requisição inválida</td>
                    <td>Quando a requisição contém parâmetros inválidos ou incompreensíveis</td>
                </tr>
                <tr>
                    <td>401 Unauthorized</td>
                    <td>Não autorizado</td>
                    <td>Quando autenticação é requerida mas não foi fornecida ou é inválida</td>
                </tr>
                <tr>
                    <td>403 Forbidden</td>
                    <td>Proibido</td>
                    <td>Quando o usuário está autenticado mas não tem permissão para a operação</td>
                </tr>
                <tr>
                    <td>404 Not Found</td>
                    <td>Não encontrado</td>
                    <td>Quando o recurso solicitado não existe</td>
                </tr>
                <tr>
                    <td>422 Unprocessable Entity</td>
                    <td>Entidade não processável</td>
                    <td>Quando a validação falha (erros nos dados enviados)</td>
                </tr>
                <tr>
                    <td>429 Too Many Requests</td>
                    <td>Muitas requisições</td>
                    <td>Quando o limite de rate limiting é excedido</td>
                </tr>
                <tr>
                    <td>500 Internal Server Error</td>
                    <td>Erro interno do servidor</td>
                    <td>Para erros inesperados no servidor</td>
                </tr>
                <tr>
                    <td>503 Service Unavailable</td>
                    <td>Serviço indisponível</td>
                    <td>Quando o sistema está temporariamente indisponível (manutenção)</td>
                </tr>
            </tbody>
        </table>

        <div class="best-practice">
            <h4>Boa Prática:</h4>
            <p>Sempre use o código HTTP mais apropriado para cada situação. O código HTTP deve refletir corretamente o
                resultado da operação, independentemente da mensagem no corpo da resposta.</p>
        </div>
    </section>

    <section id="implementacao">
        <h2>4. Implementação</h2>
        <p>Para garantir a consistência nas respostas da API, implementamos uma camada de abstração que padroniza a
            formatação.</p>

        <section id="resp-class">
            <h3>4.1. Classe ApiResponse</h3>
            <p>A classe <code>ApiResponse</code> encapsula a lógica para formatar respostas consistentes:</p>

            <div class="code-block">
                namespace App\Http\Responses;

                use Illuminate\Http\JsonResponse;
                use Illuminate\Pagination\LengthAwarePaginator;
                use Illuminate\Support\Collection;
                use Illuminate\Http\Resources\Json\JsonResource;
                use Illuminate\Http\Resources\Json\ResourceCollection;

                class ApiResponse
                {
                /**
                * Resposta de sucesso genérica
                */
                public function success($data = null, string $message = 'Operação realizada com sucesso', int
                $statusCode = 200): JsonResponse
                {
                $response = [
                'status' => 'success',
                'message' => $message,
                ];

                if ($data !== null) {
                $response['data'] = $this->formatData($data);
                }

                return response()->json($response, $statusCode);
                }

                /**
                * Resposta para criação de recurso
                */
                public function created($data = null, string $message = 'Recurso criado com sucesso'): JsonResponse
                {
                return $this->success($data, $message, 201);
                }

                /**
                * Resposta para atualizações bem-sucedidas
                */
                public function updated($data = null, string $message = 'Recurso atualizado com sucesso'): JsonResponse
                {
                return $this->success($data, $message);
                }

                /**
                * Resposta para exclusões bem-sucedidas
                */
                public function deleted(string $message = 'Recurso excluído com sucesso'): JsonResponse
                {
                return response()->json([
                'status' => 'success',
                'message' => $message,
                ], 200);
                }

                /**
                * Resposta para erros
                */
                public function error(string $message = 'Ocorreu um erro', $errors = null, int $statusCode = 400, string
                $errorCode = null): JsonResponse
                {
                $response = [
                'status' => 'error',
                'message' => $message,
                ];

                if ($errors !== null) {
                $response['errors'] = $errors;
                }

                if ($errorCode !== null) {
                $response['code'] = $errorCode;
                }

                return response()->json($response, $statusCode);
                }

                /**
                * Resposta para recurso não encontrado
                */
                public function notFound(string $message = 'Recurso não encontrado'): JsonResponse
                {
                return $this->error($message, null, 404, 'RESOURCE_NOT_FOUND');
                }

                /**
                * Resposta para erros de autorização
                */
                public function forbidden(string $message = 'Ação não autorizada'): JsonResponse
                {
                return $this->error($message, null, 403, 'FORBIDDEN');
                }

                /**
                * Resposta para erros de autenticação
                */
                public function unauthorized(string $message = 'Não autenticado'): JsonResponse
                {
                return $this->error($message, null, 401, 'UNAUTHORIZED');
                }

                /**
                * Formata dados para incluir na resposta
                */
                private function formatData($data)
                {
                // Se for um JsonResource ou ResourceCollection, obtém o array
                if ($data instanceof JsonResource || $data instanceof ResourceCollection) {
                return $data->response()->getData(true);
                }

                // Tratamento especial para dados paginados
                if ($data instanceof LengthAwarePaginator) {
                return [
                'data' => $data->items(),
                'pagination' => [
                'total' => $data->total(),
                'count' => $data->count(),
                'per_page' => $data->perPage(),
                'current_page' => $data->currentPage(),
                'total_pages' => $data->lastPage(),
                'links' => [
                'next' => $data->nextPageUrl(),
                'previous' => $data->previousPageUrl(),
                ],
                ],
                ];
                }

                // Para collections, retorna o array
                if ($data instanceof Collection) {
                return $data->all();
                }

                return $data;
                }
                }
            </div>
        </section>

        <section id="resp-controller">
            <h3>4.2. Uso em Controllers</h3>
            <p>Os controllers devem injetar e usar a classe <code>ApiResponse</code> para formatar as respostas:</p>

            <div class="code-block">
                namespace App\Http\Controllers;

                use App\Http\Responses\ApiResponse;
                use App\Services\ProductService;
                use App\Http\Requests\ProductRequest;
                use App\Http\Resources\ProductResource;

                class ProductController extends Controller
                {
                protected $productService;
                protected $response;

                public function __construct(ProductService $productService, ApiResponse $response)
                {
                $this->productService = $productService;
                $this->response = $response;
                }

                public function index()
                {
                $products = $this->productService->getAllPaginated();
                return $this->response->success($products, 'Produtos recuperados com sucesso');
                }

                public function store(ProductRequest $request)
                {
                $product = $this->productService->create($request->validated());
                return $this->response->created(
                new ProductResource($product),
                'Produto criado com sucesso'
                );
                }

                public function show($id)
                {
                $product = $this->productService->findById($id);

                if (!$product) {
                return $this->response->notFound('Produto não encontrado');
                }

                return $this->response->success(
                new ProductResource($product),
                'Produto recuperado com sucesso'
                );
                }

                public function update(ProductRequest $request, $id)
                {
                try {
                $product = $this->productService->update($id, $request->validated());
                return $this->response->updated(
                new ProductResource($product),
                'Produto atualizado com sucesso'
                );
                } catch (\App\Exceptions\NotFoundException $e) {
                return $this->response->notFound($e->getMessage());
                }
                }

                public function destroy($id)
                {
                try {
                $this->productService->delete($id);
                return $this->response->deleted('Produto excluído com sucesso');
                } catch (\App\Exceptions\NotFoundException $e) {
                return $this->response->notFound($e->getMessage());
                }
                }
                }
            </div>
        </section>

        <section id="resp-resources">
            <h3>4.3. Integração com API Resources</h3>
            <p>As API Resources podem ser usadas para transformar os modelos antes de incluí-los na resposta:</p>

            <div class="code-block">
                namespace App\Http\Resources;

                use Illuminate\Http\Resources\Json\JsonResource;

                class ProductResource extends JsonResource
                {
                public function toArray($request)
                {
                return [
                'id' => $this->id,
                'name' => $this->name,
                'slug' => $this->slug,
                'description' => $this->description,
                'price' => (float) $this->price,
                'formatted_price' => 'R$ ' . number_format($this->price, 2, ',', '.'),
                'stock' => $this->stock,
                'category' => new CategoryResource($this->whenLoaded('category')),
                'tags' => TagResource::collection($this->whenLoaded('tags')),
                'image_url' => $this->getImageUrl(),
                'created_at' => $this->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
                ];
                }
                }
            </div>

            <div class="best-practice">
                <h4>Boa Prática:</h4>
                <p>Use API Resources para transformar modelos em representações JSON adequadas para a API. Isso permite:
                </p>
                <ul>
                    <li>Controlar exatamente quais campos são expostos</li>
                    <li>Formatar valores (como datas e números)</li>
                    <li>Incluir dados calculados ou virtuais</li>
                    <li>Controlar a inclusão de relacionamentos</li>
                </ul>
            </div>
        </section>

        <section id="resp-exception">
            <h3>4.4. Integração com Exception Handler</h3>
            <p>O <code>ExceptionHandler</code> global deve formatar as respostas de erro usando o mesmo padrão:</p>

            <div class="code-block">
                namespace App\Exceptions;

                use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
                use Throwable;
                use App\Http\Responses\ApiResponse;

                class Handler extends ExceptionHandler
                {
                // ... outras propriedades e métodos

                public function register()
                {
                $this->renderable(function (Throwable $e, $request) {
                if ($request->expectsJson()) {
                return $this->handleApiException($e, $request);
                }
                });
                }

                private function handleApiException(Throwable $e, $request)
                {
                $response = app(ApiResponse::class);

                // Tratamento para diferentes tipos de exceções
                if ($e instanceof \Illuminate\Validation\ValidationException) {
                return $response->error(
                'Erros de validação foram encontrados',
                $e->validator->errors(),
                422
                );
                }

                if ($e instanceof \Illuminate\Auth\AuthenticationException) {
                return $response->unauthorized('Autenticação necessária para acessar este recurso');
                }

                if ($e instanceof \Illuminate\Auth\Access\AuthorizationException) {
                return $response->forbidden('Você não tem permissão para realizar esta ação');
                }

                if ($e instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException) {
                return $response->notFound('Recurso não encontrado');
                }

                if ($e instanceof \App\Exceptions\NotFoundException) {
                return $response->notFound($e->getMessage());
                }

                if ($e instanceof \App\Exceptions\BusinessException) {
                return $response->error($e->getMessage(), null, 400);
                }

                // Em produção, não expor detalhes de outros tipos de erro
                if (app()->environment('production')) {
                return $response->error('Ocorreu um erro no servidor', null, 500);
                }

                // Em ambiente de desenvolvimento, retornar mais detalhes
                return $response->error(
                $e->getMessage(),
                [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => collect($e->getTrace())->map(function ($trace) {
                return \Arr::except($trace, ['args']);
                })->all(),
                ],
                500
                );
                }
                }
            </div>
        </section>
    </section>

    <section id="casos-uso">
        <h2>5. Casos de Uso Comuns</h2>
        <p>A seguir, exemplos de respostas para operações comuns na API:</p>

        <section id="caso-listagem">
            <h3>5.1. Listagem de Recursos</h3>
            <div class="code-block">
                // Código no Controller
                public function index()
                {
                $products = $this->productService->getAllPaginated(
                $request->get('page', 1),
                $request->get('per_page', 15)
                );
                return $this->response->success($products, 'Produtos recuperados com sucesso');
                }

                // Exemplo de resposta
                {
                "status": "success",
                "message": "Produtos recuperados com sucesso",
                "data": {
                "data": [
                {
                "id": 1,
                "name": "Produto A",
                "price": 99.99,
                "description": "Descrição do produto A"
                },
                {
                "id": 2,
                "name": "Produto B",
                "price": 149.99,
                "description": "Descrição do produto B"
                }
                ],
                "pagination": {
                "total": 50,
                "count": 15,
                "per_page": 15,
                "current_page": 1,
                "total_pages": 4,
                "links": {
                "next": "http://api.exemplo.com/produtos?page=2",
                "previous": null
                }
                }
                }
                }
            </div>
        </section>

        <section id="caso-detalhes">
            <h3>5.2. Detalhes de um Recurso</h3>
            <div class="code-block">
                // Código no Controller
                public function show($id)
                {
                $product = $this->productService->findById($id);

                if (!$product) {
                return $this->response->notFound('Produto não encontrado');
                }

                return $this->response->success(
                new ProductResource($product),
                'Produto recuperado com sucesso'
                );
                }

                // Exemplo de resposta bem-sucedida
                {
                "status": "success",
                "message": "Produto recuperado com sucesso",
                "data": {
                "id": 1,
                "name": "Smartphone XYZ",
                "slug": "smartphone-xyz",
                "description": "Um smartphone incrível com recursos avançados",
                "price": 1299.99,
                "formatted_price": "R$ 1.299,99",
                "stock": 45,
                "category": {
                "id": 3,
                "name": "Eletrônicos"
                },
                "tags": [
                {"id": 1, "name": "Lançamento"},
                {"id": 5, "name": "Premium"}
                ],
                "image_url": "https://example.com/storage/products/smartphone-xyz.jpg",
                "created_at": "2023-06-15T14:30:45.000000Z",
                "updated_at": "2023-06-15T14:30:45.000000Z"
                }
                }

                // Exemplo de resposta quando não encontrado
                {
                "status": "error",
                "message": "Produto não encontrado",
                "code": "RESOURCE_NOT_FOUND"
                }
            </div>
        </section>

        <section id="caso-criacao">
            <h3>5.3. Criação de Recurso</h3>
            <div class="code-block">
                // Código no Controller
                public function store(ProductRequest $request)
                {
                $product = $this->productService->create($request->validated());
                return $this->response->created(
                new ProductResource($product),
                'Produto criado com sucesso'
                );
                }

                // Exemplo de resposta
                {
                "status": "success",
                "message": "Produto criado com sucesso",
                "data": {
                "id": 101,
                "name": "Novo Produto",
                "slug": "novo-produto",
                "description": "Descrição do novo produto",
                "price": 79.90,
                "formatted_price": "R$ 79,90",
                "stock": 100,
                "category": {
                "id": 2,
                "name": "Acessórios"
                },
                "created_at": "2023-09-01T10:15:30.000000Z",
                "updated_at": "2023-09-01T10:15:30.000000Z"
                }
                }
            </div>
        </section>

        <section id="caso-atualizacao">
            <h3>5.4. Atualização de Recurso</h3>
            <div class="code-block">
                // Código no Controller
                public function update(ProductRequest $request, $id)
                {
                try {
                $product = $this->productService->update($id, $request->validated());
                return $this->response->updated(
                new ProductResource($product),
                'Produto atualizado com sucesso'
                );
                } catch (\App\Exceptions\NotFoundException $e) {
                return $this->response->notFound($e->getMessage());
                }
                }

                // Exemplo de resposta bem-sucedida
                {
                "status": "success",
                "message": "Produto atualizado com sucesso",
                "data": {
                "id": 1,
                "name": "Smartphone XYZ - Edição Especial",
                "slug": "smartphone-xyz-edicao-especial",
                "description": "Um smartphone incrível com recursos avançados - Versão atualizada",
                "price": 1399.99,
                "formatted_price": "R$ 1.399,99",
                "stock": 40,
                "category": {
                "id": 3,
                "name": "Eletrônicos"
                },
                "created_at": "2023-06-15T14:30:45.000000Z",
                "updated_at": "2023-09-01T11:45:12.000000Z"
                }
                }
            </div>
        </section>

        <section id="caso-remocao">
            <h3>5.5. Remoção de Recurso</h3>
            <div class="code-block">
                // Código no Controller
                public function destroy($id)
                {
                try {
                $this->productService->delete($id);
                return $this->response->deleted('Produto excluído com sucesso');
                } catch (\App\Exceptions\NotFoundException $e) {
                return $this->response->notFound($e->getMessage());
                }
                }

                // Exemplo de resposta
                {
                "status": "success",
                "message": "Produto excluído com sucesso"
                }
            </div>
        </section>

        <section id="caso-validacao">
            <h3>5.6. Erros de Validação</h3>
            <div class="code-block">
                // O Laravel automaticamente lança ValidationException ao validar com Form Requests
                // A exceção é capturada no Handler global e formatada conforme o padrão

                // Exemplo de resposta para erros de validação
                {
                "status": "error",
                "message": "Erros de validação foram encontrados",
                "errors": {
                "name": [
                "O campo nome é obrigatório"
                ],
                "price": [
                "O preço deve ser um valor numérico",
                "O preço deve ser maior que zero"
                ],
                "category_id": [
                "A categoria selecionada não existe"
                ]
                }
                }
            </div>
        </section>

        <section id="caso-autenticacao">
            <h3>5.7. Erros de Autenticação e Autorização</h3>
            <div class="code-block">
                // Exemplo de resposta para usuário não autenticado (401 Unauthorized)
                {
                "status": "error",
                "message": "Autenticação necessária para acessar este recurso",
                "code": "UNAUTHORIZED"
                }

                // Exemplo de resposta para usuário sem permissão (403 Forbidden)
                {
                "status": "error",
                "message": "Você não tem permissão para realizar esta ação",
                "code": "FORBIDDEN"
                }
            </div>
        </section>
    </section>

    <section id="boas-praticas">
        <h2>6. Boas Práticas</h2>
        <ul>
            <li><strong>Consistência:</strong> Mantenha a estrutura da resposta consistente em toda a API</li>
            <li><strong>Mensagens descritivas:</strong> Use mensagens claras e informativas, tanto para sucesso quanto
                para erro</li>
            <li><strong>Códigos HTTP apropriados:</strong> Use o código HTTP correto para cada tipo de resposta</li>
            <li><strong>Dados de erro úteis:</strong> Forneça detalhes suficientes para ajudar na resolução de problemas
            </li>
            <li><strong>Sem vazamento de informações sensíveis:</strong> Nunca inclua detalhes de implementação interna
                ou dados sensíveis nas mensagens de erro</li>
            <li><strong>Normalização de dados:</strong> Use resources para formatar dados de forma consistente</li>
            <li><strong>Versionamento:</strong> Considere incluir a versão da API nas respostas para facilitar o suporte
            </li>
        </ul>

        <div class="bad-practice">
            <h4>Práticas a Evitar</h4>
            <ul>
                <li>Estruturas de resposta inconsistentes entre endpoints</li>
                <li>Mensagens de erro genéricas ou pouco informativas</li>
                <li>Usar códigos HTTP incorretos (como 200 OK para erros)</li>
                <li>Retornar stacktraces ou detalhes técnicos em produção</li>
                <li>Expor informações sensíveis nas respostas</li>
                <li>Formatos diferentes para dados do mesmo tipo em diferentes endpoints</li>
            </ul>
        </div>
    </section>

    <section id="internacionalizacao">
        <h2>7. Internacionalização</h2>
        <p>Para suportar diferentes idiomas nas mensagens de resposta da API, utilize o sistema de internacionalização
            do Laravel:</p>

        <div class="code-block">
            // Armazene as traduções em recursos de idioma
            // resources/lang/pt/api.php
            return [
            'product' => [
            'created' => 'Produto criado com sucesso',
            'updated' => 'Produto atualizado com sucesso',
            'deleted' => 'Produto excluído com sucesso',
            'retrieved' => 'Produto recuperado com sucesso',
            'not_found' => 'Produto não encontrado'
            ],
            'validation' => [
            'errors' => 'Erros de validação foram encontrados'
            ],
            // ...
            ];

            // resources/lang/en/api.php
            return [
            'product' => [
            'created' => 'Product created successfully',
            'updated' => 'Product updated successfully',
            'deleted' => 'Product deleted successfully',
            'retrieved' => 'Product retrieved successfully',
            'not_found' => 'Product not found'
            ],
            'validation' => [
            'errors' => 'Validation errors were found'
            ],
            // ...
            ];

            // Use nas respostas
            return $this->response->created(
            new ProductResource($product),
            __('api.product.created')
            );
        </div>

        <div class="note">
            <p>O idioma da resposta pode ser determinado com base no cabeçalho <code>Accept-Language</code> da
                requisição ou em uma preferência do usuário armazenada no sistema.</p>
        </div>

        <h3>7.1. Middleware de Localização</h3>
        <p>Configure um middleware para definir o idioma com base no cabeçalho da requisição:</p>

        <div class="code-block">
            namespace App\Http\Middleware;

            use Closure;
            use Illuminate\Http\Request;
            use Illuminate\Support\Facades\App;

            class SetApiLocale
            {
            public function handle(Request $request, Closure $next)
            {
            $locale = $request->header('Accept-Language');

            // Validar se o idioma é suportado
            if ($locale && in_array($locale, config('app.supported_locales', ['en']))) {
            App::setLocale($locale);
            }

            return $next($request);
            }
            }
        </div>
    </section>

    <section id="testes">
        <h2>8. Teste de Respostas</h2>
        <p>Implemente testes específicos para verificar se as respostas seguem o formato padronizado:</p>

        <div class="code-block">
            namespace Tests\Feature\Api;

            use Tests\TestCase;
            use App\Models\Product;
            use App\Models\User;
            use Laravel\Sanctum\Sanctum;

            class ProductApiTest extends TestCase
            {
            /** @test */
            public function it_returns_formatted_response_when_listing_products()
            {
            // Arrange
            Product::factory()->count(3)->create();

            // Act
            $response = $this->getJson('/api/products');

            // Assert
            $response->assertStatus(200);
            $response->assertJsonStructure([
            'status',
            'message',
            'data' => [
            'data' => [
            '*' => ['id', 'name', 'price']
            ],
            'pagination' => [
            'total', 'count', 'per_page', 'current_page', 'total_pages', 'links'
            ]
            ]
            ]);
            $response->assertJson([
            'status' => 'success',
            ]);
            }

            /** @test */
            public function it_returns_formatted_error_response_for_validation_failure()
            {
            // Arrange
            Sanctum::actingAs(
            User::factory()->create(['role' => 'admin'])
            );

            // Act
            $response = $this->postJson('/api/products', [
            // Dados inválidos (faltando campos obrigatórios)
            ]);

            // Assert
            $response->assertStatus(422);
            $response->assertJsonStructure([
            'status',
            'message',
            'errors' => [
            'name',
            'price'
            ]
            ]);
            $response->assertJson([
            'status' => 'error',
            ]);
            }

            /** @test */
            public function it_returns_formatted_error_response_for_not_found()
            {
            // Act
            $response = $this->getJson('/api/products/999');

            // Assert
            $response->assertStatus(404);
            $response->assertJsonStructure([
            'status',
            'message',
            'code'
            ]);
            $response->assertJson([
            'status' => 'error',
            'code' => 'RESOURCE_NOT_FOUND'
            ]);
            }
            }
        </div>
    </section>

    <section id="documentacao">
        <h2>9. Documentação da API</h2>
        <p>Documente claramente o formato de resposta padronizado para desenvolvedores que utilizarão a API:</p>

        <h3>9.1. Ferramentas de Documentação</h3>
        <ul>
            <li><strong>Swagger/OpenAPI:</strong> Inclua exemplos de resposta para cada endpoint</li>
            <li><strong>Postman Collections:</strong> Crie e compartilhe coleções com exemplos de requisição e resposta
            </li>
            <li><strong>Documentação Manual:</strong> Explique o padrão de resposta em uma seção dedicada</li>
        </ul>

        <h3>9.2. Exemplo de Documentação Swagger</h3>
        <div class="code-block">
            /**
            * @OA\Get(
            * path="/api/products/{id}",
            * summary="Obter detalhes de um produto",
            * tags={"Products"},
            * @OA\Parameter(
            * name="id",
            * in="path",
            * required=true,
            * description="ID do produto",
            * @OA\Schema(type="integer")
            * ),
            * @OA\Response(
            * response=200,
            * description="Produto encontrado",
            * @OA\JsonContent(
            * @OA\Property(property="status", type="string", example="success"),
            * @OA\Property(property="message", type="string", example="Produto recuperado com sucesso"),
            * @OA\Property(
            * property="data",
            * type="object",
            * ref="#/components/schemas/Product"
            * )
            * )
            * ),
            * @OA\Response(
            * response=404,
            * description="Produto não encontrado",
            * @OA\JsonContent(
            * @OA\Property(property="status", type="string", example="error"),
            * @OA\Property(property="message", type="string", example="Produto não encontrado"),
            * @OA\Property(property="code", type="string", example="RESOURCE_NOT_FOUND")
            * )
            * )
            * )
            */
        </div>

        <h3>9.3. Diretrizes de Documentação</h3>
        <ul>
            <li>Descreva claramente a estrutura padrão para respostas de sucesso e erro</li>
            <li>Explique o significado dos diferentes campos da resposta</li>
            <li>Liste os possíveis códigos HTTP e quando eles são usados</li>
            <li>Forneça exemplos para todos os principais casos de uso</li>
            <li>Documente os códigos de erro internos e seu significado</li>
            <li>Explique o formato de paginação para listas de recursos</li>
        </ul>

        <div class="best-practice">
            <h4>Lembre-se:</h4>
            <p>Uma API bem documentada é mais fácil de usar, resultando em menos perguntas de suporte, integração mais
                rápida pelos clientes e maior satisfação dos desenvolvedores. Inclua sempre exemplos reais de respostas
                para facilitar a compreensão.</p>
        </div>
    </section>

    <footer>
        <p>Manual de Respostas Padronizadas - Versão 1.0</p>
        <p>Última atualização: <span id="current-date"></span></p>
        <script>
            document.getElementById('current-date').textContent = new Date().toLocaleDateString();
        </script>
    </footer>
</body>

</html>