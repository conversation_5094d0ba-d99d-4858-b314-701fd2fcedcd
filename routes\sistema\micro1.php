<?php

use App\Http\Sistema\Micro1\Controllers\ExampleController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Micro1 API Routes
|--------------------------------------------------------------------------
|
| Rotas específicas para o microserviço Micro1 dentro do Sistema
|
*/

// Rotas públicas do Micro1 (sem autenticação)
Route::group([
    'middleware' => ['api'],
], function () {
    Route::get('/health', function () {
        return response()->json([
            'status' => 'healthy',
            'service' => 'micro1',
            'timestamp' => now()->toIso8601String(),
        ]);
    });
    Route::get('/users', [ExampleController::class, 'index']);

    // Outras rotas públicas
});


// Rotas privadas do Micro1 (com autenticação)
Route::group([
    'middleware' => ['api', 'jwt.verify'],
], function () {
    // Rotas do ExampleController
    Route::prefix('exemplo')->group(function () {
        Route::get('/', [ExampleController::class, 'index'])->name('micro1.exemplo.index');
        Route::get('/{id}', [ExampleController::class, 'show'])->name('micro1.exemplo.show');
        Route::post('/', [ExampleController::class, 'store'])->name('micro1.exemplo.store');
        Route::put('/{id}', [ExampleController::class, 'update'])->name('micro1.exemplo.update');
        Route::delete('/{id}', [ExampleController::class, 'destroy'])->name('micro1.exemplo.destroy');

        // Rota para getAllUsers (se necessário)
        Route::get('/users/all', [ExampleController::class, 'getAllUsers'])->name('micro1.exemplo.getAllUsers');
    });

    // Adicione outros controladores do Micro1 aqui
});
