/**
 * prism.css - Tema personalizado para realce de sintaxe
 */

code[class*="language-"],
pre[class*="language-"] {
    color: #383e42;
    background: none;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    text-align: left;
    white-space: pre;
    word-spacing: normal;
    word-break: normal;
    word-wrap: normal;
    line-height: 1.5;
    tab-size: 4;
    hyphens: none;
}

pre[class*="language-"] {
    padding: 1em;
    margin: 0.5em 0;
    overflow: auto;
    border-radius: 0.3em;
    background-color: #f8f9fa;
}

:not(pre)>code[class*="language-"],
pre[class*="language-"] {
    background: #f8f9fa;
}

:not(pre)>code[class*="language-"] {
    padding: 0.2em 0.4em;
    border-radius: 0.3em;
    white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: #6a737d;
}

.token.punctuation {
    color: #5c6370;
}

.namespace {
    opacity: 0.7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant {
    color: #e83e8c;
}

.token.symbol,
.token.deleted {
    color: #e83e8c;
}

.token.selector,
.token.attr-name,
.token.char,
.token.builtin,
.token.inserted {
    color: #50a14f;
}

.token.string {
    color: #0a8;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
    color: #a67f59;
}

.token.atrule,
.token.attr-value,
.token.keyword {
    color: #0086b3;
}

.token.function,
.token.class-name {
    color: #6f42c1;
}

.token.regex,
.token.important,
.token.variable {
    color: #e90;
}

.token.important,
.token.bold {
    font-weight: bold;
}

.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}

/* Linha destacada */
.line-highlight {
    background: rgba(193, 222, 241, 0.2);
    background: -webkit-linear-gradient(left, rgba(193, 222, 241, 0.2) 70%, rgba(221, 222, 241, 0));
    background: linear-gradient(to right, rgba(193, 222, 241, 0.2) 70%, rgba(221, 222, 241, 0));
}

/* Títulos das seções de código (opcional) */
.code-block {
    position: relative;
    margin: 1.5em 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    overflow: hidden;
}

.code-block pre[class*="language-"] {
    margin: 0;
    border-radius: 0;
    padding-top: 1.5em;
}

.code-block::before {
    content: "";
    height: 10px;
    width: 10px;
    position: absolute;
    top: 10px;
    left: 10px;
    border-radius: 50%;
    background: #ff5f56;
    box-shadow: 0 0 0 1px #e0443e,
        18px 0 0 0 #ffbd2e,
        18px 0 0 1px #dea123,
        36px 0 0 0 #27c93f,
        36px 0 0 1px #1aab29;
    z-index: 1;
}

.code-block pre {
    padding-top: 30px;
}

@media print {

    code[class*="language-"],
    pre[class*="language-"] {
        text-shadow: none;
    }
}