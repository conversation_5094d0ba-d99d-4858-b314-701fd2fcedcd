<?php

namespace Tests\Unit\Responses;

use App\Responses\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ResponseAbstractTest extends TestCase
{
    protected $response;
    protected string $fixedDate = '2023-01-01T12:00:00+00:00';

    protected function setUp(): void
    {
        parent::setUp();

        $this->response = new ApiResponse();

        Carbon::setTestNow(Carbon::parse($this->fixedDate));
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow();
        Mockery::close();
        parent::tearDown();
    }

    #[Test]
    public function it_returns_ok_response()
    {
        $data = ['name' => 'Test Product'];
        $message = 'Operation successful';

        $response = $this->response->ok($data, $message);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('success', $content['status']);
        $this->assertEquals($message, $content['message']);
        $this->assertEquals(200, $content['code']);
        $this->assertEquals($this->fixedDate, $content['timestamp']);
        $this->assertEquals('1.0', $content['api_version']);
        $this->assertEquals($data, $content['data']);
    }

    #[Test]
    public function it_returns_created_response()
    {
        $data = ['id' => 1, 'name' => 'New Product'];
        $message = 'Resource created';
        $headers = ['Location' => 'api/products/1'];

        $response = $this->response->created($data, $message, $headers);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(201, $response->getStatusCode());
        $this->assertEquals('api/products/1', $response->headers->get('Location'));

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('success', $content['status']);
        $this->assertEquals($message, $content['message']);
        $this->assertEquals($data, $content['data']);
    }


    #[Test]
    public function it_returns_no_content_response()
    {
        $message = 'No content';

        $response = $this->response->noContent($message);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(204, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('success', $content['status']);
        $this->assertEquals($message, $content['message']);
        $this->assertArrayNotHasKey('data', $content);
    }

    #[Test]
    public function it_returns_not_modified_response()
    {
        $message = 'Not modified';

        $response = $this->response->notModified($message);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(304, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('success', $content['status']);
        $this->assertEquals($message, $content['message']);
        $this->assertArrayNotHasKey('data', $content);
    }

    #[Test]
    public function it_returns_bad_request_response()
    {
        $message = 'Bad request';
        $errors = ['field' => 'Invalid'];

        $response = $this->response->badRequest($message, $errors);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
        $this->assertEquals($errors, $content['data']['errors']);
    }

    #[Test]
    public function it_returns_unauthorized_response()
    {
        $message = 'Unauthorized';

        $response = $this->response->unauthorized($message);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
    }

    #[Test]
    public function it_returns_forbidden_response()
    {
        $message = 'Forbidden';

        $response = $this->response->forbidden($message);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(403, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
    }

    #[Test]
    public function it_returns_not_found_response()
    {
        $message = 'Not found';

        $response = $this->response->notFound($message);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
    }

    #[Test]
    public function it_returns_method_not_allowed_response()
    {
        $message = 'Method not allowed';

        $response = $this->response->methodNotAllowed($message);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(405, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
    }

    #[Test]
    public function it_returns_conflict_response()
    {
        $message = 'Conflict';
        $errors = ['reason' => 'Already exists'];

        $response = $this->response->conflict($message, $errors);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(409, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
        $this->assertEquals($errors, $content['data']['errors']);
    }

    #[Test]
    public function it_returns_unprocessable_entity_response()
    {
        $message = 'Validation error';
        $errors = ['name' => ['Required']];

        $response = $this->response->unprocessableEntity($message, $errors);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
        $this->assertEquals($errors, $content['data']['errors']);
    }


    #[Test]
    public function it_returns_too_many_requests_response()
    {
        $message = 'Too many requests';

        $response = $this->response->tooManyRequests($message);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(429, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
    }

    #[Test]
    public function it_returns_server_error_response()
    {
        $message = 'Server error';
        $errors = ['exception' => 'Error details'];

        $response = $this->response->serverError($message, $errors);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
        $this->assertEquals($errors, $content['data']['errors']);
    }

    #[Test]
    public function it_returns_service_unavailable_response()
    {
        $message = 'Service unavailable';

        $response = $this->response->serviceUnavailable($message);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(503, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals($message, $content['message']);
    }
}
