<!DOCTYPE html>
<html lang="pt-br">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Manual de Arquitetura - Laravel 12</title>
        <link rel="stylesheet" href="css/manual.css" />
        <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    </head>

    <body>
        <div class="hero-section">
            <h1>Manual de Arquitetura</h1>
            <p>
                Princípios e padrões para construção de uma arquitetura robusta
                e escalável para APIs Laravel 12
            </p>
        </div>

        <div class="container">
            <div class="manual-content">
                <div class="manual-header">
                    <div class="manual-metadata">
                        <strong>Versão:</strong> 1.0.0 |
                        <strong>Última atualização:</strong> Junho 2023 |
                        <strong>Autor:</strong> Equipe de Arquitetura
                    </div>
                    <div class="tags">
                        <span class="tag">Arquitetura</span>
                        <span class="tag">SOLID</span>
                        <span class="tag">Design Patterns</span>
                        <span class="tag">Laravel 12</span>
                        <span class="tag">PHP 8.2</span>
                    </div>
                </div>

                <div class="manual-toc">
                    <h3>Índice</h3>
                    <ul>
                        <li><a href="#introducao">1. Introdução</a></li>
                        <li>
                            <a href="#visao-geral">2. Visão Geral da Arquitetura</a>
                        </li>
                        <li>
                            <a href="#camadas">3. Camadas da Arquitetura</a>
                            <ul>
                                <li>
                                    <a href="#camada-apresentacao"
                                        >3.1. Camada de Apresentação</a>
                                </li>
                                <li>
                                    <a href="#camada-aplicacao"
                                        >3.2. Camada de Aplicação</a>
                                </li>
                                <li>
                                    <a href="#camada-dominio"
                                        >3.3. Camada de Domínio</a
                                    >
                                </li>
                                <li>
                                    <a href="#camada-infraestrutura"
                                        >3.4. Camada de Infraestrutura</a
                                    >
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="#componentes-base">4. Componentes Base</a>
                            <ul>
                                <li>
                                    <a href="#controller-abstract"
                                        >4.1. ControllerAbstract</a
                                    >
                                </li>
                                <li>
                                    <a href="#service-abstract"
                                        >4.2. ServiceAbstract</a
                                    >
                                </li>
                                <li>
                                    <a href="#repository-abstract"
                                        >4.3. RepositoryAbstract</a
                                    >
                                </li>
                                <li>
                                    <a href="#response-abstract"
                                        >4.4. ResponseAbstract</a
                                    >
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="#fluxo-requisicao"
                                >5. Fluxo de Requisição</a
                            >
                            <ul>
                                <li>
                                    <a href="#fluxo-crud"
                                        >5.1. Fluxo CRUD Padrão</a
                                    >
                                </li>
                                <li>
                                    <a href="#fluxo-autenticacao"
                                        >5.2. Fluxo de Autenticação</a
                                    >
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="#autenticacao"
                                >6. Autenticação e Autorização</a
                            >
                            <ul>
                                <li>
                                    <a href="#jwt"
                                        >6.1. JWT (JSON Web Tokens)</a
                                    >
                                </li>
                                <li>
                                    <a href="#middleware"
                                        >6.2. Middlewares de Autenticação</a
                                    >
                                </li>
                                <li>
                                    <a href="#policies"
                                        >6.3. Policies e Gates</a
                                    >
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="#validacao"
                                >7. Validação e Tratamento de Dados</a
                            >
                            <ul>
                                <li>
                                    <a href="#form-requests"
                                        >7.1. Form Requests</a
                                    >
                                </li>
                                <li>
                                    <a href="#regras-validacao"
                                        >7.2. Regras de Validação
                                        Customizadas</a
                                    >
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="#tratamento-erros"
                                >8. Tratamento de Erros e Exceções</a
                            >
                            <ul>
                                <li>
                                    <a href="#exception-handler"
                                        >8.1. Exception Handler</a
                                    >
                                </li>
                                <li>
                                    <a href="#excecoes-customizadas"
                                        >8.2. Exceções Customizadas</a
                                    >
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="#boas-praticas"
                                >9. Boas Práticas e Padrões</a
                            >
                            <ul>
                                <li>
                                    <a href="#solid">9.1. Princípios SOLID</a>
                                </li>
                                <li>
                                    <a href="#design-patterns"
                                        >9.2. Design Patterns</a
                                    >
                                </li>
                                <li>
                                    <a href="#clean-code">9.3. Clean Code</a>
                                </li>
                            </ul>
                        </li>
                        <li><a href="#conclusao">10. Conclusão</a></li>
                    </ul>
                </div>

                <div class="manual-section" id="introducao">
                    <h2>1. Introdução</h2>
                    <p>
                        Este manual descreve a arquitetura de referência para
                        desenvolvimento de APIs RESTful utilizando Laravel 12 e
                        PHP 8.2. A arquitetura foi projetada para atender aos
                        requisitos de escalabilidade, manutenibilidade,
                        testabilidade e segurança, seguindo os princípios SOLID
                        e padrões de design modernos.
                    </p>

                    <p>
                        O objetivo deste documento é fornecer uma visão clara e
                        abrangente da arquitetura do sistema, servindo como
                        referência para todos os desenvolvedores envolvidos no
                        projeto.
                    </p>

                    <div class="note">
                        <p>
                            Este manual assume que você já possui conhecimento
                            básico sobre Laravel, PHP orientado a objetos e
                            desenvolvimento de APIs RESTful. Para informações
                            mais detalhadas sobre Laravel 12, consulte a
                            <a href="https://laravel.com/docs" target="_blank"
                                >documentação oficial</a
                            >.
                        </p>
                    </div>
                </div>

                <div class="manual-section" id="visao-geral">
                    <h2>2. Visão Geral da Arquitetura</h2>
                    <p>
                        Nossa arquitetura segue uma abordagem em camadas,
                        inspirada em conceitos de Clean Architecture e
                        Domain-Driven Design (DDD), adaptados para o contexto do
                        Laravel. Esta abordagem permite uma clara separação de
                        responsabilidades, facilitando a manutenção,
                        testabilidade e evolução do sistema.
                    </p>
<div class="diagram">
    <div class="mermaid">
    graph TD
        classDef presentation fill:#f9d5e5,stroke:#333,stroke-width:1px;
        classDef application fill:#eeeeee,stroke:#333,stroke-width:1px;
        classDef domain fill:#d0f0c0,stroke:#333,stroke-width:1px;
        classDef infrastructure fill:#b5dcff,stroke:#333,stroke-width:1px;
        classDef external fill:#ffcc99,stroke:#333,stroke-width:1px;

        Client[Cliente HTTP] --> Presentation
        Presentation --> Application
        Application --> Domain
        Domain --> Infrastructure
        Infrastructure --> External[Banco de Dados/Serviços Externos]

        subgraph Presentation[Camada de Apresentação]
            Controllers[Controllers]
            Middlewares[Middlewares]
            FormRequests[Form Requests]
            Resources[Resources]
        end

        subgraph Application[Camada de Aplicação]
            Services[Services]
            DTOs[DTOs]
            Events[Events]
        end

        subgraph Domain[Camada de Domínio]
            Models[Models]
            Interfaces[Interfaces]
            ValueObjects[Value Objects]
            Enums[Enums]
        end

        subgraph Infrastructure[Camada de Infraestrutura]
            Repositories[Repositories]
            ExternalServices[External Services]
            Cache[Cache]
            Queue[Queue]
        end

        class Presentation presentation;
        class Application application;
        class Domain domain;
        class Infrastructure infrastructure;
        class External external;
    </div>
    <p class="diagram-caption">Figura 1: Visão geral da arquitetura em camadas</p>
</div>

                    <p>
                        A arquitetura é composta por quatro camadas principais:
                    </p>

                    <ol>
                        <li>
                            <strong>Camada de Apresentação</strong>: Responsável
                            pela interface com o cliente, recebendo requisições
                            HTTP e retornando respostas.
                        </li>
                        <li>
                            <strong>Camada de Aplicação</strong>: Orquestra o
                            fluxo de dados entre a camada de apresentação e a
                            camada de domínio, implementando a lógica de
                            aplicação.
                        </li>
                        <li>
                            <strong>Camada de Domínio</strong>: Contém as
                            entidades de negócio, regras de negócio e interfaces
                            de repositórios.
                        </li>
                        <li>
                            <strong>Camada de Infraestrutura</strong>:
                            Implementa interfaces definidas na camada de
                            domínio, fornecendo acesso a recursos externos como
                            banco de dados, serviços de e-mail, cache, etc.
                        </li>
                    </ol>

                    <p>
                        Cada camada possui responsabilidades bem definidas e se
                        comunica apenas com camadas adjacentes, seguindo o
                        princípio de dependência unidirecional, onde as camadas
                        internas não conhecem as camadas externas.
                    </p>
                </div>

                <div class="manual-section" id="camadas">
                    <h2>3. Camadas da Arquitetura</h2>

                    <div class="manual-section" id="camada-apresentacao">
                        <h3>3.1. Camada de Apresentação</h3>
                        <p>
                            A camada de apresentação é o ponto de entrada da
                            aplicação, responsável por receber requisições HTTP,
                            validar dados de entrada, delegar o processamento
                            para a camada de aplicação e formatar as respostas.
                        </p>

                        <p>Componentes principais:</p>

                        <ul>
                            <li>
                                <strong>Controllers</strong>: Recebem
                                requisições HTTP, validam dados de entrada e
                                delegam o processamento para os serviços.
                            </li>
                            <li>
                                <strong>Middlewares</strong>: Processam
                                requisições antes de chegarem aos controllers
                                (autenticação, logging, etc.).
                            </li>
                            <li>
                                <strong>Form Requests</strong>: Encapsulam a
                                validação de dados de entrada.
                            </li>
                            <li>
                                <strong>Resources</strong>: Transformam modelos
                                em respostas JSON estruturadas.
                            </li>
                            <li>
                                <strong>API Routes</strong>: Definem os
                                endpoints da API e mapeiam para os controllers.
                            </li>
                        </ul>

                        <div class="code-block-header">
                            Exemplo de Controller
                        </div>
                        <pre><code>namespace App\Http\Controllers\Api;

use App\Http\Controllers\ControllerAbstract;
use App\Http\Requests\UserStoreRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Services\UserService;
use Illuminate\Http\Request;

class UserController extends ControllerAbstract
{
    public function __construct(protected UserService $service)
    {
        parent::__construct($service);
    }

    public function store(UserStoreRequest $request)
    {
        return $this->service->store($request->validated());
    }

    public function update(UserUpdateRequest $request, int $id)
    {
        return $this->service->update($id, $request->validated());
    }
}
</code></pre>
                    </div>

                    <div class="manual-section" id="camada-aplicacao">
                        <h3>3.2. Camada de Aplicação</h3>
                        <p>
                            A camada de aplicação contém a lógica de aplicação,
                            orquestrando o fluxo de dados entre a camada de
                            apresentação e a camada de domínio. É responsável
                            por implementar casos de uso e coordenar operações
                            que envolvem múltiplas entidades.
                        </p>

                        <p>Componentes principais:</p>

                        <ul>
                            <li>
                                <strong>Services</strong>: Implementam a lógica
                                de negócio e orquestram operações entre
                                múltiplas entidades.
                            </li>
                            <li>
                                <strong>DTOs (Data Transfer Objects)</strong>:
                                Transferem dados entre camadas de forma
                                estruturada.
                            </li>
                            <li>
                                <strong>Validators</strong>: Implementam regras
                                de validação complexas que vão além da validação
                                básica de formulários.
                            </li>
                        </ul>

                        <div class="code-block-header">Exemplo de Service</div>
                        <pre><code>namespace App\Services;

use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Hash;

class UserService extends ServiceAbstract
{
    public function __construct(protected UserRepository $repository)
    {
        parent::__construct($repository);
    }

    public function store(array $data): array
    {
        // Lógica de negócio específica antes de persistir
        $data['password'] = Hash::make($data['password']);

        // Delega a persistência para o repositório
        $user = $this->repository->create($data);

        // Lógica adicional após persistência
        // ...

        return $this->response->success('User created successfully', $user);
    }
}
</code></pre>
                    </div>

                    <div class="manual-section" id="camada-dominio">
    <h3>3.3. Camada de Domínio</h3>
    <p>A camada de domínio é o coração da aplicação, contendo as entidades de negócio, regras de negócio e interfaces de repositórios. Esta camada é independente de frameworks e tecnologias específicas.</p>

    <p>Componentes principais:</p>

    <ul>
        <li><strong>Models</strong>: Representam entidades de negócio e encapsulam regras de negócio relacionadas à entidade.</li>
        <li><strong>Interfaces de Repositórios</strong>: Definem contratos para acesso a dados.</li>
        <li><strong>Value Objects</strong>: Encapsulam conceitos de domínio que não possuem identidade.</li>
        <li><strong>Enums</strong>: Representam conjuntos fixos de valores relacionados ao domínio.</li>
        <li><strong>Events</strong>: Representam ocorrências de interesse no domínio.</li>
        <li><strong>Exceptions</strong>: Exceções específicas do domínio.</li>
    </ul>

    <div class="code-block-header">Exemplo de Model</div>
<pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'price',
        'category_id',
        'is_active'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    // Relacionamentos
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    // Regras de negócio encapsuladas no modelo
    public function isAvailableForPurchase(): bool
    {
        return $this->is_active && $this->stock > 0;
    }

    // Escopo de consulta
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
</code></pre>

    <div class="code-block-header">Exemplo de Enum</div>
<pre><code>namespace App\Enums;

enum OrderStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case CANCELLED = 'cancelled';
    case REFUNDED = 'refunded';

    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pendente',
            self::PROCESSING => 'Em Processamento',
            self::COMPLETED => 'Concluído',
            self::CANCELLED => 'Cancelado',
            self::REFUNDED => 'Reembolsado',
        };
    }

    public function isFinalized(): bool
    {
        return in_array($this, [self::COMPLETED, self::CANCELLED, self::REFUNDED]);
    }
}
</code></pre>
</div>

<div class="manual-section" id="camada-infraestrutura">
    <h3>3.4. Camada de Infraestrutura</h3>
    <p>A camada de infraestrutura implementa interfaces definidas na camada de domínio, fornecendo acesso a recursos externos como banco de dados, serviços de e-mail, cache, etc. Esta camada é responsável por lidar com detalhes técnicos e integrações com sistemas externos.</p>

    <p>Componentes principais:</p>

    <ul>
        <li><strong>Repositories</strong>: Implementam interfaces de repositório definidas na camada de domínio.</li>
        <li><strong>External Services</strong>: Integração com serviços externos (APIs, gateways de pagamento, etc.).</li>
        <li><strong>Mail</strong>: Implementações de envio de e-mails.</li>
        <li><strong>Cache</strong>: Implementações de cache.</li>
        <li><strong>Queue</strong>: Implementações de filas.</li>
        <li><strong>Storage</strong>: Implementações de armazenamento de arquivos.</li>
    </ul>

    <div class="code-block-header">Exemplo de Repository</div>
<pre><code>namespace App\Repositories;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class UserRepository extends RepositoryAbstract
{
    protected $model = User::class;

    public function findByEmail(string $email): ?User
    {
        return $this->model::where('email', $email)->first();
    }

    public function getActiveUsers(int $perPage = 15): LengthAwarePaginator
    {
        return $this->model::where('is_active', true)
            ->orderBy('name')
            ->paginate($perPage);
    }

    public function getAdmins(): Collection
    {
        return $this->model::whereHas('roles', function($query) {
            $query->where('name', 'admin');
        })->get();
    }
}
</code></pre>

<div class="diagram">
    <div class="mermaid" style="min-height: 600px;">
    flowchart TD
        classDef presentation fill:#f9d5e5,stroke:#333,stroke-width:2px;
        classDef application fill:#eeeeee,stroke:#333,stroke-width:2px;
        classDef domain fill:#d0f0c0,stroke:#333,stroke-width:2px;
        classDef infrastructure fill:#b5dcff,stroke:#333,stroke-width:2px;
        classDef external fill:#ffcc99,stroke:#333,stroke-width:2px;

        %% Camada de Apresentação
        subgraph Presentation["CAMADA DE APRESENTAÇÃO"]
            direction TB
            Controllers["Controllers<br>• Recebem requisições<br>• Delegam processamento<br>• Retornam respostas"]
            Middlewares["Middlewares<br>• Autenticação<br>• Autorização<br>• Logging<br>• CORS"]
            FormRequests["Form Requests<br>• Validação de dados<br>• Regras de validação<br>• Mensagens de erro"]
            Resources["Resources<br>• Transformação de dados<br>• Formatação JSON<br>• Inclusão de relacionamentos"]
            ApiRoutes["API Routes<br>• Definição de endpoints<br>• Agrupamento de rotas<br>• Aplicação de middlewares"]
        end

        %% Camada de Aplicação
        subgraph Application["CAMADA DE APLICAÇÃO"]
            direction TB
            Services["Services<br>• Lógica de negócio<br>• Orquestração<br>• Validação complexa"]
            DTOs["DTOs<br>• Transferência de dados<br>• Imutabilidade<br>• Tipagem forte"]
            AppEvents["Application Events<br>• Notificações do sistema<br>• Integração entre módulos<br>• Processamento assíncrono"]
            Jobs["Jobs<br>• Tarefas em background<br>• Processamento pesado<br>• Operações assíncronas"]
        end

        %% Camada de Domínio
        subgraph Domain["CAMADA DE DOMÍNIO"]
            direction TB
            Models["Models<br>• Entidades de negócio<br>• Regras de negócio<br>• Relacionamentos"]
            Interfaces["Interfaces<br>• Contratos de repositórios<br>• Inversão de dependência<br>• Abstração de serviços"]
            ValueObjects["Value Objects<br>• Objetos imutáveis<br>• Encapsulamento de conceitos<br>• Validação interna"]
            DomainEvents["Domain Events<br>• Eventos de negócio<br>• Notificações de mudanças<br>• Consistência eventual"]
            Enums["Enums<br>• Constantes tipadas<br>• Estados e tipos<br>• Comportamentos associados"]
        end

        %% Camada de Infraestrutura
        subgraph Infrastructure["CAMADA DE INFRAESTRUTURA"]
            direction TB
            Repositories["Repositories<br>• Acesso a dados<br>• Implementação de interfaces<br>• Queries e persistência"]
            ExternalServices["External Services<br>• Integração com APIs<br>• Gateways de pagamento<br>• Serviços de email"]
            Cache["Cache<br>• Armazenamento em memória<br>• Redis/Memcached<br>• Cache de queries"]
            Queue["Queue<br>• Filas de processamento<br>• Tarefas assíncronas<br>• Retry e fallback"]
            Storage["Storage<br>• Armazenamento de arquivos<br>• S3/Filesystem<br>• Uploads e downloads"]
        end

        %% Camada Externa
        subgraph External["BANCO DE DADOS / SERVIÇOS EXTERNOS"]
            direction TB
            Database["Banco de Dados<br>• MySQL/PostgreSQL<br>• MongoDB<br>• SQLite (testes)"]
            APIs["APIs Externas<br>• Serviços de terceiros<br>• Microserviços<br>• Integrações"]
            FileStorage["Armazenamento<br>• Amazon S3<br>• Google Cloud Storage<br>• Armazenamento local"]
            MessageBroker["Message Broker<br>• RabbitMQ<br>• Kafka<br>• Redis Pub/Sub"]
        end

        %% Conexões entre camadas
        Presentation --> Application
        Application --> Domain
        Domain --> Infrastructure
        Infrastructure --> External

        %% Conexões específicas
        Controllers --> Services
        Services --> Models
        Services --> Repositories
        Repositories --> Database
        ExternalServices --> APIs
        Storage --> FileStorage
        Queue --> MessageBroker

        %% Aplicar estilos
        class Presentation presentation;
        class Application application;
        class Domain domain;
        class Infrastructure infrastructure;
        class External external;
    </div>
    <p class="diagram-caption">Figura 2: Diagrama detalhado das camadas da arquitetura</p>
</div>

</div>

<div class="manual-section" id="componentes-base">
    <h2>4. Componentes Base</h2>
    <p>Para promover a consistência e reduzir a duplicação de código, a arquitetura define um conjunto de classes abstratas base que fornecem funcionalidades comuns para controllers, services, repositories e responses.</p>

    <div class="manual-section" id="controller-abstract">
        <h3>4.1. ControllerAbstract</h3>
        <p>A classe <code>ControllerAbstract</code> fornece uma base para todos os controllers da API, implementando métodos CRUD padrão e outras funcionalidades comuns.</p>

        <div class="code-block-header">Implementação do ControllerAbstract</div>
<pre><code>namespace App\Http\Controllers;

use App\Services\ServiceAbstract;
use Illuminate\Http\Request;

abstract class ControllerAbstract extends Controller
{
    protected $service;

    public function __construct(ServiceAbstract $service)
    {
        $this->service = $service;
    }

    public function index(Request $request)
    {
        return $this->service->index($request->all());
    }

    public function show(int $id)
    {
        return $this->service->show($id);
    }

    public function store(Request $request)
    {
        return $this->service->store($request->all());
    }

    public function update(Request $request, int $id)
    {
        return $this->service->update($id, $request->all());
    }

    public function destroy(int $id)
    {
        return $this->service->destroy($id);
    }
}
</code></pre>

        <p>Benefícios do <code>ControllerAbstract</code>:</p>
        <ul>
            <li>Reduz código boilerplate em controllers</li>
            <li>Padroniza a implementação de endpoints CRUD</li>
            <li>Facilita a manutenção e evolução dos controllers</li>
            <li>Promove a consistência na API</li>
        </ul>
    </div>

    <div class="manual-section" id="service-abstract">
        <h3>4.2. ServiceAbstract</h3>
        <p>A classe <code>ServiceAbstract</code> fornece uma base para todos os services da aplicação, implementando métodos CRUD padrão e outras funcionalidades comuns.</p>

        <div class="code-block-header">Implementação do ServiceAbstract</div>
<pre><code>namespace App\Services;

use App\Http\Responses\ResponseAbstract;
use App\Repositories\RepositoryAbstract;
use Illuminate\Support\Facades\Validator;

abstract class ServiceAbstract
{
    protected $repository;
    protected $response;

    public function __construct(RepositoryAbstract $repository)
    {
        $this->repository = $repository;
        $this->response = app(ResponseAbstract::class);
    }

    public function index(array $params = [])
    {
        $data = $this->repository->paginate($params);
        return $this->response->success('Resources retrieved successfully', $data);
    }

    public function show(int $id)
    {
        $data = $this->repository->find($id);

        if (!$data) {
            return $this->response->notFound('Resource not found');
        }

        return $this->response->success('Resource retrieved successfully', $data);
    }

    public function store(array $data)
    {
        $validator = $this->validateStore($data);

        if ($validator->fails()) {
            return $this->response->badRequest('Validation failed', $validator->errors());
        }

        $resource = $this->repository->create($data);
        return $this->response->created('Resource created successfully', $resource);
    }

    public function update(int $id, array $data)
    {
        $resource = $this->repository->find($id);

        if (!$resource) {
            return $this->response->notFound('Resource not found');
        }

        $validator = $this->validateUpdate($data, $id);

        if ($validator->fails()) {
            return $this->response->badRequest('Validation failed', $validator->errors());
        }

        $updated = $this->repository->update($id, $data);
        return $this->response->success('Resource updated successfully', $updated);
    }

    public function destroy(int $id)
    {
        $resource = $this->repository->find($id);

        if (!$resource) {
            return $this->response->notFound('Resource not found');
        }

        $this->repository->delete($id);
        return $this->response->success('Resource deleted successfully');
    }

    protected function validateStore(array $data)
    {
        return Validator::make($data, []);
    }

    protected function validateUpdate(array $data, int $id)
    {
        return Validator::make($data, []);
    }
}
</code></pre>

        <p>Benefícios do <code>ServiceAbstract</code>:</p>
        <ul>
            <li>Encapsula lógica de negócio comum</li>
            <li>Padroniza validação e tratamento de erros</li>
            <li>Facilita a implementação de novos serviços</li>
            <li>Promove a consistência na camada de aplicação</li>
        </ul>
    </div>

    <div class="manual-section" id="repository-abstract">
        <h3>4.3. RepositoryAbstract</h3>
        <p>A classe <code>RepositoryAbstract</code> fornece uma base para todos os repositories da aplicação, implementando métodos de acesso a dados comuns.</p>

        <div class="code-block-header">Implementação do RepositoryAbstract</div>
<pre><code>namespace App\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;

abstract class RepositoryAbstract
{
    protected $model;

    public function __construct()
    {
        $this->model = app($this->model);
    }

    public function all(array $columns = ['*'])
    {
        return $this->model::all($columns);
    }

    public function find(int $id, array $columns = ['*'])
    {
        return $this->model::find($id, $columns);
    }

    public function findOrFail(int $id, array $columns = ['*'])
    {
        return $this->model::findOrFail($id, $columns);
    }

    public function create(array $data)
    {
        return $this->model::create($data);
    }

    public function update(int $id, array $data)
    {
        $model = $this->find($id);

        if (!$model) {
            return false;
        }

        $model->update($data);
        return $model->fresh();
    }

    public function delete(int $id)
    {
        $model = $this->find($id);

        if (!$model) {
            return false;
        }

        return $model->delete();
    }

    public function paginate(array $params = [])
    {
        $perPage = $params['per_page'] ?? 15;
        $page = $params['page'] ?? 1;
        $columns = $params['columns'] ?? ['*'];
        $orderBy = $params['order_by'] ?? 'id';
        $orderDir = $params['order_dir'] ?? 'desc';

        $query = $this->model::query();

        // Aplicar filtros se existirem
        if (isset($params['filters']) && is_array($params['filters'])) {
            foreach ($params['filters'] as $field => $value) {
                if (is_array($value)) {
                    $query->whereIn($field, $value);
                } else {
                    $query->where($field, $value);
                }
            }
        }

        // Aplicar busca se existir
        if (isset($params['search']) && !empty($params['search'])) {
            $searchFields = $params['search_fields'] ?? ['name'];
            $search = $params['search'];

            $query->where(function($q) use ($searchFields, $search) {
                foreach ($searchFields as $field) {
                    $q->orWhere($field, 'like', "%{$search}%");
                }
            });
        }

        return $query->orderBy($orderBy, $orderDir)
            ->paginate($perPage, $columns, 'page', $page);
    }
}
</code></pre>

        <p>Benefícios do <code>RepositoryAbstract</code>:</p>
        <ul>
            <li>Abstrai a complexidade do acesso a dados</li>
            <li>Padroniza operações de CRUD</li>
            <li>Facilita a implementação de filtros e paginação</li>
            <li>Promove a consistência na camada de infraestrutura</li>
        </ul>
    </div>

    <div class="manual-section" id="response-abstract">
        <h3>4.4. ResponseAbstract</h3>
        <p>A classe <code>ResponseAbstract</code> fornece uma base para formatação consistente de respostas da API.</p>

        <div class="code-block-header">Implementação do ResponseAbstract</div>
<pre><code>namespace App\Http\Responses;

use Illuminate\Http\JsonResponse;

abstract class ResponseAbstract
{
    public function success(string $message, $data = null, int $statusCode = 200): JsonResponse
    {
        return $this->response($message, $data, $statusCode);
    }

    public function created(string $message, $data = null): JsonResponse
    {
        return $this->response($message, $data, 201);
    }

    public function badRequest(string $message, $errors = null): JsonResponse
    {
        return $this->response($message, null, 400, $errors);
    }

    public function unauthorized(string $message): JsonResponse
    {
        return $this->response($message, null, 401);
    }

    public function forbidden(string $message): JsonResponse
    {
        return $this->response($message, null, 403);
    }

    public function notFound(string $message): JsonResponse
    {
        return $this->response($message, null, 404);
    }

    public function error(string $message, int $statusCode = 500, $errors = null): JsonResponse
    {
        return $this->response($message, null, $statusCode, $errors);
    }

    protected function response(string $message, $data = null, int $statusCode = 200, $errors = null): JsonResponse
    {
        $response = [
            'success' => $statusCode < 400,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode);
    }
}
</code></pre>

        <div class="code-block-header">Exemplo de Implementação Concreta</div>
<pre><code>namespace App\Http\Responses;

class ApiResponse extends ResponseAbstract
{
    // Métodos específicos da API podem ser adicionados aqui
}
</code></pre>

        <p>Benefícios do <code>ResponseAbstract</code>:</p>
        <ul>
            <li>Padroniza o formato de respostas da API</li>
            <li>Facilita a manutenção e evolução do formato de respostas</li>
            <li>Promove a consistência na experiência do cliente da API</li>
            <li>Simplifica o tratamento de erros</li>
        </ul>

<div class="diagram">
    <div class="mermaid">
    classDiagram
        class ControllerAbstract {
            #service: ServiceAbstract
            +__construct(service)
            +index(request)
            +show(id)
            +store(request)
            +update(request, id)
            +destroy(id)
        }

        class ServiceAbstract {
            #repository: RepositoryAbstract
            #response: ResponseAbstract
            +__construct(repository)
            +index(params)
            +show(id)
            +store(data)
            +update(id, data)
            +destroy(id)
            #validateStore(data)
            #validateUpdate(data, id)
        }

        class RepositoryAbstract {
            #model: Model
            +__construct()
            +all(columns)
            +find(id, columns)
            +findOrFail(id, columns)
            +create(data)
            +update(id, data)
            +delete(id)
            +paginate(params)
        }

        class ResponseAbstract {
            +success(message, data, statusCode)
            +created(message, data)
            +badRequest(message, errors)
            +unauthorized(message)
            +forbidden(message)
            +notFound(message)
            +error(message, statusCode, errors)
            #response(message, data, statusCode, errors)
        }

        ControllerAbstract --> ServiceAbstract: uses
        ServiceAbstract --> RepositoryAbstract: uses
        ServiceAbstract --> ResponseAbstract: uses

        class UserController {
            +__construct(service)
            +store(request)
            +update(request, id)
        }

        class UserService {
            +__construct(repository)
            +store(data)
        }

        class UserRepository {
            #model: User
            +findByEmail(email)
            +getActiveUsers(perPage)
            +getAdmins()
        }

        class ApiResponse {
        }

        UserController --|> ControllerAbstract
        UserService --|> ServiceAbstract
        UserRepository --|> RepositoryAbstract
        ApiResponse --|> ResponseAbstract
    </div>
    <p class="diagram-caption">Figura 3: Diagrama das classes abstratas base e suas relações</p>
</div>

    </div>
</div>

<div class="manual-section" id="fluxo-requisicao">
    <h2>5. Fluxo de Requisição</h2>
    <p>Esta seção descreve o fluxo de uma requisição HTTP desde o momento em que chega à aplicação até o retorno da resposta.</p>

    <div class="manual-section" id="fluxo-crud">
        <h3>5.1. Fluxo CRUD Padrão</h3>
        <p>O fluxo de uma requisição CRUD padrão segue os seguintes passos:</p>

        <ol>
            <li><strong>Entrada da Requisição</strong>: A requisição HTTP chega ao servidor e é processada pelo Laravel.</li>
            <li><strong>Roteamento</strong>: O router do Laravel identifica a rota correspondente e o controller responsável.</li>
            <li><strong>Middlewares</strong>: A requisição passa pelos middlewares configurados (autenticação, logging, etc.).</li>
            <li><strong>Controller</strong>: O método apropriado do controller é invocado.</li>
            <li><strong>Validação</strong>: Os dados de entrada são validados (via Form Request ou no Service).</li>
            <li><strong>Service</strong>: O controller delega o processamento para o service correspondente.</li>
            <li><strong>Repository</strong>: O service utiliza o repository para acessar ou persistir dados.</li>
            <li><strong>Modelo</strong>: O repository interage com os modelos do Eloquent.</li>
            <li><strong>Banco de Dados</strong>: Os modelos interagem com o banco de dados.</li>
            <li><strong>Resposta</strong>: O resultado é formatado pelo service e retornado como resposta HTTP.</li>
        </ol>

<div class="diagram">
    <div class="mermaid">
    sequenceDiagram
        participant Client as Cliente
        participant Router as Router
        participant Middleware as Middleware
        participant Controller as Controller
        participant FormRequest as Form Request
        participant Service as Service
        participant Repository as Repository
        participant Model as Model
        participant DB as Banco de Dados

        Client->>Router: HTTP Request
        Router->>Middleware: Encaminha requisição
        Middleware->>Controller: Autoriza requisição
        Controller->>FormRequest: Valida dados
        FormRequest-->>Controller: Dados validados
        Controller->>Service: Delega processamento
        Service->>Repository: Solicita operação de dados
        Repository->>Model: Interage com modelo
        Model->>DB: Query SQL
        DB-->>Model: Resultado
        Model-->>Repository: Dados
        Repository-->>Service: Dados processados
        Service-->>Controller: Resposta formatada
        Controller-->>Client: HTTP Response
    </div>
    <p class="diagram-caption">Figura 4: Fluxo de requisição CRUD padrão</p>
</div>


        <div class="code-block-header">Exemplo de Fluxo Completo (Criação de Usuário)</div>
<pre><code>// 1. Rota (routes/api.php)
Route::post('/users', [UserController::class, 'store'])
    ->middleware(['auth:api', 'permission:create-users']);

// 2. Form Request (app/Http/Requests/UserStoreRequest.php)
class UserStoreRequest extends FormRequest
{
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id'
        ];
    }
}

// 3. Controller (app/Http/Controllers/Api/UserController.php)
public function store(UserStoreRequest $request)
{
    return $this->service->store($request->validated());
}

// 4. Service (app/Services/UserService.php)
public function store(array $data)
{
    // Lógica de negócio
    $data['password'] = Hash::make($data['password']);

    // Persistência via repository
    $user = $this->repository->create($data);

    // Atribuir role
    $user->roles()->attach($data['role_id']);

    // Enviar e-mail de boas-vindas
    Mail::to($user)->send(new WelcomeEmail($user));

    // Retornar resposta formatada
    return $this->response->created('User created successfully', $user);
}

// 5. Repository (app/Repositories/UserRepository.php)
// Utiliza os métodos herdados de RepositoryAbstract

// 6. Model (app/Models/User.php)
// Interage com o banco de dados via Eloquent
</code></pre>
    </div>

    <div class="manual-section" id="fluxo-autenticacao">
        <h3>5.2. Fluxo de Autenticação</h3>
        <p>O fluxo de autenticação utilizando JWT segue os seguintes passos:</p>

        <ol>
            <li><strong>Login</strong>: O usuário envia credenciais (email/senha) para o endpoint de login.</li>
            <li><strong>Validação</strong>: As credenciais são validadas contra o banco de dados.</li>
            <li><strong>Geração de Token</strong>: Um token JWT é gerado contendo o ID do usuário e outras claims.</li>
            <li><strong>Resposta</strong>: O token é retornado ao cliente.</li>
            <li><strong>Requisições Autenticadas</strong>: O cliente inclui o token no header Authorization de requisições subsequentes.</li>
            <li><strong>Middleware de Autenticação</strong>: O middleware JWT verifica e valida o token.</li>
            <li><strong>Usuário Autenticado</strong>: O usuário é identificado e a requisição prossegue.</li>
        </ol>

<div class="diagram">
    <div class="mermaid">
    sequenceDiagram
        participant Client as Cliente
        participant AuthController as AuthController
        participant AuthService as AuthService
        participant JWT as JWT Auth
        participant DB as Banco de Dados
        participant API as API Protegida
        participant JWTMiddleware as JWT Middleware

        Client->>AuthController: Login (email/senha)
        AuthController->>AuthService: Valida credenciais
        AuthService->>DB: Verifica usuário
        DB-->>AuthService: Confirma usuário
        AuthService->>JWT: Gera token JWT
        JWT-->>AuthService: Token JWT
        AuthService-->>AuthController: Token JWT
        AuthController-->>Client: Retorna token JWT

        Client->>API: Requisição com token no header
        API->>JWTMiddleware: Verifica token
        JWTMiddleware->>JWT: Valida token
        JWT-->>JWTMiddleware: Token válido
        JWTMiddleware-->>API: Autoriza requisição
        API-->>Client: Resposta protegida
    </div>
    <p class="diagram-caption">Figura 5: Fluxo de autenticação JWT</p>
</div>


        <div class="code-block-header">Exemplo de Fluxo de Autenticação</div>
<pre><code>// 1. Rota (routes/api.php)
Route::post('/auth/login', [AuthController::class, 'login']);
Route::post('/auth/refresh', [AuthController::class, 'refresh']);
Route::post('/auth/logout', [AuthController::class, 'logout'])->middleware('auth:api');

// 2. Controller (app/Http/Controllers/Api/AuthController.php)
public function login(Request $request)
{
    $credentials = $request->validate([
        'email' => 'required|email',
        'password' => 'required|string',
    ]);

    return $this->authService->login($credentials);
}

// 3. Service (app/Services/AuthService.php)
public function login(array $credentials)
{
    if (!$token = auth()->attempt($credentials)) {
        return $this->response->unauthorized('Invalid credentials');
    }

    return $this->respondWithToken($token);
}

protected function respondWithToken($token)
{
    return $this->response->success('Authentication successful', [
        'access_token' => $token,
        'token_type' => 'bearer',
        'expires_in' => auth()->factory()->getTTL() * 60
    ]);
}

// 4. Middleware (app/Http/Middleware/JwtMiddleware.php)
public function handle($request, Closure $next)
{
    try {
        $user = JWTAuth::parseToken()->authenticate();
    } catch (Exception $e) {
        if ($e instanceof TokenInvalidException) {
            return response()->json(['message' => 'Token is invalid'], 401);
        } else if ($e instanceof TokenExpiredException) {
            return response()->json(['message' => 'Token has expired'], 401);
        } else {
            return response()->json(['message' => 'Authorization token not found'], 401);
        }
    }

    return $next($request);
}
</code></pre>
    </div>
</div>

<div class="manual-section" id="autenticacao">
    <h2>6. Autenticação e Autorização</h2>
    <p>Esta seção descreve a implementação de autenticação e autorização na API.</p>

    <div class="manual-section" id="jwt">
        <h3>6.1. JWT (JSON Web Tokens)</h3>
        <p>A autenticação na API é implementada utilizando JWT (JSON Web Tokens), que oferece um mecanismo seguro e stateless para autenticação de usuários.</p>

        <p>Principais características da implementação JWT:</p>
        <ul>
            <li><strong>Stateless</strong>: Não requer armazenamento de sessão no servidor.</li>
            <li><strong>Seguro</strong>: Tokens são assinados digitalmente para garantir integridade.</li>
            <li><strong>Expiração</strong>: Tokens têm tempo de vida limitado.</li>
            <li><strong>Refresh Tokens</strong>: Mecanismo para renovar tokens expirados sem exigir nova autenticação.</li>
            <li><strong>Blacklist</strong>: Mecanismo para invalidar tokens antes da expiração (logout).</li>
        </ul>

        <div class="code-block-header">Configuração JWT (config/jwt.php)</div>
<pre><code>return [
    'secret' => env('JWT_SECRET'),
    'ttl' => env('JWT_TTL', 60), // Tempo de vida em minutos
    'refresh_ttl' => env('JWT_REFRESH_TTL', 20160), // 2 semanas
    'algo' => env('JWT_ALGO', 'HS256'),
    'blacklist_enabled' => env('JWT_BLACKLIST_ENABLED', true),
    'blacklist_grace_period' => env('JWT_BLACKLIST_GRACE_PERIOD', 30),
];
</code></pre>
    </div>

    <div class="manual-section" id="middleware">
        <h3>6.2. Middlewares de Autenticação</h3>
        <p>A API utiliza middlewares para verificar e processar tokens JWT em requisições.</p>

        <p>Principais middlewares:</p>
        <ul>
            <li><strong>JwtVerify</strong>: Verifica a validade do token JWT.</li>
            <li><strong>JwtRefreshMiddleware</strong>: Gerencia refresh de tokens.</li>
            <li><strong>RoleMiddleware</strong>: Verifica se o usuário possui determinada role.</li>
            <li><strong>PermissionMiddleware</strong>: Verifica se o usuário possui determinada permissão.</li>
        </ul>

        <div class="code-block-header">Exemplo de Middleware JWT</div>
<pre><code>namespace App\Http\Middleware;

use Closure;
use Exception;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;

class JwtVerify
{
    public function handle($request, Closure $next)
    {
        try {
            $user = JWTAuth::parseToken()->authenticate();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

        } catch (Exception $e) {
            if ($e instanceof TokenInvalidException) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token is invalid'
                ], 401);
            } else if ($e instanceof TokenExpiredException) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token has expired'
                ], 401);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Authorization token not found'
                ], 401);
            }
        }

        return $next($request);
    }
}
</code></pre>

        <div class="code-block-header">Registro de Middlewares (app/Http/Kernel.php)</div>
<pre><code>protected $routeMiddleware = [
    // ...
    'jwt.verify' => \App\Http\Middleware\JwtVerify::class,
    'jwt.refresh' => \App\Http\Middleware\JwtRefreshMiddleware::class,
    'role' => \App\Http\Middleware\RoleMiddleware::class,
    'permission' => \App\Http\Middleware\PermissionMiddleware::class,
];
</code></pre>
    </div>

    <div class="manual-section" id="policies">
    <h3>6.3. Policies e Gates</h3>
    <p>Para autorização mais granular, a API utiliza Policies e Gates do Laravel. Policies são classes que encapsulam lógica de autorização relacionada a um modelo específico, enquanto Gates são closures que definem regras de autorização mais gerais.</p>

    <div class="code-block-header">Exemplo de Policy</div>
<pre><code>namespace App\Policies;

use App\Models\User;
use App\Models\Post;
use Illuminate\Auth\Access\HandlesAuthorization;

class PostPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user)
    {
        return true;
    }

    public function view(User $user, Post $post)
    {
        return true;
    }

    public function create(User $user)
    {
        return $user->hasPermission('create-posts');
    }

    public function update(User $user, Post $post)
    {
        return $user->id === $post->user_id || $user->hasPermission('edit-any-post');
    }

    public function delete(User $user, Post $post)
    {
        return $user->id === $post->user_id || $user->hasPermission('delete-any-post');
    }

    public function restore(User $user, Post $post)
    {
        return $user->hasPermission('restore-posts');
    }

    public function forceDelete(User $user, Post $post)
    {
        return $user->hasPermission('force-delete-posts');
    }
}
</code></pre>

    <div class="code-block-header">Registro de Policies (app/Providers/AuthServiceProvider.php)</div>
<pre><code>namespace App\Providers;

use App\Models\Post;
use App\Policies\PostPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        Post::class => PostPolicy::class,
    ];

    public function boot()
    {
        $this->registerPolicies();

        // Definição de Gates
        Gate::define('access-admin', function ($user) {
            return $user->hasRole('admin');
        });

        Gate::define('manage-users', function ($user) {
            return $user->hasPermission('manage-users');
        });

        // Gate com múltiplos parâmetros
        Gate::define('manage-team', function ($user, $team) {
            return $user->teams->contains($team) && $user->hasRole('team-leader');
        });
    }
}
</code></pre>

    <div class="code-block-header">Uso de Policies em Controllers</div>
<pre><code>namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Post;
use Illuminate\Http\Request;

class PostController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Post::class, 'post');
    }

    public function update(Request $request, Post $post)
    {
        // A autorização já foi verificada pelo authorizeResource
        // mas você pode fazer verificações adicionais
        $this->authorize('update', $post);

        // Lógica de atualização
    }

    public function forceDelete(Post $post)
    {
        // Verificação manual de autorização
        if ($this->authorize('forceDelete', $post)) {
            $post->forceDelete();
            return response()->json(['message' => 'Post permanently deleted']);
        }
    }
}
</code></pre>

    <div class="note">
        <p>As Policies devem ser usadas para lógica de autorização relacionada a modelos específicos, enquanto Gates são mais adequadas para regras de autorização gerais do sistema.</p>
    </div>

    <div class="tip">
        <p>Use o comando <code>php artisan make:policy PostPolicy --model=Post</code> para gerar uma nova Policy com todos os métodos padrão.</p>
    </div>
</div>

<div class="manual-section" id="validacao">
    <h2>7. Validação e Tratamento de Dados</h2>
    <p>Esta seção descreve as estratégias e implementações para validação e tratamento de dados na API, garantindo a integridade, segurança e consistência dos dados.</p>

    <div class="manual-section" id="form-requests">
        <h3>7.1. Form Requests</h3>
        <p>Form Requests são classes dedicadas à validação de dados de entrada, permitindo encapsular regras de validação complexas e lógica de autorização.</p>

        <div class="code-block-header">Exemplo de Form Request</div>
<pre><code>namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserStoreRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'email',
                Rule::unique('users')->ignore($this->user),
            ],
            'password' => [
                'required',
                'string',
                'min:8',
                'confirmed',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/'
            ],
            'role_id' => ['required', 'exists:roles,id'],
            'is_active' => ['boolean'],
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'O nome é obrigatório',
            'email.required' => 'O e-mail é obrigatório',
            'email.email' => 'Digite um e-mail válido',
            'email.unique' => 'Este e-mail já está em uso',
            'password.required' => 'A senha é obrigatória',
            'password.min' => 'A senha deve ter no mínimo 8 caracteres',
            'password.regex' => 'A senha deve conter letras maiúsculas, minúsculas, números e caracteres especiais',
            'role_id.required' => 'O papel do usuário é obrigatório',
            'role_id.exists' => 'O papel selecionado não existe',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'is_active' => $this->boolean('is_active', true),
        ]);
    }
}
</code></pre>

        <p>Benefícios dos Form Requests:</p>
        <ul>
            <li>Separa a lógica de validação dos controllers, mantendo-os enxutos</li>
            <li>Permite reutilização de regras de validação em diferentes endpoints</li>
            <li>Facilita a manutenção e evolução das regras de validação</li>
            <li>Permite a definição de mensagens de erro personalizadas</li>
            <li>Oferece hooks para pré-processamento e pós-processamento dos dados</li>
        </ul>

        <div class="tip">
            <p>Use o comando <code>php artisan make:request UserStoreRequest</code> para gerar um novo Form Request.</p>
        </div>
    </div>

    <div class="manual-section" id="regras-validacao">
        <h3>7.2. Regras de Validação Customizadas</h3>
        <p>Além das regras de validação padrão do Laravel, a arquitetura permite a criação de regras de validação customizadas para atender requisitos específicos de negócio.</p>

        <div class="code-block-header">Criando uma Regra de Validação Customizada</div>
<pre><code>namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class StrongPassword implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        // Pelo menos 8 caracteres
        if (strlen($value) < 8) {
            return false;
        }

        // Pelo menos uma letra maiúscula
        if (!preg_match('/[A-Z]/', $value)) {
            return false;
        }

        // Pelo menos uma letra minúscula
        if (!preg_match('/[a-z]/', $value)) {
            return false;
        }

        // Pelo menos um número
        if (!preg_match('/[0-9]/', $value)) {
            return false;
        }

        // Pelo menos um caractere especial
        if (!preg_match('/[^A-Za-z0-9]/', $value)) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'A senha deve conter pelo menos 8 caracteres, incluindo letras maiúsculas, minúsculas, números e caracteres especiais.';
    }
}
</code></pre>

        <div class="code-block-header">Usando a Regra Customizada em um Form Request</div>
<pre><code>namespace App\Http\Requests;

use App\Rules\StrongPassword;
use Illuminate\Foundation\Http\FormRequest;

class UserStoreRequest extends FormRequest
{
    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email'],
            'password' => ['required', 'string', new StrongPassword, 'confirmed'],
            // outras regras...
        ];
    }
}
</code></pre>

        <p>Também é possível criar regras de validação usando closures para casos mais simples:</p>

        <div class="code-block-header">Regra de Validação com Closure</div>
<pre><code>public function rules()
{
    return [
        'phone' => [
            'required',
            'string',
            function ($attribute, $value, $fail) {
                // Remove caracteres não numéricos
                $numericValue = preg_replace('/[^0-9]/', '', $value);

                // Verifica se tem o formato correto (DDD + número)
                if (strlen($numericValue) < 10 || strlen($numericValue) > 11) {
                    $fail('O campo telefone deve estar no formato (XX) XXXXX-XXXX.');
                }
            },
        ],
    ];
}
</code></pre>

        <div class="tip">
            <p>Use o comando <code>php artisan make:rule StrongPassword</code> para gerar uma nova regra de validação customizada.</p>
        </div>
    </div>

    <div class="manual-section" id="validacao-avancada">
        <h3>7.3. Validação Avançada</h3>
        <p>Para casos de validação mais complexos que envolvem múltiplos campos ou consultas ao banco de dados, a arquitetura oferece abordagens avançadas.</p>

        <h4>7.3.1. Validação Condicional</h4>
        <div class="code-block-header">Exemplo de Validação Condicional</div>
<pre><code>public function rules()
{
    $rules = [
        'name' => ['required', 'string', 'max:255'],
        'email' => ['required', 'email', 'unique:users,email'],
        'type' => ['required', 'in:individual,company'],
    ];

    if ($this->input('type') === 'individual') {
        $rules['cpf'] = ['required', 'cpf', 'unique:users,cpf'];
        $rules['birth_date'] = ['required', 'date', 'before:today'];
    }

    if ($this->input('type') === 'company') {
        $rules['cnpj'] = ['required', 'cnpj', 'unique:users,cnpj'];
        $rules['company_name'] = ['required', 'string', 'max:255'];
    }

    return $rules;
}
</code></pre>

        <h4>7.3.2. Validação com Dependências</h4>
        <div class="code-block-header">Exemplo de Validação com Dependências</div>
<pre><code>public function rules()
{
    return [
        'start_date' => ['required', 'date'],
        'end_date' => [
            'required',
            'date',
            'after_or_equal:start_date'
        ],
        'discount_code' => [
            'nullable',
            'string',
            function ($attribute, $value, $fail) {
                // Verifica se o código de desconto existe e está ativo
                $discountCode = DiscountCode::where('code', $value)
                    ->where('is_active', true)
                    ->where('expires_at', '>', now())
                    ->first();

                if (!$discountCode) {
                    $fail('O código de desconto informado é inválido ou expirou.');
                }
            },
        ],
    ];
}
</code></pre>

        <h4>7.3.3. Validação de Arrays</h4>
        <div class="code-block-header">Exemplo de Validação de Arrays</div>
<pre><code>public function rules()
{
    return [
        'products' => ['required', 'array', 'min:1'],
        'products.*.id' => ['required', 'exists:products,id'],
        'products.*.quantity' => ['required', 'integer', 'min:1'],
        'products.*.options' => ['sometimes', 'array'],
        'products.*.options.*.name' => ['required_with:products.*.options', 'string'],
        'products.*.options.*.value' => ['required_with:products.*.options', 'string'],
    ];
}
</code></pre>
    </div>

    <div class="manual-section" id="sanitizacao">
        <h3>7.4. Sanitização de Dados</h3>
        <p>A sanitização de dados é o processo de limpeza e formatação dos dados de entrada para garantir que estejam em um formato consistente antes de serem processados ou armazenados.</p>

        <h4>7.4.1. Sanitização no Form Request</h4>
        <div class="code-block-header">Exemplo de Sanitização no Form Request</div>
<pre><code>protected function prepareForValidation()
{
    // Remove espaços extras e formata o nome
    if ($this->has('name')) {
        $this->merge([
            'name' => ucwords(strtolower(trim($this->name))),
        ]);
    }

    // Formata o telefone removendo caracteres não numéricos
    if ($this->has('phone')) {
        $this->merge([
            'phone' => preg_replace('/[^0-9]/', '', $this->phone),
        ]);
    }

    // Converte para minúsculas
    if ($this->has('email')) {
        $this->merge([
            'email' => strtolower(trim($this->email)),
        ]);
    }

    // Converte valores booleanos
    if ($this->has('is_active')) {
        $this->merge([
            'is_active' => $this->boolean('is_active'),
        ]);
    }
}
</code></pre>

        <h4>7.4.2. Sanitização após Validação</h4>
        <div class="code-block-header">Exemplo de Sanitização após Validação</div>
<pre><code>protected function passedValidation()
{
    // Criptografa a senha após a validação
    if ($this->has('password')) {
        $this->merge([
            'password' => Hash::make($this->password),
        ]);
    }

    // Formata o CPF/CNPJ após validação
    if ($this->has('document')) {
        $document = $this->document;
        // Remove formatação
        $document = preg_replace('/[^0-9]/', '', $document);

        // Armazena sem formatação
        $this->merge(['document' => $document]);
    }
}
</code></pre>

        <div class="note">
            <p>A sanitização de dados deve ser feita com cuidado para não alterar o significado dos dados ou remover informações importantes. Em alguns casos, pode ser preferível armazenar os dados originais e formatar apenas na apresentação.</p>
        </div>
    </div>

    <div class="manual-section" id="validacao-service">
        <h3>7.5. Validação na Camada de Serviço</h3>
        <p>Embora a maior parte da validação deva ser feita nos Form Requests, algumas validações mais complexas ou que dependem de lógica de negócio podem ser implementadas na camada de serviço.</p>

        <div class="code-block-header">Exemplo de Validação na Camada de Serviço</div>
<pre><code>namespace App\Services;

use App\Exceptions\BusinessRuleException;
use App\Models\Order;
use App\Repositories\OrderRepository;
use App\Repositories\ProductRepository;

class OrderService extends ServiceAbstract
{
    protected $productRepository;

    public function __construct(
        OrderRepository $repository,
        ProductRepository $productRepository
    ) {
        parent::__construct($repository);
        $this->productRepository = $productRepository;
    }

    public function store(array $data)
    {
        // Validação de regras de negócio complexas
        $this->validateProductsAvailability($data['products']);
        $this->validateCustomerCreditLimit($data['customer_id'], $data['total_amount']);

        // Continua com a criação do pedido
        $order = $this->repository->create($data);

        return $this->response->created('Order created successfully', $order);
    }

    protected function validateProductsAvailability(array $products)
    {
        foreach ($products as $item) {
            $product = $this->productRepository->find($item['product_id']);

            if (!$product) {
                throw new BusinessRuleException("Product not found: {$item['product_id']}");
            }

            if (!$product->is_active) {
                throw new BusinessRuleException("Product is not active: {$product->name}");
            }

            if ($product->stock < $item['quantity']) {
                throw new BusinessRuleException("Insufficient stock for product: {$product->name}");
            }
        }
    }

    protected function validateCustomerCreditLimit(int $customerId, float $amount)
    {
        $customer = $this->customerRepository->find($customerId);

        if (!$customer) {
            throw new BusinessRuleException("Customer not found");
        }

        $currentBalance = $customer->credit_limit - $customer->used_credit;

        if ($amount > $currentBalance) {
            throw new BusinessRuleException("Insufficient credit limit. Available: {$currentBalance}");
        }
    }
}
</code></pre>

        <div class="note">
            <p>A validação na camada de serviço deve ser focada em regras de negócio complexas que não podem ser facilmente expressas em Form Requests, como verificações que envolvem múltiplas entidades ou cálculos complexos.</p>
        </div>
    </div>

    <div class="manual-section" id="boas-praticas-validacao">
        <h3>7.6. Boas Práticas de Validação</h3>
        <p>Seguir boas práticas de validação é essencial para garantir a segurança, usabilidade e manutenibilidade da API.</p>

        <ul>
            <li><strong>Validação em Camadas</strong>: Implemente validação em múltiplas camadas (cliente, API, banco de dados) para garantir a integridade dos dados.</li>
            <li><strong>Mensagens Claras</strong>: Forneça mensagens de erro claras e específicas que ajudem o cliente a entender e corrigir o problema.</li>
            <li><strong>Validação Consistente</strong>: Mantenha regras de validação consistentes em toda a aplicação para evitar comportamentos inesperados.</li>
            <li><strong>Evite Duplicação</strong>: Reutilize regras de validação quando possível para evitar duplicação de código.</li>
            <li><strong>Validação Contextual</strong>: Adapte as regras de validação ao contexto da operação (criação, atualização, etc.).</li>
            <li><strong>Segurança Primeiro</strong>: Sempre valide dados de entrada para prevenir vulnerabilidades como injeção SQL, XSS e CSRF.</li>
            <li><strong>Testes Automatizados</strong>: Escreva testes para suas regras de validação para garantir que funcionem como esperado.</li>
        </ul>

        <div class="code-block-header">Exemplo de Teste para Form Request</div>
<pre><code>namespace Tests\Unit\Http\Requests;

use App\Http\Requests\UserStoreRequest;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class UserStoreRequestTest extends TestCase
{
    /** @test */
    public function it_fails_when_required_fields_are_missing()
    {
        $request = new UserStoreRequest();

        $validator = Validator::make([], $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
        $this->assertArrayHasKey('email', $validator->errors()->toArray());
        $this->assertArrayHasKey('password', $validator->errors()->toArray());
    }

    /** @test */
    public function it_passes_with_valid_data()
    {
        $request = new UserStoreRequest();

        $validator = Validator::make([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'role_id' => 1,
        ], $request->rules());

        $this->assertTrue($validator->passes());
    }

    /** @test */
    public function it_fails_with_invalid_email()
    {
        $request = new UserStoreRequest();

        $validator = Validator::make([
            'name' => 'John Doe',
            'email' => 'invalid-email',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'role_id' => 1,
        ], $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('email', $validator->errors()->toArray());
    }
}
</code></pre>
    </div>
</div>

<div class="manual-section" id="tratamento-erros">
    <h2>8. Tratamento de Erros e Exceções</h2>
    <p>Esta seção descreve as estratégias e implementações para tratamento de erros e exceções na API, garantindo respostas consistentes e informativas para os clientes.</p>

    <div class="manual-section" id="exception-handler">
        <h3>8.1. Exception Handler</h3>
        <p>O Laravel fornece um mecanismo centralizado para tratamento de exceções através da classe <code>App\Exceptions\Handler</code>. Esta classe pode ser estendida para personalizar o tratamento de exceções específicas da aplicação.</p>

        <div class="code-block-header">Implementação do Exception Handler</div>
<pre><code>namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        AuthenticationException::class,
        AuthorizationException::class,
        ValidationException::class,
        BusinessRuleException::class,
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            // Lógica de relatório personalizada
        });

        $this->renderable(function (Throwable $e, $request) {
            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->handleApiException($e, $request);
            }
        });
    }

    /**
     * Handle API exceptions and return consistent JSON responses.
     *
     * @param  \Throwable  $e
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    private function handleApiException(Throwable $e, $request): JsonResponse
    {
        // Tratamento de exceções específicas
        if ($e instanceof ValidationException) {
            return $this->convertValidationExceptionToResponse($e, $request);
        }

        if ($e instanceof ModelNotFoundException) {
            return response()->json([
                'success' => false,
                'message' => 'Resource not found',
            ], 404);
        }

        if ($e instanceof AuthenticationException) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated',
            ], 401);
        }

        if ($e instanceof AuthorizationException) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized',
            ], 403);
        }

        if ($e instanceof NotFoundHttpException) {
            return response()->json([
                'success' => false,
                'message' => 'The specified URL cannot be found',
            ], 404);
        }

        if ($e instanceof BusinessRuleException) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }

        if ($e instanceof HttpException) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage() ?: 'HTTP Exception',
            ], $e->getStatusCode());
        }

        // Tratamento genérico para outras exceções
        if (config('app.debug')) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTrace(),
            ], 500);
        }

        return response()->json([
            'success' => false,
            'message' => 'An unexpected error occurred',
        ], 500);
    }

    /**
     * Convert a validation exception into a JSON response.
     *
     * @param  \Illuminate\Validation\ValidationException  $e
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    protected function convertValidationExceptionToResponse(ValidationException $e, $request)
    {
        return response()->json([
            'success' => false,
            'message' => 'The given data was invalid',
            'errors' => $e->errors(),
        ], $e->status);
    }
}
</code></pre>

        <p>O Exception Handler personalizado garante que todas as exceções sejam tratadas de forma consistente, retornando respostas JSON formatadas para clientes da API.</p>
    </div>

    <div class="manual-section" id="excecoes-customizadas">
        <h3>8.2. Exceções Customizadas</h3>
        <p>A arquitetura define exceções customizadas para representar diferentes tipos de erros específicos da aplicação.</p>

        <div class="code-block-header">Exemplo de Exceção Customizada</div>
<pre><code>namespace App\Exceptions;

use Exception;

class BusinessRuleException extends Exception
{
    /**
     * Create a new business rule exception instance.
     *
     * @param  string  $message
     * @param  int  $code
     * @param  \Throwable|null  $previous
     * @return void
     */
    public function __construct(string $message = 'Business rule violation', int $code = 422, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
</code></pre>

        <div class="code-block-header">Exemplo de Uso de Exceção Customizada</div>
<pre><code>namespace App\Services;

use App\Exceptions\BusinessRuleException;
use App\Models\Order;
use App\Repositories\OrderRepository;

class OrderService extends ServiceAbstract
{
    public function cancelOrder(int $orderId)
    {
        $order = $this->repository->find($orderId);

        if (!$order) {
            return $this->response->notFound('Order not found');
        }

        if ($order->status === 'completed') {
            throw new BusinessRuleException('Cannot cancel a completed order');
        }

        if ($order->status === 'shipped') {
            throw new BusinessRuleException('Cannot cancel an order that has been shipped');
        }

        $order->status = 'cancelled';
        $order->cancelled_at = now();
        $order->save();

        // Lógica adicional para cancelamento

        return $this->response->success('Order cancelled successfully', $order);
    }
}
</code></pre>

        <p>Outros exemplos de exceções customizadas que podem ser úteis:</p>

        <div class="code-block-header">Outras Exceções Customizadas</div>
<pre><code>// App\Exceptions\ResourceNotFoundException.php
namespace App\Exceptions;

use Exception;

class ResourceNotFoundException extends Exception
{
    public function __construct(string $resource = 'Resource', int $code = 404, \Throwable $previous = null)
    {
        parent::__construct("{$resource} not found", $code, $previous);
    }
}

// App\Exceptions\UnauthorizedException.php
namespace App\Exceptions;

use Exception;

class UnauthorizedException extends Exception
{
    public function __construct(string $message = 'You are not authorized to perform this action', int $code = 403, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

// App\Exceptions\ExternalServiceException.php
namespace App\Exceptions;

use Exception;

class ExternalServiceException extends Exception
{
    protected $service;
    protected $errorData;

    public function __construct(string $service, string $message = 'External service error', array $errorData = [], int $code = 500, \Throwable $previous = null)
    {
        $this->service = $service;
        $this->errorData = $errorData;

        parent::__construct($message, $code, $previous);
    }

    public function getService()
    {
        return $this->service;
    }

    public function getErrorData()
    {
        return $this->errorData;
    }
}
</code></pre>

        <div class="tip">
            <p>Crie exceções customizadas para representar erros específicos do domínio da aplicação. Isso torna o código mais expressivo e facilita o tratamento centralizado de erros.</p>
        </div>
    </div>

    <div class="manual-section" id="logging-erros">
        <h3>8.3. Logging de Erros</h3>
        <p>O logging adequado de erros é essencial para monitoramento, debugging e auditoria da aplicação.</p>

        <div class="code-block-header">Exemplo de Logging de Erros</div>
<pre><code>namespace App\Services;

use App\Exceptions\BusinessRuleException;
use App\Repositories\PaymentRepository;
use Illuminate\Support\Facades\Log;

class PaymentService extends ServiceAbstract
{
    public function processPayment(array $data)
    {
        try {
            // Lógica de processamento de pagamento
            $payment = $this->repository->create($data);

            $result = $this->paymentGateway->charge($data['amount'], $data['payment_method']);

            if ($result['status'] === 'success') {
                $payment->status = 'completed';
                $payment->transaction_id = $result['transaction_id'];
                $payment->save();

                return $this->response->success('Payment processed successfully', $payment);
            } else {
                $payment->status = 'failed';
                $payment->error_message = $result['message'];
                $payment->save();

                Log::warning('Payment failed', [
                    'payment_id' => $payment->id,
                    'error' => $result['message'],
                    'gateway_response' => $result
                ]);

                throw new BusinessRuleException('Payment failed: ' . $result['message']);
            }
        } catch (\Exception $e) {
            if (!$e instanceof BusinessRuleException) {
                Log::error('Payment processing error', [
                    'exception' => get_class($e),
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'payment_data' => $data
                ]);
            }

            throw $e;
        }
    }
}
</code></pre>

        <p>Boas práticas para logging de erros:</p>
        <ul>
            <li>Use níveis de log apropriados (debug, info, warning, error, critical)</li>
            <li>Inclua informações contextuais relevantes nos logs</li>
            <li>Evite logar informações sensíveis (senhas, tokens, dados pessoais)</li>
            <li>Configure canais de log diferentes para diferentes tipos de erros</li>
            <li>Implemente um sistema de alerta para erros críticos</li>
        </ul>

        <div class="note">
            <p>Configure o Laravel para usar um serviço de logging externo como Papertrail, Loggly ou ELK Stack em ambientes de produção para facilitar o monitoramento e análise de logs.</p>
        </div>
    </div>

    <div class="manual-section" id="respostas-padronizadas">
    <h3>8.4. Respostas Padronizadas</h3>
    <p>Para garantir consistência nas respostas da API, a arquitetura define um formato padronizado para todas as respostas, tanto para sucesso quanto para erro.</p>

    <h4>8.4.1. Formato de Resposta de Sucesso</h4>
    <div class="code-block-header">Exemplo de Resposta de Sucesso</div>
<pre><code>{
    "success": true,
    "message": "Resource retrieved successfully",
    "data": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "created_at": "2023-06-15T10:30:00Z",
        "updated_at": "2023-06-15T10:30:00Z"
    }
}
</code></pre>

    <h4>8.4.2. Formato de Resposta de Erro</h4>
    <div class="code-block-header">Exemplo de Resposta de Erro</div>
<pre><code>{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "name": ["The name field is required."],
        "email": ["The email must be a valid email address."]
    }
}
</code></pre>

    <h4>8.4.3. Códigos de Status HTTP</h4>
    <p>A API utiliza códigos de status HTTP padrão para indicar o resultado da requisição:</p>
    <ul>
        <li><strong>200 OK</strong>: Requisição bem-sucedida</li>
        <li><strong>201 Created</strong>: Recurso criado com sucesso</li>
        <li><strong>204 No Content</strong>: Requisição bem-sucedida, sem conteúdo para retornar</li>
        <li><strong>400 Bad Request</strong>: Requisição inválida (erro de validação)</li>
        <li><strong>401 Unauthorized</strong>: Autenticação necessária</li>
        <li><strong>403 Forbidden</strong>: Sem permissão para acessar o recurso</li>
        <li><strong>404 Not Found</strong>: Recurso não encontrado</li>
        <li><strong>422 Unprocessable Entity</strong>: Erro de validação ou regra de negócio</li>
        <li><strong>429 Too Many Requests</strong>: Limite de requisições excedido</li>
        <li><strong>500 Internal Server Error</strong>: Erro interno do servidor</li>
    </ul>

    <div class="code-block-header">Implementação de Respostas Padronizadas</div>
<pre><code>namespace App\Http\Responses;

use Illuminate\Http\JsonResponse;

class ApiResponse
{
    /**
     * Return a success response.
     *
     * @param string $message
     * @param mixed $data
     * @param int $statusCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function success(string $message, $data = null, int $statusCode = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return a created response.
     *
     * @param string $message
     * @param mixed $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function created(string $message, $data = null): JsonResponse
    {
        return $this->success($message, $data, 201);
    }

    /**
     * Return a no content response.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function noContent(): JsonResponse
    {
        return response()->json(null, 204);
    }

    /**
     * Return an error response.
     *
     * @param string $message
     * @param mixed $errors
     * @param int $statusCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function error(string $message, $errors = null, int $statusCode = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return a not found response.
     *
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function notFound(string $message = 'Resource not found'): JsonResponse
    {
        return $this->error($message, null, 404);
    }

    /**
     * Return an unauthorized response.
     *
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function unauthorized(string $message = 'Unauthorized'): JsonResponse
    {
        return $this->error($message, null, 401);
    }

    /**
     * Return a forbidden response.
     *
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function forbidden(string $message = 'Forbidden'): JsonResponse
    {
        return $this->error($message, null, 403);
    }

    /**
     * Return a validation error response.
     *
     * @param string $message
     * @param mixed $errors
     * @return \Illuminate\Http\JsonResponse
     */
    public function validationError(string $message = 'Validation failed', $errors = null): JsonResponse
    {
        return $this->error($message, $errors, 422);
    }
}
</code></pre>

    <div class="tip">
        <p>Registre a classe ApiResponse como um singleton no service container para facilitar seu uso em toda a aplicação:</p>
        <pre><code>// app/Providers/AppServiceProvider.php
public function register()
{
    $this->app->singleton(ApiResponse::class, function () {
        return new ApiResponse();
    });
}</code></pre>
    </div>
</div>

<div class="manual-section" id="boas-praticas">
    <h2>9. Boas Práticas e Padrões</h2>
    <p>Esta seção descreve as boas práticas e padrões de design que devem ser seguidos no desenvolvimento da API.</p>

    <div class="manual-section" id="solid">
        <h3>9.1. Princípios SOLID</h3>
        <p>Os princípios SOLID são fundamentais para o desenvolvimento de software orientado a objetos de qualidade. A arquitetura da API é baseada nesses princípios:</p>

        <h4>9.1.1. Single Responsibility Principle (SRP)</h4>
        <p>Cada classe deve ter uma única responsabilidade, ou seja, deve haver apenas um motivo para alterá-la.</p>

        <div class="code-block-header">Exemplo de SRP</div>
<pre><code>// Bom: Classe com responsabilidade única
class UserRepository
{
    public function find(int $id)
    {
        return User::find($id);
    }

    public function create(array $data)
    {
        return User::create($data);
    }
}

// Bom: Outra classe com responsabilidade única
class UserPasswordService
{
    public function hashPassword(string $password): string
    {
        return Hash::make($password);
    }

    public function verifyPassword(string $password, string $hash): bool
    {
        return Hash::check($password, $hash);
    }
}

// Ruim: Classe com múltiplas responsabilidades
class UserManager
{
    public function find(int $id)
    {
        return User::find($id);
    }

    public function hashPassword(string $password): string
    {
        return Hash::make($password);
    }

    public function sendWelcomeEmail(User $user)
    {
        Mail::to($user)->send(new WelcomeEmail($user));
    }
}
</code></pre>

        <h4>9.1.2. Open/Closed Principle (OCP)</h4>
        <p>Entidades de software devem estar abertas para extensão, mas fechadas para modificação.</p>

        <div class="code-block-header">Exemplo de OCP</div>
<pre><code>// Interface que define o contrato
interface PaymentGateway
{
    public function processPayment(array $paymentData): array;
}

// Implementações concretas
class StripeGateway implements PaymentGateway
{
    public function processPayment(array $paymentData): array
    {
        // Lógica específica do Stripe
        return ['status' => 'success', 'transaction_id' => 'stripe_123'];
    }
}

class PayPalGateway implements PaymentGateway
{
    public function processPayment(array $paymentData): array
    {
        // Lógica específica do PayPal
        return ['status' => 'success', 'transaction_id' => 'paypal_456'];
    }
}

// Serviço que usa o gateway
class PaymentService
{
    protected $gateway;

    public function __construct(PaymentGateway $gateway)
    {
        $this->gateway = $gateway;
    }

    public function processPayment(array $paymentData): array
    {
        return $this->gateway->processPayment($paymentData);
    }
}

// Adicionar um novo gateway não requer modificação do PaymentService
</code></pre>

        <h4>9.1.3. Liskov Substitution Principle (LSP)</h4>
        <p>Objetos de uma classe derivada devem poder substituir objetos da classe base sem afetar a corretude do programa.</p>

        <div class="code-block-header">Exemplo de LSP</div>
<pre><code>// Classe base
abstract class FileStorage
{
    abstract public function save(string $filename, string $content): bool;
    abstract public function get(string $filename): ?string;
    abstract public function delete(string $filename): bool;
}

// Implementação para armazenamento local
class LocalFileStorage extends FileStorage
{
    protected $basePath;

    public function __construct(string $basePath)
    {
        $this->basePath = $basePath;
    }

    public function save(string $filename, string $content): bool
    {
        return file_put_contents($this->basePath . '/' . $filename, $content) !== false;
    }

    public function get(string $filename): ?string
    {
        $path = $this->basePath . '/' . $filename;
        return file_exists($path) ? file_get_contents($path) : null;
    }

    public function delete(string $filename): bool
    {
        $path = $this->basePath . '/' . $filename;
        return file_exists($path) ? unlink($path) : false;
    }
}

// Implementação para Amazon S3
class S3FileStorage extends FileStorage
{
    protected $s3Client;
    protected $bucket;

    public function __construct(S3Client $s3Client, string $bucket)
    {
        $this->s3Client = $s3Client;
        $this->bucket = $bucket;
    }

    public function save(string $filename, string $content): bool
    {
        try {
            $this->s3Client->putObject([
                'Bucket' => $this->bucket,
                'Key' => $filename,
                'Body' => $content
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function get(string $filename): ?string
    {
        try {
            $result = $this->s3Client->getObject([
                'Bucket' => $this->bucket,
                'Key' => $filename
            ]);
            return (string) $result['Body'];
        } catch (\Exception $e) {
            return null;
        }
    }

    public function delete(string $filename): bool
    {
        try {
            $this->s3Client->deleteObject([
                'Bucket' => $this->bucket,
                'Key' => $filename
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}

// Ambas as implementações podem ser usadas de forma intercambiável
</code></pre>

        <h4>9.1.4. Interface Segregation Principle (ISP)</h4>
        <p>Clientes não devem ser forçados a depender de interfaces que não utilizam.</p>

        <div class="code-block-header">Exemplo de ISP</div>
<pre><code>// Ruim: Interface muito grande
interface UserRepository
{
    public function find(int $id);
    public function create(array $data);
    public function update(int $id, array $data);
    public function delete(int $id);
    public function findByEmail(string $email);
    public function findByUsername(string $username);
    public function getActiveUsers();
    public function getAdmins();
    public function getUsersWithRole(string $role);
    public function syncRoles(int $userId, array $roleIds);
    public function attachPermissions(int $userId, array $permissionIds);
}

// Bom: Interfaces segregadas
interface UserRepository
{
    public function find(int $id);
    public function create(array $data);
    public function update(int $id, array $data);
    public function delete(int $id);
    public function findByEmail(string $email);
}

interface UserRoleRepository
{
    public function getUsersWithRole(string $role);
    public function syncRoles(int $userId, array $roleIds);
}

interface UserPermissionRepository
{
    public function attachPermissions(int $userId, array $permissionIds);
}

// Implementação que usa apenas as interfaces necessárias
class EloquentUserRepository implements UserRepository
{
    // Implementação dos métodos
}

class EloquentUserRoleRepository implements UserRoleRepository
{
    // Implementação dos métodos
}
</code></pre>

        <h4>9.1.5. Dependency Inversion Principle (DIP)</h4>
        <p>Módulos de alto nível não devem depender de módulos de baixo nível. Ambos devem depender de abstrações.</p>

        <div class="code-block-header">Exemplo de DIP</div>
<pre><code>// Abstração (interface)
interface NotificationService
{
    public function send(string $to, string $subject, string $content): bool;
}

// Implementação de baixo nível
class EmailNotificationService implements NotificationService
{
    public function send(string $to, string $subject, string $content): bool
    {
        // Lógica para enviar e-mail
        return Mail::to($to)->send(new GenericEmail($subject, $content)) !== null;
    }
}

class SmsNotificationService implements NotificationService
{
    public function send(string $to, string $subject, string $content): bool
    {
        // Lógica para enviar SMS
        return true;
    }
}

// Módulo de alto nível que depende da abstração
class UserRegistrationService
{
    protected $notificationService;

    // Injeção de dependência via construtor
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function register(array $userData): User
    {
        // Lógica para registrar o usuário
        $user = User::create($userData);

        // Enviar notificação usando a abstração
        $this->notificationService->send(
            $user->email,
            'Welcome to our platform',
            'Thank you for registering!'
        );

        return $user;
    }
}
</code></pre>

        <div class="note">
            <p>Seguir os princípios SOLID leva a um código mais modular, testável e manutenível. Esses princípios devem ser aplicados em toda a arquitetura da API.</p>
        </div>
    </div>

    <div class="manual-section" id="design-patterns">
        <h3>9.2. Design Patterns</h3>
        <p>A arquitetura utiliza diversos padrões de design para resolver problemas comuns de forma elegante e eficiente.</p>

        <h4>9.2.1. Repository Pattern</h4>
        <p>O Repository Pattern é utilizado para abstrair o acesso a dados, permitindo a substituição da fonte de dados sem afetar o restante da aplicação.</p>

        <div class="code-block-header">Exemplo de Repository Pattern</div>
<pre><code>// Interface do repositório
interface ProductRepositoryInterface
{
    public function all();
    public function find(int $id);
    public function create(array $data);
    public function update(int $id, array $data);
    public function delete(int $id);
    public function findByCategory(int $categoryId);
}

// Implementação usando Eloquent
class EloquentProductRepository implements ProductRepositoryInterface
{
    protected $model;

    public function __construct(Product $model)
    {
        $this->model = $model;
    }

    public function all()
    {
        return $this->model->all();
    }

    public function find(int $id)
    {
        return $this->model->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update(int $id, array $data)
    {
        $product = $this->find($id);
        if (!$product) {
            return false;
        }
        return $product->update($data);
    }

    public function delete(int $id)
    {
        $product = $this->find($id);
        if (!$product) {
            return false;
        }
        return $product->delete();
    }

    public function findByCategory(int $categoryId)
    {
        return $this->model->where('category_id', $categoryId)->get();
    }
}

// Registro no service container
class AppServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(
            ProductRepositoryInterface::class,
            EloquentProductRepository::class
        );
    }
}

// Uso no serviço
class ProductService
{
    protected $repository;

    public function __construct(ProductRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function getProductsByCategory(int $categoryId)
    {
        return $this->repository->findByCategory($categoryId);
    }
}
</code></pre>

        <h4>9.2.2. Factory Pattern</h4>
        <p>O Factory Pattern é utilizado para criar objetos sem expor a lógica de criação ao cliente.</p>

        <div class="code-block-header">Exemplo de Factory Pattern</div>
<pre><code>// Interface para os produtos
interface ExportableReport
{
    public function generate(): string;
    public function getFilename(): string;
}

// Implementações concretas
class PdfReport implements ExportableReport
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function generate(): string
    {
        // Lógica para gerar PDF
        return 'PDF content';
    }

    public function getFilename(): string
    {
        return 'report.pdf';
    }
}

class CsvReport implements ExportableReport
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function generate(): string
    {
        // Lógica para gerar CSV
        return 'CSV content';
    }

    public function getFilename(): string
    {
        return 'report.csv';
    }
}

// Factory para criar os relatórios
class ReportFactory
{
    public static function create(string $type, array $data): ExportableReport
    {
        switch ($type) {
            case 'pdf':
                return new PdfReport($data);
            case 'csv':
                return new CsvReport($data);
            default:
                throw new \InvalidArgumentException("Report type '{$type}' not supported");
        }
    }
}

// Uso da factory
class ReportController extends Controller
{
    public function download(Request $request)
    {
        $type = $request->input('type', 'pdf');
        $data = $this->getReportData();

        $report = ReportFactory::create($type, $data);
        $content = $report->generate();

        return response($content)
            ->header('Content-Type', $this->getContentType($type))
            ->header('Content-Disposition', 'attachment; filename="' . $report->getFilename() . '"');
    }

    private function getContentType(string $type): string
    {
        $types = [
            'pdf' => 'application/pdf',
            'csv' => 'text/csv',
        ];

        return $types[$type] ?? 'application/octet-stream';
    }

    private function getReportData(): array
    {
        // Lógica para obter os dados do relatório
        return [];
    }
}
</code></pre>

        <h4>9.2.3. Strategy Pattern</h4>
        <p>O Strategy Pattern permite definir uma família de algoritmos, encapsular cada um deles e torná-los intercambiáveis.</p>

        <div class="code-block-header">Exemplo de Strategy Pattern</div>
<pre><code>// Interface para as estratégias de desconto
interface DiscountStrategy
{
    public function calculate(float $amount): float;
}

// Implementações concretas
class PercentageDiscount implements DiscountStrategy
{
    protected $percentage;

    public function __construct(float $percentage)
    {
        $this->percentage = $percentage;
    }

    public function calculate(float $amount): float
    {
        return $amount * ($this->percentage / 100);
    }
}

class FixedDiscount implements DiscountStrategy
{
    protected $value;

    public function __construct(float $value)
    {
        $this->value = $value;
    }

    public function calculate(float $amount): float
    {
        return min($amount, $this->value);
    }
}

class NoDiscount implements DiscountStrategy
{
    public function calculate(float $amount): float
    {
        return 0;
    }
}

// Contexto que usa a estratégia
class OrderService
{
    public function calculateOrderTotal(Order $order, ?DiscountStrategy $discountStrategy = null): float
    {
        $subtotal = $order->items->sum(function ($item) {
            return $item->price * $item->quantity;
        });

        $discountStrategy = $discountStrategy ?? new NoDiscount();
        $discount = $discountStrategy->calculate($subtotal);

        return $subtotal - $discount;
    }
}

// Uso do padrão
class CheckoutController extends Controller
{
    protected $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function checkout(Request $request)
    {
        $order = Order::find($request->input('order_id'));
        $discountCode = $request->input('discount_code');

        $discountStrategy = $this->getDiscountStrategy($discountCode);
        $total = $this->orderService->calculateOrderTotal($order, $discountStrategy);

        // Continuar com o checkout
    }

    private function getDiscountStrategy(?string $discountCode): DiscountStrategy
    {
        if (!$discountCode) {
            return new NoDiscount();
        }

        $discount = DiscountCode::where('code', $discountCode)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->first();

        if (!$discount) {
            return new NoDiscount();
        }

        if ($discount->type === 'percentage') {
            return new PercentageDiscount($discount->value);
        }

        if ($discount->type === 'fixed') {
            return new FixedDiscount($discount->value);
        }

        return new NoDiscount();
    }
}
</code></pre>

        <h4>9.2.4. Observer Pattern</h4>
        <p>O Observer Pattern permite que objetos notifiquem outros objetos sobre mudanças em seu estado.</p>

        <div class="code-block-header">Exemplo de Observer Pattern com Laravel Events</div>
<pre><code>// Evento
class OrderShipped
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }
}

// Listeners
class SendShipmentNotification
{
    public function handle(OrderShipped $event)
    {
        $order = $event->order;

        // Enviar notificação ao cliente
        Notification::send(
            $order->customer,
            new OrderShippedNotification($order)
        );
    }
}

class UpdateInventory
{
    public function handle(OrderShipped $event)
    {
        $order = $event->order;

        // Atualizar inventário
        foreach ($order->items as $item) {
            $product = $item->product;
            $product->stock -= $item->quantity;
            $product->save();
        }
    }
}

// Registro dos listeners no EventServiceProvider
protected $listen = [
    OrderShipped::class => [
        SendShipmentNotification::class,
        UpdateInventory::class,
    ],
];

// Disparando o evento
class OrderService
{
    public function shipOrder(Order $order)
    {
        // Lógica para enviar o pedido
        $order->status = 'shipped';
        $order->shipped_at = now();
        $order->save();

        // Disparar evento
        event(new OrderShipped($order));

        return $order;
    }
}
</code></pre>

        <div class="tip">
            <p>Utilize os padrões de design de forma apropriada para resolver problemas específicos. Não force o uso de um padrão quando uma solução mais simples seria suficiente.</p>
        </div>
    </div>

    <div class="manual-section" id="clean-code">
        <h3>9.3. Clean Code</h3>
        <p>Escrever código limpo é essencial para a manutenibilidade e evolução do projeto. Seguem algumas diretrizes para escrever código limpo:</p>

        <h4>9.3.1. Nomenclatura Significativa</h4>
        <ul>
            <li>Use nomes descritivos para variáveis, funções, classes e métodos</li>
            <li>Evite abreviações ambíguas</li>
            <li>Seja consistente com a convenção de nomenclatura</li>
        </ul>

        <div class="code-block-header">Exemplo de Nomenclatura</div>
<pre><code>// Ruim
function calc($a, $b) {
    return $a * $b;
}

// Bom
function calculateProductPrice(float $unitPrice, int $quantity): float {
    return $unitPrice * $quantity;
}

// Ruim
$d = 5; // dias

// Bom
$daysToExpire = 5;
</code></pre>

        <h4>9.3.2. Funções Pequenas e Focadas</h4>
        <ul>
            <li>Funções devem fazer apenas uma coisa</li>
            <li>Funções devem ser pequenas e focadas</li>
            <li>Evite efeitos colaterais</li>
        </ul>

        <div class="code-block-header">Exemplo de Funções Focadas</div>
<pre><code>// Ruim
public function processOrder(Order $order)
{
    // Validar estoque
    foreach ($order->items as $item) {
        $product = Product::find($item->product_id);
        if ($product->stock < $item->quantity) {
            throw new \Exception("Insufficient stock for product: {$product->name}");
        }
    }

    // Calcular total
    $total = 0;
    foreach ($order->items as $item) {
        $product = Product::find($item->product_id);
        $total += $product->price * $item->quantity;
    }

    // Aplicar desconto
    if ($order->discount_code) {
        $discount = DiscountCode::where('code', $order->discount_code)->first();
        if ($discount && $discount->is_active) {
            $total -= $discount->value;
        }
    }

    // Processar pagamento
    $payment = Payment::create([
        'order_id' => $order->id,
        'amount' => $total,
        'method' => $order->payment_method,
    ]);

    // Atualizar estoque
    foreach ($order->items as $item) {
        $product = Product::find($item->product_id);
        $product->stock -= $item->quantity;
        $product->save();
    }

    // Atualizar status do pedido
    $order->status = 'paid';
    $order->save();

    return $order;
}

// Bom
public function processOrder(Order $order)
{
    $this->validateStock($order);
    $total = $this->calculateOrderTotal($order);
    $total = $this->applyDiscount($order, $total);
    $payment = $this->processPayment($order, $total);
    $this->updateStock($order);
    $this->updateOrderStatus($order, 'paid');

    return $order;
}

private function validateStock(Order $order)
{
    foreach ($order->items as $item) {
        $product = $this->productRepository->find($item->product_id);
        if ($product->stock < $item->quantity) {
            throw new InsufficientStockException($product);
        }
    }
}

private function calculateOrderTotal(Order $order): float
{
    return $order->items->sum(function ($item) {
        $product = $this->productRepository->find($item->product_id);
        return $product->price * $item->quantity;
    });
}

// ... outros métodos
</code></pre>

        <h4>9.3.3. Comentários Apropriados</h4>
<ul>
    <li>Escreva código autoexplicativo</li>
    <li>Use comentários para explicar o porquê, não o quê ou como</li>
    <li>Mantenha a documentação de API atualizada</li>
</ul>

<div class="code-block-header">Exemplo de Comentários</div>
<pre><code>// Ruim: Comentário que apenas repete o código
// Incrementa o contador
$counter++;

// Ruim: Comentário desnecessário
// Verifica se o usuário está autenticado
if (auth()->check()) {
    // ...
}

// Bom: Explica o porquê
// Incrementamos o contador duas vezes para compensar o processamento em lote
$counter += 2;

// Bom: Explica uma decisão de negócio
// Permitimos apenas 3 tentativas para evitar ataques de força bruta
if ($attempts > 3) {
    $this->lockAccount($user);
}

// Bom: Documenta um workaround
// FIXME: Estamos usando uma solução temporária devido a um bug no pacote XYZ
// Issue relacionada: https://github.com/xyz/package/issues/123
$result = $this->workaroundForXyzBug($data);
</code></pre>

<h4>9.3.4. Evitar Duplicação (DRY - Don't Repeat Yourself)</h4>
<ul>
    <li>Extraia código duplicado para métodos ou classes reutilizáveis</li>
    <li>Use traits para compartilhar funcionalidades entre classes</li>
    <li>Crie abstrações para padrões recorrentes</li>
</ul>

<div class="code-block-header">Exemplo de DRY</div>
<pre><code>// Ruim: Código duplicado
public function activateUser($userId)
{
    $user = User::find($userId);
    $user->is_active = true;
    $user->activated_at = now();
    $user->save();

    Log::info("User {$userId} activated");

    Mail::to($user)->send(new AccountActivated($user));
}

public function activateProduct($productId)
{
    $product = Product::find($productId);
    $product->is_active = true;
    $product->activated_at = now();
    $product->save();

    Log::info("Product {$productId} activated");

    // Notificar administradores
    $admins = User::where('is_admin', true)->get();
    Notification::send($admins, new ProductActivated($product));
}

// Bom: Extrair padrão comum
public function activateUser($userId)
{
    $user = User::find($userId);
    $this->activate($user);

    Mail::to($user)->send(new AccountActivated($user));
}

public function activateProduct($productId)
{
    $product = Product::find($productId);
    $this->activate($product);

    // Notificar administradores
    $admins = User::where('is_admin', true)->get();
    Notification::send($admins, new ProductActivated($product));
}

protected function activate($model)
{
    $model->is_active = true;
    $model->activated_at = now();
    $model->save();

    $type = class_basename($model);
    Log::info("{$type} {$model->id} activated");
}
</code></pre>

<h4>9.3.5. Princípio da Responsabilidade Única</h4>
<ul>
    <li>Cada classe deve ter uma única responsabilidade</li>
    <li>Cada método deve fazer apenas uma coisa</li>
    <li>Extraia métodos longos em métodos menores e focados</li>
</ul>

<div class="code-block-header">Exemplo de Métodos Focados</div>
<pre><code>// Ruim: Método com múltiplas responsabilidades
public function processOrder(Order $order)
{
    // Validar estoque
    foreach ($order->items as $item) {
        $product = Product::find($item->product_id);
        if ($product->stock < $item->quantity) {
            throw new \Exception("Insufficient stock for product: {$product->name}");
        }
    }

    // Calcular total
    $subtotal = 0;
    foreach ($order->items as $item) {
        $product = Product::find($item->product_id);
        $subtotal += $product->price * $item->quantity;
    }

    // Aplicar impostos
    $taxRate = 0.1; // 10%
    $taxes = $subtotal * $taxRate;
    $total = $subtotal + $taxes;

    // Processar pagamento
    $paymentGateway = new PaymentGateway();
    $paymentResult = $paymentGateway->charge([
        'amount' => $total,
        'card_token' => $order->payment_token,
        'description' => "Order #{$order->id}"
    ]);

    if ($paymentResult['status'] !== 'success') {
        throw new \Exception("Payment failed: {$paymentResult['message']}");
    }

    // Atualizar estoque
    foreach ($order->items as $item) {
        $product = Product::find($item->product_id);
        $product->stock -= $item->quantity;
        $product->save();
    }

    // Atualizar pedido
    $order->status = 'paid';
    $order->total = $total;
    $order->taxes = $taxes;
    $order->transaction_id = $paymentResult['transaction_id'];
    $order->save();

    // Enviar e-mail
    Mail::to($order->customer)->send(new OrderConfirmation($order));

    return $order;
}

// Bom: Métodos focados
public function processOrder(Order $order)
{
    $this->validateStock($order);

    $subtotal = $this->calculateSubtotal($order);
    $taxes = $this->calculateTaxes($subtotal);
    $total = $subtotal + $taxes;

    $transactionId = $this->processPayment($order, $total);

    $this->updateStock($order);
    $this->updateOrderStatus($order, $total, $taxes, $transactionId);
    $this->sendConfirmationEmail($order);

    return $order;
}

protected function validateStock(Order $order)
{
    foreach ($order->items as $item) {
        $product = $this->productRepository->find($item->product_id);
        if ($product->stock < $item->quantity) {
            throw new InsufficientStockException($product);
        }
    }
}

protected function calculateSubtotal(Order $order): float
{
    $subtotal = 0;
    foreach ($order->items as $item) {
        $product = $this->productRepository->find($item->product_id);
        $subtotal += $product->price * $item->quantity;
    }
    return $subtotal;
}

// ... outros métodos focados
</code></pre>

<h4>9.3.6. Convenções de Nomenclatura</h4>
<p>Seguir convenções de nomenclatura consistentes torna o código mais legível e previsível.</p>

<div class="code-block-header">Convenções de Nomenclatura no Laravel</div>
<pre><code>// Classes: PascalCase
class UserController {}
class PaymentService {}

// Interfaces: PascalCase com sufixo Interface ou sem sufixo
interface UserRepositoryInterface {}
interface PaymentGateway {}

// Traits: PascalCase
trait Notifiable {}
trait HasUuid {}

// Métodos e propriedades: camelCase
public function getUserById($id) {}
protected $paymentGateway;

// Constantes: UPPER_CASE
const MAX_LOGIN_ATTEMPTS = 5;
const API_VERSION = '1.0';

// Variáveis: camelCase
$userCount = User::count();
$isActive = true;

// Nomes de arquivos de configuração: snake_case
// config/app.php
// config/database.php

// Nomes de tabelas: snake_case, plural
// users
// order_items

// Nomes de colunas: snake_case
// first_name
// is_active

// Nomes de rotas: kebab-case
// /api/user-profiles
// /admin/sales-reports
</code></pre>

<div class="tip">
    <p>Mantenha a consistência com as convenções do Laravel. Quando em dúvida, siga o estilo usado no próprio framework.</p>
</div>

<h4>9.3.7. Princípio do Menor Privilégio</h4>
<ul>
    <li>Use o modificador de acesso mais restritivo possível (private, protected, public)</li>
    <li>Exponha apenas o que é necessário para a API pública da classe</li>
    <li>Mantenha o estado interno da classe encapsulado</li>
</ul>

<div class="code-block-header">Exemplo de Encapsulamento</div>
<pre><code>// Bom: Encapsulamento adequado
class User
{
    private $password;

    public function setPassword(string $plainPassword): void
    {
        $this->validatePassword($plainPassword);
        $this->password = $this->hashPassword($plainPassword);
    }

    public function verifyPassword(string $plainPassword): bool
    {
        return Hash::check($plainPassword, $this->password);
    }

    private function validatePassword(string $password): void
    {
        if (strlen($password) < 8) {
            throw new \InvalidArgumentException('Password must be at least 8 characters');
        }
    }

    private function hashPassword(string $password): string
    {
        return Hash::make($password);
    }
}
</code></pre>
</div>

<div class="manual-section" id="conclusao">
    <h2>10. Conclusão</h2>
    <p>Este manual apresentou a arquitetura de referência para desenvolvimento de APIs RESTful utilizando Laravel 12 e PHP 8.2. A arquitetura foi projetada para atender aos requisitos de escalabilidade, manutenibilidade, testabilidade e segurança, seguindo os princípios SOLID e padrões de design modernos.</p>

    <p>Os principais componentes e conceitos abordados incluem:</p>
    <ul>
        <li>Arquitetura em camadas (Apresentação, Aplicação, Domínio e Infraestrutura)</li>
        <li>Componentes base abstratos para Controllers, Services, Repositories e Responses</li>
        <li>Fluxo de requisição e processamento</li>
        <li>Autenticação e autorização com JWT</li>
        <li>Validação e tratamento de dados</li>
        <li>Tratamento de erros e exceções</li>
        <li>Boas práticas e padrões de design</li>
    </ul>

    <p>A adoção desta arquitetura proporciona diversos benefícios:</p>
    <ul>
        <li><strong>Separação de Responsabilidades</strong>: Cada componente tem uma responsabilidade bem definida, facilitando a manutenção e evolução do sistema.</li>
        <li><strong>Testabilidade</strong>: A arquitetura foi projetada para facilitar a escrita de testes unitários, de integração e funcionais.</li>
        <li><strong>Escalabilidade</strong>: A separação em camadas permite escalar componentes específicos conforme necessário.</li>
        <li><strong>Manutenibilidade</strong>: Código organizado e padronizado é mais fácil de manter e evoluir.</li>
        <li><strong>Consistência</strong>: Padrões e convenções consistentes facilitam o onboarding de novos desenvolvedores e a colaboração entre equipes.</li>
    </ul>

    <p>Ao seguir as diretrizes e padrões descritos neste manual, os desenvolvedores poderão construir APIs robustas, escaláveis e de alta qualidade, que atendam às necessidades do negócio e proporcionem uma excelente experiência para os consumidores da API.</p>

    <div class="note">
        <p>Este manual é um documento vivo e será atualizado conforme a arquitetura evolui e novas práticas são adotadas. Sugestões de melhorias são sempre bem-vindas.</p>
    </div>

    <div class="manual-footer">
        <p><strong>Versão:</strong> 1.0.0 | <strong>Última atualização:</strong> Junho 2023 | <strong>Autor:</strong> Equipe de Arquitetura</p>
    </div>
</div>

</div></div></div></div></div></div>
<script>
    mermaid.initialize({ startOnLoad: true });
    mermaid.initialize({
        startOnLoad: true,
        theme: 'default',
        securityLevel: 'loose',
        flowchart: {
            htmlLabels: true,
            curve: 'basis'
        },
        fontSize: 18
    });
</script>
</body></html>
