/**
 * Debugbar Injector
 * 
 * <PERSON>ste script injeta a debugbar diretamente na página.
 */
(function() {
    // Verificar se estamos em uma página HTML
    if (document.body) {
        // Fazer uma requisição para obter a debugbar
        fetch('/_debugbar/open?max=20')
            .then(response => response.text())
            .then(html => {
                // Criar um elemento temporário para analisar o HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                
                // Extrair o conteúdo da debugbar
                const debugbarContent = tempDiv.querySelector('.phpdebugbar');
                
                if (debugbarContent) {
                    // Adicionar a debugbar ao final do body
                    document.body.appendChild(debugbarContent);
                    
                    // Adicionar os estilos da debugbar
                    const styles = tempDiv.querySelectorAll('style, link[rel="stylesheet"]');
                    styles.forEach(style => {
                        document.head.appendChild(style.cloneNode(true));
                    });
                    
                    // Adicionar os scripts da debugbar
                    const scripts = tempDiv.querySelectorAll('script');
                    scripts.forEach(script => {
                        const newScript = document.createElement('script');
                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }
                        document.body.appendChild(newScript);
                    });
                    
                    console.log('Debugbar injetada com sucesso!');
                } else {
                    console.error('Não foi possível encontrar a debugbar no HTML retornado.');
                }
            })
            .catch(error => {
                console.error('Erro ao carregar a debugbar:', error);
            });
    }
})();
