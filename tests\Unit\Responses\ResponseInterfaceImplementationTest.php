<?php

namespace Tests\Unit\Responses;

use App\Responses\ResponseInterface;
use Illuminate\Http\JsonResponse;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ResponseInterfaceImplementationTest extends TestCase
{
    protected $response;

    protected function setUp(): void
    {
        parent::setUp();
        $this->response = app(ResponseInterface::class);
    }

    #[Test]
    public function it_returns_json_response_for_ok()
    {
        $result = $this->response->ok(['data' => 'test']);
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(200, $result->getStatusCode());
    }

    #[Test]
    public function it_returns_json_response_for_too_many_requests()
    {
        $result = $this->response->tooManyRequests('Too many requests');
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(429, $result->getStatusCode());
    }

    // Testes para todos os outros métodos da interface
}
