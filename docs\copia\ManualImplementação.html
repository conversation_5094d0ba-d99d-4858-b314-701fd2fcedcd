<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Implementação</title>
    <link rel="stylesheet" href="css/manual.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Implementação</h1>
        <p>Guia prático para implementação do sistema seguindo os padrões da arquitetura</p>
        <div class="version">Laravel 12</div>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#ambiente">Ambiente</a></li>
            <li><a href="#estrutura">Estrutura</a></li>
            <li><a href="#implementacao-camadas">Implementação</a></li>
            <li><a href="#padroes">Padrões</a></li>
            <li><a href="#excecoes">Exceções</a></li>
            <li><a href="#testes">Testes</a></li>
            <li><a href="#validacao">Validação</a></li>
            <li><a href="#versionamento">Versionamento</a></li>
            <li><a href="#boas-praticas">Boas Práticas</a></li>
            <li><a href="#laravel12">Laravel 12</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#ambiente">2. Configuração do Ambiente de Desenvolvimento</a></li>
            <li><a href="#estrutura">3. Estrutura do Projeto</a></li>
            <li><a href="#implementacao-camadas">4. Guias de Implementação por Camada</a>
                <ul>
                    <li><a href="#camada-apresentacao">4.1. Camada de Apresentação</a></li>
                    <li><a href="#camada-aplicacao">4.2. Camada de Aplicação</a></li>
                    <li><a href="#camada-dominio">4.3. Camada de Domínio</a></li>
                    <li><a href="#camada-infraestrutura">4.4. Camada de Infraestrutura</a></li>
                </ul>
            </li>
            <li><a href="#padroes">5. Padrões de Codificação</a></li>
            <li><a href="#excecoes">6. Tratamento de Exceções</a></li>
            <li><a href="#testes">7. Testes</a></li>
            <li><a href="#validacao">8. Validação de Dados</a></li>
            <li><a href="#versionamento">9. Controle de Versão</a></li>
            <li><a href="#boas-praticas">10. Boas Práticas</a></li>
            <li><a href="#laravel12">11. Novidades do Laravel 12</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <p class="intro-text">Este manual fornece diretrizes práticas para implementação do sistema seguindo a
            arquitetura em camadas
            definida. Destina-se a desenvolvedores que estão trabalhando no projeto e busca garantir consistência,
            qualidade e manutenibilidade do código.</p>

        <p>Os exemplos e recomendações deste manual são baseados nas melhores práticas de desenvolvimento com Laravel 12
            e seguem os princípios arquiteturais descritos no Manual de Arquitetura.</p>

        <div class="key-points">
            <p><strong>Nota:</strong> Este documento pressupõe familiaridade com os conceitos apresentados no Manual de
                Arquitetura. Recomenda-se a leitura prévia daquele documento antes de prosseguir com este guia de
                implementação.</p>
        </div>
    </section>

    <section id="ambiente" class="manual-section">
        <h2>2. Configuração do Ambiente de Desenvolvimento</h2>

        <div class="subsection">
            <h3>2.1. Requisitos de Sistema</h3>
            <ul>
                <li>PHP 8.2 ou superior (recomendado PHP 8.3+)</li>
                <li>Composer 2.6 ou superior</li>
                <li>MySQL 8.0 ou superior / PostgreSQL 15 ou superior</li>
                <li>Node.js 20 LTS ou superior (para assets)</li>
                <li>Git 2.30 ou superior</li>
                <li>Redis 7.0 ou superior (para cache e filas)</li>
            </ul>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 requer PHP 8.2+ (recomendado PHP 8.3) e Node.js 20 LTS.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>2.2. Instalação do Projeto</h3>
            <div class="code-block">
                <pre><code># Clone o repositório
git clone https://github.com/empresa/projeto.git
cd projeto

# Instale as dependências PHP
composer install

# Copie o arquivo de ambiente
cp .env.example .env

# Gere a chave da aplicação
php artisan key:generate

# Configure o banco de dados no arquivo .env
# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=nome_do_banco
# DB_USERNAME=usuario
# DB_PASSWORD=senha

# Execute as migrações
php artisan migrate

# Instale as dependências JavaScript
npm install

# Compile os assets
npm run dev</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>2.3. Ferramentas Recomendadas</h3>
            <div class="tools-list">
                <ul>
                    <li>
                        <strong>IDE:</strong>
                        <p>PhpStorm, VS Code com extensões PHP</p>
                    </li>
                    <li>
                        <strong>Debugging:</strong>
                        <p>Xdebug 3.0+</p>
                    </li>
                    <li>
                        <strong>API Testing:</strong>
                        <p>Postman, Insomnia</p>
                    </li>
                    <li>
                        <strong>Controle de Qualidade:</strong>
                        <p>PHPStan, PHP CS Fixer, Laravel Pint</p>
                    </li>
                </ul>
            </div>
        </div>

        <div class="subsection">
            <h3>2.4. Docker (Opcional)</h3>
            <p>O projeto oferece suporte para desenvolvimento com Docker através do Laravel Sail:</p>
            <div class="code-block">
                <pre><code># Inicializar os contêineres de desenvolvimento
./vendor/bin/sail up -d

# Executar comandos dentro do contêiner
./vendor/bin/sail artisan migrate
./vendor/bin/sail composer install
./vendor/bin/sail test</code></pre>
            </div>
        </div>
    </section>

    <section id="estrutura" class="manual-section">
        <h2>3. Estrutura do Projeto</h2>

        <p>Além da estrutura padrão do Laravel, nosso projeto utiliza diretórios adicionais para acomodar a arquitetura
            em camadas:</p>

        <div class="code-block">
            <pre><code>projeto/
├── app/ # Código da aplicação
│ ├── Console/ # Comandos artisan
│ ├── Contracts/ # Interfaces
│ ├── DTOs/ # Data Transfer Objects
│ ├── Exceptions/ # Exceções customizadas
│ ├── Http/ # Camada de Apresentação
│ │ ├── Controllers/ # Controllers
│ │ ├── Middleware/ # Middlewares
│ │ ├── Requests/ # Form Requests
│ │ └── Resources/ # API Resources
│ ├── Models/ # Modelos Eloquent
│ ├── Repositories/ # Repositórios
│ │ ├── Contracts/ # Interfaces de repositórios
│ │ └── Eloquent/ # Implementações Eloquent
│ ├── Services/ # Serviços de aplicação
│ │ ├── External/ # Serviços externos
│ │ └── Internal/ # Serviços internos
│ ├── ValueObjects/ # Objetos de valor
│ └── Providers/ # Service providers
├── bootstrap/ # Arquivos de inicialização
├── config/ # Configurações
├── database/ # Migrations, seeders
├── public/ # Assets públicos
├── resources/ # Views, assets não compilados
├── routes/ # Definição de rotas
├── storage/ # Armazenamento (logs, cache)
└── tests/ # Testes
    ├── Feature/ # Testes de feature
    └── Unit/ # Testes unitários</code></pre>
        </div>

        <div class="alerts-section">
            <h4>Atualização Laravel 12</h4>
            <p>O Laravel 12 simplificou a estrutura de diretórios de testes, consolidando os testes em Feature e Unit. A
                pasta bootstrap também ganhou maior importância no processo de inicialização da aplicação.</p>
        </div>
    </section>

    <section id="implementacao-camadas" class="manual-section">
        <h2>4. Guias de Implementação por Camada</h2>

        <section id="camada-apresentacao" class="subsection">
            <h3>4.1. Camada de Apresentação</h3>

            <h4>4.1.1. Controllers</h4>
            <p>Os controllers devem ser enxutos, delegando a lógica de negócios para os services:</p>

            <div class="best-practice">
                <h4>Boa Prática</h4>
                <pre><code>namespace App\Http\Controllers;

use App\Http\Requests\ProductRequest;
use App\Http\Resources\ProductResource;
use App\Services\ProductService;

class ProductController extends Controller
{
    protected $productService;

    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }

    public function store(ProductRequest $request)
    {
        $product = $this->productService->create($request->validated());

        return response()->json([
            'status' => 'success',
            'message' => 'Produto criado com sucesso',
            'data' => new ProductResource($product)
        ], 201);
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Má Prática</h4>
                <pre><code>namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
        ]);

        // Lógica de negócio no controller ❌
        $existingProduct = Product::where('name', $request->name)->first();
        if ($existingProduct) {
            return response()->json(['error' => 'Produto já existe'], 409);
        }

        $product = new Product();
        $product->name = $request->name;
        $product->price = $request->price;
        $product->save();

        // Sem padronização de resposta ❌
        return response()->json($product, 201);
    }
}</code></pre>
            </div>

            <h4>4.1.2. Form Requests</h4>
            <p>Utilize Form Requests para validação de dados de entrada:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id'
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'O nome do produto é obrigatório',
            'price.numeric' => 'O preço deve ser um valor numérico',
            'category_id.exists' => 'A categoria selecionada não existe'
        ];
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 reforça o uso de tipos de retorno explícitos em todos os métodos, como mostrado acima
                    com <code>bool</code> e <code>array</code>.</p>
            </div>

            <h4>4.1.3. API Resources</h4>
            <p>Use API Resources para transformar modelos em respostas JSON:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;

class ProductResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'price' => (float) $this->price,
            'formatted_price' => 'R$ ' . number_format($this->price, 2, ',', '.'),
            'category' => new CategoryResource($this->whenLoaded('category')),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O método <code>toArray</code> agora recebe explicitamente o objeto <code>Request</code> e deve
                    retornar um <code>array</code>.</p>
            </div>

            <h4>4.1.4. Middlewares</h4>
            <p>Implemente middlewares para requisitos transversais como autenticação, autorização e logging:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class LogApiRequests
{
    public function handle(Request $request, Closure $next): Response
    {
        // Registrar início da requisição
        $startTime = microtime(true);

        // Continuar o processamento
        $response = $next($request);

        // Calcular tempo de execução
        $executionTime = microtime(true) - $startTime;

        // Registrar informações da requisição
        logger()->info('API Request', [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'status' => $response->status(),
            'execution_time' => round($executionTime * 1000, 2) . 'ms',
            'user_id' => auth()->id() ?? 'guest'
        ]);

        return $response;
    }
}</code></pre>
            </div>
        </section>

        <section id="camada-aplicacao" class="subsection">
            <h3>4.2. Camada de Aplicação</h3>

            <h4>4.2.1. Services</h4>
            <p>Os services implementam a lógica de negócio e orquestram operações entre repositórios:</p>

            <div class="code-block">
                <pre><code>namespace App\Services;

use App\DTOs\ProductDTO;
use App\Exceptions\ProductException;
use App\Repositories\Contracts\ProductRepositoryInterface;
use App\Repositories\Contracts\CategoryRepositoryInterface;
use Illuminate\Support\Facades\DB;

class ProductService
{
    protected $productRepository;
    protected $categoryRepository;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        CategoryRepositoryInterface $categoryRepository
    ) {
        $this->productRepository = $productRepository;
        $this->categoryRepository = $categoryRepository;
    }

    public function create(array $data)
    {
        // Verificar se produto já existe
        if ($this->productRepository->findByName($data['name'])) {
            throw new ProductException('Um produto com este nome já existe');
        }

        // Verificar se categoria existe
        if (!$this->categoryRepository->find($data['category_id'])) {
            throw new ProductException('Categoria não encontrada');
        }

        return DB::transaction(function () use ($data) {
            $product = $this->productRepository->create($data);

            // Lógica adicional, como criar histórico, notificar, etc.

            return $product;
        });
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 introduziu uma API mais limpa para transações de banco de dados, como mostrado acima com
                    <code>DB::transaction()</code> que agora retorna diretamente o resultado da closure.
                </p>
            </div>

            <h4>4.2.2. DTOs (Data Transfer Objects)</h4>
            <p>Use DTOs para transferir dados estruturados entre camadas:</p>

            <div class="code-block">
                <pre><code>namespace App\DTOs;

readonly class ProductDTO
{
    public function __construct(
        public ?int $id = null,
        public ?string $name = null,
        public ?string $description = null,
        public ?float $price = null,
        public ?int $categoryId = null
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            $data['id'] ?? null,
            $data['name'] ?? null,
            $data['description'] ?? null,
            $data['price'] ?? null,
            $data['category_id'] ?? null
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'category_id' => $this->categoryId
        ];
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 incentiva o uso de classes readonly e constructor property promotion do PHP 8.1+,
                    tornando os DTOs mais concisos e imutáveis.</p>
            </div>

            <h4>4.2.3. Validação a Nível de Serviço</h4>
            <p>Implemente validação de regras de negócio nos services:</p>

            <div class="code-block">
                <pre><code>namespace App\Services;

use App\Exceptions\OrderException;

class OrderService
{
    // ...

    public function processPayment(int $orderId, array $paymentData): array
    {
        $order = $this->orderRepository->find($orderId);

        if (!$order) {
            throw new OrderException('Pedido não encontrado');
        }

        if ($order->status !== 'pending') {
            throw new OrderException('Apenas pedidos pendentes podem receber pagamentos');
        }

        if ($paymentData['amount'] < $order->total) {
            throw new OrderException('Valor do pagamento inferior ao total do pedido');
        }

        // Continuar com processamento do pagamento...
        return ['transaction_id' => '...', 'status' => 'approved'];
    }
}</code></pre>
            </div>
        </section>

        <section id="camada-dominio" class="subsection">
            <h3>4.3. Camada de Domínio</h3>

            <h4>4.3.1. Models</h4>
            <p>Os models representam as entidades de negócio e suas relações:</p>

            <div class="code-block">
                <pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'price',
        'category_id',
        'sku',
        'is_active'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Relacionamentos
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    // Escopos
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory(Builder $query, int $categoryId): Builder
    {
        return $query->where('category_id', $categoryId);
    }

    // Accessors
    protected function formattedPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => 'R$ ' . number_format($this->price, 2, ',', '.')
        );
    }

    // Métodos de negócio
    public function isInStock(): bool
    {
        return $this->stock_quantity > 0;
    }

    public function canBePurchased(int $quantity = 1): bool
    {
        return $this->is_active && $this->stock_quantity >= $quantity;
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 reforça o uso de tipos de retorno explícitos para relacionamentos
                    (<code>BelongsTo</code>, <code>HasMany</code>, etc.) e escopos (<code>Builder</code>). Os accessors
                    e mutators agora usam a API <code>Attribute</code> em vez da convenção de nomenclatura antiga.</p>
            </div>

            <h4>4.3.2. Value Objects</h4>
            <p>Implemente Value Objects para encapsular conceitos do domínio que são identificados por seus valores:</p>

            <div class="code-block">
                <pre><code>namespace App\ValueObjects;

readonly class Money
{
    public function __construct(
        private float $amount,
        private string $currency = 'BRL'
    ) {
        if ($amount < 0) {
            throw new \InvalidArgumentException('Valor não pode ser negativo');
        }
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function add(Money $money): Money
    {
        if ($money->getCurrency() !== $this->currency) {
            throw new \InvalidArgumentException('Moedas incompatíveis');
        }

        return new Money($this->amount + $money->getAmount(), $this->currency);
    }

    public function subtract(Money $money): Money
    {
        if ($money->getCurrency() !== $this->currency) {
            throw new \InvalidArgumentException('Moedas incompatíveis');
        }

        $newAmount = $this->amount - $money->getAmount();
        if ($newAmount < 0) {
            throw new \InvalidArgumentException('Resultado não pode ser negativo');
        }
        return new Money($newAmount, $this->currency);
    }

    public function format(): string
    {
        $symbols = [
            'BRL' => 'R$',
            'USD' => '$',
            'EUR' => '€'
        ];

        $symbol = $symbols[$this->currency] ?? '';
        return $symbol . ' ' . number_format($this->amount, 2, ',', '.');
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 incentiva o uso de classes <code>readonly</code> do PHP 8.2+ para Value Objects,
                    garantindo imutabilidade.</p>
            </div>

            <h4>4.3.3. Enums</h4>
            <p>Utilize Enums (PHP 8.1+) para representar conjuntos fixos de valores:</p>

            <div class="code-block">
                <pre><code>namespace App\Enums;

enum OrderStatus: string
{
    case PENDING = 'pending';
    case PAID = 'paid';
    case PROCESSING = 'processing';
    case SHIPPED = 'shipped';
    case DELIVERED = 'delivered';
    case CANCELED = 'canceled';
    case REFUNDED = 'refunded';

    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pendente',
            self::PAID => 'Pago',
            self::PROCESSING => 'Em processamento',
            self::SHIPPED => 'Enviado',
            self::DELIVERED => 'Entregue',
            self::CANCELED => 'Cancelado',
            self::REFUNDED => 'Reembolsado',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::PENDING => 'yellow',
            self::PAID => 'blue',
            self::PROCESSING => 'purple',
            self::SHIPPED => 'indigo',
            self::DELIVERED => 'green',
            self::CANCELED => 'red',
            self::REFUNDED => 'gray',
        };
    }

    public function canTransitionTo(self $status): bool
    {
        return match($this) {
            self::PENDING => in_array($status, [self::PAID, self::CANCELED]),
            self::PAID => in_array($status, [self::PROCESSING, self::REFUNDED]),
            self::PROCESSING => in_array($status, [self::SHIPPED, self::CANCELED]),
            self::SHIPPED => in_array($status, [self::DELIVERED]),
            self::DELIVERED => in_array($status, [self::REFUNDED]),
            self::CANCELED, self::REFUNDED => false,
        };
    }
}</code></pre>
            </div>
        </section>

        <section id="camada-infraestrutura" class="subsection">
            <h3>4.4. Camada de Infraestrutura</h3>

            <h4>4.4.1. Repositories</h4>
            <p>Implemente repositórios para abstrair o acesso aos dados:</p>

            <h5>Interface do Repositório</h5>
            <div class="code-block">
                <pre><code>namespace App\Repositories\Contracts;

interface ProductRepositoryInterface
{
    public function all();
    public function find(int $id);
    public function findByName(string $name);
    public function create(array $data);
    public function update(int $id, array $data);
    public function delete(int $id);
    public function findByCategory(int $categoryId);
    public function search(array $params);
}</code></pre>
            </div>
            <h5>Implementação do Repositório</h5>
            <div class="code-block">
                <pre><code>namespace App\Repositories\Eloquent;

use App\Models\Product;
use App\Repositories\Contracts\ProductRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class ProductRepository implements ProductRepositoryInterface
{
    protected $model;

    public function __construct(Product $model)
    {
        $this->model = $model;
    }

    public function all(): Collection
    {
        return $this->model->all();
    }

    public function find(int $id): ?Product
    {
        return Cache::remember("product:{$id}", 3600, function () use ($id) {
            return $this->model->find($id);
        });
    }

    public function findByName(string $name): ?Product
    {
        return $this->model->where('name', $name)->first();
    }

    public function create(array $data): Product
    {
        $product = $this->model->create($data);
        $this->clearCache($product->id);
        return $product;
    }

    public function update(int $id, array $data): ?Product
    {
        $product = $this->model->find($id);
        if (!$product) {
            return null;
        }

        $product->update($data);
        $this->clearCache($id);
        return $product;
    }

    public function delete(int $id): bool
    {
        $this->clearCache($id);
        return (bool) $this->model->destroy($id);
    }

    public function findByCategory(int $categoryId): Collection
    {
        return $this->model->where('category_id', $categoryId)->get();
    }

    public function search(array $params): LengthAwarePaginator
    {
        $query = $this->model->query();

        if (isset($params['name'])) {
            $query->where('name', 'like', "%{$params['name']}%");
        }

        if (isset($params['category_id'])) {
            $query->where('category_id', $params['category_id']);
        }

        if (isset($params['min_price'])) {
            $query->where('price', '>=', $params['min_price']);
        }

        if (isset($params['max_price'])) {
            $query->where('price', '<=', $params['max_price']);
        }

        if (isset($params['active'])) {
            $query->where('is_active', (bool) $params['active']);
        }

        $sortField = $params['sort_by'] ?? 'created_at';
        $sortDirection = $params['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $perPage = $params['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    protected function clearCache(int $id): void
    {
        Cache::forget("product:{$id}");
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 reforça o uso de tipos de retorno explícitos (<code>Collection</code>,
                    <code>?Product</code>, <code>bool</code>, etc.) e introduz melhorias no sistema de cache com tempos
                    de expiração mais precisos.
                </p>
            </div>

            <h4>4.4.2. Serviços Externos</h4>
            <p>Implemente adaptadores para serviços externos, isolando a lógica de integração:</p>

            <div class="code-block">
                <pre><code>namespace App\Services\External;

use App\Contracts\PaymentGatewayInterface;
use App\DTOs\PaymentDTO;
use App\Exceptions\PaymentException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class StripePaymentService implements PaymentGatewayInterface
{
    protected $apiKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.stripe.secret');
        $this->baseUrl = config('services.stripe.base_url');
    }

    public function processPayment(PaymentDTO $payment): array
    {
        try {
            $response = Http::withToken($this->apiKey)
                ->post("{$this->baseUrl}/v1/charges", [
                    'amount' => $payment->amount * 100, // Stripe usa centavos
                    'currency' => strtolower($payment->currency),
                    'source' => $payment->token,
                    'description' => $payment->description,
                    'metadata' => [
                        'order_id' => $payment->orderId,
                        'customer_email' => $payment->customerEmail
                    ]
                ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'transaction_id' => $response->json('id'),
                    'status' => $response->json('status'),
                    'amount' => $response->json('amount') / 100,
                    'currency' => $response->json('currency'),
                    'created_at' => date('Y-m-d H:i:s', $response->json('created'))
                ];
            }

            throw new PaymentException(
                $response->json('error.message') ?? 'Erro desconhecido no gateway de pagamento',
                $response->status()
            );
        } catch (\Exception $e) {
            Log::error('Erro no processamento do pagamento Stripe', [
                'error' => $e->getMessage(),
                'payment' => [
                    'amount' => $payment->amount,
                    'currency' => $payment->currency,
                    'order_id' => $payment->orderId
                ]
            ]);

            throw new PaymentException(
                'Falha ao processar pagamento: ' . $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    public function refundPayment(string $transactionId, ?float $amount = null): array
    {
        try {
            $data = ['charge' => $transactionId];
            
            if ($amount !== null) {
                $data['amount'] = $amount * 100;
            }

            $response = Http::withToken($this->apiKey)
                ->post("{$this->baseUrl}/v1/refunds", $data);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'refund_id' => $response->json('id'),
                    'status' => $response->json('status'),
                    'amount' => $response->json('amount') / 100,
                    'currency' => $response->json('currency')
                ];
            }

            throw new PaymentException(
                $response->json('error.message') ?? 'Erro desconhecido no reembolso',
                $response->status()
            );
        } catch (\Exception $e) {
            Log::error('Erro no processamento do reembolso Stripe', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);

            throw new PaymentException(
                'Falha ao processar reembolso: ' . $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 aprimorou o cliente HTTP com melhor suporte para tipagem e tratamento de respostas. O
                    uso de DTOs fortemente tipados também é incentivado para transferência de dados entre camadas.</p>
            </div>

            <h4>4.4.3. Jobs e Filas</h4>
            <p>Utilize jobs para processar tarefas em segundo plano:</p>

            <div class="code-block">
                <pre><code>namespace App\Jobs;

use App\Models\Order;
use App\Services\NotificationService;
use App\Services\PDFGeneratorService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class GenerateAndSendInvoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Order $order;
    public int $tries = 3;
    public int $backoff = 60;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    public function handle(PDFGeneratorService $pdfService, NotificationService $notificationService): void
    {
        // Gerar PDF da fatura
        $pdfContent = $pdfService->generateInvoice($this->order);
        
        // Salvar PDF no storage
        $filename = "invoices/{$this->order->id}-invoice.pdf";
        Storage::disk('private')->put($filename, $pdfContent);
        
        // Atualizar ordem com o caminho do arquivo
        $this->order->update([
            'invoice_path' => $filename,
            'invoice_generated_at' => now()
        ]);
        
        // Enviar notificação ao cliente
        $notificationService->sendInvoiceNotification(
            $this->order->customer->email,
            $this->order,
            $pdfContent
        );
    }

    public function failed(\Throwable $exception): void
    {
        // Registrar falha
        logger()->error('Falha ao gerar fatura', [
            'order_id' => $this->order->id,
            'exception' => $exception->getMessage()
        ]);
        
        // Notificar administrador
        // $notificationService->notifyAdmin('Falha ao gerar fatura', $exception->getMessage());
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 introduziu melhorias no sistema de filas, incluindo melhor suporte para tipagem de
                    parâmetros no método <code>handle()</code> e propriedades de classe fortemente tipadas.</p>
            </div>

            <h4>4.4.4. Eventos e Listeners</h4>
            <p>Utilize eventos para desacoplar componentes do sistema:</p>

            <div class="code-block">
                <pre><code>// Evento
namespace App\Events;

use App\Models\Order;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderShipped
{
    use Dispatchable, SerializesModels;

    public Order $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }
}

// Listener
namespace App\Listeners;

use App\Events\OrderShipped;
use App\Jobs\GenerateAndSendInvoice;
use App\Services\InventoryService;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProcessShippedOrder implements ShouldQueue
{
    protected $inventoryService;

    public function __construct(InventoryService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    public function handle(OrderShipped $event): void
    {
        // Atualizar inventário
        $this->inventoryService->updateInventoryForShippedOrder($event->order);
        
        // Gerar e enviar fatura
        GenerateAndSendInvoice::dispatch($event->order);
        
        // Registrar atividade
        activity()
            ->performedOn($event->order)
            ->withProperties(['status' => 'shipped'])
            ->log('Pedido enviado');
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 aprimorou o sistema de eventos com melhor suporte para tipagem e propriedades públicas
                    tipadas. O uso de <code>ShouldQueue</code> para listeners assíncronos também foi otimizado.</p>
            </div>

            <h4>4.4.5. Caching</h4>
            <p>Implemente estratégias de cache para melhorar a performance:</p>

            <div class="code-block">
                <pre><code>namespace App\Services;

use App\Models\Product;
use App\Repositories\Contracts\ProductRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Collection;

class CachedProductService
{
    protected $productRepository;
    protected $cacheExpiration = 3600; // 1 hora

    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    public function getFeaturedProducts(): Collection
    {
        return Cache::remember('featured_products', $this->cacheExpiration, function () {
            return $this->productRepository->getFeaturedProducts();
        });
    }

    public function getProductsByCategory(int $categoryId): Collection
    {
        return Cache::remember("products_by_category:{$categoryId}", $this->cacheExpiration, function () use ($categoryId) {
            return $this->productRepository->findByCategory($categoryId);
        });
    }

    public function getProduct(int $id): ?Product
    {
        return Cache::remember("product:{$id}", $this->cacheExpiration, function () use ($id) {
            return $this->productRepository->find($id);
        });
    }

    public function clearProductCache(int $id): void
    {
        Cache::forget("product:{$id}");
        
        // Limpar caches relacionados
        Cache::forget('featured_products');
        
        // Obter categoria do produto para limpar cache específico
        $product = $this->productRepository->find($id);
        if ($product && $product->category_id) {
            Cache::forget("products_by_category:{$product->category_id}");
        }
    }

    public function clearAllProductCaches(): void
    {
        // Usando tags no Laravel 12 para facilitar limpeza de grupos de cache
        Cache::tags(['products'])->flush();
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 introduziu melhorias no sistema de cache, incluindo suporte aprimorado para tags de
                    cache e tempos de expiração mais precisos. O uso de tipos de retorno explícitos também é
                    incentivado.</p>
            </div>
        </section>

        <section id="padroes" class="manual-section">
            <h2>5. Padrões de Codificação</h2>

            <p>Seguir padrões de codificação consistentes é essencial para manter a qualidade e legibilidade do código.
            </p>

            <h3>5.1. PSR-12</h3>
            <p>O projeto segue o padrão PSR-12 para formatação de código PHP. Utilize o Laravel Pint para automatizar a
                formatação:</p>

            <div class="code-block">
                <pre><code># Verificar problemas de formatação
./vendor/bin/pint --test

# Corrigir problemas de formatação
./vendor/bin/pint</code></pre>
            </div>

            <h3>5.2. Convenções de Nomenclatura</h3>

            <table>
                <thead>
                    <tr>
                        <th>Elemento</th>
                        <th>Convenção</th>
                        <th>Exemplo</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Classes</td>
                        <td>PascalCase</td>
                        <td>ProductService, OrderController</td>
                    </tr>
                    <tr>
                        <td>Métodos</td>
                        <td>camelCase</td>
                        <td>getProducts(), createOrder()</td>
                    </tr>
                    <tr>
                        <td>Variáveis</td>
                        <td>camelCase</td>
                        <td>$orderItems, $totalPrice</td>
                    </tr>
                    <tr>
                        <td>Constantes</td>
                        <td>UPPER_SNAKE_CASE</td>
                        <td>MAX_ATTEMPTS, API_VERSION</td>
                    </tr>
                    <tr>
                        <td>Arquivos de Configuração</td>
                        <td>snake_case</td>
                        <td>app_settings.php, payment_gateways.php</td>
                    </tr>
                    <tr>
                        <td>Tabelas do Banco</td>
                        <td>snake_case, plural</td>
                        <td>products, order_items</td>
                    </tr>
                    <tr>
                        <td>Models</td>
                        <td>PascalCase, singular</td>
                        <td>Product, OrderItem</td>
                    </tr>
                </tbody>
            </table>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 reforça o uso de tipagem estrita e declarações de tipo de retorno em todos os métodos,
                    seguindo as melhores práticas do PHP 8.2+.</p>
            </div>
        </section>

        <section id="excecoes" class="manual-section">
            <h2>6. Tratamento de Exceções</h2>

            <p>Um tratamento de exceções adequado é essencial para criar aplicações robustas e fornecer feedback útil
                aos usuários e desenvolvedores.</p>

            <h3>6.1. Hierarquia de Exceções</h3>
            <p>Crie uma hierarquia de exceções específicas para o domínio da aplicação:</p>

            <div class="code-block">
                <pre><code>namespace App\Exceptions;

use Exception;

// Exceção base da aplicação
class AppException extends Exception
{
    protected $errorCode;
    protected $errorData;

    public function __construct(string $message = "", int $code = 0, ?Exception $previous = null, array $errorData = [])
    {
        parent::__construct($message, $code, $previous);
        $this->errorCode = $code;
        $this->errorData = $errorData;
    }

    public function getErrorCode(): int
    {
        return $this->errorCode;
    }

    public function getErrorData(): array
    {
        return $this->errorData;
    }
}

// Exceções específicas de domínio
class ProductException extends AppException {}
class OrderException extends AppException {}
class PaymentException extends AppException {}
class ValidationException extends AppException {}</code></pre>
            </div>

            <h3>6.2. Handler Global</h3>
            <p>Personalize o Handler para tratar exceções de forma consistente:</p>

            <div class="code-block">
                <pre><code>namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    protected $dontReport = [
        \Illuminate\Auth\AuthenticationException::class,
        \Illuminate\Auth\Access\AuthorizationException::class,
        \Symfony\Component\HttpKernel\Exception\HttpException::class,
        \Illuminate\Database\Eloquent\ModelNotFoundException::class,
        \Illuminate\Validation\ValidationException::class,
    ];

    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            if (app()->bound('sentry')) {
                app('sentry')->captureException($e);
            }
        });

        // Tratamento personalizado para exceções específicas
        $this->renderable(function (AppException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'error_code' => $e->getErrorCode(),
                    'error_data' => $e->getErrorData()
                ], 400);
            }
        });

        $this->renderable(function (ModelNotFoundException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Recurso não encontrado',
                ], 404);
            }
        });

        $this->renderable(function (NotFoundHttpException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Endpoint não encontrado',
                ], 404);
            }
        });

        $this->renderable(function (ValidationException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Dados de entrada inválidos',
                    'errors' => $e->errors()
                ], 422);
            }
        });

        $this->renderable(function (AuthenticationException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Não autenticado',
                ], 401);
            }
        });
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 introduziu melhorias no sistema de tratamento de exceções, incluindo o método
                    <code>register()</code> para registrar handlers de exceção e o uso de closures tipadas para o método
                    <code>renderable()</code>.
                </p>
            </div>

            <h3>6.3. Try-Catch em Serviços</h3>
            <p>Utilize blocos try-catch em serviços para capturar e tratar exceções específicas:</p>

            <div class="code-block">
                <pre><code>namespace App\Services;

use App\Exceptions\PaymentException;
use App\Services\External\PaymentGatewayService;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    protected $paymentGateway;

    public function __construct(PaymentGatewayService $paymentGateway)
    {
        $this->paymentGateway = $paymentGateway;
    }

    public function processPayment(array $paymentData): array
    {
        try {
            // Validar dados
            if (empty($paymentData['amount']) || $paymentData['amount'] <= 0) {
                throw new PaymentException('Valor de pagamento inválido', 400);
            }

            // Processar pagamento
            $result = $this->paymentGateway->charge($paymentData);

            // Registrar transação bem-sucedida
            Log::info('Pagamento processado com sucesso', [
                'transaction_id' => $result['transaction_id'],
                'amount' => $paymentData['amount']
            ]);

            return $result;
        } catch (PaymentException $e) {
            // Repassar exceções específicas de pagamento
            throw $e;
        } catch (\Exception $e) {
            // Registrar erro e lançar exceção personalizada
            Log::error('Erro ao processar pagamento', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData
            ]);

            throw new PaymentException(
                'Falha ao processar pagamento: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }
}</code></pre>
            </div>
        </section>

        <section id="testes" class="manual-section">
            <h2>7. Testes</h2>

            <p>Testes automatizados são essenciais para garantir a qualidade e a manutenibilidade do código.</p>

            <h3>7.1. Testes Unitários</h3>
            <p>Teste unidades individuais de código, como métodos de serviços e repositórios:</p>

            <div class="code-block">
                <pre><code>namespace Tests\Unit\Services;

use App\DTOs\ProductDTO;
use App\Exceptions\ProductException;
use App\Models\Product;
use App\Repositories\Contracts\ProductRepositoryInterface;
use App\Repositories\Contracts\CategoryRepositoryInterface;
use App\Services\ProductService;
use Mockery;
use Tests\TestCase;

class ProductServiceTest extends TestCase
{
    protected $productRepository;
    protected $categoryRepository;
    protected $productService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->productRepository = Mockery::mock(ProductRepositoryInterface::class);
        $this->categoryRepository = Mockery::mock(CategoryRepositoryInterface::class);
        $this->productService = new ProductService(
            $this->productRepository,
            $this->categoryRepository
        );
    }

    public function test_create_product_with_valid_data(): void
    {
        // Arrange
        $productData = [
            'name' => 'Test Product',
            'description' => 'Test Description',
            'price' => 99.99,
            'category_id' => 1
        ];

        $expectedProduct = new Product($productData);
        $expectedProduct->id = 1;

        // Mock repository responses
        $this->productRepository->shouldReceive('findByName')
            ->once()
            ->with($productData['name'])
            ->andReturn(null);

        $this->categoryRepository->shouldReceive('find')
            ->once()
            ->with($productData['category_id'])
            ->andReturn((object)['id' => 1, 'name' => 'Test Category']);

        $this->productRepository->shouldReceive('create')
            ->once()
            ->with($productData)
            ->andReturn($expectedProduct);

        // Act
        $result = $this->productService->create($productData);

        // Assert
        $this->assertEquals($expectedProduct, $result);
        $this->assertEquals('Test Product', $result->name);
        $this->assertEquals(99.99, $result->price);
    }

    public function test_create_product_with_existing_name_throws_exception(): void
    {
        // Arrange
        $productData = [
            'name' => 'Existing Product',
            'price' => 99.99,
            'category_id' => 1
        ];

        $existingProduct = new Product($productData);
        $existingProduct->id = 1;

        // Mock repository response
        $this->productRepository->shouldReceive('findByName')
            ->once()
            ->with($productData['name'])
            ->andReturn($existingProduct);

        // Assert & Act
        $this->expectException(ProductException::class);
        $this->expectExceptionMessage('Um produto com este nome já existe');

        $this->productService->create($productData);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 simplificou a estrutura de testes e reforça o uso de tipos de retorno <code>void</code>
                    para métodos de teste. O Pest, um framework de testes construído sobre o PHPUnit, também é agora
                    oficialmente suportado.</p>
            </div>

            <h3>7.2. Testes de Feature</h3>
            <p>Teste a integração de múltiplos componentes e endpoints da API:</p>

            <div class="code-block">
                <pre><code>namespace Tests\Feature\Api;

use App\Models\Category;
use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Category $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Criar usuário para autenticação
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);

        // Criar categoria para testes
        $this->category = Category::factory()->create([
            'name' => 'Test Category'
        ]);
    }

    public function test_can_get_all_products(): void
    {
        // Arrange
        Product::factory()->count(3)->create([
            'category_id' => $this->category->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/products');

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(3, 'data')
            ->assertJsonStructure([
                'status',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'description',
                        'price',
                        'formatted_price',
                        'category_id',
                        'created_at',
                        'updated_at'
                    ]
                ]
            ]);
    }

    public function test_can_create_product(): void
    {
        // Arrange
        $productData = [
            'name' => 'New Test Product',
            'description' => 'Product description',
            'price' => 149.99,
            'category_id' => $this->category->id
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/products', $productData);

        // Assert
        $response->assertStatus(201)
            ->assertJson([
                'status' => 'success',
                'message' => 'Produto criado com sucesso',
                'data' => [
                    'name' => 'New Test Product',
                    'description' => 'Product description',
                    'price' => 149.99,
                    'category_id' => $this->category->id
                ]
            ]);

        $this->assertDatabaseHas('products', [
            'name' => 'New Test Product',
            'price' => 149.99
        ]);
    }

    public function test_cannot_create_product_with_invalid_data(): void
    {
        // Arrange
        $invalidData = [
            'name' => '',
            'price' => -10,
            'category_id' => 999 // categoria inexistente
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/products', $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'price', 'category_id']);
    }
}</code></pre>
            </div>

            <h3>7.3. Testes com Pest</h3>
            <p>O Laravel 12 suporta oficialmente o Pest, um framework de testes elegante construído sobre o PHPUnit:</p>

            <div class="code-block">
                <pre><code>use App\Models\Product;
use App\Models\Category;
use App\Models\User;

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->category = Category::factory()->create();
});

test('can get all products', function () {
    // Arrange
    Product::factory()->count(3)->create([
        'category_id' => $this->category->id
    ]);

    // Act & Assert
    $this->actingAs($this->user)
        ->getJson('/api/products')
        ->assertStatus(200)
        ->assertJsonCount(3, 'data');
});

test('can create a product', function () {
    // Arrange
    $productData = [
        'name' => 'Pest Test Product',
        'description' => 'Created with Pest',
        'price' => 199.99,
        'category_id' => $this->category->id
    ];

    // Act & Assert
    $this->actingAs($this->user)
        ->postJson('/api/products', $productData)
        ->assertStatus(201)
        ->assertJson([
            'status' => 'success',
            'data' => [
                'name' => 'Pest Test Product',
                'price' => 199.99
            ]
        ]);

    $this->assertDatabaseHas('products', [
        'name' => 'Pest Test Product'
    ]);
});</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 inclui suporte oficial para o Pest, um framework de testes mais expressivo e conciso. O
                    Pest pode ser instalado com <code>composer require pestphp/pest --dev</code> e configurado com
                    <code>./vendor/bin/pest --init</code>.
                </p>
            </div>
        </section>

        <section id="validacao" class="manual-section">
            <h2>8. Validação de Dados</h2>

            <p>A validação adequada dos dados de entrada é crucial para a segurança e integridade da aplicação.</p>

            <h3>8.1. Form Requests</h3>
            <p>Use Form Requests para validação complexa:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('update', $this->route('user'));
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => [
                'sometimes',
                'email',
                'max:255',
                Rule::unique('users')->ignore($this->route('user')->id)
            ],
            'password' => ['sometimes', 'string', 'min:8', 'confirmed'],
            'role_id' => [
                'sometimes',
                'integer',
                Rule::exists('roles', 'id'),
                function ($attribute, $value, $fail) {
                    if (!$this->user()->hasPermission('assign_roles') && $value != $this->route('user')->role_id) {
                        $fail('Você não tem permissão para alterar o papel do usuário.');
                    }
                }
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'email.unique' => 'Este e-mail já está em uso.',
            'password.min' => 'A senha deve ter pelo menos :min caracteres.',
            'role_id.exists' => 'O papel selecionado não existe.'
        ];
    }

    protected function prepareForValidation(): void
    {
        if ($this->has('name')) {
            $this->merge([
                'name' => trim($this->name)
            ]);
        }
    }
}</code></pre>
            </div>

            <h3>8.2. Regras de Validação Personalizadas</h3>
            <p>Crie regras de validação personalizadas para lógicas específicas:</p>

            <div class="code-block">
                <pre><code>namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class StrongPassword implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strlen($value) < 8) {
            $fail('O campo :attribute deve ter pelo menos 8 caracteres.');
        }

        if (!preg_match('/[A-Z]/', $value)) {
            $fail('O campo :attribute deve conter pelo menos uma letra maiúscula.');
        }

        if (!preg_match('/[a-z]/', $value)) {
            $fail('O campo :attribute deve conter pelo menos uma letra minúscula.');
        }

        if (!preg_match('/[0-9]/', $value)) {
            $fail('O campo :attribute deve conter pelo menos um número.');
        }

        if (!preg_match('/[^A-Za-z0-9]/', $value)) {
            $fail('O campo :attribute deve conter pelo menos um caractere especial.');
        }
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 introduziu a nova interface <code>ValidationRule</code> com o método
                    <code>validate</code> que recebe uma closure <code>$fail</code>, substituindo o antigo método
                    <code>passes</code>. Isso torna as regras de validação mais expressivas e permite múltiplas
                    mensagens de erro.
                </p>
            </div>

            <h3>8.3. Validação em Serviços</h3>
            <p>Implemente validação de regras de negócio nos serviços:</p>

            <div class="code-block">
                <pre><code>namespace App\Services;

use App\Exceptions\OrderException;
use App\Models\Order;
use App\Models\Product;
use App\Repositories\Contracts\OrderRepositoryInterface;
use App\Repositories\Contracts\ProductRepositoryInterface;
use Illuminate\Support\Facades\DB;

class OrderService
{
    protected $orderRepository;
    protected $productRepository;

    public function __construct(
        OrderRepositoryInterface $orderRepository,
        ProductRepositoryInterface $productRepository
    ) {
        $this->orderRepository = $orderRepository;
        $this->productRepository = $productRepository;
    }

    public function createOrder(array $data): Order
    {
        // Validar cliente
        if (empty($data['customer_id'])) {
            throw new OrderException('ID do cliente é obrigatório');
        }

        // Validar itens do pedido
        if (empty($data['items']) || !is_array($data['items'])) {
            throw new OrderException('O pedido deve conter pelo menos um item');
        }

        // Verificar disponibilidade de produtos
        foreach ($data['items'] as $item) {
            $product = $this->productRepository->find($item['product_id']);
            
            if (!$product) {
                throw new OrderException("Produto ID {$item['product_id']} não encontrado");
            }
            
            if (!$product->is_active) {
                throw new OrderException("Produto '{$product->name}' não está disponível para compra");
            }
            
            if ($product->stock_quantity < $item['quantity']) {
                throw new OrderException("Quantidade insuficiente em estoque para '{$product->name}'");
            }
        }

        return DB::transaction(function () use ($data) {
            // Criar pedido
            $order = $this->orderRepository->create([
                'customer_id' => $data['customer_id'],
                'status' => 'pending',
                'total' => 0 // Será calculado ao adicionar itens
            ]);

            // Adicionar itens e calcular total
            $total = 0;
            foreach ($data['items'] as $item) {
                $product = $this->productRepository->find($item['product_id']);
                $subtotal = $product->price * $item['quantity'];
                
                $order->items()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $product->price,
                    'subtotal' => $subtotal
                ]);
                
                // Atualizar estoque
                $this->productRepository->updateStock(
                    $item['product_id'], 
                    $product->stock_quantity - $item['quantity']
                );
                
                $total += $subtotal;
            }

            // Atualizar total do pedido
            $order->total = $total;
            $order->save();

            return $order;
        });
    }
}</code></pre>
            </div>
        </section>

        <section id="versionamento" class="manual-section">
            <h2>9. Controle de Versão</h2>
            <p>Esta seção descreve as práticas recomendadas para o controle de versão em projetos Laravel 12, garantindo
                um fluxo de trabalho colaborativo e eficiente.</p>

            <section id="git-workflow">
                <h3>9.1. Git Workflow</h3>
                <p>Adotamos o modelo Gitflow para gerenciar o ciclo de vida do desenvolvimento de software,
                    proporcionando uma estrutura clara para o trabalho colaborativo.</p>

                <div class="best-practice">
                    <h4>Estrutura de Branches</h4>
                    <ul>
                        <li><strong>main:</strong> Código em produção, sempre estável</li>
                        <li><strong>develop:</strong> Branch de integração para desenvolvimento</li>
                        <li><strong>feature/*:</strong> Branches para novas funcionalidades</li>
                        <li><strong>release/*:</strong> Branches de preparação para lançamentos</li>
                        <li><strong>hotfix/*:</strong> Branches para correções urgentes em produção</li>
                        <li><strong>bugfix/*:</strong> Branches para correções não urgentes</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Comandos Git para o Workflow</h4>
                    <pre>
# Iniciar uma nova feature
git checkout develop
git pull origin develop
git checkout -b feature/nome-da-feature

# Trabalhar na feature e fazer commits
git add .
git commit -m "feat: implementa funcionalidade X"

# Atualizar com develop e resolver conflitos
git fetch origin
git rebase origin/develop

# Finalizar e enviar para revisão
git push origin feature/nome-da-feature

# Após aprovação do PR, mesclar na develop
git checkout develop
git merge --no-ff feature/nome-da-feature
git push origin develop
            </pre>
                </div>
            </section>

            <section id="commits">
                <h3>9.2. Padrões de Commits</h3>
                <p>Utilizamos o padrão Conventional Commits para manter o histórico de commits organizado e facilitar a
                    geração automática de changelogs.</p>

                <div class="best-practice">
                    <h4>Estrutura de Mensagens de Commit</h4>
                    <p>Formato: <code>&lt;tipo&gt;[(escopo opcional)]: &lt;descrição&gt;</code></p>
                    <p>Tipos principais:</p>
                    <ul>
                        <li><strong>feat:</strong> Nova funcionalidade</li>
                        <li><strong>fix:</strong> Correção de bug</li>
                        <li><strong>docs:</strong> Alterações na documentação</li>
                        <li><strong>style:</strong> Formatação, ponto e vírgula, etc; sem alteração de código</li>
                        <li><strong>refactor:</strong> Refatoração de código</li>
                        <li><strong>test:</strong> Adição ou correção de testes</li>
                        <li><strong>chore:</strong> Alterações em processos de build, ferramentas, etc</li>
                        <li><strong>perf:</strong> Melhorias de performance</li>
                    </ul>
                </div>

                <div class="example-commits">
                    <h4>Exemplos de Bons Commits</h4>
                    <pre>
feat(auth): implementa autenticação via OAuth2
fix(api): corrige resposta 500 no endpoint de usuários
refactor(queries): otimiza consultas na listagem de produtos
test(unit): adiciona testes para o serviço de pagamento
docs(readme): atualiza instruções de instalação
style(lint): aplica formatação PSR-12
            </pre>
                </div>
            </section>

            <section id="versionamento-api">
                <h3>9.3. Versionamento de API</h3>
                <p>O versionamento adequado de APIs é crucial para manter a compatibilidade com clientes existentes
                    enquanto evolui a aplicação.</p>

                <div class="best-practice">
                    <h4>Estratégias de Versionamento</h4>
                    <ul>
                        <li><strong>URI Path:</strong> <code>/api/v1/users</code>, <code>/api/v2/users</code></li>
                        <li><strong>Query Parameter:</strong> <code>/api/users?version=1</code></li>
                        <li><strong>Header:</strong> <code>Accept: application/vnd.myapp.v1+json</code></li>
                        <li><strong>Content Negotiation:</strong> <code>Accept: application/json;version=1.0</code></li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Implementação de Versionamento via URI no Laravel 12</h4>
                    <pre>
// routes/api.php
Route::prefix('v1')->group(function () {
    Route::get('/users', [UserControllerV1::class, 'index']);
    Route::get('/users/{id}', [UserControllerV1::class, 'show']);
});

Route::prefix('v2')->group(function () {
    Route::get('/users', [UserControllerV2::class, 'index']);
    Route::get('/users/{id}', [UserControllerV2::class, 'show']);
});

// Estrutura de diretórios recomendada
// app/Http/Controllers/Api/V1/UserController.php
// app/Http/Controllers/Api/V2/UserController.php
            </pre>
                </div>

                <div class="best-practice">
                    <h4>Boas Práticas para Versionamento de API</h4>
                    <ul>
                        <li>Nunca remova ou modifique endpoints em uma versão existente</li>
                        <li>Adicione novas funcionalidades em novas versões</li>
                        <li>Documente claramente as diferenças entre versões</li>
                        <li>Mantenha compatibilidade com versões anteriores pelo maior tempo possível</li>
                        <li>Comunique antecipadamente a depreciação de versões antigas</li>
                        <li>Considere o uso de feature flags para funcionalidades experimentais</li>
                    </ul>
                </div>
            </section>

            <section id="semantic-versioning">
                <h3>9.4. Versionamento Semântico</h3>
                <p>Adotamos o Versionamento Semântico (SemVer) para numerar as versões do nosso software de forma clara
                    e previsível.</p>

                <div class="best-practice">
                    <h4>Formato: MAJOR.MINOR.PATCH</h4>
                    <ul>
                        <li><strong>MAJOR:</strong> Mudanças incompatíveis com versões anteriores</li>
                        <li><strong>MINOR:</strong> Adições de funcionalidades compatíveis com versões anteriores</li>
                        <li><strong>PATCH:</strong> Correções de bugs compatíveis com versões anteriores</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Gerando Tags de Versão</h4>
                    <pre>
# Criar uma nova tag de versão
git tag -a v1.2.3 -m "Versão 1.2.3"
git push origin v1.2.3

# Listar todas as tags
git tag -l

# Ver detalhes de uma tag específica
git show v1.2.3
            </pre>
                </div>

                <div class="tip">
                    <p>Considere usar ferramentas como <code>standard-version</code> ou <code>semantic-release</code>
                        para automatizar o versionamento baseado nos commits convencionais.</p>
                </div>
            </section>

            <section id="gerenciamento-dependencias">
                <h3>9.5. Gerenciamento de Dependências</h3>
                <p>O gerenciamento adequado de dependências é crucial para manter a estabilidade e segurança do projeto.
                </p>

                <div class="best-practice">
                    <h4>Práticas Recomendadas</h4>
                    <ul>
                        <li>Especifique versões exatas ou intervalos seguros no composer.json</li>
                        <li>Utilize o composer.lock para garantir instalações consistentes</li>
                        <li>Atualize regularmente as dependências para obter correções de segurança</li>
                        <li>Verifique vulnerabilidades com <code>composer audit</code></li>
                        <li>Documente dependências específicas de ambiente no README</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo de Composer.json com Versionamento Adequado</h4>
                    <pre>
{
    "require": {
        "php": "^8.2",
        "laravel/framework": "^12.0",
        "laravel/sanctum": "^4.0",
        "spatie/laravel-permission": "^6.0"
    },
    "require-dev": {
        "fakerphp/faker": "^1.23",
        "laravel/pint": "^1.13",
        "laravel/sail": "^1.26",
        "mockery/mockery": "^1.6",
        "nunomaduro/collision": "^8.0",
        "phpunit/phpunit": "^10.4",
        "spatie/laravel-ignition": "^2.4"
    }
}
            </pre>
                </div>
            </section>
        </section>

        <section id="boas-praticas" class="manual-section">
            <h2>10. Boas Práticas</h2>
            <p>Esta seção apresenta um conjunto de boas práticas para o desenvolvimento com Laravel 12, visando melhorar
                a qualidade, manutenibilidade e segurança do código.</p>

            <section id="principios-solid">
                <h3>10.1. Princípios SOLID</h3>
                <p>Os princípios SOLID são fundamentais para criar código orientado a objetos de alta qualidade e fácil
                    manutenção.</p>

                <div class="best-practice">
                    <h4>Aplicação dos Princípios SOLID</h4>
                    <ul>
                        <li><strong>S - Responsabilidade Única:</strong> Uma classe deve ter apenas uma razão para mudar
                        </li>
                        <li><strong>O - Aberto/Fechado:</strong> Entidades devem estar abertas para extensão, mas
                            fechadas para modificação</li>
                        <li><strong>L - Substituição de Liskov:</strong> Subtipos devem ser substituíveis por seus tipos
                            base</li>
                        <li><strong>I - Segregação de Interface:</strong> Clientes não devem ser forçados a depender de
                            interfaces que não utilizam</li>
                        <li><strong>D - Inversão de Dependência:</strong> Dependa de abstrações, não de implementações
                            concretas</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo de Aplicação do Princípio de Responsabilidade Única</h4>
                    <pre>
// ❌ Violação do SRP - Classe com múltiplas responsabilidades
class UserController extends Controller
{
    public function store(Request $request)
    {
        // Validação
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8',
        ]);
        
        // Criação do usuário
        $user = new User();
        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->password = Hash::make($validated['password']);
        $user->save();
        
        // Envio de email
        Mail::to($user->email)->send(new WelcomeEmail($user));
        
        // Criação de log
        Log::info('Novo usuário criado', ['user_id' => $user->id]);
        
        return redirect()->route('users.index');
    }
}

// ✅ Aplicando SRP - Separando responsabilidades
class UserController extends Controller
{
    public function __construct(
        protected UserService $userService,
        protected UserMailer $userMailer,
        protected ActivityLogger $logger
    ) {}

    public function store(UserStoreRequest $request)
    {
        // UserStoreRequest já contém a lógica de validação
        $validated = $request->validated();
        
        // Serviço responsável pela criação do usuário
        $user = $this->userService->create($validated);
        
        // Classe separada para envio de emails
        $this->userMailer->sendWelcomeEmail($user);
        
        // Serviço de log
        $this->logger->logUserCreation($user);
        
        return redirect()->route('users.index');
    }
}
            </pre>
                </div>
            </section>

            <section id="padrao-repositorio">
                <h3>10.2. Padrão Repositório</h3>
                <p>O padrão repositório ajuda a isolar a lógica de acesso a dados do restante da aplicação, facilitando
                    testes e manutenção.</p>

                <div class="best-practice">
                    <h4>Benefícios do Padrão Repositório</h4>
                    <ul>
                        <li>Abstrai a lógica de persistência de dados</li>
                        <li>Facilita a substituição da fonte de dados</li>
                        <li>Melhora a testabilidade do código</li>
                        <li>Centraliza a lógica de consulta</li>
                        <li>Reduz a duplicação de código</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Implementação do Padrão Repositório</h4>
                    <pre>
// app/Repositories/Interfaces/UserRepositoryInterface.php
namespace App\Repositories\Interfaces;

interface UserRepositoryInterface
{
    public function all();
    public function findById(int $id);
    public function findByEmail(string $email);
    public function create(array $data);
    public function update(int $id, array $data);
    public function delete(int $id);
}

// app/Repositories/Eloquent/UserRepository.php
namespace App\Repositories\Eloquent;

use App\Models\User;
use App\Repositories\Interfaces\UserRepositoryInterface;

class UserRepository implements UserRepositoryInterface
{
    protected $model;
    
    public function __construct(User $model)
    {
        $this->model = $model;
    }
    
    public function all()
    {
        return $this->model->all();
    }
    
    public function findById(int $id)
    {
        return $this->model->find($id);
    }
    
    public function findByEmail(string $email)
    {
        return $this->model->where('email', $email)->first();
    }
    
    public function create(array $data)
    {
        return $this->model->create($data);
    }
    
    public function update(int $id, array $data)
    {
        $record = $this->findById($id);
        return $record->update($data);
    }
    
    public function delete(int $id)
    {
        return $this->findById($id)->delete();
    }
}

// app/Providers/RepositoryServiceProvider.php
namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\Interfaces\UserRepositoryInterface;
use App\Repositories\Eloquent\UserRepository;

class RepositoryServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(UserRepositoryInterface::class, UserRepository::class);
    }
}
            </pre>
                </div>

                <div class="code-block">
                    <h4>Uso do Repositório em um Serviço</h4>
                    <pre>
// app/Services/UserService.php
namespace App\Services;

use App\Repositories\Interfaces\UserRepositoryInterface;
use Illuminate\Support\Facades\Hash;

class UserService
{
    protected $userRepository;
    
    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }
    
    public function create(array $data)
    {
        $data['password'] = Hash::make($data['password']);
        return $this->userRepository->create($data);
    }
    
    public function update(int $id, array $data)
    {
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }
        
        return $this->userRepository->update($id, $data);
    }
}
            </pre>
                </div>
            </section>

            <section id="service-layer">
                <h3>10.3. Camada de Serviço</h3>
                <p>A camada de serviço encapsula a lógica de negócios da aplicação, separando-a dos controladores e
                    modelos.</p>

                <div class="best-practice">
                    <h4>Responsabilidades da Camada de Serviço</h4>
                    <ul>
                        <li>Implementar regras de negócio complexas</li>
                        <li>Orquestrar operações que envolvem múltiplos repositórios</li>
                        <li>Gerenciar transações</li>
                        <li>Processar eventos de domínio</li>
                        <li>Abstrair lógica de terceiros</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo de Serviço para Processamento de Pedidos</h4>
                    <pre>
// app/Services/OrderService.php
namespace App\Services;

use App\Repositories\Interfaces\OrderRepositoryInterface;
use App\Repositories\Interfaces\ProductRepositoryInterface;
use App\Repositories\Interfaces\PaymentRepositoryInterface;
use App\Events\OrderCreated;
use Illuminate\Support\Facades\DB;
use App\Exceptions\InsufficientStockException;

class OrderService
{
    public function __construct(
        protected OrderRepositoryInterface $orderRepository,
        protected ProductRepositoryInterface $productRepository,
        protected PaymentRepositoryInterface $paymentRepository,
        protected InventoryService $inventoryService
    ) {}
    
    public function createOrder(array $orderData, array $items, array $paymentData)
    {
        // Verificar estoque antes de iniciar a transação
        foreach ($items as $item) {
            $product = $this->productRepository->findById($item['product_id']);
            if ($product->stock < $item['quantity']) {
                throw new InsufficientStockException("Produto {$product->name} não possui estoque suficiente");
            }
        }
        
        return DB::transaction(function () use ($orderData, $items, $paymentData) {
            // Criar o pedido
            $order = $this->orderRepository->create($orderData);
            
            // Adicionar itens ao pedido
            foreach ($items as $item) {
                $order->items()->create($item);
                
                // Atualizar estoque
                $this->inventoryService->decreaseStock(
                    $item['product_id'], 
                    $item['quantity']
                );
            }
            
            // Processar pagamento
            $payment = $this->paymentRepository->processPayment($order, $paymentData);
            
            // Atualizar status do pedido com base no pagamento
            $this->orderRepository->updateStatus(
                $order->id, 
                $payment->status === 'approved' ? 'paid' : 'pending'
            );
            
            // Disparar evento
            event(new OrderCreated($order));
            
            return $order;
        });
    }
}
            </pre>
                </div>

                <div class="code-block">
                    <h4>Uso do Serviço no Controller</h4>
                    <pre>
// app/Http/Controllers/OrderController.php
namespace App\Http\Controllers;

use App\Http\Requests\OrderStoreRequest;
use App\Services\OrderService;
use App\Exceptions\InsufficientStockException;

class OrderController extends Controller
{
    public function __construct(
        protected OrderService $orderService
    ) {}
    
    public function store(OrderStoreRequest $request)
    {
        try {
            $order = $this->orderService->createOrder(
                $request->only(['user_id', 'shipping_address']),
                $request->input('items'),
                $request->input('payment')
            );
            
            return response()->json([
                'message' => 'Pedido criado com sucesso',
                'order' => $order
            ], 201);
        } catch (InsufficientStockException $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 422);
        } catch (\Exception $e) {
            report($e);
            return response()->json([
                'message' => 'Erro ao processar pedido'
            ], 500);
        }
    }
}
            </pre>
                </div>
            </section>

            <section id="dto-pattern">
                <h3>10.4. Padrão DTO (Data Transfer Object)</h3>
                <p>DTOs são objetos simples usados para transferir dados entre subsistemas da aplicação, melhorando a
                    tipagem e a clareza do código.</p>

                <div class="best-practice">
                    <h4>Vantagens do Uso de DTOs</h4>
                    <ul>
                        <li>Tipagem forte para transferência de dados</li>
                        <li>Desacoplamento entre camadas</li>
                        <li>Documentação implícita da estrutura de dados</li>
                        <li>Facilita a validação e transformação de dados</li>
                        <li>Melhora a legibilidade do código</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Implementação de DTO</h4>
                    <pre>
// app/DTOs/UserData.php
namespace App\DTOs;

use Spatie\LaravelData\Data;

class UserData extends Data
{
    public function __construct(
        public readonly string $name,
        public readonly string $email,
        public readonly ?string $password = null,
        public readonly ?string $address = null,
        public readonly ?string $phone = null,
    ) {}
    
    // Método para criar DTO a partir de um request
    public static function fromRequest($request): self
    {
        return new self(
            name: $request->input('name'),
            email: $request->input('email'),
            password: $request->input('password'),
            address: $request->input('address'),
            phone: $request->input('phone')
        );
    }
    
    // Método para converter para array, excluindo campos sensíveis
    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'email' => $this->email,
            'address' => $this->address,
            'phone' => $this->phone,
        ];
    }
}

// Uso no Controller
namespace App\Http\Controllers;

use App\DTOs\UserData;
use App\Services\UserService;
use App\Http\Requests\UserStoreRequest;

class UserController extends Controller
{
    public function __construct(
        protected UserService $userService
    ) {}
    
    public function store(UserStoreRequest $request)
    {
        // Criar DTO a partir do request validado
        $userData = UserData::fromRequest($request);
        
        // Passar DTO para o serviço
        $user = $this->userService->createUser($userData);
        
        return response()->json($user, 201);
    }
}
            </pre>
                </div>
            </section>

            <section id="query-builder">
                <h3>10.5. Query Builder e Eloquent Otimizado</h3>
                <p>Otimizar consultas ao banco de dados é crucial para a performance da aplicação.</p>

                <div class="best-practice">
                    <h4>Práticas para Consultas Eficientes</h4>
                    <ul>
                        <li>Selecione apenas as colunas necessárias</li>
                        <li>Use eager loading para evitar o problema N+1</li>
                        <li>Utilize índices adequadamente</li>
                        <li>Prefira chunking para grandes conjuntos de dados</li>
                        <li>Use consultas em lote quando apropriado</li>
                        <li>Considere o uso de caching para consultas frequentes</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplos de Consultas Otimizadas</h4>
                    <pre>
// ❌ Consulta ineficiente - problema N+1
$posts = Post::all();
foreach ($posts as $post) {
    echo $post->user->name; // Gera uma consulta para cada post
}

// ✅ Consulta otimizada com eager loading
$posts = Post::with('user')->get();
foreach ($posts as $post) {
    echo $post->user->name; // Não gera consultas adicionais
}

// ❌ Selecionando todas as colunas
$users = User::all();

// ✅ Selecionando apenas colunas necessárias
$users = User::select('id', 'name', 'email')->get();

// ❌ Carregando todos os registros de uma vez
$allUsers = User::all();
foreach ($allUsers as $user) {
    // Processar usuário
}

// ✅ Processando em chunks para economizar memória
User::chunk(100, function ($users) {
    foreach ($users as $user) {
        // Processar usuário
    }
});

// ✅ Usando consultas em lote para atualizações
$userIds = [1, 2, 3, 4, 5];
$role = 'editor';

// Em vez de fazer um update para cada usuário
User::whereIn('id', $userIds)->update(['role' => $role]);
            </pre>
                </div>
            </section>

            <section id="seguranca">
                <h3>10.6. Práticas de Segurança</h3>
                <p>A segurança deve ser uma preocupação constante durante o desenvolvimento.</p>

                <div class="best-practice">
                    <h4>Medidas de Segurança Essenciais</h4>
                    <ul>
                        <li>Sempre valide e sanitize dados de entrada</li>
                        <li>Use prepared statements para consultas SQL</li>
                        <li>Implemente proteção CSRF em todos os formulários</li>
                        <li>Aplique rate limiting em APIs e formulários de autenticação</li>
                        <li>Utilize HTTPS em todos os ambientes</li>
                        <li>Configure cabeçalhos de segurança HTTP</li>
                        <li>Implemente autenticação de dois fatores</li>
                        <li>Mantenha dependências atualizadas</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplos de Implementações de Segurança</h4>
                    <pre>
// Proteção contra XSS em Blade
&lt;div>{{ $userInput }}&lt;/div> // Escapado automaticamente

// Proteção CSRF em formulários
&lt;form method="POST" action="/profile">
    @csrf
    &lt;!-- Campos do formulário -->
&lt;/form>

// Rate limiting em rotas
Route::middleware(['throttle:login'])->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// Configuração de rate limiting
// app/Providers/RouteServiceProvider.php
protected function configureRateLimiting(): void
{
    RateLimiter::for('login', function (Request $request) {
        return Limit::perMinute(5)->by($request->ip());
    });
    
    RateLimiter::for('api', function (Request $request) {
        return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
    });
}

// Cabeçalhos de segurança
// config/secure-headers.php (usando bepsvpt/secure-headers)
return [
    'x-frame-options' => 'DENY',
    'x-xss-protection' => '1; mode=block',
    'x-content-type-options' => 'nosniff',
    'referrer-policy' => 'no-referrer-when-downgrade',
    'content-security-policy' => [
        'default-src' => ["'self'"],
        'script-src' => ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'https://www.google-analytics.com'],
        'style-src' => ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
        'img-src' => ["'self'", 'data:', 'https://www.google-analytics.com'],
        'font-src' => ["'self'", 'https://fonts.gstatic.com'],
    ],
];
            </pre>
                </div>
            </section>

            <section id="logging-monitoring">
                <h3>10.7. Logging e Monitoramento</h3>
                <p>Logging e monitoramento adequados são essenciais para identificar problemas e manter a saúde da
                    aplicação.</p>

                <div class="best-practice">
                    <h4>Práticas de Logging</h4>
                    <ul>
                        <li>Use níveis de log apropriados (debug, info, warning, error, critical)</li>
                        <li>Inclua contexto suficiente em cada log</li>
                        <li>Estruture logs para facilitar a análise</li>
                        <li>Configure canais de log diferentes para diferentes propósitos</li>
                        <li>Implemente rotação de logs para gerenciar o espaço em disco</li>
                        <li>Considere serviços de log centralizados para ambientes distribuídos</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplos de Logging Eficaz</h4>
                    <pre>
// Logging básico
Log::info('Usuário logado', ['user_id' => $user->id, 'ip' => $request->ip()]);

// Logging com contexto de exceção
try {
    // Código que pode lançar exceção
} catch (\Exception $e) {
    Log::error('Erro ao processar pagamento', [
        'exception' => $e->getMessage(),
        '


        <section id="laravel12" class="manual-section">
            <h2>11. Novidades do Laravel 12</h2>

            <p>O Laravel 12 introduziu várias melhorias e novos recursos que devem ser considerados durante o
                desenvolvimento.</p>

            <h3>11.1. Requisitos Atualizados</h3>
            <ul>
                <li>PHP 8.2+ (recomendado PHP 8.3)</li>
                <li>Node.js 20 LTS para compilação de assets</li>
                <li>Composer 2.6+</li>
            </ul>

            <h3>11.2. Novo Sistema de Inicialização</h3>
            <p>O Laravel 12 introduziu um novo sistema de inicialização (bootstrap) mais eficiente:</p>

            <div class="code-block">
                <pre><code>// bootstrap/app.php
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure()
    ->withProviders()
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Configuração de middleware
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Configuração de exceções
    })
    ->create();</code></pre>
                </div>

                <h3>11.3. Tipagem Estrita</h3>
                <p>O Laravel 12 incentiva fortemente o uso de tipagem estrita em todo o código:</p>

                <div class="code-block">
                    <pre><code>// Antes (Laravel 11 e anteriores)
public function show($id)
{
    $product = Product::find($id);
    return view('products.show', compact('product'));
}

// Agora (Laravel 12)
public function show(int $id): View
{
    $product = Product::findOrFail($id);
    return view('products.show', compact('product'));
}</code></pre>
                </div>

                <h3>11.4. Novas APIs de Coleção</h3>
                <p>O Laravel 12 introduziu novos métodos para trabalhar com coleções:</p>

                <div class="code-block">
                    <pre><code>// Novo método collect() para arrays associativos
$collection = collect(['name' => 'John', 'age' => 30]);

// Novo método sole() para obter um único item que corresponda a uma condição
$user = User::sole(['email' => '<EMAIL>']);

// Novo método collect()->partition()
[$activeUsers, $inactiveUsers] = User::all()->partition(fn ($user) => $user->is_active);

// Novo método collect()->groupBy() com múltiplos níveis
$usersByRoleAndStatus = User::all()->groupBy(['role', 'is_active']);</code></pre>
                </div>

                <h3>11.5. Melhorias no Sistema de Filas</h3>
                <p>O Laravel 12 aprimorou o sistema de filas com novas funcionalidades:</p>

                <div class="code-block">
                    <pre><code>// Novo método dispatchSync() para executar jobs imediatamente
ProcessPayment::dispatchSync($order);

// Novo método dispatchAfterResponse() para executar após a resposta HTTP
SendOrderConfirmation::dispatchAfterResponse($order);

// Novo método batch() com dependências
Bus::batch([
    new ProcessPayment($order),
    new UpdateInventory($order),
    new SendOrderConfirmation($order),
])->then(function (Batch $batch) {
    // Todos os jobs foram processados com sucesso
})->catch(function (Batch $batch, Throwable $e) {
    // Um job falhou
})->dispatch();</code></pre>
                </div>

                <h3>11.6. Vite 5 para Assets</h3>
                <p>O Laravel 12 utiliza o Vite 5 para compilação de assets:</p>

                <div class="code-block">
                    <pre><code>// vite.config.js
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: true,
        }),
    ],
});</code></pre>
                </div>

                <div class="code-block">
                    <pre><code>// Em suas views Blade
@vite(['resources/css/app.css', 'resources/js/app.js'])</code></pre>
                </div>

                <h3>11.7. Novas Regras de Validação</h3>
                <p>O Laravel 12 introduziu novas regras de validação:</p>

                <div class="code-block">
                    <pre><code>// Nova regra 'missing' - verifica se um valor NÃO existe na tabela
'email' => 'required|email|missing:users,email'

// Nova regra 'decimal' com precisão e escala
'price' => 'required|decimal:2,2' // Exige exatamente 2 casas decimais

// Nova regra 'prohibits' - campos mutuamente exclusivos
'credit_card' => 'required_without:paypal|prohibits:paypal',
'paypal' => 'required_without:credit_card|prohibits:credit_card',

// Nova regra 'enum' para validar enums PHP
'status' => ['required', new Enum(OrderStatus::class)]</code></pre>
                </div>

                <h3>11.8. Migração de Versões Anteriores</h3>
                <p>Para migrar de versões anteriores do Laravel para o Laravel 12, siga estes passos:</p>

                <ol>
                    <li>Atualize o PHP para 8.2+ (preferencialmente 8.3)</li>
                    <li>Atualize o Node.js para a versão 20 LTS</li>
                    <li>Crie um novo projeto Laravel 12 e migre o código gradualmente</li>
                    <li>Atualize as dependências no composer.json</li>
                    <li>Adapte o código para usar tipagem estrita</li>
                    <li>Atualize o sistema de bootstrap conforme o novo padrão</li>
                    <li>Migre de Laravel Mix para Vite (se ainda estiver usando Mix)</li>
                    <li>Execute testes para garantir compatibilidade</li>
                </ol>

                <div class="alerts-section">
                    <h4>Importante</h4>
                    <p>O Laravel 12 introduziu várias breaking changes. Consulte a documentação oficial para uma lista
                        completa de alterações e instruções detalhadas de migração.</p>
                </div>
            </section>

            <footer class="manual-footer">
                <p>Manual de Implementação - Laravel 12</p>
                <p>Última atualização: Maio 2023</p>
            </footer>
            </pre>
            </div>
        </section>
    </section>
    </section>
</body>

</html>