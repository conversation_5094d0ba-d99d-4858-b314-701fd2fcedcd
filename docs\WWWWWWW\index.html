<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manuais Técnicos de Desenvolvimento</title>
    <link rel="stylesheet" href="css/manual.css">
    <style>
        /* Estilos específicos para a página index */
        .manual-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .manual-card {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            background-color: white;
            box-shadow: var(--shadow);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .manual-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }

        .manual-card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1.5rem;
        }

        .manual-card-header h3 {
            margin: 0;
            font-size: 1.3rem;
            color: white;
        }

        .manual-card-body {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .manual-card-body p {
            margin-bottom: 1.5rem;
            flex-grow: 1;
        }

        .tag {
            display: inline-block;
            background-color: var(--light-bg);
            padding: 0.3rem 0.6rem;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }

        .btn {
            display: inline-block;
            background-color: var(--secondary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            text-decoration: none;
            text-align: center;
            transition: background-color 0.2s;
            margin-top: auto;
        }

        .btn:hover {
            background-color: #2980b9;
            text-decoration: none;
            color: white;
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--secondary-color);
            color: var(--secondary-color);
        }

        .btn-outline:hover {
            background-color: var(--secondary-color);
            color: white;
        }

        .coming-soon {
            opacity: 0.7;
        }

        .coming-soon .btn {
            background-color: var(--text-light);
            cursor: not-allowed;
        }

        .search-container {
            margin: 2rem 0;
        }

        .search-container input {
            width: 100%;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 1rem;
        }

        .coming-soon-section {
            margin-top: 4rem;
        }

        .section-title {
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 2rem;
        }

        .hero-section {
            background-color: var(--primary-color);
            color: white;
            padding: 4rem 2rem;
            text-align: center;
            margin: -20px -20px 2rem -20px;
        }

        .hero-section h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: white;
        }

        .hero-section p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 3rem 1rem;
            }

            .hero-section h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>

<body>
    <div class="hero-section">
        <h1>Manuais Técnicos de Desenvolvimento</h1>
        <p>Biblioteca de manuais e guias para melhores práticas, padrões e técnicas para desenvolvimento de APIs com
            Laravel.</p>
    </div>

    <div class="container">
        <div class="search-container">
            <input type="text" id="searchInput" placeholder="Buscar manuais..." onkeyup="filterManuals()">
        </div>

        <h2 class="section-title">Manuais Disponíveis</h2>

        <div class="manual-grid" id="availableManuals">
            <!-- Manual de Arquitetura -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Arquitetura</h3>
                </div>
                <div class="manual-card-body">
                    <p>Princípios e padrões para construção de uma arquitetura robusta e escalável para APIs Laravel,
                        incluindo SOLID, camadas de aplicação e organização de código.</p>
                    <div class="tags">
                        <span class="tag">Arquitetura</span>
                        <span class="tag">SOLID</span>
                        <span class="tag">Design Patterns</span>
                        <span class="tag">Laravel</span>
                    </div>
                    <a href="ManualArquitetura.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Implementação -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Implementação</h3>
                </div>
                <div class="manual-card-body">
                    <p>Guia completo para implementação de APIs RESTful com Laravel, abordando boas práticas,
                        organização de código e padrões de desenvolvimento.</p>
                    <div class="tags">
                        <span class="tag">Implementação</span>
                        <span class="tag">REST</span>
                        <span class="tag">API</span>
                        <span class="tag">Laravel</span>
                    </div>
                    <a href="ManualImplementação.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Segurança -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Segurança</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação de segurança em APIs Laravel, incluindo autenticação, autorização, proteção contra
                        vulnerabilidades OWASP e melhores práticas de segurança.</p>
                    <div class="tags">
                        <span class="tag">Segurança</span>
                        <span class="tag">Autenticação</span>
                        <span class="tag">OWASP</span>
                        <span class="tag">Laravel</span>
                    </div>
                    <a href="ManualSeguranca.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Respostas Padronizadas -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Respostas Padronizadas</h3>
                </div>
                <div class="manual-card-body">
                    <p>Padrões de formatação de respostas para APIs RESTful, incluindo estrutura de JSON, códigos de
                        status HTTP, tratamento de erros e mensagens consistentes.</p>
                    <div class="tags">
                        <span class="tag">API</span>
                        <span class="tag">REST</span>
                        <span class="tag">JSON</span>
                        <span class="tag">Padronização</span>
                    </div>
                    <a href="ManualRespostasPadronizadas.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Swagger -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Swagger</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação e manutenção de documentação de API com OpenAPI/Swagger no Laravel, incluindo
                        automação, exemplos e melhores práticas.</p>
                    <div class="tags">
                        <span class="tag">Swagger</span>
                        <span class="tag">OpenAPI</span>
                        <span class="tag">Documentação</span>
                        <span class="tag">API</span>
                    </div>
                    <a href="ManualSwagger.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Performance -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Performance</h3>
                </div>
                <div class="manual-card-body">
                    <p>Técnicas avançadas para otimização de performance em APIs Laravel, incluindo queries eficientes,
                        cache, filas e melhores práticas para escalabilidade.</p>
                    <div class="tags">
                        <span class="tag">Performance</span>
                        <span class="tag">Otimização</span>
                        <span class="tag">Laravel</span>
                        <span class="tag">Cache</span>
                    </div>
                    <a href="ManualPerformance.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Testes -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Testes</h3>
                </div>
                <div class="manual-card-body">
                    <p>Estratégias e ferramentas para testes unitários, integração e ponta a ponta em APIs Laravel,
                        incluindo TDD, PHPUnit e ferramentas para garantir a qualidade.</p>
                    <div class="tags">
                        <span class="tag">Testes</span>
                        <span class="tag">PHPUnit</span>
                        <span class="tag">TDD</span>
                        <span class="tag">Quality Assurance</span>
                    </div>
                    <a href="ManualTestes.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Deployment -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Deployment</h3>
                </div>
                <div class="manual-card-body">
                    <p>Processos e ferramentas para automação de deployment seguro de APIs Laravel em ambientes de
                        produção, homologação e desenvolvimento.</p>
                    <div class="tags">
                        <span class="tag">Deployment</span>
                        <span class="tag">DevOps</span>
                        <span class="tag">CI/CD</span>
                        <span class="tag">Automação</span>
                    </div>
                    <a href="ManualDeployment.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Logging -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Logging</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação de logging eficiente para monitoramento, debugging e auditoria em APIs Laravel,
                        incluindo ferramentas, níveis de log e boas práticas.</p>
                    <div class="tags">
                        <span class="tag">Logging</span>
                        <span class="tag">Monitoramento</span>
                        <span class="tag">Debug</span>
                        <span class="tag">Auditoria</span>
                    </div>
                    <a href="ManualLogging.html" class="btn">Acessar Manual</a>
                </div>
            </div>

            <!-- Manual de Commits -->
            <div class="manual-card">
                <div class="manual-card-header">
                    <h3>Manual de Boas Práticas para Commits Git</h3>
                </div>
                <div class="manual-card-body">
                    <p>Um guia completo sobre como criar commits efetivos que facilitam a colaboração, revisão de código
                        e manutenção de projetos.</p>
                    <div class="tags">
                        <span class="tag">Git</span>
                        <span class="tag">Controle de Versão</span>
                        <span class="tag">Boas Práticas</span>
                    </div>
                    <a href="ManualCommits.html" class="btn">Acessar Manual</a>
                </div>
            </div>
        </div>

        <h2 class="section-title coming-soon-section">Manuais Futuros</h2>
        <p>Estes manuais estão em desenvolvimento e estarão disponíveis em breve:</p>

        <div class="manual-grid" id="comingSoonManuals">
            <!-- Manual de Microsserviços -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Microsserviços com Laravel</h3>
                </div>
                <div class="manual-card-body">
                    <p>Estruturação, implementação e gestão de arquiteturas de microsserviços utilizando o ecossistema
                        Laravel, incluindo comunicação entre serviços e controle de estado.</p>
                    <div class="tags">
                        <span class="tag">Microsserviços</span>
                        <span class="tag">Arquitetura</span>
                        <span class="tag">Distribuído</span>
                        <span class="tag">Laravel</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Integração Contínua -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Integração Contínua para Laravel</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação de pipelines CI/CD específicos para projetos Laravel, integrando ferramentas de
                        qualidade de código, testes automatizados e deployment.</p>
                    <div class="tags">
                        <span class="tag">CI/CD</span>
                        <span class="tag">GitHub Actions</span>
                        <span class="tag">Jenkins</span>
                        <span class="tag">Automação</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de GraphQL -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de GraphQL com Laravel</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação de APIs GraphQL com Laravel, incluindo queries, mutations, autenticação e boas
                        práticas para construir esquemas eficientes.</p>
                    <div class="tags">
                        <span class="tag">GraphQL</span>
                        <span class="tag">API</span>
                        <span class="tag">Lighthouse</span>
                        <span class="tag">Laravel</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Escalabilidade -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Escalabilidade para APIs Laravel</h3>
                </div>
                <div class="manual-card-body">
                    <p>Estratégias e técnicas para escalar APIs Laravel de forma horizontal e vertical, incluindo
                        balanceamento de carga, estratégias de cache distribuído e otimização.</p>
                    <div class="tags">
                        <span class="tag">Escalabilidade</span>
                        <span class="tag">Alta Disponibilidade</span>
                        <span class="tag">Performance</span>
                        <span class="tag">Cloud</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Cache -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Estratégias de Cache</h3>
                </div>
                <div class="manual-card-body">
                    <p>Implementação de estratégias avançadas de cache em diferentes níveis para APIs Laravel, incluindo
                        Redis, Memcached, cache HTTP e cache de queries.</p>
                    <div class="tags">
                        <span class="tag">Cache</span>
                        <span class="tag">Redis</span>
                        <span class="tag">Performance</span>
                        <span class="tag">Otimização</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>

            <!-- Manual de Versionamento de APIs -->
            <div class="manual-card coming-soon">
                <div class="manual-card-header">
                    <h3>Manual de Versionamento de APIs</h3>
                </div>
                <div class="manual-card-body">
                    <p>Estratégias e implementação para versionamento eficiente de APIs em projetos Laravel, incluindo
                        gestão de compatibilidade, migrações e evolução de interfaces.</p>
                    <div class="tags">
                        <span class="tag">Versionamento</span>
                        <span class="tag">API</span>
                        <span class="tag">Compatibilidade</span>
                        <span class="tag">Evolução</span>
                    </div>
                    <span class="btn">Em Breve</span>
                </div>
            </div>
        </div>

        <div class="suggest-section">
            <h2 class="section-title">Sugira um Manual</h2>
            <p>Tem uma ideia para um novo manual técnico? Envie sua sugestão para o time de desenvolvimento!</p>
            <a href="#" class="btn btn-outline">Enviar Sugestão</a>
        </div>
    </div>

    <footer class="manual-footer">
        <div class="footer-content">
            <p>Manuais Técnicos de Desenvolvimento © 2023</p>
            <p>Desenvolvido pela equipe de Engenharia de Software</p>
        </div>
    </footer>

    <script>
        // Função para filtrar os manuais com base na busca
        function filterManuals() {
            let input = document.getElementById('searchInput');
            let filter = input.value.toUpperCase();
            let availableCards = document.querySelectorAll('#availableManuals .manual-card');
            let comingSoonCards = document.querySelectorAll('#comingSoonManuals .manual-card');

            // Filtrar manuais disponíveis
            filterCards(availableCards, filter);

            // Filtrar manuais futuros
            filterCards(comingSoonCards, filter);
        }

        function filterCards(cards, filter) {
            for (let i = 0; i < cards.length; i++) {
                let card = cards[i];
                let title = card.querySelector('h3').textContent;
                let description = card.querySelector('p').textContent;
                let tags = card.querySelectorAll('.tag');

                let found = title.toUpperCase().indexOf(filter) > -1 ||
                    description.toUpperCase().indexOf(filter) > -1;

                // Verificar também nas tags
                if (!found) {
                    for (let j = 0; j < tags.length; j++) {
                        if (tags[j].textContent.toUpperCase().indexOf(filter) > -1) {
                            found = true;
                            break;
                        }
                    }
                }

                card.style.display = found ? "" : "none";
            }
        }
    </script>
</body>

</html>