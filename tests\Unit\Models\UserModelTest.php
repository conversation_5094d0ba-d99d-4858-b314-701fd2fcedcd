<?php

namespace Tests\Unit\Models;

use App\Models\UserModel;
use App\Models\RoleModel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class UserModelTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_hides_password_from_arrays()
    {
        $user = new UserModel([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('secret')
        ]);

        $user->save();

        $userArray = $user->toArray();

        $this->assertArrayNotHasKey('password', $userArray);
    }

    #[Test]
    public function it_can_assign_and_check_roles()
    {
        $user = UserModel::factory()->create();
        $role = RoleModel::factory()->create([
            'name' => 'admin',
            'slug' => 'admin'
        ]);

        $user->assignRole('admin');
        $this->assertTrue($user->hasRole('admin'));
    }

    #[Test]
    public function it_can_remove_roles()
    {
        $user = UserModel::factory()->create();
        $role = RoleModel::factory()->create([
            'name' => 'admin',
            'slug' => 'admin'
        ]);

        $user->assignRole('admin');
        $user->removeRole('admin');
        $this->assertFalse($user->hasRole('admin'));
    }

    #[Test]
    public function it_returns_jwt_custom_claims()
    {
        $user = UserModel::factory()->create();
        $claims = $user->getJWTCustomClaims();

        $this->assertArrayHasKey('email', $claims);
        $this->assertArrayHasKey('name', $claims);
        $this->assertArrayHasKey('roles', $claims);
    }
}
