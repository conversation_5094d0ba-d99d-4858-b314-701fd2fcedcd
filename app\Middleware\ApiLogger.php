<?php

namespace App\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ApiLogger
{
    public function handle(Request $request, Closure $next): Response
    {
        // Log da requisição
        Log::channel('api')->info('API Request', [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'headers' => $request->headers->all(),
            'body' => $request->except(['password', 'password_confirmation']),
        ]);

        // Processar a requisição
        $response = $next($request);

        // Log da resposta
        Log::channel('api')->info('API Response', [
            'status' => $response->getStatusCode(),
            'content' => json_decode($response->getContent(), true),
        ]);

        return $response;
    }
}
