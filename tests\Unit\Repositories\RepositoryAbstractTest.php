<?php

namespace Tests\Unit\Repositories;

use App\Repositories\RepositoryAbstract;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use PHPUnit\Framework\Attributes\Test;

#[CoversClass(\App\Repositories\RepositoryAbstract::class)]
#[Group('repositories')]
class RepositoryAbstractTest extends TestCase
{
    /**
     * Mock do modelo Eloquent
     * 
     * @var \Mockery\MockInterface
     */
    protected $modelMock;

    /**
     * Instância do repositório para testes
     * 
     * @var RepositoryAbstract
     */
    protected $repository;

    /**
     * Configuração inicial para cada teste
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Criar mock do modelo
        $this->modelMock = Mockery::mock(Model::class);

        // Criar uma implementação concreta da classe abstrata para testes
        $this->repository = new class($this->modelMock) extends RepositoryAbstract {
            // Implementação do método clearModelCache para testes
            protected function clearModelCache($id = null): void
            {
                // Este método é chamado pelos testes, mas não precisa fazer nada real
            }
        };

        // Configurar o Cache facade para testes
        Cache::shouldReceive('remember')
            ->byDefault()
            ->andReturnUsing(function ($key, $minutes, $callback) {
                return $callback();
            });

        Cache::shouldReceive('forget')
            ->byDefault()
            ->andReturn(true);
    }

    /**
     * Limpa os mocks após cada teste
     *
     * @return void
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Testa o método find()
     *
     * @return void
     */
    #[Test]
    public function testFind(): void
    {
        // Dados para o teste
        $id = 1;

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('findOrFail')
            ->once()
            ->with($id)
            ->andReturn($this->modelMock);

        // Configurar o comportamento do mock para getTable
        $this->modelMock->shouldReceive('getTable')
            ->andReturn('test_table');

        // Executar o método a ser testado
        $result = $this->repository->find($id);

        // Verificar o resultado
        $this->assertSame($this->modelMock, $result);
    }

    /**
     * Testa o método find() com ID inexistente
     *
     * @return void
     */
    #[Test]
    public function testFindWithInvalidId(): void
    {
        // Dados para o teste
        $id = 999;

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('findOrFail')
            ->once()
            ->with($id)
            ->andThrow(new ModelNotFoundException());

        // Configurar o comportamento do mock para getTable
        $this->modelMock->shouldReceive('getTable')
            ->andReturn('test_table');

        // Configurar o Log facade para testes
        Log::shouldReceive('error')
            ->once()
            ->with("Model not found: {$id}", Mockery::any());

        // Executar o método a ser testado
        $result = $this->repository->find($id);

        // Verificar o resultado
        $this->assertNull($result);
    }

    /**
     * Testa o método findAll()
     *
     * @return void
     */
    #[Test]
    public function testFindAll(): void
    {
        // Dados para o teste
        $collection = new Collection([$this->modelMock]);

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('all')
            ->once()
            ->andReturn($collection);

        // Configurar o comportamento do mock para getTable
        $this->modelMock->shouldReceive('getTable')
            ->andReturn('test_table');

        // Executar o método a ser testado
        $result = $this->repository->findAll();

        // Verificar o resultado
        $this->assertSame($collection, $result);
    }

    /**
     * Testa o método create()
     *
     * @return void
     */
    #[Test]
    public function testCreate(): void
    {
        // Dados para o teste
        $data = ['name' => 'Test', 'email' => '<EMAIL>'];

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('create')
            ->once()
            ->with($data)
            ->andReturn($this->modelMock);

        // Configurar o DB facade para testes
        DB::shouldReceive('beginTransaction')
            ->once();
        DB::shouldReceive('commit')
            ->once();

        // Executar o método a ser testado
        $result = $this->repository->create($data);

        // Verificar o resultado
        $this->assertSame($this->modelMock, $result);
    }

    /**
     * Testa o método create() com erro
     *
     * @return void
     */
    #[Test]
    public function testCreateWithError(): void
    {
        // Dados para o teste
        $data = ['name' => 'Test', 'email' => '<EMAIL>'];

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('create')
            ->once()
            ->with($data)
            ->andThrow(new \Exception('Error creating model'));

        // Configurar o DB facade para testes
        DB::shouldReceive('beginTransaction')
            ->once();
        DB::shouldReceive('rollBack')
            ->once();

        // Configurar o Log facade para testes
        Log::shouldReceive('error')
            ->once()
            ->with('Error creating model', Mockery::any());

        // Executar o método a ser testado
        $result = $this->repository->create($data);

        // Verificar o resultado
        $this->assertNull($result);
    }

    /**
     * Testa o método update()
     *
     * @return void
     */
    #[Test]
    public function testUpdate(): void
    {
        // Dados para o teste
        $id = 1;
        $data = ['name' => 'Updated Test', 'email' => '<EMAIL>'];

        // Criar um mock parcial do repositório que permite mockar métodos protegidos
        $repositoryMock = Mockery::mock(get_class($this->repository))
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();

        // Configurar o comportamento do mock para find
        $repositoryMock->shouldReceive('find')
            ->once()
            ->with($id)
            ->andReturn($this->modelMock);

        // Configurar o comportamento do mock para clearModelCache
        $repositoryMock->shouldReceive('clearModelCache')
            ->once()
            ->with($id);

        // Configurar o comportamento do mock para update
        $this->modelMock->shouldReceive('update')
            ->once()
            ->with($data)
            ->andReturn(true);

        // Configurar o DB facade para testes
        DB::shouldReceive('beginTransaction')
            ->once();
        DB::shouldReceive('commit')
            ->once();

        // Executar o método a ser testado
        $result = $repositoryMock->update($id, $data);

        // Verificar o resultado
        $this->assertSame($this->modelMock, $result);
    }

    /**
     * Testa o método update() com ID inexistente
     *
     * @return void
     */
    #[Test]
    public function testUpdateWithInvalidId(): void
    {
        // Dados para o teste
        $id = 999;
        $data = ['name' => 'Updated Test', 'email' => '<EMAIL>'];

        // Criar um mock parcial do repositório
        $repositoryMock = Mockery::mock(get_class($this->repository))->makePartial();

        // Configurar o comportamento do mock para find
        $repositoryMock->shouldReceive('find')
            ->once()
            ->with($id)
            ->andReturn(null);

        // Executar o método a ser testado
        $result = $repositoryMock->update($id, $data);

        // Verificar o resultado
        $this->assertNull($result);
    }

    /**
     * Testa o método update() com erro
     *
     * @return void
     */
    #[Test]
    public function testUpdateWithError(): void
    {
        // Dados para o teste
        $id = 1;
        $data = ['name' => 'Updated Test', 'email' => '<EMAIL>'];

        // Criar um mock parcial do repositório
        $repositoryMock = Mockery::mock(get_class($this->repository))->makePartial();

        // Configurar o comportamento do mock para find
        $repositoryMock->shouldReceive('find')
            ->once()
            ->with($id)
            ->andReturn($this->modelMock);

        // Configurar o comportamento do mock para update
        $this->modelMock->shouldReceive('update')
            ->once()
            ->with($data)
            ->andThrow(new \Exception('Error updating model'));

        // Configurar o DB facade para testes
        DB::shouldReceive('beginTransaction')
            ->once();
        DB::shouldReceive('rollBack')
            ->once();

        // Configurar o Log facade para testes
        Log::shouldReceive('error')
            ->once()
            ->with("Error updating model: {$id}", Mockery::any());

        // Executar o método a ser testado
        $result = $repositoryMock->update($id, $data);

        // Verificar o resultado
        $this->assertNull($result);
    }

    /**
     * Testa o método delete()
     *
     * @return void
     */
    #[Test]
    public function testDelete(): void
    {
        // Dados para o teste
        $id = 1;

        // Criar um mock parcial do repositório que permite mockar métodos protegidos
        $repositoryMock = Mockery::mock(get_class($this->repository))
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();

        // Configurar o comportamento do mock para find
        $repositoryMock->shouldReceive('find')
            ->once()
            ->with($id)
            ->andReturn($this->modelMock);

        // Configurar o comportamento do mock para clearModelCache
        $repositoryMock->shouldReceive('clearModelCache')
            ->once()
            ->with($id);

        // Configurar o comportamento do mock para delete
        $this->modelMock->shouldReceive('delete')
            ->once()
            ->andReturn(true);

        // Configurar o DB facade para testes
        DB::shouldReceive('beginTransaction')
            ->once();
        DB::shouldReceive('commit')
            ->once();

        // Executar o método a ser testado
        $result = $repositoryMock->delete($id);

        // Verificar o resultado
        $this->assertTrue($result);
    }

    /**
     * Testa o método delete() com ID inexistente
     *
     * @return void
     */
    #[Test]
    public function testDeleteWithInvalidId(): void
    {
        // Dados para o teste
        $id = 999;

        // Criar um mock parcial do repositório
        $repositoryMock = Mockery::mock(get_class($this->repository))->makePartial();

        // Configurar o comportamento do mock para find
        $repositoryMock->shouldReceive('find')
            ->once()
            ->with($id)
            ->andReturn(null);

        // Executar o método a ser testado
        $result = $repositoryMock->delete($id);

        // Verificar o resultado
        $this->assertFalse($result);
    }

    /**
     * Testa o método delete() com erro
     *
     * @return void
     */
    #[Test]
    public function testDeleteWithError(): void
    {
        // Dados para o teste
        $id = 1;

        // Criar um mock parcial do repositório
        $repositoryMock = Mockery::mock(get_class($this->repository))->makePartial();

        // Configurar o comportamento do mock para find
        $repositoryMock->shouldReceive('find')
            ->once()
            ->with($id)
            ->andReturn($this->modelMock);

        // Configurar o comportamento do mock para delete
        $this->modelMock->shouldReceive('delete')
            ->once()
            ->andThrow(new \Exception('Error deleting model'));

        // Configurar o DB facade para testes
        DB::shouldReceive('beginTransaction')
            ->once();
        DB::shouldReceive('rollBack')
            ->once();

        // Configurar o Log facade para testes
        Log::shouldReceive('error')
            ->once()
            ->with("Error deleting model: {$id}", Mockery::any());

        // Executar o método a ser testado
        $result = $repositoryMock->delete($id);

        // Verificar o resultado
        $this->assertFalse($result);
    }

    /**
     * Testa o método paginate()
     *
     * @return void
     */
    #[Test]
    public function testPaginate(): void
    {
        // Dados para o teste
        $perPage = 15;
        $paginatorMock = Mockery::mock(LengthAwarePaginator::class);

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('paginate')
            ->once()
            ->with($perPage)
            ->andReturn($paginatorMock);

        // Executar o método a ser testado
        $result = $this->repository->paginate($perPage);

        // Verificar o resultado
        $this->assertSame($paginatorMock, $result);
    }

    /**
     * Testa o método findBy()
     *
     * @return void
     */
    #[Test]
    public function testFindBy(): void
    {
        // Dados para o teste
        $criteria = ['status' => 'active', 'type' => 'user'];
        $collection = new Collection([$this->modelMock]);
        $queryMock = Mockery::mock('query');

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('query')
            ->once()
            ->andReturn($queryMock);

        $queryMock->shouldReceive('where')
            ->once()
            ->with('status', 'active')
            ->andReturnSelf();

        $queryMock->shouldReceive('where')
            ->once()
            ->with('type', 'user')
            ->andReturnSelf();

        $queryMock->shouldReceive('get')
            ->once()
            ->andReturn($collection);

        // Executar o método a ser testado
        $result = $this->repository->findBy($criteria);

        // Verificar o resultado
        $this->assertSame($collection, $result);
    }

    /**
     * Testa o método findByField()
     *
     * @return void
     */
    #[Test]
    public function testFindByField(): void
    {
        // Dados para o teste
        $field = 'status';
        $value = 'active';
        $collection = new Collection([$this->modelMock]);
        $queryMock = Mockery::mock('query');

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('where')
            ->once()
            ->with($field, $value)
            ->andReturn($queryMock);

        $queryMock->shouldReceive('get')
            ->once()
            ->andReturn($collection);

        // Executar o método a ser testado
        $result = $this->repository->findByField($field, $value);

        // Verificar o resultado
        $this->assertSame($collection, $result);
    }

    /**
     * Testa o método findWhereIn()
     *
     * @return void
     */
    #[Test]
    public function testFindWhereIn(): void
    {
        // Dados para o teste
        $field = 'id';
        $values = [1, 2, 3];
        $collection = new Collection([$this->modelMock]);
        $queryMock = Mockery::mock('query');

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('whereIn')
            ->once()
            ->with($field, $values)
            ->andReturn($queryMock);

        $queryMock->shouldReceive('get')
            ->once()
            ->andReturn($collection);

        // Executar o método a ser testado
        $result = $this->repository->findWhereIn($field, $values);

        // Verificar o resultado
        $this->assertSame($collection, $result);
    }

    /**
     * Testa o método getModel()
     *
     * @return void
     */
    #[Test]
    public function testGetModel(): void
    {
        // Executar o método a ser testado
        $result = $this->repository->getModel();

        // Verificar o resultado
        $this->assertSame($this->modelMock, $result);
    }

    /**
     * Testa o método restore() com ID inexistente
     *
     * @return void
     */
    #[Test]
    public function testRestoreWithInvalidId(): void
    {
        // Dados para o teste
        $id = 999;
        $queryMock = Mockery::mock('query');

        // Criar um mock parcial do repositório
        $repositoryMock = Mockery::mock(get_class($this->repository))->makePartial();

        // Configurar o modelo no repositório mock
        $reflection = new \ReflectionClass($repositoryMock);
        $property = $reflection->getProperty('model');
        $property->setAccessible(true);
        $property->setValue($repositoryMock, $this->modelMock);

        // Configurar o comportamento do mock para method_exists
        // Não precisamos mockar method_exists, apenas garantir que withTrashed seja chamado
        $this->modelMock->shouldReceive('withTrashed')
            ->andReturn($queryMock);

        $queryMock->shouldReceive('find')
            ->with($id)
            ->andReturn(null);

        // Executar o método a ser testado
        $result = $repositoryMock->restore($id);

        // Verificar o resultado
        $this->assertFalse($result);
    }


    /**
     * Testa o método forceDelete() com ID inexistente
     *
     * @return void
     */
    #[Test]
    public function testForceDeleteWithInvalidId(): void
    {
        // Dados para o teste
        $id = 999;
        $queryMock = Mockery::mock('query');

        // Criar um mock parcial do repositório
        $repositoryMock = Mockery::mock(get_class($this->repository))->makePartial();

        // Configurar o modelo no repositório mock
        $reflection = new \ReflectionClass($repositoryMock);
        $property = $reflection->getProperty('model');
        $property->setAccessible(true);
        $property->setValue($repositoryMock, $this->modelMock);

        // Configurar o comportamento do mock para withTrashed
        $this->modelMock->shouldReceive('withTrashed')
            ->andReturn($queryMock);

        $queryMock->shouldReceive('find')
            ->with($id)
            ->andReturn(null);

        // Executar o método a ser testado
        $result = $repositoryMock->forceDelete($id);

        // Verificar o resultado
        $this->assertFalse($result);
    }

    /**
     * Testa o método search()
     *
     * @return void
     */
    #[Test]
    public function testSearch(): void
    {
        // Dados para o teste
        $term = 'test';
        $columns = ['id', 'name'];
        $collection = new Collection([$this->modelMock]);
        $queryMock = Mockery::mock('query');

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('query')
            ->once()
            ->andReturn($queryMock);

        // Configurar o mock para ter a propriedade searchable
        $this->modelMock->shouldReceive('__isset')
            ->with('searchable')
            ->andReturn(true);
        $this->modelMock->shouldReceive('offsetExists')
            ->with('searchable')
            ->andReturn(true);
        $this->modelMock->shouldReceive('offsetGet')
            ->with('searchable')
            ->andReturn(['name', 'description', 'title']);

        $queryMock->shouldReceive('orWhere')
            ->times(3)
            ->andReturnSelf();

        $queryMock->shouldReceive('get')
            ->once()
            ->with($columns)
            ->andReturn($collection);

        // Executar o método a ser testado
        $result = $this->repository->search($term, $columns);

        // Verificar o resultado
        $this->assertSame($collection, $result);
    }

    /**
     * Testa o método with()
     *
     * @return void
     */
    #[Test]
    public function testWith(): void
    {
        // Dados para o teste
        $relations = ['posts', 'comments'];
        $queryMock = Mockery::mock('query');

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('with')
            ->once()
            ->with($relations)
            ->andReturn($queryMock);

        // Executar o método a ser testado
        $result = $this->repository->with($relations);

        // Verificar o resultado
        $this->assertSame($queryMock, $result);
    }

    /**
     * Testa o método getFiltered()
     *
     * @return void
     */
    #[Test]
    public function testGetFiltered(): void
    {
        // Dados para o teste
        $filters = [
            'status' => 'active',
            'type_like' => 'user',
            'age_gt' => 18,
            'age_lt' => 65,
            'created_at_between' => ['2023-01-01', '2023-12-31'],
            'tags' => ['tag1', 'tag2']
        ];
        $sortField = 'created_at';
        $sortDirection = 'desc';
        $collection = new Collection([$this->modelMock]);
        $queryMock = Mockery::mock('query');

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('query')
            ->once()
            ->andReturn($queryMock);

        // Configurar o comportamento para os filtros
        $queryMock->shouldReceive('where')
            ->once()
            ->with('status', 'active')
            ->andReturnSelf();

        $queryMock->shouldReceive('where')
            ->once()
            ->with('type', 'LIKE', '%user%')
            ->andReturnSelf();

        $queryMock->shouldReceive('where')
            ->once()
            ->with('age', '>', 18)
            ->andReturnSelf();

        $queryMock->shouldReceive('where')
            ->once()
            ->with('age', '<', 65)
            ->andReturnSelf();

        $queryMock->shouldReceive('whereBetween')
            ->once()
            ->with('created_at', ['2023-01-01', '2023-12-31'])
            ->andReturnSelf();

        $queryMock->shouldReceive('whereIn')
            ->once()
            ->with('tags', ['tag1', 'tag2'])
            ->andReturnSelf();

        // Configurar o comportamento para ordenação
        $queryMock->shouldReceive('orderBy')
            ->once()
            ->with($sortField, $sortDirection)
            ->andReturnSelf();

        $queryMock->shouldReceive('get')
            ->once()
            ->andReturn($collection);

        // Executar o método a ser testado
        $result = $this->repository->getFiltered($filters, $sortField, $sortDirection);

        // Verificar o resultado
        $this->assertSame($collection, $result);
    }

    /**
     * Testa o método getFiltered() com erro
     *
     * @return void
     */
    #[Test]
    public function testGetFilteredWithError(): void
    {
        // Dados para o teste
        $filters = ['status' => 'active'];
        $sortField = 'created_at';
        $sortDirection = 'desc';

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('query')
            ->once()
            ->andThrow(new \Exception('Error filtering models'));

        // Configurar o Log facade para testes
        Log::shouldReceive('error')
            ->once()
            ->with('Error filtering models', Mockery::any());

        // Executar o método a ser testado
        $result = $this->repository->getFiltered($filters, $sortField, $sortDirection);

        // Verificar o resultado
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertTrue($result->isEmpty());
    }

    /**
     * Testa o método loadRelations() com um modelo
     *
     * @return void
     */
    #[Test]
    public function testLoadRelationsWithModel(): void
    {
        // Dados para o teste
        $relations = ['posts', 'comments'];

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('load')
            ->once()
            ->with($relations)
            ->andReturn($this->modelMock);

        // Executar o método a ser testado
        $result = $this->repository->loadRelations($this->modelMock, $relations);

        // Verificar o resultado
        $this->assertSame($this->modelMock, $result);
    }

    /**
     * Testa o método loadRelations() com um ID
     *
     * @return void
     */
    #[Test]
    public function testLoadRelationsWithId(): void
    {
        // Dados para o teste
        $id = 1;
        $relations = ['posts', 'comments'];

        // Criar um mock parcial do repositório
        $repositoryMock = Mockery::mock(get_class($this->repository))->makePartial();

        // Configurar o comportamento do mock para find
        $repositoryMock->shouldReceive('find')
            ->once()
            ->with($id)
            ->andReturn($this->modelMock);

        // Configurar o comportamento do mock para load
        $this->modelMock->shouldReceive('load')
            ->once()
            ->with($relations)
            ->andReturn($this->modelMock);

        // Executar o método a ser testado
        $result = $repositoryMock->loadRelations($id, $relations);

        // Verificar o resultado
        $this->assertSame($this->modelMock, $result);
    }

    /**
     * Testa o método loadRelations() com um ID inválido
     *
     * @return void
     */
    #[Test]
    public function testLoadRelationsWithInvalidId(): void
    {
        // Dados para o teste
        $id = 999;
        $relations = ['posts', 'comments'];

        // Criar um mock parcial do repositório
        $repositoryMock = Mockery::mock(get_class($this->repository))->makePartial();

        // Configurar o comportamento do mock para find
        $repositoryMock->shouldReceive('find')
            ->once()
            ->with($id)
            ->andReturn(null);

        // Executar o método a ser testado
        $result = $repositoryMock->loadRelations($id, $relations);

        // Verificar o resultado
        $this->assertNull($result);
    }

    /**
     * Testa o método search() sem campos pesquisáveis definidos
     *
     * @return void
     */
    #[Test]
    public function testSearchWithoutSearchableFields(): void
    {
        // Dados para o teste
        $term = 'test';
        $columns = ['id', 'name'];
        $collection = new Collection([$this->modelMock]);
        $queryMock = Mockery::mock('query');

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('query')
            ->once()
            ->andReturn($queryMock);

        // Configurar o mock para não ter a propriedade searchable
        $this->modelMock->shouldReceive('__isset')
            ->with('searchable')
            ->andReturn(false);

        // Configurar o comportamento para os campos padrão
        $queryMock->shouldReceive('orWhere')
            ->times(3) // name, description, title
            ->andReturnSelf();

        $queryMock->shouldReceive('get')
            ->once()
            ->with($columns)
            ->andReturn($collection);

        // Executar o método a ser testado
        $result = $this->repository->search($term, $columns);

        // Verificar o resultado
        $this->assertSame($collection, $result);
    }

    /**
     * Testa o método search() com erro
     *
     * @return void
     */
    #[Test]
    public function testSearchWithError(): void
    {
        // Dados para o teste
        $term = 'test';
        $columns = ['id', 'name'];

        // Configurar o comportamento do mock
        $this->modelMock->shouldReceive('query')
            ->once()
            ->andThrow(new \Exception('Error searching models'));

        // Configurar o Log facade para testes
        Log::shouldReceive('error')
            ->once()
            ->with('Error searching models', Mockery::any());

        // Executar o método a ser testado
        $result = $this->repository->search($term, $columns);

        // Verificar o resultado
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertTrue($result->isEmpty());
    }
}
