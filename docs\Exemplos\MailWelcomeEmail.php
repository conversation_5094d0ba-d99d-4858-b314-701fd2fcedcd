<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

/**
 * Email de boas-vindas para novos usuários
 */
class WelcomeEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Usuário que receberá o email
     *
     * @var User
     */
    public $user;

    /**
     * Construtor
     *
     * @param User $user
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Constrói a mensagem
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Bem-vindo ao ' . config('app.name'))
            ->markdown('emails.welcome')
            ->with([
                'name' => $this->user->name,
                'appName' => config('app.name')
            ]);
    }
}
