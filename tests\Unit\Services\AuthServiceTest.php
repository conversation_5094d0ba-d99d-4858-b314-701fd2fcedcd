<?php

namespace Tests\Unit\Services;

use App\Models\UserModel;
use App\Repositories\UserRepository;
use App\Services\AuthService;
use App\Services\JwtService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Mockery;

class AuthServiceTest extends TestCase
{
    protected $authService;
    protected $mockUserRepo;
    protected $mockJwtService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockUserRepo = Mockery::mock(UserRepository::class);
        $this->mockJwtService = Mockery::mock(JwtService::class);

        $this->authService = new AuthService(
            $this->mockUserRepo,
            $this->mockJwtService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    #[Test]
    public function it_can_register_user()
    {
        // Dados de usuário
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $hashedPassword = 'hashed_password_123';

        // Mock do Hash facade
        Hash::shouldReceive('make')
            ->once()
            ->with('password123')
            ->andReturn($hashedPassword);

        // Criar um mock correto para UserModel (deve estender Model)
        $mockUser = Mockery::mock(UserModel::class);

        // Configurar os métodos que serão chamados
        $mockUser->shouldReceive('makeVisible')
            ->with(['created_at', 'updated_at'])
            ->andReturnSelf();

        $mockUser->shouldReceive('toArray')
            ->andReturn([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => $hashedPassword,
                'created_at' => '2023-01-01 12:00:00',
                'updated_at' => '2023-01-01 12:00:00'
            ]);

        // Mock do repository
        $this->mockUserRepo->shouldReceive('create')
            ->once()
            ->with(Mockery::on(function ($data) use ($hashedPassword) {
                return $data['password'] === $hashedPassword;
            }))
            ->andReturn($mockUser);

        // Executar
        $result = $this->authService->register($userData);

        // Verificar
        $this->assertEquals('Test User', $result['name']);
        $this->assertEquals('<EMAIL>', $result['email']);
    }

    #[Test]
    public function it_can_login_user()
    {
        // Dados de teste
        $credentials = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $tokenData = [
            'access_token' => 'test-token',
            'token_type' => 'bearer',
            'expires_in' => 3600,
            'refresh_token' => 'test-refresh-token'
        ];

        // Mock para Auth facade
        Auth::shouldReceive('attempt')
            ->once()
            ->with($credentials)
            ->andReturn(true);

        // Criar mock do UserModel
        $mockUser = Mockery::mock(UserModel::class);

        // Configurar Get Attribute
        $mockUser->shouldReceive('getAttribute')
            ->with('id')
            ->andReturn(1);

        $mockUser->shouldReceive('getAttribute')
            ->with('email')
            ->andReturn('<EMAIL>');

        // Importante: Configurar SetAttribute para qualquer chamada
        $mockUser->shouldReceive('setAttribute')
            ->withAnyArgs()
            ->andReturnSelf();

        // Mock para Auth::user()
        Auth::shouldReceive('user')
            ->once()
            ->andReturn($mockUser);

        // Mock para JwtService
        $this->mockJwtService->shouldReceive('generateToken')
            ->once()
            ->with(Mockery::type(UserModel::class))
            ->andReturn($tokenData);

        // Executar
        $result = $this->authService->login($credentials);

        // Verificar
        $this->assertEquals($tokenData, $result);
    }

    #[Test]
    public function it_can_get_authenticated_user()
    {
        // Mock para Auth facade
        $mockUser = Mockery::mock(UserModel::class);
        $mockUser->shouldReceive('makeVisible')
            ->once()
            ->with(['created_at', 'updated_at'])
            ->andReturnSelf();

        $mockUser->shouldReceive('toArray')
            ->once()
            ->andReturn([
                'id' => 1,
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'created_at' => '2023-01-01 12:00:00',
                'updated_at' => '2023-01-01 12:00:00'
            ]);

        Auth::shouldReceive('user')
            ->once()
            ->andReturn($mockUser);

        // Executar
        $result = $this->authService->getAuthenticatedUser();

        // Verificar
        $this->assertEquals(1, $result['id']);
        $this->assertEquals('Test User', $result['name']);
    }

    #[Test]
    public function it_can_logout_user()
    {
        // Mock para Auth
        $mockUser = Mockery::mock(UserModel::class);
        $mockUser->shouldReceive('getAttribute')
            ->with('id')
            ->andReturn(1);

        Auth::shouldReceive('user')
            ->once()
            ->andReturn($mockUser);

        Auth::shouldReceive('getToken->get')
            ->once()
            ->andReturn('current-token');

        Auth::shouldReceive('logout')
            ->once()
            ->andReturn(true);

        // Mock para JwtService
        $this->mockJwtService->shouldReceive('invalidateToken')
            ->once()
            ->with(1, 'current-token')
            ->andReturn(true);

        // Executar
        $result = $this->authService->logout();

        // Verificar
        $this->assertTrue($result);
    }

    #[Test]
    public function it_can_logout_from_all_devices()
    {
        // Mock para Auth
        $mockUser = Mockery::mock(UserModel::class);
        $mockUser->shouldReceive('getAttribute')
            ->with('id')
            ->andReturn(1);

        Auth::shouldReceive('user')
            ->once()
            ->andReturn($mockUser);

        // Mock para JwtService
        $this->mockJwtService->shouldReceive('invalidateAllTokens')
            ->once()
            ->with(1)
            ->andReturn(true);

        // Executar
        $result = $this->authService->logoutFromAllDevices();

        // Verificar
        $this->assertTrue($result);
    }
}
