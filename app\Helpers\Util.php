<?php

namespace App\Helpers;

use Carbon\Carbon;
use Illuminate\Support\Str;

class Util
{
    /**
     * Formata data por extenso
     */
    public function getDataAtualExtenso()
    {
        return Carbon::now()->locale('pt_BR')->translatedFormat('j \\d\\e F \\d\\e Y');
    }

    /**
     * Formata um valor para moeda brasileira
     */
    public function formatCurrency($value)
    {
        return 'R$ ' . number_format($value, 2, ',', '.');
    }

    /**
     * Converte data no formato MySQL (yyyy-mm-dd) para o formato brasileiro (dd/mm/yyyy)
     */
    public function formatDateMySQLToBR($date)
    {
        // Verifica se a data está no formato esperado (yyyy-mm-dd)
        if (preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
            return date('d/m/Y', strtotime($date));
        }

        // Caso o formato seja inválido, lança uma exceção
        throw new Exception("Formato de data inválido: $date");
    }

    /**
     * Converte data brasileira (dd/mm/yyyy) para o formato padrão MySQL (yyyy-mm-dd)
     */
    public function formatDateBRToMySQL($date)
    {
        if (preg_match('/\d{2}\/\d{2}\/\d{4}/', $date)) {
            $date = str_replace('/', '-', $date);
            return date('Y-m-d', strtotime($date));
        }

        return date('Y-m-d', strtotime($date));
    }


    /**
     * Remove caracteres especiais de uma string
     */
    public function removeSpecialChars($string)
    {
        return preg_replace('/[^a-zA-Z0-9]/', '', $string);
    }

    /**
     * Verifica se um CPF é válido
     */
    public function validarCPF($cpf)
    {
        // Remove caracteres não numéricos
        $cpf = preg_replace('/[^0-9]/is', '', $cpf);

        // Verifica se o CPF tem 11 dígitos ou se é uma sequência inválida
        if (strlen($cpf) != 11 || preg_match('/(\d)\1{10}/', $cpf)) {
            return false;
        }

        // Calcula os dígitos verificadores
        for ($t = 9; $t < 11; $t++) {
            $d = 0;
            for ($c = 0; $c < $t; $c++) {
                $d += $cpf[$c] * (($t + 1) - $c);
            }
            $d = ((10 * $d) % 11) % 10;
            if ($cpf[$c] != $d) {
                return false;
            }
        }

        return true;
    }

    /**
     * Verifica se um CNPJ é válido
     */
    public function validarCNPJ($cnpj)
    {
        // Remove caracteres não numéricos
        $cnpj = preg_replace('/[^0-9]/', '', $cnpj);

        // Verifica se o CNPJ tem 14 dígitos ou se é uma sequência inválida
        if (strlen($cnpj) != 14 || preg_match('/(\d)\1{13}/', $cnpj)) {
            return false;
        }

        // Valida os dois dígitos verificadores
        for ($t = 12; $t < 14; $t++) {
            $d = 0;
            $pos = $t - 7;
            for ($c = 0; $c < $t; $c++) {
                $d += $cnpj[$c] * $pos--;
                if ($pos < 2) {
                    $pos = 9;
                }
            }
            $d = ((10 * $d) % 11) % 10;
            if ($cnpj[$c] != $d) {
                return false;
            }
        }

        return true;
    }

    /**
     * Aplica uma máscara a um valor
     */
    public static function aplicarMascara($value, $mask)
    {
        $mascarado = '';
        $k = 0;

        for ($i = 0; $i < strlen($mask); $i++) {
            if ($mask[$i] === '#') {
                if (isset($value[$k])) {
                    $mascarado .= $value[$k++];
                }
            } else {
                $mascarado .= $mask[$i];
            }
        }

        return $mascarado;
    }

    /**
     * Aplica a máscara padrão de CPF
     */
    public static function mascararCPF($cpf)
    {
        return self::aplicarMascara($cpf, '###.###.###-##');
    }

    /**
     * Aplica a máscara padrão de CNPJ
     */
    public static function mascararCNPJ($cnpj)
    {
        return self::aplicarMascara($cnpj, '##.###.###/####-##');
    }

    /**
     * Gera um UUID v4 (aleatório)
     */
    public function gerarUUID()
    {
        return Str::uuid()->toString();
    }

    /**
     * Valida a extensão do arquivo enviado.
     *
     * @param string $fileName O nome do arquivo enviado.
     * @return bool Retorna true se a extensão for válida, caso contrário false.
     */
    public static function validateExtension($fileName)
    {
        // Obtém as extensões permitidas do .env
        $allowedExtensions = explode(',', env('UPLOAD_ALLOWED_EXTENSIONS', ''));
        // Obtém a extensão do arquivo em letras minúsculas
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        // Verifica se a extensão está na lista de permitidas
        return in_array($extension, $allowedExtensions);
    }


    /**
     * Valida o tamanho do arquivo enviado.
     *
     * @param UploadedFile $file O arquivo enviado.
     * @return bool Retorna true se o tamanho for permitido, caso contrário false.
     */
    public static function validateFileSize($file)
    {
        // Obtém o tamanho máximo permitido (em KB) do .env
        $maxSize = env('UPLOAD_MAX_SIZE', 2048); // Valor padrão de 2048 KB
        // Converte o tamanho máximo para bytes e compara com o tamanho do arquivo
        return $file->getSize() <= $maxSize * 1024;
    }

    /**
     * Valida o número máximo de arquivos enviados.
     *
     * @param array $files Lista de arquivos enviados.
     * @return bool Retorna true se o número de arquivos for permitido, caso contrário false.
     */
    public static function validateFileCount($files)
    {
        // Obtém o número máximo de arquivos permitidos do .env
        $maxFiles = env('UPLOAD_MAX_FILES', 5); // Valor padrão de 5 arquivos
        // Verifica se o número de arquivos enviados é menor ou igual ao limite
        return count($files) <= $maxFiles;
    }

    /**
     * Retorna o caminho para armazenar os arquivos enviados.
     *
     * @return string O caminho para o diretório de uploads.
     */
    public static function getUploadPath()
    {
        // Obtém o caminho configurado no .env ou usa o padrão
        return env('UPLOAD_PATH', 'storage/app/uploads');
    }

    /**
     * Gera a URL para acessar o arquivo enviado.
     *
     * @param string $fileName O nome do arquivo enviado.
     * @return string A URL para acessar o arquivo.
     */
    public static function getUploadUrl($fileName)
    {
        // Obtém a URL base configurada no .env e concatena com o nome do arquivo
        return env('UPLOAD_URL', 'http://localhost:8000/uploads') . '/' . $fileName;
    }

    /**
     * Valida o tamanho total dos arquivos enviados em uma requisição POST.
     *
     * @param array $files Lista de arquivos enviados.
     * @return bool Retorna true se o tamanho total for permitido, caso contrário false.
     */
    public static function validatePostSize($files)
    {
        // Obtém o tamanho máximo permitido para POST (em KB)
        $maxPostSize = env('POST_MAX_SIZE', 8192); // Valor padrão de 8MB

        // Calcula o tamanho total dos arquivos
        $totalSize = array_reduce($files, function ($carry, $file) {
            return $carry + $file->getSize(); // Soma os tamanhos dos arquivos
        }, 0);

        // Valida se o tamanho total é menor ou igual ao limite configurado
        return $totalSize <= $maxPostSize * 1024; // Converte KB para bytes
    }
}
