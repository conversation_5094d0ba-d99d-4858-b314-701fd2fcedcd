<?php

namespace App\Middleware;

use Closure;
use Illuminate\Http\Request;
use PHPOpenSourceSaver\JWTAuth\Exceptions\TokenExpiredException;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use Symfony\Component\HttpFoundation\Response;

class JwtRefreshMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $token = JWTAuth::parseToken();
            $user = $token->authenticate();

            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User not found',
                    'code' => 404
                ], 404);
            }
        } catch (TokenExpiredException $e) {
            try {
                // Tentar renovar o token
                $token = JWTAuth::parseToken()->refresh();

                // Adicionar o novo token ao cabeçalho da resposta
                return $next($request)->withHeaders([
                    'Authorization' => 'Bearer ' . $token,
                    'Access-Control-Expose-Headers' => 'Authorization'
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Token cannot be refreshed, please login again',
                    'code' => 401
                ], 401);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'code' => 401
            ], 401);
        }

        return $next($request);
    }
}
