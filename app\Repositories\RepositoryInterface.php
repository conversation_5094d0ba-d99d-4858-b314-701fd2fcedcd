<?php

namespace App\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\LazyCollection;
use Illuminate\Database\Eloquent\Builder;

interface RepositoryInterface
{
    /* Operações básicas */
    public function find($id): ?Model;
    public function findOrFail($id): Model;
    public function findAll(): Collection;

    /* CRUD */
    public function create(array $data): Model;
    public function update($id, array $data): ?Model;
    public function delete($id): bool;
    public function forceDelete($id): bool;

    /* Paginação */
    public function paginate(int $perPage = 15, array $columns = ['*']): LengthAwarePaginator;
    public function cursorPaginate(int $perPage = 15, array $columns = ['*']);
    public function lazy(): LazyCollection;

    /* Buscas */
    public function findBy(array $criteria): Collection;
    public function findOneBy(array $criteria): ?Model;
    public function findWhereIn(string $field, array $values): Collection;

    /* Operações em massa */
    public function bulkInsert(array $data): bool;
    public function bulkUpdate(array $ids, array $data): int;

    /* Metadados */
    public function getModel(): Model;
    public function newQuery(): Builder;
    public function count(): int;

    /* Escopos */
    public function withCriteria(array $criteria): self;

    /* Cache */
    public function withCache(int $ttl = 60): RepositoryInterface;
}
