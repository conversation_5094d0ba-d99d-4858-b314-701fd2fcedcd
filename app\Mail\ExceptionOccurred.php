<?php

namespace App\Mail;

use Throwable;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ExceptionOccurred extends Mailable
{
    use Queueable, SerializesModels;

    public $exception;
    public $url;
    public $service;

    /**
     * Cria uma nova instância da mensagem.
     *
     * @param Throwable $exception A exceção capturada
     * @param string $url A URL onde ocorreu a exceção
     * @param string $service O serviço onde ocorreu a exceção
     * @return void
     */
    public function __construct(Throwable $exception, $url, $service)
    {
        $this->exception = $exception;
        $this->url = $url;
        $this->service = $service;
    }

    public function build()
    {
        return $this->view('emails.exception_occurred')
            ->with([
                'exception' => $this->exception,
                'url' => $this->url,
                'service' => $this->service,
            ]);
    }
}
