<?php

namespace App\Exceptions\JWT;

class TokenBlacklistException extends \Exception
{
    protected $token;

    public function __construct(string $message = "Erro ao verificar blacklist", string $token = '', \Throwable $previous = null)
    {
        $this->token = $token;
        parent::__construct($message, 0, $previous);
    }

    public function getTokenPreview(): string
    {
        return substr($this->token, 0, 10) . '...';
    }

    public function getContext(): array
    {
        return [
            'token' => $this->getTokenPreview(),
        ];
    }
}
