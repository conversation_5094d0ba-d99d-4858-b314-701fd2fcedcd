<?php

namespace Tests\Unit\Repositories;

use App\Models\UserModel;
use App\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Collection;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Mockery;

class UserRepositoryTest extends TestCase
{
    protected $repository;
    protected $modelMock;

    protected function setUp(): void
    {
        parent::setUp();
        $this->modelMock = Mockery::mock(UserModel::class);
        $this->repository = new UserRepository($this->modelMock);
    }

    #[Test]
    public function it_can_find_user_by_email()
    {
        $email = '<EMAIL>';
        $userMock = Mockery::mock(UserModel::class);

        $this->modelMock->shouldReceive('where')
            ->once()
            ->with('email', $email)
            ->andReturnSelf();

        $this->modelMock->shouldReceive('first')
            ->once()
            ->andReturn($userMock);

        $result = $this->repository->findByEmail($email);

        $this->assertSame($userMock, $result);
    }

    // Outros métodos específicos de UserRepository...
}
