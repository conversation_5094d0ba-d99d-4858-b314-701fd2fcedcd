<?php

namespace App\Responses;

use Illuminate\Http\JsonResponse;

interface ResponseInterface
{
    /* Códigos HTTP - Sucesso */
    public const HTTP_OK = 200;
    public const HTTP_CREATED = 201;
    public const HTTP_NO_CONTENT = 204;

    /* Códigos HTTP - Redirecionamento */
    public const HTTP_MOVED_PERMANENTLY = 301;
    public const HTTP_FOUND = 302;

    /* Códigos HTTP - Erros do Cliente */
    public const HTTP_BAD_REQUEST = 400;
    public const HTTP_UNAUTHORIZED = 401;
    public const HTTP_FORBIDDEN = 403;
    public const HTTP_NOT_FOUND = 404;
    public const HTTP_METHOD_NOT_ALLOWED = 405;
    public const HTTP_UNPROCESSABLE_ENTITY = 422;

    /* Códigos HTTP - Erros do Servidor */
    public const HTTP_INTERNAL_SERVER_ERROR = 500;
    public const HTTP_BAD_GATEWAY = 502;
    public const HTTP_SERVICE_UNAVAILABLE = 503;

    /* Mensa<PERSON> padrão */
    public const MSG_SUCCESS = 'Operação realizada com sucesso';
    public const MSG_CREATED = 'Registro criado com sucesso';
    public const MSG_NO_CONTENT = 'Sem conteúdo para retornar';
    public const MSG_BAD_REQUEST = 'Requisição inválida';
    public const MSG_UNAUTHORIZED = 'Acesso não autorizado';
    public const MSG_FORBIDDEN = 'Acesso proibido';
    public const MSG_NOT_FOUND = 'Registro não encontrado';
    public const MSG_METHOD_NOT_ALLOWED = 'Método não permitido';
    public const MSG_VALIDATION_ERROR = 'Erro de validação';
    public const MSG_SERVER_ERROR = 'Erro interno do servidor';
    public const MSG_BAD_GATEWAY = 'Erro de gateway';
    public const MSG_SERVICE_UNAVAILABLE = 'Serviço indisponível';

    /* Métodos principais */
    public function success($data = null, string $message = self::MSG_SUCCESS, int $status = self::HTTP_OK): JsonResponse;
    public function created($data = null, string $message = self::MSG_CREATED): JsonResponse;
    public function noContent(string $message = self::MSG_NO_CONTENT): JsonResponse;

    /* Redirecionamentos */
    public function redirect(string $url, bool $permanent = false): JsonResponse;

    /* Erros do cliente */
    public function badRequest(string $message = self::MSG_BAD_REQUEST, $errors = null): JsonResponse;
    public function unauthorized(string $message = self::MSG_UNAUTHORIZED): JsonResponse;
    public function forbidden(string $message = self::MSG_FORBIDDEN): JsonResponse;
    public function notFound(string $message = self::MSG_NOT_FOUND): JsonResponse;
    public function methodNotAllowed(string $message = self::MSG_METHOD_NOT_ALLOWED): JsonResponse;
    public function validationError($errors, string $message = self::MSG_VALIDATION_ERROR): JsonResponse;

    /* Erros do servidor */
    public function serverError(string $message = self::MSG_SERVER_ERROR): JsonResponse;
    public function badGateway(string $message = self::MSG_BAD_GATEWAY): JsonResponse;
    public function serviceUnavailable(string $message = self::MSG_SERVICE_UNAVAILABLE): JsonResponse;

    /* Paginação */
    public function paginated($items, int $total, int $perPage, int $currentPage): JsonResponse;
}
