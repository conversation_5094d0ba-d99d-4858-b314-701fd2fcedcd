<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log; // Adicionado o import do Log
use Illuminate\Support\Str;
use App\Models\UserModel;
use Carbon\Carbon;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use App\Exceptions\JWT\TokenInvalidationException;
use App\Exceptions\JWT\TokenBlacklistException;
use App\Exceptions\JWT\TokenRefreshException;
use App\Exceptions\JWT\InvalidRefreshTokenException;

/**
 * @OA\Schema(
 *     schema="TokenPair",
 *     title="Token Pair",
 *     description="Par de tokens de acesso e refresh",
 *     @OA\Property(property="access_token", type="string", example="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."),
 *     @OA\Property(property="token_type", type="string", example="bearer"),
 *     @OA\Property(property="expires_in", type="integer", example=3600),
 *     @OA\Property(property="refresh_token", type="string", example="eyJ1c2VyX2lkIjoxLCJhY2Nlc3NfdG9rZW4i...")
 * )
 *
 * @OA\Schema(
 *     schema="Session",
 *     title="Session",
 *     description="Informações de sessão ativa",
 *     @OA\Property(property="device", type="string", example="Mozilla/5.0 (Windows NT 10.0; Win64; x64)..."),
 *     @OA\Property(property="ip", type="string", example="***********"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T00:00:00.000000Z"),
 *     @OA\Property(property="expires_at", type="string", format="date-time", example="2023-01-01T01:00:00.000000Z"),
 *     @OA\Property(property="last_activity", type="string", format="date-time", example="2023-01-01T00:30:00.000000Z"),
 *     @OA\Property(property="platform", type="string", example="windows")
 * )
 */
class JwtService
{
    /**
     * Gera um novo token JWT
     *
     * @param UserModel $user
     * @param array $customClaims
     * @return array
     */
    public function generateToken(UserModel $user, array $customClaims = []): array
    {
        // Claims padrão
        $claims = [
            'user_id' => $user->id,
            'email' => $user->email,
            'device' => request()->userAgent(),
            'ip' => request()->ip(),
        ];

        // Adicionar roles se existirem
        if (method_exists($user, 'roles') && $user->roles !== null) {
            $claims['roles'] = $user->roles->pluck('name')->toArray();
        } else {
            $claims['roles'] = [];
        }

        // Adicionar permissions se existirem
        if (method_exists($user, 'getAllPermissions')) {
            $permissions = $user->getAllPermissions();
            if ($permissions !== null) {
                $claims['permissions'] = $permissions->pluck('name')->toArray();
            } else {
                $claims['permissions'] = [];
            }
        } else {
            $claims['permissions'] = [];
        }

        // Mesclar com claims personalizados
        $claims = array_merge($claims, $customClaims);

        // Gerar token
        $token = JWTAuth::claims($claims)->fromUser($user);

        // Registrar token no cache para controle de sessões ativas
        $this->registerActiveToken($user->id, $token);

        return [
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => auth()->factory()->getTTL() * 60,
            'refresh_token' => $this->generateRefreshToken($user->id, $token),
        ];
    }

    /**
     * Gera um refresh token
     *
     * @param int $userId
     * @param string $accessToken
     * @return string
     */
    public function generateRefreshToken(int $userId, string $accessToken): string
    {
        $refreshTtl = Config::get('jwt.refresh_ttl', 20160); // 2 semanas por padrão
        $expiresAt = Carbon::now()->addMinutes($refreshTtl);

        // Adiciona fingerprint do dispositivo
        $deviceFingerprint = hash('sha256', request()->userAgent() . request()->ip());

        $tokenId = Str::random(40);

        $payload = [
            'user_id' => $userId,
            'access_token' => $accessToken, // Para poder invalidar o token antigo quando refreshed
            'token_id' => $tokenId,
            'expires_at' => $expiresAt->toIso8601String(),
            'fingerprint' => $deviceFingerprint,
        ];

        // Armazenar o refresh token no cache para validação posterior
        $cacheKey = 'refresh_token:' . $payload['token_id'];
        Cache::put($cacheKey, $payload, $expiresAt);

        // Retornar o token codificado
        return base64_encode(json_encode($payload));
    }

    /**
     * Registra um token ativo para um usuário
     *
     * @param int $userId
     * @param string $token
     * @return void
     */
    public function registerActiveToken(int $userId, string $token): void
    {
        $ttl = auth()->factory()->getTTL();
        $expiresAt = Carbon::now()->addMinutes($ttl);

        // Enriquecer com informações de auditoria
        $sessionData = [
            'token' => $token,
            'device' => request()->userAgent(),
            'ip' => request()->ip(),
            'created_at' => Carbon::now()->toIso8601String(),
            'expires_at' => $expiresAt->toIso8601String(),
            'last_activity' => Carbon::now()->toIso8601String(),
            'last_activity_ip' => request()->ip(),
            'issued_for' => request()->path(), // Para qual rota o token foi emitido
            'platform' => $this->detectPlatform(request()->userAgent()),
            'location' => $this->getLocationFromIp(request()->ip()),
        ];

        // Armazenar no cache
        $cacheKey = 'user_tokens:' . $userId;
        $userTokens = Cache::get($cacheKey, []);
        $userTokens[$token] = $sessionData;

        // Limpar tokens expirados
        $userTokens = array_filter($userTokens, function ($session) {
            return Carbon::parse($session['expires_at'])->isFuture();
        });

        Cache::put($cacheKey, $userTokens, Carbon::now()->addDays(30));

        // Registrar no log para auditoria
        Log::info('Token JWT gerado', [
            'user_id' => $userId,
            'ip' => request()->ip(),
            'device' => request()->userAgent(),
            'expires_at' => $expiresAt->toIso8601String()
        ]);
    }

    /**
     * Detecta a plataforma a partir do User-Agent
     *
     * @param string $userAgent
     * @return string
     */
    private function detectPlatform(?string $userAgent): string
    {
        if (!$userAgent) return 'unknown';

        if (preg_match('/android/i', $userAgent)) {
            return 'android';
        } elseif (preg_match('/iphone|ipad|ipod/i', $userAgent)) {
            return 'ios';
        } elseif (preg_match('/windows/i', $userAgent)) {
            return 'windows';
        } elseif (preg_match('/macintosh|mac os x/i', $userAgent)) {
            return 'mac';
        } elseif (preg_match('/linux/i', $userAgent)) {
            return 'linux';
        }

        return 'other';
    }

    /**
     * Obtém informações de localização a partir do IP
     * Nota: Implementação simples, considere usar um serviço real de geolocalização
     *
     * @param string $ip
     * @return array
     */
    private function getLocationFromIp(?string $ip): array
    {
        // Implementação básica, na produção você usaria um serviço como MaxMind GeoIP
        if ($ip === '127.0.0.1' || $ip === '::1' || !$ip) {
            return [
                'country' => 'Local',
                'city' => 'Development',
                'is_known_proxy' => false
            ];
        }

        // Para um serviço real, você faria algo como:
        // $result = GeoIP::getLocation($ip);

        // Retornando dados fictícios para exemplo
        return [
            'country' => 'Unknown',
            'city' => 'Unknown',
            'is_known_proxy' => false
        ];
    }

    /**
     * Invalida um token específico de um usuário
     *
     * @param int $userId
     * @param string $token
     * @return bool
     * @throws TokenInvalidationException
     */
    public function invalidateToken(int $userId, string $token): bool
    {
        try {
            // Adicionar à blacklist do JWT
            JWTAuth::setToken($token)->invalidate();

            // Remover do cache de sessões ativas
            $cacheKey = 'user_tokens:' . $userId;
            $userTokens = Cache::get($cacheKey, []);

            if (isset($userTokens[$token])) {
                unset($userTokens[$token]);
                Cache::put($cacheKey, $userTokens, Carbon::now()->addDays(30));
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Erro ao invalidar token', [
                'user_id' => $userId,
                'token' => substr($token, 0, 10) . '...',
                'error' => $e->getMessage()
            ]);

            throw new TokenInvalidationException(
                "Falha ao invalidar token: {$e->getMessage()}",
                $userId,
                $token,
                $e
            );
        }
    }



    /**
     * Invalida todos os tokens de um usuário
     *
     * @param int $userId
     * @return bool
     * @throws TokenInvalidationException
     */
    public function invalidateAllTokens(int $userId): bool
    {
        try {
            $cacheKey = 'user_tokens:' . $userId;
            $userTokens = Cache::get($cacheKey, []);

            foreach ($userTokens as $token => $data) {
                try {
                    JWTAuth::setToken($token)->invalidate();
                } catch (\Exception $e) {
                    // Ignorar erros de tokens já inválidos
                }
            }

            // Limpar o cache
            Cache::forget($cacheKey);

            return true;
        } catch (\Exception $e) {
            \Log::error('Erro ao invalidar todos os tokens', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw new TokenInvalidationException(
                "Falha ao invalidar todos os tokens: {$e->getMessage()}",
                $userId,
                '',
                $e
            );
        }
    }

    /**
     * Obtém todas as sessões ativas de um usuário
     *
     * @param int $userId
     * @return array
     */
    public function getActiveSessions(int $userId): array
    {
        $cacheKey = 'user_tokens:' . $userId;
        $userTokens = Cache::get($cacheKey, []);

        // Remover o token em si por segurança
        $sessions = [];
        foreach ($userTokens as $token => $data) {
            $session = $data;
            unset($session['token']); // Não expor o token
            $sessions[] = $session;
        }

        return $sessions;
    }

    /**
     * Verifica se um token está na lista negra
     *
     * @param string $token
     * @return bool
     * @throws TokenBlacklistException
     */
    public function isTokenBlacklisted(string $token): bool
    {
        try {
            return JWTAuth::getBlacklist()->has($token);
        } catch (\Exception $e) {
            \Log::error('Erro ao verificar blacklist', [
                'token' => substr($token, 0, 10) . '...',
                'error' => $e->getMessage()
            ]);

            throw new TokenBlacklistException(
                "Falha ao verificar blacklist: {$e->getMessage()}",
                $token,
                $e
            );
        }
    }

    /**
     * Atualiza um token usando um refresh token
     *
     * @param string $refreshToken
     * @return array|null
     * @throws TokenRefreshException
     * @throws InvalidRefreshTokenException
     */
    public function refreshToken(string $refreshToken): ?array
    {
        try {
            // Verificar se o refresh token é válido
            $payload = $this->decodeRefreshToken($refreshToken);

            if (!isset($payload['user_id'])) {
                throw new InvalidRefreshTokenException(
                    "Token não contém identificação de usuário",
                    "missing_user_id",
                    $refreshToken
                );
            }

            // Buscar o usuário
            $user = $this->findUserById($payload['user_id']);

            if (!$user) {
                throw new InvalidRefreshTokenException(
                    "Usuário não encontrado",
                    "user_not_found",
                    $refreshToken
                );
            }

            // Invalidar o token antigo se estiver presente
            if (isset($payload['access_token'])) {
                try {
                    $this->invalidateToken($user->id, $payload['access_token']);
                } catch (TokenInvalidationException $e) {
                    // Log mas continua, pois o token pode já estar inválido
                    \Log::warning("Falha ao invalidar token antigo durante refresh", $e->getContext());
                }
            }

            // Gerar um novo token
            return $this->generateToken($user);
        } catch (InvalidRefreshTokenException $e) {
            // Rethrow as it's already the correct type
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Erro ao atualizar token', [
                'error' => $e->getMessage(),
                'refresh_token' => substr($refreshToken, 0, 10) . '...'
            ]);

            throw new TokenRefreshException(
                "Falha ao atualizar token: {$e->getMessage()}",
                $refreshToken,
                $e
            );
        }
    }


    public function cleanupExpiredTokens(): int
    {
        $cleanupCount = 0;
        $users = $this->getUsersForCleanup(); // Método que pode ser mockado

        foreach ($users as $user) {
            $cacheKey = 'user_tokens:' . $user->id;
            $userTokens = Cache::get($cacheKey, []);

            if (empty($userTokens)) continue;

            $originalCount = count($userTokens);
            $userTokens = array_filter($userTokens, function ($session) {
                return Carbon::parse($session['expires_at'])->isFuture();
            });

            // Se alguns tokens foram removidos, atualize o cache
            if (count($userTokens) < $originalCount) {
                Cache::put($cacheKey, $userTokens, Carbon::now()->addDays(30));
                $cleanupCount += ($originalCount - count($userTokens));
            }
        }

        return $cleanupCount;
    }


    /**
     * Obtém todos os usuários para limpeza de tokens
     * (Método criado para permitir testes unitários)
     *
     * @return \Illuminate\Support\Collection
     */
    public function getUsersForCleanup()
    {
        return UserModel::all(['id']);
    }

    /**
     * Busca um usuário pelo ID
     * (Método criado para permitir testes unitários)
     *
     * @param int $userId
     * @return UserModel|null
     */
    public function findUserById(int $userId)
    {
        return UserModel::find($userId);
    }


    /**
     * Decodifica um refresh token
     *
     * @param string $refreshToken
     * @return array|null
     * @throws InvalidRefreshTokenException
     */
    private function decodeRefreshToken(string $refreshToken): ?array
    {
        try {
            // Decodificar o token
            $decoded = json_decode(base64_decode($refreshToken), true);

            // Verificar se o token é válido
            if (!$decoded) {
                throw new InvalidRefreshTokenException(
                    "Formato de token inválido",
                    "invalid_format",
                    $refreshToken
                );
            }

            if (!isset($decoded['user_id']) || !isset($decoded['expires_at'])) {
                throw new InvalidRefreshTokenException(
                    "Token não contém os campos necessários",
                    "missing_fields",
                    $refreshToken
                );
            }

            // Verificar se o token expirou
            if (Carbon::parse($decoded['expires_at'])->isPast()) {
                throw new InvalidRefreshTokenException(
                    "Token expirado",
                    "expired",
                    $refreshToken
                );
            }

            // Verificar se o token está no cache (validação adicional)
            $cacheKey = 'refresh_token:' . $decoded['token_id'];
            $cachedToken = Cache::get($cacheKey);

            if (!$cachedToken) {
                throw new InvalidRefreshTokenException(
                    "Token não encontrado no cache ou já utilizado",
                    "not_in_cache",
                    $refreshToken
                );
            }

            return $decoded;
        } catch (InvalidRefreshTokenException $e) {
            // Rethrow as it's already the correct type
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Erro ao decodificar refresh token', [
                'error' => $e->getMessage(),
                'refresh_token' => substr($refreshToken, 0, 10) . '...'
            ]);

            throw new InvalidRefreshTokenException(
                "Erro ao processar token: {$e->getMessage()}",
                "processing_error",
                $refreshToken,
                $e
            );
        }
    }
}
