<?php

use App\Middleware\ApiCache;
use App\Middleware\ApiLogger;
use App\Middleware\ApiMiddleware;
use App\Middleware\AuthenticateApi;
use App\Middleware\ForceJsonResponse;
use App\Middleware\JwtMiddleware;
use App\Middleware\JwtRefreshMiddleware;
use App\Middleware\JwtVerify;
use App\Middleware\QueryLogger;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Middleware global
        $middleware->append(ForceJsonResponse::class);

        // Middleware específico para API
        $middleware->api([
            ApiMiddleware::class,
            ApiLogger::class,
            QueryLogger::class,
        ]);

        // Middleware para grupos específicos
        $middleware->group('api-throttle', [
            'throttle:60,1',
        ]);

        $middleware->group('saml', [
            //\App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
        ]);

        // Middleware para rotas específicas
        $middleware->alias([
            'auth.api' => AuthenticateApi::class,
            'cache.api' => ApiCache::class,
            'jwt.verify' => JwtVerify::class,
            'jwt.middleware' => JwtMiddleware::class,
            'jwt.refresh' => JwtRefreshMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (AuthenticationException $e, Request $request) {
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => $e->getMessage(),
                ], 401);
            }
        });
    })
    ->create();
