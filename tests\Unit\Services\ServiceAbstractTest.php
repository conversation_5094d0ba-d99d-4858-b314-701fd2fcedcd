<?php

namespace Tests\Unit\Services;

use App\Repositories\RepositoryInterface;
use App\Services\ServiceAbstract;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Mockery;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use ReflectionClass;
use Exception;

#[CoversClass(\App\Services\ServiceAbstract::class)]
#[Group('services')]
class ServiceAbstractTest extends TestCase
{
    /**
     * Mock do repositório
     * 
     * @var \Mockery\MockInterface
     */
    protected $repositoryMock;

    /**
     * Instância do serviço para testes
     * 
     * @var ServiceAbstract
     */
    protected $service;

    /**
     * Configuração inicial para cada teste
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Criar mock do repositório
        $this->repositoryMock = Mockery::mock(RepositoryInterface::class);

        // Configurar o mock para getModel() que é usado em vários testes
        $modelMock = Mockery::mock(Model::class);
        $modelMock->shouldReceive('getTable')->andReturn('test_table')->byDefault();
        $this->repositoryMock->shouldReceive('getModel')
            ->andReturn($modelMock)
            ->byDefault();

        // Criar uma implementação concreta da classe abstrata para testes
        $this->service = $this->createConcreteService();

        // Configurar o Event facade para testes
        Event::fake();
    }

    /**
     * Cria uma implementação concreta do ServiceAbstract para testes
     *
     * @return ServiceAbstract
     */
    protected function createConcreteService(): ServiceAbstract
    {
        return new class($this->repositoryMock) extends ServiceAbstract {
            protected $createRules = [
                'name' => 'required|string|max:255',
                'email' => 'required|email'
            ];

            protected $updateRules = [
                'name' => 'string|max:255',
                'email' => 'email'
            ];

            protected $validationMessages = [
                'name.required' => 'O nome é obrigatório.',
                'email.email' => 'O email deve ser um endereço válido.'
            ];

            // Métodos para expor métodos protegidos para testes
            public function callPrepareForCreate(array $data): array
            {
                return $this->prepareForCreate($data);
            }

            public function callPrepareForUpdate(array $data): array
            {
                return $this->prepareForUpdate($data);
            }

            public function callSafeOperation(callable $callback, string $errorMessage, $default = null)
            {
                return $this->safeOperation($callback, $errorMessage, $default);
            }

            public function callToResource($data, $resourceClass = null)
            {
                return $this->toResource($data, $resourceClass);
            }
        };
    }

    /**
     * Limpa os mocks após cada teste
     *
     * @return void
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Fornece dados para testes de validação na criação
     */
    public static function createValidationDataProvider(): array
    {
        return [
            'dados_validos' => [
                [
                    'name' => 'Test User',
                    'email' => '<EMAIL>'
                ],
                true,
                null
            ],
            'email_invalido' => [
                [
                    'name' => 'Test User',
                    'email' => 'invalid-email'
                ],
                false,
                ValidationException::class
            ],
            'nome_ausente' => [
                [
                    'email' => '<EMAIL>'
                ],
                false,
                ValidationException::class
            ]
        ];
    }

    /**
     * Testa o método createWithValidation com diferentes cenários
     * 
     * @dataProvider createValidationDataProvider
     */
    #[Test]
    #[DataProvider('createValidationDataProvider')]
    public function testCreateWithValidation(array $data, bool $shouldSucceed, ?string $expectedException): void
    {
        if ($expectedException) {
            $this->expectException($expectedException);
        }

        if ($shouldSucceed) {
            $modelMock = Mockery::mock(Model::class);
            $this->repositoryMock->shouldReceive('create')
                ->once()
                ->with($data)
                ->andReturn($modelMock);

            $result = $this->service->createWithValidation($data);
            $this->assertSame($modelMock, $result);

            Event::assertDispatched('model.created', function ($event, $model) use ($modelMock) {
                return $model === $modelMock;
            });

            // Verificar se o evento específico do modelo também foi disparado
            $modelClassName = class_basename($this->service->getModel());
            Event::assertDispatched($modelClassName . 'Created');
        } else {
            $this->service->createWithValidation($data);
        }
    }

    /**
     * Testa o método createWithValidation quando o repositório retorna null
     */
    #[Test]
    public function testCreateWithValidationWhenRepositoryReturnsNull(): void
    {
        $data = [
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ];

        $this->repositoryMock->shouldReceive('create')
            ->once()
            ->with($data)
            ->andReturn(null);

        $result = $this->service->createWithValidation($data);
        $this->assertNull($result);

        Event::assertNotDispatched('model.created');
    }

    /**
     * Fornece dados para testes de validação na atualização
     */
    public static function updateValidationDataProvider(): array
    {
        return [
            'dados_validos' => [
                1,
                [
                    'name' => 'Updated User',
                    'email' => '<EMAIL>'
                ],
                true,
                null
            ],
            'email_invalido' => [
                1,
                [
                    'name' => 'Updated User',
                    'email' => 'invalid-email-format'
                ],
                false,
                ValidationException::class
            ],
            'dados_vazios' => [
                1,
                [],
                true,
                null
            ]
        ];
    }

    /**
     * Testa o método updateWithValidation com diferentes cenários
     * 
     * @dataProvider updateValidationDataProvider
     */
    #[Test]
    #[DataProvider('updateValidationDataProvider')]
    public function testUpdateWithValidation($id, array $data, bool $shouldSucceed, ?string $expectedException): void
    {
        if ($expectedException) {
            $this->expectException($expectedException);
        }

        if ($shouldSucceed) {
            $modelMock = Mockery::mock(Model::class);
            $this->repositoryMock->shouldReceive('update')
                ->once()
                ->with($id, $data)
                ->andReturn($modelMock);

            $result = $this->service->updateWithValidation($id, $data);
            $this->assertSame($modelMock, $result);

            Event::assertDispatched('model.updated');

            // Verificar se o evento específico do modelo também foi disparado
            $modelClassName = class_basename($this->service->getModel());
            Event::assertDispatched($modelClassName . 'Updated');
        } else {
            $this->service->updateWithValidation($id, $data);
        }
    }

    /**
     * Testa o método updateWithValidation quando o repositório retorna null
     */
    #[Test]
    public function testUpdateWithValidationWhenRepositoryReturnsNull(): void
    {
        $id = 1;
        $data = [
            'name' => 'Updated User',
            'email' => '<EMAIL>'
        ];

        $this->repositoryMock->shouldReceive('update')
            ->once()
            ->with($id, $data)
            ->andReturn(null);

        $result = $this->service->updateWithValidation($id, $data);
        $this->assertNull($result);

        Event::assertNotDispatched('model.updated');
    }

    /**
     * Fornece dados para testes de exclusão
     */
    public static function deleteDataProvider(): array
    {
        return [
            'exclusao_com_sucesso' => [1, true],
            'exclusao_com_falha' => [999, false]
        ];
    }

    /**
     * Testa o método deleteAndNotify com diferentes cenários
     * 
     * @dataProvider deleteDataProvider
     */
    #[Test]
    #[DataProvider('deleteDataProvider')]
    public function testDeleteAndNotify($id, bool $shouldSucceed): void
    {
        $this->repositoryMock->shouldReceive('delete')
            ->once()
            ->with($id)
            ->andReturn($shouldSucceed);

        $result = $this->service->deleteAndNotify($id);

        $this->assertSame($shouldSucceed, $result);

        if ($shouldSucceed) {
            Event::assertDispatched('model.deleted');

            // Verificar se o evento específico do modelo também foi disparado
            $modelClassName = class_basename($this->service->getModel());
            Event::assertDispatched($modelClassName . 'Deleted');
        } else {
            Event::assertNotDispatched('model.deleted');
        }
    }

    /**
     * Fornece dados para testes de verificação de existência
     */
    public static function existsDataProvider(): array
    {
        return [
            'registro_existe' => [1, true],
            'registro_nao_existe' => [999, false]
        ];
    }

    /**
     * Testa o método exists com diferentes cenários
     * 
     * @dataProvider existsDataProvider
     */
    #[Test]
    #[DataProvider('existsDataProvider')]
    public function testExists($id, bool $exists): void
    {
        $this->repositoryMock->shouldReceive('find')
            ->once()
            ->with($id)
            ->andReturn($exists ? Mockery::mock(Model::class) : null);

        $result = $this->service->exists($id);

        $this->assertSame($exists, $result);
    }

    /**
     * Fornece dados para testes de transformação
     */
    public static function transformDataProvider(): array
    {
        return [
            'sem_transformador' => [
                ['status' => 'active'],
                null
            ],
            'com_transformador' => [
                ['status' => 'active'],
                function ($item) {
                    return ['id' => $item->id, 'name' => strtoupper($item->name)];
                }
            ]
        ];
    }

    /**
     * Testa o método getTransformed sem transformador
     */
    #[Test]
    public function testGetTransformedWithoutTransformer(): void
    {
        $collection = new Collection(['item1', 'item2']);

        $this->repositoryMock->shouldReceive('findBy')
            ->once()
            ->with(['status' => 'active'])
            ->andReturn($collection);

        $result = $this->service->getTransformed(['status' => 'active']);

        $this->assertSame($collection, $result);
    }

    /**
     * Testa o método getTransformed com transformador
     */
    #[Test]
    public function testGetTransformedWithTransformer(): void
    {
        $items = [
            (object)['id' => 1, 'name' => 'Item 1'],
            (object)['id' => 2, 'name' => 'Item 2']
        ];

        $collection = new Collection($items);

        $this->repositoryMock->shouldReceive('findBy')
            ->once()
            ->with(['status' => 'active'])
            ->andReturn($collection);

        $transformer = function ($item) {
            return ['id' => $item->id, 'name' => strtoupper($item->name)];
        };

        $result = $this->service->getTransformed(['status' => 'active'], $transformer);

        $expected = [
            ['id' => 1, 'name' => 'ITEM 1'],
            ['id' => 2, 'name' => 'ITEM 2']
        ];

        $this->assertEquals($expected, $result);
    }

    /**
     * Fornece dados para testes de paginação
     */
    public static function paginationDataProvider(): array
    {
        return [
            'paginacao_padrao' => [15],
            'paginacao_personalizada' => [10]
        ];
    }

    /**
     * Testa o método getPaginatedAndTransformed sem transformador
     * 
     * @dataProvider paginationDataProvider
     */
    #[Test]
    #[DataProvider('paginationDataProvider')]
    public function testGetPaginatedAndTransformedWithoutTransformer(int $perPage): void
    {
        $paginatorMock = Mockery::mock(LengthAwarePaginator::class);

        $this->repositoryMock->shouldReceive('paginate')
            ->once()
            ->with($perPage)
            ->andReturn($paginatorMock);

        $result = $this->service->getPaginatedAndTransformed($perPage);

        $this->assertSame($paginatorMock, $result);
    }

    /**
     * Testa o método getPaginatedAndTransformed com transformador
     */
    #[Test]
    public function testGetPaginatedAndTransformedWithTransformer(): void
    {
        $perPage = 10;
        $currentPage = 1;
        $total = 25;

        // Criar um mock do paginator
        $items = [
            (object)['id' => 1, 'name' => 'Item 1'],
            (object)['id' => 2, 'name' => 'Item 2']
        ];

        $paginatorMock = Mockery::mock(LengthAwarePaginator::class);
        $paginatorMock->shouldReceive('items')
            ->andReturn($items)
            ->byDefault();
        $paginatorMock->shouldReceive('total')
            ->andReturn($total)
            ->byDefault();
        $paginatorMock->shouldReceive('perPage')
            ->andReturn($perPage)
            ->byDefault();
        $paginatorMock->shouldReceive('currentPage')
            ->andReturn($currentPage)
            ->byDefault();

        $this->repositoryMock->shouldReceive('paginate')
            ->once()
            ->with($perPage)
            ->andReturn($paginatorMock);

        $transformer = function ($item) {
            return ['id' => $item->id, 'name' => strtoupper($item->name)];
        };

        $result = $this->service->getPaginatedAndTransformed($perPage, $transformer);

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    /**
     * Testa o método getFiltered
     */
    #[Test]
    public function testGetFiltered(): void
    {
        $filters = ['status' => 'active'];
        $sortField = 'created_at';
        $sortDirection = 'desc';
        $collection = new Collection(['item1', 'item2']);

        $this->repositoryMock->shouldReceive('getFiltered')
            ->once()
            ->with($filters, $sortField, $sortDirection)
            ->andReturn($collection);

        $result = $this->service->getFiltered($filters, $sortField, $sortDirection);

        $this->assertSame($collection, $result);
    }

    /**
     * Fornece dados para testes de carregamento de relações
     */
    public static function loadRelationsDataProvider(): array
    {
        return [
            'com_id' => [
                true,
                ['posts', 'comments']
            ],
            'com_modelo' => [
                false,
                ['users', 'roles']
            ]
        ];
    }

    /**
     * Testa o método loadRelations com diferentes cenários
     * 
     * @dataProvider loadRelationsDataProvider
     */
    #[Test]
    #[DataProvider('loadRelationsDataProvider')]
    public function testLoadRelations(bool $useId, array $relations): void
    {
        $modelMock = Mockery::mock(Model::class);

        if ($useId) {
            $id = 1;
            $this->repositoryMock->shouldReceive('loadRelations')
                ->once()
                ->with($id, $relations)
                ->andReturn($modelMock);

            $result = $this->service->loadRelations($id, $relations);
        } else {
            $this->repositoryMock->shouldReceive('loadRelations')
                ->once()
                ->with($modelMock, $relations)
                ->andReturn($modelMock);

            $result = $this->service->loadRelations($modelMock, $relations);
        }

        $this->assertSame($modelMock, $result);
    }

    /**
     * Testa o método loadRelations quando o modelo não é encontrado
     */
    #[Test]
    public function testLoadRelationsWithNonExistentModel(): void
    {
        $id = 999;
        $relations = ['posts', 'comments'];

        $this->repositoryMock->shouldReceive('loadRelations')
            ->once()
            ->with($id, $relations)
            ->andReturn(null);

        $result = $this->service->loadRelations($id, $relations);

        $this->assertNull($result);
    }

    /**
     * Testa o método getModel
     */
    #[Test]
    public function testGetModel(): void
    {
        $modelMock = Mockery::mock(Model::class);

        $this->repositoryMock->shouldReceive('getModel')
            ->once()
            ->andReturn($modelMock);

        $result = $this->service->getModel();

        $this->assertSame($modelMock, $result);
    }

    /**
     * Testa o método prepareForCreate
     */
    #[Test]
    public function testPrepareForCreate(): void
    {
        $data = ['name' => 'Test', 'email' => '<EMAIL>'];

        $result = $this->service->callPrepareForCreate($data);

        // Por padrão, prepareForCreate retorna os dados sem modificação
        $this->assertEquals($data, $result);
    }

    /**
     * Testa o método prepareForUpdate
     */
    #[Test]
    public function testPrepareForUpdate(): void
    {
        $data = ['name' => 'Test', 'email' => '<EMAIL>'];

        $result = $this->service->callPrepareForUpdate($data);

        // Por padrão, prepareForUpdate retorna os dados sem modificação
        $this->assertEquals($data, $result);
    }

    /**
     * Testa o método safeOperation com sucesso
     */
    #[Test]
    public function testSafeOperationSuccess(): void
    {
        $callback = function () {
            return 'success';
        };

        $result = $this->service->callSafeOperation($callback, 'Error message', 'default');

        $this->assertEquals('success', $result);
    }

    /**
     * Testa o método safeOperation com exceção
     */
    #[Test]
    public function testSafeOperationWithException(): void
    {
        // Configurar o mock do Log
        Log::shouldReceive('error')
            ->once()
            ->with('Error message', Mockery::type('array'));

        $callback = function () {
            throw new Exception('Test exception');
        };

        $result = $this->service->callSafeOperation($callback, 'Error message', 'default');

        $this->assertEquals('default', $result);
    }

    /**
     * Testa o método toResource sem classe de recurso
     */
    #[Test]
    public function testToResourceWithoutResourceClass(): void
    {
        $data = ['name' => 'Test'];

        $result = $this->service->callToResource($data);

        $this->assertEquals($data, $result);
    }

    /**
     * Testa o método toResource com classe de recurso e coleção
     */
    #[Test]
    public function testToResourceWithResourceClassAndCollection(): void
    {
        // Criar uma classe de recurso mock
        $resourceClass = new class {
            public static function collection($data)
            {
                return ['transformed' => true, 'data' => $data];
            }
        };

        $collection = new Collection(['item1', 'item2']);

        $result = $this->service->callToResource($collection, get_class($resourceClass));

        $this->assertEquals(['transformed' => true, 'data' => $collection], $result);
    }

    /**
     * Testa o método toResource com classe de recurso e modelo único
     */
    #[Test]
    public function testToResourceWithResourceClassAndSingleModel(): void
    {
        // Criar um modelo mock
        $model = Mockery::mock(Model::class);

        // Criar uma classe de recurso mock
        $resourceClass = new class() {
            public $model;

            public function __construct($model = null)
            {
                $this->model = $model;
            }
        };

        // Obter o nome da classe
        $resourceClassName = get_class($resourceClass);

        // Chamar o método toResource
        $result = $this->service->callToResource($model, $resourceClassName);

        // Verificar se o resultado é uma instância da classe de recurso
        $this->assertInstanceOf($resourceClassName, $result);

        // Verificar se o modelo foi passado corretamente para o construtor
        $this->assertSame($model, $result->model);
    }
}
