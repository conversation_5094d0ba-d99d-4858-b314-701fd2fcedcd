<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Implementação</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #2980b9;
            margin-top: 40px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        h3 {
            color: #3498db;
            margin-top: 25px;
        }

        .code-section {
            margin-bottom: 50px;
        }

        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', Courier, monospace;
            overflow-x: auto;
        }

        .example {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .example h4 {
            margin-top: 0;
            color: #2980b9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        table,
        th,
        td {
            border: 1px solid #ddd;
        }

        th {
            background-color: #f2f2f2;
            padding: 12px;
            text-align: left;
        }

        td {
            padding: 10px;
        }

        .best-practice {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }

        .bad-practice {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }

        .note {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
            padding: 10px;
        }

        footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }

        #sumario {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }

        #sumario ul {
            list-style-type: none;
            padding-left: 20px;
        }

        #sumario ul li {
            margin-bottom: 8px;
        }

        #sumario a {
            color: #2980b9;
            text-decoration: none;
        }

        #sumario a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <header>
        <h1 style="color: white; border-bottom: none;">Manual de Implementação</h1>
        <p>Guia prático para implementação do sistema seguindo os padrões da arquitetura</p>
    </header>

    <section id="sumario">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#ambiente">2. Configuração do Ambiente de Desenvolvimento</a></li>
            <li><a href="#estrutura">3. Estrutura do Projeto</a></li>
            <li><a href="#implementacao-camadas">4. Guias de Implementação por Camada</a>
                <ul>
                    <li><a href="#camada-apresentacao">4.1. Camada de Apresentação</a></li>
                    <li><a href="#camada-aplicacao">4.2. Camada de Aplicação</a></li>
                    <li><a href="#camada-dominio">4.3. Camada de Domínio</a></li>
                    <li><a href="#camada-infraestrutura">4.4. Camada de Infraestrutura</a></li>
                </ul>
            </li>
            <li><a href="#padroes">5. Padrões de Codificação</a></li>
            <li><a href="#excecoes">6. Tratamento de Exceções</a></li>
            <li><a href="#testes">7. Testes</a></li>
            <li><a href="#validacao">8. Validação de Dados</a></li>
            <li><a href="#versionamento">9. Controle de Versão</a></li>
            <li><a href="#boas-praticas">10. Boas Práticas</a></li>
        </ul>
    </section>

    <section id="introducao">
        <h2>1. Introdução</h2>
        <p>Este manual fornece diretrizes práticas para implementação do sistema seguindo a arquitetura em camadas
            definida. Destina-se a desenvolvedores que estão trabalhando no projeto e busca garantir consistência,
            qualidade e manutenibilidade do código.</p>

        <p>Os exemplos e recomendações deste manual são baseados nas melhores práticas de desenvolvimento com Laravel 12
            e seguem os princípios arquiteturais descritos no Manual de Arquitetura.</p>

        <div class="note">
            <p><strong>Nota:</strong> Este documento pressupõe familiaridade com os conceitos apresentados no Manual de
                Arquitetura. Recomenda-se a leitura prévia daquele documento antes de prosseguir com este guia de
                implementação.</p>
        </div>
    </section>

    <section id="ambiente">
        <h2>2. Configuração do Ambiente de Desenvolvimento</h2>

        <h3>2.1. Requisitos de Sistema</h3>
        <ul>
            <li>PHP 8.2 ou superior</li>
            <li>Composer 2.5 ou superior</li>
            <li>MySQL 8.0 ou superior / PostgreSQL 15 ou superior</li>
            <li>Node.js 18 LTS ou superior (para assets)</li>
            <li>Git 2.30 ou superior</li>
            <li>Redis 7.0 ou superior (para cache e filas)</li>
        </ul>

        <h3>2.2. Instalação do Projeto</h3>
        <div class="code-block">
            # Clone o repositório
            git clone https://github.com/empresa/projeto.git
            cd projeto

            # Instale as dependências PHP
            composer install

            # Copie o arquivo de ambiente
            cp .env.example .env

            # Gere a chave da aplicação
            php artisan key:generate

            # Configure o banco de dados no arquivo .env
            # DB_CONNECTION=mysql
            # DB_HOST=127.0.0.1
            # DB_PORT=3306
            # DB_DATABASE=nome_do_banco
            # DB_USERNAME=usuario
            # DB_PASSWORD=senha

            # Execute as migrações
            php artisan migrate

            # Instale as dependências JavaScript
            npm install

            # Compile os assets
            npm run dev
        </div>

        <h3>2.3. Ferramentas Recomendadas</h3>
        <ul>
            <li><strong>IDE:</strong> PhpStorm, VS Code com extensões PHP</li>
            <li><strong>Debugging:</strong> Xdebug 3.0+</li>
            <li><strong>API Testing:</strong> Postman, Insomnia</li>
            <li><strong>Controle de Qualidade:</strong> PHPStan, PHP CS Fixer, Laravel Pint</li>
        </ul>

        <h3>2.4. Docker (Opcional)</h3>
        <p>O projeto oferece suporte para desenvolvimento com Docker através do Laravel Sail:</p>
        <div class="code-block">
            # Inicializar os contêineres de desenvolvimento
            ./vendor/bin/sail up -d

            # Executar comandos dentro do contêiner
            ./vendor/bin/sail artisan migrate
            ./vendor/bin/sail composer install
            ./vendor/bin/sail test
        </div>
    </section>

    <section id="estrutura">
        <h2>3. Estrutura do Projeto</h2>

        <p>Além da estrutura padrão do Laravel, nosso projeto utiliza diretórios adicionais para acomodar a arquitetura
            em camadas:</p>

        <div class="code-block">
            projeto/
            ├── app/ # Código da aplicação
            │ ├── Console/ # Comandos artisan
            │ ├── Contracts/ # Interfaces
            │ ├── DTOs/ # Data Transfer Objects
            │ ├── Exceptions/ # Exceções customizadas
            │ ├── Http/ # Camada de Apresentação
            │ │ ├── Controllers/ # Controllers
            │ │ ├── Middleware/ # Middlewares
            │ │ ├── Requests/ # Form Requests
            │ │ └── Resources/ # API Resources
            │ ├── Models/ # Modelos Eloquent
            │ ├── Repositories/ # Repositórios
            │ │ ├── Contracts/ # Interfaces de repositórios
            │ │ └── Eloquent/ # Implementações Eloquent
            │ ├── Services/ # Serviços de aplicação
            │ │ ├── External/ # Serviços externos
            │ │ └── Internal/ # Serviços internos
            │ ├── ValueObjects/ # Objetos de valor
            │ └── Providers/ # Service providers
            ├── config/ # Configurações
            ├── database/ # Migrations, seeders
            ├── public/ # Assets públicos
            ├── resources/ # Views, assets não compilados
            ├── routes/ # Definição de rotas
            ├── storage/ # Armazenamento (logs, cache)
            └── tests/ # Testes
            ├── Feature/ # Testes de feature
            ├── Integration/ # Testes de integração
            └── Unit/ # Testes unitários
        </div>
    </section>

    <section id="implementacao-camadas">
        <h2>4. Guias de Implementação por Camada</h2>

        <section id="camada-apresentacao">
            <h3>4.1. Camada de Apresentação</h3>

            <h4>4.1.1. Controllers</h4>
            <p>Os controllers devem ser enxutos, delegando a lógica de negócios para os services:</p>

            <div class="best-practice">
                <h4>Boa Prática</h4>
                <div class="code-block">
                    namespace App\Http\Controllers;

                    use App\Http\Requests\ProductRequest;
                    use App\Http\Resources\ProductResource;
                    use App\Services\ProductService;

                    class ProductController extends Controller
                    {
                    protected $productService;

                    public function __construct(ProductService $productService)
                    {
                    $this->productService = $productService;
                    }

                    public function store(ProductRequest $request)
                    {
                    $product = $this->productService->create($request->validated());

                    return response()->json([
                    'status' => 'success',
                    'message' => 'Produto criado com sucesso',
                    'data' => new ProductResource($product)
                    ], 201);
                    }
                    }
                </div>
            </div>

            <div class="bad-practice">
                <h4>Má Prática</h4>
                <div class="code-block">
                    namespace App\Http\Controllers;

                    use App\Models\Product;
                    use Illuminate\Http\Request;

                    class ProductController extends Controller
                    {
                    public function store(Request $request)
                    {
                    $request->validate([
                    'name' => 'required|string|max:255',
                    'price' => 'required|numeric|min:0',
                    ]);

                    // Lógica de negócio no controller ❌
                    $existingProduct = Product::where('name', $request->name)->first();
                    if ($existingProduct) {
                    return response()->json(['error' => 'Produto já existe'], 409);
                    }

                    $product = new Product();
                    $product->name = $request->name;
                    $product->price = $request->price;
                    $product->save();

                    // Sem padronização de resposta ❌
                    return response()->json($product, 201);
                    }
                    }
                </div>
            </div>

            <h4>4.1.2. Form Requests</h4>
            <p>Utilize Form Requests para validação de dados de entrada:</p>

            <div class="code-block">
                namespace App\Http\Requests;

                use Illuminate\Foundation\Http\FormRequest;

                class ProductRequest extends FormRequest
                {
                public function authorize()
                {
                return true;
                }

                public function rules()
                {
                return [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'category_id' => 'required|exists:categories,id'
                ];
                }

                public function messages()
                {
                return [
                'name.required' => 'O nome do produto é obrigatório',
                'price.numeric' => 'O preço deve ser um valor numérico',
                'category_id.exists' => 'A categoria selecionada não existe'
                ];
                }
                }
            </div>

            <h4>4.1.3. API Resources</h4>
            <p>Use API Resources para transformar modelos em respostas JSON:</p>

            <div class="code-block">
                namespace App\Http\Resources;

                use Illuminate\Http\Resources\Json\JsonResource;

                class ProductResource extends JsonResource
                {
                public function toArray($request)
                {
                return [
                'id' => $this->id,
                'name' => $this->name,
                'description' => $this->description,
                'price' => (float) $this->price,
                'formatted_price' => 'R$ ' . number_format($this->price, 2, ',', '.'),
                'category' => new CategoryResource($this->whenLoaded('category')),
                'created_at' => $this->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
                ];
                }
                }
            </div>

            <h4>4.1.4. Middlewares</h4>
            <p>Implemente middlewares para requisitos transversais como autenticação, autorização e logging:</p>

            <div class="code-block">
                namespace App\Http\Middleware;

                use Closure;
                use Illuminate\Http\Request;

                class LogApiRequests
                {
                public function handle(Request $request, Closure $next)
                {
                // Registrar início da requisição
                $startTime = microtime(true);

                // Continuar o processamento
                $response = $next($request);

                // Calcular tempo de execução
                $executionTime = microtime(true) - $startTime;

                // Registrar informações da requisição
                logger()->info('API Request', [
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'status' => $response->status(),
                'execution_time' => round($executionTime * 1000, 2) . 'ms',
                'user_id' => auth()->id() ?? 'guest'
                ]);

                return $response;
                }
                }
            </div>
        </section>

        <section id="camada-aplicacao">
            <h3>4.2. Camada de Aplicação</h3>

            <h4>4.2.1. Services</h4>
            <p>Os services implementam a lógica de negócio e orquestram operações entre repositórios:</p>

            <div class="code-block">
                namespace App\Services;

                use App\DTOs\ProductDTO;
                use App\Exceptions\ProductException;
                use App\Repositories\Contracts\ProductRepositoryInterface;
                use App\Repositories\Contracts\CategoryRepositoryInterface;
                use Illuminate\Support\Facades\DB;

                class ProductService
                {
                protected $productRepository;
                protected $categoryRepository;

                public function __construct(
                ProductRepositoryInterface $productRepository,
                CategoryRepositoryInterface $categoryRepository
                ) {
                $this->productRepository = $productRepository;
                $this->categoryRepository = $categoryRepository;
                }

                public function create(array $data)
                {
                // Verificar se produto já existe
                if ($this->productRepository->findByName($data['name'])) {
                throw new ProductException('Um produto com este nome já existe');
                }

                // Verificar se categoria existe
                if (!$this->categoryRepository->find($data['category_id'])) {
                throw new ProductException('Categoria não encontrada');
                }

                DB::beginTransaction();
                try {
                $product = $this->productRepository->create($data);

                // Lógica adicional, como criar histórico, notificar, etc.

                DB::commit();
                return $product;
                } catch (\Exception $e) {
                DB::rollBack();
                throw new ProductException('Erro ao criar produto: ' . $e->getMessage());
                }
                }
                }
            </div>

            <h4>4.2.2. DTOs (Data Transfer Objects)</h4>
            <p>Use DTOs para transferir dados estruturados entre camadas:</p>

            <div class="code-block">
                namespace App\DTOs;

                class ProductDTO
                {
                public $id;
                public $name;
                public $description;
                public $price;
                public $categoryId;

                public function __construct($id = null, $name = null, $description = null, $price = null, $categoryId =
                null)
                {
                $this->id = $id;
                $this->name = $name;
                $this->description = $description;
                $this->price = $price;
                $this->categoryId = $categoryId;
                }

                public static function fromArray(array $data)
                {
                return new self(
                $data['id'] ?? null,
                $data['name'] ?? null,
                $data['description'] ?? null,
                $data['price'] ?? null,
                $data['category_id'] ?? null
                );
                }

                public function toArray()
                {
                return [
                'id' => $this->id,
                'name' => $this->name,
                'description' => $this->description,
                'price' => $this->price,
                'category_id' => $this->categoryId
                ];
                }
                }
            </div>

            <h4>4.2.3. Validação a Nível de Serviço</h4>
            <p>Implemente validação de regras de negócio nos services:</p>

            <div class="code-block">
                namespace App\Services;

                use App\Exceptions\OrderException;

                class OrderService
                {
                // ...

                public function processPayment($orderId, $paymentData)
                {
                $order = $this->orderRepository->find($orderId);

                if (!$order) {
                throw new OrderException('Pedido não encontrado');
                }

                if ($order->status !== 'pending') {
                throw new OrderException('Apenas pedidos pendentes podem receber pagamentos');
                }

                if ($paymentData['amount'] < $order->total) {
                    throw new OrderException('Valor do pagamento inferior ao total do pedido');
                    }

                    // Continuar com processamento do pagamento...
                    }
                    }
            </div>
        </section>

        <section id="camada-dominio">
            <h3>4.3. Camada de Domínio</h3>

            <h4>4.3.1. Models</h4>
            <p>Os models representam as entidades de negócio e suas relações:</p>

            <div class="code-block">
                namespace App\Models;

                use Illuminate\Database\Eloquent\Factories\HasFactory;
                use Illuminate\Database\Eloquent\Model;
                use Illuminate\Database\Eloquent\SoftDeletes;

                class Product extends Model
                {
                use HasFactory, SoftDeletes;

                protected $fillable = [
                'name',
                'description',
                'price',
                'category_id',
                'sku',
                'is_active'
                ];

                protected $casts = [
                'price' => 'decimal:2',
                'is_active' => 'boolean',
                'created_at' => 'datetime',
                'updated_at' => 'datetime'
                ];

                // Relacionamentos
                public function category()
                {
                return $this->belongsTo(Category::class);
                }

                public function orderItems()
                {
                return $this->hasMany(OrderItem::class);
                }

                // Escopos
                public function scopeActive($query)
                {
                return $query->where('is_active', true);
                }

                public function scopeByCategory($query, $categoryId)
                {
                return $query->where('category_id', $categoryId);
                }

                // Mutators & Accessors
                public function getFormattedPriceAttribute()
                {
                return 'R$ ' . number_format($this->price, 2, ',', '.');
                }

                // Métodos de negócio
                public function isInStock()
                {
                return $this->stock_quantity > 0;
                }

                public function canBePurchased($quantity = 1)
                {
                return $this->is_active && $this->stock_quantity >= $quantity;
                }
                }
            </div>

            <h4>4.3.2. Value Objects</h4>
            <p>Implemente Value Objects para encapsular conceitos do domínio que são identificados por seus valores:</p>

            <div class="code-block">
                namespace App\ValueObjects;

                class Money
                {
                private $amount;
                private $currency;

                public function __construct(float $amount, string $currency = 'BRL')
                {
                if ($amount < 0) { throw new \InvalidArgumentException('Valor não pode ser negativo'); } $this->amount =
                    $amount;
                    $this->currency = $currency;
                    }

                    public function getAmount(): float
                    {
                    return $this->amount;
                    }

                    public function getCurrency(): string
                    {
                    return $this->currency;
                    }

                    public function add(Money $money): Money
                    {
                    if ($money->getCurrency() !== $this->currency) {
                    throw new \InvalidArgumentException('Moedas incompatíveis');
                    }

                    return new Money($this->amount + $money->getAmount(), $this->currency);
                    }

                    public function subtract(Money $money): Money
                    {
                    if ($money->getCurrency() !== $this->currency) {
                    throw new \InvalidArgumentException('Moedas incompatíveis');
                    }

                    $newAmount = $this->amount - $money->getAmount();
                    if ($newAmount < 0) { throw new \InvalidArgumentException('Resultado não pode ser negativo'); }
                        return new Money($newAmount, $this->currency);
                        }

                        public function format(): string
                        {
                        $symbols = [
                        'BRL' => 'R$',
                        'USD' => '$',
                        'EUR' => '€'
                        ];

                        $symbol = $symbols[$this->currency] ?? '';
                        return $symbol . ' ' . number_format($this->amount, 2, ',', '.');
                        }
                        }
            </div>

            <h4>4.3.3. Enums</h4>
            <p>Utilize Enums (PHP 8.1+) para representar conjuntos fixos de valores:</p>

            <div class="code-block">
                namespace App\Enums;

                enum OrderStatus: string
                {
                case PENDING = 'pending';
                case PAID = 'paid';
                case PROCESSING = 'processing';
                case SHIPPED = 'shipped';
                case DELIVERED = 'delivered';
                case CANCELED = 'canceled';
                case REFUNDED = 'refunded';

                public function label(): string
                {
                return match($this) {
                self::PENDING => 'Pendente',
                self::PAID => 'Pago',
                self::PROCESSING => 'Em processamento',
                self::SHIPPED => 'Enviado',
                self::DELIVERED => 'Entregue',
                self::CANCELED => 'Cancelado',
                self::REFUNDED => 'Reembolsado',
                };
                }

                public function color(): string
                {
                return match($this) {
                self::PENDING => 'yellow',
                self::PAID => 'blue',
                self::PROCESSING => 'purple',
                self::SHIPPED => 'indigo',
                self::DELIVERED => 'green',
                self::CANCELED => 'red',
                self::REFUNDED => 'gray',
                };
                }

                public function canTransitionTo(self $status): bool
                {
                return match($this) {
                self::PENDING => in_array($status, [self::PAID, self::CANCELED]),
                self::PAID => in_array($status, [self::PROCESSING, self::REFUNDED]),
                self::PROCESSING => in_array($status, [self::SHIPPED, self::CANCELED]),
                self::SHIPPED => in_array($status, [self::DELIVERED]),
                self::DELIVERED => in_array($status, [self::REFUNDED]),
                self::CANCELED, self::REFUNDED => false,
                };
                }
                }
            </div>
        </section>

        <section id="camada-infraestrutura">
            <h3>4.4. Camada de Infraestrutura</h3>

            <h4>4.4.1. Repositories</h4>
            <p>Implemente repositórios para abstrair o acesso aos dados:</p>

            <h5>Interface do Repositório</h5>
            <div class="code-block">
                namespace App\Repositories\Contracts;

                interface ProductRepositoryInterface
                {
                public function all();
                public function find($id);
                public function findByName($name);
                public function create(array $data);
                public function update($id, array $data);
                public function delete($id);
                public function findByCategory($categoryId);
                public function search(array $params);
                }
            </div>

            <h5>Implementação do Repositório</h5>
            <div class="code-block">
                namespace App\Repositories\Eloquent;

                use App\Models\Product;
                use App\Repositories\Contracts\ProductRepositoryInterface;
                use Illuminate\Support\Facades\Cache;

                class ProductRepository implements ProductRepositoryInterface
                {
                protected $model;

                public function __construct(Product $model)
                {
                $this->model = $model;
                }

                public function all()
                {
                return $this->model->all();
                }

                public function find($id)
                {
                return Cache::remember("product:{$id}", 3600, function () use ($id) {
                return $this->model->find($id);
                });
                }

                public function findByName($name)
                {
                return $this->model->where('name', $name)->first();
                }

                public function create(array $data)
                {
                $product = $this->model->create($data);
                $this->clearCache($product->id);
                return $product;
                }

                public function update($id, array $data)
                {
                $product = $this->model->find($id);
                if (!$product) {
                return false;
                }

                $product->update($data);
                $this->clearCache($id);
                return $product;
                }

                public function delete($id)
                {
                $this->clearCache($id);
                return $this->model->destroy($id);
                }

                public function findByCategory($categoryId)
                {
                return $this->model->where('category_id', $categoryId)->get();
                }

                public function search(array $params)
                {
                $query = $this->model->query();

                if (isset($params['name'])) {
                $query->where('name', 'like', "%{$params['name']}%");
                }

                if (isset($params['category_id'])) {
                $query->where('category_id', $params['category_id']);
                }

                if (isset($params['min_price'])) {
                $query->where('price', '>=', $params['min_price']);
                }

                if (isset($params['max_price'])) {
                $query->where('price', '<=', $params['max_price']); } if (isset($params['is_active'])) { $query->
                    where('is_active', $params['is_active']);
                    }

                    return $query->paginate($params['per_page'] ?? 15);
                    }

                    protected function clearCache($id)
                    {
                    Cache::forget("product:{$id}");
                    }
                    }
            </div>

            <div class="best-practice">
                <h4>Boas Práticas em Repositórios</h4>
                <ol>
                    <li>Implemente sempre uma interface para cada repositório</li>
                    <li>Utilize cache para consultas frequentes</li>
                    <li>Não esqueça de invalidar o cache quando os dados são alterados</li>
                    <li>Use paginação para conjuntos grandes de dados</li>
                    <li>Implemente filtros com base em parâmetros de busca</li>
                    <li>Mantenha a lógica de acesso a dados isolada nos repositórios</li>
                    <li>Utilize transações quando necessário</li>
                </ol>
            </div>

            <h4>4.4.2. External Services</h4>
            <p>Implemente serviços externos para integração com APIs de terceiros:</p>

            <div class="code-block">
                namespace App\Services\External;

                use GuzzleHttp\Client;
                use GuzzleHttp\Exception\GuzzleException;
                use App\Exceptions\ExternalServiceException;
                use Illuminate\Support\Facades\Log;

                class ShippingService
                {
                protected $client;
                protected $apiKey;
                protected $baseUrl;

                public function __construct(Client $client)
                {
                $this->client = $client;
                $this->apiKey = config('services.shipping.key');
                $this->baseUrl = config('services.shipping.url');
                }

                public function calculateShipping(string $zipCodeOrigin, string $zipCodeDestination, float $weight,
                array $dimensions)
                {
                try {
                $response = $this->client->post("{$this->baseUrl}/v1/shipping/calculate", [
                'headers' => [
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json'
                ],
                'json' => [
                'origin' => $zipCodeOrigin,
                'destination' => $zipCodeDestination,
                'weight' => $weight,
                'dimensions' => $dimensions
                ],
                'timeout' => 15
                ]);

                $result = json_decode($response->getBody()->getContents(), true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                throw new ExternalServiceException("Erro ao decodificar resposta do serviço de frete");
                }

                return $result;
                } catch (GuzzleException $e) {
                Log::error('Erro no serviço de frete', [
                'exception' => $e->getMessage(),
                'code' => $e->getCode()
                ]);

                throw new ExternalServiceException("Falha ao consultar serviço de frete: {$e->getMessage()}",
                $e->getCode(), $e);
                }
                }
                }
            </div>

            <h4>4.4.3. Cache</h4>
            <p>Implemente estratégias de cache para melhorar a performance:</p>

            <div class="code-block">
                namespace App\Services;

                use Illuminate\Support\Facades\Cache;
                use App\Repositories\Contracts\ProductRepositoryInterface;

                class CachedProductService
                {
                protected $repository;
                protected $cacheTime;

                public function __construct(ProductRepositoryInterface $repository)
                {
                $this->repository = $repository;
                $this->cacheTime = config('cache.times.products', 3600);
                }

                public function getFeatureProducts()
                {
                return Cache::remember('featured_products', $this->cacheTime, function () {
                return $this->repository->getFeatured();
                });
                }

                public function getProductsByCategory($categoryId)
                {
                return Cache::tags(['products', "category_{$categoryId}"])
                ->remember("products_by_category_{$categoryId}", $this->cacheTime, function () use ($categoryId) {
                return $this->repository->findByCategory($categoryId);
                });
                }

                public function clearCategoryCache($categoryId)
                {
                Cache::tags(["category_{$categoryId}"])->flush();
                }

                public function clearAllProductCache()
                {
                Cache::tags(['products'])->flush();
                }
                }
            </div>

            <h4>4.4.4. Jobs</h4>
            <p>Implemente jobs para processamento em background:</p>

            <div class="code-block">
                namespace App\Jobs;

                use Illuminate\Bus\Queueable;
                use Illuminate\Contracts\Queue\ShouldQueue;
                use Illuminate\Foundation\Bus\Dispatchable;
                use Illuminate\Queue\InteractsWithQueue;
                use Illuminate\Queue\SerializesModels;
                use App\Services\OrderService;
                use Illuminate\Support\Facades\Log;
                use App\Notifications\OrderShipped;

                class ProcessOrderShipmentJob implements ShouldQueue
                {
                use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

                protected $orderId;
                protected $shippingData;

                public $timeout = 120;
                public $tries = 3;

                public function __construct($orderId, array $shippingData)
                {
                $this->orderId = $orderId;
                $this->shippingData = $shippingData;
                }

                public function handle(OrderService $orderService)
                {
                try {
                $order = $orderService->findById($this->orderId);

                $result = $orderService->processShipment($order, $this->shippingData);

                // Notificar o usuário
                $order->user->notify(new OrderShipped($order));

                Log::info('Pedido enviado com sucesso', [
                'order_id' => $this->orderId,
                'tracking_code' => $result['tracking_code']
                ]);

                return $result;
                } catch (\Exception $e) {
                Log::error('Falha ao processar envio do pedido', [
                'order_id' => $this->orderId,
                'exception' => $e->getMessage()
                ]);

                if ($this->attempts() < $this->tries) {
                    $this->release(30 * $this->attempts());
                    }

                    throw $e;
                    }
                    }

                    public function failed(\Throwable $exception)
                    {
                    Log::critical('Falha definitiva no processamento de envio', [
                    'order_id' => $this->orderId,
                    'exception' => $exception->getMessage()
                    ]);

                    // Notificar a equipe sobre a falha
                    }
                    }
            </div>

            <h4>4.4.5. File Storage</h4>
            <p>Implemente serviços para gerenciar o armazenamento de arquivos:</p>

            <div class="code-block">
                namespace App\Services;

                use Illuminate\Contracts\Filesystem\Filesystem;
                use Illuminate\Http\UploadedFile;
                use Illuminate\Support\Str;

                class FileStorageService
                {
                protected $disk;
                protected $basePath;

                public function __construct(Filesystem $disk, string $basePath = 'uploads')
                {
                $this->disk = $disk;
                $this->basePath = $basePath;
                }

                public function storeFile(UploadedFile $file, string $directory = '', array $options = []): string
                {
                $path = $this->getStoragePath($directory);
                $filename = $this->generateFilename($file);

                $filePath = $this->disk->putFileAs(
                $path,
                $file,
                $filename,
                $options['visibility'] ?? 'public'
                );

                return $filePath;
                }

                public function deleteFile(string $path): bool
                {
                if ($this->disk->exists($path)) {
                return $this->disk->delete($path);
                }

                return false;
                }

                public function getUrl(string $path): string
                {
                return $this->disk->url($path);
                }

                protected function getStoragePath(string $directory): string
                {
                $path = $this->basePath;

                if ($directory) {
                $path .= '/' . trim($directory, '/');
                }

                return $path;
                }

                protected function generateFilename(UploadedFile $file): string
                {
                $extension = $file->getClientOriginalExtension();
                $name = Str::random(40);

                return "{$name}.{$extension}";
                }
                }
            </div>
        </section>

        <section id="padroes">
            <h2>5. Padrões de Codificação</h2>

            <h3>5.1. Convenções de Nomenclatura</h3>
            <table>
                <thead>
                    <tr>
                        <th>Elemento</th>
                        <th>Convenção</th>
                        <th>Exemplo</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Classes</td>
                        <td>PascalCase</td>
                        <td><code>ProductRepository</code>, <code>CreateUserService</code></td>
                    </tr>
                    <tr>
                        <td>Interfaces</td>
                        <td>PascalCase com prefixo "I" ou sufixo "Interface"</td>
                        <td><code>ProductRepositoryInterface</code>, <code>Searchable</code></td>
                    </tr>
                    <tr>
                        <td>Métodos</td>
                        <td>camelCase</td>
                        <td><code>findByEmail()</code>, <code>processPayment()</code></td>
                    </tr>
                    <tr>
                        <td>Variáveis/Propriedades</td>
                        <td>camelCase</td>
                        <td><code>$firstName</code>, <code>$orderItems</code></td>
                    </tr>
                    <tr>
                        <td>Constantes</td>
                        <td>UPPER_CASE com underscores</td>
                        <td><code>MAX_LOGIN_ATTEMPTS</code></td>
                    </tr>
                    <tr>
                        <td>Arquivos de Configuração</td>
                        <td>snake_case</td>
                        <td><code>auth.php</code>, <code>payment_gateways.php</code></td>
                    </tr>
                    <tr>
                        <td>Migrations</td>
                        <td>snake_case com data</td>
                        <td><code>2023_05_25_create_products_table.php</code></td>
                    </tr>
                    <tr>
                        <td>Tabelas de Banco</td>
                        <td>snake_case, plural</td>
                        <td><code>products</code>, <code>order_items</code></td>
                    </tr>
                    <tr>
                        <td>Colunas de Banco</td>
                        <td>snake_case</td>
                        <td><code>first_name</code>, <code>created_at</code></td>
                    </tr>
                </tbody>
            </table>

            <h3>5.2. PSR-12</h3>
            <p>O projeto segue o padrão PSR-12 para formatação de código. Alguns pontos importantes:</p>
            <ul>
                <li>Use 4 espaços para indentação (não use tabs)</li>
                <li>Linhas não devem ter mais que 120 caracteres</li>
                <li>A declaração de namespace deve ser seguida por uma linha em branco</li>
                <li>As chaves de abertura "{" devem estar na mesma linha da declaração</li>
                <li>As chaves de fechamento "}" devem estar em uma nova linha</li>
                <li>A visibilidade (public, protected, private) deve ser declarada em todos os métodos e propriedades
                </li>
            </ul>

            <h3>5.3. Documentação</h3>
            <p>Documente o código utilizando DocBlocks para classes, métodos e propriedades:</p>

            <div class="code-block">
                /**
                * Processa o pagamento de um pedido
                *
                * @param int $orderId ID do pedido a ser processado
                * @param array $paymentData Dados do pagamento (gateway, token, valor, etc)
                * @return array Retorna os dados da transação de pagamento
                * @throws PaymentException Quando ocorre um erro no processamento do pagamento
                */
                public function processPayment(int $orderId, array $paymentData): array
                {
                // Implementação...
                }
            </div>
        </section>

        <section id="excecoes">
            <h2>6. Tratamento de Exceções</h2>

            <h3>6.1. Hierarquia de Exceções</h3>
            <p>Implemente uma hierarquia de exceções para facilitar o tratamento específico de erros:</p>

            <div class="code-block">
                namespace App\Exceptions;

                class AppException extends \Exception {}

                // Exceções de domínio
                class DomainException extends AppException {}
                class OrderException extends DomainException {}
                class ProductException extends DomainException {}
                class PaymentException extends DomainException {}

                // Exceções de infraestrutura
                class InfrastructureException extends AppException {}
                class DatabaseException extends InfrastructureException {}
                class ExternalServiceException extends InfrastructureException {}
                class CacheException extends InfrastructureException {}

                // Exceções de aplicação
                class ApplicationException extends AppException {}
                class ValidationException extends ApplicationException {}
                class AuthorizationException extends ApplicationException {}
                class NotFoundException extends ApplicationException {}
            </div>

            <h3>6.2. Handler Global de Exceções</h3>
            <p>Customize o Handler para tratar diferentes tipos de exceções e retornar respostas adequadas:</p>

            <div class="code-block">
                namespace App\Exceptions;

                use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
                use Throwable;
                use Illuminate\Validation\ValidationException;
                use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
                use Illuminate\Auth\AuthenticationException;
                use Illuminate\Auth\Access\AuthorizationException;

                class Handler extends ExceptionHandler
                {
                protected $dontReport = [
                // Classes de exceção que não devem ser reportadas
                ];

                public function register()
                {
                $this->reportable(function (Throwable $e) {
                // Código para reportar a exceção
                });

                $this->renderable(function (Throwable $e, $request) {
                if ($request->expectsJson()) {
                return $this->handleApiException($e, $request);
                }
                });
                }

                private function handleApiException(Throwable $exception, $request)
                {
                // Trata ValidationException
                if ($exception instanceof ValidationException) {
                return response()->json([
                'status' => 'error',
                'message' => 'Erros de validação foram encontrados',
                'errors' => $exception->validator->errors()->toArray()
                ], 422);
                }

                // Trata exceções de autorização
                if ($exception instanceof AuthorizationException) {
                return response()->json([
                'status' => 'error',
                'message' => 'Você não tem permissão para realizar esta ação'
                ], 403);
                }

                // Trata exceções de autenticação
                if ($exception instanceof AuthenticationException) {
                return response()->json([
                'status' => 'error',
                'message' => 'Autenticação necessária'
                ], 401);
                }

                // Trata recurso não encontrado
                if ($exception instanceof NotFoundHttpException || $exception instanceof NotFoundException) {
                return response()->json([
                'status' => 'error',
                'message' => 'Recurso não encontrado'
                ], 404);
                }

                // Trata exceções de domínio do sistema
                if ($exception instanceof DomainException) {
                return response()->json([
                'status' => 'error',
                'message' => $exception->getMessage()
                ], 400);
                }

                // Trata exceções de serviços externos
                if ($exception instanceof ExternalServiceException) {
                return response()->json([
                'status' => 'error',
                'message' => 'Erro em serviço externo: ' . $exception->getMessage()
                ], 502);
                }

                // Para outras exceções, em ambiente de produção, retornar uma mensagem genérica
                if (app()->environment('production')) {
                return response()->json([
                'status' => 'error',
                'message' => 'Ocorreu um erro inesperado'
                ], 500);
                }

                // Em ambiente de desenvolvimento, retorna mais detalhes
                return response()->json([
                'status' => 'error',
                'message' => $exception->getMessage(),
                'exception' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTrace()
                ], 500);
                }
                }
            </div>

            <h3>6.3. Boas Práticas para Tratamento de Exceções</h3>
            <ol>
                <li>Lançe exceções específicas com mensagens descritivas</li>
                <li>Não engula exceções sem tratá-las adequadamente</li>
                <li>Registre (log) detalhes das exceções para facilitar o diagnóstico</li>
                <li>Nas camadas mais externas da aplicação, converta exceções técnicas em exceções de domínio
                    significativas</li>
                <li>Em APIs, retorne códigos HTTP apropriados para cada tipo de erro</li>
            </ol>
        </section>

        <section id="testes">
            <h2>7. Testes</h2>

            <h3>7.1. Tipos de Teste</h3>
            <ul>
                <li><strong>Testes Unitários</strong>: Testam componentes individuais isoladamente</li>
                <li><strong>Testes de Integração</strong>: Testam a integração entre componentes</li>
                <li><strong>Testes de Feature</strong>: Testam funcionalidades completas, incluindo HTTP requests</li>
                <li><strong>Testes de API</strong>: Testam endpoints de API e seus comportamentos</li>
            </ul>

            <h3>7.2. Estrutura de Diretórios de Teste</h3>
            <div class="code-block">
                tests/
                ├── Unit/ # Testes unitários
                │ ├── Services/ # Testes de serviços
                │ ├── Repositories/ # Testes de repositórios
                │ └── Models/ # Testes de modelos
                ├── Integration/ # Testes de integração
                │ ├── Services/ # Integração entre serviços
                │ └── Repositories/ # Integração com banco de dados
                └── Feature/ # Testes de feature
                ├── Controllers/ # Testes de controllers
                ├── API/ # Testes de API completos
                └── Auth/ # Testes de autenticação
            </div>

            <h3>7.3. Exemplo de Teste Unitário</h3>
            <div class="code-block">
                namespace Tests\Unit\Services;

                use App\Services\ProductService;
                use App\Repositories\Contracts\ProductRepositoryInterface;
                use App\Exceptions\ProductException;
                use Mockery;
                use Tests\TestCase;

                class ProductServiceTest extends TestCase
                {
                protected $productRepository;
                protected $service;

                protected function setUp(): void
                {
                parent::setUp();

                $this->productRepository = Mockery::mock(ProductRepositoryInterface::class);
                $this->service = new ProductService($this->productRepository);
                }

                public function test_create_product_with_valid_data()
                {
                // Arrange
                $productData = [
                'name' => 'Test Product',
                'price' => 99.99,
                'description' => 'Test description',
                'category_id' => 1
                ];

                $expectedProduct = (object) $productData;

                $this->productRepository
                ->shouldReceive('findByName')
                ->with('Test Product')
                ->once()
                ->andReturn(null);

                $this->productRepository
                ->shouldReceive('create')
                ->with($productData)
                ->once()
                ->andReturn($expectedProduct);

                // Act
                $result = $this->service->create($productData);

                // Assert
                $this->assertEquals($expectedProduct, $result);
                }

                public function test_create_product_with_duplicate_name_throws_exception()
                {
                // Arrange
                $productData = [
                'name' => 'Test Product',
                'price' => 99.99
                ];

                $existingProduct = (object) ['id' => 1, 'name' => 'Test Product'];

                $this->productRepository
                ->shouldReceive('findByName')
                ->with('Test Product')
                ->once()
                ->andReturn($existingProduct);

                // Assert & Act
                $this->expectException(ProductException::class);
                $this->expectExceptionMessage('Um produto com este nome já existe');

                $this->service->create($productData);
                }
                }
            </div>

            <h3>7.4. Exemplo de Teste de Feature</h3>
            <div class="code-block">
                namespace Tests\Feature\API;

                use App\Models\Product;
                use App\Models\User;
                use Tests\TestCase;
                use Laravel\Sanctum\Sanctum;

                class ProductApiTest extends TestCase
                {
                protected function setUp(): void
                {
                parent::setUp();

                // Criar dados de teste
                $this->user = User::factory()->create(['role' => 'admin']);
                }

                public function test_can_get_products_listing()
                {
                // Arrange
                $products = Product::factory()->count(3)->create();

                // Act
                $response = $this->getJson('/api/products');

                // Assert
                $response->assertStatus(200)
                ->assertJsonStructure([
                'status',
                'data' => [
                'data' => [
                '*' => ['id', 'name', 'price', 'description']
                ],
                'current_page',
                'total'
                ]
                ]);
                }

                public function test_can_create_product_with_valid_data()
                {
                // Arrange
                Sanctum::actingAs($this->user);

                $productData = [
                'name' => 'New Test Product',
                'price' => 129.99,
                'description' => 'Test description',
                'category_id' => 1
                ];

                // Act
                $response = $this->postJson('/api/products', $productData);

                // Assert
                $response->assertStatus(201)
                ->assertJson([
                'status' => 'success',
                'message' => 'Produto criado com sucesso',
                'data' => [
                'name' => 'New Test Product',
                'price' => 129.99,
                ]
                ]);

                $this->assertDatabaseHas('products', [
                'name' => 'New Test Product',
                ]);
                }

                public function test_cannot_create_product_with_invalid_data()
                {
                // Arrange
                Sanctum::actingAs($this->user);

                $productData = [
                'name' => '',
                'price' => -10,
                ];

                // Act
                $response = $this->postJson('/api/products', $productData);

                // Assert
                $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'price']);
                }
                }
            </div>
        </section>

        <section id="validacao">
            <h2>8. Validação de Dados</h2>

            <h3>8.1. Níveis de Validação</h3>
            <p>A validação de dados ocorre em múltiplas camadas:</p>

            <ul>
                <li><strong>Frontend</strong>: Validação preliminar para melhor experiência do usuário</li>
                <li><strong>Request</strong>: Validação dos dados de entrada usando Form Requests</li>
                <li><strong>Service</strong>: Validação de regras de negócio mais complexas</li>
                <li><strong>Model</strong>: Validação de integridade dos dados e restrições de banco</li>
            </ul>

            <h3>8.2. Regras de Validação Customizadas</h3>
            <p>Crie regras de validação customizadas para regras de negócio específicas:</p>

            <div class="code-block">
                namespace App\Rules;

                use Illuminate\Contracts\Validation\Rule;

                class ValidCpf implements Rule
                {
                public function passes($attribute, $value)
                {
                // Remove caracteres não numéricos
                $cpf = preg_replace('/[^0-9]/', '', $value);

                // Verifica se tem 11 dígitos
                if (strlen($cpf) != 11) {
                return false;
                }

                // Verifica se todos os dígitos são iguais
                if (preg_match('/(\d)\1{10}/', $cpf)) {
                return false;
                }

                // Algoritmo de validação de CPF
                for ($t = 9; $t < 11; $t++) { for ($d=0, $c=0; $c < $t; $c++) { $d +=$cpf[$c] * (($t + 1) - $c); }
                    $d=((10 * $d) % 11) % 10; if ($cpf[$c] !=$d) { return false; } } return true; } public function
                    message() { return 'O :attribute não é um CPF válido.' ; } } </div>

                    <h3>8.3. Uso de Regras de Validação</h3>
                    <div class="code-block">
                        namespace App\Http\Requests;

                        use App\Rules\ValidCpf;
                        use Illuminate\Foundation\Http\FormRequest;

                        class CustomerRequest extends FormRequest
                        {
                        public function authorize()
                        {
                        return true;
                        }

                        public function rules()
                        {
                        return [
                        'name' => 'required|string|max:255',
                        'email' => 'required|email|unique:customers,email,' . $this->id,
                        'cpf' => ['required', new ValidCpf, 'unique:customers,cpf,' . $this->id],
                        'birth_date' => 'nullable|date|before:today',
                        'phone' => 'required|string|regex:/^\(\d{2}\) \d{5}-\d{4}$/'
                        ];
                        }
                        }
                    </div>
        </section>

        <section id="versionamento">
            <h2>9. Controle de Versão</h2>

            <h3>9.1. Fluxo de Trabalho Git</h3>
            <p>Utilizamos o modelo Gitflow para o controle de versão:</p>

            <ul>
                <li><strong>main</strong>: Contém código de produção</li>
                <li><strong>develop</strong>: Contém código em desenvolvimento</li>
                <li><strong>feature/*</strong>: Branches para novas funcionalidades</li>
                <li><strong>release/*</strong>: Branches para preparação de releases</li>
                <li><strong>hotfix/*</strong>: Branches para correções urgentes em produção</li>
            </ul>

            <div class="example">
                <h4>Exemplo de Fluxo de Desenvolvimento</h4>
                <div class="code-block">
                    # Criando uma nova feature
                    git checkout develop
                    git pull origin develop
                    git checkout -b feature/nova-funcionalidade

                    # Trabalhando na feature...
                    git add .
                    git commit -m "Implementa nova funcionalidade"

                    # Finalizando a feature
                    git checkout develop
                    git pull origin develop
                    git merge feature/nova-funcionalidade
                    git push origin develop

                    # Criando uma release
                    git checkout develop
                    git checkout -b release/v1.2.0
                    # Ajustes finais, bump de versão, etc.
                    git checkout main
                    git merge release/v1.2.0
                    git tag -a v1.2.0 -m "Versão 1.2.0"
                    git push origin main --tags
                </div>
            </div>

            <h3>9.2. Padrões de Commit</h3>
            <p>Seguimos o padrão Conventional Commits para mensagens de commit:</p>

            <div class="code-block">
                <tipo>[escopo opcional]: <descrição>

                        [corpo opcional]

                        [rodapé(s) opcional(is)]
            </div>

            <p>Tipos de commit mais comuns:</p>
            <ul>
                <li><strong>feat</strong>: Nova funcionalidade</li>
                <li><strong>fix</strong>: Correção de bug</li>
                <li><strong>docs</strong>: Alteração na documentação</li>
                <li><strong>style</strong>: Alterações que não afetam o código (formatação, espaços, etc)</li>
                <li><strong>refactor</strong>: Refatoração de código</li>
                <li><strong>test</strong>: Adição ou correção de testes</li>
                <li><strong>chore</strong>: Alterações em ferramentas, configurações, etc</li>
            </ul>

            <div class="example">
                <h4>Exemplos de Mensagens de Commit</h4>
                <div class="code-block">
                    feat(orders): adiciona funcionalidade de cupom de desconto

                    fix(auth): corrige verificação de token expirado

                    refactor(products): otimiza consultas no repositório de produtos

                    docs: atualiza README com instruções de instalação

                    test(api): adiciona testes para endpoint de listagem de produtos
                </div>
            </div>

            <h3>9.3. Pull Requests e Code Review</h3>
            <p>Todas as alterações devem passar por pull requests e code review:</p>

            <ol>
                <li>Crie sua branch a partir de <code>develop</code></li>
                <li>Implemente suas alterações seguindo os padrões de codificação</li>
                <li>Escreva testes que cubram suas alterações</li>
                <li>Execute os testes localmente para garantir que tudo está funcionando</li>
                <li>Faça o push da sua branch para o repositório remoto</li>
                <li>Crie um pull request com uma descrição detalhada das alterações</li>
                <li>Aguarde a revisão de pelo menos um outro desenvolvedor</li>
                <li>Atenda aos comentários e feedback recebidos</li>
                <li>Após aprovação, faça o merge (ou peça para o revisor fazê-lo)</li>
            </ol>

            <div class="best-practice">
                <h4>Template de Pull Request</h4>
                <div class="code-block">
                    ## Descrição
                    Breve descrição das alterações implementadas.

                    ## Tipo de alteração
                    - [ ] Bug fix
                    - [ ] Nova feature
                    - [ ] Alteração que quebra compatibilidade
                    - [ ] Refatoração (sem alteração funcional)
                    - [ ] Documentação

                    ## Como testar?
                    Descreva os passos para testar as alterações.

                    1. Passo 1
                    2. Passo 2
                    3. ...

                    ## Checklist
                    - [ ] Código segue os padrões de estilo do projeto
                    - [ ] Testes unitários foram adicionados/atualizados
                    - [ ] Documentação foi atualizada se necessário
                    - [ ] Todos os testes estão passando
                </div>
            </div>

            <h3>9.4. Versionamento Semântico</h3>
            <p>Utilizamos Versionamento Semântico (SemVer) para o controle de versões:</p>

            <ul>
                <li><strong>MAJOR</strong>: Quando há mudanças incompatíveis com versões anteriores</li>
                <li><strong>MINOR</strong>: Quando há adições de funcionalidades compatíveis com versões anteriores</li>
                <li><strong>PATCH</strong>: Quando há correções de bugs compatíveis com versões anteriores</li>
            </ul>

            <p>Exemplo: <code>v2.3.1</code> (major 2, minor 3, patch 1)</p>
        </section>

        <section id="boas-praticas">
            <h2>10. Boas Práticas</h2>

            <h3>10.1. Princípios SOLID</h3>
            <p>Siga os princípios SOLID em seu código:</p>

            <ul>
                <li><strong>S</strong> - Single Responsibility Principle (Princípio da Responsabilidade Única)</li>
                <li><strong>O</strong> - Open/Closed Principle (Princípio Aberto/Fechado)</li>
                <li><strong>L</strong> - Liskov Substitution Principle (Princípio da Substituição de Liskov)</li>
                <li><strong>I</strong> - Interface Segregation Principle (Princípio da Segregação de Interface)</li>
                <li><strong>D</strong> - Dependency Inversion Principle (Princípio da Inversão de Dependência)</li>
            </ul>

            <h3>10.2. Clean Code</h3>
            <p>Algumas práticas de código limpo:</p>

            <ul>
                <li>Use nomes descritivos para variáveis, métodos e classes</li>
                <li>Evite métodos longos - divida em métodos menores com responsabilidades específicas</li>
                <li>Mantenha classes com responsabilidade única</li>
                <li>Evite comentários óbvios - escreva código que se explique</li>
                <li>Documente APIs públicas e decisões importantes</li>
                <li>Mantenha consistência na formatação e estilo</li>
            </ul>

            <div class="best-practice">
                <h4>Exemplo de Código Limpo</h4>
                <div class="code-block">
                    // Bom exemplo
                    public function calculateOrderTotal(Order $order): float
                    {
                    $subtotal = $this->calculateSubtotal($order);
                    $taxAmount = $this->calculateTaxAmount($subtotal, $order->customer->address->state);
                    $discountAmount = $this->applyDiscounts($order, $subtotal);

                    return $subtotal + $taxAmount - $discountAmount;
                    }

                    private function calculateSubtotal(Order $order): float
                    {
                    return $order->items->sum(function ($item) {
                    return $item->price * $item->quantity;
                    });
                    }
                </div>
            </div>

            <div class="bad-practice">
                <h4>Exemplo de Código Ruim</h4>
                <div class="code-block">
                    // Mau exemplo
                    public function calc($o)
                    {
                    // Calcula o total
                    $t = 0;
                    foreach ($o->items as $i) {
                    $t += $i->price * $i->quantity; // Soma os itens
                    }

                    // Calcula impostos
                    $tx = 0;
                    if ($o->customer->address->state == 'SP') {
                    $tx = $t * 0.18;
                    } else if ($o->customer->address->state == 'RJ') {
                    $tx = $t * 0.20;
                    } else {
                    $tx = $t * 0.15;
                    }

                    // Calcula descontos
                    $d = 0;
                    // ... mais lógica complexa aqui

                    return $t + $tx - $d;
                    }
                </div>
            </div>

            <h3>10.3. Otimização de Desempenho</h3>
            <ul>
                <li>Utilize eager loading (with()) para evitar o problema N+1</li>
                <li>Adicione índices apropriados nas tabelas do banco de dados</li>
                <li>Utilize cache para consultas frequentes ou dados que mudam pouco</li>
                <li>Evite realizar operações pesadas em loops</li>
                <li>Utilize filas para processamento assíncrono de tarefas demoradas</li>
                <li>Pagine resultados grandes em vez de carregar tudo de uma vez</li>
                <li>Otimize consultas SQL quando necessário, evitando JOINs desnecessários</li>
            </ul>

            <h3>10.4. Segurança</h3>
            <ul>
                <li>Sempre valide e sanitize dados de entrada</li>
                <li>Use prepared statements para consultas SQL</li>
                <li>Nunca confie em dados enviados pelo cliente</li>
                <li>Evite expor informações sensíveis em mensagens de erro</li>
                <li>Utilize HTTPS para todas as comunicações</li>
                <li>Armazene senhas usando algoritmos de hash seguros (bcrypt, Argon2)</li>
                <li>Implemente rate limiting para prevenir ataques de força bruta</li>
                <li>Mantenha dependências atualizadas para evitar vulnerabilidades conhecidas</li>
                <li>Implemente proteção CSRF em todos os formulários e requisições não-GET</li>
            </ul>

            <h3>10.5. Checklist de Implementação</h3>
            <p>Antes de considerar uma implementação completa, verifique se:</p>

            <ul>
                <li>A lógica de negócio está implementada corretamente nos services</li>
                <li>As validações estão completas e cobrem todos os casos de erro possíveis</li>
                <li>Os modelos e seus relacionamentos estão definidos corretamente</li>
                <li>As respostas API seguem o formato padronizado e informativo</li>
                <li>O código segue as convenções e padrões definidos</li>
                <li>Os repositórios implementam a interface definida</li>
                <li>Os testes unitários e de feature cobrem a funcionalidade</li>
                <li>O tratamento de exceções é adequado e informativo</li>
                <li>As regras de autorização estão implementadas</li>
                <li>A documentação do código está atualizada</li>
            </ul>

            <div class="note">
                <p><strong>Nota importante:</strong> Este manual é um documento vivo e será atualizado conforme o
                    projeto evolui e novas práticas são adotadas. Sugestões de melhoria são sempre bem-vindas e podem
                    ser enviadas através de pull requests.</p>
            </div>
        </section>

        <footer>
            <p>Manual de Implementação - Versão 1.0</p>
            <p>Última atualização: <span id="current-date"></span></p>
            <script>
                document.getElementById('current-date').textContent = new Date().toLocaleDateString();
            </script>
        </footer>
</body>

</html>