<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Testes</title>
    <link rel="stylesheet" href="css/manual.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Testes</h1>
        <p>Guia completo para implementação, execução e manutenção de testes no Laravel 12</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#tipos-testes">Tipos de Testes</a></li>
            <li><a href="#frameworks">Frameworks e Ferramentas</a></li>
            <li><a href="#config-ambiente">Configuração do Ambiente</a></li>
            <li><a href="#escrevendo-testes">Escrevendo Testes</a></li>
            <li><a href="#bdd">BDD</a></li>
            <li><a href="#tdd">TDD</a></li>
            <li><a href="#execucao-testes">Execução de Testes</a></li>
            <li><a href="#cobertura">Cobertura de Testes</a></li>
            <li><a href="#boas-praticas">Boas Práticas</a></li>
            <li><a href="#troubleshooting">Troubleshooting</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#tipos-testes">2. Tipos de Testes</a>
                <ul>
                    <li><a href="#testes-unitarios">2.1. Testes Unitários</a></li>
                    <li><a href="#testes-integracao">2.2. Testes de Integração</a></li>
                    <li><a href="#testes-api">2.3. Testes de API</a></li>
                    <li><a href="#testes-browser">2.4. Testes de Browser</a></li>
                    <li><a href="#testes-performance">2.5. Testes de Performance</a></li>
                    <li><a href="#testes-seguranca">2.6. Testes de Segurança</a></li>
                </ul>
            </li>
            <li><a href="#frameworks">3. Frameworks e Ferramentas</a>
                <ul>
                    <li><a href="#phpunit">3.1. PHPUnit</a></li>
                    <li><a href="#pest">3.2. Pest</a></li>
                    <li><a href="#jest">3.3. Jest</a></li>
                    <li><a href="#cypress">3.4. Cypress</a></li>
                    <li><a href="#outras-ferramentas">3.5. Outras Ferramentas Úteis</a></li>
                </ul>
            </li>
            <li><a href="#config-ambiente">4. Configuração do Ambiente de Testes</a>
                <ul>
                    <li><a href="#config-basica">4.1. Configuração Básica</a></li>
                    <li><a href="#banco-dados-teste">4.2. Banco de Dados para Testes</a></li>
                    <li><a href="#factories-seeders">4.3. Factories e Seeders</a></li>
                    <li><a href="#mocks-stubs">4.4. Mocks e Stubs</a></li>
                </ul>
            </li>
            <li><a href="#escrevendo-testes">5. Escrevendo Testes Eficazes</a>
                <ul>
                    <li><a href="#estrutura-teste">5.1. Estrutura de um Bom Teste</a></li>
                    <li><a href="#nomeando-testes">5.2. Nomeando Testes</a></li>
                    <li><a href="#assertions">5.3. Assertions Eficazes</a></li>
                    <li><a href="#data-providers">5.4. Data Providers</a></li>
                </ul>
            </li>
            <li><a href="#bdd">6. Behavior-Driven Development (BDD)</a>
                <ul>
                    <li><a href="#bdd-conceitos">6.1. Conceitos Básicos de BDD</a></li>
                    <li><a href="#bdd-laravel">6.2. BDD com Laravel e Pest</a></li>
                    <li><a href="#bdd-behat">6.3. BDD com Behat</a></li>
                </ul>
            </li>
            <li><a href="#tdd">7. Test-Driven Development (TDD)</a>
                <ul>
                    <li><a href="#tdd-ciclo">7.1. O Ciclo do TDD</a></li>
                    <li><a href="#tdd-laravel">7.2. TDD com Laravel 12</a></li>
                </ul>
            </li>
            <li><a href="#execucao-testes">8. Execução de Testes</a>
                <ul>
                    <li><a href="#linha-comando">8.1. Execução via Linha de Comando</a></li>
                    <li><a href="#testes-paralelos">8.2. Testes Paralelos</a></li>
                    <li><a href="#ci-cd">8.3. Integração com CI/CD</a></li>
                </ul>
            </li>
            <li><a href="#cobertura">9. Cobertura de Testes</a>
                <ul>
                    <li><a href="#ferramentas-cobertura">9.1. Ferramentas de Cobertura</a></li>
                    <li><a href="#metricas-cobertura">9.2. Métricas de Cobertura</a></li>
                    <li><a href="#estrategias-cobertura">9.3. Estratégias para Aumentar a Cobertura</a></li>
                </ul>
            </li>
            <li><a href="#boas-praticas">10. Boas Práticas</a>
                <ul>
                    <li><a href="#organizacao">10.1. Organização de Testes</a></li>
                    <li><a href="#anti-patterns">10.2. Anti-Patterns a Evitar</a></li>
                    <li><a href="#testes-legados">10.3. Testando Código Legado</a></li>
                    <li><a href="#testes-eficientes">10.4. Escrevendo Testes Eficientes</a></li>
                </ul>
            </li>
            <li><a href="#troubleshooting">11. Troubleshooting</a>
                <ul>
                    <li><a href="#problemas-comuns">11.1. Problemas Comuns e Soluções</a></li>
                    <li><a href="#depurando-testes">11.2. Depurando Testes</a></li>
                    <li><a href="#testes-flaky">11.3. Lidando com Testes Flaky</a></li>
                </ul>
            </li>
            <li><a href="#conclusao">12. Conclusão</a></li>
        </ul>
    </section>


    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <p>Este manual fornece diretrizes abrangentes para testes de aplicações Laravel 12, abordando diferentes tipos
            de testes,
            ferramentas, metodologias e boas práticas. O objetivo é estabelecer um padrão de teste consistente que
            garanta a qualidade e a robustez do software.</p>

        <section id="objetivos">
            <h3>1.1. Objetivos</h3>
            <p>Os principais objetivos da nossa estratégia de testes são:</p>
            <ul>
                <li>Garantir que o software atenda aos requisitos funcionais e não-funcionais</li>
                <li>Identificar e corrigir defeitos o mais cedo possível no ciclo de desenvolvimento</li>
                <li>Proporcionar confiança para refatoração e evolução do código</li>
                <li>Documentar o comportamento esperado do sistema</li>
                <li>Reduzir o custo total de manutenção do software</li>
                <li>Prevenir regressões durante a evolução do produto</li>
                <li>Melhorar a qualidade geral do código e da arquitetura</li>
                <li>Aproveitar os recursos de teste aprimorados do Laravel 12</li>
            </ul>
        </section>

        <section id="principios">
            <h3>1.2. Princípios de Teste</h3>
            <div class="best-practice">
                <ul>
                    <li><strong>Teste Cedo e Frequentemente:</strong> Quanto mais cedo um defeito for encontrado, menor
                        o custo para corrigi-lo.</li>
                    <li><strong>Independência:</strong> Os testes devem ser independentes e não devem depender uns dos
                        outros.</li>
                    <li><strong>Repetibilidade:</strong> Os testes devem produzir os mesmos resultados quando executados
                        nas mesmas condições.</li>
                    <li><strong>Determinismo:</strong> Testes não devem apresentar comportamento aleatório ou depender
                        de condições externas não controladas.</li>
                    <li><strong>Isolamento:</strong> Os testes devem isolar as funcionalidades testadas, controlando
                        todas as dependências externas.</li>
                    <li><strong>Objetividade:</strong> Cada teste deve ter um propósito claro e verificar um único
                        conceito.</li>
                    <li><strong>Manutenibilidade:</strong> Os testes devem ser fáceis de entender e manter.</li>
                    <li><strong>Velocidade:</strong> Os testes devem ser rápidos para fornecer feedback imediato.</li>
                </ul>
            </div>
        </section>

        <section id="estrategia-geral">
            <h3>1.3. Estratégia Geral de Testes</h3>
            <p>Nossa estratégia de testes segue a <a
                    href="https://martinfowler.com/articles/practical-test-pyramid.html" target="_blank">Pirâmide de
                    Testes</a>, que sugere:</p>

            <ul>
                <li><strong>Base:</strong> Muitos testes unitários de execução rápida</li>
                <li><strong>Meio:</strong> Testes de integração em quantidade moderada</li>
                <li><strong>Topo:</strong> Poucos testes de ponta a ponta (end-to-end)</li>
            </ul>

            <div class="example">
                <h4>Distribuição Recomendada</h4>
                <ul>
                    <li>~70% Testes Unitários</li>
                    <li>~20% Testes de Integração</li>
                    <li>~10% Testes End-to-End / UI</li>
                </ul>
            </div>

            <div class="note">
                <p>Esta distribuição não é rígida e pode variar conforme as necessidades específicas do projeto. O
                    importante é manter uma base sólida de testes rápidos e confiáveis, complementados por testes mais
                    abrangentes para validar o sistema como um todo.</p>
            </div>
        </section>
    </section>

    <section id="tipos-testes" class="manual-section">
        <h2>2. Tipos de Testes</h2>
        <p>Esta seção descreve os diferentes tipos de testes que devem ser implementados em projetos Laravel 12.</p>

        <section id="testes-unitarios">
            <h3>2.1. Testes Unitários</h3>
            <p>Testes unitários verificam o funcionamento isolado de componentes individuais do código, como classes ou
                métodos.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam uma única unidade de código</li>
                    <li>São rápidos (milissegundos)</li>
                    <li>Não dependem de sistemas externos (banco de dados, API, sistema de arquivos)</li>
                    <li>Utilizam mocks ou stubs para simular dependências</li>
                    <li>Não requerem configurações complexas</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste Unitário em Laravel 12</h4>
                <div class="code-block">
                    namespace Tests\Unit;

                    use App\Services\CalculatorService;
                    use PHPUnit\Framework\TestCase;

                    class CalculatorServiceTest extends TestCase
                    {
                    public function test_it_sums_two_numbers_correctly(): void
                    {
                    // Arrange
                    $calculator = new CalculatorService();

                    // Act
                    $result = $calculator->add(5, 3);

                    // Assert
                    $this->assertEquals(8, $result);
                    }

                    public function test_it_subtracts_two_numbers_correctly(): void
                    {
                    $calculator = new CalculatorService();
                    $result = $calculator->subtract(10, 4);
                    $this->assertEquals(6, $result);
                    }
                    }
                </div>
            </div>

            <p>Os testes unitários devem focar na lógica de negócios, validações, transformações de dados e outros
                comportamentos específicos da aplicação. No Laravel 12, aproveite os tipos de retorno e propriedades
                tipadas para melhorar a qualidade dos testes.</p>
        </section>

        <section id="testes-integracao">
            <h3>2.2. Testes de Integração</h3>
            <p>Testes de integração verificam se diferentes componentes do sistema funcionam juntos corretamente.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam a interação entre múltiplos componentes</li>
                    <li>Podem incluir acesso a banco de dados real ou simulado</li>
                    <li>São mais lentos que os testes unitários</li>
                    <li>Podem verificar fluxos de trabalho completos</li>
                    <li>Geralmente requerem configuração mais elaborada</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Integração em Laravel 12</h4>
                <div class="code-block">
                    namespace Tests\Integration;

                    use App\Models\User;
                    use App\Models\Order;
                    use App\Services\PaymentService;
                    use App\Repositories\OrderRepository;
                    use Tests\TestCase;
                    use Illuminate\Foundation\Testing\RefreshDatabase;

                    class OrderProcessingTest extends TestCase
                    {
                    use RefreshDatabase;

                    public function test_order_is_processed_and_saved_to_database(): void
                    {
                    // Arrange
                    $user = User::factory()->create();
                    $orderData = [
                    'product_id' => 1,
                    'quantity' => 2,
                    'total' => 100.00
                    ];

                    $orderRepo = app(OrderRepository::class);
                    $paymentService = app(PaymentService::class);

                    // Act
                    $result = $orderRepo->createOrder($user->id, $orderData);
                    $paymentProcessed = $paymentService->processPayment($result->id, $result->total);

                    // Assert
                    $this->assertInstanceOf(Order::class, $result);
                    $this->assertTrue($paymentProcessed);
                    $this->assertDatabaseHas('orders', [
                    'id' => $result->id,
                    'user_id' => $user->id,
                    'total' => 100.00,
                    'status' => 'paid'
                    ]);
                    }
                    }
                </div>
            </div>

            <p>Os testes de integração são particularmente importantes para verificar operações relacionadas ao banco de
                dados, interações entre serviços internos e pontos de integração com sistemas externos. O Laravel 12
                oferece melhorias significativas no sistema de banco de dados que podem ser aproveitadas nos testes de
                integração.</p>
        </section>

        <section id="testes-api">
            <h3>2.3. Testes de API</h3>
            <p>Os testes de API verificam se os endpoints da aplicação retornam as respostas esperadas para diferentes
                solicitações.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam as interfaces públicas da aplicação</li>
                    <li>Verificam códigos de status HTTP, estrutura de resposta e cabeçalhos</li>
                    <li>Validam tanto caminhos felizes quanto tratamento de erros</li>
                    <li>Podem testar autorização e autenticação</li>
                    <li>Geralmente utilizam banco de dados de teste</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de API em Laravel 12</h4>
                <div class="code-block">
                    namespace Tests\Feature\Api;

                    use App\Models\User;
                    use App\Models\Product;
                    use Tests\TestCase;
                    use Laravel\Sanctum\Sanctum;
                    use Illuminate\Foundation\Testing\RefreshDatabase;

                    class ProductApiTest extends TestCase
                    {
                    use RefreshDatabase;

                    public function test_it_returns_list_of_products(): void
                    {
                    // Arrange
                    Product::factory()->count(3)->create();

                    // Act
                    $response = $this->getJson('/api/products');

                    // Assert
                    $response->assertStatus(200)
                    ->assertJsonCount(3, 'data')
                    ->assertJsonStructure([
                    'data' => [
                    '*' => ['id', 'name', 'price', 'description', 'created_at']
                    ],
                    'links',
                    'meta'
                    ]);
                    }

                    public function test_it_returns_product_details(): void
                    {
                    // Arrange
                    $product = Product::factory()->create();

                    // Act
                    $response = $this->getJson("/api/products/{$product->id}");

                    // Assert
                    $response->assertStatus(200)
                    ->assertJson([
                    'data' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $product->price,
                    ]
                    ]);
                    }

                    public function test_it_returns_404_for_nonexistent_product(): void
                    {
                    $response = $this->getJson("/api/products/999");

                    $response->assertStatus(404);
                    }

                    public function test_authenticated_user_can_create_product(): void
                    {
                    // Arrange
                    Sanctum::actingAs(User::factory()->create(['is_admin' => true]));

                    $productData = [
                    'name' => 'New Product',
                    'price' => 99.99,
                    'description' => 'Product description'
                    ];

                    // Act
                    $response = $this->postJson('/api/products', $productData);

                    // Assert
                    $response->assertStatus(201)
                    ->assertJsonFragment(['name' => 'New Product']);

                    $this->assertDatabaseHas('products', ['name' => 'New Product']);
                    }
                    }
                </div>
            </div>

            <p>Os testes de API são essenciais para garantir que a interface pública da aplicação funciona conforme
                esperado e que as mudanças no código interno não quebrem essa interface. O Laravel 12 introduz melhorias
                na API Resource que podem ser aproveitadas nos testes.</p>
        </section>

        <section id="testes-browser">
            <h3>2.4. Testes de Browser</h3>
            <p>Testes de browser (ou end-to-end) simulam a interação do usuário com a aplicação através da interface do
                navegador.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam o sistema completo em um ambiente similar ao de produção</li>
                    <li>Verificam a integração frontend-backend</li>
                    <li>Simulam interações reais do usuário (cliques, preenchimento de formulários)</li>
                    <li>São mais lentos e mais frágeis que outros tipos de testes</li>
                    <li>Geralmente requerem um navegador real ou headless</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Browser com Laravel Dusk</h4>
                <div class="code-block">
                    namespace Tests\Browser;

                    use App\Models\User;
                    use Tests\DuskTestCase;
                    use Laravel\Dusk\Browser;

                    class LoginTest extends DuskTestCase
                    {
                    public function test_user_can_login(): void
                    {
                    $user = User::factory()->create([
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password123')
                    ]);

                    $this->browse(function (Browser $browser) {
                    $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->press('Login')
                    ->assertPathIs('/dashboard')
                    ->assertSee('Welcome');
                    });
                    }

                    public function test_user_cannot_login_with_invalid_credentials(): void
                    {
                    $this->browse(function (Browser $browser) {
                    $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'wrong-password')
                    ->press('Login')
                    ->assertPathIs('/login')
                    ->assertSee('These credentials do not match our records');
                    });
                    }

                    public function test_user_can_register_and_login(): void
                    {
                    $this->browse(function (Browser $browser) {
                    $browser->visit('/register')
                    ->type('name', 'Test User')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->type('password_confirmation', 'password123')
                    ->press('Register')
                    ->assertPathIs('/dashboard')
                    ->assertSee('Welcome');
                    });
                    }
                    }
                </div>
            </div>

            <p>Os testes de browser são valiosos para validar fluxos de usuário críticos, como registro, login,
                processos de pagamento e outras interações complexas com a interface de usuário. O Laravel Dusk foi
                atualizado para funcionar perfeitamente com o Laravel 12.</p>
        </section>

        <section id="testes-performance">
            <h3>2.5. Testes de Performance</h3>
            <p>Testes de performance avaliam como o sistema se comporta sob carga e se ele atende aos requisitos de
                desempenho.</p>

            <div class="best-practice">
                <h4>Tipos de Testes de Performance</h4>
                <ul>
                    <li><strong>Testes de carga:</strong> Verificam se o sistema funciona adequadamente sob carga
                        esperada</li>
                    <li><strong>Testes de estresse:</strong> Avaliam os limites do sistema aumentando a carga além do
                        esperado</li>
                    <li><strong>Testes de resistência:</strong> Verificam estabilidade e comportamento durante longos
                        períodos</li>
                    <li><strong>Testes de pico:</strong> Simulam picos repentinos de carga</li>
                    <li><strong>Testes de escalabilidade:</strong> Avaliam como o sistema escala com o aumento da carga
                    </li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Performance com k6</h4>
                <div class="code-block">
                    // tests/performance/api_load_test.js
                    import http from 'k6/http';
                    import { check, sleep } from 'k6';

                    export const options = {
                    stages: [
                    { duration: '30s', target: 20 }, // ramp up to 20 users
                    { duration: '1m', target: 20 }, // stay at 20 users for 1 minute
                    { duration: '30s', target: 0 }, // ramp down to 0 users
                    ],
                    thresholds: {
                    http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms http_req_failed:
                        ['rate<0.01'], // less than 1% can fail }, }; export default function() { const
                        BASE_URL='http://app.test/api' ; // Get products listing const
                        productsResponse=http.get(`${BASE_URL}/products`); check(productsResponse,
                        { 'products status 200' : (r)=> r.status === 200,
                        'products response time < 200ms': (r)=> r.timings.duration < 200, 'products returns data' :
                                (r)=> r.json('data').length > 0,
                                });

                                // Get a single product
                                const productId = 1;
                                const productResponse = http.get(`${BASE_URL}/products/${productId}`);
                                check(productResponse, {
                                'product status 200': (r) => r.status === 200,
                                'product response time < 150ms': (r)=> r.timings.duration < 150, }); sleep(1); } </div>
                </div>

                <p>Os testes de performance são cruciais para garantir uma boa experiência do usuário e devem ser
                    executados regularmente, especialmente antes de releases importantes ou após mudanças significativas
                    na arquitetura. O Laravel 12 traz melhorias de performance que podem ser verificadas através destes
                    testes.</p>
        </section>

        <section id="testes-seguranca">
            <h3>2.6. Testes de Segurança</h3>
            <p>Testes de segurança identificam vulnerabilidades e garantem que a aplicação proteja adequadamente dados e
                funcionalidades sensíveis.</p>

            <div class="best-practice">
                <h4>Áreas de Foco em Testes de Segurança</h4>
                <ul>
                    <li><strong>Autenticação e Autorização:</strong> Verificar controles de acesso e proteção de rotas
                    </li>
                    <li><strong>Injeção de SQL:</strong> Garantir que consultas ao banco de dados estejam protegidas
                    </li>
                    <li><strong>Cross-Site Scripting (XSS):</strong> Verificar se a aplicação sanitiza corretamente
                        entradas de usuário</li>
                    <li><strong>Cross-Site Request Forgery (CSRF):</strong> Confirmar que formulários estão protegidos
                        com tokens CSRF</li>
                    <li><strong>Exposição de dados sensíveis:</strong> Verificar se dados confidenciais são protegidos
                        adequadamente</li>
                    <li><strong>Configurações de segurança:</strong> Validar cabeçalhos HTTP de segurança e
                        configurações</li>
                    <li><strong>Vulnerabilidades em bibliotecas:</strong> Verificar dependências contra bases de
                        vulnerabilidades conhecidas</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Segurança em Laravel 12</h4>
                <div class="code-block">
                    namespace Tests\Security;

                    use Tests\TestCase;
                    use Illuminate\Foundation\Testing\RefreshDatabase;
                    use App\Models\User;

                    class AuthorizationTest extends TestCase
                    {
                    use RefreshDatabase;

                    public function test_non_admin_cannot_access_admin_panel(): void
                    {
                    // Arrange
                    $regularUser = User::factory()->create();

                    // Act
                    $this->actingAs($regularUser)
                    ->get('/admin/dashboard')
                    // Assert
                    ->assertStatus(403);
                    }

                    public function test_user_cannot_access_another_users_data(): void
                    {
                    // Arrange
                    $user1 = User::factory()->create();
                    $user2 = User::factory()->create();

                    // Act: user1 tries to access user2's profile
                    $response = $this->actingAs($user1)
                    ->get("/users/{$user2->id}/profile");

                    // Assert
                    $response->assertForbidden();
                    }

                    public function test_csrf_protection_is_working(): void
                    {
                    // Arrange
                    $user = User::factory()->create();

                    // Act: attempt to post without CSRF token
                    $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);

                    $response = $this->post('/update-profile', [
                    'name' => 'New Name'
                    ]);

                    // Assert
                    $response->assertStatus(419); // CSRF token mismatch
                    }

                    public function test_sql_injection_protection(): void
                    {
                    // Attempt SQL injection in query parameter
                    $response = $this->get('/search?q=test%27%20OR%20%271%27=%271');

                    // Should not expose any SQL errors or unexpected results
                    $response->assertStatus(200);
                    $response->assertDontSee('syntax error');
                    $response->assertDontSee('mysql');
                    $response->assertDontSee('SQL');
                    }
                    }
                </div>
            </div>

            <p>Além dos testes automáticos, considere usar ferramentas especializadas como OWASP ZAP, Burp Suite, ou
                Snyk para análises mais completas de segurança. Integrá-las ao pipeline de CI/CD pode ajudar a
                identificar problemas de segurança mais cedo no ciclo de desenvolvimento. O Laravel 12 inclui melhorias
                de segurança que devem ser verificadas em seus testes.</p>
        </section>
    </section>

    <section id="frameworks" class="manual-section">
        <h2>3. Frameworks e Ferramentas</h2>
        <p>Esta seção detalha os frameworks e ferramentas recomendados para a implementação de testes em aplicações
            Laravel 12.</p>

        <section id="phpunit">
            <h3>3.1. PHPUnit</h3>
            <p>PHPUnit é o framework de teste padrão para PHP e vem integrado com o Laravel 12.</p>

            <div class="best-practice">
                <h4>Prós do PHPUnit</h4>
                <ul>
                    <li>Integração nativa com Laravel 12</li>
                    <li>Ampla documentação e comunidade</li>
                    <li>Suporte extensivo para mocks e stubs</li>
                    <li>Assertions poderosas para diversos cenários</li>
                    <li>Facilidade de integração com ferramentas de cobertura de código</li>
                    <li>Suporte completo para PHP 8.3 e tipos de retorno</li>
                </ul>
            </div>

            <div class="example">
                <h4>Estrutura Básica de um Teste PHPUnit em Laravel 12</h4>
                <div class="code-block">
                    namespace Tests\Unit;

                    use PHPUnit\Framework\TestCase;
                    use App\Services\TaxCalculator;

                    class TaxCalculatorTest extends TestCase
                    {
                    protected TaxCalculator $calculator;

                    protected function setUp(): void
                    {
                    parent::setUp();
                    $this->calculator = new TaxCalculator();
                    }

                    public function test_calculates_tax_correctly_for_standard_rate(): void
                    {
                    // Arrange
                    $amount = 100;
                    $rate = 0.1; // 10%

                    // Act
                    $result = $this->calculator->calculate($amount, $rate);

                    // Assert
                    $this->assertEquals(10, $result);
                    }

                    public function test_throws_exception_for_negative_amount(): void
                    {
                    $this->expectException(\InvalidArgumentException::class);
                    $this->calculator->calculate(-50, 0.1);
                    }

                    public function test_returns_zero_for_zero_amount(): void
                    {
                    $this->assertEquals(0, $this->calculator->calculate(0, 0.2));
                    }
                    }
                </div>
            </div>
        </section>

        <section id="pest">
            <h3>3.2. Pest</h3>
            <p>Pest é um framework de teste elegante construído sobre o PHPUnit, oferecendo uma experiência mais fluente
                e expressiva. O Laravel 12 tem suporte aprimorado para Pest.</p>

            <div class="best-practice">
                <h4>Prós do Pest</h4>
                <ul>
                    <li>Sintaxe mais limpa e expressiva</li>
                    <li>Compatível com todas as funcionalidades do PHPUnit</li>
                    <li>Melhor legibilidade de testes</li>
                    <li>Expectativas encadeáveis fluentes</li>
                    <li>Hooks de teste mais intuitivos</li>
                    <li>Integração perfeita com Laravel 12</li>
                    <li>Suporte para arquivos de teste com tipagem estrita</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste com Pest em Laravel 12</h4>
                <div class="code-block">
                    // tests/Unit/TaxCalculatorTest.php

                    use App\Services\TaxCalculator;

                    // Setup
                    beforeEach(function () {
                    $this->calculator = new TaxCalculator();
                    });

                    // Testes
                    test('calculates tax correctly for standard rate', function () {
                    $result = $this->calculator->calculate(100, 0.1);

                    expect($result)->toBe(10);
                    });

                    test('throws exception for negative amount', function () {
                    $this->expectException(InvalidArgumentException::class);

                    $this->calculator->calculate(-50, 0.1);
                    });

                    test('returns zero for zero amount', function () {
                    $result = $this->calculator->calculate(0, 0.2);

                    expect($result)->toBe(0);
                    });

                    // Testes agrupados
                    describe('special tax calculations', function () {
                    test('applies discount for large amounts', function () {
                    $result = $this->calculator->calculateWithDiscount(1000, 0.1);

                    expect($result)->toBeLessThan(100);
                    });

                    test('respects minimum tax amount', function () {
                    $result = $this->calculator->calculate(10, 0.05);

                    expect($result)->toBeGreaterThanOrEqual(1);
                    });
                    });
                </div>
            </div>
        </section>

        <section id="jest">
            <h3>3.3. Jest</h3>
            <p>Jest é um framework de teste JavaScript mantido pelo Facebook, adequado para testar componentes frontend.
            </p>

            <div class="best-practice">
                <h4>Prós do Jest</h4>
                <ul>
                    <li>Configuração mínima necessária</li>
                    <li>Execução paralela e rápida dos testes</li>
                    <li>Mocking simples e poderoso</li>
                    <li>Snapshots para testes de UI</li>
                    <li>Cobertura de código integrada</li>
                    <li>Watch mode para desenvolvimento iterativo</li>
                    <li>Compatibilidade com frameworks modernos como Vue 3 e React</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste com Jest</h4>
                <div class="code-block">
                    // tests/js/utils/formatter.test.js

                    import { formatCurrency, formatDate } from '@/utils/formatter';

                    describe('Formatter Utils', () => {
                    describe('formatCurrency', () => {
                    test('formats zero value correctly', () => {
                    expect(formatCurrency(0)).toBe('R$ 0,00');
                    });

                    test('formats positive integers correctly', () => {
                    expect(formatCurrency(1234)).toBe('R$ 1.234,00');
                    });

                    test('formats decimals correctly', () => {
                    expect(formatCurrency(1234.56)).toBe('R$ 1.234,56');
                    });

                    test('formats negative values correctly', () => {
                    expect(formatCurrency(-99.99)).toBe('-R$ 99,99');
                    });
                    });

                    describe('formatDate', () => {
                    test('formats date in Brazilian format', () => {
                    const date = new Date('2023-12-31');
                    expect(formatDate(date)).toBe('31/12/2023');
                    });

                    test('returns empty string for null', () => {
                    expect(formatDate(null)).toBe('');
                    });
                    });
                    });
                </div>
            </div>

            <p>Jest é particularmente útil para testar componentes Vue ou React, bem como funções utilitárias
                JavaScript.
                Integre-o ao seu pipeline de CI/CD para garantir que o código frontend seja testado automaticamente.</p>
        </section>

        <section id="cypress">
            <h3>3.4. Cypress</h3>
            <p>Cypress é uma ferramenta moderna para testes end-to-end que permite testar qualquer coisa que seja
                executada em um navegador.</p>

            <div class="best-practice">
                <h4>Prós do Cypress</h4>
                <ul>
                    <li>Interface visual para depuração de testes</li>
                    <li>Espera automática por elementos e ações</li>
                    <li>Captura de screenshots e vídeos dos testes</li>
                    <li>Simulação de requisições de rede</li>
                    <li>Execução rápida em comparação com outras ferramentas E2E</li>
                    <li>Boa integração com aplicações modernas</li>
                    <li>Suporte para componentes isolados</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste com Cypress</h4>
                <div class="code-block">
                    // cypress/e2e/login.cy.js

                    describe('Login Page', () => {
                    beforeEach(() => {
                    cy.visit('/login');
                    });

                    it('should display login form', () => {
                    cy.get('form').should('be.visible');
                    cy.get('input[name="email"]').should('be.visible');
                    cy.get('input[name="password"]').should('be.visible');
                    cy.get('button[type="submit"]').contains('Login');
                    });

                    it('should show error with invalid credentials', () => {
                    cy.get('input[name="email"]').type('<EMAIL>');
                    cy.get('input[name="password"]').type('wrongpassword');
                    cy.get('button[type="submit"]').click();

                    cy.get('.alert-error').should('be.visible')
                    .and('contain', 'These credentials do not match our records');
                    });

                    it('should login successfully with valid credentials', () => {
                    // Create a user via API or DB before test
                    cy.request('POST', '/api/test/create-user', {
                    email: '<EMAIL>',
                    password: 'password123'
                    });

                    // Login with created user
                    cy.get('input[name="email"]').type('<EMAIL>');
                    cy.get('input[name="password"]').type('password123');
                    cy.get('button[type="submit"]').click();

                    // Verify successful login
                    cy.url().should('include', '/dashboard');
                    cy.get('h1').should('contain', 'Dashboard');
                    });
                    });
                </div>
            </div>
        </section>

        <section id="outras-ferramentas">
            <h3>3.5. Outras Ferramentas Úteis</h3>

            <div class="tools-list">
                <ul>
                    <li>
                        <strong>Laravel Pint</strong>
                        <p>Ferramenta de estilo de código baseada em PHP-CS-Fixer que ajuda a manter a consistência do
                            código.</p>
                        <pre>./vendor/bin/pint --test</pre>
                    </li>
                    <li>
                        <strong>PHPStan</strong>
                        <p>Analisador estático de código que encontra erros sem executar o código.</p>
                        <pre>./vendor/bin/phpstan analyse app tests</pre>
                    </li>
                    <li>
                        <strong>Larastan</strong>
                        <p>Extensão do PHPStan específica para Laravel.</p>
                        <pre>./vendor/bin/phpstan analyse --memory-limit=2G</pre>
                    </li>
                    <li>
                        <strong>Mockery</strong>
                        <p>Framework de mock para criar objetos simulados em testes.</p>
                        <pre>$mock = Mockery::mock(Service::class);</pre>
                    </li>
                    <li>
                        <strong>Faker</strong>
                        <p>Biblioteca para gerar dados falsos para testes.</p>
                        <pre>$faker = \Faker\Factory::create('pt_BR');</pre>
                    </li>
                    <li>
                        <strong>Laravel Parallel Testing</strong>
                        <p>Recurso do Laravel 12 para executar testes em paralelo.</p>
                        <pre>php artisan test --parallel</pre>
                    </li>
                </ul>
            </div>
        </section>
    </section>

    <section id="config-ambiente" class="manual-section">
        <h2>4. Configuração do Ambiente de Testes</h2>
        <p>Esta seção descreve como configurar adequadamente o ambiente para executar testes em aplicações Laravel 12.
        </p>

        <section id="config-basica">
            <h3>4.1. Configuração Básica</h3>
            <p>O Laravel 12 já vem com uma configuração básica para testes, mas alguns ajustes podem ser necessários
                para otimizar o ambiente.</p>

            <div class="code-block">
                <h4>Arquivo phpunit.xml</h4>
                <pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         cacheDirectory=".phpunit.cache"
>
    &lt;testsuites>
        &lt;testsuite name="Unit">
            &lt;directory>tests/Unit&lt;/directory>
        &lt;/testsuite>
        &lt;testsuite name="Feature">
            &lt;directory>tests/Feature&lt;/directory>
        &lt;/testsuite>
    &lt;/testsuites>
    &lt;source>
        &lt;include>
            &lt;directory>app&lt;/directory>
        &lt;/include>
    &lt;/source>
    &lt;php>
        &lt;env name="APP_ENV" value="testing"/>
        &lt;env name="BCRYPT_ROUNDS" value="4"/>
        &lt;env name="CACHE_DRIVER" value="array"/>
        &lt;env name="DB_CONNECTION" value="sqlite"/>
        &lt;env name="DB_DATABASE" value=":memory:"/>
        &lt;env name="MAIL_MAILER" value="array"/>
        &lt;env name="QUEUE_CONNECTION" value="sync"/>
        &lt;env name="SESSION_DRIVER" value="array"/>
        &lt;env name="TELESCOPE_ENABLED" value="false"/>
    &lt;/php>
&lt;/phpunit>
                </pre>
            </div>

            <div class="best-practice">
                <h4>Recomendações para Configuração</h4>
                <ul>
                    <li>Use SQLite em memória para testes mais rápidos</li>
                    <li>Desative serviços externos e use implementações em memória quando possível</li>
                    <li>Reduza o número de rounds de bcrypt para acelerar os testes</li>
                    <li>Desative recursos pesados como Telescope durante os testes</li>
                    <li>Configure o cache e sessões para usar o driver array</li>
                    <li>Use o driver de fila sync para processar jobs imediatamente</li>
                </ul>
            </div>
        </section>

        <section id="banco-dados-teste">
            <h3>4.2. Banco de Dados para Testes</h3>
            <p>A configuração adequada do banco de dados é crucial para testes eficientes e isolados.</p>

            <div class="best-practice">
                <h4>Opções de Banco de Dados para Testes</h4>
                <ul>
                    <li><strong>SQLite em memória:</strong> Mais rápido, mas com limitações de recursos</li>
                    <li><strong>MySQL/PostgreSQL de teste:</strong> Mais fiel ao ambiente de produção, mas mais lento
                    </li>
                    <li><strong>Bancos de dados Docker:</strong> Boa opção para CI/CD e desenvolvimento local</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Usando Traits de Banco de Dados do Laravel</h4>
                <pre>
namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class UserTest extends TestCase
{
    // Opção 1: Recriar o banco a cada teste
    use RefreshDatabase;
    
    // Opção 2: Executar migrações antes dos testes
    // use DatabaseMigrations;
    
    // Opção 3: Usar transações para isolar testes
    // use DatabaseTransactions;
    
    public function test_user_can_be_created(): void
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password'
        ];
        
        $user = User::create($userData);
        
        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('Test User', $user->name);
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }
}
                </pre>
            </div>

            <div class="tip">
                <p>Para testes mais rápidos, considere usar <code>RefreshDatabase</code> com SQLite em memória. Para
                    testes mais fiéis ao ambiente de produção, use <code>DatabaseTransactions</code> com o mesmo SGBD
                    usado em produção.</p>
            </div>
        </section>

        <section id="factories-seeders">
            <h3>4.3. Factories e Seeders</h3>
            <p>Factories e seeders são essenciais para criar dados de teste consistentes e realistas.</p>

            <div class="code-block">
                <h4>Exemplo de Factory em Laravel 12</h4>
                <pre>
namespace Database\Factories;

use App\Models\User;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => bcrypt('password'),
            'remember_token' => Str::random(10),
        ];
    }

    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'admin',
            'is_admin' => true,
        ]);
    }

    public function withTeam(Team $team = null): static
    {
        return $this->state(fn (array $attributes) => [
            'team_id' => $team?->id ?? Team::factory(),
        ]);
    }
}
                </pre>
            </div>

            <div class="code-block">
                <h4>Usando Factories em Testes</h4>
                <pre>
public function test_admin_can_see_dashboard(): void
{
    // Criar um usuário admin
    $admin = User::factory()->admin()->create();
    
    // Criar múltiplos usuários regulares
    $users = User::factory()->count(5)->create();
    
    // Criar usuário com relacionamentos
    $userWithTeam = User::factory()
        ->withTeam()
        ->has(Post::factory()->count(3))
        ->create();
    
    // Testar acesso
    $this->actingAs($admin)
        ->get('/admin/dashboard')
        ->assertStatus(200)
        ->assertSee('Admin Dashboard');
}
                </pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Factories</h4>
                <ul>
                    <li>Crie factories para todos os modelos principais</li>
                    <li>Use estados para variações comuns (admin, premium, etc.)</li>
                    <li>Defina relacionamentos nas factories</li>
                    <li>Use dados realistas com Faker</li>
                    <li>Mantenha as factories atualizadas quando o modelo mudar</li>
                    <li>Aproveite as novas funcionalidades de factory do Laravel 12</li>
                </ul>
            </div>
        </section>

        <section id="mocks-stubs">
            <h3>4.4. Mocks e Stubs</h3>
            <p>Mocks e stubs são essenciais para isolar o código sendo testado de suas dependências externas.</p>

            <div class="code-block">
                <h4>Exemplo de Mock com Mockery</h4>
                <pre>
public function test_order_service_sends_confirmation_email(): void
{
    // Criar mock do serviço de email
    $emailService = Mockery::mock(EmailService::class);
    
    // Definir expectativa - o método sendConfirmation deve ser chamado uma vez
    // com um objeto Order e deve retornar true
    $emailService->shouldReceive('sendConfirmation')
        ->once()
        ->with(Mockery::type(Order::class))
        ->andReturn(true);
    
    // Injetar o mock no container
    $this->app->instance(EmailService::class, $emailService);
    
    // Criar ordem
    $order = Order::factory()->create();
    
    // Executar o serviço que estamos testando
    $orderService = app(OrderService::class);
    $result = $orderService->processOrder($order->id);
    
    // Verificar resultado
    $this->assertTrue($result);
}
                </pre>
            </div>

            <div class="code-block">
                <h4>Exemplo de Fake em Laravel 12</h4>
                <pre>
public function test_password_reset_sends_email(): void
{
    // Usar o fake de Mail do Laravel
    Mail::fake();
    
    // Executar código que envia email
    $response = $this->post('/password/reset', [
        'email' => '<EMAIL>'
    ]);
    
    // Verificar que o email foi enviado
    Mail::assertSent(ResetPasswordMail::class, function ($mail) {
        return $mail->hasTo('<EMAIL>') &&
               $mail->token !== null;
    });
}
                </pre>
            </div>

            <div class="best-practice">
                <h4>Quando Usar Mocks vs. Fakes vs. Objetos Reais</h4>
                <ul>
                    <li><strong>Mocks:</strong> Quando precisa verificar interações específicas ou comportamento</li>
                    <li><strong>Stubs:</strong> Quando precisa apenas simular retornos específicos</li>
                    <li><strong>Fakes:</strong> Quando precisa de uma implementação simplificada mas funcional</li>
                    <li><strong>Objetos Reais:</strong> Para testes de integração ou quando a dependência é simples</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="escrevendo-testes" class="manual-section">
        <h2>5. Escrevendo Testes Eficazes</h2>
        <p>Esta seção aborda as melhores práticas para escrever testes claros, eficazes e de fácil manutenção.</p>

        <section id="estrutura-teste">
            <h3>5.1. Estrutura de um Bom Teste</h3>
            <p>Um teste bem estruturado segue o padrão AAA (Arrange-Act-Assert) ou Given-When-Then.</p>

            <div class="code-block">
                <h4>Exemplo de Estrutura AAA</h4>
                <pre>
public function test_user_can_update_profile(): void
{
    // Arrange (Given) - Configurar o cenário de teste
    $user = User::factory()->create([
        'name' => 'Original Name',
        'email' => '<EMAIL>'
    ]);
    
    $newData = [
        'name' => 'Updated Name',
        'email' => '<EMAIL>'
    ];
    
    // Act (When) - Executar a ação que está sendo testada
    $this->actingAs($user)
        ->put('/profile', $newData);
    
    // Assert (Then) - Verificar o resultado esperado
    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'name' => 'Updated Name',
        'email' => '<EMAIL>'
    ]);
}
                </pre>
            </div>

            <div class="best-practice">
                <h4>Características de um Bom Teste</h4>
                <ul>
                    <li><strong>Foco:</strong> Testa uma única funcionalidade ou comportamento</li>
                    <li><strong>Independência:</strong> Não depende de outros testes ou estado global</li>
                    <li><strong>Repetibilidade:</strong> Produz o mesmo resultado em execuções sucessivas</li>
                    <li><strong>Clareza:</strong> Intenção do teste é clara pela sua estrutura e nome</li>
                    <li><strong>Rapidez:</strong> Executa rapidamente para feedback imediato</li>
                    <li><strong>Robustez:</strong> Não é frágil a pequenas mudanças na implementação</li>
                </ul>
            </div>
        </section>

        <section id="nomeando-testes">
            <h3>5.2. Nomeando Testes</h3>
            <p>Nomes de testes claros e descritivos servem como documentação e facilitam a identificação de falhas.</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Ruim</th>
                            <th>Bom</th>
                            <th>Explicação</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>test_login()</code></td>
                            <td><code>test_user_can_login_with_valid_credentials()</code></td>
                            <td>Descreve claramente o comportamento esperado</td>
                        </tr>
                        <tr>
                            <td><code>test_validation()</code></td>
                            <td><code>test_post_creation_fails_with_empty_title()</code></td>
                            <td>Especifica o cenário e o resultado esperado</td>
                        </tr>
                        <tr>
                            <td><code>test_admin_feature()</code></td>
                            <td><code>test_only_admin_can_access_user_management_page()</code></td>
                            <td>Descreve a restrição de acesso específica</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="best-practice">
                <h4>Convenções de Nomenclatura</h4>
                <ul>
                    <li>Use o prefixo <code>test_</code> para métodos de teste (ou configure o anotador
                        <code>@test</code>)
                    </li>
                    <li>Siga o padrão <code>test_[o que está sendo testado]_[condições]_[resultado esperado]()</code>
                    </li>
                    <li>Use verbos que indicam ação ou verificação: can, should, fails, returns, etc.</li>
                    <li>Seja específico sobre as condições e resultados</li>
                    <li>Mantenha consistência em todo o projeto</li>
                </ul>
            </div>
        </section>

        <section id="assertions">
            <h3>5.3. Assertions Eficazes</h3>
            <p>Assertions (afirmações) são o coração de um teste, verificando se o comportamento real corresponde ao
                esperado.</p>

            <div class="code-block">
                <h4>Assertions Comuns em Laravel 12</h4>
                <pre>
// Assertions básicas
$this->assertTrue($result);
$this->assertEquals(5, $count);
$this->assertSame($expectedObject, $actualObject);
$this->assertInstanceOf(User::class, $user);

// Assertions de coleção
$this->assertCount(3, $collection);
$this->assertContains('expected', $array);
$this->assertEmpty($collection);

// Assertions de banco de dados
$this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
$this->assertDatabaseMissing('posts', ['title' => 'Deleted Post']);
$this->assertDatabaseCount('comments', 5);

// Assertions de resposta HTTP
$response->assertStatus(200);
$response->assertRedirect('/dashboard');
$response->assertJson(['status' => 'success']);
$response->assertJsonStructure(['data' => ['*' => ['id', 'name']]]);
$response->assertSee('Welcome');
$response->assertDontSee('Error');

// Assertions de autenticação
$this->assertAuthenticated();
$this->assertGuest();
$this->assertAuthenticatedAs($user);

// Assertions de validação
$response->assertValid(['name', 'email']);
$response->assertInvalid(['password' => 'The password field is required.']);

// Assertions de view
$response->assertViewIs('user.profile');
$response->assertViewHas('user', $user);
                </pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Assertions</h4>
                <ul>
                    <li>Use assertions específicas em vez de genéricas quando possível</li>
                    <li>Verifique apenas o que é relevante para o teste atual</li>
                    <li>Prefira assertions que fornecem mensagens de erro claras</li>
                    <li>Use múltiplas assertions quando necessário, mas mantenha o foco</li>
                    <li>Considere criar assertions personalizadas para verificações complexas ou repetitivas</li>
                </ul>
            </div>
        </section>

        <section id="data-providers">
            <h3>5.4. Data Providers</h3>
            <p>Data providers permitem executar o mesmo teste com diferentes conjuntos de dados, reduzindo duplicação de
                código.</p>

            <div class="code-block">
                <h4>Exemplo de Data Provider em PHPUnit</h4>
                <pre>
class CalculatorTest extends TestCase
{
    /**
     * @dataProvider additionProvider
     */
    public function test_it_adds_numbers_correctly(int $a, int $b, int $expected): void
    {
        $calculator = new Calculator();
        $result = $calculator->add($a, $b);
        $this->assertEquals($expected, $result);
    }
    
    public static function additionProvider(): array
    {
        return [
            'positive numbers' => [1, 2, 3],
            'negative numbers' => [-1, -2, -3],
            'mixed numbers' => [1, -2, -1],
            'zeros' => [0, 0, 0],
            'large numbers' => [1000, 2000, 3000],
        ];
    }
    
    /**
     * @dataProvider validationProvider
     */
    public function test_it_validates_input_correctly(mixed $input, bool $shouldBeValid): void
    {
        $validator = new InputValidator();
        $result = $validator->isValid($input);
        $this->assertEquals($shouldBeValid, $result);
    }
    
    public static function validationProvider(): array
    {
        return [
            'valid email' => ['<EMAIL>', true],
            'invalid email' => ['not-an-email', false],
            'empty string' => ['', false],
            'null value' => [null, false],
            'numeric value' => [123, false],
        ];
    }
}
                </pre>
            </div>

            <div class="code-block">
                <h4>Exemplo de Data Provider em Pest</h4>
                <pre>
// tests/Unit/CalculatorTest.php

use App\Services\Calculator;

it('adds numbers correctly', function (int $a, int $b, int $expected) {
    $calculator = new Calculator();
    $result = $calculator->add($a, $b);
    
    expect($result)->toBe($expected);
})->with([
    [1, 2, 3],
    [-1, -2, -3],
    [1, -2, -1],
    [0, 0, 0],
    [1000, 2000, 3000],
]);

it('validates input correctly', function (mixed $input, bool $shouldBeValid) {
    $validator = new InputValidator();
    $result = $validator->isValid($input);
    
    expect($result)->toBe($shouldBeValid);
})->with([
    ['<EMAIL>', true],
    ['not-an-email', false],
    ['', false],
    [null, false],
    [123, false],
]);
                </pre>
            </div>

            <div class="best-practice">
                <h4>Quando Usar Data Providers</h4>
                <ul>
                    <li>Para testar a mesma funcionalidade com diferentes entradas</li>
                    <li>Para testar casos limites e valores extremos</li>
                    <li>Para testar validações com diferentes tipos de dados</li>
                    <li>Para reduzir duplicação de código de teste</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="bdd" class="manual-section">
        <h2>6. Behavior-Driven Development (BDD)</h2>
        <p>BDD é uma abordagem que foca no comportamento do sistema do ponto de vista do negócio, usando linguagem
            natural para descrever testes.</p>

        <section id="bdd-conceitos">
            <h3>6.1. Conceitos Básicos de BDD</h3>
            <p>BDD usa uma linguagem ubíqua que pode ser entendida por desenvolvedores, QA e stakeholders de negócio.
            </p>

            <div class="best-practice">
                <h4>Elementos-chave do BDD</h4>
                <ul>
                    <li><strong>Feature:</strong> Funcionalidade ou característica do sistema</li>
                    <li><strong>Scenario:</strong> Exemplo específico de como a feature deve se comportar</li>
                    <li><strong>Given:</strong> Contexto inicial ou pré-condições</li>
                    <li><strong>When:</strong> Ação ou evento que ocorre</li>
                    <li><strong>Then:</strong> Resultado esperado ou pós-condições</li>
                </ul>
            </div>
        </section>

        <section id="bdd-laravel">
            <h3>6.2. BDD com Laravel e Pest</h3>
            <p>O Pest facilita a escrita de testes em estilo BDD no Laravel 12.</p>

            <div class="code-block">
                <h4>Exemplo de BDD com Pest</h4>
                <pre>
// tests/Feature/ShoppingCartTest.php

use App\Models\Product;
use App\Models\User;
use App\Services\CartService;

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->product = Product::factory()->create(['price' => 100]);
    $this->cartService = new CartService();
});

describe('Shopping Cart', function () {
    it('allows a user to add a product', function () {
        // Given a user and a product
        $user = $this->user;
        $product = $this->product;
        
        // When the user adds the product to their cart
        $result = $this->cartService->addToCart($user->id, $product->id, 1);
        
        // Then the product should be in the user's cart
        expect($result)->toBeTrue();
        $this->assertDatabaseHas('cart_items', [
            'user_id' => $user->id,
            'product_id' => $product->id,
            'quantity' => 1
        ]);
    });
    
    it('calculates the correct total', function () {
        // Given a user with products in cart
        $user = $this->user;
        $this->cartService->addToCart($user->id, $this->product->id, 2);
        
        // When we calculate the cart total
        $total = $this->cartService->getCartTotal($user->id);
        
        // Then the total should be correct
        expect($total)->toBe(200); // 2 * $100
    });
    
                        it('prevents adding out-of-stock products', function () {
                        // Given a product that is out of stock
                        $product = Product::factory()->create([
                            'stock_quantity' => 0,
                            'price' => 50
                        ]);
                        
                        // When the user tries to add it to their cart
                        $result = $this->cartService->addToCart($this->user->id, $product->id, 1);
                        
                        // Then it should fail
                        expect($result)->toBeFalse();
                        $this->assertDatabaseMissing('cart_items', [
                            'user_id' => $this->user->id,
                            'product_id' => $product->id
                        ]);
                    });
                    
                    it('allows removing products from cart', function () {
                        // Given a user with a product in cart
                        $user = $this->user;
                        $product = $this->product;
                        $this->cartService->addToCart($user->id, $product->id, 1);
                        
                        // When the user removes the product
                        $result = $this->cartService->removeFromCart($user->id, $product->id);
                        
                        // Then the product should be removed
                        expect($result)->toBeTrue();
                        $this->assertDatabaseMissing('cart_items', [
                            'user_id' => $user->id,
                            'product_id' => $product->id
                        ]);
                    });
                });
                </pre>
            </div>

            <div class="best-practice">
                <h4>Benefícios do BDD com Pest</h4>
                <ul>
                    <li>Testes mais legíveis e expressivos</li>
                    <li>Melhor comunicação entre equipes técnicas e não técnicas</li>
                    <li>Documentação viva do comportamento do sistema</li>
                    <li>Foco no comportamento em vez da implementação</li>
                    <li>Facilidade para refatorar sem quebrar testes</li>
                </ul>
            </div>
        </section>

        <section id="bdd-behat">
            <h3>6.3. BDD com Behat</h3>
            <p>Para uma abordagem BDD mais completa, o Behat permite escrever testes em linguagem Gherkin que é ainda
                mais próxima da linguagem natural.</p>

            <div class="code-block">
                <h4>Exemplo de Feature em Gherkin</h4>
                <pre>
# features/cart.feature
Feature: Shopping Cart
  In order to purchase products
  As a customer
  I need to be able to manage my shopping cart

  Scenario: Adding a product to cart
    Given I am a logged in user
    And there is a product with id "1" and price "100"
    When I add the product to my cart with quantity "2"
    Then my cart should contain 1 product
    And my cart total should be "200"

  Scenario: Removing a product from cart
    Given I am a logged in user
    And there is a product in my cart
    When I remove the product from my cart
    Then my cart should be empty
                </pre>
            </div>

            <div class="code-block">
                <h4>Implementação dos Steps em Behat</h4>
                <pre>
// features/bootstrap/FeatureContext.php

use Behat\Behat\Context\Context;
use Behat\Behat\Tester\Exception\PendingException;
use App\Models\User;
use App\Models\Product;
use App\Services\CartService;

class FeatureContext implements Context
{
    private User $user;
    private Product $product;
    private CartService $cartService;
    private bool $result;

    /**
     * @Given I am a logged in user
     */
    public function iAmALoggedInUser()
    {
        $this->user = User::factory()->create();
        $this->cartService = new CartService();
    }

    /**
     * @Given there is a product with id :id and price :price
     */
    public function thereIsAProductWithIdAndPrice($id, $price)
    {
        $this->product = Product::factory()->create([
            'id' => $id,
            'price' => $price
        ]);
    }

    /**
     * @When I add the product to my cart with quantity :quantity
     */
    public function iAddTheProductToMyCartWithQuantity($quantity)
    {
        $this->result = $this->cartService->addToCart(
            $this->user->id, 
            $this->product->id, 
            $quantity
        );
    }

    /**
     * @Then my cart should contain :count product
     */
    public function myCartShouldContainProduct($count)
    {
        $cartItems = $this->cartService->getCartItems($this->user->id);
        if (count($cartItems) != $count) {
            throw new \Exception("Cart contains " . count($cartItems) . " items, expected $count");
        }
    }

    /**
     * @Then my cart total should be :total
     */
    public function myCartTotalShouldBe($total)
    {
        $cartTotal = $this->cartService->getCartTotal($this->user->id);
        if ($cartTotal != $total) {
            throw new \Exception("Cart total is $cartTotal, expected $total");
        }
    }
}
                </pre>
            </div>
        </section>
    </section>

    <section id="tdd" class="manual-section">
        <h2>7. Test-Driven Development (TDD)</h2>
        <p>TDD é uma metodologia de desenvolvimento que coloca os testes em primeiro lugar, antes mesmo da implementação
            do código.</p>

        <section id="tdd-ciclo">
            <h3>7.1. O Ciclo do TDD</h3>
            <p>O TDD segue um ciclo simples conhecido como "Red-Green-Refactor":</p>

            <div class="best-practice">
                <ol>
                    <li><strong>Red:</strong> Escreva um teste que falha para a funcionalidade desejada</li>
                    <li><strong>Green:</strong> Implemente o código mínimo necessário para fazer o teste passar</li>
                    <li><strong>Refactor:</strong> Melhore o código mantendo os testes passando</li>
                </ol>
            </div>

            <div class="example">
                <h4>Exemplo de Ciclo TDD</h4>
                <p><strong>1. Red:</strong> Escreva um teste que falha</p>
                <div class="code-block">
                    <pre>
// tests/Unit/DiscountCalculatorTest.php
namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\DiscountCalculator;

class DiscountCalculatorTest extends TestCase
{
    public function test_it_applies_percentage_discount_correctly(): void
    {
        $calculator = new DiscountCalculator();
        $result = $calculator->applyDiscount(100, 20, 'percentage');
        
        $this->assertEquals(80, $result);
    }
}
                    </pre>
                </div>

                <p><strong>2. Green:</strong> Implemente o código mínimo para passar</p>
                <div class="code-block">
                    <pre>
// app/Services/DiscountCalculator.php
namespace App\Services;

class DiscountCalculator
{
    public function applyDiscount(float $amount, float $discount, string $type): float
    {
        if ($type === 'percentage') {
            return $amount - ($amount * $discount / 100);
        }
        
        return $amount;
    }
}
                    </pre>
                </div>

                <p><strong>3. Refactor:</strong> Melhore o código mantendo os testes passando</p>
                <div class="code-block">
                    <pre>
// app/Services/DiscountCalculator.php
namespace App\Services;

class DiscountCalculator
{
    public function applyDiscount(float $amount, float $discount, string $type): float
    {
        return match ($type) {
            'percentage' => $this->applyPercentageDiscount($amount, $discount),
            'fixed' => $this->applyFixedDiscount($amount, $discount),
            default => $amount,
        };
    }
    
    private function applyPercentageDiscount(float $amount, float $percentage): float
    {
        return $amount * (1 - $percentage / 100);
    }
    
    private function applyFixedDiscount(float $amount, float $discount): float
    {
        return max(0, $amount - $discount);
    }
}
                    </pre>
                </div>
            </div>
        </section>

        <section id="tdd-laravel">
            <h3>7.2. TDD com Laravel 12</h3>
            <p>O Laravel 12 oferece um excelente suporte para TDD com ferramentas que facilitam o ciclo de
                desenvolvimento.</p>

            <div class="code-block">
                <h4>Exemplo de TDD para um Controller</h4>
                <pre>
// 1. Red: Escreva um teste que falha
// tests/Feature/TaskControllerTest.php
namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Task;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TaskControllerTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_user_can_create_task(): void
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)->postJson('/api/tasks', [
            'title' => 'New Task',
            'description' => 'Task description',
            'due_date' => '2023-12-31'
        ]);
        
        $response->assertStatus(201)
            ->assertJson([
                'data' => [
                    'title' => 'New Task',
                    'description' => 'Task description',
                    'due_date' => '2023-12-31',
                    'user_id' => $user->id
                ]
            ]);
            
        $this->assertDatabaseHas('tasks', [
            'title' => 'New Task',
            'user_id' => $user->id
        ]);
    }
}

// 2. Green: Implemente o código mínimo para passar
// app/Http/Controllers/TaskController.php
namespace App\Http\Controllers;

use App\Models\Task;
use Illuminate\Http\Request;
use App\Http\Resources\TaskResource;

class TaskController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'due_date' => 'nullable|date'
        ]);
        
        $task = Task::create([
            'user_id' => auth()->id(),
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'due_date' => $validated['due_date'] ?? null
        ]);
        
        return new TaskResource($task);
    }
}

// 3. Refactor: Melhore o código mantendo os testes passando
// Extrair a lógica para um serviço
// app/Services/TaskService.php
namespace App\Services;

use App\Models\Task;

class TaskService
{
    public function createTask(int $userId, array $data): Task
    {
        return Task::create([
            'user_id' => $userId,
            'title' => $data['title'],
            'description' => $data['description'] ?? null,
            'due_date' => $data['due_date'] ?? null,
            'status' => $data['status'] ?? 'pending'
        ]);
    }
}

// app/Http/Controllers/TaskController.php (refatorado)
namespace App\Http\Controllers;

use App\Services\TaskService;
use Illuminate\Http\Request;
use App\Http\Resources\TaskResource;

class TaskController extends Controller
{
    public function __construct(
        protected TaskService $taskService
    ) {}
    
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'due_date' => 'nullable|date'
        ]);
        
        $task = $this->taskService->createTask(auth()->id(), $validated);
        
        return new TaskResource($task);
    }
}
                </pre>
            </div>

            <div class="best-practice">
                <h4>Dicas para TDD Eficaz com Laravel 12</h4>
                <ul>
                    <li>Use o comando <code>php artisan make:test</code> para criar rapidamente arquivos de teste</li>
                    <li>Aproveite o <code>php artisan test --filter=MethodName</code> para executar testes específicos
                    </li>
                    <li>Utilize o modo de observação <code>php artisan test --watch</code> para feedback imediato</li>
                    <li>Comece com testes simples e vá aumentando a complexidade</li>
                    <li>Mantenha os testes rápidos para não interromper o fluxo de desenvolvimento</li>
                    <li>Use factories para criar dados de teste consistentes</li>
                    <li>Aproveite o paralelismo de testes do Laravel 12 para execução mais rápida</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="execucao-testes" class="manual-section">
        <h2>8. Execução de Testes</h2>
        <p>Esta seção aborda as diferentes formas de executar testes em aplicações Laravel 12.</p>

        <section id="linha-comando">
            <h3>8.1. Execução via Linha de Comando</h3>
            <p>O Laravel 12 oferece várias opções para executar testes via linha de comando.</p>

            <div class="code-block">
                <h4>Comandos Básicos</h4>
                <pre>
# Executar todos os testes
php artisan test

# Executar testes em paralelo (novo no Laravel 12)
php artisan test --parallel

# Executar um arquivo de teste específico
php artisan test tests/Feature/UserTest.php

# Executar um método de teste específico
php artisan test --filter=test_user_can_login

# Executar testes com saída detalhada
php artisan test --verbose

# Executar testes e parar no primeiro erro
php artisan test --stop-on-failure

# Executar testes em modo de observação (requer pacote adicional)
php artisan test --watch

# Executar testes com PHPUnit diretamente
./vendor/bin/phpunit

# Executar testes com Pest
./vendor/bin/pest
                </pre>
            </div>

            <div class="best-practice">
                <h4>Opções Úteis para Execução de Testes</h4>
                <ul>
                    <li><code>--coverage</code>: Gera relatório de cobertura de código (requer Xdebug ou PCOV)</li>
                    <li><code>--profile</code>: Mostra os testes mais lentos</li>
                    <li><code>--env=testing</code>: Especifica o ambiente de execução</li>
                    <li><code>--without-tty</code>: Útil para execução em ambientes CI/CD</li>
                    <li><code>--compact</code>: Saída mais compacta</li>
                    <li><code>--testsuite=unit</code>: Executa apenas uma suíte de testes específica</li>
                </ul>
            </div>
        </section>

        <section id="testes-paralelos">
            <h3>8.2. Testes Paralelos</h3>
            <p>O Laravel 12 aprimorou significativamente o suporte para execução de testes em paralelo, o que pode
                reduzir drasticamente o tempo de execução.</p>

            <div class="code-block">
                <h4>Configuração de Testes Paralelos</h4>
                <pre>
# phpunit.xml
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    &lt;testsuites>
        &lt;testsuite name="Unit">
            &lt;directory>tests/Unit&lt;/directory>
        &lt;/testsuite>
        &lt;testsuite name="Feature">
            &lt;directory>tests/Feature&lt;/directory>
        &lt;/testsuite>
    &lt;/testsuites>
    &lt;source>
        &lt;include>
            &lt;directory>app&lt;/directory>
        &lt;/include>
    &lt;/source>
    &lt;php>
        &lt;env name="APP_ENV" value="testing"/>
        &lt;env name="BCRYPT_ROUNDS" value="4"/>
        &lt;env name="CACHE_DRIVER" value="array"/>
        &lt;env name="DB_CONNECTION" value="sqlite"/>
        &lt;env name="DB_DATABASE" value=":memory:"/>
        &lt;env name="MAIL_MAILER" value="array"/>
        &lt;env name="QUEUE_CONNECTION" value="sync"/>
        &lt;env name="SESSION_DRIVER" value="array"/>
        &lt;env name="TELESCOPE_ENABLED" value="false"/>
        
        <!-- Configuração para testes paralelos -->
        &lt;env name="PARALLEL_TESTS" value="true"/>
    &lt;/php>
    
    <!-- Configuração para testes paralelos -->
    &lt;parallel processesCount="4">
        &lt;testcase class="Tests\Feature\*Test"/>
    &lt;/parallel>
&lt;/phpunit>
                </pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Testes Paralelos</h4>
                <ul>
                    <li>Garanta que seus testes sejam independentes e não dependam de estado compartilhado</li>
                    <li>Use bancos de dados isolados para cada processo de teste</li>
                    <li>Evite dependências de arquivos compartilhados</li>
                    <li>Considere usar o trait <code>RefreshDatabase</code> em vez de <code>DatabaseTransactions</code>
                    </li>
                    <li>Ajuste o número de processos com base nos recursos da sua máquina</li>
                    <li>Use SQLite em memória para testes mais rápidos</li>
                </ul>
            </div>
        </section>

        <section id="ci-cd">
            <h3>8.3. Integração com CI/CD</h3>
            <p>Integrar testes automatizados em pipelines de CI/CD é essencial para garantir a qualidade do código.</p>

            <div class="code-block">
                <h4>Exemplo de Configuração GitHub Actions</h4>
                <pre>
# .github/workflows/tests.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  laravel-tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, mysql, pdo_mysql
        coverage: xdebug
    
    - uses: actions/checkout@v3
    
    - name: Copy .env
      run: cp .env.example .env
    
    - name: Install Dependencies
      run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
    
    - name: Generate key
      run: php artisan key:generate
    
    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache
    
    - name: Create Database
      run: |
        mkdir -p database
        touch database/database.sqlite
    
    - name: Execute tests (Unit and Feature tests) via PHPUnit
      env:
        DB_CONNECTION: sqlite
        DB_DATABASE: database/database.sqlite
      run: php artisan test --parallel
    
    - name: Execute tests with coverage
      env:
        DB_CONNECTION: sqlite
        DB_DATABASE: database/database.sqlite
      run: php artisan test --coverage --min=80
                </pre>
            </div>

            <div class="best-practice">
                <h4>Práticas Recomendadas para CI/CD</h4>
                <ul>
                    <li>Execute testes em cada push e pull request</li>
                    <li>Configure diferentes ambientes de teste (PHP 8.2, 8.3, etc.)</li>
                    <li>Armazene resultados de testes como artefatos</li>
                    <li>Defina limites mínimos de cobertura de código</li>
                    <li>Integre análise estática de código (PHPStan, Larastan)</li>
                    <li>Configure notificações para falhas de teste</li>
                    <li>Use cache para dependências para acelerar o processo</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="cobertura" class="manual-section">
        <h2>9. Cobertura de Testes</h2>
        <p>A cobertura de testes mede quanto do seu código está sendo testado, ajudando a identificar áreas que precisam
            de mais atenção.</p>

        <section id="ferramentas-cobertura">
            <h3>9.1. Ferramentas de Cobertura</h3>
            <p>Existem várias ferramentas para medir a cobertura de código em aplicações Laravel 12.</p>

            <div class="best-practice">
                <h4>Opções de Ferramentas</h4>
                <ul>
                    <li><strong>Xdebug:</strong> Extensão PHP que fornece informações detalhadas de cobertura</li>
                    <li><strong>PCOV:</strong> Alternativa mais leve ao Xdebug, focada apenas em cobertura</li>
                    <li><strong>PHPUnit + Xdebug/PCOV:</strong> Combinação nativa para gerar relatórios</li>
                    <li><strong>Laravel Artisan Test:</strong> Comando integrado com suporte a cobertura</li>
                    <li><strong>Codecov/Coveralls:</strong> Serviços online para visualização e histórico de cobertura
                    </li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Gerando Relatórios de Cobertura</h4>
                <pre>
# Com Laravel 12 (requer Xdebug ou PCOV)
php artisan test --coverage

# Com opção de cobertura mínima
php artisan test --coverage --min=80

# Com PHPUnit diretamente
./vendor/bin/phpunit --coverage-html reports/

# Com Pest
./vendor/bin/pest --coverage --coverage-html=reports/
                </pre>
            </div>
        </section>

        <section id="metricas-cobertura">
            <h3>9.2. Métricas de Cobertura</h3>
            <p>Entender as diferentes métricas de cobertura é importante para interpretar corretamente os relatórios.
            </p>

            <div class="best-practice">
                <h4>Principais Métricas</h4>
                <ul>
                    <li><strong>Line Coverage:</strong> Percentual de linhas de código executadas</li>
                    <li><strong>Branch Coverage:</strong> Percentual de ramos condicionais executados (if/else, switch)
                    </li>
                    <li><strong>Function Coverage:</strong> Percentual de funções/métodos executados</li>
                    <li><strong>Class Coverage:</strong> Percentual de classes executadas</li>
                    <li><strong>Path Coverage:</strong> Percentual de caminhos de execução possíveis testados</li>
                </ul>
            </div>

            <div class="tip">
                <p>Uma alta cobertura de linha não garante testes de qualidade. Foque em testar comportamentos críticos
                    e casos de borda, não apenas em aumentar a porcentagem de cobertura.</p>
            </div>
        </section>

        <section id="estrategias-cobertura">
            <h3>9.3. Estratégias para Aumentar a Cobertura</h3>
            <p>Aumentar a cobertura de testes de forma eficaz requer uma abordagem estratégica.</p>

            <div class="best-practice">
                <h4>Abordagens Recomendadas</h4>
                <ul>
                    <li>Priorize código crítico para o negócio</li>
                    <li>Identifique áreas com alta complexidade ciclomática</li>
                    <li>Foque em código propenso a erros (manipulação de datas, cálculos financeiros)</li>
                    <li>Use TDD para novos recursos</li>
                    <li>Adicione testes ao corrigir bugs</li>
                    <li>Estabeleça metas graduais de cobertura</li>
                    <li>Considere excluir código trivial ou gerado da análise</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Configurando Exclusões de Cobertura</h4>
                <pre>
# phpunit.xml
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;phpunit>
    &lt;!-- ... outras configurações ... -->
    
    &lt;source>
        &lt;include>
            &lt;directory>app&lt;/directory>
        &lt;/include>
        &lt;exclude>
            &lt;directory>app/Console&lt;/directory>
            &lt;directory>app/Exceptions&lt;/directory>
            &lt;file>app/Http/Middleware/Authenticate.php&lt;/file>
        &lt;/exclude>
    &lt;/source>
&lt;/phpunit>
                </pre>
            </div>
        </section>
    </section>

    <section id="boas-praticas" class="manual-section">
        <h2>10. Boas Práticas</h2>
        <p>Esta seção resume as melhores práticas para testes eficazes em aplicações Laravel 12.</p>

        <section id="organizacao">
            <h3>10.1. Organização de Testes</h3>
            <p>Uma boa organização dos testes facilita a manutenção e compreensão.</p>

            <div class="best-practice">
                <h4>Estrutura de Diretórios Recomendada</h4>
                <pre>
tests/
├── Unit/                   # Testes unitários
│   ├── Services/           # Testes para serviços
│   ├── Models/             # Testes para modelos
│   └── Helpers/            # Testes para helpers
├── Feature/                # Testes de feature/integração
│   ├── Api/                # Testes para endpoints de API
│   ├── Http/               # Testes para controllers web
│   └── Console/            # Testes para comandos artisan
├── Integration/            # Testes de integração específicos
│   ├── Database/           # Testes de integração com banco de dados
│   └── External/           # Testes de integração com serviços externos
├── Browser/                # Testes de browser com Dusk
├── Security/               # Testes de segurança
├── Performance/            # Testes de performance
└── TestCase.php            # Classe base para testes
                </pre>
            </div>

            <div class="best-practice">
                <h4>Convenções de Nomenclatura</h4>
                <ul>
                    <li>Nome de classes de teste: <code>{Funcionalidade}Test.php</code></li>
                    <li>Nome de métodos de teste:
                        <code>test_{o_que_está_sendo_testado}_{condições}_{resultado_esperado}</code>
                    </li>
                    <li>Agrupe testes relacionados em classes específicas</li>
                    <li>Use namespaces que reflitam a estrutura da aplicação</li>
                </ul>
            </div>
        </section>

        <section id="anti-patterns">
            <h3>10.2. Anti-Patterns a Evitar</h3>
            <p>Certos padrões de teste podem levar a problemas de manutenção e confiabilidade.</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Anti-Pattern</th>
                            <th>Problema</th>
                            <th>Solução</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Testes Frágeis</td>
                            <td>Testes que quebram com mudanças não relacionadas</td>
                            <td>Teste comportamentos, não implementações; use mocks com moderação</td>
                        </tr>
                        <tr>
                            <td>Testes Lentos</td>
                            <td>Testes que demoram muito para executar</td>
                            <td>Use mocks para dependências externas; otimize acesso ao banco de dados</td>
                        </tr>
                        <tr>
                            <td>Testes Interdependentes</td>
                            <td>Testes que dependem do estado de outros testes</td>
                            <td>Cada teste deve configurar seu próprio estado e limpar após a execução</td>
                        </tr>
                        <tr>
                            <td>Testes Obscuros</td>
                            <td>Testes difíceis de entender</td>
                            <td>Use nomes descritivos; siga o padrão AAA; evite lógica complexa nos testes</td>
                        </tr>
                        <tr>
                            <td>Testes Excessivamente Mockados</td>
                            <td>Testes com muitos mocks que não testam comportamento real</td>
                            <td>Equilibre o uso de mocks; considere testes de integração para interações complexas</td>
                        </tr>
                        <tr>
                            <td>Testes Duplicados</td>
                            <td>Múltiplos testes verificando a mesma funcionalidade</td>
                            <td>Use data providers; refatore testes para eliminar duplicação</td>
                        </tr>
                        <tr>
                            <td>Testes de Implementação</td>
                            <td>Testes acoplados à implementação interna</td>
                            <td>Teste comportamentos públicos e resultados, não detalhes de implementação</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="testes-legados">
            <h3>10.3. Testando Código Legado</h3>
            <p>Adicionar testes a código legado pode ser desafiador, mas é possível com as estratégias certas.</p>

            <div class="best-practice">
                <h4>Estratégias para Testar Código Legado</h4>
                <ul>
                    <li>Comece com testes de caracterização que documentam o comportamento atual</li>
                    <li>Use seams (costuras) para quebrar dependências difíceis de testar</li>
                    <li>Refatore gradualmente para melhorar a testabilidade</li>
                    <li>Priorize áreas de alto risco ou com mudanças frequentes</li>
                    <li>Use ferramentas de cobertura para identificar código não testado</li>
                    <li>Adicione testes antes de corrigir bugs ou adicionar recursos</li>
                    <li>Considere testes de integração quando testes unitários forem muito difíceis</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo de Teste de Caracterização</h4>
                <pre>
public function test_legacy_calculator_produces_expected_results(): void
{
    // Capturar o comportamento atual do sistema legado
    $legacyCalculator = new LegacyTaxCalculator();
    
    // Documentar resultados atuais, mesmo que possam estar "errados"
    $this->assertEquals(110.00, $legacyCalculator->calculateWithTax(100, 0.1));
    $this->assertEquals(100.00, $legacyCalculator->calculateWithTax(100, 0));
    $this->assertEquals(0.00, $legacyCalculator->calculateWithTax(0, 0.2));
    $this->assertEquals(120.00, $legacyCalculator->calculateWithTax(100, 0.2));
    
    // Documentar comportamentos estranhos também
    $this->assertEquals(100.00, $legacyCalculator->calculateWithTax(100, -0.1)); // Não aplica taxa negativa
}
                </pre>
            </div>
        </section>

        <section id="testes-eficientes">
            <h3>10.4. Escrevendo Testes Eficientes</h3>
            <p>Testes eficientes fornecem valor máximo com custo mínimo de manutenção e execução.</p>

            <div class="best-practice">
                <h4>Princípios para Testes Eficientes</h4>
                <ul>
                    <li><strong>Mantenha-os Rápidos:</strong> Testes lentos reduzem a produtividade e são executados com
                        menos frequência</li>
                    <li><strong>Mantenha-os Independentes:</strong> Cada teste deve poder ser executado isoladamente
                    </li>
                    <li><strong>Mantenha-os Repetíveis:</strong> Os resultados devem ser consistentes em execuções
                        sucessivas</li>
                    <li><strong>Mantenha-os Auto-validantes:</strong> Os testes devem claramente indicar sucesso ou
                        falha</li>
                    <li><strong>Mantenha-os Oportunos:</strong> Escreva testes próximo ao código de produção (idealmente
                        antes)</li>
                    <li><strong>Mantenha-os Focados:</strong> Cada teste deve verificar uma única funcionalidade ou
                        comportamento</li>
                    <li><strong>Mantenha-os Legíveis:</strong> Os testes devem servir como documentação do código</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo de Teste Eficiente vs. Ineficiente</h4>
                <pre>
// ❌ Teste ineficiente
public function test_user_management(): void
{
    // Teste faz muitas coisas
    $user = User::factory()->create();
    $this->assertDatabaseHas('users', ['email' => $user->email]);
    
    // Atualiza usuário
    $user->name = 'Updated Name';
    $user->save();
    $this->assertEquals('Updated Name', $user->fresh()->name);
    
    // Testa permissões
    $this->assertFalse($user->isAdmin());
    $user->assignRole('admin');
    $this->assertTrue($user->isAdmin());
    
    // Testa exclusão
    $user->delete();
    $this->assertDatabaseMissing('users', ['id' => $user->id]);
}

// ✅ Testes eficientes
public function test_user_can_be_created(): void
{
    $user = User::factory()->create();
    $this->assertDatabaseHas('users', ['email' => $user->email]);
}

public function test_user_can_be_updated(): void
{
    $user = User::factory()->create(['name' => 'Original Name']);
    
    $user->name = 'Updated Name';
    $user->save();
    
    $this->assertEquals('Updated Name', $user->fresh()->name);
}

public function test_user_can_be_assigned_admin_role(): void
{
    $user = User::factory()->create();
    $this->assertFalse($user->isAdmin());
    
    $user->assignRole('admin');
    
    $this->assertTrue($user->isAdmin());
}

public function test_user_can_be_deleted(): void
{
    $user = User::factory()->create();
    
    $user->delete();
    
    $this->assertDatabaseMissing('users', ['id' => $user->id]);
}
                </pre>
            </div>
        </section>
    </section>

    <section id="troubleshooting" class="manual-section">
        <h2>11. Troubleshooting</h2>
        <p>Esta seção aborda problemas comuns encontrados ao escrever e executar testes em Laravel 12 e como
            resolvê-los.</p>

        <section id="problemas-comuns">
            <h3>11.1. Problemas Comuns e Soluções</h3>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Problema</th>
                            <th>Possíveis Causas</th>
                            <th>Soluções</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Testes falham aleatoriamente</td>
                            <td>
                                <ul>
                                    <li>Dependência entre testes</li>
                                    <li>Condições de corrida</li>
                                    <li>Dados de teste inconsistentes</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Use <code>RefreshDatabase</code> para isolar testes</li>
                                    <li>Evite dependências de tempo com Carbon::setTestNow()</li>
                                    <li>Use factories com estados consistentes</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Testes muito lentos</td>
                            <td>
                                <ul>
                                    <li>Muitos acessos ao banco de dados</li>
                                    <li>Chamadas a serviços externos</li>
                                    <li>Configuração pesada</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Use SQLite em memória</li>
                                    <li>Mock dependências externas</li>
                                    <li>Execute testes em paralelo</li>
                                    <li>Otimize seeds e factories</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Erros de memória</td>
                            <td>
                                <ul>
                                    <li>Vazamentos de memória</li>
                                    <li>Objetos grandes em testes</li>
                                    <li>Muitos testes sem limpeza</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Aumente o limite de memória no phpunit.xml</li>
                                    <li>Limpe recursos em tearDown()</li>
                                    <li>Execute testes em grupos menores</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Falhas em ambiente CI mas não local</td>
                            <td>
                                <ul>
                                    <li>Diferenças de ambiente</li>
                                    <li>Dependências de timezone</li>
                                    <li>Permissões de arquivo</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Use Docker para garantir ambientes consistentes</li>
                                    <li>Fixe timezone em testes</li>
                                    <li>Verifique permissões em arquivos temporários</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Problemas com autenticação em testes</td>
                            <td>
                                <ul>
                                    <li>Configuração incorreta de guard</li>
                                    <li>Sessão não persistida</li>
                                    <li>Tokens inválidos</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <li>Use <code>actingAs($user, 'api')</code> para especificar o guard</li>
                                    <li>Verifique a configuração de sessão para testes</li>
                                    <li>Para API, use <code>withToken()</code> ou Sanctum::actingAs()</li>
                                </ul>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="depurando-testes">
            <h3>11.2. Depurando Testes</h3>
            <p>Técnicas eficazes para depurar testes que estão falhando.</p>

            <div class="best-practice">
                <h4>Ferramentas e Técnicas de Depuração</h4>
                <ul>
                    <li><strong>Verbose Output:</strong> Use <code>php artisan test -v</code> para saída detalhada</li>
                    <li><strong>Dump and Die:</strong> Use <code>dd()</code> ou <code>dump()</code> para inspecionar
                        variáveis</li>
                    <li><strong>Laravel Debug Bar:</strong> Visualize queries e performance em testes de browser</li>
                    <li><strong>Log to File:</strong> Escreva informações de depuração em arquivos de log</li>
                    <li><strong>PHPUnit Breakpoints:</strong> Use <code>$this->markTestIncomplete()</code> para parar em
                        pontos específicos</li>
                    <li><strong>Xdebug:</strong> Configure Xdebug com sua IDE para depuração passo a passo</li>
                    <li><strong>Dusk Screenshots:</strong> Use <code>$browser->screenshot('filename')</code> para
                        capturar o estado da UI</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo de Depuração de Teste</h4>
                <pre>
public function test_order_calculation_is_correct(): void
{
    // Configurar cenário de teste
    $products = Product::factory()->count(3)->create([
        'price' => 100
    ]);
    
    $cart = new ShoppingCart();
    foreach ($products as $product) {
        $cart->add($product, 2); // Adiciona 2 unidades de cada produto
    }
    
    // Depuração: verificar estado do carrinho
    // dump($cart->items()); // Descomente para depurar
    
    // Aplicar desconto
    $cart->applyDiscount(10); // 10% de desconto
    
    // Depuração: verificar cálculos intermediários
    // Log::info('Cart after discount', [
    //     'subtotal' => $cart->subtotal(),
    //     'discount' => $cart->discount(),
    //     'tax' => $cart->tax(),
    //     'total' => $cart->total()
    // ]);
    
    // Verificar resultado
    $expectedTotal = 540; // (3 produtos * 2 unidades * $100) - 10% + 8% tax
    $this->assertEquals($expectedTotal, $cart->total());
    
    // Se o teste falhar, mostrar detalhes do cálculo
    if ($cart->total() !== $expectedTotal) {
        $this->markTestIncomplete(
            "Cálculo incorreto: esperado $expectedTotal, obtido " . $cart->total() . "\n" .
            "Subtotal: " . $cart->subtotal() . "\n" .
            "Desconto: " . $cart->discount() . "\n" .
            "Taxa: " . $cart->tax()
        );
    }
}
                </pre>
            </div>
        </section>

        <section id="testes-flaky">
            <h3>11.3. Lidando com Testes Flaky</h3>
            <p>Testes "flaky" (instáveis) são aqueles que passam e falham intermitentemente sem mudanças no código.</p>

            <div class="best-practice">
                <h4>Estratégias para Identificar e Corrigir Testes Flaky</h4>
                <ul>
                    <li><strong>Identificação:</strong> Execute os testes múltiplas vezes para identificar instabilidade
                    </li>
                    <li><strong>Isolamento:</strong> Garanta que cada teste limpe seu próprio estado</li>
                    <li><strong>Determinismo:</strong> Elimine dependências de tempo, ordem ou recursos externos</li>
                    <li><strong>Esperas:</strong> Use esperas explícitas em vez de implícitas em testes de UI</li>
                    <li><strong>Sementes:</strong> Use sementes fixas para geradores aleatórios</li>
                    <li><strong>Logging:</strong> Adicione logs detalhados para identificar condições de falha</li>
                    <li><strong>Quarentena:</strong> Marque testes flaky para execução separada até serem corrigidos
                    </li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo de Correção de Teste Flaky</h4>
                <pre>
// ❌ Teste flaky (depende de tempo real)
public function test_subscription_expires_after_trial(): void
{
    $user = User::factory()->create();
    $subscription = $user->subscriptions()->create([
        'trial_ends_at' => now()->addDays(7)
    ]);
    
    // Avança o tempo em 8 dias (problema: usa sleep real)
    sleep(8 * 24 * 60 * 60); // Dorme por 8 dias - NUNCA FAÇA ISSO!
    
    $this->assertTrue($subscription->fresh()->expired());
}

// ✅ Teste estável (usa mock de tempo)
public function test_subscription_expires_after_trial(): void
{
    // Fixa o tempo atual para o teste
    $now = now();
    Carbon::setTestNow($now);
    
    $user = User::factory()->create();
    $subscription = $user->subscriptions()->create([
        'trial_ends_at' => $now->copy()->addDays(7)
    ]);
    
    // Avança o tempo em 8 dias (usando mock)
    Carbon::setTestNow($now->copy()->addDays(8));
    
    // Verifica se expirou
    $this->assertTrue($subscription->fresh()->expired());
    
    // Restaura o tempo normal
    Carbon::setTestNow();
}
                </pre>
            </div>
        </section>
    </section>

    <section id="conclusao" class="manual-section">
        <h2>12. Conclusão</h2>
        <p>Testes são um investimento essencial para garantir a qualidade e a manutenibilidade de aplicações Laravel 12.
            Este manual forneceu uma visão abrangente das melhores práticas, ferramentas e técnicas para implementar uma
            estratégia de testes eficaz.</p>

        <div class="best-practice">
            <h4>Pontos-Chave</h4>
            <ul>
                <li>Adote uma abordagem equilibrada com diferentes tipos de testes (unitários, integração, end-to-end)
                </li>
                <li>Escolha as ferramentas certas para suas necessidades (PHPUnit, Pest, Cypress, etc.)</li>
                <li>Configure adequadamente o ambiente de testes para execução rápida e confiável</li>
                <li>Escreva testes claros, focados e independentes</li>
                <li>Considere metodologias como TDD e BDD para melhorar a qualidade do código</li>
                <li>Monitore a cobertura de testes, mas priorize a qualidade sobre a quantidade</li>
                <li>Integre testes ao seu pipeline de CI/CD para feedback contínuo</li>
                <li>Aproveite os recursos de teste aprimorados do Laravel 12</li>
            </ul>
        </div>

        <div class="conclusion">
            <h3>Próximos Passos</h3>
            <p>Para continuar aprimorando sua estratégia de testes:</p>
            <ol>
                <li>Avalie a cobertura de testes atual da sua aplicação</li>
                <li>Identifique áreas críticas que precisam de mais testes</li>
                <li>Implemente testes automatizados no seu pipeline de CI/CD</li>
                <li>Treine sua equipe nas melhores práticas de teste</li>
                <li>Estabeleça padrões de qualidade e revisão de código que incluam testes</li>
                <li>Refine continuamente sua abordagem com base nos resultados</li>
            </ol>
        </div>

        <div class="resources">
            <h3>Recursos Adicionais</h3>
            <ul>
                <li><a href="https://laravel.com/docs/12.x/testing" target="_blank">Documentação Oficial de Testes do
                        Laravel 12</a></li>
                <li><a href="https://pestphp.com/docs" target="_blank">Documentação do Pest PHP</a></li>
                <li><a href="https://phpunit.de/documentation.html" target="_blank">Documentação do PHPUnit</a></li>
                <li><a href="https://docs.cypress.io" target="_blank">Documentação do Cypress</a></li>
                <li><a href="https://laracasts.com/topics/testing" target="_blank">Tutoriais de Teste no Laracasts</a>
                </li>
                <li><a href="https://github.com/nunomaduro/larastan" target="_blank">Larastan para Análise Estática</a>
                </li>
            </ul>
        </div>
    </section>

    <footer class="manual-footer">
        <div class="footer-content">
            <p>Manual de Testes para Laravel 12 © 2024</p>
            <p>Para atualizações e mais recursos, visite nossa <a href="#">documentação online</a>.</p>
        </div>
    </footer>

</body>

</html>