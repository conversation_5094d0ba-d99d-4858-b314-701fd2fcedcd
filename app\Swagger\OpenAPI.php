<?php

namespace App\Swagger;

/**
 * @OA\Info(
 *     title="Sistema de Backend API",
 *     version="1.0.0",
 *     description="API para sistema de backend com autenticação JWT",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="Equipe de Suporte"
 *     ),
 *     @OA\License(
 *         name="MIT",
 *         url="https://opensource.org/licenses/MIT"
 *     )
 * )
 *
 * @OA\Server(
 *     url=L5_SWAGGER_CONST_HOST,
 *     description="API Server"
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT"
 * )
 *
 * @OA\Tag(
 *     name="Autenticação",
 *     description="Endpoints para autenticação de usuários"
 * )
 * @OA\Tag(
 *     name="Usuários",
 *     description="Operações relacionadas a usuários"
 * )
 */
class OpenAPI
{
    // Esta classe serve apenas como contêiner para anotações OpenAPI globais
}
