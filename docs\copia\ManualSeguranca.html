<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Segurança - Laravel 12</title>
    <link rel="stylesheet" href="css/manual.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Segurança</h1>
        <p>Guia para implementação de práticas de segurança no Laravel 12</p>
        <div class="version">Versão 2.0 - Laravel 12.x</div>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#autenticacao">Autenticação</a></li>
            <li><a href="#autorizacao">Autorização</a></li>
            <li><a href="#protecao-dados">Proteção de Dados</a></li>
            <li><a href="#csrf">CSRF</a></li>
            <li><a href="#xss">XSS</a></li>
            <li><a href="#sql-injection">SQL Injection</a></li>
            <li><a href="#headers">Cabeçalhos HTTP</a></li>
            <li><a href="#rate-limiting">Rate Limiting</a></li>
            <li><a href="#seguranca-api">APIs</a></li>
            <li><a href="#dependencias">Dependências</a></li>
            <li><a href="#checklist">Checklist</a></li>
        </ul>
    </nav>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <p class="intro-text">A segurança é um aspecto crítico de qualquer aplicação web moderna. Este manual estabelece
            os padrões e
            práticas recomendadas para implementação de segurança em nossa aplicação Laravel 12, protegendo dados
            sensíveis,
            prevenindo vulnerabilidades comuns e garantindo uma experiência segura para os usuários.</p>

        <p>As diretrizes aqui apresentadas são baseadas em práticas estabelecidas pela indústria, incluindo as
            recomendações da OWASP (Open Web Application Security Project) e adequadas às especificidades do framework
            Laravel 12.</p>

        <div class="note">
            <p><strong>Nota:</strong> Este documento é complementar aos Manuais de Arquitetura, Implementação e Logging.
                As diretrizes aqui apresentadas devem ser seguidas por todos os componentes da aplicação.</p>
        </div>

        <div class="key-points">
            <h3>Novidades de Segurança no Laravel 12</h3>
            <ul>
                <li><strong>Tipagem Estrita:</strong> Maior segurança de tipos em todo o framework</li>
                <li><strong>Validação Aprimorada:</strong> Novos recursos de validação com tipagem forte</li>
                <li><strong>Proteção CSRF Aprimorada:</strong> Melhorias na proteção contra ataques CSRF</li>
                <li><strong>Suporte Nativo a WebAuthn:</strong> Autenticação sem senha usando padrões FIDO2</li>
                <li><strong>Melhorias no Rate Limiting:</strong> Controle mais granular de limites de requisição</li>
                <li><strong>Sanitização de Dados Aprimorada:</strong> Novas ferramentas para sanitização de entrada</li>
                <li><strong>Melhorias no Sistema de Permissões:</strong> Autorização mais robusta e flexível</li>
                <li><strong>Suporte a PHP 8.2+:</strong> Aproveitando recursos de segurança das versões mais recentes do
                    PHP</li>
            </ul>
        </div>

        <h3>Princípios de Segurança</h3>
        <ul>
            <li><strong>Defesa em Profundidade:</strong> Implementar múltiplas camadas de segurança</li>
            <li><strong>Privilégio Mínimo:</strong> Conceder apenas as permissões estritamente necessárias</li>
            <li><strong>Validação de Entrada:</strong> Nunca confiar em dados fornecidos pelo usuário</li>
            <li><strong>Codificação de Saída:</strong> Sempre sanitizar dados antes de exibi-los</li>
            <li><strong>Falha Segura:</strong> Em caso de erro, falhar de forma segura e sem revelar informações
                sensíveis</li>
            <li><strong>Segurança por Design:</strong> Considerar aspectos de segurança desde o início do projeto</li>
            <li><strong>Auditabilidade:</strong> Registrar eventos relevantes para análise de segurança</li>
        </ul>
    </section>

    <section id="autenticacao" class="manual-section">
        <h2>2. Autenticação</h2>
        <p>A autenticação é o processo de verificar a identidade de um usuário. Uma implementação robusta é essencial
            para proteger recursos e dados sensíveis.</p>

        <section id="auth-password" class="subsection">
            <h3>2.1. Autenticação por Senha</h3>
            <p>A autenticação baseada em senha deve seguir estas diretrizes:</p>

            <div class="best-practice">
                <h4>Requisitos para Senhas</h4>
                <ul>
                    <li>Comprimento mínimo de 12 caracteres (recomendado no Laravel 12)</li>
                    <li>Combinação de letras maiúsculas, minúsculas, números e símbolos</li>
                    <li>Verificação contra senhas comuns e vazadas</li>
                    <li>Não permitir senhas que contenham informações pessoais do usuário</li>
                    <li>Armazenamento utilizando hashing seguro (bcrypt ou Argon2id)</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Validação de Senha</h4>
                <pre>
// Validação de senha no formulário de registro
public function rules(): array
{
    return [
        'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
        'password' => [
            'required',
            'string',
            'min:12', // Mínimo de 12 caracteres no Laravel 12
            'confirmed',
            'regex:/[a-z]/', // Pelo menos uma letra minúscula
            'regex:/[A-Z]/', // Pelo menos uma letra maiúscula
            'regex:/[0-9]/', // Pelo menos um número
            'regex:/[@$!%*#?&]/', // Pelo menos um caractere especial
            Password::defaults() // Regras padrão do Laravel (inclui verificação de exposição)
        ],
    ];
}

// Implementação do login com tipagem estrita do Laravel 12
public function authenticate(Request $request): RedirectResponse
{
    $credentials = $request->validate([
        'email' => ['required', 'email'],
        'password' => ['required'],
    ]);

    if (Auth::attempt($credentials, $request->boolean('remember'))) {
        $request->session()->regenerate();

        // Registrar evento de login bem-sucedido
        activity()
            ->causedBy(Auth::user())
            ->log('login');

        return redirect()->intended('dashboard');
    }

    // Registrar tentativa falha (mas sem revelar se o usuário existe)
    Log::warning('Falha na tentativa de login', [
        'email' => $request->email,
        'ip' => $request->ip(),
        'user_agent' => $request->userAgent()
    ]);

    return back()->withErrors([
        'email' => 'As credenciais fornecidas não correspondem aos nossos registros.',
    ])->onlyInput('email');
}</pre>
            </div>

            <h4>Recuperação de Senha</h4>
            <p>O processo de recuperação de senha deve ser seguro e não revelar informações sensíveis:</p>
            <ul>
                <li>Utilizar tokens de uso único com expiração (padrão do Laravel)</li>
                <li>Limitar número de tentativas de recuperação</li>
                <li>Não revelar se o e-mail existe na base de dados</li>
                <li>Enviar notificação ao usuário quando a senha for alterada</li>
            </ul>

            <div class="code-block">
                <h4>Reset de Senha</h4>
                <pre>
// Utilização do sistema padrão de reset de senha do Laravel 12
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Support\Facades\Password;

// No controller
public function sendResetLinkEmail(Request $request): RedirectResponse
{
    $request->validate(['email' => 'required|email']);

    // Enviar link de reset independentemente de encontrar o usuário
    $status = Password::sendResetLink(
        $request->only('email')
    );

    // Logar a tentativa de recuperação
    Log::info('Solicitação de recuperação de senha', [
        'email' => $request->email,
        'ip' => $request->ip(),
        'success' => $status === Password::RESET_LINK_SENT
    ]);

    return $status === Password::RESET_LINK_SENT
        ? back()->with(['status' => __($status)])
        : back()->withErrors(['email' => __($status)]);
}</pre>
            </div>
        </section>

        <section id="auth-2fa" class="subsection">
            <h3>2.2. Autenticação de Dois Fatores (2FA)</h3>
            <p>A autenticação de dois fatores adiciona uma camada extra de segurança, exigindo que o usuário forneça
                duas formas diferentes de identificação:</p>

            <div class="code-block">
                <h4>Implementação de 2FA com Laravel Fortify</h4>
                <pre>
// Em config/fortify.php
'features' => [
    Features::registration(),
    Features::resetPasswords(),
    Features::emailVerification(),
    Features::updateProfileInformation(),
    Features::updatePasswords(),
    Features::twoFactorAuthentication([
        'confirm' => true,
        'confirmPassword' => true,
    ]),
],

// Em app/Providers/FortifyServiceProvider.php
use App\Actions\Fortify\EnableTwoFactorAuthentication;
use App\Actions\Fortify\DisableTwoFactorAuthentication;

public function boot(): void
{
    Fortify::createUsersUsing(CreateNewUser::class);
    Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
    Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
    Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

    Fortify::registerView(function () {
        return view('auth.register');
    });

    Fortify::loginView(function () {
        return view('auth.login');
    });

    Fortify::twoFactorChallengeView(function () {
        return view('auth.two-factor-challenge');
    });
}</pre>
            </div>

            <div class="note">
                <p>É recomendado implementar 2FA usando o algoritmo TOTP (Time-based One-time Password) com aplicativos
                    como Google Authenticator, Authy ou Microsoft Authenticator. Também é importante fornecer códigos de
                    recuperação para que os usuários não percam o acesso às suas contas.</p>
            </div>

            <h4>WebAuthn no Laravel 12</h4>
            <p>O Laravel 12 introduz suporte nativo para WebAuthn, permitindo autenticação sem senha usando dispositivos
                de segurança:</p>

            <div class="code-block">
                <h4>Configuração de WebAuthn</h4>
                <pre>
// Instalação do pacote
composer require laravel/webauthn

// Publicar migrações
php artisan vendor:publish --provider="Laravel\WebAuthn\WebAuthnServiceProvider"
php artisan migrate

// Em app/Models/User.php
use Laravel\WebAuthn\WebAuthnAuthenticatable;
use Laravel\WebAuthn\Contracts\WebAuthnAuthenticatable as WebAuthnAuthenticatableContract;

class User extends Authenticatable implements WebAuthnAuthenticatableContract
{
    use WebAuthnAuthenticatable;
    
    // ...
}

// Em routes/web.php
use Laravel\WebAuthn\Http\Controllers\WebAuthnRegisterController;
use Laravel\WebAuthn\Http\Controllers\WebAuthnLoginController;

Route::middleware(['auth'])->group(function () {
    // Rotas para registro de dispositivos
    Route::get('/webauthn/register', [WebAuthnRegisterController::class, 'create'])
        ->name('webauthn.register');
    Route::post('/webauthn/register', [WebAuthnRegisterController::class, 'store']);
    Route::delete('/webauthn/register/{id}', [WebAuthnRegisterController::class, 'destroy'])
        ->name('webauthn.destroy');
});

// Rotas para autenticação
Route::get('/webauthn/login', [WebAuthnLoginController::class, 'create'])
    ->name('webauthn.login');
Route::post('/webauthn/login', [WebAuthnLoginController::class, 'store']);</pre>
            </div>
        </section>

        <section id="auth-sanctum" class="subsection">
            <h3>2.3. Autenticação de API com Sanctum</h3>
            <p>Para APIs RESTful ou aplicativos SPA, use Laravel Sanctum para autenticação baseada em token:</p>

            <div class="code-block">
                <h4>Configuração do Sanctum</h4>
                <pre>
// Autenticação para SPA e APIs móveis
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, Notifiable;

    // ...
}

// No controller de login da API
public function login(Request $request): JsonResponse
{
    $request->validate([
        'email' => 'required|email',
        'password' => 'required',
        'device_name' => 'required',
    ]);

    $user = User::where('email', $request->email)->first();

    if (! $user || ! Hash::check($request->password, $user->password)) {
        Log::warning('Falha na tentativa de login via API', [
            'email' => $request->email,
            'ip' => $request->ip(),
            'device_name' => $request->device_name
        ]);

        return response()->json([
            'status' => 'error',
            'message' => 'Credenciais inválidas'
        ], 401);
    }

    // Criar token com escopos apropriados baseados nas permissões do usuário
    $token = $user->createToken($request->device_name, $user->getPermissionScopes());

    Log::info('Login via API realizado com sucesso', [
        'user_id' => $user->id,
        'email' => $user->email,
        'ip' => $request->ip(),
        'device_name' => $request->device_name
    ]);

    return response()->json([
        'status' => 'success',
        'message' => 'Autenticação realizada com sucesso',
        'data' => [
            'token' => $token->plainTextToken
        ]
    ]);
}</pre>
            </div>

            <h4>Proteção de Rotas com Sanctum</h4>

            <div class="code-block">
                <h4>Configuração de Rotas Protegidas</h4>
                <pre>
// Em routes/api.php
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Rotas que exigem um escopo específico
    Route::middleware(['ability:manage-accounts'])->group(function () {
        Route::apiResource('accounts', AccountController::class);
    });
});

// Revogação de token ao fazer logout
public function logout(Request $request): JsonResponse
{
    // Revogar token atual
    $request->user()->currentAccessToken()->delete();

    // OU revogar todos os tokens (logout em todos os dispositivos)
    // $request->user()->tokens()->delete();

    return response()->json([
        'status' => 'success',
        'message' => 'Logout realizado com sucesso'
    ]);
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhorias no Sanctum para Laravel 12</h4>
                <ul>
                    <li>Suporte aprimorado para tokens de longa duração com rotação automática</li>
                    <li>Melhor integração com sistemas de permissões</li>
                    <li>Suporte para revogação seletiva de tokens</li>
                    <li>Tipagem estrita em todas as interfaces</li>
                </ul>
            </div>
        </section>

        <section id="auth-social" class="subsection">
            <h3>2.4. Autenticação com Provedores Sociais</h3>
            <p>A autenticação social pode ser implementada utilizando Laravel Socialite:</p>

            <div class="code-block">
                <h4>Configuração do Socialite</h4>
                <pre>
// No config/services.php
'github' => [
    'client_id' => env('GITHUB_CLIENT_ID'),
    'client_secret' => env('GITHUB_CLIENT_SECRET'),
    'redirect' => env('GITHUB_CALLBACK_URL'),
],

'google' => [
    'client_id' => env('GOOGLE_CLIENT_ID'),
    'client_secret' => env('GOOGLE_CLIENT_SECRET'),
    'redirect' => env('GOOGLE_CALLBACK_URL'),
],

// No controller de autenticação social
public function redirectToProvider(string $provider): RedirectResponse
{
    if (!in_array($provider, ['github', 'google', 'facebook'])) {
        abort(404);
    }

    return Socialite::driver($provider)->redirect();
}

public function handleProviderCallback(string $provider): RedirectResponse
{
    try {
        $socialUser = Socialite::driver($provider)->user();
    } catch (\Exception $e) {
        Log::error("Erro na autenticação social com {$provider}", [
            'error' => $e->getMessage()
        ]);

        return redirect()->route('login')
            ->withErrors(['error' => 'Ocorreu um erro na autenticação. Por favor, tente novamente.']);
    }

    // Buscar usuário pelo ID social ou email
    $user = User::where("{$provider}_id", $socialUser->getId())
        ->orWhere('email', $socialUser->getEmail())
        ->first();

    // Se o usuário não existir, crie um novo
    if (!$user) {
        $user = User::create([
            'name' => $socialUser->getName(),
            'email' => $socialUser->getEmail(),
            "{$provider}_id" => $socialUser->getId(),
            'password' => Hash::make(Str::random(24)), // Senha aleatória forte
            'email_verified_at' => now(),
        ]);
    } else if (!$user->{"{$provider}_id"}) {
        // Atualizar ID social se o usuário foi encontrado apenas pelo email
        $user->{"{$provider}_id"} = $socialUser->getId();
        $user->save();
    }

    // Autenticar o usuário
    Auth::login($user, true);

    Log::info("Login via {$provider} realizado com sucesso", [
        'user_id' => $user->id,
        'email' => $user->email
    ]);

    return redirect()->intended('/dashboard');
}</pre>
            </div>

            <div class="warning">
                <p>Ao implementar autenticação social, sempre verifique a integridade do e-mail e outros dados
                    retornados pelo provedor. Em alguns casos, pode ser necessário solicitar permissões adicionais para
                    obter o e-mail verificado do usuário.</p>
            </div>
        </section>
    </section>

    <section id="autorizacao" class="manual-section">
        <h2>3. Autorização</h2>
        <p>A autorização determina o que um usuário autenticado pode fazer dentro do sistema. O Laravel 12 fornece
            vários
            mecanismos para implementar controle de acesso granular com tipagem estrita.</p>

        <section id="auth-gates" class="subsection">
            <h3>3.1. Gates e Policies</h3>
            <p>Gates e Policies permitem definir regras claras de autorização para diferentes partes da aplicação:</p>

            <div class="code-block">
                <h4>Definindo Gates</h4>
                <pre>
// Definindo Gates em AuthServiceProvider
public function boot(): void
{
    $this->registerPolicies();

    // Gate simples
    Gate::define('view-dashboard', function (User $user): bool {
        return $user->role === 'admin' || $user->role === 'manager';
    });

    // Gate com parâmetros adicionais
    Gate::define('update-post', function (User $user, Post $post): bool {
        return $user->id === $post->user_id || $user->hasRole('editor');
    });
}

// Usando Gates em controllers
public function show(): View
{
    if (Gate::denies('view-dashboard')) {
        abort(403, 'Acesso não autorizado.');
    }

    // OU
    $this->authorize('view-dashboard');

    return view('dashboard');
}</pre>
            </div>

            <p>Para models específicos, as Policies fornecem uma abordagem orientada a objetos para autorização:</p>

            <div class="code-block">
                <h4>Implementando Policies</h4>
                <pre>
// Criar uma Policy
php artisan make:policy PostPolicy --model=Post

// Em app/Policies/PostPolicy.php
public function update(User $user, Post $post): bool
{
    return $user->id === $post->user_id || $user->hasPermissionTo('edit posts');
}

public function delete(User $user, Post $post): bool
{
    return $user->id === $post->user_id || $user->hasPermissionTo('delete posts');
}

// Em controller
public function edit(Post $post): View
{
    $this->authorize('update', $post);

    return view('posts.edit', compact('post'));
}

// Em blade templates
@can('update', $post)
    <a href="{{ route('posts.edit', $post) }}">Editar</a>
@endcan</pre>
            </div>

            <div class="best-practice">
                <h4>Novidades em Gates e Policies no Laravel 12</h4>
                <ul>
                    <li>Tipagem estrita em todas as definições de gates e policies</li>
                    <li>Melhor suporte para injeção de dependências</li>
                    <li>Suporte para gates condicionais baseados em contexto</li>
                    <li>Melhor integração com o sistema de cache para autorização de alto desempenho</li>
                </ul>
            </div>
        </section>

        <section id="auth-roles" class="subsection">
            <h3>3.2. Controle de Acesso Baseado em Papéis (RBAC)</h3>
            <p>O RBAC permite definir papéis (roles) com conjuntos específicos de permissões:</p>

            <div class="best-practice">
                <h4>Implementação com Spatie Permission Package</h4>
                <p>Recomendamos o uso do pacote <code>spatie/laravel-permission</code> para implementar RBAC de forma
                    robusta.</p>
            </div>

            <div class="code-block">
                <h4>Configuração do RBAC</h4>
                <pre>
// Instalação
composer require spatie/laravel-permission

// Publicar migrações
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan migrate

// No modelo User
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;

    // ...
}

// Criando papéis e permissões
// Em um seeder ou durante o bootstrap da aplicação:
$adminRole = Role::create(['name' => 'admin']);
$editorRole = Role::create(['name' => 'editor']);
$userRole = Role::create(['name' => 'user']);

Permission::create(['name' => 'create posts']);
Permission::create(['name' => 'edit posts']);
Permission::create(['name' => 'delete posts']);
Permission::create(['name' => 'publish posts']);
Permission::create(['name' => 'manage users']);

// Atribuir permissões aos papéis
$adminRole->givePermissionTo(Permission::all());
$editorRole->givePermissionTo(['create posts', 'edit posts', 'delete posts', 'publish posts']);
$userRole->givePermissionTo(['create posts', 'edit posts']);

// Uso em código
if ($user->hasRole('admin')) {
    // Lógica para administradores
}

if ($user->can('delete posts')) {
    // Lógica para usuários com permissão para excluir posts
}

// Uso em middleware
Route::group(['middleware' => ['role:admin']], function () {
    Route::get('/admin/dashboard', [AdminController::class, 'dashboard']);
    Route::resource('/admin/users', UserController::class);
});

Route::group(['middleware' => ['permission:edit posts']], function () {
    Route::get('/posts/{post}/edit', [PostController::class, 'edit']);
    Route::put('/posts/{post}', [PostController::class, 'update']);
});

// Uso em blade templates
@role('admin')
    <li><a href="{{ route('admin.settings') }}">Configurações do Sistema</a></li>
@endrole

@can('publish posts')
    <button type="button" class="btn btn-publish">Publicar</button>
@endcan</pre>
            </div>

            <div class="note">
                <p>No Laravel 12, o pacote spatie/laravel-permission foi atualizado para aproveitar a tipagem estrita e
                    outros recursos do PHP 8.2+, tornando o sistema de permissões mais robusto e seguro.</p>
            </div>
        </section>

        <section id="auth-middleware" class="subsection">
            <h3>3.3. Middleware de Autorização</h3>
            <p>Os middlewares de autorização permitem proteger rotas com base em regras de negócio específicas:</p>

            <div class="code-block">
                <h4>Middleware Personalizado</h4>
                <pre>
// Criando um middleware personalizado
php artisan make:middleware CheckSubscriptionStatus

// Em app/Http/Middleware/CheckSubscriptionStatus.php
public function handle(Request $request, Closure $next): Response
{
    if (auth()->check()) {
        $user = auth()->user();

        if ($user->subscription_status !== 'active') {
            // Registrar tentativa de acesso
            Log::warning('Usuário com assinatura inativa tentando acessar recurso premium', [
                'user_id' => $user->id,
                'subscription_status' => $user->subscription_status,
                'route' => $request->path()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Assinatura inativa',
                    'code' => 'SUBSCRIPTION_INACTIVE'
                ], 403);
            }

            return redirect()->route('subscription.expired');
        }
    }

    return $next($request);
}

// Registrando o middleware em app/Http/Kernel.php
protected $routeMiddleware = [
    // ...
    'subscription.active' => \App\Http\Middleware\CheckSubscriptionStatus::class,
];

// Usando o middleware
Route::middleware(['auth', 'subscription.active'])->group(function () {
    Route::get('/premium-content', [ContentController::class, 'premium']);
});</pre>
            </div>

            <div class="note">
                <p>Combine vários middlewares para criar regras de acesso complexas e granulares. Sempre registre
                    tentativas de acesso não autorizado para detecção de potenciais abusos.</p>
            </div>
        </section>
    </section>

    <section id="protecao-dados" class="manual-section">
        <h2>4. Proteção de Dados</h2>
        <p>A proteção adequada dos dados é fundamental para garantir a confidencialidade e integridade das informações
            sensíveis.</p>

        <section id="dados-criptografia" class="subsection">
            <h3>4.1. Criptografia e Hashing</h3>
            <p>Laravel 12 fornece ferramentas aprimoradas para criptografar e fazer hash de dados sensíveis:</p>

            <div class="code-block">
                <h4>Criptografia e Hashing</h4>
                <pre>
// Criptografar dados sensíveis (reversível)
use Illuminate\Support\Facades\Crypt;

// Criptografar
$encryptedValue = Crypt::encrypt($sensitiveData);

// Descriptografar
try {
    $decryptedValue = Crypt::decrypt($encryptedValue);
} catch (DecryptException $e) {
    // Tratar erro de descriptografia
}

// Hash de senhas (irreversível)
use Illuminate\Support\Facades\Hash;

$hashedPassword = Hash::make($password);

// Verificar senha
if (Hash::check($inputPassword, $user->password)) {
    // Senha válida
}

// Rehash caso necessário (se o algoritmo de hash foi atualizado)
if (Hash::needsRehash($user->password)) {
    $user->password = Hash::make($inputPassword);
    $user->save();
}</pre>
            </div>

            <div class="best-practice">
                <h4>Criptografia de Atributos no Modelo</h4>
                <p>Automatize a criptografia de campos sensíveis nos modelos Eloquent:</p>
                <div class="code-block">
                    <pre>
// Em um modelo Eloquent
use App\Traits\HasEncryptedAttributes;

class Patient extends Model
{
    use HasEncryptedAttributes;

    /**
     * Atributos que devem ser criptografados.
     *
     * @var array<int, string>
     */
    protected array $encrypted = [
        'social_security_number',
        'medical_record',
        'health_insurance_id',
    ];

    /**
     * Acessor para obter o valor descriptografado
     */
    public function getSocialSecurityNumberAttribute($value): ?string
    {
        return $this->decryptAttribute($value);
    }

    /**
     * Mutator para criptografar o valor antes de salvar
     */
    public function setSocialSecurityNumberAttribute($value): void
    {
        $this->attributes['social_security_number'] = $this->encryptAttribute($value);
    }
}

// Implementação do trait HasEncryptedAttributes
namespace App\Traits;

use Illuminate\Support\Facades\Crypt;

trait HasEncryptedAttributes
{
    /**
     * Criptografa um valor
     */
    protected function encryptAttribute(?string $value): ?string
    {
        if (is_null($value)) {
            return null;
        }

        return Crypt::encrypt($value);
    }

    /**
     * Descriptografa um valor
     */
    protected function decryptAttribute(?string $value): ?string
    {
        if (is_null($value)) {
            return null;
        }

        try {
            return Crypt::decrypt($value);
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            report($e);
            return null;
        }
    }
}</pre>
                </div>

                <div class="note">
                    <p>No Laravel 12, você também pode usar o pacote <code>laravel/encrypted-attributes</code> que
                        fornece uma implementação mais robusta e integrada para criptografia de atributos de modelo.</p>
                </div>
        </section>

        <section id="dados-sanitizacao" class="subsection">
            <h3>4.2. Sanitização de Dados</h3>
            <p>A sanitização adequada de dados de entrada e saída é essencial para prevenir vulnerabilidades de
                segurança:</p>

            <div class="code-block">
                <h4>Sanitização de Entrada</h4>
                <pre>
// Validação e sanitização em um FormRequest
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class StoreUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'regex:/^[\pL\s\-]+$/u'],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:255', 'unique:users'],
            'phone' => ['nullable', 'string', 'regex:/^[0-9\-\(\)\+\s]+$/'],
            'bio' => ['nullable', 'string', 'max:1000'],
            'website' => ['nullable', 'url', 'max:255'],
            'password' => [
                'required',
                'confirmed',
                Password::min(12)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()
            ],
        ];
    }

    /**
     * Sanitizar dados após validação
     */
    protected function passedValidation(): void
    {
        // Remover tags HTML do campo bio
        if ($this->has('bio')) {
            $this->merge([
                'bio' => strip_tags($this->bio)
            ]);
        }

        // Normalizar número de telefone
        if ($this->has('phone')) {
            $this->merge([
                'phone' => preg_replace('/[^0-9+]/', '', $this->phone)
            ]);
        }
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Sanitização de Saída</h4>
                <pre>
// Em um blade template
&lt;div class="user-content">
    {!! Str::of(e($user->bio))->markdown() !!}
&lt;/div>

// Em uma API Resource
namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->when($request->user()->can('view-user-email', $this->resource), $this->email),
            'bio' => $this->when($this->bio, Str::limit(strip_tags($this->bio), 200)),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Sanitização</h4>
                <ul>
                    <li>Sempre valide e sanitize dados de entrada usando regras de validação do Laravel</li>
                    <li>Use <code>strip_tags()</code> ou <code>Str::of($value)->stripTags()</code> para remover HTML não
                        desejado</li>
                    <li>Use <code>e()</code> ou <code>@{{ }}</code> no Blade para escapar saída HTML</li>
                    <li>Para conteúdo rico, considere usar bibliotecas como HTML Purifier ou markdown seguro</li>
                    <li>Normalize dados antes de armazenar (e-mails em minúsculas, números de telefone apenas com
                        dígitos)</li>
                    <li>Valide URLs e e-mails com as regras específicas do Laravel</li>
                    <li>Utilize o novo sistema de tipagem estrita do Laravel 12 para garantir tipos de dados corretos
                    </li>
                </ul>
            </div>
        </section>

        <section id="dados-mascaramento" class="subsection">
            <h3>4.3. Mascaramento de Dados Sensíveis</h3>
            <p>O mascaramento de dados sensíveis é importante para logs, respostas de API e interfaces de usuário:</p>

            <div class="code-block">
                <h4>Mascaramento em Logs</h4>
                <pre>
// Em app/Logging/Processors/SensitiveDataMaskProcessor.php
namespace App\Logging\Processors;

use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;

class SensitiveDataMaskProcessor implements ProcessorInterface
{
    /**
     * Campos que devem ser mascarados nos logs
     */
    protected array $sensitiveFields = [
        'password',
        'password_confirmation',
        'credit_card',
        'card_number',
        'cvv',
        'ssn',
        'social_security',
        'access_token',
        'refresh_token',
        'api_key',
    ];

    /**
     * Processa o registro de log
     */
    public function __invoke(LogRecord $record): LogRecord
    {
        $record->context = $this->maskSensitiveData($record->context);
        
        if (isset($record->context['exception'])) {
            // Não mascarar objetos de exceção
            return $record;
        }

        return $record;
    }

    /**
     * Mascara dados sensíveis em um array
     */
    protected function maskSensitiveData(array $data): array
    {
        foreach ($data as $key => $value) {
            // Processar arrays aninhados recursivamente
            if (is_array($value)) {
                $data[$key] = $this->maskSensitiveData($value);
                continue;
            }

            // Verificar se o campo deve ser mascarado
            if (is_string($value) && $this->shouldMaskField($key)) {
                $data[$key] = $this->mask($value);
            }
        }

        return $data;
    }

    /**
     * Verifica se um campo deve ser mascarado
     */
    protected function shouldMaskField(string $fieldName): bool
    {
        $fieldName = strtolower($fieldName);
        
        foreach ($this->sensitiveFields as $sensitiveField) {
            if (str_contains($fieldName, $sensitiveField)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Mascara um valor sensível
     */
    protected function mask(string $value): string
    {
        if (strlen($value) <= 4) {
            return '****';
        }
        
        // Manter os primeiros 2 e últimos 2 caracteres
        return substr($value, 0, 2) . str_repeat('*', strlen($value) - 4) . substr($value, -2);
    }
}

// Registrando o processador em app/Providers/AppServiceProvider.php
use App\Logging\Processors\SensitiveDataMaskProcessor;
use Illuminate\Support\Facades\Log;

public function boot(): void
{
    // Adicionar processador a todos os canais de log
    foreach (array_keys(config('logging.channels')) as $channel) {
        Log::channel($channel)->pushProcessor(new SensitiveDataMaskProcessor());
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Mascaramento em Modelos</h4>
                <pre>
// Em app/Models/CreditCard.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CreditCard extends Model
{
    protected $fillable = [
        'user_id',
        'card_number',
        'expiration_month',
        'expiration_year',
        'card_holder',
        'brand',
    ];

    /**
     * Atributos que devem ser ocultos em arrays.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'card_number',
        'cvv',
    ];

    /**
     * Atributos que devem ser convertidos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expiration_month' => 'integer',
        'expiration_year' => 'integer',
    ];

    /**
     * Obter número do cartão mascarado
     */
    public function getMaskedCardNumberAttribute(): string
    {
        $cardNumber = $this->attributes['card_number'];
        $lastFour = substr($cardNumber, -4);
        
        return str_repeat('*', strlen($cardNumber) - 4) . $lastFour;
    }

    /**
     * Acessor para exibição segura
     */
    public function toArray(): array
    {
        $array = parent::toArray();
        
        // Adicionar versão mascarada do número do cartão
        $array['masked_card_number'] = $this->masked_card_number;
        
        return $array;
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Mascaramento</h4>
                <ul>
                    <li>Nunca armazene dados sensíveis como números de cartão de crédito completos, a menos que seja
                        absolutamente necessário</li>
                    <li>Utilize serviços de tokenização para pagamentos quando possível</li>
                    <li>Mascare dados sensíveis em logs, respostas de API e interfaces de usuário</li>
                    <li>Implemente mascaramento consistente em toda a aplicação</li>
                    <li>Considere o uso de ferramentas de descoberta de dados sensíveis para identificar dados que
                        precisam ser mascarados</li>
                    <li>Documente claramente quais campos contêm dados sensíveis e como devem ser tratados</li>
                </ul>
            </div>
        </section>

        <section id="dados-auditoria" class="subsection">
            <h3>4.4. Auditoria de Dados</h3>
            <p>A auditoria de dados é essencial para rastrear alterações em dados sensíveis e cumprir requisitos
                regulatórios:</p>

            <div class="code-block">
                <h4>Implementação de Auditoria</h4>
                <pre>
// Instalação do pacote de auditoria
composer require owen-it/laravel-auditing

// Em app/Models/User.php
namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class User extends Authenticatable implements Auditable
{
    use AuditableTrait;

    /**
     * Atributos que devem ser auditados.
     *
     * @var array<int, string>
     */
    protected $auditInclude = [
        'name',
        'email',
        'role',
        'is_active',
        'last_login_at',
    ];

    /**
     * Atributos que não devem ser auditados.
     *
     * @var array<int, string>
     */
    protected $auditExclude = [
        'password',
        'remember_token',
    ];

    /**
     * Eventos que devem disparar auditorias.
     *
     * @var array<int, string>
     */
    protected $auditEvents = [
        'created',
        'updated',
        'deleted',
        'restored',
    ];

    /**
     * Personalizar os dados de auditoria
     */
    public function transformAudit(array $data): array
    {
        // Adicionar informações contextuais
        $data['request_ip'] = request()->ip();
        $data['request_user_agent'] = request()->userAgent();
        $data['request_url'] = request()->fullUrl();
        $data['request_method'] = request()->method();
        
        return $data;
    }
}

// Visualização de logs de auditoria em um controller
namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\View\View;

class UserAuditController extends Controller
{
    public function index(User $user): View
    {
        $this->authorize('view-audit-logs', $user);
        
        $audits = $user->audits()->with('user')->latest()->paginate(15);
        
        return view('users.audit', compact('user', 'audits'));
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Auditoria</h4>
                <ul>
                    <li>Audite todas as alterações em dados sensíveis ou críticos</li>
                    <li>Registre quem fez a alteração, quando, o que foi alterado e de onde</li>
                    <li>Armazene logs de auditoria em um local seguro e separado</li>
                    <li>Implemente políticas de retenção adequadas para logs de auditoria</li>
                    <li>Considere requisitos regulatórios específicos do seu setor (GDPR, HIPAA, PCI DSS, etc.)</li>
                    <li>Forneça interfaces para visualização e busca de logs de auditoria para usuários autorizados</li>
                    <li>Configure alertas para alterações críticas ou suspeitas</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="csrf" class="manual-section">
        <h2>5. Proteção CSRF</h2>
        <p>A proteção contra Cross-Site Request Forgery (CSRF) é essencial para prevenir ataques onde sites maliciosos
            forçam usuários autenticados a executar ações não intencionais.</p>

        <div class="best-practice">
            <h4>Proteção CSRF no Laravel 12</h4>
            <p>O Laravel 12 inclui proteção CSRF robusta por padrão. O middleware <code>VerifyCsrfToken</code> é
                aplicado automaticamente a todas as rotas web.</p>
        </div>

        <div class="code-block">
            <h4>Implementação em Formulários</h4>
            <pre>
&lt;!-- Em formulários Blade -->
&lt;form method="POST" action="/profile">
    @csrf
    &lt;!-- Campos do formulário -->
    &lt;button type="submit">Atualizar Perfil&lt;/button>
&lt;/form>

// Em requisições AJAX com Axios (configuração global)
// Em resources/js/bootstrap.js
import axios from 'axios';

window.axios = axios;
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Adicionar token CSRF a todas as requisições
const token = document.head.querySelector('meta[name="csrf-token"]');

if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Em requisições AJAX individuais
const response = await fetch('/api/user', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({ name: 'John Doe' })
});</pre>
        </div>

        <div class="code-block">
            <h4>Configuração e Personalização</h4>
            <pre>
// Em app/Http/Middleware/VerifyCsrfToken.php
namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * URIs que devem ser excluídas da verificação CSRF.
     *
     * @var array<int, string>
     */
    protected $except = [
        'webhook/*', // Webhooks de serviços externos
        'api/public/*', // APIs públicas
    ];
    
    /**
     * Determina se a requisição tem um token CSRF válido
     */
    protected function tokensMatch($request): bool
    {
        // Implementação personalizada para verificação de token
        // Por exemplo, para permitir tokens específicos para APIs de parceiros
        
        if ($request->hasHeader('X-Partner-API-Key') && $this->isValidPartnerKey($request)) {
            return true;
        }
        
        return parent::tokensMatch($request);
    }
    
    /**
     * Verifica se a chave de API do parceiro é válida
     */
    protected function isValidPartnerKey($request): bool
    {
        $key = $request->header('X-Partner-API-Key');
        // Verificar a chave contra uma lista de chaves válidas
        return in_array($key, config('services.partners.valid_api_keys', []));
    }
}</pre>
        </div>

        <div class="warning">
            <p><strong>Atenção:</strong> Tenha muito cuidado ao excluir rotas da proteção CSRF. Apenas faça isso para
                endpoints que precisam ser acessados por serviços externos que não podem incluir tokens CSRF, como
                webhooks. Sempre implemente métodos alternativos de autenticação para essas rotas.</p>
        </div>

        <h3>5.1. SameSite Cookies</h3>
        <p>O Laravel 12 configura cookies com o atributo SameSite apropriado para aumentar a proteção contra ataques
            CSRF:</p>

        <div class="code-block">
            <h4>Configuração de Cookies</h4>
            <pre>
// Em config/session.php
return [
    // ...
    
    'same_site' => 'lax', // Opções: 'lax', 'strict', 'none'
    
    // ...
];

// Criando cookies personalizados com atributos SameSite
use Illuminate\Support\Facades\Cookie;

Cookie::make('name', 'value', $minutes, $path, $domain, $secure, $httpOnly, $raw, $sameSite);

// Exemplo:
return response('Hello World')
    ->cookie('preference', 'value', 60, '/', null, true, true, false, 'strict');</pre>
        </div>

        <div class="best-practice">
            <h4>Melhores Práticas para Proteção CSRF</h4>
            <ul>
                <li>Sempre use o helper <code>@csrf</code> em todos os formulários HTML</li>
                <li>Configure corretamente o cabeçalho <code>X-CSRF-TOKEN</code> em requisições AJAX</li>
                <li>Use o atributo SameSite apropriado para cookies (geralmente 'lax' é um bom equilíbrio)</li>
                <li>Implemente proteção CSRF mesmo em APIs que usam autenticação por token</li>
                <li>Considere usar o padrão de CSRF por sessão para maior segurança</li>
                <li>Teste regularmente a proteção CSRF com ferramentas de segurança</li>
                <li>Mantenha o Laravel atualizado para obter as últimas melhorias de segurança</li>
            </ul>
        </div>
    </section>

    <section id="xss" class="manual-section">
        <h2>6. Proteção XSS</h2>
        <p>Cross-Site Scripting (XSS) é uma vulnerabilidade que permite que atacantes injetem scripts maliciosos em
            páginas
            visualizadas por outros usuários.</p>

        <div class="code-block">
            <h4>Escapando Saída em Blade</h4>
            <pre>
&lt;!-- Escapando automaticamente -->
{{ $userInput }}

&lt;!-- Permitindo HTML (use com cuidado) -->
{!! $safeHtml !!}

&lt;!-- Escapando em atributos -->
&lt;div title="{{ $userTitle }}">Conteúdo&lt;/div>

&lt;!-- Escapando em JavaScript -->
&lt;script>
    const username = "{{ addslashes($username) }}";
    const config = @json($config);
&lt;/script></pre>
        </div>

        <div class="code-block">
            <h4>Sanitização de HTML</h4>
            <pre>
// Instalação do HTML Purifier
composer require mews/purifier

// Configuração em config/purifier.php
return [
    'encoding' => 'UTF-8',
    'finalize' => true,
    'cachePath' => storage_path('app/purifier'),
    'settings' => [
        'default' => [
            'HTML.Allowed' => 'b,strong,i,em,a[href|title],ul,ol,li,p[style],br,span[style],img[width|height|alt|src]',
            'CSS.AllowedProperties' => 'font,font-size,font-weight,font-style,font-family,text-decoration,padding-left,color,background-color,text-align',
            'AutoFormat.AutoParagraph' => true,
            'AutoFormat.RemoveEmpty' => true,
        ],
        'strict' => [
            'HTML.Allowed' => 'b,strong,i,em,a[href|title],ul,ol,li,p,br',
            'CSS.AllowedProperties' => '',
        ],
    ],
];

// Uso em um controller
use Mews\Purifier\Facades\Purifier;

public function store(Request $request): RedirectResponse
{
    $validatedData = $request->validate([
        'title' => 'required|string|max:255',
        'content' => 'required|string',
    ]);
    
    // Sanitizar conteúdo HTML
    $sanitizedContent = Purifier::clean($validatedData['content']);
    
    $post = Post::create([
        'title' => $validatedData['title'],
        'content' => $sanitizedContent,
        'user_id' => auth()->id(),
    ]);
    
    return redirect()->route('posts.show', $post);
}</pre>
        </div>

        <div class="code-block">
            <h4>Proteção em APIs</h4>
            <pre>
// Em app/Http/Resources/PostResource.php
namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class PostResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'content' => $this->when($request->query('include_content'), 
                // Sanitizar ou limitar conteúdo HTML para APIs
                Str::of($this->content)->stripTags(['p', 'b', 'i', 'strong', 'em', 'a', 'ul', 'ol', 'li'])
            ),
            'excerpt' => Str::limit(strip_tags($this->content), 100),
            'author' => new UserResource($this->whenLoaded('author')),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}</pre>
        </div>

        <div class="best-practice">
            <h4>Melhores Práticas para Proteção XSS</h4>
            <ul>
                <li>Sempre use <code>{{ }}</code> em vez de <code>{!! !!}</code> no Blade, a menos que seja
                    absolutamente necessário</li>
                <li>Sanitize HTML com bibliotecas como HTML Purifier antes de armazenar</li>
                <li>Implemente Content Security Policy (CSP) para limitar origens de scripts</li>
                <li>Use <code>@json</code> para escapar dados em contextos JavaScript</li>
                <li>Considere usar markdown em vez de HTML para conteúdo gerado pelo usuário</li>
                <li>Valide e sanitize todas as entradas do usuário, mesmo em APIs</li>
                <li>Implemente cabeçalhos de segurança como X-XSS-Protection</li>
                <li>Teste regularmente com ferramentas de segurança para detectar vulnerabilidades XSS</li>
            </ul>
        </div>

        <h3>6.1. Content Security Policy (CSP)</h3>
        <p>O CSP é uma camada adicional de segurança que ajuda a detectar e mitigar certos tipos de ataques, incluindo
            XSS:</p>

        <div class="code-block">
            <h4>Implementação de CSP</h4>
            <pre>
// Instalação do pacote
composer require spatie/laravel-csp

// Em app/Http/Kernel.php
protected $middlewareGroups = [
    'web' => [
        // ...
        \Spatie\Csp\AddCspHeaders::class,
    ],
];

// Em app/Policies/Csp/StandardPolicy.php
namespace App\Policies\Csp;

use Spatie\Csp\Directive;
use Spatie\Csp\Policies\Policy;

class StandardPolicy extends Policy
{
    public function configure(): void
    {
        $this
            ->addDirective(Directive::BASE, 'self')
            ->addDirective(Directive::CONNECT, 'self')
            ->addDirective(Directive::DEFAULT, 'self')
            ->addDirective(Directive::FORM_ACTION, 'self')
            ->addDirective(Directive::IMG, ['self', 'data:', 'https://secure.example.com'])
            ->addDirective(Directive::MEDIA, 'self')
            ->addDirective(Directive::OBJECT, 'none')
            ->addDirective(Directive::SCRIPT, ['self', 'https://js.stripe.com'])
            ->addDirective(Directive::STYLE, ['self', 'https://fonts.googleapis.com'])
            ->addDirective(Directive::FONT, ['self', 'https://fonts.gstatic.com'])
            ->addNonceForDirective(Directive::SCRIPT)
            ->addNonceForDirective(Directive::STYLE);
    }
}

// Em config/csp.php
return [
    'policy' => \App\Policies\Csp\StandardPolicy::class,
    'report_only' => env('CSP_REPORT_ONLY', false),
    'report_uri' => env('CSP_REPORT_URI', null),
];

// Uso em templates Blade
&lt;script nonce="{{ csp_nonce() }}">
    // JavaScript seguro
&lt;/script></pre>
        </div>
    </section>

    <section id="sql-injection" class="manual-section">
        <h2>7. Proteção contra SQL Injection</h2>
        <p>SQL Injection é uma técnica de ataque que explora vulnerabilidades em aplicações que interagem com bancos de
            dados.</p>

        <div class="best-practice">
            <h4>Proteção no Laravel 12</h4>
            <p>O Laravel oferece proteção robusta contra SQL Injection através do Query Builder e Eloquent ORM, que
                utilizam prepared statements e parâmetros vinculados.</p>
        </div>

        <div class="code-block">
            <h4>Práticas Seguras</h4>
            <pre>
// SEGURO: Usando Eloquent
$users = User::where('status', 'active')
    ->where('role', $role)
    ->get();

// SEGURO: Usando Query Builder
$users = DB::table('users')
    ->where('status', 'active')
    ->where('role', $role)
    ->get();

// SEGURO: Usando prepared statements diretamente
$users = DB::select('SELECT * FROM users WHERE status = ? AND role = ?', ['active', $role]);

// INSEGURO: Concatenação de strings (NUNCA FAÇA ISSO)
$users = DB::select("SELECT * FROM users WHERE status = 'active' AND role = '" . $role . "'");

// INSEGURO: Raw expressions sem sanitização (EVITE)
$users = User::whereRaw("role = '$role'")->get();</pre>
        </div>

        <div class="code-block">
            <h4>Ordenação e Filtragem Segura</h4>
            <pre>
// Ordenação segura com validação de entrada
public function index(Request $request): View
{
    $validatedData = $request->validate([
        'sort' => ['sometimes', 'string', Rule::in(['name', 'created_at', 'email'])],
        'direction' => ['sometimes', 'string', Rule::in(['asc', 'desc'])],
        'filter' => ['sometimes', 'string', 'max:50'],
    ]);
    
    $query = User::query();
    
    // Aplicar filtro se fornecido
    if ($filter = $request->input('filter')) {
        $query->where('name', 'like', "%{$filter}%")
              ->orWhere('email', 'like', "%{$filter}%");
    }
    
    // Ordenação segura
    $sort = $request->input('sort', 'created_at');
    $direction = $request->input('direction', 'desc');
    
    // Garantir que os campos de ordenação são seguros
    if (in_array($sort, ['name', 'created_at', 'email'])) {
        $query->orderBy($sort, $direction);
    }
    
    $users = $query->paginate(15)->withQueryString();
    
    return view('users.index', compact('users'));
}

// Uso seguro de Raw Queries (quando necessário)
public function searchBySkills(array $skills): Collection
{
    // Construir a consulta de forma segura
    $placeholders = implode(',', array_fill(0, count($skills), '?'));
    
    return DB::select(
        "SELECT users.* FROM users 
         JOIN user_skills ON users.id = user_skills.user_id 
         JOIN skills ON user_skills.skill_id = skills.id 
         WHERE skills.name IN ({$placeholders})
         GROUP BY users.id
         HAVING COUNT(DISTINCT skills.id) = ?",
        [...$skills, count($skills)]
    );
}</pre>
        </div>

        <div class="best-practice">
            <h4>Melhores Práticas para Prevenção de SQL Injection</h4>
            <ul>
                <li>Sempre use Eloquent ou Query Builder em vez de consultas SQL brutas</li>
                <li>Quando precisar usar consultas brutas, use prepared statements com parâmetros vinculados</li>
                <li>Valide e sanitize todas as entradas do usuário antes de usá-las em consultas</li>
                <li>Use listas de permissão (whitelists) para campos de ordenação e filtros</li>
                <li>Implemente um sistema de permissões para limitar o acesso a operações sensíveis de banco de dados
                </li>
                <li>Utilize o menor nível de privilégio possível para o usuário do banco de dados</li>
                <li>Considere usar ferramentas de análise estática de código para detectar vulnerabilidades de SQL
                    Injection</li>
                <li>Aproveite a tipagem estrita do Laravel 12 para garantir que os tipos de dados sejam consistentes
                </li>
            </ul>
        </div>
    </section>

    <section id="headers" class="manual-section">
        <h2>8. Cabeçalhos HTTP de Segurança</h2>
        <p>Os cabeçalhos HTTP de segurança são uma camada adicional de proteção que ajuda a mitigar vários tipos de
            ataques.</p>

        <div class="code-block">
            <h4>Implementação de Cabeçalhos de Segurança</h4>
            <pre>
// Instalação do pacote
composer require bepsvpt/secure-headers

// Publicar configuração
php artisan vendor:publish --provider="Bepsvpt\SecureHeaders\SecureHeadersServiceProvider"

// Em app/Http/Kernel.php
protected $middlewareGroups = [
    'web' => [
        // ...
        \Bepsvpt\SecureHeaders\SecureHeadersMiddleware::class,
    ],
];

// Em config/secure-headers.php (configuração personalizada)
return [
    'x-frame-options' => 'SAMEORIGIN',
    'x-xss-protection' => '1; mode=block',
    'x-content-type-options' => 'nosniff',
    'referrer-policy' => 'strict-origin-when-cross-origin',
    'permissions-policy' => [
        'accelerometer' => ['self'],
        'camera' => ['self'],
        'geolocation' => ['self'],
        'gyroscope' => ['self'],
        'magnetometer' => ['self'],
        'microphone' => ['self'],
        'payment' => ['self'],
        'usb' => ['self'],
    ],
    'strict-transport-security' => [
        'enable' => true,
        'max-age' => 31536000,
        'include-sub-domains' => true,
        'preload' => false,
    ],
    'clear-site-data' => [
        'enable' => false,
        'all' => false,
        'cache' => true,
        'cookies' => true,
        'storage' => true,
        'executionContexts' => true,
    ],
    'content-security-policy' => [
        'enable' => true,
        'default-src' => ['self'],
        'script-src' => ['self', 'https://js.stripe.com'],
        'style-src' => ['self', 'https://fonts.googleapis.com'],
        'img-src' => ['self', 'data:', 'https://secure.example.com'],
        'font-src' => ['self', 'https://fonts.gstatic.com'],
        'form-action' => ['self'],
        'frame-ancestors' => ['self'],
        'block-all-mixed-content' => true,
        'upgrade-insecure-requests' => true,
    ],
];</pre>
        </div>

        <div class="best-practice">
            <h4>Principais Cabeçalhos de Segurança</h4>
            <ul>
                <li><strong>Content-Security-Policy (CSP):</strong> Controla quais recursos o navegador pode carregar
                </li>
                <li><strong>Strict-Transport-Security (HSTS):</strong> Força conexões HTTPS</li>
                <li><strong>X-Content-Type-Options:</strong> Previne MIME type sniffing</li>
                <li><strong>X-Frame-Options:</strong> Protege contra clickjacking</li>
                <li><strong>X-XSS-Protection:</strong> Filtro adicional contra XSS em navegadores mais antigos</li>
                <li><strong>Referrer-Policy:</strong> Controla informações de referência enviadas em requisições</li>
                <li><strong>Permissions-Policy:</strong> Controla quais APIs o navegador pode usar</li>
                <li><strong>Clear-Site-Data:</strong> Limpa dados do site armazenados no navegador</li>
            </ul>
        </div>

        <div class="code-block">
            <h4>Implementação Manual de Cabeçalhos</h4>
            <pre>
// Em app/Http/Middleware/SecurityHeaders.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeaders
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(self)');
        
        if ($request->secure()) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        }
        
        return $response;
    }
}

// Registrando o middleware em app/Http/Kernel.php
protected $middleware = [
    // ...
    \App\Http\Middleware\SecurityHeaders::class,
];</pre>
        </div>

        <div class="note">
            <p>No Laravel 12, é recomendado usar o pacote <code>bepsvpt/secure-headers</code> para uma implementação
                mais completa e configurável de cabeçalhos de segurança. Este pacote é mantido regularmente e segue as
                melhores práticas de segurança.</p>
        </div>
    </section>

    <section id="rate-limiting" class="manual-section">
        <h2>9. Rate Limiting</h2>
        <p>O rate limiting é uma técnica essencial para proteger sua aplicação contra ataques de força bruta, DDoS e
            abuso de API.</p>

        <div class="code-block">
            <h4>Configuração Básica</h4>
            <pre>
// Em routes/api.php
Route::middleware('throttle:api')
    ->group(function () {
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/register', [AuthController::class, 'register']);
    });

// Em routes/web.php
Route::middleware('throttle:6,1') // 6 requisições por minuto
    ->group(function () {
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/password/email', [PasswordResetController::class, 'sendResetLinkEmail']);
    });

// Em config/cache.php (configurar driver para rate limiting)
'stores' => [
    // ...
    'rate_limiting' => [
        'driver' => 'redis',
        'connection' => 'cache',
    ],
],

// Em app/Providers/RouteServiceProvider.php
protected function configureRateLimiting(): void
{
    RateLimiter::for('api', function (Request $request) {
        return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
    });

    // Rate limiting específico para autenticação
    RateLimiter::for('auth', function (Request $request) {
        return Limit::perMinute(5)->by($request->ip());
    });

    // Rate limiting para endpoints sensíveis
    RateLimiter::for('sensitive', function (Request $request) {
        return Limit::perMinute(3)->by($request->user()?->id ?: $request->ip());
    });
}</pre>
        </div>

        <div class="code-block">
            <h4>Rate Limiting Avançado</h4>
            <pre>
// Em app/Providers/RouteServiceProvider.php
protected function configureRateLimiting(): void
{
    // Rate limiting dinâmico baseado no tipo de usuário
    RateLimiter::for('api', function (Request $request) {
        $user = $request->user();
        
        if (!$user) {
            // Usuários não autenticados: limite mais restritivo
            return Limit::perMinute(20)->by($request->ip());
        }
        
        if ($user->hasRole('premium')) {
            // Usuários premium: limite mais alto
            return Limit::perMinute(120)->by($user->id);
        }
        
        // Usuários padrão
        return Limit::perMinute(60)->by($user->id);
    });
    
    // Rate limiting com múltiplos limites
    RateLimiter::for('uploads', function (Request $request) {
        return [
            Limit::perMinute(10)->by($request->user()?->id ?: $request->ip()),
            Limit::perDay(100)->by($request->user()?->id ?: $request->ip()),
        ];
    });
    
    // Rate limiting com resposta personalizada
    RateLimiter::for('login', function (Request $request) {
        return Limit::perMinute(5)
            ->by($request->ip())
            ->response(function () {
                Log::warning('Possível tentativa de força bruta', [
                    'ip' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                    'email' => request()->input('email')
                ]);
                
                return response()->json([
                    'status' => 'error',
                    'message' => 'Muitas tentativas de login. Por favor, tente novamente em alguns minutos.',
                    'code' => 'TOO_MANY_ATTEMPTS'
                ], 429);
            });
    });
}</pre>
        </div>

        <div class="code-block">
            <h4>Middleware Personalizado para Rate Limiting</h4>
            <pre>
// Em app/Http/Middleware/CustomRateLimiter.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Cache\RateLimiter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CustomRateLimiter
{
    protected $limiter;

    public function __construct(RateLimiter $limiter)
    {
        $this->limiter = $limiter;
    }

    public function handle(Request $request, Closure $next, int $maxAttempts = 60, int $decayMinutes = 1): Response
    {
        // Chave personalizada baseada em múltiplos fatores
        $key = $this->resolveRequestSignature($request);
        
        // Verificar se o limite foi atingido
        if ($this->limiter->tooManyAttempts($key, $maxAttempts)) {
            // Registrar tentativa de abuso
            Log::warning('Rate limit excedido', [
                'ip' => $request->ip(),
                'user_id' => $request->user()?->id,
                'route' => $request->path(),
                'key' => $key
            ]);
            
            return $this->buildResponse($key, $maxAttempts);
        }
        
        // Incrementar o contador de tentativas
        $this->limiter->hit($key, $decayMinutes * 60);
        
        // Adicionar cabeçalhos de rate limiting à resposta
        $response = $next($request);
        
        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts)
        );
    }
    
    protected function resolveRequestSignature(Request $request): string
    {
        // Criar uma assinatura única para a requisição
        // Pode combinar IP, user agent, rota, etc.
        $signature = $request->ip();
        
        if ($user = $request->user()) {
            $signature .= '|' . $user->id;
        }
        
        // Adicionar fingerprint do dispositivo se disponível
        if ($request->has('device_fingerprint')) {
            $signature .= '|' . $request->input('device_fingerprint');
        }
        
        return sha1($signature . '|' . $request->path());
    }
    
    protected function buildResponse(string $key, int $maxAttempts): Response
    {
        $retryAfter = $this->limiter->availableIn($key);
        
        return response()->json([
            'status' => 'error',
            'message' => 'Limite de requisições excedido. Por favor, tente novamente mais tarde.',
            'retry_after' => $retryAfter
        ], 429)->withHeaders([
            'Retry-After' => $retryAfter,
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => 0,
        ]);
    }
    
    protected function addHeaders(Response $response, int $maxAttempts, int $remainingAttempts): Response
    {
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
        ]);
        
        return $response;
    }
    
    protected function calculateRemainingAttempts(string $key, int $maxAttempts): int
    {
        return $maxAttempts - $this->limiter->attempts($key) + 1;
    }
}</pre>
        </div>

        <div class="best-practice">
            <h4>Melhores Práticas para Rate Limiting</h4>
            <ul>
                <li>Aplique limites mais restritivos para endpoints sensíveis (login, registro, reset de senha)</li>
                <li>Use diferentes limites para usuários autenticados e não autenticados</li>
                <li>Considere limites por IP, usuário e/ou combinação de ambos</li>
                <li>Implemente limites em diferentes escalas de tempo (por minuto, hora, dia)</li>
                <li>Registre tentativas de abuso quando os limites forem excedidos</li>
                <li>Forneça cabeçalhos HTTP informativos sobre os limites (X-RateLimit-*)</li>
                <li>Configure alertas para padrões suspeitos de uso</li>
                <li>Use Redis ou outro armazenamento distribuído para rate limiting em ambientes com múltiplos
                    servidores</li>
                <li>Considere implementar backoff exponencial para tentativas repetidas</li>
            </ul>
        </div>
    </section>

    <section id="seguranca-api" class="manual-section">
        <h2>10. Segurança em APIs</h2>
        <p>As APIs requerem considerações especiais de segurança, especialmente quando expostas publicamente.</p>

        <div class="code-block">
            <h4>Autenticação de API</h4>
            <pre>
// Em routes/api.php
Route::prefix('v1')->group(function () {
    // Rotas públicas
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    
    // Rotas protegidas
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/user', [UserController::class, 'show']);
        Route::put('/user', [UserController::class, 'update']);
        
        // Rotas com verificação de escopo
        Route::middleware(['ability:manage-users'])->group(function () {
            Route::apiResource('users', AdminUserController::class);
        });
    });
});

// Em app/Http/Controllers/Api/AuthController.php
public function login(Request $request): JsonResponse
{
    $request->validate([
        'email' => 'required|email',
        'password' => 'required',
        'device_name' => 'required|string|max:255',
    ]);
    
    $user = User::where('email', $request->email)->first();
    
    if (! $user || ! Hash::check($request->password, $user->password)) {
        // Registrar tentativa falha
        Log::warning('Falha na tentativa de login via API', [
            'email' => $request->email,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);
        
        // Atrasar resposta para dificultar ataques de força bruta
        sleep(1);
        
        return response()->json([
            'message' => 'As credenciais fornecidas estão incorretas.'
        ], 401);
    }
    
    // Revogar tokens antigos (opcional)
    if ($request->boolean('revoke_old')) {
        $user->tokens()->where('name', $request->device_name)->delete();
    }
    
    // Criar token com escopos apropriados
    $abilities = $this->getAbilitiesForUser($user);
    $token = $user->createToken($request->device_name, $abilities);
    
    // Registrar login bem-sucedido
    Log::info('Login via API realizado com sucesso', [
        'user_id' => $user->id,
        'ip' => $request->ip()
    ]);
    
    return response()->json([
        'token' => $token->plainTextToken,
        'user' => new UserResource($user),
        'abilities' => $abilities
    ]);
}</pre>
        </div>

        <div class="code-block">
            <h4>Validação e Sanitização em APIs</h4>
            <pre>
// Em app/Http/Requests/Api/UpdateUserRequest.php
namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->id === $this->route('user')->id || 
               $this->user()->can('manage-users');
    }
    
    public function rules(): array
    {
        $userId = $this->route('user')->id;
        
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => [
                'sometimes', 
                'email:rfc,dns', 
                Rule::unique('users')->ignore($userId)
            ],
            'bio' => ['sometimes', 'nullable', 'string', 'max:1000'],
            'settings' => ['sometimes', 'array'],
            'settings.notifications' => ['sometimes', 'boolean'],
            'settings.theme' => ['sometimes', 'string', Rule::in(['light', 'dark', 'system'])],
        ];
    }
    
    protected function passedValidation(): void
    {
        // Sanitizar dados
        if ($this->has('bio')) {
            $this->merge([
                'bio' => strip_tags($this->bio)
            ]);
        }
        
        // Normalizar email
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->email)
            ]);
        }
    }
    
    // Personalizar mensagens de erro para APIs
    public function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        throw new \Illuminate\Validation\ValidationException($validator, response()->json([
            'message' => 'Os dados fornecidos são inválidos.',
            'errors' => $validator->errors()
        ], 422));
    }
}</pre>
        </div>

        <div class="code-block">
            <h4>Proteção contra Vazamento de Dados</h4>
            <pre>
// Em app/Http/Resources/UserResource.php
namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
        
        // Incluir email apenas para o próprio usuário ou administradores
        if ($request->user()->id === $this->id || $request->user()->can('view-user-details')) {
            $data['email'] = $this->email;
            $data['phone'] = $this->when($this->phone, function () {
                return $this->phone;
            });
            $data['settings'] = $this->settings;
        }
        
        // Incluir dados sensíveis apenas para administradores
        if ($request->user()->can('manage-users')) {
            $data['last_login_at'] = $this->when($this->last_login_at, function () {
                return $this->last_login_at->toIso8601String();
            });
            $data['login_count'] = $this->login_count;
            $data['is_active'] = $this->is_active;
        }
        
        return $data;
    }
}</pre>
        </div>

        <div class="best-practice">
            <h4>Melhores Práticas para Segurança de API</h4>
            <ul>
                <li>Use HTTPS para todas as comunicações de API</li>
                <li>Implemente autenticação robusta (OAuth 2.0, JWT ou Sanctum)</li>
                <li>Utilize escopos/habilidades para controle de acesso granular</li>
                <li>Valide e sanitize todas as entradas</li>
                <li>Implemente rate limiting para prevenir abusos</li>
                <li>Use cabeçalhos de segurança apropriados</li>
                <li>Evite expor informações sensíveis em respostas de erro</li>
                <li>Implemente versionamento de API para atualizações seguras</li>
                <li>Registre e monitore o uso da API para detectar comportamentos anômalos</li>
                <li>Considere a implementação de expiração e rotação de tokens</li>
                <li>Utilize CORS corretamente para controlar o acesso de diferentes origens</li>
            </ul>
        </div>

        <div class="code-block">
            <h4>Configuração de CORS</h4>
            <pre>
// Em config/cors.php
return [
    'paths' => ['api/*', 'sanctum/csrf-cookie'],
    'allowed_methods' => ['*'],
    'allowed_origins' => [env('FRONTEND_URL', 'http://localhost:3000')],
    'allowed_origins_patterns' => [],
    'allowed_headers' => ['*'],
    'exposed_headers' => ['X-RateLimit-Limit', 'X-RateLimit-Remaining'],
    'max_age' => 0,
    'supports_credentials' => true,
];</pre>
        </div>
    </section>

    <section id="dependencias" class="manual-section">
        <h2>11. Segurança de Dependências</h2>
        <p>Manter as dependências seguras é crucial para a segurança geral da aplicação.</p>

        <div class="code-block">
            <h4>Verificação de Vulnerabilidades</h4>
            <pre>
// Verificar vulnerabilidades com Composer
composer audit

// Adicionar verificação ao pipeline CI/CD
// Em .github/workflows/security.yml
name: Security Checks

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 0 * * 0'  # Executar semanalmente

jobs:
  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          
      - name: Install dependencies
        run: composer install --prefer-dist --no-progress
        
      - name: Security check
        run: composer audit
        
      - name: Static analysis
        run: |
          composer require --dev phpstan/phpstan
          vendor/bin/phpstan analyse app --level=5</pre>
        </div>

        <div class="best-practice">
            <h4>Melhores Práticas para Segurança de Dependências</h4>
            <ul>
                <li>Execute <code>composer audit</code> regularmente para verificar vulnerabilidades conhecidas</li>
                <li>Mantenha todas as dependências atualizadas</li>
                <li>Configure alertas de segurança para seu repositório (GitHub Security Alerts, Dependabot)</li>
                <li>Utilize ferramentas de análise estática de código (PHPStan, Psalm)</li>
                <li>Implemente verificações de segurança em seu pipeline CI/CD</li>
                <li>Considere usar ferramentas como OWASP Dependency-Check</li>
                <li>Documente e revise as políticas de atualização de dependências</li>
                <li>Mantenha um inventário de todas as dependências e suas licenças</li>
                <li>Avalie cuidadosamente novas dependências antes de adicioná-las ao projeto</li>
            </ul>
        </div>

        <div class="code-block">
            <h4>Automatização de Atualizações</h4>
            <pre>
// Configuração do Dependabot
// Em .github/dependabot.yml
version: 2
updates:
  - package-ecosystem: "composer"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    versioning-strategy: auto
    allow:
      - dependency-type: "direct"
    labels:
      - "dependencies"
      - "security"
    
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    versioning-strategy: auto
    labels:
      - "dependencies"
      - "security"</pre>
        </div>
    </section>

    <section id="checklist" class="manual-section">
        <h2>12. Checklist de Segurança</h2>
        <p>Use esta checklist para garantir que sua aplicação Laravel 12 esteja seguindo as melhores práticas de
            segurança.</p>

        <div class="checklist-container">
            <div class="checklist-group">
                <h3>Autenticação</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Implementar política de senhas fortes (mínimo 12 caracteres)</li>
                    <li><input type="checkbox"> Verificar senhas contra listas de senhas vazadas</li>
                    <li><input type="checkbox"> Implementar autenticação de dois fatores (2FA)</li>
                    <li><input type="checkbox"> Configurar bloqueio de conta após tentativas falhas</li>
                    <li><input type="checkbox"> Implementar processo seguro de recuperação de senha</li>
                    <li><input type="checkbox"> Usar tokens seguros para "lembrar-me" e sessões</li>
                    <li><input type="checkbox"> Registrar eventos de autenticação (login, logout, falhas)</li>
                    <li><input type="checkbox"> Implementar expiração de sessão após inatividade</li>
                    <li><input type="checkbox"> Considerar WebAuthn para autenticação sem senha</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Autorização</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Implementar controle de acesso baseado em papéis (RBAC)</li>
                    <li><input type="checkbox"> Usar Gates e Policies para autorização granular</li>
                    <li><input type="checkbox"> Verificar autorização em todas as rotas e controllers</li>
                    <li><input type="checkbox"> Implementar middleware de autorização para proteção de rotas</li>
                    <li><input type="checkbox"> Usar tipagem estrita em todas as definições de autorização</li>
                    <li><input type="checkbox"> Implementar verificação de permissões em templates Blade</li>
                    <li><input type="checkbox"> Configurar políticas de autorização para APIs</li>
                    <li><input type="checkbox"> Implementar auditoria de decisões de autorização</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Proteção de Dados</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Criptografar dados sensíveis em repouso</li>
                    <li><input type="checkbox"> Implementar hashing seguro para senhas</li>
                    <li><input type="checkbox"> Sanitizar todas as entradas de usuário</li>
                    <li><input type="checkbox"> Mascarar dados sensíveis em logs e respostas</li>
                    <li><input type="checkbox"> Implementar auditoria para alterações em dados sensíveis</li>
                    <li><input type="checkbox"> Configurar políticas de retenção de dados</li>
                    <li><input type="checkbox"> Implementar backup seguro de dados</li>
                    <li><input type="checkbox"> Usar atributos criptografados em modelos Eloquent</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Proteção contra Ataques</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Implementar proteção CSRF em todos os formulários</li>
                    <li><input type="checkbox"> Configurar cabeçalhos HTTP de segurança</li>
                    <li><input type="checkbox"> Implementar Content Security Policy (CSP)</li>
                    <li><input type="checkbox"> Configurar SameSite cookies apropriadamente</li>
                    <li><input type="checkbox"> Implementar rate limiting para endpoints sensíveis</li>
                    <li><input type="checkbox"> Proteger contra ataques de SQL Injection</li>
                    <li><input type="checkbox"> Implementar proteção contra XSS</li>
                    <li><input type="checkbox"> Configurar CORS para APIs</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Segurança de API</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Implementar autenticação robusta (Sanctum/JWT)</li>
                    <li><input type="checkbox"> Configurar escopos/habilidades para tokens</li>
                    <li><input type="checkbox"> Implementar expiração e rotação de tokens</li>
                    <li><input type="checkbox"> Validar e sanitizar todas as entradas de API</li>
                    <li><input type="checkbox"> Implementar rate limiting específico para API</li>
                    <li><input type="checkbox"> Configurar respostas de erro seguras</li>
                    <li><input type="checkbox"> Implementar versionamento de API</li>
                    <li><input type="checkbox"> Registrar e monitorar uso da API</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Infraestrutura e Configuração</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Forçar HTTPS em produção</li>
                    <li><input type="checkbox"> Proteger variáveis de ambiente (.env)</li>
                    <li><input type="checkbox"> Configurar permissões de arquivo adequadas</li>
                    <li><input type="checkbox"> Manter dependências atualizadas e seguras</li>
                    <li><input type="checkbox"> Implementar monitoramento de segurança</li>
                    <li><input type="checkbox"> Configurar backups seguros</li>
                    <li><input type="checkbox"> Implementar logging seguro</li>
                    <li><input type="checkbox"> Configurar firewall e proteções de rede</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Testes de Segurança</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Realizar testes de penetração regulares</li>
                    <li><input type="checkbox"> Implementar análise estática de código</li>
                    <li><input type="checkbox"> Testar vulnerabilidades de dependências</li>
                    <li><input type="checkbox"> Realizar revisões de código focadas em segurança</li>
                    <li><input type="checkbox"> Testar proteções contra ataques comuns</li>
                    <li><input type="checkbox"> Verificar configurações de segurança em ambientes</li>
                    <li><input type="checkbox"> Implementar testes automatizados de segurança</li>
                    <li><input type="checkbox"> Documentar e corrigir vulnerabilidades encontradas</li>
                </ul>
            </div>
        </div>

        <div class="conclusion">
            <h3>Conclusão</h3>
            <p>A segurança é um processo contínuo, não um estado final. Mantenha-se atualizado sobre as melhores
                práticas de segurança e novas vulnerabilidades. O Laravel 12 fornece muitas ferramentas e recursos para
                construir aplicações seguras, mas é responsabilidade dos desenvolvedores implementá-las corretamente e
                mantê-las atualizadas.</p>
            <p>Recomendamos revisar este manual regularmente e atualizar suas práticas de segurança conforme necessário.
                Lembre-se de que a segurança deve ser considerada em todas as fases do desenvolvimento, desde o design
                até a implantação e manutenção.</p>
        </div>

        <footer class="manual-footer">
            <p>Manual de Segurança - Laravel 12 | Última atualização: Junho 2024</p>
        </footer>
</body>

</html>