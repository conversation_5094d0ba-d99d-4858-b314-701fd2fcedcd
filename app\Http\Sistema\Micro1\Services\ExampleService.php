<?php

namespace App\Http\Sistema\Micro1\Services;

use App\Http\Sistema\Micro1\Repositories\ExampleRepository;
use App\Services\ServiceAbstract;


class ExampleService extends ServiceAbstract
{
    /**
     * Construtor
     *
     * @param ExampleRepository $repository
     */
    public function __construct(ExampleRepository $repository)
    {
        parent::__construct($repository);
    }

    /**
     * Obtém todos os usuários
     *
     * @return array
     */
    public function getAllUsers(): array
    {
        return $this->repository->findAll()->toArray();
    }
}
