<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Segurança - Laravel 12</title>
    <link rel="stylesheet" href="css/manual.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Segurança</h1>
        <p>Guia para implementação de práticas de segurança no Laravel 12</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#autenticacao">Autenticação</a></li>
            <li><a href="#autorizacao">Autorização</a></li>
            <li><a href="#protecao-dados">Proteção de Dados</a></li>
            <li><a href="#csrf">CSRF</a></li>
            <li><a href="#xss">XSS</a></li>
            <li><a href="#sql-injection">SQL Injection</a></li>
            <li><a href="#headers">Cabeçalhos HTTP</a></li>
            <li><a href="#rate-limiting">Rate Limiting</a></li>
            <li><a href="#seguranca-api">APIs</a></li>
            <li><a href="#dependencias">Dependências</a></li>
            <li><a href="#seguranca-modular">Segurança Modular</a></li>
            <li><a href="#checklist">Checklist</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#autenticacao">2. Autenticação</a></li>
            <li><a href="#autorizacao">3. Autorização</a></li>
            <li><a href="#protecao-dados">4. Proteção de Dados</a></li>
            <li><a href="#csrf">5. Proteção CSRF</a></li>
            <li><a href="#xss">6. Proteção XSS</a></li>
            <li><a href="#sql-injection">7. Proteção contra SQL Injection</a></li>
            <li><a href="#headers">8. Cabeçalhos HTTP de Segurança</a></li>
            <li><a href="#rate-limiting">9. Rate Limiting</a></li>
            <li><a href="#seguranca-api">10. Segurança em APIs</a></li>
            <li><a href="#dependencias">11. Segurança de Dependências</a></li>
            <li><a href="#seguranca-modular">12. Segurança em Arquitetura Modular</a>
                <ul>
                    <li><a href="#seguranca-microsservicos">12.1. Segurança em Microsserviços</a></li>
                    <li><a href="#comunicacao-segura">12.2. Comunicação Segura entre Microsserviços</a></li>
                    <li><a href="#permissoes-microsservicos">12.3. Gerenciamento de Permissões em Microsserviços</a>
                    </li>
                </ul>
            </li>
            <li><a href="#checklist">13. Checklist de Segurança</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <p class="intro-text">A segurança é um aspecto crítico de qualquer aplicação web moderna. Este manual estabelece
            os padrões e
            práticas recomendadas para implementação de segurança em nossa aplicação Laravel 12, protegendo dados
            sensíveis,
            prevenindo vulnerabilidades comuns e garantindo uma experiência segura para os usuários.</p>

        <p>As diretrizes aqui apresentadas são baseadas em práticas estabelecidas pela indústria, incluindo as
            recomendações da OWASP (Open Web Application Security Project) e adequadas às especificidades do framework
            Laravel 12.</p>

        <div class="note">
            <p><strong>Nota:</strong> Este documento é complementar aos Manuais de Arquitetura, Implementação e Logging.
                As diretrizes aqui apresentadas devem ser seguidas por todos os componentes da aplicação.</p>
        </div>

        <div class="key-points">
            <h3>Novidades de Segurança no Laravel 12</h3>
            <ul>
                <li><strong>Tipagem Estrita:</strong> Maior segurança de tipos em todo o framework</li>
                <li><strong>Validação Aprimorada:</strong> Novos recursos de validação com tipagem forte</li>
                <li><strong>Proteção CSRF Aprimorada:</strong> Melhorias na proteção contra ataques CSRF</li>
                <li><strong>Suporte Nativo a WebAuthn:</strong> Autenticação sem senha usando padrões FIDO2</li>
                <li><strong>Melhorias no Rate Limiting:</strong> Controle mais granular de limites de requisição</li>
                <li><strong>Sanitização de Dados Aprimorada:</strong> Novas ferramentas para sanitização de entrada</li>
                <li><strong>Melhorias no Sistema de Permissões:</strong> Autorização mais robusta e flexível</li>
                <li><strong>Suporte a PHP 8.2+:</strong> Aproveitando recursos de segurança das versões mais recentes do
                    PHP</li>
            </ul>
        </div>

        <h3>Princípios de Segurança</h3>
        <ul>
            <li><strong>Defesa em Profundidade:</strong> Implementar múltiplas camadas de segurança</li>
            <li><strong>Privilégio Mínimo:</strong> Conceder apenas as permissões estritamente necessárias</li>
            <li><strong>Validação de Entrada:</strong> Nunca confiar em dados fornecidos pelo usuário</li>
            <li><strong>Codificação de Saída:</strong> Sempre sanitizar dados antes de exibi-los</li>
            <li><strong>Falha Segura:</strong> Em caso de erro, falhar de forma segura e sem revelar informações
                sensíveis</li>
            <li><strong>Segurança por Design:</strong> Considerar aspectos de segurança desde o início do projeto</li>
            <li><strong>Auditabilidade:</strong> Registrar eventos relevantes para análise de segurança</li>
        </ul>
    </section>

    <section id="autenticacao" class="manual-section">
        <h2>2. Autenticação</h2>
        <p>A autenticação é o processo de verificar a identidade de um usuário. Uma implementação robusta é essencial
            para proteger recursos e dados sensíveis.</p>

        <section id="auth-password" class="subsection">
            <h3>2.1. Autenticação por Senha</h3>
            <p>A autenticação baseada em senha deve seguir estas diretrizes:</p>

            <div class="best-practice">
                <h4>Requisitos para Senhas</h4>
                <ul>
                    <li>Comprimento mínimo de 12 caracteres (recomendado no Laravel 12)</li>
                    <li>Combinação de letras maiúsculas, minúsculas, números e símbolos</li>
                    <li>Verificação contra senhas comuns e vazadas</li>
                    <li>Não permitir senhas que contenham informações pessoais do usuário</li>
                    <li>Armazenamento utilizando hashing seguro (bcrypt ou Argon2id)</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Validação de Senha</h4>
                <pre>
// Validação de senha no formulário de registro
// Em app/Http/Sistema/Autenticacao/Requests/RegisterRequest.php
namespace App\Http\Sistema\Autenticacao\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => [
                'required',
                'string',
                'min:12', // Mínimo de 12 caracteres no Laravel 12
                'confirmed',
                'regex:/[a-z]/', // Pelo menos uma letra minúscula
                'regex:/[A-Z]/', // Pelo menos uma letra maiúscula
                'regex:/[0-9]/', // Pelo menos um número
                'regex:/[@$!%*#?&]/', // Pelo menos um caractere especial
                Password::defaults() // Regras padrão do Laravel (inclui verificação de exposição)
            ],
        ];
    }
}

// Implementação do login com tipagem estrita do Laravel 12
// Em app/Http/Sistema/Autenticacao/Controllers/AuthController.php
namespace App\Http\Sistema\Autenticacao\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Autenticacao\Services\AuthService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AuthController extends ControllerAbstract
{
    protected $response;

    public function __construct(AuthService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function authenticate(Request $request): RedirectResponse
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();

            // Registrar evento de login bem-sucedido
            activity()
                ->causedBy(Auth::user())
                ->log('login');

            return redirect()->intended('dashboard');
        }

        // Registrar tentativa falha (mas sem revelar se o usuário existe)
        Log::warning('Falha na tentativa de login', [
            'email' => $request->email,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        return back()->withErrors([
            'email' => 'As credenciais fornecidas não correspondem aos nossos registros.',
        ])->onlyInput('email');
    }
}</pre>
            </div>

            <h4>Recuperação de Senha</h4>
            <p>O processo de recuperação de senha deve ser seguro e não revelar informações sensíveis:</p>
            <ul>
                <li>Utilizar tokens de uso único com expiração (padrão do Laravel)</li>
                <li>Limitar número de tentativas de recuperação</li>
                <li>Não revelar se o e-mail existe na base de dados</li>
                <li>Enviar notificação ao usuário quando a senha for alterada</li>
            </ul>

            <div class="code-block">
                <h4>Reset de Senha</h4>
                <pre>
// Utilização do sistema padrão de reset de senha do Laravel 12
// Em app/Http/Sistema/Autenticacao/Controllers/PasswordResetController.php
namespace App\Http\Sistema\Autenticacao\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Autenticacao\Services\PasswordResetService;
use App\Responses\ApiResponse;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Log;

class PasswordResetController extends ControllerAbstract
{
    protected $response;

    public function __construct(PasswordResetService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function sendResetLinkEmail(Request $request): RedirectResponse
    {
        $request->validate(['email' => 'required|email']);

        // Enviar link de reset independentemente de encontrar o usuário
        $status = Password::sendResetLink(
            $request->only('email')
        );

        // Logar a tentativa de recuperação
        Log::info('Solicitação de recuperação de senha', [
            'email' => $request->email,
            'ip' => $request->ip(),
            'success' => $status === Password::RESET_LINK_SENT
        ]);

        return $status === Password::RESET_LINK_SENT
            ? back()->with(['status' => __($status)])
            : back()->withErrors(['email' => __($status)]);
    }
}</pre>
            </div>
        </section>

        <section id="auth-2fa" class="subsection">
            <h3>2.2. Autenticação de Dois Fatores (2FA)</h3>
            <p>A autenticação de dois fatores adiciona uma camada extra de segurança, exigindo que o usuário forneça
                duas formas diferentes de identificação:</p>

            <div class="code-block">
                <h4>Implementação de 2FA com Laravel Fortify</h4>
                <pre>
// Em app/Http/Sistema/Autenticacao/Providers/FortifyServiceProvider.php
namespace App\Http\Sistema\Autenticacao\Providers;

use Illuminate\Support\ServiceProvider;
use Laravel\Fortify\Fortify;
use Laravel\Fortify\Features;
use App\Http\Sistema\Autenticacao\Actions\EnableTwoFactorAuthentication;
use App\Http\Sistema\Autenticacao\Actions\DisableTwoFactorAuthentication;

class FortifyServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

        Fortify::registerView(function () {
            return view('auth.register');
        });

        Fortify::loginView(function () {
            return view('auth.login');
        });

        Fortify::twoFactorChallengeView(function () {
            return view('auth.two-factor-challenge');
        });
        
        // Configuração de recursos
        Fortify::$features = [
            Features::registration(),
            Features::resetPasswords(),
            Features::emailVerification(),
            Features::updateProfileInformation(),
            Features::updatePasswords(),
            Features::twoFactorAuthentication([
                'confirm' => true,
                'confirmPassword' => true,
            ]),
        ];
    }
}</pre>
            </div>

            <div class="note">
                <p>É recomendado implementar 2FA usando o algoritmo TOTP (Time-based One-time Password) com aplicativos
                    como Google Authenticator, Authy ou Microsoft Authenticator. Também é importante fornecer códigos de
                    recuperação para que os usuários não percam o acesso às suas contas.</p>
            </div>

            <h4>WebAuthn no Laravel 12</h4>
            <p>O Laravel 12 introduz suporte nativo para WebAuthn, permitindo autenticação sem senha usando dispositivos
                de segurança:</p>

            <div class="code-block">
                <h4>Configuração de WebAuthn</h4>
                <pre>
// Em app/Http/Sistema/Autenticacao/Providers/WebAuthnServiceProvider.php
namespace App\Http\Sistema\Autenticacao\Providers;

use Illuminate\Support\ServiceProvider;
use Laravel\WebAuthn\WebAuthnServiceProvider as BaseWebAuthnServiceProvider;

class WebAuthnServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->register(BaseWebAuthnServiceProvider::class);
    }

    public function boot(): void
    {
        // Configurações personalizadas para WebAuthn
    }
}

// Em app/Http/Sistema/Autenticacao/Models/User.php
namespace App\Http\Sistema\Autenticacao\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\WebAuthn\WebAuthnAuthenticatable;
use Laravel\WebAuthn\Contracts\WebAuthnAuthenticatable as WebAuthnAuthenticatableContract;

class User extends Authenticatable implements WebAuthnAuthenticatableContract
{
    use WebAuthnAuthenticatable;
    
    // ...
}

// Em routes/web.php
use Laravel\WebAuthn\Http\Controllers\WebAuthnRegisterController;
use Laravel\WebAuthn\Http\Controllers\WebAuthnLoginController;

Route::middleware(['auth'])->group(function () {
    // Rotas para registro de dispositivos
    Route::get('/webauthn/register', [WebAuthnRegisterController::class, 'create'])
        ->name('webauthn.register');
    Route::post('/webauthn/register', [WebAuthnRegisterController::class, 'store']);
    Route::delete('/webauthn/register/{id}', [WebAuthnRegisterController::class, 'destroy'])
        ->name('webauthn.destroy');
});

// Rotas para autenticação
Route::get('/webauthn/login', [WebAuthnLoginController::class, 'create'])
    ->name('webauthn.login');
Route::post('/webauthn/login', [WebAuthnLoginController::class, 'store']);</pre>
            </div>
        </section>

        <section id="auth-sanctum" class="subsection">
            <h3>2.3. Autenticação de API com Sanctum</h3>
            <p>Para APIs RESTful ou aplicativos SPA, use Laravel Sanctum para autenticação baseada em token:</p>

            <div class="code-block">
                <h4>Configuração do Sanctum</h4>
                <pre>
// Em app/Http/Sistema/Autenticacao/Controllers/ApiAuthController.php
namespace App\Http\Sistema\Autenticacao\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Autenticacao\Services\ApiAuthService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Http\Sistema\Autenticacao\Models\User;

class ApiAuthController extends ControllerAbstract
{
    protected $response;

    public function __construct(ApiAuthService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function login(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'device_name' => 'required|string|max:255',
        ]);
        
        $user = User::where('email', $request->email)->first();
        
        if (! $user || ! Hash::check($request->password, $user->password)) {
            Log::warning('Falha na tentativa de login via API', [
                'email' => $request->email,
                'ip' => $request->ip(),
                'device_name' => $request->device_name
            ]);

            return $this->response->authenticationError('Credenciais inválidas.');
        }

        // Criar token com escopos apropriados baseados nas permissões do usuário
        $abilities = $this->service->getAbilitiesForUser($user);
        $token = $user->createToken($request->device_name, $abilities);

        Log::info('Login via API realizado com sucesso', [
            'user_id' => $user->id,
            'email' => $user->email,
            'ip' => $request->ip(),
            'device_name' => $request->device_name
        ]);

        return $this->response->loginSuccess($user, $token->plainTextToken);
    }
    
    public function logout(Request $request): JsonResponse
    {
        // Revogar token atual
        $request->user()->currentAccessToken()->delete();

        return $this->response->logoutSuccess();
    }
}</pre>
            </div>

            <h4>Proteção de Rotas com Sanctum</h4>

            <div class="code-block">
                <h4>Configuração de Rotas Protegidas</h4>
                <pre>
// Em routes/api.php
Route::prefix('v1')->group(function () {
    // Rotas públicas
    Route::post('/login', [\App\Http\Sistema\Autenticacao\Controllers\ApiAuthController::class, 'login']);
    Route::post('/register', [\App\Http\Sistema\Autenticacao\Controllers\ApiAuthController::class, 'register']);
    
    // Rotas protegidas
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/user', [\App\Http\Sistema\Usuarios\Controllers\UserController::class, 'show']);
        Route::put('/user', [\App\Http\Sistema\Usuarios\Controllers\UserController::class, 'update']);
        
        // Rotas com verificação de escopo
        Route::middleware(['ability:manage-users'])->group(function () {
            Route::apiResource('users', \App\Http\Sistema\Admin\Controllers\AdminUserController::class);
        });
    });
});</pre>
            </div>

            <div class="best-practice">
                <h4>Melhorias no Sanctum para Laravel 12</h4>
                <ul>
                    <li>Suporte aprimorado para tokens de longa duração com rotação automática</li>
                    <li>Melhor integração com sistemas de permissões</li>
                    <li>Suporte para revogação seletiva de tokens</li>
                    <li>Tipagem estrita em todas as interfaces</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="autorizacao" class="manual-section">
        <h2>3. Autorização</h2>
        <p>A autorização determina o que um usuário autenticado pode fazer dentro do sistema. O Laravel 12 fornece
            vários mecanismos para implementar controle de acesso granular com tipagem estrita.</p>

        <section id="auth-gates" class="subsection">
            <h3>3.1. Gates e Policies</h3>
            <p>Gates e Policies permitem definir regras claras de autorização para diferentes partes da aplicação:</p>

            <div class="code-block">
                <h4>Definindo Gates</h4>
                <pre>
// Em app/Http/Sistema/Autenticacao/Providers/AuthServiceProvider.php
namespace App\Http\Sistema\Autenticacao\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Http\Sistema\Autenticacao\Models\User;
use App\Http\Sistema\Blog\Models\Post;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        \App\Http\Sistema\Blog\Models\Post::class => \App\Http\Sistema\Blog\Policies\PostPolicy::class,
    ];

    public function boot(): void
    {
        $this->registerPolicies();

        // Gate simples
        Gate::define('view-dashboard', function (User $user): bool {
            return $user->role === 'admin' || $user->role === 'manager';
        });

        // Gate com parâmetros adicionais
        Gate::define('update-post', function (User $user, Post $post): bool {
            return $user->id === $post->user_id || $user->hasRole('editor');
        });
    }
}</pre>
            </div>

            <p>Para models específicos, as Policies fornecem uma abordagem orientada a objetos para autorização:</p>

            <div class="code-block">
                <h4>Implementando Policies</h4>
                <pre>
// Em app/Http/Sistema/Blog/Policies/PostPolicy.php
namespace App\Http\Sistema\Blog\Policies;

use App\Http\Sistema\Autenticacao\Models\User;
use App\Http\Sistema\Blog\Models\Post;
use Illuminate\Auth\Access\HandlesAuthorization;

class PostPolicy
{
    use HandlesAuthorization;

    public function update(User $user, Post $post): bool
    {
        return $user->id === $post->user_id || $user->hasPermissionTo('edit posts');
    }

    public function delete(User $user, Post $post): bool
    {
        return $user->id === $post->user_id || $user->hasPermissionTo('delete posts');
    }
}

// Em app/Http/Sistema/Blog/Controllers/PostController.php
namespace App\Http\Sistema\Blog\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Blog\Services\PostService;
use App\Http\Sistema\Blog\Models\Post;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PostController extends ControllerAbstract
{
    protected $response;

    public function __construct(PostService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function edit(Post $post): View
    {
        $this->authorize('update', $post);

        return view('posts.edit', compact('post'));
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Novidades em Gates e Policies no Laravel 12</h4>
                <ul>
                    <li>Tipagem estrita em todas as definições de gates e policies</li>
                    <li>Melhor suporte para injeção de dependências</li>
                    <li>Suporte para gates condicionais baseados em contexto</li>
                    <li>Melhor integração com o sistema de cache para autorização de alto desempenho</li>
                </ul>
            </div>
        </section>

        <section id="auth-roles" class="subsection">
            <h3>3.2. Controle de Acesso Baseado em Papéis (RBAC)</h3>
            <p>O RBAC permite definir papéis (roles) com conjuntos específicos de permissões:</p>

            <div class="best-practice">
                <h4>Implementação com Spatie Permission Package</h4>
                <p>Recomendamos o uso do pacote <code>spatie/laravel-permission</code> para implementar RBAC de forma
                    robusta.</p>
            </div>

            <div class="code-block">
                <h4>Configuração do RBAC</h4>
                <pre>
// Em app/Http/Sistema/Autenticacao/Models/User.php
namespace App\Http\Sistema\Autenticacao\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;

    // ...
}

// Em app/Http/Sistema/Autenticacao/Database/Seeders/RolesAndPermissionsSeeder.php
namespace App\Http\Sistema\Autenticacao\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        // Criar permissões por microsserviço
        // Permissões para o microsserviço Blog
        Permission::create(['name' => 'blog.posts.create']);
        Permission::create(['name' => 'blog.posts.edit']);
        Permission::create(['name' => 'blog.posts.delete']);
        Permission::create(['name' => 'blog.posts.publish']);

        // Permissões para o microsserviço Usuários
        Permission::create(['name' => 'usuarios.view']);
        Permission::create(['name' => 'usuarios.create']);
        Permission::create(['name' => 'usuarios.edit']);
        Permission::create(['name' => 'usuarios.delete']);

        // Criar papéis
        $adminRole = Role::create(['name' => 'admin']);
        $editorRole = Role::create(['name' => 'editor']);
        $userRole = Role::create(['name' => 'user']);

        // Atribuir permissões aos papéis
        $adminRole->givePermissionTo(Permission::all());
        
        $editorRole->givePermissionTo([
            'blog.posts.create', 
            'blog.posts.edit', 
            'blog.posts.delete', 
            'blog.posts.publish'
        ]);
        
        $userRole->givePermissionTo([
            'blog.posts.create', 
            'blog.posts.edit'
        ]);
    }
}

// Em app/Http/Sistema/Blog/Controllers/PostController.php
namespace App\Http\Sistema\Blog\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Blog\Services\PostService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;

class PostController extends ControllerAbstract
{
    protected $response;

    public function __construct(PostService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function store(Request $request)
    {
        // Verificar permissão específica do microsserviço
        if (!$request->user()->can('blog.posts.create')) {
            return $this->response->error('Não autorizado', 403);
        }
        
        // Resto da implementação...
    }
}</pre>
            </div>

            <div class="note">
                <p>No Laravel 12, o pacote spatie/laravel-permission foi atualizado para aproveitar a tipagem estrita e
                    outros recursos do PHP 8.2+, tornando o sistema de permissões mais robusto e seguro.</p>
            </div>
        </section>

        <section id="auth-middleware" class="subsection">
            <h3>3.3. Middleware de Autorização</h3>
            <p>Os middlewares de autorização permitem proteger rotas com base em regras de negócio específicas:</p>

            <div class="code-block">
                <h4>Middleware Personalizado</h4>
                <pre>
// Criando um middleware personalizado para verificação de permissões em microsserviços
// Em app/Http/Middleware/CheckMicroservicePermission.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class CheckMicroservicePermission
{
    public function handle(Request $request, Closure $next, string $microsservico, string $permissao): Response
    {
        $user = $request->user();

        if (!$user) {
            abort(401, 'Não autenticado');
        }

        $permissaoCompleta = "{$microsservico}.{$permissao}";
        
        if (!$user->can($permissaoCompleta)) {
            // Registrar tentativa de acesso não autorizado
            Log::warning('Tentativa de acesso não autorizado', [
                'user_id' => $user->id,
                'permission' => $permissaoCompleta,
                'route' => $request->path(),
                'ip' => $request->ip()
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Acesso não autorizado',
                    'code' => 'PERMISSION_DENIED'
                ], 403);
            }

            abort(403, 'Acesso não autorizado');
        }

        return $next($request);
    }
}

// Registrando o middleware em app/Http/Kernel.php
protected $routeMiddleware = [
    // ...
    'micro.permission' => \App\Http\Middleware\CheckMicroservicePermission::class,
];

// Usando o middleware
Route::middleware(['auth:sanctum', 'micro.permission:blog,posts.edit'])->group(function () {
    Route::put('/posts/{post}', [\App\Http\Sistema\Blog\Controllers\PostController::class, 'update']);
});</pre>
            </div>

            <div class="note">
                <p>Combine vários middlewares para criar regras de acesso complexas e granulares. Sempre registre
                    tentativas de acesso não autorizado para detecção de potenciais abusos.</p>
            </div>
        </section>
    </section>

    <section id="protecao-dados" class="manual-section">
        <h2>4. Proteção de Dados</h2>
        <p>A proteção adequada dos dados é fundamental para garantir a confidencialidade e integridade das informações
            sensíveis.</p>

        <section id="dados-criptografia" class="subsection">
            <h3>4.1. Criptografia e Hashing</h3>
            <p>Laravel 12 fornece ferramentas aprimoradas para criptografar e fazer hash de dados sensíveis:</p>

            <div class="code-block">
                <h4>Criptografia e Hashing</h4>
                <pre>
// Criptografar dados sensíveis (reversível)
use Illuminate\Support\Facades\Crypt;

// Criptografar
$encryptedValue = Crypt::encrypt($sensitiveData);

// Descriptografar
try {
    $decryptedValue = Crypt::decrypt($encryptedValue);
} catch (DecryptException $e) {
    // Tratar erro de descriptografia
}

// Hash de senhas (irreversível)
use Illuminate\Support\Facades\Hash;

$hashedPassword = Hash::make($password);

// Verificar senha
if (Hash::check($inputPassword, $user->password)) {
    // Senha válida
}

// Rehash caso necessário (se o algoritmo de hash foi atualizado)
if (Hash::needsRehash($user->password)) {
    $user->password = Hash::make($inputPassword);
    $user->save();
}</pre>
            </div>

            <div class="best-practice">
                <h4>Criptografia de Atributos no Modelo</h4>
                <p>Automatize a criptografia de campos sensíveis nos modelos Eloquent:</p>
                <div class="code-block">
                    <pre>
// Em um modelo Eloquent dentro de um microsserviço
// Em app/Http/Sistema/Saude/Models/Patient.php
namespace App\Http\Sistema\Saude\Models;

use App\Traits\HasEncryptedAttributes;
use Illuminate\Database\Eloquent\Model;

class Patient extends Model
{
    use HasEncryptedAttributes;

    /**
     * Atributos que devem ser criptografados.
     *
     * @var array<int, string>
     */
    protected array $encrypted = [
        'social_security_number',
        'medical_record',
        'health_insurance_id',
    ];

    /**
     * Acessor para obter o valor descriptografado
     */
    public function getSocialSecurityNumberAttribute($value): ?string
    {
        return $this->decryptAttribute($value);
    }

    /**
     * Mutator para criptografar o valor antes de salvar
     */
    public function setSocialSecurityNumberAttribute($value): void
    {
        $this->attributes['social_security_number'] = $this->encryptAttribute($value);
    }
}

// Implementação do trait HasEncryptedAttributes
namespace App\Traits;

use Illuminate\Support\Facades\Crypt;

trait HasEncryptedAttributes
{
    /**
     * Criptografa um valor
     */
    protected function encryptAttribute(?string $value): ?string
    {
        if (is_null($value)) {
            return null;
        }

        return Crypt::encrypt($value);
    }

    /**
     * Descriptografa um valor
     */
    protected function decryptAttribute(?string $value): ?string
    {
        if (is_null($value)) {
            return null;
        }

        try {
            return Crypt::decrypt($value);
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            report($e);
            return null;
        }
    }
}</pre>
                </div>

                <div class="note">
                    <p>No Laravel 12, você também pode usar o pacote <code>laravel/encrypted-attributes</code> que
                        fornece uma implementação mais robusta e integrada para criptografia de atributos de modelo.</p>
                </div>
            </div>
        </section>

        <section id="dados-sanitizacao" class="subsection">
            <h3>4.2. Sanitização de Dados</h3>
            <p>A sanitização adequada de dados de entrada e saída é essencial para prevenir vulnerabilidades de
                segurança:</p>

            <div class="code-block">
                <h4>Sanitização de Entrada</h4>
                <pre>
// Validação e sanitização em um FormRequest específico de microsserviço
namespace App\Http\Sistema\Usuarios\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class StoreUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'regex:/^[\pL\s\-]+$/u'],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:255', 'unique:users'],
            'phone' => ['nullable', 'string', 'regex:/^[0-9\-\(\)\+\s]+$/'],
            'bio' => ['nullable', 'string', 'max:1000'],
            'website' => ['nullable', 'url', 'max:255'],
            'password' => [
                'required',
                'confirmed',
                Password::min(12)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()
            ],
        ];
    }

    /**
     * Sanitizar dados após validação
     */
    protected function passedValidation(): void
    {
        // Remover tags HTML do campo bio
        if ($this->has('bio')) {
            $this->merge([
                'bio' => strip_tags($this->bio)
            ]);
        }

        // Normalizar número de telefone
        if ($this->has('phone')) {
            $this->merge([
                'phone' => preg_replace('/[^0-9+]/', '', $this->phone)
            ]);
        }
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Sanitização de Saída</h4>
                <pre>
// Em um blade template
&lt;div class="user-content">
    {!! Str::of(e($user->bio))->markdown() !!}
&lt;/div>

// Em uma API Resource específica de microsserviço
namespace App\Http\Sistema\Usuarios\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->when($request->user()->can('usuarios.view-email', $this->resource), $this->email),
            'bio' => $this->when($this->bio, Str::limit(strip_tags($this->bio), 200)),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Sanitização</h4>
                <ul>
                    <li>Sempre valide e sanitize dados de entrada usando regras de validação do Laravel</li>
                    <li>Use <code>strip_tags()</code> ou <code>Str::of($value)->stripTags()</code> para remover HTML não
                        desejado</li>
                    <li>Use <code>e()</code> ou <code>@{{ }}</code> no Blade para escapar saída HTML</li>
                    <li>Para conteúdo rico, considere usar bibliotecas como HTML Purifier ou markdown seguro</li>
                    <li>Normalize dados antes de armazenar (e-mails em minúsculas, números de telefone apenas com
                        dígitos)</li>
                    <li>Valide URLs e e-mails com as regras específicas do Laravel</li>
                    <li>Utilize o novo sistema de tipagem estrita do Laravel 12 para garantir tipos de dados corretos
                    </li>
                </ul>
            </div>
        </section>

        <section id="dados-mascaramento" class="subsection">
            <h3>4.3. Mascaramento de Dados Sensíveis</h3>
            <p>O mascaramento de dados sensíveis é importante para logs, respostas de API e interfaces de usuário:</p>

            <div class="code-block">
                <h4>Mascaramento em Logs</h4>
                <pre>
// Em app/Logging/Processors/SensitiveDataMaskProcessor.php
namespace App\Logging\Processors;

use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;

class SensitiveDataMaskProcessor implements ProcessorInterface
{
    /**
     * Campos que devem ser mascarados nos logs
     */
    protected array $sensitiveFields = [
        'password',
        'password_confirmation',
        'credit_card',
        'card_number',
        'cvv',
        'ssn',
        'social_security',
        'access_token',
        'refresh_token',
        'api_key',
    ];

    /**
     * Processa o registro de log
     */
    public function __invoke(LogRecord $record): LogRecord
    {
        $record->context = $this->maskSensitiveData($record->context);
        
        if (isset($record->context['exception'])) {
            // Não mascarar objetos de exceção
            return $record;
        }

        return $record;
    }

    /**
     * Mascara dados sensíveis em um array
     */
    protected function maskSensitiveData(array $data): array
    {
        foreach ($data as $key => $value) {
            // Processar arrays aninhados recursivamente
            if (is_array($value)) {
                $data[$key] = $this->maskSensitiveData($value);
                continue;
            }

            // Verificar se o campo deve ser mascarado
            if (is_string($value) && $this->shouldMaskField($key)) {
                $data[$key] = $this->mask($value);
            }
        }

        return $data;
    }

    /**
     * Verifica se um campo deve ser mascarado
     */
    protected function shouldMaskField(string $fieldName): bool
    {
        $fieldName = strtolower($fieldName);
        
        foreach ($this->sensitiveFields as $sensitiveField) {
            if (str_contains($fieldName, $sensitiveField)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Mascara um valor sensível
     */
    protected function mask(string $value): string
    {
        if (strlen($value) <= 4) {
            return '****';
        }
        
        // Manter os primeiros 2 e últimos 2 caracteres
        return substr($value, 0, 2) . str_repeat('*', strlen($value) - 4) . substr($value, -2);
    }
}

// Registrando o processador em app/Providers/AppServiceProvider.php
use App\Logging\Processors\SensitiveDataMaskProcessor;
use Illuminate\Support\Facades\Log;

public function boot(): void
{
    // Adicionar processador a todos os canais de log
    foreach (array_keys(config('logging.channels')) as $channel) {
        Log::channel($channel)->pushProcessor(new SensitiveDataMaskProcessor());
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Mascaramento em Modelos</h4>
                <pre>
// Em app/Http/Sistema/Financeiro/Models/CreditCard.php
namespace App\Http\Sistema\Financeiro\Models;

use Illuminate\Database\Eloquent\Model;

class CreditCard extends Model
{
    protected $fillable = [
        'user_id',
        'card_number',
        'expiration_month',
        'expiration_year',
        'card_holder',
        'brand',
    ];

    /**
     * Atributos que devem ser ocultos em arrays.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'card_number',
        'cvv',
    ];

    /**
     * Atributos que devem ser convertidos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expiration_month' => 'integer',
        'expiration_year' => 'integer',
    ];

    /**
     * Obter número do cartão mascarado
     */
    public function getMaskedCardNumberAttribute(): string
    {
        $cardNumber = $this->attributes['card_number'];
        $lastFour = substr($cardNumber, -4);
        
        return str_repeat('*', strlen($cardNumber) - 4) . $lastFour;
    }

    /**
     * Acessor para exibição segura
     */
    public function toArray(): array
    {
        $array = parent::toArray();
        
        // Adicionar versão mascarada do número do cartão
        $array['masked_card_number'] = $this->masked_card_number;
        
        return $array;
    }
}</pre>
            </div>
        </section>

        <section id="dados-auditoria" class="subsection">
            <h3>4.4. Auditoria de Dados</h3>
            <p>A auditoria de dados é essencial para rastrear alterações em dados sensíveis e cumprir requisitos
                regulatórios:</p>

            <div class="code-block">
                <h4>Implementação de Auditoria</h4>
                <pre>
// Em app/Http/Sistema/Usuarios/Models/User.php
namespace App\Http\Sistema\Usuarios\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class User extends Authenticatable implements Auditable
{
    use AuditableTrait;

    /**
     * Atributos que devem ser auditados.
     *
     * @var array<int, string>
     */
    protected $auditInclude = [
        'name',
        'email',
        'role',
        'is_active',
        'last_login_at',
    ];

    /**
     * Atributos que não devem ser auditados.
     *
     * @var array<int, string>
     */
    protected $auditExclude = [
        'password',
        'remember_token',
    ];

    /**
     * Eventos que devem disparar auditorias.
     *
     * @var array<int, string>
     */
    protected $auditEvents = [
        'created',
        'updated',
        'deleted',
        'restored',
    ];

    /**
     * Personalizar os dados de auditoria
     */
    public function transformAudit(array $data): array
    {
        // Adicionar informações contextuais
        $data['request_ip'] = request()->ip();
        $data['request_user_agent'] = request()->userAgent();
        $data['request_url'] = request()->fullUrl();
        $data['request_method'] = request()->method();
        
        return $data;
    }
}

// Visualização de logs de auditoria em um controller
// Em app/Http/Sistema/Admin/Controllers/UserAuditController.php
namespace App\Http\Sistema\Admin\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Admin\Services\AuditService;
use App\Http\Sistema\Usuarios\Models\User;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class UserAuditController extends ControllerAbstract
{
    protected $response;

    public function __construct(AuditService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function index(User $user): View
    {
        $this->authorize('admin.view-audit-logs', $user);
        
        $audits = $user->audits()->with('user')->latest()->paginate(15);
        
        return view('admin.users.audit', compact('user', 'audits'));
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Auditoria</h4>
                <ul>
                    <li>Audite todas as alterações em dados sensíveis ou críticos</li>
                    <li>Registre quem fez a alteração, quando, o que foi alterado e de onde</li>
                    <li>Armazene logs de auditoria em um local seguro e separado</li>
                    <li>Implemente políticas de retenção adequadas para logs de auditoria</li>
                    <li>Considere requisitos regulatórios específicos do seu setor (GDPR, HIPAA, PCI DSS, etc.)</li>
                    <li>Forneça interfaces para visualização e busca de logs de auditoria para usuários autorizados</li>
                    <li>Configure alertas para alterações críticas ou suspeitas</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="csrf" class="manual-section">
        <h2>5. Proteção CSRF</h2>
        <p>A proteção contra Cross-Site Request Forgery (CSRF) é essencial para prevenir ataques onde sites maliciosos
            podem forçar usuários autenticados a executar ações não intencionais.</p>

        <section id="csrf-laravel" class="subsection">
            <h3>5.1. Proteção CSRF no Laravel</h3>
            <p>O Laravel 12 inclui proteção CSRF automática para todas as rotas que usam os verbos POST, PUT, PATCH ou
                DELETE:</p>

            <div class="code-block">
                <h4>Implementação em Formulários</h4>
                <pre>
&lt;!-- Em um formulário Blade -->
&lt;form method="POST" action="/profile">
    @csrf
    &lt;!-- Campos do formulário -->
    &lt;button type="submit">Atualizar Perfil&lt;/button>
&lt;/form>

// Em uma requisição AJAX com Axios
axios.post('/api/comment', {
    comment: this.comment
}, {
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    }
});</pre>
            </div>

            <div class="best-practice">
                <h4>Configuração do CSRF</h4>
                <p>A configuração do CSRF pode ser personalizada no arquivo
                    <code>app/Http/Middleware/VerifyCsrfToken.php</code>:
                </p>
                <pre>
namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * URIs que devem ser excluídos da verificação CSRF.
     * Use com extrema cautela e apenas quando absolutamente necessário.
     *
     * @var array&lt;int, string>
     */
    protected $except = [
        'webhook/*', // Webhooks de serviços externos
        'api/payment-callback', // Callbacks de pagamento
    ];
}</pre>
            </div>
        </section>

        <section id="csrf-spa" class="subsection">
            <h3>5.2. CSRF em Aplicações SPA</h3>
            <p>Para aplicações Single Page Application (SPA) que usam Laravel como backend, a proteção CSRF requer uma
                abordagem específica:</p>

            <div class="code-block">
                <h4>Configuração para SPA</h4>
                <pre>
// Em routes/api.php
Route::middleware('web')->get('/csrf-cookie', function () {
    return response()->json(['message' => 'CSRF cookie set']);
});

// No cliente JavaScript (exemplo com Vue.js e Axios)
// Primeiro, obtenha o cookie CSRF
axios.get('/csrf-cookie').then(() => {
    // Agora você pode fazer requisições POST, PUT, etc.
    axios.post('/api/profile', userData)
        .then(response => {
            // Manipular resposta
        });
});</pre>
            </div>

            <div class="note">
                <p>O Laravel Sanctum simplifica este processo para SPAs, gerenciando automaticamente os cookies CSRF
                    quando configurado corretamente.</p>
            </div>
        </section>

        <section id="csrf-best-practices" class="subsection">
            <h3>5.3. Melhores Práticas CSRF</h3>
            <ul>
                <li>Nunca desative a proteção CSRF globalmente</li>
                <li>Use a diretiva <code>@csrf</code> em todos os formulários</li>
                <li>Para APIs, use tokens de API (como Sanctum) em vez de desativar CSRF</li>
                <li>Implemente o SameSite=Lax (padrão no Laravel 12) para cookies</li>
                <li>Mantenha a lista de exceções CSRF o mais restrita possível</li>
                <li>Considere usar tokens CSRF de uso único para operações críticas</li>
            </ul>

            <div class="code-block">
                <h4>Token CSRF de Uso Único</h4>
                <pre>
// Em um controller
public function showDeleteAccountForm()
{
    $token = Str::random(60);
    
    // Armazenar token na sessão com tempo de expiração
    session()->put('delete_account_token', [
        'token' => $token,
        'expires_at' => now()->addMinutes(10)
    ]);
    
    return view('account.delete', ['token' => $token]);
}

public function deleteAccount(Request $request)
{
    $storedToken = session('delete_account_token');
    
    // Verificar se o token existe, não expirou e corresponde
    if (!$storedToken || 
        now()->isAfter($storedToken['expires_at']) || 
        $request->token !== $storedToken['token']) {
        
        return back()->withErrors(['token' => 'Token inválido ou expirado']);
    }
    
    // Remover token da sessão (uso único)
    session()->forget('delete_account_token');
    
    // Processar exclusão da conta
    // ...
}</pre>
            </div>
        </section>
    </section>

    <section id="xss" class="manual-section">
        <h2>6. Proteção XSS</h2>
        <p>Cross-Site Scripting (XSS) é uma vulnerabilidade que permite que atacantes injetem scripts maliciosos em
            páginas visualizadas por outros usuários.</p>

        <section id="xss-prevention" class="subsection">
            <h3>6.1. Prevenção de XSS no Laravel</h3>
            <p>O Laravel 12 fornece várias ferramentas para prevenir ataques XSS:</p>

            <div class="code-block">
                <h4>Escapamento Automático no Blade</h4>
                <pre>
&lt;!-- Escapamento automático -->
{{ $userInput }}

&lt;!-- Sem escapamento (use apenas para conteúdo confiável) -->
{!! $trustedHtml !!}

&lt;!-- Escapamento explícito -->
{{ e($userInput) }}</pre>
            </div>

            <div class="best-practice">
                <h4>Sanitização de Conteúdo Rico</h4>
                <p>Para conteúdo rico como HTML de editores WYSIWYG, use bibliotecas de sanitização:</p>
                <pre>
// Instalação do HTML Purifier
composer require mews/purifier

// Uso em um controller ou serviço
use Mews\Purifier\Facades\Purifier;

public function store(Request $request)
{
    $article = new Article();
    $article->title = $request->title;
    $article->content = Purifier::clean($request->content);
    $article->save();
    
    return redirect()->route('articles.show', $article);
}</pre>
            </div>
        </section>

        <section id="xss-headers" class="subsection">
            <h3>6.2. Cabeçalhos de Segurança para XSS</h3>
            <p>Implemente cabeçalhos HTTP de segurança para mitigar ataques XSS:</p>

            <div class="code-block">
                <h4>Content Security Policy (CSP)</h4>
                <pre>
// Em app/Http/Middleware/AddSecurityHeaders.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AddSecurityHeaders
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        // Content Security Policy
        $response->headers->set('Content-Security-Policy', 
            "default-src 'self'; " .
            "script-src 'self' https://cdn.jsdelivr.net; " .
            "style-src 'self' https://fonts.googleapis.com; " .
            "img-src 'self' data: https://secure.example.com; " .
            "font-src 'self' https://fonts.gstatic.com; " .
            "connect-src 'self'; " .
            "frame-src 'none'; " .
            "object-src 'none'; " .
            "base-uri 'self';"
        );
        
        // Outros cabeçalhos de segurança
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        
        return $response;
    }
}

// Registrar o middleware em app/Http/Kernel.php
protected $middleware = [
    // ...
    \App\Http\Middleware\AddSecurityHeaders::class,
];</pre>
            </div>
        </section>

        <section id="xss-api" class="subsection">
            <h3>6.3. Proteção XSS em APIs</h3>
            <p>Para APIs, é importante sanitizar dados de entrada e saída:</p>

            <div class="code-block">
                <h4>Sanitização em API Resources</h4>
                <pre>
namespace App\Http\Sistema\Blog\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class ArticleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            // Sanitizar conteúdo HTML para exibição segura
            'excerpt' => Str::limit(strip_tags($this->content), 150),
            // Conteúdo completo apenas para usuários autorizados
            'content' => $this->when(
                $request->user()->can('view', $this->resource),
                fn() => clean($this->content) // Função helper para sanitização
            ),
            'author' => new UserResource($this->author),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Prevenção de XSS</h4>
                <ul>
                    <li>Sempre escape dados de saída por padrão (use {{ }} no Blade)</li>
                    <li>Sanitize dados de entrada, especialmente conteúdo HTML</li>
                    <li>Implemente Content Security Policy (CSP)</li>
                    <li>Use bibliotecas de sanitização para conteúdo rico</li>
                    <li>Valide tipos de dados e formatos (emails, URLs, etc.)</li>
                    <li>Considere usar o atributo HttpOnly para cookies sensíveis</li>
                    <li>Implemente o cabeçalho X-XSS-Protection</li>
                    <li>Evite inserir dados do usuário diretamente em scripts JavaScript</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="sql-injection" class="manual-section">
        <h2>7. Proteção contra SQL Injection</h2>
        <p>SQL Injection é uma vulnerabilidade que permite que atacantes injetem comandos SQL maliciosos em consultas ao
            banco de dados.</p>

        <section id="sql-eloquent" class="subsection">
            <h3>7.1. Proteção com Eloquent ORM</h3>
            <p>O Eloquent ORM do Laravel fornece proteção automática contra SQL Injection:</p>

            <div class="code-block">
                <h4>Consultas Seguras com Eloquent</h4>
                <pre>
// Seguro - Parâmetros vinculados automaticamente
$users = User::where('status', $status)
    ->where('role', $role)
    ->get();

// Seguro - Parâmetros nomeados
$users = User::where('email', 'like', "%{$searchTerm}%") // Seguro no Eloquent
    ->orWhere('name', 'like', "%{$searchTerm}%")
    ->get();</pre>
            </div>

            <div class="code-block">
                <h4>Consultas Inseguras (Evitar)</h4>
                <pre>
// INSEGURO - Nunca faça isso!
$query = "SELECT * FROM users WHERE email = '{$email}'";
$users = DB::select(DB::raw($query));

// INSEGURO - Concatenação de strings em consultas
$users = DB::table('users')
    ->whereRaw("email = '{$email}'") // Vulnerável a SQL Injection
    ->get();</pre>
            </div>
        </section>

        <section id="sql-query-builder" class="subsection">
            <h3>7.2. Query Builder Seguro</h3>
            <p>Quando precisar usar o Query Builder, utilize parâmetros vinculados:</p>

            <div class="code-block">
                <h4>Parâmetros Vinculados</h4>
                <pre>
// Seguro - Parâmetros posicionais
$users = DB::select('SELECT * FROM users WHERE status = ? AND role = ?', [$status, $role]);

// Seguro - Parâmetros nomeados
$users = DB::select('SELECT * FROM users WHERE status = :status AND role = :role', [
    'status' => $status,
    'role' => $role
]);

// Seguro - Query Builder com where
$users = DB::table('users')
    ->where('status', $status)
    ->where('role', $role)
    ->get();</pre>
            </div>

            <div class="best-practice">
                <h4>Ordenação e Filtragem Segura</h4>
                <p>Para ordenação e filtragem dinâmica baseada em entrada do usuário:</p>
                <pre>
// Em um controller ou serviço
public function index(Request $request)
{
    $query = User::query();
    
    // Filtragem segura
    if ($request->has('status')) {
        $query->where('status', $request->status);
    }
    
    // Ordenação segura
    $allowedSortFields = ['name', 'email', 'created_at'];
    $sortField = in_array($request->sort_by, $allowedSortFields) 
        ? $request->sort_by 
        : 'created_at';
    
    $sortDirection = $request->sort_dir === 'asc' ? 'asc' : 'desc';
    
    $users = $query->orderBy($sortField, $sortDirection)->paginate(15);
    
    return UserResource::collection($users);
}</pre>
            </div>
        </section>

        <section id="sql-raw-queries" class="subsection">
            <h3>7.3. Consultas Raw Seguras</h3>
            <p>Quando consultas raw são necessárias, sempre use parâmetros vinculados:</p>

            <div class="code-block">
                <h4>Consultas Raw Seguras</h4>
                <pre>
// Consulta raw com parâmetros vinculados
$users = DB::select(
    'SELECT * FROM users WHERE status = ? AND created_at >= ?',
    [$status, $date]
);

// Expressões raw em consultas Eloquent
$users = User::select('id', 'name')
    ->selectRaw('YEAR(created_at) as year')
    ->whereRaw('LOWER(email) LIKE ?', ["%{$searchTerm}%"])
    ->get();</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Prevenção de SQL Injection</h4>
                <ul>
                    <li>Use Eloquent ORM ou Query Builder sempre que possível</li>
                    <li>Nunca concatene strings diretamente em consultas SQL</li>
                    <li>Use parâmetros vinculados para todas as entradas de usuário</li>
                    <li>Valide e sanitize entradas antes de usá-las em consultas</li>
                    <li>Implemente o princípio do privilégio mínimo para usuários do banco de dados</li>
                    <li>Use migrações para definir esquemas de banco de dados em vez de SQL direto</li>
                    <li>Considere usar Prepared Statements para consultas complexas ou repetitivas</li>
                    <li>Implemente validação de tipo para parâmetros de consulta</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="headers" class="manual-section">
        <h2>8. Cabeçalhos HTTP de Segurança</h2>
        <p>Os cabeçalhos HTTP de segurança são uma camada adicional de proteção contra vários tipos de ataques.</p>

        <section id="headers-implementation" class="subsection">
            <h3>8.1. Implementação de Cabeçalhos de Segurança</h3>
            <p>Implemente cabeçalhos de segurança através de um middleware:</p>

            <div class="code-block">
                <h4>Middleware de Cabeçalhos de Segurança</h4>
                <pre>
// Em app/Http/Middleware/SecurityHeaders.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeaders
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        if (!$response instanceof Response) {
            return $response;
        }
        
        // Content Security Policy
        $response->headers->set('Content-Security-Policy', 
            "default-src 'self'; " .
            "script-src 'self' https://cdn.jsdelivr.net; " .
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " .
                       "img-src 'self' data: https://secure.example.com; " .
            "font-src 'self' https://fonts.gstatic.com; " .
            "connect-src 'self'; " .
            "frame-src 'none'; " .
            "object-src 'none'; " .
            "base-uri 'self';"
        );
        
        // Outros cabeçalhos de segurança
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
        
        // Strict-Transport-Security (HSTS)
        if (app()->environment('production')) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        }
        
        return $response;
    }
}</pre>
            </div>
        </section>

        <section id="headers-types" class="subsection">
            <h3>8.2. Tipos de Cabeçalhos de Segurança</h3>
            <p>Explicação dos principais cabeçalhos de segurança e sua função:</p>

            <table>
                <thead>
                    <tr>
                        <th>Cabeçalho</th>
                        <th>Descrição</th>
                        <th>Exemplo</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Content-Security-Policy (CSP)</td>
                        <td>Controla quais recursos o navegador pode carregar</td>
                        <td><code>default-src 'self';</code></td>
                    </tr>
                    <tr>
                        <td>X-XSS-Protection</td>
                        <td>Ativa filtros XSS no navegador</td>
                        <td><code>1; mode=block</code></td>
                    </tr>
                    <tr>
                        <td>X-Content-Type-Options</td>
                        <td>Previne MIME-type sniffing</td>
                        <td><code>nosniff</code></td>
                    </tr>
                    <tr>
                        <td>Strict-Transport-Security</td>
                        <td>Força conexões HTTPS</td>
                        <td><code>max-age=31536000; includeSubDomains</code></td>
                    </tr>
                    <tr>
                        <td>Referrer-Policy</td>
                        <td>Controla informações de referência enviadas</td>
                        <td><code>strict-origin-when-cross-origin</code></td>
                    </tr>
                    <tr>
                        <td>Permissions-Policy</td>
                        <td>Controla quais recursos o navegador pode usar</td>
                        <td><code>camera=(), microphone=()</code></td>
                    </tr>
                    <tr>
                        <td>X-Frame-Options</td>
                        <td>Previne clickjacking</td>
                        <td><code>DENY</code> ou <code>SAMEORIGIN</code></td>
                    </tr>
                </tbody>
            </table>
        </section>

        <section id="headers-csp" class="subsection">
            <h3>8.3. Configuração Avançada de CSP</h3>
            <p>O Content Security Policy (CSP) é um dos cabeçalhos mais importantes e complexos:</p>

            <div class="code-block">
                <h4>Configuração CSP Detalhada</h4>
                <pre>
// Em app/Http/Middleware/ContentSecurityPolicy.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ContentSecurityPolicy
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        if (!$response instanceof Response) {
            return $response;
        }
        
        $cspDirectives = [
            // Fontes padrão - restringir a 'self' (mesmo domínio)
            "default-src 'self'",
            
            // Scripts - permitir apenas do mesmo domínio e CDNs confiáveis
            "script-src 'self' https://cdn.jsdelivr.net https://www.google-analytics.com 'nonce-" . $this->generateNonce() . "'",
            
            // Estilos - permitir mesmo domínio, Google Fonts e inline (com nonce)
            "style-src 'self' https://fonts.googleapis.com 'nonce-" . $this->generateNonce() . "'",
            
            // Imagens - permitir mesmo domínio, data URIs e domínios confiáveis
            "img-src 'self' data: https://secure.example.com; " .
            "font-src 'self' https://fonts.gstatic.com; " .
            "connect-src 'self'; " .
            "frame-src 'none'; " .
            "object-src 'none'; " .
            "base-uri 'self';"
        );
        
        // Gerar um nonce único para cada requisição
        protected function generateNonce(): string
        {
            return bin2hex(random_bytes(16));
        }
        
        // Configurar CSP para ambiente de desenvolvimento vs produção
        protected function getCspDirectives(): string
        {
            if (app()->environment('production')) {
                return $this->getProductionCsp();
            }
            
            return $this->getDevelopmentCsp();
        }
        
        protected function getProductionCsp(): string
        {
            // CSP mais restritivo para produção
            return implode('; ', $cspDirectives);
        }
        
        protected function getDevelopmentCsp(): string
        {
            // CSP mais permissivo para desenvolvimento
            $devDirectives = $cspDirectives;
            $devDirectives[] = "script-src 'self' 'unsafe-eval'"; // Permitir eval para ferramentas de desenvolvimento
            
            return implode('; ', $devDirectives);
        }
    }
}</pre>
            </div>
        </section>

        <section id="headers-cookies" class="subsection">
            <h3>8.4. Configuração Segura de Cookies</h3>
            <p>A configuração adequada de cookies é essencial para proteger sessões e dados do usuário:</p>

            <div class="code-block">
                <h4>Configuração de Cookies</h4>
                <pre>
// Em config/session.php
return [
    // ...
    'secure' => env('SESSION_SECURE_COOKIE', true),
    'http_only' => true,
    'same_site' => 'lax',
    // ...
];

// Em app/Http/Middleware/EncryptCookies.php
namespace App\Http\Middleware;

use Illuminate\Cookie\Middleware\EncryptCookies as Middleware;

class EncryptCookies extends Middleware
{
    /**
     * Cookies que não devem ser criptografados.
     * Mantenha esta lista o menor possível.
     *
     * @var array<int, string>
     */
    protected $except = [
        // 'cookie_name',
    ];
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Cookies</h4>
                <ul>
                    <li><strong>Secure:</strong> Garante que o cookie só seja enviado por HTTPS</li>
                    <li><strong>HttpOnly:</strong> Impede acesso ao cookie via JavaScript</li>
                    <li><strong>SameSite:</strong> Controla quando os cookies são enviados em requisições cross-site
                    </li>
                    <li><strong>Criptografia:</strong> Criptografe todos os cookies que contêm dados sensíveis</li>
                    <li><strong>Expiração:</strong> Configure tempos de expiração apropriados para cookies de sessão
                    </li>
                </ul>
            </div>
        </section>
    </section>

    <section id="rate-limiting" class="manual-section">
        <h2>9. Rate Limiting</h2>
        <p>O rate limiting é uma técnica essencial para proteger sua aplicação contra abusos, ataques de força bruta e
            DoS (Denial of Service).</p>

        <section id="rate-global" class="subsection">
            <h3>9.1. Rate Limiting Global</h3>
            <p>O Laravel 12 fornece um sistema robusto de rate limiting que pode ser configurado globalmente:</p>

            <div class="code-block">
                <h4>Configuração Global</h4>
                <pre>
// Em app/Providers/RouteServiceProvider.php
namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    // ...

    protected function configureRateLimiting(): void
    {
        // Rate limit global para API
        RateLimiter::for('api', function (Request $request) {
            return $request->user()
                ? Limit::perMinute(60)->by($request->user()->id)
                : Limit::perMinute(20)->by($request->ip());
        });
        
        // Rate limit para tentativas de login
        RateLimiter::for('login', function (Request $request) {
            return Limit::perMinute(5)->by($request->input('email') . $request->ip());
        });
        
        // Rate limit para registro de usuários
        RateLimiter::for('register', function (Request $request) {
            return Limit::perHour(3)->by($request->ip());
        });
        
        // Rate limit para recuperação de senha
        RateLimiter::for('password-reset', function (Request $request) {
            return Limit::perDay(5)->by($request->input('email'));
        });
    }
}</pre>
            </div>

            <div class="code-block">
                <h4>Aplicando Rate Limiting</h4>
                <pre>
// Em routes/api.php
Route::middleware(['throttle:api'])->group(function () {
    // Rotas da API
});

// Em routes/web.php
Route::post('/login', [AuthController::class, 'login'])
    ->middleware(['throttle:login']);
    
Route::post('/register', [AuthController::class, 'register'])
    ->middleware(['throttle:register']);
    
Route::post('/forgot-password', [PasswordResetController::class, 'sendResetLinkEmail'])
    ->middleware(['throttle:password-reset']);</pre>
            </div>
        </section>

        <section id="rate-dynamic" class="subsection">
            <h3>9.2. Rate Limiting Dinâmico</h3>
            <p>O Laravel 12 permite implementar rate limiting dinâmico baseado em vários fatores:</p>

            <div class="code-block">
                <h4>Rate Limiting Baseado em Usuário</h4>
                <pre>
// Em app/Providers/RouteServiceProvider.php
RateLimiter::for('api-user-tier', function (Request $request) {
    $user = $request->user();
    
    if (!$user) {
        return Limit::perMinute(20)->by($request->ip());
    }
    
    // Limites diferentes baseados no plano do usuário
    return match ($user->subscription_tier) {
        'premium' => Limit::perMinute(120)->by($user->id),
        'standard' => Limit::perMinute(60)->by($user->id),
        default => Limit::perMinute(30)->by($user->id),
    };
});</pre>
            </div>

            <div class="code-block">
                <h4>Rate Limiting por Endpoint</h4>
                <pre>
// Em app/Http/Middleware/RateLimitSensitiveEndpoints.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class RateLimitSensitiveEndpoints
{
    /**
     * Configurações de rate limit para endpoints sensíveis
     */
    protected array $endpointLimits = [
        'api/v1/payments' => ['attempts' => 10, 'decay' => 60], // 10 por minuto
        'api/v1/users' => ['attempts' => 30, 'decay' => 60],    // 30 por minuto
        'api/v1/reports' => ['attempts' => 5, 'decay' => 60],   // 5 por minuto
    ];

    public function handle(Request $request, Closure $next): Response
    {
        $path = $request->path();
        
        // Verificar se o endpoint atual está na lista de endpoints sensíveis
        foreach ($this->endpointLimits as $endpoint => $limits) {
            if (str_starts_with($path, $endpoint)) {
                $key = "rate_limit_{$endpoint}_" . ($request->user()?->id ?? $request->ip());
                
                if (RateLimiter::tooManyAttempts($key, $limits['attempts'])) {
                    $seconds = RateLimiter::availableIn($key);
                    
                    return response()->json([
                        'status' => 'error',
                        'message' => "Too many requests. Please try again in {$seconds} seconds.",
                        'code' => 'RATE_LIMITED'
                    ], 429);
                }
                
                RateLimiter::hit($key, $limits['decay']);
                break;
            }
        }
        
        return $next($request);
    }
}</pre>
            </div>
        </section>

        <section id="rate-response" class="subsection">
            <h3>9.3. Respostas Personalizadas para Rate Limiting</h3>
            <p>Personalize as respostas quando um usuário excede o limite de requisições:</p>

            <div class="code-block">
                <h4>Respostas Personalizadas</h4>
                <pre>
// Em app/Exceptions/Handler.php
namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Throwable;

class Handler extends ExceptionHandler
{
    // ...

    public function render($request, Throwable $exception)
    {
        if ($exception instanceof ThrottleRequestsException) {
            $retryAfter = $exception->getHeaders()['Retry-After'] ?? null;
            $message = $retryAfter 
                ? "Muitas requisições. Por favor, tente novamente em {$retryAfter} segundos." 
                : "Muitas requisições. Por favor, tente novamente mais tarde.";
            
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => $message,
                    'code' => 'RATE_LIMITED'
                ], 429);
            }
            
            return back()->with('error', $message);
        }
        
        return parent::render($request, $exception);
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Rate Limiting</h4>
                <ul>
                    <li>Aplique limites mais restritivos para endpoints sensíveis (login, registro, pagamentos)</li>
                    <li>Use identificadores únicos (IP + user agent ou token) para evitar bypass de limites</li>
                    <li>Implemente limites diferentes para usuários autenticados vs. não autenticados</li>
                    <li>Monitore e ajuste os limites com base no tráfego real e padrões de uso</li>
                    <li>Forneça cabeçalhos informativos (X-RateLimit-*) para clientes da API</li>
                    <li>Considere implementar backoff exponencial para tentativas repetidas</li>
                    <li>Registre e monitore violações de rate limit para detectar possíveis ataques</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="seguranca-api" class="manual-section">
        <h2>10. Segurança em APIs</h2>
        <p>As APIs requerem considerações especiais de segurança, especialmente em uma arquitetura de microsserviços.
        </p>

        <section id="api-auth" class="subsection">
            <h3>10.1. Autenticação de API</h3>
            <p>O Laravel 12 oferece várias opções para autenticação de API:</p>

            <div class="code-block">
                <h4>Autenticação com Sanctum</h4>
                <pre>
// Em app/Http/Sistema/Autenticacao/Controllers/ApiTokenController.php
namespace App\Http\Sistema\Autenticacao\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Autenticacao\Services\ApiTokenService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class ApiTokenController extends ControllerAbstract
{
    protected $response;

    public function __construct(ApiTokenService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function createToken(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'device_name' => 'required|string|max:255',
            'abilities' => 'sometimes|array',
        ]);

        $user = $this->service->findUserByEmail($request->email);

        if (!$user || !Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['As credenciais fornecidas estão incorretas.'],
            ]);
        }

        // Determinar habilidades com base nas permissões do usuário
        $abilities = $request->abilities ?? $this->service->getDefaultAbilitiesForUser($user);

        // Criar token com expiração
        $token = $user->createToken(
            $request->device_name,
            $abilities,
            now()->addDays(30) // Token expira em 30 dias
        );

        return $this->response->success([
            'token' => $token->plainTextToken,
            'abilities' => $abilities,
            'expires_at' => now()->addDays(30)->toIso8601String()
        ]);
    }

    public function revokeToken(Request $request): JsonResponse
    {
        // Revogar token atual
        $request->user()->currentAccessToken()->delete();

        return $this->response->success(null, 'Token revogado com sucesso');
    }

    public function revokeAllTokens(Request $request): JsonResponse
    {
        // Revogar todos os tokens do usuário
        $request->user()->tokens()->delete();

        return $this->response->success(null, 'Todos os tokens foram revogados');
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Autenticação de API</h4>
                <ul>
                    <li>Use HTTPS para todas as comunicações de API</li>
                    <li>Implemente expiração de tokens</li>
                    <li>Use escopos/habilidades para limitar o acesso de tokens</li>
                    <li>Permita que os usuários revoguem seus próprios tokens</li>
                    <li>Implemente rotação de tokens para sessões de longa duração</li>
                    <li>Registre todas as operações de criação e revogação de tokens</li>
                    <li>Considere usar JWT para cenários específicos de microsserviços</li>
                </ul>
            </div>
        </section>

        <section id="api-versioning" class="subsection">
            <h3>10.2. Versionamento de API</h3>
            <p>O versionamento adequado de APIs é importante para manter a compatibilidade e segurança:</p>

            <div class="code-block">
                <h4>Implementação de Versionamento</h4>
                <pre>
// Em routes/api.php
Route::prefix('v1')->group(function () {
    // Rotas da API v1
    Route::post('/login', [\App\Http\Sistema\Autenticacao\Controllers\ApiAuthController::class, 'login']);
    
    Route::middleware('auth:sanctum')->group(function () {
        // Rotas protegidas v1
    });
});

Route::prefix('v2')->group(function () {
    // Rotas da API v2 com melhorias de segurança
    Route::post('/login', [\App\Http\Sistema\Autenticacao\Controllers\ApiAuthControllerV2::class, 'login']);
    
    Route::middleware(['auth:sanctum', 'ability:api-v2'])->group(function () {
        // Rotas protegidas v2
    });
});</pre>
            </div>

            <div class="code-block">
                <h4>Controle de Versão em Controllers</h4>
                <pre>
// Em app/Http/Sistema/Produtos/Controllers/V1/ProdutoController.php
namespace App\Http\Sistema\Produtos\Controllers\V1;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Produtos\Services\ProdutoService;
use App\Http\Sistema\Produtos\Resources\V1\ProdutoResource;
use App\Responses\ApiResponse;

class ProdutoController extends ControllerAbstract
{
    // Implementação da API v1
}

// Em app/Http/Sistema/Produtos/Controllers/V2/ProdutoController.php
namespace App\Http\Sistema\Produtos\Controllers\V2;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Produtos\Services\ProdutoService;
use App\Http\Sistema\Produtos\Resources\V2\ProdutoResource;
use App\Responses\ApiResponse;

class ProdutoController extends ControllerAbstract
{
    // Implementação da API v2 com melhorias de segurança
}</pre>
            </div>
        </section>

        <section id="api-cors" class="subsection">
            <h3>10.3. Configuração de CORS</h3>
            <p>O Cross-Origin Resource Sharing (CORS) deve ser configurado adequadamente para APIs:</p>

            <div class="code-block">
                <h4>Configuração de CORS</h4>
                <pre>
// Em config/cors.php
return [
    'paths' => ['api/*', 'sanctum/csrf-cookie'],
    'allowed_methods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    'allowed_origins' => [env('FRONTEND_URL', 'https://example.com')],
    'allowed_origins_patterns' => [],
    'allowed_headers' => ['Content-Type', 'X-Requested-With', 'Authorization'],
    'exposed_headers' => ['X-RateLimit-Limit', 'X-RateLimit-Remaining'],
    'max_age' => 60 * 60 * 24, // 24 horas
    'supports_credentials' => true,
];</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para CORS</h4>
                <ul>
                    <li>Limite as origens permitidas apenas aos domínios necessários</li>
                    <li>Evite usar <code>'*'</code> para allowed_origins em produção</li>
                    <li>Limite os métodos HTTP permitidos aos realmente necessários</li>
                    <li>Configure supports_credentials corretamente se usar cookies ou autenticação HTTP</li>
                    <li>Considere usar padrões de origem para ambientes de desenvolvimento e staging</li>
                </ul>
            </div>
        </section>

        <section id="api-responses" class="subsection">
            <h3>10.4. Respostas de API Seguras</h3>
            <p>Padronize as respostas de API para garantir segurança e consistência:</p>

            <div class="code-block">
                <h4>Classe de Resposta Padronizada</h4>
                <pre>
// Em app/Responses/ApiResponse.php
namespace App\Responses;

use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ApiResponse
{
    protected string $apiVersion = '1.0';

    public function success($data = null, string $message = 'Operação realizada com sucesso', array $headers = []): JsonResponse
    {
        return $this->buildResponse(true, $message, $data, Response::HTTP_OK, $headers);
    }

    public function created($data = null, string $message = 'Recurso criado com sucesso', array $headers = []): JsonResponse
    {
        return $this->buildResponse(true, $message, $data, Response::HTTP_CREATED, $headers);
    }

    public function error(string $message = 'Erro ao processar requisição', int $statusCode = Response::HTTP_BAD_REQUEST, $errors = null, array $headers = []): JsonResponse
    {
        return $this->buildResponse(false, $message, null, $statusCode, $headers, $errors);
    }

    public function validationError($errors, string $message = 'Dados de entrada inválidos', array $headers = []): JsonResponse
    {
        return $this->buildResponse(false, $message, null, Response::HTTP_UNPROCESSABLE_ENTITY, $headers, $errors);
    }

    public function authenticationError(string $message = 'Não autenticado', array $headers = []): JsonResponse
    {
        return $this->error($message, Response::HTTP_UNAUTHORIZED, null, $headers);
    }

    public function authorizationError(string $message = 'Não autorizado', array $headers = []): JsonResponse
    {
        return $this->error($message, Response::HTTP_FORBIDDEN, null, $headers);
    }

    public function notFound(string $message = 'Recurso não encontrado', array $headers = []): JsonResponse
    {
        return $this->error($message, Response::HTTP_NOT_FOUND, null, $headers);
    }

    public function serverError(string $message = 'Erro interno do servidor', array $headers = []): JsonResponse
    {
        return $this->error($message, Response::HTTP_INTERNAL_SERVER_ERROR, null, $headers);
    }

    protected function buildResponse(bool $success, string $message, $data = null, int $statusCode = 200, array $headers = [], $errors = null): JsonResponse
    {
        $response = [
            'success' => $success,
            'message' => $message,
            'api_version' => $this->apiVersion,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        // Adicionar timestamp para auditoria
        $response['timestamp'] = now()->toIso8601String();

        return response()->json($response, $statusCode, $headers);
    }
}</pre>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Respostas de API</h4>
                <ul>
                    <li>Use códigos de status HTTP apropriados</li>
                    <li>Forneça mensagens de erro claras, mas sem expor detalhes técnicos</li>
                    <li>Inclua códigos de erro específicos para facilitar o tratamento pelo cliente</li>
                    <li>Padronize o formato de resposta em toda a API</li>
                    <li>Inclua informações de paginação quando aplicável</li>
                    <li>Considere incluir um timestamp para fins de auditoria</li>
                    <li>Não exponha informações sensíveis ou detalhes de implementação</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="dependencias" class="manual-section">
        <h2>11. Segurança de Dependências</h2>
        <p>Manter as dependências seguras é crucial para a segurança geral da aplicação.</p>

        <section id="dependencias-atualizacao" class="subsection">
            <h3>11.1. Atualização de Dependências</h3>
            <p>Mantenha suas dependências atualizadas para evitar vulnerabilidades conhecidas:</p>

            <div class="code-block">
                <h4>Verificação e Atualização</h4>
                <pre>
# Verificar atualizações disponíveis
composer outdated

# Atualizar dependências
composer update

# Atualizar uma dependência específica
composer update vendor/package

# Verificar vulnerabilidades conhecidas
composer audit</pre>
            </div>

            <div class="best-practice">
                <h4>Estratégia de Atualização</h4>
                <ul>
                    <li>Estabeleça uma rotina regular para verificar e aplicar atualizações</li>
                    <li>Priorize atualizações de segurança</li>
                    <li>Teste completamente após atualizações importantes</li>
                    <li>Mantenha um registro de dependências e suas versões</li>
                    <li>Configure alertas para vulnerabilidades em dependências</li>
                </ul>
            </div>
        </section>

        <section id="dependencias-analise" class="subsection">
            <h3>11.2. Análise de Vulnerabilidades</h3>
            <p>Utilize ferramentas para analisar vulnerabilidades em suas dependências:</p>

            <div class="code-block">
                <h4>Ferramentas de Análise</h4>
                <pre>
# Usando o Composer Audit (Laravel 12)
composer audit

# Usando o Enlightn
composer require --dev enlightn/enlightn
php artisan enlightn

# Configurando GitHub Dependabot
# Em .github/dependabot.yml
version: 2
updates:
  - package-ecosystem: "composer"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    labels:
      - "dependencies"
      - "security"</pre>
            </div>

            <div class="code-block">
                <h4>Integração com CI/CD</h4>
                <pre>
# Em .github/workflows/security-checks.yml
name: Security Checks

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 0 * * 0'  # Executa todo domingo à meia-noite

jobs:
  security-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          
      - name: Install Dependencies
        run: composer install --prefer-dist --no-progress
        
      - name: Security Check
        run: composer audit
        
      - name: Static Analysis
        run: ./vendor/bin/phpstan analyse app --level=5</pre>
            </div>
        </section>

        <section id="dependencias-politicas" class="subsection">
            <h3>11.3. Políticas de Dependências</h3>
            <p>Estabeleça políticas claras para gerenciar dependências:</p>

            <div class="best-practice">
                <h4>Políticas Recomendadas</h4>
                <ul>
                    <li><strong>Avaliação de Novas Dependências:</strong> Avalie a segurança, manutenção e comunidade
                        antes de adicionar</li>
                    <li><strong>Versionamento:</strong> Use versionamento semântico com restrições específicas no
                        composer.json</li>
                    <li><strong>Dependências Abandonadas:</strong> Evite ou substitua dependências sem manutenção</li>
                    <li><strong>Auditoria Regular:</strong> Estabeleça um cronograma para auditar dependências</li>
                    <li><strong>Documentação:</strong> Mantenha um registro de por que cada dependência é necessária
                    </li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo de composer.json com Restrições de Versão</h4>
                <pre>
{
    "require": {
        "php": "^8.2",
        "laravel/framework": "^12.0",
        "laravel/sanctum": "^4.0",
        "spatie/laravel-permission": "^6.0",
        "guzzlehttp/guzzle": "^7.5"
    },
    "require-dev": {
        "fakerphp/faker": "^1.9.1",
        "laravel/pint": "^1.0",
        "laravel/sail": "^1.18",
        "mockery/mockery": "^1.4.4",
        "nunomaduro/collision": "^8.1",
        "phpunit/phpunit": "^10.1",
        "enlightn/enlightn": "^2.0"
    },
    "config": {
        "allow-plugins": {
            "pestphp/pest-plugin": true
        },
        "optimize-autoloader": true,
        "preferred-install": "dist",
        "sort-packages": true
    },
    "minimum-stability": "stable",
    "prefer-stable": true
}</pre>
            </div>
        </section>

        <section id="dependencias-alternativas" class="subsection">
            <h3>11.4. Alternativas Seguras</h3>
            <p>Algumas dependências comuns e suas alternativas mais seguras:</p>

            <table>
                <thead>
                    <tr>
                        <th>Tipo</th>
                        <th>Dependência Comum</th>
                        <th>Alternativa Segura</th>
                        <th>Razão</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Sanitização HTML</td>
                        <td>strip_tags()</td>
                        <td>HTML Purifier</td>
                        <td>Sanitização mais robusta contra XSS</td>
                    </tr>
                    <tr>
                        <td>Manipulação de Imagens</td>
                        <td>Intervention Image</td>
                        <td>Intervention Image + validação</td>
                        <td>Validação adicional contra uploads maliciosos</td>
                    </tr>
                    <tr>
                        <td>Markdown</td>
                        <td>Parsedown</td>
                        <td>League CommonMark</td>
                        <td>Melhor segurança contra XSS</td>
                    </tr>
                    <tr>
                        <td>HTTP Client</td>
                        <td>file_get_contents()</td>
                        <td>Guzzle HTTP</td>
                        <td>Melhor tratamento de erros e segurança</td>
                    </tr>
                    <tr>
                        <td>Validação de Formulários</td>
                        <td>Validação manual</td>
                        <td>Laravel Form Requests</td>
                        <td>Validação mais robusta e centralizada</td>
                    </tr>
                    <tr>
                        <td>Autenticação</td>
                        <td>Implementação própria</td>
                        <td>Laravel Sanctum/Fortify</td>
                        <td>Implementações testadas e seguras</td>
                    </tr>
                    <tr>
                        <td>Gerenciamento de Permissões</td>
                        <td>Arrays e verificações manuais</td>
                        <td>spatie/laravel-permission</td>
                        <td>Sistema robusto e testado para RBAC</td>
                    </tr>
                    <tr>
                        <td>Logging</td>
                        <td>Log::info() direto</td>
                        <td>Monolog com processadores</td>
                        <td>Mascaramento automático de dados sensíveis</td>
                    </tr>
                </tbody>
            </table>

            <div class="best-practice">
                <h4>Avaliação de Dependências</h4>
                <p>Antes de adicionar uma nova dependência ao projeto, avalie-a considerando os seguintes critérios:</p>
                <ul>
                    <li>Histórico de segurança e vulnerabilidades anteriores</li>
                    <li>Frequência de atualizações e manutenção ativa</li>
                    <li>Tamanho e atividade da comunidade</li>
                    <li>Qualidade da documentação e exemplos</li>
                    <li>Compatibilidade com as versões do PHP e Laravel utilizadas</li>
                    <li>Dependências transitivas (dependências das dependências)</li>
                </ul>
            </div>
        </section>


        <section id="seguranca-modular" class="manual-section">
            <h2>12. Segurança em Arquitetura Modular</h2>
            <p>Nossa arquitetura modular baseada em sistemas e microsserviços requer considerações
                específicas de segurança
                para garantir que cada componente seja seguro e que a comunicação entre eles seja
                protegida.</p>

            <section id="seguranca-microsservicos" class="subsection">
                <h3>12.1. Segurança em Microsserviços</h3>
                <p>Cada microsserviço deve implementar suas próprias medidas de segurança, seguindo os
                    princípios de defesa
                    em profundidade:</p>

                <div class="best-practice">
                    <h4>Princípios de Segurança para Microsserviços</h4>
                    <ul>
                        <li><strong>Isolamento de Responsabilidades:</strong> Cada microsserviço deve
                            ter acesso apenas aos
                            recursos necessários para sua função</li>
                        <li><strong>Validação em Camadas:</strong> Validar dados em cada camada, mesmo
                            quando já validados
                            por outro microsserviço</li>
                        <li><strong>Autenticação e Autorização Consistentes:</strong> Manter um modelo
                            de segurança
                            consistente entre todos os microsserviços</li>
                        <li><strong>Comunicação Segura:</strong> Garantir que a comunicação entre
                            microsserviços seja
                            autenticada e protegida</li>
                        <li><strong>Logging Centralizado:</strong> Implementar logging consistente em
                            todos os
                            microsserviços para facilitar a detecção de problemas</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Implementação de Segurança em Microsserviços</h4>
                    <pre>
// Em app/Http/Sistema/Financeiro/Controllers/TransacaoController.php
namespace App\Http\Sistema\Financeiro\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Financeiro\Services\TransacaoService;
use App\Http\Sistema\Financeiro\Requests\TransacaoRequest;
use App\Responses\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class TransacaoController extends ControllerAbstract
{
    protected $response;

    public function __construct(TransacaoService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function store(TransacaoRequest $request): JsonResponse
    {
        try {
            // Autorização específica do microsserviço
            $this->authorize('financeiro.create-transacao');
            
            // Validação já realizada pelo FormRequest
            $validatedData = $request->validated();
            
            // Processamento seguro
            $transacao = $this->service->createTransacao($validatedData);
            
            // Logging de evento sensível
            Log::info('Transação criada', [
                'user_id' => auth()->id(),
                'transacao_id' => $transacao->id,
                'valor' => $transacao->valor,
                // Não logar dados sensíveis como números de cartão
            ]);
            
            return $this->response->success($transacao, 'Transação criada com sucesso');
        } catch (\Exception $e) {
            Log::error('Erro ao criar transação', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            
            return $this->response->error('Não foi possível processar a transação');
        }
    }
}</pre>
                </div>
            </section>

            <section id="comunicacao-segura" class="subsection">
                <h3>12.2. Comunicação Segura entre Microsserviços</h3>
                <p>A comunicação entre microsserviços deve ser segura e controlada:</p>

                <div class="code-block">
                    <h4>Exemplo de Comunicação Segura</h4>
                    <pre>
// Em app/Http/Sistema/Pedidos/Services/PedidoService.php
namespace App\Http\Sistema\Pedidos\Services;

use App\Services\ServiceAbstract;
use App\Http\Sistema\Pedidos\Repositories\PedidoRepository;
use App\Http\Sistema\Produtos\Services\ProdutoService;
use App\Http\Sistema\Financeiro\Services\PagamentoService;
use App\Exceptions\BusinessException;
use Illuminate\Support\Facades\Log;

class PedidoService extends ServiceAbstract
{
    protected $produtoService;
    protected $pagamentoService;

    public function __construct(
        PedidoRepository $repository,
        ProdutoService $produtoService,
        PagamentoService $pagamentoService
    ) {
        parent::__construct($repository);
        $this->produtoService = $produtoService;
        $this->pagamentoService = $pagamentoService;
    }

    public function criarPedidoComPagamento(array $data)
    {
        // Iniciar transação para garantir consistência
        return \DB::transaction(function () use ($data) {
            try {
                // Verificar disponibilidade do produto
                $produto = $this->produtoService->findById($data['produto_id']);
                
                if (!$produto || $produto->estoque < $data['quantidade']) {
                    throw new BusinessException('Produto indisponível ou estoque insuficiente');
                }
                
                // Criar pedido
                $pedido = $this->repository->create([
                    'cliente_id' => $data['cliente_id'],
                    'produto_id' => $produto->id,
                    'quantidade' => $data['quantidade'],
                    'valor_total' => $produto->preco * $data['quantidade']
                ]);
                
                // Processar pagamento
                $pagamento = $this->pagamentoService->processarPagamento([
                    'pedido_id' => $pedido->id,
                    'valor' => $pedido->valor_total,
                    'metodo' => $data['metodo_pagamento'],
                    'dados_pagamento' => $data['dados_pagamento']
                ]);
                
                // Atualizar estoque
                $this->produtoService->atualizarEstoque($produto->id, -$data['quantidade']);
                
                // Registrar operação bem-sucedida
                Log::info('Pedido criado com pagamento', [
                    'pedido_id' => $pedido->id,
                    'cliente_id' => $data['cliente_id'],
                    'valor_total' => $pedido->valor_total
                ]);
                
                return [
                    'pedido' => $pedido,
                    'pagamento' => $pagamento
                ];
            } catch (\Exception $e) {
                // Registrar falha
                Log::error('Falha ao criar pedido com pagamento', [
                    'error' => $e->getMessage(),
                    'cliente_id' => $data['cliente_id'] ?? null,
                    'produto_id' => $data['produto_id'] ?? null
                ]);
                
                throw $e;
            }
        });
    }
}</pre>
                </div>

                <div class="best-practice">
                    <h4>Melhores Práticas para Comunicação entre Microsserviços</h4>
                    <ul>
                        <li>Use transações de banco de dados quando necessário para manter consistência
                        </li>
                        <li>Implemente mecanismos de retry para lidar com falhas temporárias</li>
                        <li>Valide dados em cada ponto de entrada, mesmo quando já validados por outro
                            microsserviço</li>
                        <li>Registre eventos importantes para auditoria e depuração</li>
                        <li>Implemente circuit breakers para evitar falhas em cascata</li>
                        <li>Use eventos para comunicação assíncrona quando apropriado</li>
                        <li>Mantenha contratos de API claros e versionados entre microsserviços</li>
                    </ul>
                </div>
            </section>

            <section id="permissoes-microsservicos" class="subsection">
                <h3>12.3. Gerenciamento de Permissões em Microsserviços</h3>
                <p>O gerenciamento de permissões em uma arquitetura modular requer uma abordagem
                    consistente:</p>

                <div class="code-block">
                    <h4>Implementação de Permissões</h4>
                    <pre>
// Em app/Http/Sistema/Autenticacao/Services/PermissaoService.php
namespace App\Http\Sistema\Autenticacao\Services;

use App\Services\ServiceAbstract;
use App\Http\Sistema\Autenticacao\Repositories\PermissaoRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class PermissaoService extends ServiceAbstract
{
    public function __construct(PermissaoRepository $repository)
    {
        parent::__construct($repository);
    }

    /**
     * Obtém todas as permissões de um usuário, agrupadas por microsserviço
     */
    public function getPermissoesPorMicrosservico(int $userId): Collection
    {
        return Cache::remember("user_permissions_{$userId}", 3600, function () use ($userId) {
            return $this->repository->getPermissoesPorMicrosservico($userId);
        });
    }

    /**
     * Verifica se um usuário tem uma permissão específica
     */
    public function usuarioTemPermissao(int $userId, string $permissao): bool
    {
        $permissoes = $this->getPermissoesUsuario($userId);
        return $permissoes->contains($permissao);
    }

    /**
     * Obtém todas as permissões de um usuário
     */
    public function getPermissoesUsuario(int $userId): Collection
    {
        return Cache::remember("user_permissions_flat_{$userId}", 3600, function () use ($userId) {
            return $this->repository->getPermissoesUsuario($userId);
        });
    }

    /**
     * Limpa o cache de permissões de um usuário
     */
    public function limparCachePermissoes(int $userId): void
    {
        Cache::forget("user_permissions_{$userId}");
        Cache::forget("user_permissions_flat_{$userId}");
    }
}</pre>
                </div>

                <div class="code-block">
                    <h4>Middleware de Verificação de Permissões</h4>
                    <pre>
// Em app/Http/Middleware/VerifyMicroservicePermission.php
namespace App\Http\Middleware;

use App\Http\Sistema\Autenticacao\Services\PermissaoService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class VerifyMicroservicePermission
{
    protected $permissaoService;

    public function __construct(PermissaoService $permissaoService)
    {
        $this->permissaoService = $permissaoService;
    }

    public function handle(Request $request, Closure $next, string $microsservico, string $permissao): Response
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Não autenticado',
                'code' => 'UNAUTHENTICATED'
            ], 401);
        }

        $permissaoCompleta = "{$microsservico}.{$permissao}";

        if (!$this->permissaoService->usuarioTemPermissao($user->id, $permissaoCompleta)) {
            // Registrar tentativa de acesso não autorizado
            Log::warning('Tentativa de acesso não autorizado a microsserviço', [
                'user_id' => $user->id,
                'microsservico' => $microsservico,
                'permissao' => $permissao,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Acesso não autorizado',
                    'code' => 'PERMISSION_DENIED'
                ], 403);
            }

            abort(403, 'Acesso não autorizado');
        }

        return $next($request);
    }
}</pre>
                </div>

                <div class="best-practice">
                    <h4>Estrutura de Permissões para Microsserviços</h4>
                    <p>Recomendamos estruturar as permissões seguindo um padrão consistente para
                        facilitar o gerenciamento:
                    </p>
                    <ul>
                        <li><strong>Formato:</strong> <code>[microsservico].[recurso].[ação]</code></li>
                        <li><strong>Exemplos:</strong>
                            <ul>
                                <li><code>usuarios.perfil.visualizar</code></li>
                                <li><code>financeiro.transacoes.criar</code></li>
                                <li><code>produtos.estoque.atualizar</code></li>
                                <li><code>relatorios.vendas.exportar</code></li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Uso em Rotas</h4>
                    <pre>
// Em routes/api.php
Route::prefix('api/v1')->group(function () {
    // Rotas públicas
    Route::post('/login', [\App\Http\Sistema\Autenticacao\Controllers\ApiAuthController::class, 'login']);
    
    // Rotas protegidas por autenticação e permissões específicas de microsserviço
    Route::middleware(['auth:sanctum'])->group(function () {
        // Rotas do microsserviço Financeiro
        Route::prefix('financeiro')->group(function () {
            Route::middleware(['verify.permission:financeiro,transacoes.listar'])->get('/transacoes', 
                [\App\Http\Sistema\Financeiro\Controllers\TransacaoController::class, 'index']);
                
            Route::middleware(['verify.permission:financeiro,transacoes.criar'])->post('/transacoes', 
                [\App\Http\Sistema\Financeiro\Controllers\TransacaoController::class, 'store']);
                
            Route::middleware(['verify.permission:financeiro,relatorios.gerar'])->get('/relatorios', 
                [\App\Http\Sistema\Financeiro\Controllers\RelatorioController::class, 'index']);
        });
        
        // Rotas do microsserviço Produtos
        Route::prefix('produtos')->group(function () {
            Route::middleware(['verify.permission:produtos,catalogo.listar'])->get('/', 
                [\App\Http\Sistema\Produtos\Controllers\ProdutoController::class, 'index']);
                
            Route::middleware(['verify.permission:produtos,catalogo.criar'])->post('/', 
                [\App\Http\Sistema\Produtos\Controllers\ProdutoController::class, 'store']);
                
            Route::middleware(['verify.permission:produtos,estoque.atualizar'])->put('/{produto}/estoque', 
                [\App\Http\Sistema\Produtos\Controllers\EstoqueController::class, 'update']);
        });
    });
});</pre>
                </div>

                <div class="note">
                    <p>O sistema de permissões baseado em microsserviços permite um controle de acesso
                        granular e facilita a
                        manutenção à medida que novos microsserviços são adicionados. Cada equipe
                        responsável por um
                        microsserviço pode definir suas próprias permissões seguindo o padrão
                        estabelecido.</p>
                </div>
            </section>

            <section id="seguranca-eventos" class="subsection">
                <h3>12.4. Segurança em Eventos entre Microsserviços</h3>
                <p>A comunicação assíncrona entre microsserviços através de eventos também requer
                    considerações de
                    segurança:</p>

                <div class="code-block">
                    <h4>Implementação de Eventos Seguros</h4>
                    <pre>
// Em app/Http/Sistema/Pedidos/Events/PedidoCriado.php
namespace App\Http\Sistema\Pedidos\Events;

use App\Http\Sistema\Pedidos\Models\Pedido;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Crypt;

class PedidoCriado
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $pedido;
    public $dadosSensiveisEncriptados;
    public $timestamp;
    public $assinatura;

    public function __construct(Pedido $pedido, array $dadosSensiveis = [])
    {
        $this->pedido = $pedido;
        
        // Criptografar dados sensíveis
        if (!empty($dadosSensiveis)) {
            $this->dadosSensiveisEncriptados = Crypt::encrypt($dadosSensiveis);
        }
        
        // Adicionar timestamp para prevenir replay attacks
        $this->timestamp = now()->timestamp;
        
        // Gerar assinatura para verificar integridade
        $this->assinatura = $this->gerarAssinatura();
    }
    
    protected function gerarAssinatura(): string
    {
        // Gerar assinatura baseada nos dados do evento e uma chave secreta
        $dados = $this->pedido->id . $this->timestamp . config('app.key');
        return hash_hmac('sha256', $dados, config('app.key'));
    }
    
    public function verificarAssinatura(): bool
    {
        return $this->assinatura === $this->gerarAssinatura();
    }
}</pre>
                </div>

                <div class="code-block">
                    <h4>Listener com Verificação de Segurança</h4>
                    <pre>
// Em app/Http/Sistema/Estoque/Listeners/AtualizarEstoqueQuandoPedidoCriado.php
namespace App\Http\Sistema\Estoque\Listeners;

use App\Http\Sistema\Pedidos\Events\PedidoCriado;
use App\Http\Sistema\Produtos\Services\EstoqueService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class AtualizarEstoqueQuandoPedidoCriado implements ShouldQueue
{
    use InteractsWithQueue;
    
    protected $estoqueService;
    
    // Número máximo de tentativas
    public $tries = 3;
    
    public function __construct(EstoqueService $estoqueService)
    {
        $this->estoqueService = $estoqueService;
    }
    
    public function handle(PedidoCriado $event): void
    {
        // Verificar assinatura do evento para garantir integridade
        if (!$event->verificarAssinatura()) {
            Log::error('Evento PedidoCriado com assinatura inválida', [
                'pedido_id' => $event->pedido->id,
                'timestamp' => $event->timestamp
            ]);
            
            $this->fail('Assinatura de evento inválida');
            return;
        }
        
        // Verificar se o evento não é muito antigo (prevenção contra replay attacks)
        $tempoMaximoEmSegundos = 300; // 5 minutos
        if (now()->timestamp - $event->timestamp > $tempoMaximoEmSegundos) {
            Log::warning('Evento PedidoCriado expirado', [
                'pedido_id' => $event->pedido->id,
                'timestamp' => $event->timestamp,
                'diferenca' => now()->timestamp - $event->timestamp
            ]);
            
            $this->fail('Evento expirado');
            return;
        }
        
        try {
            // Processar o evento
            $this->estoqueService->reduzirEstoque(
                $event->pedido->produto_id,
                $event->pedido->quantidade
            );
            
            Log::info('Estoque atualizado após pedido', [
                'pedido_id' => $event->pedido->id,
                'produto_id' => $event->pedido->produto_id,
                'quantidade' => $event->pedido->quantidade
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao atualizar estoque após pedido', [
                'pedido_id' => $event->pedido->id,
                'erro' => $e->getMessage()
            ]);
            
            // Retentar mais tarde
            $this->release(30); // Tentar novamente em 30 segundos
        }
    }
}</pre>
                </div>

                <div class="best-practice">
                    <h4>Melhores Práticas para Segurança em Eventos</h4>
                    <ul>
                        <li>Adicione assinaturas digitais aos eventos para verificar integridade</li>
                        <li>Inclua timestamps para prevenir replay attacks</li>
                        <li>Criptografe dados sensíveis incluídos nos eventos</li>
                        <li>Implemente verificações de autorização nos listeners</li>
                        <li>Registre tentativas de processamento de eventos inválidos ou expirados</li>
                        <li>Configure filas específicas para diferentes tipos de eventos</li>
                        <li>Implemente mecanismos de retry com backoff exponencial</li>
                        <li>Monitore falhas em filas de eventos para detectar possíveis ataques</li>
                    </ul>
                </div>
            </section>
        </section>

        <section id="checklist" class="manual-section">
            <h2>13. Checklist de Segurança</h2>
            <p>Use esta checklist para garantir que sua aplicação Laravel 12 com arquitetura modular
                esteja seguindo as
                melhores práticas de segurança.</p>

            <div class="checklist-container">
                <div class="checklist-group">
                    <h3>Autenticação</h3>
                    <ul class="checklist">
                        <li><input type="checkbox"> Implementar política de senhas fortes (mínimo 12
                            caracteres)</li>
                        <li><input type="checkbox"> Verificar senhas contra listas de senhas vazadas
                        </li>
                        <li><input type="checkbox"> Implementar autenticação de dois fatores (2FA)</li>
                        <li><input type="checkbox"> Configurar bloqueio de conta após tentativas falhas
                        </li>
                        <li><input type="checkbox"> Implementar processo seguro de recuperação de senha
                        </li>
                        <li><input type="checkbox"> Usar tokens seguros para "lembrar-me" e sessões</li>
                        <li><input type="checkbox"> Registrar eventos de autenticação (login, logout,
                            falhas)</li>
                        <li><input type="checkbox"> Implementar expiração de sessão após inatividade
                        </li>
                        <li><input type="checkbox"> Considerar WebAuthn para autenticação sem senha</li>
                    </ul>
                </div>

                <div class="checklist-group">
                    <h3>Autorização</h3>
                    <ul class="checklist">
                        <li><input type="checkbox"> Implementar controle de acesso baseado em papéis
                            (RBAC)</li>
                        <li><input type="checkbox"> Usar Gates e Policies para autorização granular</li>
                        <li><input type="checkbox"> Verificar autorização em todas as rotas e
                            controllers</li>
                        <li><input type="checkbox"> Implementar middleware de autorização para proteção
                            de rotas</li>
                        <li><input type="checkbox"> Usar tipagem estrita em todas as definições de
                            autorização</li>
                        <li><input type="checkbox"> Implementar verificação de permissões em templates
                            Blade</li>
                        <li><input type="checkbox"> Configurar políticas de autorização para APIs</li>
                        <li><input type="checkbox"> Implementar auditoria de decisões de autorização
                        </li>
                        <li><input type="checkbox"> Estruturar permissões por microsserviço</li>
                    </ul>
                </div>

                <div class="checklist-group">
                    <h3>Proteção de Dados</h3>
                    <ul class="checklist">
                        <li><input type="checkbox"> Criptografar dados sensíveis em repouso</li>
                        <li><input type="checkbox"> Implementar hashing seguro para senhas</li>
                        <li><input type="checkbox"> Sanitizar todas as entradas de usuário</li>
                        <li><input type="checkbox"> Mascarar dados sensíveis em logs e respostas</li>
                        <li><input type="checkbox"> Implementar auditoria para alterações em dados
                            sensíveis</li>
                        <li><input type="checkbox"> Configurar políticas de retenção de dados</li>
                        <li><input type="checkbox"> Implementar backup seguro de dados</li>
                        <li><input type="checkbox"> Usar atributos criptografados em modelos Eloquent
                        </li>
                    </ul>
                </div>

                <div class="checklist-group">
                    <h3>Proteção contra Ataques</h3>
                    <ul class="checklist">
                        <li><input type="checkbox"> Implementar proteção CSRF em todos os formulários
                        </li>
                        <li><input type="checkbox"> Configurar cabeçalhos HTTP de segurança</li>
                        <li><input type="checkbox"> Implementar Content Security Policy (CSP)</li>
                        <li><input type="checkbox"> Configurar SameSite cookies apropriadamente</li>
                        <li><input type="checkbox"> Implementar rate limiting para endpoints sensíveis
                        </li>
                        <li><input type="checkbox"> Proteger contra ataques de SQL Injection</li>
                        <li><input type="checkbox"> Implementar proteção contra XSS</li>
                        <li><input type="checkbox"> Configurar CORS para APIs</li>
                    </ul>
                </div>

                <div class="checklist-group">
                    <h3>Segurança de API</h3>
                    <ul class="checklist">
                        <li><input type="checkbox"> Implementar autenticação robusta (Sanctum/JWT)</li>
                        <li><input type="checkbox"> Configurar escopos/habilidades para tokens</li>
                        <li><input type="checkbox"> Implementar expiração e rotação de tokens</li>
                        <li><input type="checkbox"> Validar e sanitizar todas as entradas de API</li>
                        <li><input type="checkbox"> Implementar rate limiting específico para API</li>
                        <li><input type="checkbox"> Configurar respostas de erro seguras</li>
                        <li><input type="checkbox"> Implementar versionamento de API</li>
                        <li><input type="checkbox"> Registrar e monitorar uso da API</li>
                    </ul>
                </div>

                <div class="checklist-group">
                    <h3>Segurança em Microsserviços</h3>
                    <ul class="checklist">
                        <li><input type="checkbox"> Implementar isolamento de responsabilidades entre
                            microsserviços</li>
                        <li><input type="checkbox"> Validar dados em cada camada de microsserviço</li>
                        <li><input type="checkbox"> Implementar comunicação segura entre microsserviços
                        </li>
                        <li><input type="checkbox"> Usar transações para manter consistência entre
                            microsserviços</li>
                        <li><input type="checkbox"> Implementar circuit breakers para evitar falhas em
                            cascata</li>
                        <li><input type="checkbox"> Estruturar permissões específicas para cada
                            microsserviço</li>
                        <li><input type="checkbox"> Implementar assinaturas digitais em eventos entre
                            microsserviços</li>
                        <li><input type="checkbox"> Configurar logging centralizado para todos os
                            microsserviços</li>
                    </ul>
                </div>

                <div class="checklist-group">
                    <h3>Infraestrutura e Configuração</h3>
                    <ul class="checklist">
                        <li><input type="checkbox"> Forçar HTTPS em produção</li>
                        <li><input type="checkbox"> Proteger variáveis de ambiente (.env)</li>
                        <li><input type="checkbox"> Configurar permissões de arquivo adequadas</li>
                        <li><input type="checkbox"> Manter dependências atualizadas e seguras</li>
                        <li><input type="checkbox"> Implementar monitoramento de segurança</li>
                        <li><input type="checkbox"> Configurar backups seguros</li>
                        <li><input type="checkbox"> Implementar logging seguro</li>
                        <li><input type="checkbox"> Configurar firewall e proteções de rede</li>
                    </ul>
                </div>

                <div class="checklist-group">
                    <h3>Testes de Segurança</h3>
                    <ul class="checklist">
                        <li><input type="checkbox"> Realizar testes de penetração regulares</li>
                        <li><input type="checkbox"> Implementar análise estática de código</li>
                        <li><input type="checkbox"> Testar vulnerabilidades de dependências</li>
                        <li><input type="checkbox"> Realizar revisões de código focadas em segurança
                        </li>
                        <li><input type="checkbox"> Testar proteções contra ataques comuns</li>
                        <li><input type="checkbox"> Verificar configurações de segurança em ambientes
                        </li>
                        <li><input type="checkbox"> Implementar testes automatizados de segurança</li>
                        <li><input type="checkbox"> Documentar e corrigir vulnerabilidades encontradas
                        </li>
                    </ul>
                </div>
            </div>

            <div class="conclusion">
                <h3>Conclusão</h3>
                <p>A segurança é um processo contínuo, não um estado final. Mantenha-se atualizado sobre
                    as melhores
                    práticas de segurança e novas vulnerabilidades. O Laravel 12 fornece muitas
                    ferramentas e recursos para
                    construir aplicações seguras, mas é responsabilidade dos desenvolvedores
                    implementá-las corretamente e
                    mantê-las atualizadas.</p>
                <p>Em uma arquitetura modular baseada em sistemas e microsserviços, é especialmente
                    importante garantir que
                    cada componente seja seguro individualmente e que a comunicação entre eles seja
                    protegida. Siga as
                    diretrizes deste manual para implementar uma estratégia de segurança abrangente em
                    toda a aplicação.</p>
            </div>
        </section>

        <footer class="manual-footer">
            <p>Manual de Segurança - Laravel 12 | Última atualização: Junho 2024</p>
        </footer>
</body>

</html>