/* manual.css */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-bg: #f8f9fa;
    --dark-bg: #2c3e50;
    --border-color: #dee2e6;
    --text-color: #333;
    --text-light: #6c757d;
    --text-dark: #212529;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --code-bg: #f7f9fa;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
}

body {
    font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.3;
    color: var(--primary-color);
}

p {
    margin-bottom: 1.2rem;
}

a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.2s;
}

a:hover {
    color: #1a6aae;
    text-decoration: underline;
}

ul,
ol {
    margin-bottom: 1.2rem;
    padding-left: 2rem;
}

li {
    margin-bottom: 0.5rem;
}

code {
    font-family: 'Courier New', Courier, monospace;
    background: var(--code-bg);
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.9em;
    color: var(--accent-color);
}

pre {
    background: var(--code-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
    margin: 1rem 0;
}

pre code {
    padding: 0;
    background: none;
    color: var(--text-dark);
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
}

th,
td {
    padding: 0.75rem;
    text-align: left;
    border: 1px solid var(--border-color);
}

th {
    background-color: var(--light-bg);
    font-weight: 600;
}

tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Header Styles */
.manual-header {
    padding: 3rem 1rem;
    text-align: center;
    background-color: var(--primary-color);
    color: white;
    margin-bottom: 2rem;
    border-radius: 0 0 8px 8px;
}

.manual-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: white;
}

.manual-header .version {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
}

/* Navigation Styles */
.manual-nav {
    background: var(--light-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 2rem;
    position: sticky;
    top: 20px;
    z-index: 100;
}

.manual-nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
}

.manual-nav li {
    margin: 0;
}

.manual-nav a {
    display: block;
    padding: 1rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: all 0.2s ease;
    font-weight: 500;
}

.manual-nav a:hover {
    background-color: var(--secondary-color);
    color: white;
}

/* Section Styles */
.manual-section {
    margin-bottom: 3rem;
    padding: 2rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.manual-section h2 {
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-color);
    margin-bottom: 1.5rem;
}

.subsection {
    margin: 2rem 0;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.subsection h3 {
    margin-bottom: 1.2rem;
}

.content-block {
    margin-bottom: 1.5rem;
}

/* Intro Text and Key Points */
.intro-text {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.key-points {
    background-color: var(--light-bg);
    padding: 1.5rem;
    border-left: 4px solid var(--secondary-color);
    border-radius: 0 4px 4px 0;
}

.key-points h3 {
    margin-top: 0;
    color: var(--secondary-color);
}

.key-points ul {
    margin-bottom: 0;
}

/* Code Blocks */
.code-block {
    margin: 1.5rem 0;
}

.code-block h4 {
    background-color: var(--primary-color);
    color: white;
    padding: 0.7rem 1rem;
    border-radius: 4px 4px 0 0;
    margin: 0;
}

.code-block pre {
    margin-top: 0;
    border-top: none;
    border-radius: 0 0 4px 4px;
}

/* Tables */
.comparison-table {
    margin: 1.5rem 0;
    overflow-x: auto;
}

.comparison-table table {
    min-width: 600px;
}

.comparison-table th {
    background-color: var(--primary-color);
    color: white;
}

.metrics-table table {
    border: 2px solid var(--secondary-color);
}

.metrics-table th {
    background-color: var(--secondary-color);
    color: white;
}

/* Best Practices */
.best-practice {
    background-color: #e8f4fd;
    border: 1px solid #b8daff;
    border-radius: 6px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.best-practice h4 {
    color: var(--secondary-color);
    margin-top: 0;
    margin-bottom: 1rem;
}

.best-practice ul {
    margin-bottom: 0;
}

/* Tips */
.tip {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.tip h4 {
    color: #28a745;
    margin-top: 0;
}

/* Explanation */
.explanation {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 6px;
    margin: 1.5rem 0;
}

/* Example Commits */
.example-commits {
    background-color: var(--code-bg);
    border-left: 4px solid var(--secondary-color);
    padding: 1rem;
    margin: 1.5rem 0;
    overflow-x: auto;
}

.example-commits pre {
    border: none;
    background: transparent;
    padding: 0;
    margin: 0;
}

/* Tools List */
.tools-list ul {
    list-style-type: none;
    padding: 0;
}

.tools-list li {
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: #fff;
}

.tools-list strong {
    font-size: 1.1rem;
    color: var(--primary-color);
    display: block;
    margin-bottom: 0.5rem;
}

.tools-list p {
    margin-bottom: 0.8rem;
}

.tools-list pre {
    margin-bottom: 0;
    background-color: #f1f3f4;
}

/* Architecture Diagram */
.architecture-diagram {
    display: flex;
    flex-direction: column;
    margin: 2rem 0;
    gap: 0.5rem;
}

.arch-layer {
    padding: 1.2rem;
    background: var(--light-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    text-align: center;
}

.arch-layer h4 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.arch-layer p {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.arch-layer.split {
    display: flex;
    gap: 1rem;
}

.arch-layer.split .half {
    flex: 1;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
}

/* Checklist */
.checklist-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.checklist-group {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    overflow: hidden;
}

.checklist-group h3 {
    background-color: var(--primary-color);
    color: white;
    padding: 0.8rem 1rem;
    margin: 0;
}

.checklist {
    list-style-type: none;
    padding: 1rem;
    margin: 0;
}

.checklist li {
    margin-bottom: 0.8rem;
    display: flex;
    align-items: flex-start;
}

.checklist input[type="checkbox"] {
    margin-top: 0.2rem;
    margin-right: 0.5rem;
    transform: scale(1.2);
}

/* Conclusion */
.conclusion {
    background-color: var(--light-bg);
    border-left: 4px solid var(--primary-color);
    padding: 1.5rem;
    margin: 2rem 0;
}

.conclusion h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 1rem;
}

/* Footer */
.manual-footer {
    margin-top: 4rem;
    padding: 2rem 0;
    text-align: center;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .manual-header h1 {
        font-size: 2rem;
    }

    .manual-nav ul {
        flex-direction: column;
    }

    .manual-nav li {
        border-bottom: 1px solid var(--border-color);
    }

    .manual-nav li:last-child {
        border-bottom: none;
    }

    .checklist-container {
        grid-template-columns: 1fr;
    }

    .manual-section {
        padding: 1.5rem;
    }

    .arch-layer.split {
        flex-direction: column;
    }

    table {
        display: block;
        overflow-x: auto;
    }
}

/* Layer Diagrams for Caching Section */
.layers-diagram {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 1.5rem 0;
}

.layer {
    background-color: var(--light-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 1rem;
    text-align: center;
}

.layer h4 {
    margin-top: 0;
    color: var(--secondary-color);
}

.layer p {
    margin-bottom: 0;
}

/* Alerts Section */
.alerts-section {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: 4px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.alerts-section h4 {
    color: #856404;
    margin-top: 0;
    margin-bottom: 1rem;
}

.alerts-section ul {
    margin-bottom: 0;
}

/* Print Styles */
@media print {
    .manual-nav {
        display: none;
    }

    .manual-section {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    a {
        text-decoration: underline;
        color: var(--text-dark);
    }

    html {
        scroll-padding-top: 0;
    }
}

.header-container {
    display: flex;
    align-items: center;
    gap: 20px;
}

.home-button {
    font-size: 24px;
    color: #fff;
    background-color: #4a6da7;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: background-color 0.3s;
}

.home-button:hover {
    background-color: #2c4a7c;
}

/* Estilos específicos para a página index */
.manual-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.manual-card {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background-color: white;
    box-shadow: var(--shadow);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.manual-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.manual-card-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1.5rem;
}

.manual-card-header.frontend {
    background-color: #42b883;
    /* Vue.js green */
}

.manual-card-header.auxiliar {
    background-color: #7957d5;
    /* Purple for auxiliary */
}

.manual-card-header h3 {
    margin: 0;
    font-size: 1.3rem;
    color: white;
}

.manual-card-body {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.manual-card-body p {
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.tag {
    display: inline-block;
    background-color: var(--light-bg);
    padding: 0.3rem 0.6rem;
    border-radius: 3px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.btn {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    text-decoration: none;
    text-align: center;
    transition: background-color 0.2s;
    margin-top: auto;
}

.btn:hover {
    background-color: #2980b9;
    text-decoration: none;
    color: white;
}

.btn-frontend {
    background-color: #42b883;
}

.btn-frontend:hover {
    background-color: #3aa876;
}

.btn-auxiliar {
    background-color: #7957d5;
}

.btn-auxiliar:hover {
    background-color: #6a4ec2;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--secondary-color);
    color: var(--secondary-color);
}

.btn-outline:hover {
    background-color: var(--secondary-color);
    color: white;
}

.coming-soon {
    opacity: 0.7;
}

.coming-soon .btn {
    background-color: var(--text-light);
    cursor: not-allowed;
}

.search-container {
    margin: 2rem 0;
}

.search-container input {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
}

.coming-soon-section {
    margin-top: 4rem;
}

.section-title {
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 0.5rem;
    margin-bottom: 2rem;
}

.section-title.frontend {
    border-bottom-color: #42b883;
}

.section-title.auxiliar {
    border-bottom-color: #7957d5;
}

.hero-section {
    background-color: var(--primary-color);
    color: white;
    padding: 4rem 2rem;
    text-align: center;
    margin: -20px -20px 2rem -20px;
}

.hero-section h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: white;
}

.hero-section p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 1rem;
    }

    .hero-section h1 {
        font-size: 2rem;
    }
}