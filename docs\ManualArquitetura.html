<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Arquitetura</title>
    <link rel="stylesheet" href="css/manual.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Arquitetura</h1>
        <p>Descrição da arquitetura do sistema e seus componentes</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#visao-geral">Visão Geral</a></li>
            <li><a href="#camadas">Camadas</a></li>
            <li><a href="#componentes">Componentes</a></li>
            <li><a href="#fluxo-dados">Fluxo de Dados</a></li>
            <li><a href="#seguranca">Segurança</a></li>
            <li><a href="#integracoes">Integrações</a></li>
            <li><a href="#escalabilidade">Escalabilidade</a></li>
            <li><a href="#decisoes">Decisões</a></li>
            <li><a href="#laravel12">Laravel 12</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#visao-geral">2. Visão Geral da Arquitetura</a></li>
            <li><a href="#camadas">3. Arquitetura em Camadas</a></li>
            <li><a href="#componentes">4. Principais Componentes</a></li>
            <li><a href="#fluxo-dados">5. Fluxo de Dados</a></li>
            <li><a href="#seguranca">6. Segurança na Arquitetura</a></li>
            <li><a href="#integracoes">7. Integrações Externas</a></li>
            <li><a href="#escalabilidade">8. Escalabilidade e Performance</a></li>
            <li><a href="#decisoes">9. Decisões Arquiteturais</a></li>
            <li><a href="#laravel12">10. Novidades do Laravel 12</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <p class="intro-text">Este documento descreve a arquitetura do nosso sistema, baseada no framework Laravel 12
            com PHP 8.2, detalhando os componentes, camadas, fluxos de dados e princípios arquiteturais adotados.</p>

        <p>A arquitetura foi projetada seguindo os princípios SOLID e padrões de design que promovem:</p>
        <ul>
            <li>Baixo acoplamento entre componentes</li>
            <li>Alta coesão dentro dos módulos</li>
            <li>Testabilidade e manutenibilidade</li>
            <li>Escalabilidade horizontal e vertical</li>
            <li>Segurança em múltiplas camadas</li>
        </ul>

        <div class="alerts-section">
            <h4>Arquitetura em Camadas</h4>
            <p>Nossa implementação utiliza uma arquitetura em camadas bem definida, com classes abstratas que fornecem
                funcionalidades base para Controllers, Services, Repositories e Responses, permitindo uma implementação
                consistente e reduzindo a duplicação de código.</p>
        </div>
    </section>

    <section id="visao-geral" class="manual-section">
        <h2>2. Visão Geral da Arquitetura</h2>
        <p>O sistema segue uma arquitetura em camadas que separa claramente as responsabilidades entre diferentes
            componentes do software:</p>

        <div class="diagram" align="center">
            <svg width="800" height="500" xmlns="http://www.w3.org/2000/svg">
                <!-- Fundo do diagrama -->
                <rect width="800" height="500" fill="#f8f9fa" rx="10" ry="10" stroke="#dee2e6" stroke-width="2" />

                <!-- Título do diagrama -->
                <text x="400" y="40" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="24" text-anchor="middle" fill="#2c3e50" font-weight="bold">Arquitetura em Camadas</text>

                <!-- Camada de Apresentação -->
                <rect x="100" y="80" width="600" height="80" rx="5" ry="5" fill="#3498db" stroke="#2980b9"
                    stroke-width="2" />

                <!-- Seta 1 -->
                <line x1="400" y1="160" x2="400" y2="180" stroke="#2c3e50" stroke-width="2" stroke-dasharray="5,5" />
                <polygon points="400,190 395,180 405,180" fill="#2c3e50" />

                <!-- Camada de Aplicação -->
                <rect x="100" y="190" width="600" height="80" rx="5" ry="5" fill="#2ecc71" stroke="#27ae60"
                    stroke-width="2" />

                <!-- Seta 2 -->
                <line x1="400" y1="270" x2="400" y2="290" stroke="#2c3e50" stroke-width="2" stroke-dasharray="5,5" />
                <polygon points="400,300 395,290 405,290" fill="#2c3e50" />

                <!-- Camada de Domínio -->
                <rect x="100" y="300" width="600" height="80" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad"
                    stroke-width="2" />

                <!-- Seta 3 -->
                <line x1="400" y1="380" x2="400" y2="400" stroke="#2c3e50" stroke-width="2" stroke-dasharray="5,5" />
                <polygon points="400,410 395,400 405,400" fill="#2c3e50" />

                <!-- Camada de Infraestrutura -->
                <rect x="100" y="410" width="600" height="80" rx="5" ry="5" fill="#f39c12" stroke="#d35400"
                    stroke-width="2" />

                <!-- Componentes da Camada de Apresentação -->
                <rect x="120" y="100" width="100" height="40" rx="3" ry="3" fill="#2980b9" stroke="white"
                    stroke-width="1" />
                <text x="170" y="125" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Controllers</text>

                <rect x="230" y="100" width="100" height="40" rx="3" ry="3" fill="#2980b9" stroke="white"
                    stroke-width="1" />
                <text x="280" y="125" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Requests</text>

                <rect x="340" y="100" width="100" height="40" rx="3" ry="3" fill="#2980b9" stroke="white"
                    stroke-width="1" />
                <text x="390" y="125" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Resources</text>

                <rect x="450" y="100" width="100" height="40" rx="3" ry="3" fill="#2980b9" stroke="white"
                    stroke-width="1" />
                <text x="500" y="125" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Responses</text>

                <rect x="560" y="100" width="120" height="40" rx="3" ry="3" fill="#2980b9" stroke="white"
                    stroke-width="1" />
                <text x="620" y="125" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Middlewares</text>

                <!-- Componentes da Camada de Aplicação -->
                <rect x="120" y="210" width="170" height="40" rx="3" ry="3" fill="#27ae60" stroke="white"
                    stroke-width="1" />
                <text x="205" y="235" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Services</text>

                <rect x="300" y="210" width="170" height="40" rx="3" ry="3" fill="#27ae60" stroke="white"
                    stroke-width="1" />
                <text x="385" y="235" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Validação</text>

                <rect x="480" y="210" width="200" height="40" rx="3" ry="3" fill="#27ae60" stroke="white"
                    stroke-width="1" />
                <text x="580" y="235" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Transações</text>

                <!-- Componentes da Camada de Domínio -->
                <rect x="120" y="320" width="130" height="40" rx="3" ry="3" fill="#8e44ad" stroke="white"
                    stroke-width="1" />
                <text x="185" y="345" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Models</text>

                <rect x="260" y="320" width="130" height="40" rx="3" ry="3" fill="#8e44ad" stroke="white"
                    stroke-width="1" />
                <text x="325" y="345" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Policies</text>

                <rect x="400" y="320" width="130" height="40" rx="3" ry="3" fill="#8e44ad" stroke="white"
                    stroke-width="1" />
                <text x="465" y="345" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Enums</text>

                <rect x="540" y="320" width="140" height="40" rx="3" ry="3" fill="#8e44ad" stroke="white"
                    stroke-width="1" />
                <text x="610" y="345" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Regras de Negócio</text>

                <!-- Componentes da Camada de Infraestrutura -->
                <rect x="120" y="430" width="130" height="40" rx="3" ry="3" fill="#d35400" stroke="white"
                    stroke-width="1" />
                <text x="185" y="455" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Repositories</text>

                <rect x="260" y="430" width="130" height="40" rx="3" ry="3" fill="#d35400" stroke="white"
                    stroke-width="1" />
                <text x="325" y="455" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">External Services</text>

                <rect x="400" y="430" width="100" height="40" rx="3" ry="3" fill="#d35400" stroke="white"
                    stroke-width="1" />
                <text x="450" y="455" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Cache</text>

                <rect x="510" y="430" width="80" height="40" rx="3" ry="3" fill="#d35400" stroke="white"
                    stroke-width="1" />
                <text x="550" y="455" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Queue</text>

                <rect x="600" y="430" width="80" height="40" rx="3" ry="3" fill="#d35400" stroke="white"
                    stroke-width="1" />
                <text x="640" y="455" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Mail</text>
            </svg>
        </div>


        <div class="architecture-diagram">
            <div class="arch-layer">
                <h4>Camada de Apresentação</h4>
                <p>Controllers, Requests, Resources, Responses, Middlewares</p>
            </div>
            <div class="arch-layer">
                <h4>Camada de Aplicação</h4>
                <p>Services, Validação, Transações</p>
            </div>
            <div class="arch-layer">
                <h4>Camada de Domínio</h4>
                <p>Models, Policies, Enums, Regras de Negócio</p>
            </div>
            <div class="arch-layer">
                <h4>Camada de Infraestrutura</h4>
                <p>Repositories, External Services, Cache, Queue, Mail</p>
            </div>
        </div>

        <p>A arquitetura do sistema está estruturada nas seguintes camadas principais:</p>
        <ul>
            <li><strong>Camada de Apresentação</strong>: Responsável pela interação com o usuário e formatação das
                respostas</li>
            <li><strong>Camada de Aplicação</strong>: Orquestra as operações do sistema e implementa a lógica de negócio
            </li>
            <li><strong>Camada de Domínio</strong>: Contém as regras de negócio e entidades do domínio</li>
            <li><strong>Camada de Infraestrutura</strong>: Lida com interações com sistemas externos, persistência e
                operações técnicas</li>
        </ul>
    </section>

    <section id="camadas" class="manual-section">
        <h2>3. Arquitetura em Camadas</h2>
        <p>Detalhamento de cada camada da arquitetura:</p>

        <div class="subsection">
            <h3>3.1. Camada de Apresentação</h3>
            <p>Esta camada é responsável pela interface com o usuário, principalmente através de endpoints API.</p>
            <ul>
                <li><strong>Controllers</strong>: Recebem as requisições HTTP, delegam o processamento para a camada de
                    aplicação e retornam respostas apropriadas.</li>
                <li><strong>Responses</strong>: Formatam os dados de saída em formato padronizado JSON.</li>
                <li><strong>Middlewares</strong>: Processam requisições antes que cheguem aos controllers (autenticação,
                    logs, etc).</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Http\Controllers\Api;

use App\Http\Controllers\ControllerAbstract;
use App\Models\User;
use App\Services\UserService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class UserController extends ControllerAbstract
{
    protected $response;

    public function __construct(UserService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function index(Request $request)
    {
        $this->authorize('viewAny', User::class);

        $perPage = $request->get('per_page', 15);
        $sortField = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $filters = $request->only(['name_like', 'email_like']);

        if ($request->has('paginate') && $request->paginate === 'false') {
            $users = $this->service->getFiltered($filters, $sortField, $sortDirection);
            $transformedUsers = collect($users)->map(function ($user) {
                return $this->service->transformUser($user);
            })->all();

            return $this->response->success($transformedUsers);
        } else {
            $paginator = $this->service->getPaginatedWithFilters(
                $perPage, 
                $filters, 
                $sortField, 
                $sortDirection
            );

            $transformedItems = collect($paginator->items())->map(function ($user) {
                return $this->service->transformUser($user);
            })->all();

            return $this->response->paginated(
                $transformedItems,
                [
                    'current_page' => $paginator->currentPage(),
                    'last_page' => $paginator->lastPage(),
                    'per_page' => $paginator->perPage(),
                    'total' => $paginator->total()
                ]
            );
        }
    }</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Classe Base: ControllerAbstract</h4>
                <p>Todos os controllers da API estendem a classe <code>ControllerAbstract</code>, que fornece
                    funcionalidades comuns como injeção do serviço e métodos auxiliares. Isso garante consistência e
                    reduz a duplicação de código.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>3.2. Camada de Aplicação (Services)</h3>
            <p>Esta camada contém a lógica de aplicação e orquestra as operações entre as diferentes partes do sistema.
            </p>
            <ul>
                <li><strong>Services</strong>: Implementam a lógica de negócio, orquestrando operações entre diferentes
                    repositórios.</li>
                <li><strong>Validação</strong>: Implementam regras de validação complexas a nível de negócio.</li>
                <li><strong>Transações</strong>: Gerenciam transações que envolvem múltiplas operações.</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Services;

use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\WelcomeEmail;

class UserService extends ServiceAbstract
{
    protected $createRules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:users,email',
        'password' => 'required|string|min:8|confirmed',
    ];

    protected $updateRules = [
        'name' => 'sometimes|string|max:255',
        'email' => 'sometimes|email|unique:users,email',
        'password' => 'sometimes|nullable|string|min:8|confirmed',
    ];

    protected $validationMessages = [
        'name.required' => 'O nome é obrigatório.',
        'email.required' => 'O email é obrigatório.',
        'email.email' => 'O email deve ser um endereço válido.',
        'email.unique' => 'Este email já está em uso.',
        'password.required' => 'A senha é obrigatória.',
        'password.min' => 'A senha deve ter pelo menos 8 caracteres.',
        'password.confirmed' => 'A confirmação da senha não corresponde.',
    ];

    public function __construct(UserRepository $repository)
    {
        parent::__construct($repository);
    }

    protected function afterCreate(User $model, array $data): void
    {
        parent::afterCreate($model, $data);

        try {
            // Enviar email de boas-vindas
            Mail::to($model->email)->send(new WelcomeEmail($model));
        } catch (\Exception $e) {
            Log::error('Erro ao enviar email de boas-vindas', [
                'user_id' => $model->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function transformUser(User $user): array
    {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'email_verified' => $user->email_verified_at !== null,
            'created_at' => $user->created_at->toIso8601String(),
            'updated_at' => $user->updated_at->toIso8601String(),
        ];
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Classe Base: ServiceAbstract</h4>
                <p>A classe <code>ServiceAbstract</code> fornece funcionalidades comuns como validação, criação,
                    atualização e exclusão de registros. Os serviços específicos estendem esta classe e implementam
                    lógica de negócio específica para cada entidade.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>3.3. Camada de Domínio</h3>
            <p>Esta camada contém as entidades de negócio e as regras que governam o domínio da aplicação.</p>
            <ul>
                <li><strong>Models</strong>: Representam as entidades de negócio no sistema.</li>
                <li><strong>Policies</strong>: Definem regras de autorização para as entidades.</li>
                <li><strong>Enums</strong>: Representam conjuntos fixos de valores relacionados.</li>
                <li><strong>Regras de Negócio</strong>: Implementam as regras que são intrínsecas ao domínio.</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    // Campos pesquisáveis para filtros
    public $searchable = [
        'name',
        'email',
    ];

    // Relacionamentos
    public function posts()
    {
        return $this->hasMany(Post::class);
    }

    // Métodos de negócio
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    public function hasRole(string $role): bool
    {
        // Implementação da verificação de papel
        return true; // Simplificado para o exemplo
    }
}</code></pre>
            </div>

            <div class="code-block">
                <pre><code>namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    public function view(User $user, User $model): bool
    {
        return $user->id === $model->id || $user->isAdmin();
    }

    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    public function update(User $user, User $model): bool
    {
        return $user->id === $model->id || $user->isAdmin();
    }

    public function delete(User $user, User $model): bool
    {
        return $user->isAdmin() && $user->id !== $model->id;
    }

    public function restore(User $user): bool
    {
        return $user->isAdmin();
    }

    public function forceDelete(User $user, User $model): bool
    {
        return $user->isAdmin() && $user->id !== $model->id;
    }
}</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>3.4. Camada de Infraestrutura</h3>
            <p>Esta camada fornece suporte técnico para as outras camadas, lidando com aspectos como persistência,
                integração com sistemas externos, etc.</p>
            <ul>
                <li><strong>Repositories</strong>: Implementam o acesso aos dados persistentes.</li>
                <li><strong>Mail</strong>: Gerenciam o envio de emails.</li>
                <li><strong>Logging</strong>: Implementam mecanismos de registro de logs.</li>
                <li><strong>Cache</strong>: Gerenciam o cache do sistema.</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Repositories;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Collection;

class UserRepository extends RepositoryAbstract
{
    public function __construct(User $model)
    {
        parent::__construct($model);
    }

    public function create(array $data): User
    {
        // Se a senha estiver presente, garante que seja hasheada
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        return parent::create($data);
    }

    public function update($id, array $data): User
    {
        // Se a senha estiver presente, garante que seja hasheada
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } elseif (isset($data['password']) && empty($data['password'])) {
            // Se a senha estiver vazia, remove do array para não atualizar
            unset($data['password']);
        }

        return parent::update($id, $data);
    }

    public function findByEmail(string $email): ?User
    {
        return $this->model->where('email', $email)->first();
    }

    public function findActive(): Collection
    {
        return $this->model->whereNotNull('email_verified_at')->get();
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Classe Base: RepositoryAbstract</h4>
                <p>A classe <code>RepositoryAbstract</code> fornece métodos comuns para acesso a dados como find,
                    create, update, delete, etc. Os repositórios específicos estendem esta classe e implementam métodos
                    específicos para cada entidade.</p>
            </div>

            <div class="code-block">
                <h4>Exemplo de Email:</h4>
                <pre><code>namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class WelcomeEmail extends Mailable
{
    use Queueable, SerializesModels;

    protected $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function build()
    {
        return $this->markdown('emails.welcome')
            ->subject('Bem-vindo ao ' . config('app.name'))
            ->with([
                'name' => $this->user->name,
                'appName' => config('app.name')
            ]);
    }
}</code></pre>
            </div>
        </div>
    </section>

    <section id="componentes" class="manual-section">
        <h2>4. Principais Componentes</h2>

        <div class="subsection">
            <h3>4.1. Controllers</h3>
            <p>Os Controllers são responsáveis por receber as requisições HTTP, delegá-las para a camada de serviço
                apropriada e retornar as respostas. Seguem o princípio de manter controllers "magros", com mínima
                lógica.</p>

            <p>Em nosso projeto, todos os controllers da API estendem a classe <code>ControllerAbstract</code>, que
                fornece:</p>
            <ul>
                <li>Injeção automática do serviço correspondente</li>
                <li>Métodos para autorização de ações</li>
                <li>Tratamento padronizado de exceções</li>
            </ul>
        </div>

        <div class="subsection">
            <h3>4.2. Services</h3>
            <p>Os Services encapsulam a lógica de negócio da aplicação. Eles são responsáveis por orquestrar as
                operações entre diferentes repositórios e componentes, garantindo a consistência dos dados e
                implementando regras de negócio.</p>

            <p>Em nosso projeto, todos os services estendem a classe <code>ServiceAbstract</code>, que fornece:</p>
            <ul>
                <li>Validação de dados com regras definidas no serviço</li>
                <li>Métodos CRUD padronizados</li>
                <li>Hooks para personalizar o comportamento (beforeCreate, afterCreate, etc.)</li>
                <li>Tratamento de transações</li>
            </ul>
        </div>

        <div class="subsection">
            <h3>4.3. Repositories</h3>
            <p>Os Repositories abstraem a camada de persistência, fornecendo métodos para acesso e manipulação de dados.
                Isso desacopla a lógica de negócio do acesso a dados, facilitando testes e manutenção.</p>

            <p>Em nosso projeto, todos os repositories estendem a classe <code>RepositoryAbstract</code>, que fornece:
            </p>
            <ul>
                <li>Métodos CRUD básicos</li>
                <li>Métodos para busca com filtros</li>
                <li>Paginação</li>
                <li>Ordenação</li>
                <li>Soft delete e restauração</li>
            </ul>
        </div>

        <div class="subsection">
            <h3>4.4. Models</h3>
            <p>Os Models representam as entidades do domínio e seus relacionamentos. No contexto do Laravel, eles também
                incluem métodos de consulta e definições de relacionamentos.</p>

            <p>Características dos nossos models:</p>
            <ul>
                <li>Uso de traits como SoftDeletes, HasFactory, etc.</li>
                <li>Definição clara de $fillable, $hidden e $casts</li>
                <li>Propriedade $searchable para campos pesquisáveis</li>
                <li>Relacionamentos bem definidos</li>
                <li>Métodos de negócio específicos da entidade</li>
            </ul>
        </div>

        <div class="subsection">
            <h3>4.5. Responses</h3>
            <p>As classes de Response padronizam o formato de respostas da API, garantindo consistência em toda a
                aplicação.</p>

            <div class="code-block">
                <pre><code>namespace App\Responses;

class ApiResponse extends ResponseAbstract
{
    protected $apiVersion = '1.0';

    public function authenticationError($message = 'Credenciais inválidas.', array $headers = [])
    {
        return $this->error($message, self::HTTP_UNAUTHORIZED, [], $headers);
    }

    public function loginSuccess($userData, $token, $message = 'Login realizado com sucesso.', array $headers = [])
    {
        $data = [
            'user' => $userData,
            'access_token' => $token,
            'token_type' => 'Bearer'
        ];

        return $this->success($data, $message, $headers);
    }

    public function logoutSuccess($message = 'Logout realizado com sucesso.', array $headers = [])
    {
        return $this->success(null, $message, $headers);
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Classe Base: ResponseAbstract</h4>
                <p>A classe <code>ResponseAbstract</code> fornece métodos para criar respostas padronizadas como
                    success, error, created, notFound, etc. As classes específicas de resposta estendem esta classe e
                    implementam métodos específicos para cada contexto.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>4.6. Policies</h3>
            <p>As Policies definem regras de autorização para as entidades do sistema, centralizando a lógica de
                controle de acesso.</p>

            <p>Características das nossas policies:</p>
            <ul>
                <li>Métodos claros para cada tipo de ação (viewAny, view, create, update, delete, etc.)</li>
                <li>Integração com o sistema de autorização do Laravel</li>
                <li>Verificações baseadas em papéis e permissões</li>
            </ul>
        </div>
    </section>

    <section id="fluxo-dados" class="manual-section">
        <h2>5. Fluxo de Dados</h2>

        <p>O fluxo de dados na arquitetura segue um padrão consistente para todas as operações:</p>

        <div class="diagram" align="center">
            <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
                <!-- Fundo do diagrama -->
                <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10" stroke="#dee2e6" stroke-width="2" />

                <!-- Título do diagrama -->
                <text x="400" y="40" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="24" text-anchor="middle" fill="#2c3e50" font-weight="bold">Fluxo de Dados na Arquitetura
                    em Camadas</text>

                <!-- Cliente -->
                <rect x="300" y="80" width="200" height="60" rx="5" ry="5" fill="#3498db" stroke="#2980b9"
                    stroke-width="2" />
                <text x="400" y="115" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="16" text-anchor="middle" fill="white" font-weight="bold">Cliente</text>
                <text x="400" y="135" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Aplicação Frontend / API Client</text>

                <!-- Seta 1 -->
                <line x1="400" y1="140" x2="400" y2="170" stroke="#2c3e50" stroke-width="2" stroke-dasharray="5,5" />
                <polygon points="400,180 395,170 405,170" fill="#2c3e50" />
                <text x="450" y="165" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" fill="#2c3e50">HTTP Request</text>

                <!-- Middleware -->
                <rect x="300" y="180" width="200" height="60" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad"
                    stroke-width="2" />
                <text x="400" y="210" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="16" text-anchor="middle" fill="white" font-weight="bold">Middleware</text>
                <text x="400" y="230" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Autenticação, Logging, Validação</text>

                <!-- Seta 2 -->
                <line x1="400" y1="240" x2="400" y2="270" stroke="#2c3e50" stroke-width="2" stroke-dasharray="5,5" />
                <polygon points="400,280 395,270 405,270" fill="#2c3e50" />
                <text x="480" y="265" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" fill="#2c3e50">Requisição Validada</text>

                <!-- Controller -->
                <rect x="300" y="280" width="200" height="60" rx="5" ry="5" fill="#e74c3c" stroke="#c0392b"
                    stroke-width="2" />
                <text x="400" y="310" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="16" text-anchor="middle" fill="white" font-weight="bold">Controller</text>
                <text x="400" y="330" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Recebe e coordena a requisição</text>

                <!-- Seta 3 -->
                <line x1="400" y1="340" x2="400" y2="370" stroke="#2c3e50" stroke-width="2" stroke-dasharray="5,5" />
                <polygon points="400,380 395,370 405,370" fill="#2c3e50" />
                <text x="470" y="365" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" fill="#2c3e50">Delega processamento</text>

                <!-- Service -->
                <rect x="300" y="380" width="200" height="60" rx="5" ry="5" fill="#2ecc71" stroke="#27ae60"
                    stroke-width="2" />
                <text x="400" y="410" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="16" text-anchor="middle" fill="white" font-weight="bold">Service</text>
                <text x="400" y="430" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Implementa lógica de negócio</text>

                <!-- Seta 4 -->
                <line x1="400" y1="440" x2="400" y2="470" stroke="#2c3e50" stroke-width="2" stroke-dasharray="5,5" />
                <polygon points="400,480 395,470 405,470" fill="#2c3e50" />
                <text x="470" y="465" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" fill="#2c3e50">Acessa dados</text>

                <!-- Repository -->
                <rect x="300" y="480" width="200" height="60" rx="5" ry="5" fill="#f39c12" stroke="#d35400"
                    stroke-width="2" />
                <text x="400" y="510" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="16" text-anchor="middle" fill="white" font-weight="bold">Repository</text>
                <text x="400" y="530" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" text-anchor="middle" fill="white">Acesso a dados e persistência</text>

                <!-- Seta de retorno -->
                <path d="M 550,310 C 600,310 600,510 550,510" stroke="#2c3e50" stroke-width="2" fill="none"
                    stroke-dasharray="5,5" />
                <polygon points="550,510 560,505 560,515" fill="#2c3e50" />
                <text x="620" y="410" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" fill="#2c3e50" text-anchor="middle">Fluxo de Retorno</text>

                <!-- Seta de resposta -->
                <path d="M 250,310 C 200,310 200,115 250,115" stroke="#2c3e50" stroke-width="2" fill="none"
                    stroke-dasharray="5,5" />
                <polygon points="250,115 240,110 240,120" fill="#2c3e50" />
                <text x="180" y="210" font-family="'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
                    font-size="12" fill="#2c3e50" text-anchor="middle">Resposta HTTP</text>
            </svg>
        </div>

        <div class="layers-diagram">
            <div class="layer">
                <h4>1. Request HTTP</h4>
                <p>Cliente envia requisição para um endpoint da API</p>
            </div>
            <div class="layer">
                <h4>2. Middleware</h4>
                <p>Autenticação, logging, validação de token, etc.</p>
            </div>
            <div class="layer">
                <h4>3. Controller</h4>
                <p>Recebe a requisição e delega para o service</p>
            </div>
            <div class="layer">
                <h4>4. Service</h4>
                <p>Valida dados e implementa a lógica de negócio</p>
            </div>
            <div class="layer">
                <h4>5. Repository</h4>
                <p>Executa operações de persistência</p>
            </div>
            <div class="layer">
                <h4>6. Model</h4>
                <p>Representa as entidades do domínio</p>
            </div>
            <div class="layer">
                <h4>7. Response</h4>
                <p>Formatação da resposta e retorno ao cliente</p>
            </div>
        </div>

        <div class="subsection">
            <h3>5.1. Exemplo de Fluxo Completo</h3>

            <div class="example">
                <h4>Fluxo de criação de um usuário:</h4>
                <ol>
                    <li>O cliente envia uma requisição POST para <code>/api/users</code> com dados do usuário.</li>
                    <li>Os middlewares de autenticação e autorização verificam se o requisitante tem permissão.</li>
                    <li>O <code>UserController@store</code> recebe a requisição.</li>
                    <li>O controller chama o método <code>createWithValidation</code> do <code>UserService</code> com os
                        dados da requisição.</li>
                    <li>O service valida os dados conforme as regras definidas em <code>$createRules</code>.</li>
                    <li>Se a validação passar, o service chama o método <code>create</code> do
                        <code>UserRepository</code>.
                    </li>
                    <li>O repository cria um novo registro no banco de dados.</li>
                    <li>O service executa o método <code>afterCreate</code> que envia um email de boas-vindas.</li>
                    <li>O controller recebe o resultado e o formata usando o método <code>transformUser</code> do
                        service.</li>
                    <li>A resposta HTTP é enviada ao cliente com o código 201 (Created) e os dados do usuário criado.
                    </li>
                </ol>
            </div>
        </div>
    </section>

    <section id="seguranca" class="manual-section">
        <h2>6. Segurança na Arquitetura</h2>

        <p>A segurança é implementada em múltiplas camadas da arquitetura:</p>

        <div class="subsection">
            <h3>6.1. Autenticação e Autorização</h3>
            <ul>
                <li>Autenticação via Sanctum para APIs</li>
                <li>Controle de acesso baseado em função (RBAC) através de políticas</li>
                <li>Middleware de autenticação que verifica tokens</li>
            </ul>

            <div class="code-block">
                <pre><code>// routes/api.php
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/me', [AuthController::class, 'me']);
    Route::apiResource('users', UserController::class);
    Route::post('/users/{id}/restore', [UserController::class, 'restore']);
});</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>6.2. Proteção de Dados</h3>
            <ul>
                <li>Criptografia de dados sensíveis em trânsito (HTTPS)</li>
                <li>Hash seguro para senhas usando bcrypt</li>
                <li>Proteção contra ataques CSRF para formulários web</li>
            </ul>

            <div class="code-block">
                <pre><code>// Middleware de taxa de requisições
Route::middleware(['throttle:api'])->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>6.3. Validação e Sanitização</h3>
            <ul>
                <li>Validação rigorosa de todos os dados de entrada</li>
                <li>Sanitização de dados para prevenir XSS e injeção SQL</li>
                <li>Validação em múltiplas camadas (controllers, services)</li>
            </ul>

            <div class="code-block">
                <pre><code>// Em UserService
protected $createRules = [
    'name' => 'required|string|max:255',
    'email' => 'required|email|unique:users,email',
    'password' => 'required|string|min:8|confirmed',
];</code></pre>
            </div>
        </div>
    </section>

    <section id="integracoes" class="manual-section">
        <h2>7. Integrações Externas</h2>

        <p>Nossa arquitetura foi projetada para facilitar a integração com sistemas e serviços externos, mantendo um
            baixo acoplamento e alta coesão.</p>

        <div class="subsection">
            <h3>7.1. Padrão de Integração</h3>
            <p>Para todas as integrações externas, seguimos um padrão consistente:</p>
            <ul>
                <li>Criação de classes de serviço específicas para cada integração</li>
                <li>Uso de interfaces para permitir implementações alternativas</li>
                <li>Configuração via variáveis de ambiente</li>
                <li>Tratamento de erros e timeouts</li>
                <li>Logging detalhado de requisições e respostas</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Services\External;

use App\Interfaces\PaymentGatewayInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class StripePaymentService implements PaymentGatewayInterface
{
    protected $apiKey;
    protected $baseUrl;
    
    public function __construct()
    {
        $this->apiKey = config('services.stripe.key');
        $this->baseUrl = config('services.stripe.url');
    }
    
    public function processPayment(array $paymentData): array
    {
        try {
            $response = Http::withToken($this->apiKey)
                ->post("{$this->baseUrl}/payments", $paymentData);
                
            if ($response->successful()) {
                return [
                    'success' => true,
                    'transaction_id' => $response->json('id'),
                    'status' => $response->json('status')
                ];
            }
            
            Log::error('Stripe payment failed', [
                'error' => $response->json(),
                'status' => $response->status()
            ]);
            
            return [
                'success' => false,
                'error' => $response->json('error.message')
            ];
        } catch (\Exception $e) {
            Log::error('Stripe service exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'error' => 'Serviço de pagamento indisponível'
            ];
        }
    }
}</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>7.2. APIs de Terceiros</h3>
            <p>Para integração com APIs de terceiros, utilizamos:</p>
            <ul>
                <li>Cliente HTTP do Laravel para requisições</li>
                <li>Cache para reduzir chamadas repetidas</li>
                <li>Filas para operações assíncronas</li>
                <li>Circuit breaker para evitar falhas em cascata</li>
            </ul>

            <div class="alerts-section">
                <h4>Tratamento de Falhas</h4>
                <p>Todas as integrações implementam estratégias de retry, timeout e fallback para garantir a resiliência
                    do sistema mesmo quando serviços externos estão indisponíveis.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>7.3. Webhooks</h3>
            <p>Para receber notificações de sistemas externos, implementamos endpoints de webhook:</p>
            <ul>
                <li>Verificação de assinatura para garantir autenticidade</li>
                <li>Processamento assíncrono via filas</li>
                <li>Idempotência para evitar processamento duplicado</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Jobs\ProcessStripeWebhook;

class WebhookController extends Controller
{
    public function handleStripeWebhook(Request $request)
    {
        $payload = $request->all();
        $signature = $request->header('Stripe-Signature');
        
        try {
            // Verificar assinatura
            $this->verifySignature($payload, $signature);
            
            // Processar webhook de forma assíncrona
            ProcessStripeWebhook::dispatch($payload);
            
            return response()->json(['message' => 'Webhook received']);
        } catch (\Exception $e) {
            Log::error('Webhook error', [
                'message' => $e->getMessage(),
                'payload' => $payload
            ]);
            
            return response()->json(['error' => 'Invalid webhook'], 400);
        }
    }
    
    private function verifySignature($payload, $signature)
    {
        // Implementação da verificação de assinatura
        if (!$signature) {
            throw new \Exception('Missing signature');
        }
        
        // Verificação real da assinatura...
    }
}</code></pre>
            </div>
        </div>
    </section>

    <section id="escalabilidade" class="manual-section">
        <h2>8. Escalabilidade e Performance</h2>

        <p>Nossa arquitetura foi projetada para escalar horizontalmente e verticalmente, garantindo alta performance
            mesmo com aumento de carga.</p>

        <div class="subsection">
            <h3>8.1. Estratégias de Cache</h3>
            <p>Implementamos múltiplas camadas de cache para otimizar a performance:</p>
            <ul>
                <li><strong>Cache de Consultas</strong>: Armazenamento de resultados de consultas frequentes</li>
                <li><strong>Cache de Respostas HTTP</strong>: Armazenamento de respostas completas para endpoints
                    frequentemente acessados</li>
                <li><strong>Cache de Configurações</strong>: Armazenamento de configurações do sistema</li>
                <li><strong>Cache de Sessão</strong>: Armazenamento de dados de sessão em Redis</li>
            </ul>

            <div class="code-block">
                <pre><code>// Exemplo de cache em um repository
public function getAllActive(): Collection
{
    return Cache::remember('users.active', 3600, function () {
        return $this->model->whereNotNull('email_verified_at')->get();
    });
}

// Exemplo de cache em um controller
public function index(Request $request)
{
    $cacheKey = 'users.list.' . md5(json_encode($request->all()));
    
    return Cache::remember($cacheKey, 300, function () use ($request) {
        // Lógica normal do controller
        $perPage = $request->get('per_page', 15);
        $users = $this->service->getPaginatedWithFilters($perPage, $request->only(['name_like', 'email_like']));
        
        return $this->response->paginated($users);
    });
}</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>8.2. Processamento Assíncrono</h3>
            <p>Utilizamos filas para processar tarefas de forma assíncrona, melhorando a responsividade da API:</p>
            <ul>
                <li>Envio de emails</li>
                <li>Processamento de webhooks</li>
                <li>Geração de relatórios</li>
                <li>Sincronização com sistemas externos</li>
            </ul>

            <div class="code-block">
                <pre><code>namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\ReportService;

class GenerateMonthlyReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $month;
    protected $year;
    protected $userId;

    public function __construct(int $month, int $year, int $userId)
    {
        $this->month = $month;
        $this->year = $year;
        $this->userId = $userId;
    }

    public function handle(ReportService $reportService)
    {
        $report = $reportService->generateMonthlyReport($this->month, $this->year);
        
        // Notificar usuário que o relatório está pronto
        $user = User::find($this->userId);
        $user->notify(new ReportReady($report));
    }
}</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>8.3. Otimização de Banco de Dados</h3>
            <p>Implementamos várias estratégias para otimizar o acesso ao banco de dados:</p>
            <ul>
                <li>Índices adequados para campos frequentemente consultados</li>
                <li>Eager loading para evitar o problema N+1</li>
                <li>Paginação para limitar o volume de dados retornados</li>
                <li>Query scopes para consultas frequentes</li>
                <li>Soft deletes para manter histórico sem impactar performance</li>
            </ul>

            <div class="code-block">
                <pre><code>// Exemplo de eager loading em um repository
public function findWithRelations($id, array $relations = []): ?Model
{
    return $this->model->with($relations)->find($id);
}

// Exemplo de query scope em um model
public function scopeActive($query)
{
    return $query->whereNotNull('email_verified_at');
}

// Uso do scope
$activeUsers = User::active()->get();</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>8.4. Escalabilidade Horizontal</h3>
            <p>Nossa arquitetura suporta escalabilidade horizontal através de:</p>
            <ul>
                <li>Aplicação stateless que pode ser replicada em múltiplos servidores</li>
                <li>Uso de Redis para cache compartilhado e filas</li>
                <li>Sessões armazenadas em Redis em vez de arquivos locais</li>
                <li>Armazenamento de arquivos em serviço de storage distribuído</li>
            </ul>

            <div class="alerts-section">
                <h4>Configuração para Ambientes de Alta Disponibilidade</h4>
                <p>Em ambientes de produção, recomendamos configurar múltiplas instâncias da aplicação atrás de um
                    balanceador de carga, com Redis para cache e filas, e um banco de dados com replicação.</p>
            </div>
        </div>
    </section>

    <section id="decisoes" class="manual-section">
        <h2>9. Decisões Arquiteturais</h2>

        <p>Esta seção documenta as principais decisões arquiteturais tomadas durante o desenvolvimento do sistema e suas
            justificativas.</p>

        <div class="subsection">
            <h3>9.1. Arquitetura em Camadas</h3>
            <p><strong>Decisão:</strong> Adotar uma arquitetura em camadas com Controllers, Services, Repositories e
                Models.</p>
            <p><strong>Justificativa:</strong> Esta arquitetura promove:</p>
            <ul>
                <li>Separação clara de responsabilidades</li>
                <li>Facilidade de manutenção e evolução</li>
                <li>Testabilidade de componentes isolados</li>
                <li>Reutilização de código</li>
            </ul>
            <p><strong>Alternativas consideradas:</strong> Arquitetura MVC tradicional do Laravel, Domain-Driven Design
                (DDD).</p>
        </div>

        <div class="subsection">
            <h3>9.2. Classes Abstratas Base</h3>
            <p><strong>Decisão:</strong> Implementar classes abstratas base para Controllers, Services, Repositories e
                Responses.</p>
            <p><strong>Justificativa:</strong></p>
            <ul>
                <li>Redução de código duplicado</li>
                <li>Padronização de implementações</li>
                <li>Facilidade para implementar novos recursos</li>
                <li>Consistência nas interfaces de programação</li>
            </ul>
            <p><strong>Alternativas consideradas:</strong> Traits, interfaces, injeção de dependência sem classes base.
            </p>
        </div>

        <div class="subsection">
            <h3>9.3. API RESTful com Sanctum</h3>
            <p><strong>Decisão:</strong> Utilizar Laravel Sanctum para autenticação de API.</p>
            <p><strong>Justificativa:</strong></p>
            <ul>
                <li>Segurança robusta com tokens SPA</li>
                <li>Suporte a múltiplos dispositivos</li>
                <li>Integração nativa com o Laravel</li>
                <li>Baixa complexidade comparada a OAuth</li>
            </ul>
            <p><strong>Alternativas consideradas:</strong> JWT, Passport, autenticação básica.</p>
        </div>

        <div class="subsection">
            <h3>9.4. Soft Deletes</h3>
            <p><strong>Decisão:</strong> Implementar soft deletes para entidades principais.</p>
            <p><strong>Justificativa:</strong></p>
            <ul>
                <li>Preservação de histórico</li>
                <li>Possibilidade de restauração de dados</li>
                <li>Manutenção da integridade referencial</li>
                <li>Auditoria de exclusões</li>
            </ul>
            <p><strong>Alternativas consideradas:</strong> Exclusão permanente, tabelas de histórico separadas.</p>
        </div>

        <div class="subsection">
            <h3>9.5. Validação nos Services</h3>
            <p><strong>Decisão:</strong> Implementar validação de dados na camada de serviço em vez de requests.</p>
            <p><strong>Justificativa:</strong></p>
            <ul>
                <li>Centralização das regras de validação</li>
                <li>Reutilização das mesmas regras em diferentes contextos</li>
                <li>Validação consistente independente da fonte dos dados</li>
                <li>Facilidade para testar regras de validação</li>
            </ul>
            <p><strong>Alternativas consideradas:</strong> Form Requests, validação nos controllers.</p>
        </div>
    </section>

    <section id="laravel12" class="manual-section">
        <h2>10. Novidades do Laravel 12</h2>

        <p>Nossa aplicação utiliza o Laravel 12, que traz diversas melhorias e novos recursos que impactam nossa
            arquitetura.</p>

        <div class="subsection">
            <h3>10.1. Principais Recursos Utilizados</h3>
            <ul>
                <li><strong>Suporte Nativo a PHP 8.2</strong>: Aproveitamos os recursos de tipagem, readonly properties
                    e outras melhorias de performance do PHP 8.2.</li>
                <li><strong>Lazy Collections</strong>: Utilizamos para processamento eficiente de grandes conjuntos de
                    dados.</li>
                <li><strong>Invokable Controllers</strong>: Implementamos para endpoints simples que realizam uma única
                    ação.</li>
                <li><strong>Melhorias no Sistema de Filas</strong>: Aproveitamos para processamento assíncrono mais
                    eficiente.</li>
                <li><strong>Rate Limiting Aprimorado</strong>: Implementamos para proteger endpoints críticos.</li>
            </ul>
        </div>

        <div class="subsection">
            <h3>10.2. Impacto na Arquitetura</h3>
            <p>O Laravel 12 influenciou nossa arquitetura das seguintes formas:</p>
            <ul>
                <li>Maior uso de tipagem forte em toda a aplicação</li>
                <li>Implementação de classes readonly para objetos de valor</li>
                <li>Uso de atributos PHP 8 para metadados</li>
                <li>Aproveitamento de enums para valores constantes</li>
                <li>Utilização de novas funcionalidades de coleções para transformação de dados</li>
            </ul>

            <div class="code-block">
                <pre><code>// Exemplo de uso de readonly class para DTO
readonly class UserData
{
    public function __construct(
        public string $name,
        public string $email,
        public ?string $phone = null
    ) {}
    
    public static function fromArray(array $data): self
    {
        return new self(
            name: $data['name'],
            email: $data['email'],
            phone: $data['phone'] ?? null
        );
    }
}

// Exemplo de uso de enum
enum UserRole: string
{
    case ADMIN = 'admin';
    case MANAGER = 'manager';
    case USER = 'user';
    
    public function permissions(): array
    {
        return match($this) {
            self::ADMIN => ['read', 'write', 'delete', 'manage_users'],
            self::MANAGER => ['read', 'write', 'delete'],
            self::USER => ['read'],
        };
    }
}</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>10.3. Boas Práticas com Laravel 12</h3>
            <ul>
                <li>Utilizar tipagem forte em todos os métodos</li>
                <li>Aproveitar recursos de PHP 8.2 como readonly, enums e match expressions</li>
                <li>Implementar DTOs para transferência de dados entre camadas</li>
                <li>Utilizar Value Objects para encapsular conceitos do domínio</li>
                <li>Aproveitar as novas funcionalidades de coleções para transformação de dados</li>
            </ul>

            <div class="alerts-section">
                <h4>Compatibilidade</h4>
                <p>Ao desenvolver novos componentes, certifique-se de que são compatíveis com PHP 8.2 e aproveite os
                    recursos mais recentes do Laravel 12 para maximizar performance e legibilidade do código.</p>
            </div>
        </div>
    </section>

    <footer class="manual-footer">
        <p>© 2023 - Manual de Arquitetura - Laravel 12 | PHP 8.2</p>
        <p>Última atualização: Agosto 2023</p>
    </footer>
</body>

</html>