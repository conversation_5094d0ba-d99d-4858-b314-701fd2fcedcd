<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ config('app.name', 'Laravel') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
    
    <!-- Styles / Scripts -->
    @if (file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    @endif
    
    <?php
    // Renderizar os assets da debugbar (CSS e JavaScript)
    if (class_exists('\Debugbar')) {
        $renderer = \Debugbar::getJavascriptRenderer();
        echo $renderer->renderHead();
    }
    ?>
</head>
<body>
    <div id="app">
        @yield('content')
    </div>
    
    <?php
    // Renderizar o corpo da debugbar
    if (class_exists('\Debugbar')) {
        $renderer = \Debugbar::getJavascriptRenderer();
        echo $renderer->render();
    }
    ?>
</body>
</html>
