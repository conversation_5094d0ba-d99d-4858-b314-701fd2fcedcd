<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Implementação</title>
    <link rel="stylesheet" href="css/manual.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Implementação</h1>
        <p>Guia prático para implementação do sistema seguindo os padrões da arquitetura</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#ambiente">Ambiente</a></li>
            <li><a href="#estrutura">Estrutura</a></li>
            <li><a href="#organizacao-modular">Organização Modular</a></li>
            <li><a href="#implementacao-camadas">Implementação</a></li>
            <li><a href="#padroes">Padrões</a></li>
            <li><a href="#excecoes">Exceções</a></li>
            <li><a href="#testes">Testes</a></li>
            <li><a href="#validacao">Validação</a></li>
            <li><a href="#versionamento">Versionamento</a></li>
            <li><a href="#boas-praticas">Boas Práticas</a></li>
            <li><a href="#criacao-microsservicos">Novos Microsserviços</a></li>
            <li><a href="#laravel12">Laravel 12</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#ambiente">2. Configuração do Ambiente de Desenvolvimento</a></li>
            <li><a href="#estrutura">3. Estrutura do Projeto</a></li>
            <li><a href="#organizacao-modular">4. Organização Modular por Sistemas e Microsserviços</a>
                <ul>
                    <li><a href="#estrutura-microsservico">4.1. Estrutura de um Microsserviço</a></li>
                    <li><a href="#implementacao-microsservico">4.2. Implementação de um Microsserviço</a></li>
                    <li><a href="#comunicacao-microsservicos">4.3. Comunicação entre Microsserviços</a></li>
                </ul>
            </li>
            <li><a href="#implementacao-camadas">5. Guias de Implementação por Camada</a>
                <ul>
                    <li><a href="#camada-apresentacao">5.1. Camada de Apresentação</a></li>
                    <li><a href="#camada-aplicacao">5.2. Camada de Aplicação</a></li>
                    <li><a href="#camada-dominio">5.3. Camada de Domínio</a></li>
                    <li><a href="#camada-infraestrutura">5.4. Camada de Infraestrutura</a></li>
                </ul>
            </li>
            <li><a href="#padroes">6. Padrões de Codificação</a></li>
            <li><a href="#excecoes">7. Tratamento de Exceções</a></li>
            <li><a href="#testes">8. Testes</a></li>
            <li><a href="#validacao">9. Validação de Dados</a></li>
            <li><a href="#versionamento">10. Controle de Versão</a></li>
            <li><a href="#boas-praticas">11. Boas Práticas</a></li>
            <li><a href="#criacao-microsservicos">12. Criação de Novos Microsserviços</a></li>
            <li><a href="#laravel12">13. Novidades do Laravel 12</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <p class="intro-text">Este manual fornece diretrizes práticas para implementação do nosso sistema seguindo a
            arquitetura em camadas definida. Destina-se a desenvolvedores que estão trabalhando no projeto e busca
            garantir consistência,
            qualidade e manutenibilidade do código.</p>

        <p>Os exemplos e recomendações deste manual são baseados nas melhores práticas de desenvolvimento com Laravel 12
            e PHP 8.2, seguindo os princípios arquiteturais descritos no Manual de Arquitetura.</p>

        <div class="key-points">
            <p><strong>Nota:</strong> Este documento pressupõe familiaridade com os conceitos apresentados no Manual de
                Arquitetura. Recomenda-se a leitura prévia daquele documento antes de prosseguir com este guia de
                implementação.</p>
        </div>
    </section>

    <section id="ambiente" class="manual-section">
        <h2>2. Configuração do Ambiente de Desenvolvimento</h2>

        <div class="subsection">
            <h3>2.1. Requisitos de Sistema</h3>
            <ul>
                <li>PHP 8.2 ou superior</li>
                <li>Composer 2.6 ou superior</li>
                <li>MySQL 8.0 ou superior</li>
                <li>Node.js 20 LTS ou superior (para assets)</li>
                <li>Git 2.30 ou superior</li>
                <li>Redis 7.0 ou superior (para cache e filas)</li>
            </ul>

            <div class="alerts-section">
                <h4>Atualização Laravel 12</h4>
                <p>O Laravel 12 requer PHP 8.2+ e Node.js 20 LTS. Nosso projeto está configurado especificamente para
                    PHP 8.2.</p>
            </div>
        </div>

        <div class="subsection">
            <h3>2.2. Instalação do Projeto</h3>
            <div class="code-block">
                <pre><code># Clone o repositório
git clone https://github.com/nossa-empresa/nosso-projeto.git
cd nosso-projeto

# Instale as dependências PHP
composer install

# Copie o arquivo de ambiente
cp .env.example .env

# Gere a chave da aplicação
php artisan key:generate

# Configure o banco de dados no arquivo .env
# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=nosso_projeto
# DB_USERNAME=usuario
# DB_PASSWORD=senha

# Execute as migrações
php artisan migrate

# Instale as dependências JavaScript
npm install

# Compile os assets
npm run dev</code></pre>
            </div>
        </div>

        <div class="subsection">
            <h3>2.3. Ferramentas Recomendadas</h3>
            <div class="tools-list">
                <ul>
                    <li>
                        <strong>IDE:</strong>
                        <p>PhpStorm, VS Code com extensões PHP (PHP Intelephense, Laravel Blade Snippets, Laravel
                            Snippets)</p>
                    </li>
                    <li>
                        <strong>Debugging:</strong>
                        <p>Xdebug 3.2+</p>
                    </li>
                    <li>
                        <strong>API Testing:</strong>
                        <p>Postman, Insomnia</p>
                    </li>
                    <li>
                        <strong>Controle de Qualidade:</strong>
                        <p>PHPStan, Laravel Pint</p>
                    </li>
                </ul>
            </div>
        </div>

        <div class="subsection">
            <h3>2.4. Docker (Opcional)</h3>
            <p>O projeto oferece suporte para desenvolvimento com Docker através do Laravel Sail:</p>
            <div class="code-block">
                <pre><code># Inicializar os contêineres de desenvolvimento
./vendor/bin/sail up -d

# Executar comandos dentro do contêiner
./vendor/bin/sail artisan migrate
./vendor/bin/sail composer install
./vendor/bin/sail test</code></pre>
            </div>
        </div>
    </section>

    <section id="estrutura" class="manual-section">
        <h2>3. Estrutura do Projeto</h2>

        <p>Nosso projeto utiliza uma estrutura personalizada baseada no Laravel 12, com uma organização modular por
            sistemas e microsserviços:</p>

        <div class="code-block">
            <pre><code>nosso-projeto/
├── app/ # Código da aplicação
│ ├── Console/ # Comandos artisan
│ ├── Contracts/ # Interfaces
│ ├── DTOs/ # Data Transfer Objects
│ ├── Exceptions/ # Exceções customizadas
│ ├── Http/ # Camada de Apresentação
│ │ ├── Controllers/ # Controllers gerais
│ │ ├── Middleware/ # Middlewares
│ │ ├── Requests/ # Form Requests gerais
│ │ ├── Resources/ # API Resources
│ │ └── Sistema/ # Organização por sistemas
│ │   ├── Micro1/ # Microsserviço 1
│ │   │   ├── Controllers/ # Controllers específicos do microsserviço
│ │   │   ├── Repositories/ # Repositórios específicos do microsserviço
│ │   │   ├── Services/ # Serviços específicos do microsserviço
│ │   │   ├── Requests/ # Form Requests específicos do microsserviço
│ │   │   └── Models/ # Models específicos do microsserviço (se houver)
│ │   ├── Micro2/ # Microsserviço 2
│ │   └── ... # Outros microsserviços
│ ├── Models/ # Modelos Eloquent gerais
│ ├── Policies/ # Políticas de autorização
│ ├── Repositories/ # Repositórios base
│ │ ├── Contracts/ # Interfaces de repositórios
│ │ └── Eloquent/ # Implementações Eloquent
│ ├── Responses/ # Classes de resposta
│ ├── Services/ # Serviços base
│ ├── ValueObjects/ # Objetos de valor
│ └── Providers/ # Service providers
├── bootstrap/ # Arquivos de inicialização
├── config/ # Configurações
├── database/ # Migrations, seeders
├── docs/ # Documentação do projeto
├── public/ # Assets públicos
├── resources/ # Views, assets não compilados
├── routes/ # Definição de rotas
│ ├── api.php # Rotas da API
│ ├── web.php # Rotas da Web
│ └── console.php # Comandos personalizados
├── storage/ # Armazenamento (logs, cache)
└── tests/ # Testes
    ├── Feature/ # Testes de feature
    └── Unit/ # Testes unitários</code></pre>
        </div>

        <div class="alerts-section">
            <h4>Estrutura Modular por Sistemas</h4>
            <p>Nossa estrutura organiza o código em sistemas e microsserviços dentro do diretório
                <code>Http/Sistema</code>. Cada microsserviço tem seus próprios controllers, serviços, repositórios e
                requests, permitindo uma clara separação de responsabilidades por domínio de negócio.
            </p>
        </div>
    </section>

    <section id="organizacao-modular" class="manual-section">
        <h2>4. Organização Modular por Sistemas e Microsserviços</h2>

        <p class="intro-text">Nosso projeto utiliza uma organização modular que separa o código em sistemas e
            microsserviços, cada um representando um domínio de negócio específico. Esta abordagem facilita a
            manutenção, escalabilidade e compreensão do sistema.</p>

        <h3>4.1. Estrutura de um Microsserviço</h3>
        <p>Cada microsserviço segue uma estrutura consistente dentro do diretório <code>app/Http/Sistema</code>:</p>

        <div class="code-block">
            <pre><code>app/Http/Sistema/NomeMicrosservico/
├── Controllers/ # Controllers específicos do microsserviço
├── Repositories/ # Repositórios específicos do microsserviço
├── Services/ # Serviços específicos do microsserviço
├── Requests/ # Form Requests específicos do microsserviço
└── Models/ # Models específicos do microsserviço (se necessário)</code></pre>
        </div>

        <h3>4.2. Implementação de um Microsserviço</h3>
        <p>Para implementar um novo microsserviço, siga estes passos:</p>

        <ol>
            <li>Crie a estrutura de diretórios dentro de <code>app/Http/Sistema</code></li>
            <li>Implemente os controllers, serviços e repositórios específicos do microsserviço</li>
            <li>Defina as rotas específicas do microsserviço</li>
            <li>Implemente os testes para o microsserviço</li>
        </ol>

        <div class="best-practice">
            <h4>Exemplo de Implementação de Microsserviço</h4>
            <pre><code>// app/Http/Sistema/Produtos/Controllers/ProdutoController.php
namespace App\Http\Sistema\Produtos\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Produtos\Services\ProdutoService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;

class ProdutoController extends ControllerAbstract
{
    protected $response;

    public function __construct(ProdutoService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function index(Request $request)
    {
        // Implementação específica para o microsserviço de produtos
    }
}</code></pre>
        </div>

        <div class="alerts-section">
            <h4>Autonomia e Coesão</h4>
            <p>Cada microsserviço deve ser autônomo e coeso, contendo todas as funcionalidades relacionadas ao seu
                domínio de negócio. Isso facilita a manutenção e evolução independente de cada parte do sistema.</p>
        </div>

        <h3>4.3. Comunicação entre Microsserviços</h3>
        <p>Os microsserviços podem se comunicar entre si através de:</p>

        <ul>
            <li>Injeção de dependência de serviços</li>
            <li>Eventos e listeners</li>
            <li>Chamadas diretas a serviços de outros microsserviços (quando necessário)</li>
        </ul>

        <div class="code-block">
            <pre><code>// Exemplo de comunicação entre microsserviços
namespace App\Http\Sistema\Pedidos\Services;

use App\Http\Sistema\Produtos\Services\ProdutoService;
use App\Http\Sistema\Clientes\Services\ClienteService;

class PedidoService extends ServiceAbstract
{
    protected $produtoService;
    protected $clienteService;

    public function __construct(
        PedidoRepository $repository,
        ProdutoService $produtoService,
        ClienteService $clienteService
    ) {
        parent::__construct($repository);
        $this->produtoService = $produtoService;
        $this->clienteService = $clienteService;
    }

    public function criarPedido(array $data)
    {
        // Verificar disponibilidade do produto
        $produto = $this->produtoService->findById($data['produto_id']);
        
        // Verificar cliente
        $cliente = $this->clienteService->findById($data['cliente_id']);
        
        // Criar pedido
        return $this->repository->create([
            'cliente_id' => $cliente->id,
            'produto_id' => $produto->id,
            'quantidade' => $data['quantidade'],
            'valor_total' => $produto->preco * $data['quantidade']
        ]);
    }
}</code></pre>
        </div>
    </section>

    <section id="implementacao-camadas" class="manual-section">
        <h2>5. Guias de Implementação por Camada</h2>

        <p>Cada microsserviço segue a mesma arquitetura em camadas, mas com implementações específicas para seu domínio
            de negócio.</p>

        <section id="camada-apresentacao" class="subsection">
            <h3>5.1. Camada de Apresentação</h3>

            <h4>5.1.1. Controllers</h4>
            <p>Em nosso projeto, todos os controllers estendem a classe <code>ControllerAbstract</code>, que fornece
                funcionalidades comuns. Os controllers específicos de cada microsserviço são implementados dentro do
                diretório <code>Controllers</code> do respectivo microsserviço:</p>

            <div class="best-practice">
                <h4>Implementação de Controller em um Microsserviço</h4>
                <pre><code>namespace App\Http\Sistema\Usuarios\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\Usuarios\Services\UsuarioService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;

class UsuarioController extends ControllerAbstract
{
    protected $response;

    public function __construct(UsuarioService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function index(Request $request)
    {
        // Implementação específica para o microsserviço de usuários
    }
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Classe Base: ControllerAbstract</h4>
                <p>A classe <code>ControllerAbstract</code> fornece funcionalidades como injeção automática do serviço
                    correspondente, métodos para autorização e tratamento padronizado de exceções. Isso garante
                    consistência em todos os controllers da API.</p>
            </div>

            <h4>5.1.2. Form Requests</h4>
            <p>Utilizamos Form Requests para validação de dados de entrada, específicos para cada microsserviço:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Sistema\Usuarios\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UsuarioStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create', User::class);
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'role_id' => 'sometimes|exists:roles,id'
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'O nome é obrigatório',
            'email.unique' => 'Este email já está em uso',
            'password.min' => 'A senha deve ter pelo menos 8 caracteres'
        ];
    }
}</code></pre>
            </div>

            <h4>5.1.3. Responses</h4>
            <p>Nossa arquitetura utiliza classes de resposta personalizadas para padronizar o formato das respostas da
                API:</p>

            <div class="code-block">
                <pre><code>namespace App\Responses;

class ApiResponse extends ResponseAbstract
{
    protected $apiVersion = '1.0';

    public function authenticationError($message = 'Credenciais inválidas.', array $headers = [])
    {
        return $this->error($message, self::HTTP_UNAUTHORIZED, [], $headers);
    }

    public function loginSuccess($userData, $token, $message = 'Login realizado com sucesso.', array $headers = [])
    {
        $data = [
            'user' => $userData,
            'access_token' => $token,
            'token_type' => 'Bearer'
        ];

        return $this->success($data, $message, $headers);
    }

    public function logoutSuccess($message = 'Logout realizado com sucesso.', array $headers = [])
    {
        return $this->success(null, $message, $headers);
    }
}</code></pre>
            </div>
        </section>

        <section id="camada-aplicacao" class="subsection">
            <h3>5.2. Camada de Aplicação</h3>

            <h4>5.2.1. Services</h4>
            <p>Nossos serviços estendem a classe <code>ServiceAbstract</code> e implementam a lógica de negócio
                específica de cada microsserviço:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Sistema\Usuarios\Services;

use App\Services\ServiceAbstract;
use App\Http\Sistema\Usuarios\Repositories\UsuarioRepository;
use App\Models\User;

class UsuarioService extends ServiceAbstract
{
    protected $createRules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:users,email',
        'password' => 'required|string|min:8|confirmed',
    ];

    public function __construct(UsuarioRepository $repository)
    {
        parent::__construct($repository);
    }

    // Métodos específicos do microsserviço de usuários...
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Classe Base: ServiceAbstract</h4>
                <p>A classe <code>ServiceAbstract</code> fornece funcionalidades como validação, criação, atualização e
                    exclusão de registros. Os serviços específicos estendem esta classe e implementam lógica de negócio
                    específica para cada entidade.</p>
            </div>

            <h4>5.2.2. DTOs (Data Transfer Objects)</h4>
            <p>Utilizamos DTOs para transferir dados estruturados entre camadas:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Sistema\Usuarios\DTOs;

readonly class UsuarioDTO
{
    public function __construct(
        public ?int $id = null,
        public string $name,
        public string $email,
        public ?string $password = null,
        public ?string $role = null
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'] ?? null,
            name: $data['name'],
            email: $data['email'],
            password: $data['password'] ?? null,
            role: $data['role'] ?? null
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'password' => $this->password,
            'role' => $this->role
        ];
    }
}</code></pre>
            </div>
        </section>

        <section id="camada-dominio" class="subsection">
            <h3>5.3. Camada de Domínio</h3>

            <h4>5.3.1. Models</h4>
            <p>Nossos models representam as entidades de negócio e seus relacionamentos. Podem ser compartilhados entre
                microsserviços ou específicos para um microsserviço:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Sistema\Usuarios\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class Usuario extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    // Campos pesquisáveis para filtros
    public $searchable = [
        'name',
        'email',
    ];

    // Relacionamentos
    public function posts()
    {
        return $this->hasMany(Post::class);
    }

    // Métodos de negócio
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    public function hasRole(string $role): bool
    {
        // Implementação da verificação de papel
        return true; // Simplificado para o exemplo
    }
}</code></pre>
            </div>

            <h4>5.3.2. Policies</h4>
            <p>Utilizamos policies para centralizar a lógica de autorização:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Sistema\Usuarios\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UsuarioPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    public function view(User $user, User $model): bool
    {
        return $user->id === $model->id || $user->isAdmin();
    }

    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    public function update(User $user, User $model): bool
    {
        return $user->id === $model->id || $user->isAdmin();
    }

    public function delete(User $user, User $model): bool
    {
        return $user->isAdmin() && $user->id !== $model->id;
    }

    public function restore(User $user): bool
    {
        return $user->isAdmin();
    }

    public function forceDelete(User $user, User $model): bool
    {
        return $user->isAdmin() && $user->id !== $model->id;
    }
}</code></pre>
            </div>
        </section>

        <section id="camada-infraestrutura" class="subsection">
            <h3>5.4. Camada de Infraestrutura</h3>

            <h4>5.4.1. Repositories</h4>
            <p>Nossos repositórios estendem a classe <code>RepositoryAbstract</code> e implementam o acesso aos dados
                específicos de cada microsserviço:</p>

            <div class="code-block">
                <pre><code>namespace App\Http\Sistema\Usuarios\Repositories;

use App\Repositories\RepositoryAbstract;
use App\Models\User;

class UsuarioRepository extends RepositoryAbstract
{
    public function __construct(User $model)
    {
        parent::__construct($model);
    }

    // Métodos específicos do microsserviço de usuários...
}</code></pre>
            </div>

            <div class="alerts-section">
                <h4>Classe Base: RepositoryAbstract</h4>
                <p>A classe <code>RepositoryAbstract</code> fornece métodos comuns para acesso a dados como find,
                    create, update, delete, etc. Os repositórios específicos estendem esta classe e implementam métodos
                    específicos para cada entidade.</p>
            </div>
        </section>
    </section>

    <section id="padroes" class="manual-section">
        <h2>6. Padrões de Codificação</h2>

        <p>Seguimos padrões de codificação consistentes para manter a qualidade e legibilidade do código.</p>

        <h3>6.1. PSR-12</h3>
        <p>O projeto segue o padrão PSR-12 para formatação de código PHP. Utilizamos o Laravel Pint para automatizar a
            formatação:</p>

        <div class="code-block">
            <pre><code># Verificar problemas de formatação
./vendor/bin/pint --test

# Corrigir problemas de formatação
./vendor/bin/pint</code></pre>
        </div>

        <h3>6.2. Convenções de Nomenclatura</h3>

        <table>
            <thead>
                <tr>
                    <th>Elemento</th>
                    <th>Convenção</th>
                    <th>Exemplo</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Classes</td>
                    <td>PascalCase</td>
                    <td>UserService, OrderController</td>
                </tr>
                <tr>
                    <td>Métodos</td>
                    <td>camelCase</td>
                    <td>getUsers(), createOrder()</td>
                </tr>
                <tr>
                    <td>Variáveis</td>
                    <td>camelCase</td>
                    <td>$orderItems, $totalPrice</td>
                </tr>
                <tr>
                    <td>Constantes</td>
                    <td>UPPER_SNAKE_CASE</td>
                    <td>MAX_ATTEMPTS, API_VERSION</td>
                </tr>
                <tr>
                    <td>Arquivos de Configuração</td>
                    <td>snake_case</td>
                    <td>app_settings.php, payment_gateways.php</td>
                </tr>
                <tr>
                    <td>Tabelas do Banco</td>
                    <td>snake_case, plural</td>
                    <td>users, order_items</td>
                </tr>
                <tr>
                    <td>Models</td>
                    <td>PascalCase, singular</td>
                    <td>User, OrderItem</td>
                </tr>
                <tr>
                    <td>Controllers</td>
                    <td>PascalCase, sufixo Controller</td>
                    <td>UserController, OrderController</td>
                </tr>
                <tr>
                    <td>Services</td>
                    <td>PascalCase, sufixo Service</td>
                    <td>UserService, OrderService</td>
                </tr>
                <tr>
                    <td>Repositories</td>
                    <td>PascalCase, sufixo Repository</td>
                    <td>UserRepository, OrderRepository</td>
                </tr>
                <tr>
                    <td>Interfaces</td>
                    <td>PascalCase, prefixo I ou sufixo Interface</td>
                    <td>IUserRepository, UserRepositoryInterface</td>
                </tr>
                <tr>
                    <td>Traits</td>
                    <td>PascalCase, sufixo Trait</td>
                    <td>HasMediaTrait, FilterableTrait</td>
                </tr>
                <tr>
                    <td>Microsserviços</td>
                    <td>PascalCase</td>
                    <td>Usuarios, Produtos, Pedidos</td>
                </tr>
            </tbody>
        </table>

        <h3>6.3. Documentação de Código</h3>
        <p>Todo código deve ser documentado seguindo o padrão PHPDoc:</p>

        <div class="code-block">
            <pre><code>/**
 * Processa um pedido e realiza o pagamento.
 *
 * @param int $orderId ID do pedido a ser processado
 * @param array $paymentData Dados do pagamento
 * @param bool $sendNotification Se deve enviar notificação ao cliente
 * @return bool Retorna true se o pagamento foi processado com sucesso
 * @throws PaymentException Se ocorrer um erro no processamento do pagamento
 */
public function processPayment(int $orderId, array $paymentData, bool $sendNotification = true): bool
{
    // Implementação
}</code></pre>
        </div>

        <h3>6.4. Princípios SOLID</h3>
        <p>Nosso código segue os princípios SOLID:</p>

        <ul>
            <li><strong>S</strong> - Single Responsibility Principle (Princípio da Responsabilidade Única)</li>
            <li><strong>O</strong> - Open/Closed Principle (Princípio Aberto/Fechado)</li>
            <li><strong>L</strong> - Liskov Substitution Principle (Princípio da Substituição de Liskov)</li>
            <li><strong>I</strong> - Interface Segregation Principle (Princípio da Segregação de Interface)</li>
            <li><strong>D</strong> - Dependency Inversion Principle (Princípio da Inversão de Dependência)</li>
        </ul>

        <div class="best-practice">
            <h4>Exemplo de Aplicação do Princípio da Responsabilidade Única</h4>
            <p>Cada microsserviço tem responsabilidades bem definidas e separadas em camadas (Controllers, Services,
                Repositories).</p>
        </div>
    </section>

    <section id="excecoes" class="manual-section">
        <h2>7. Tratamento de Exceções</h2>

        <p>O tratamento adequado de exceções é essencial para manter a robustez e a segurança da aplicação.</p>

        <h3>7.1. Hierarquia de Exceções</h3>
        <p>Utilizamos uma hierarquia de exceções personalizadas para facilitar o tratamento de erros:</p>

        <div class="code-block">
            <pre><code>namespace App\Exceptions;

use Exception;

class AppException extends Exception
{
    protected $errorCode;
    protected $errorData;

    public function __construct(string $message = "", int $code = 0, array $data = [])
    {
        parent::__construct($message, $code);
        $this->errorCode = $code;
        $this->errorData = $data;
    }

    public function getErrorCode(): int
    {
        return $this->errorCode;
    }

    public function getErrorData(): array
    {
        return $this->errorData;
    }
}

// Exceções específicas
class ValidationException extends AppException {}
class BusinessException extends AppException {}
class RepositoryException extends AppException {}</code></pre>
        </div>

        <h3>7.2. Handler Global</h3>
        <p>O Handler global de exceções é configurado para tratar diferentes tipos de exceções e retornar respostas
            apropriadas:</p>

        <div class="code-block">
            <pre><code>namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Validation\ValidationException;
use App\Responses\ApiResponse;
use Throwable;

class Handler extends ExceptionHandler
{
    // ...

    public function render($request, Throwable $exception)
    {
        if ($request->expectsJson()) {
            return $this->handleApiException($request, $exception);
        }

        return parent::render($request, $exception);
    }

    private function handleApiException($request, Throwable $exception)
    {
        $response = app(ApiResponse::class);

        if ($exception instanceof AuthenticationException) {
            return $response->authenticationError('Não autenticado.');
        }

        if ($exception instanceof ValidationException) {
            return $response->validationError($exception->errors());
        }

        if ($exception instanceof BusinessException) {
            return $response->businessError($exception->getMessage(), $exception->getErrorData());
        }

        // Log exceções não tratadas
        if (!config('app.debug')) {
            report($exception);
            return $response->serverError('Erro interno do servidor.');
        }

        return parent::render($request, $exception);
    }
}</code></pre>
        </div>

        <h3>7.3. Lançamento de Exceções</h3>
        <p>Ao lançar exceções, utilize as classes personalizadas e forneça mensagens claras:</p>

        <div class="code-block">
            <pre><code>namespace App\Http\Sistema\Pedidos\Services;

use App\Exceptions\BusinessException;
use App\Services\ServiceAbstract;

class PedidoService extends ServiceAbstract
{
    public function processarPedido(int $pedidoId)
    {
        $pedido = $this->repository->find($pedidoId);

        if (!$pedido) {
            throw new BusinessException('Pedido não encontrado.', 404);
        }

        if ($pedido->status === 'processado') {
            throw new BusinessException('Pedido já foi processado.', 400);
        }

        // Processar o pedido
        // ...
    }
}</code></pre>
        </div>

        <div class="alerts-section">
            <h4>Boas Práticas para Exceções</h4>
            <ul>
                <li>Use exceções específicas para diferentes tipos de erros</li>
                <li>Forneça mensagens de erro claras e informativas</li>
                <li>Não exponha detalhes técnicos ou sensíveis nas mensagens de erro</li>
                <li>Registre exceções não tratadas para análise posterior</li>
                <li>Trate exceções o mais próximo possível de onde ocorrem</li>
            </ul>
        </div>
    </section>

    <section id="testes" class="manual-section">
        <h2>8. Testes</h2>

        <p>Testes automatizados são essenciais para garantir a qualidade e a manutenibilidade do código. Nosso projeto
            utiliza PHPUnit para testes unitários e de feature, organizados de acordo com a estrutura de microsserviços.
        </p>

        <h3>8.1. Organização dos Testes</h3>
        <p>Os testes são organizados seguindo a estrutura de microsserviços:</p>

        <div class="code-block">
            <pre><code>tests/
├── Feature/
│   ├── Sistema/
│   │   ├── Micro1/
│   │   │   ├── Controllers/
│   │   │   ├── Services/
│   │   │   └── Repositories/
│   │   └── Micro2/
│   └── ...
└── Unit/
    ├── Sistema/
    │   ├── Micro1/
    │   │   ├── Services/
    │   │   └── Repositories/
    │   └── Micro2/
    └── ...</code></pre>
        </div>

        <h3>8.2. Testes Unitários</h3>
        <p>Os testes unitários verificam o comportamento de componentes individuais, como serviços e repositórios:</p>

        <div class="code-block">
            <pre><code>namespace Tests\Unit\Sistema\Usuarios\Services;

use App\Http\Sistema\Usuarios\Repositories\UsuarioRepository;
use App\Http\Sistema\Usuarios\Services\UsuarioService;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UsuarioServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $repository;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = Mockery::mock(UsuarioRepository::class);
        $this->service = new UsuarioService($this->repository);
    }

    public function test_create_user_with_valid_data()
    {
        // Arrange
        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ];

        $user = new User($userData);
        $user->id = 1;

        $this->repository->shouldReceive('create')
            ->once()
            ->with(Mockery::subset($userData))
            ->andReturn($user);

        // Act
        $result = $this->service->create($userData);

        // Assert
        $this->assertEquals($user, $result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}</code></pre>
        </div>

        <h3>8.3. Testes de Feature</h3>
        <p>Os testes de feature verificam o comportamento de endpoints da API:</p>

        <div class="code-block">
            <pre><code>namespace Tests\Feature\Sistema\Usuarios\Controllers;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class UsuarioControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        // Configuração
    }

    public function test_can_list_users()
    {
        // Arrange
        $admin = User::factory()->create(['role' => 'admin']);
        User::factory()->count(5)->create();
        Sanctum::actingAs($admin);

        // Act
        $response = $this->getJson('/api/usuarios');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => ['id', 'name', 'email', 'created_at']
                ],
                'meta' => ['total', 'per_page', 'current_page', 'last_page']
            ]);
    }

    public function test_can_create_user()
    {
        // Arrange
        $admin = User::factory()->create(['role' => 'admin']);
        Sanctum::actingAs($admin);

        $userData = [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ];

        // Act
        $response = $this->postJson('/api/usuarios', $userData);

        // Assert
        $response->assertStatus(201)
            ->assertJsonFragment([
                'name' => 'New User',
                'email' => '<EMAIL>'
            ]);

        $this->assertDatabaseHas('users', [
            'name' => 'New User',
            'email' => '<EMAIL>'
        ]);
    }
}</code></pre>
        </div>

        <h3>8.4. Mocks e Stubs</h3>
        <p>Utilizamos mocks e stubs para isolar componentes durante os testes:</p>

        <div class="code-block">
            <pre><code>namespace Tests\Unit\Sistema\Pedidos\Services;

use App\Http\Sistema\Pedidos\Repositories\PedidoRepository;
use App\Http\Sistema\Pedidos\Services\PedidoService;
use App\Http\Sistema\Produtos\Services\ProdutoService;
use App\Models\Pedido;
use App\Models\Produto;
use Mockery;
use Tests\TestCase;

class PedidoServiceTest extends TestCase
{
    protected $pedidoRepository;
    protected $produtoService;
    protected $pedidoService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->pedidoRepository = Mockery::mock(PedidoRepository::class);
        $this->produtoService = Mockery::mock(ProdutoService::class);
        $this->pedidoService = new PedidoService(
            $this->pedidoRepository,
            $this->produtoService
        );
    }

    public function test_criar_pedido_com_produto_disponivel()
    {
        // Arrange
        $produtoId = 1;
        $quantidade = 2;
        $preco = 100.00;

        $produto = new Produto();
        $produto->id = $produtoId;
        $produto->preco = $preco;
        $produto->estoque = 10;

        $pedidoData = [
            'produto_id' => $produtoId,
            'quantidade' => $quantidade,
            'cliente_id' => 1
        ];

        $pedido = new Pedido();
        $pedido->id = 1;
        $pedido->produto_id = $produtoId;
        $pedido->quantidade = $quantidade;
        $pedido->valor_total = $quantidade * $preco;

        // Expectations
        $this->produtoService->shouldReceive('findById')
            ->once()
            ->with($produtoId)
            ->andReturn($produto);

        $this->produtoService->shouldReceive('atualizarEstoque')
            ->once()
            ->with($produtoId, -$quantidade)
            ->andReturn(true);

        $this->pedidoRepository->shouldReceive('create')
            ->once()
            ->andReturn($pedido);

        // Act
        $result = $this->pedidoService->criarPedido($pedidoData);

        // Assert
        $this->assertEquals($pedido, $result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}</code></pre>
        </div>

        <div class="alerts-section">
            <h4>Cobertura de Testes</h4>
            <p>Recomendamos uma cobertura de testes de pelo menos 80% para o código de produção. Utilize o PHPUnit com
                o relatório de cobertura para verificar:</p>
            <pre><code>./vendor/bin/phpunit --coverage-html tests/coverage</code></pre>
        </div>
    </section>

    <section id="validacao" class="manual-section">
        <h2>9. Validação de Dados</h2>

        <p>A validação adequada dos dados de entrada é essencial para garantir a integridade e segurança da aplicação.
        </p>

        <h3>9.1. Form Requests</h3>
        <p>Utilizamos Form Requests para validação de dados de entrada nos controllers:</p>

        <div class="code-block">
            <pre><code>namespace App\Http\Sistema\Produtos\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProdutoStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create', Produto::class);
    }

    public function rules(): array
    {
        return [
            'nome' => 'required|string|max:255',
            'descricao' => 'nullable|string',
            'preco' => 'required|numeric|min:0',
            'categoria_id' => 'required|exists:categorias,id',
            'imagens' => 'sometimes|array',
            'imagens.*' => 'image|max:2048', // 2MB
            'tags' => 'sometimes|array',
            'tags.*' => 'string|max:50'
        ];
    }

    public function messages(): array
    {
        return [
            'nome.required' => 'O nome do produto é obrigatório',
            'preco.required' => 'O preço do produto é obrigatório',
            'preco.min' => 'O preço não pode ser negativo',
            'categoria_id.exists' => 'A categoria selecionada não existe',
            'imagens.*.image' => 'O arquivo deve ser uma imagem',
            'imagens.*.max' => 'A imagem não pode ter mais de 2MB'
        ];
    }
}</code></pre>
        </div>

        <h3>9.2. Validação nos Services</h3>
        <p>Os serviços também implementam validação para garantir a integridade dos dados:</p>

        <div class="code-block">
            <pre><code>namespace App\Http\Sistema\Produtos\Services;

use App\Services\ServiceAbstract;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ProdutoService extends ServiceAbstract
{
    protected $createRules = [
        'nome' => 'required|string|max:255',
        'preco' => 'required|numeric|min:0',
        'categoria_id' => 'required|exists:categorias,id'
    ];

    protected $updateRules = [
        'nome' => 'sometimes|string|max:255',
        'preco' => 'sometimes|numeric|min:0',
        'categoria_id' => 'sometimes|exists:categorias,id'
    ];

    public function create(array $data)
    {
        $validator = Validator::make($data, $this->createRules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Lógica adicional de validação
        if (isset($data['preco_promocional']) && $data['preco_promocional'] >= $data['preco']) {
            throw new ValidationException(
                $validator,
                response()->json([
                    'message' => 'O preço promocional deve ser menor que o preço regular',
                    'errors' => ['preco_promocional' => ['O preço promocional deve ser menor que o preço regular']]
                ])
            );
        }

        return $this->repository->create($data);
    }
}</code></pre>
        </div>

        <h3>9.3. Validação Personalizada</h3>
        <p>Para regras de validação complexas, criamos validadores personalizados:</p>

        <div class="code-block">
            <pre><code>namespace App\Http\Sistema\Financeiro\Rules;

use Illuminate\Contracts\Validation\Rule;

class CNPJ implements Rule
{
    public function passes($attribute, $value): bool
    {
        // Remove caracteres não numéricos
        $cnpj = preg_replace('/[^0-9]/', '', $value);

        // Verifica se tem 14 dígitos
        if (strlen($cnpj) != 14) {
            return false;
        }

        // Verifica se todos os dígitos são iguais
        if (preg_match('/(\d)\1{13}/', $cnpj)) {
            return false;
        }

        // Validação do primeiro dígito verificador
        $soma = 0;
        $multiplicador = 5;
        for ($i = 0; $i < 12; $i++) {
            $soma += $cnpj[$i] * $multiplicador;
            $multiplicador = ($multiplicador == 2) ? 9 : $multiplicador - 1;
        }
        $resto = $soma % 11;
        $dv1 = ($resto < 2) ? 0 : 11 - $resto;

        // Validação do segundo dígito verificador
        $soma = 0;
        $multiplicador = 6;
        for ($i = 0; $i < 13; $i++) {
            $soma += $cnpj[$i] * $multiplicador;
            $multiplicador = ($multiplicador == 2) ? 9 : $multiplicador - 1;
        }
        $resto = $soma % 11;
        $dv2 = ($resto < 2) ? 0 : 11 - $resto;

        // Verifica se os dígitos verificadores estão corretos
        return ($cnpj[12] == $dv1 && $cnpj[13] == $dv2);
    }

    public function message(): string
    {
        return 'O :attribute não é um CNPJ válido.';
    }
}</code></pre>
        </div>

        <div class="best-practice">
            <h4>Boas Práticas de Validação</h4>
            <ul>
                <li>Valide os dados o mais cedo possível no fluxo da aplicação</li>
                <li>Use Form Requests para validação nos controllers</li>
                <li>Implemente validação adicional nos serviços para regras de negócio complexas</li>
                <li>Forneça mensagens de erro claras e específicas</li>
                <li>Crie regras de validação personalizadas para lógicas complexas</li>
            </ul>
        </div>
    </section>

    <section id="versionamento" class="manual-section">
        <h2>10. Controle de Versão</h2>

        <p>O controle de versão adequado é essencial para o desenvolvimento colaborativo e a manutenção do código.</p>

        <h3>10.1. Padrão de Commits</h3>
        <p>Utilizamos o padrão Conventional Commits para mensagens de commit:</p>

        <div class="code-block">
            <pre><code># Formato
<tipo>[escopo opcional]: <descrição>

[corpo opcional]

[rodapé opcional]

# Exemplos
feat(usuarios): adiciona autenticação por dois fatores
fix(pedidos): corrige cálculo de frete para pedidos internacionais
docs: atualiza documentação da API
refactor(produtos): simplifica lógica de busca de produtos
test(auth): adiciona testes para autenticação OAuth</code></pre>
        </div>

        <p>Tipos de commit:</p>
        <ul>
            <li><strong>feat</strong>: Nova funcionalidade</li>
            <li><strong>fix</strong>: Correção de bug</li>
            <li><strong>docs</strong>: Alterações na documentação</li>
            <li><strong>style</strong>: Alterações que não afetam o significado do código (formatação, etc)</li>
            <li><strong>refactor</strong>: Refatoração de código</li>
            <li><strong>perf</strong>: Melhorias de performance</li>
            <li><strong>test</strong>: Adição ou correção de testes</li>
            <li><strong>build</strong>: Alterações no sistema de build ou dependências</li>
            <li><strong>ci</strong>: Alterações nos arquivos de CI</li>
            <li><strong>chore</strong>: Outras alterações que não modificam código de produção</li>
        </ul>

        <h3>10.2. Fluxo de Trabalho Git</h3>
        <p>Utilizamos o fluxo de trabalho Git Flow:</p>

        <div class="code-block">
            <pre><code># Branches principais
main     # Código em produção
develop  # Código em desenvolvimento

# Branches de suporte
feature/nome-da-feature  # Novas funcionalidades
bugfix/nome-do-bug       # Correções de bugs
release/versao           # Preparação para release
hotfix/nome-do-hotfix    # Correções urgentes em produção</code></pre>
        </div>

        <h3>10.3. Pull Requests</h3>
        <p>Todas as alterações devem ser submetidas através de Pull Requests:</p>

        <div class="best-practice">
            <h4>Template de Pull Request</h4>
            <pre><code># Descrição
Descreva brevemente as alterações realizadas.

## Tipo de alteração
- [ ] Nova funcionalidade
- [ ] Correção de bug
- [ ] Refatoração
- [ ] Documentação

## Checklist
- [ ] Testes unitários foram adicionados/atualizados
- [ ] A documentação foi atualizada
- [ ] O código segue os padrões do projeto
- [ ] Não há código comentado ou debug prints

## Screenshots (se aplicável)

## Notas adicionais</code></pre>
        </div>

        <div class="alerts-section">
            <h4>Code Review</h4>
            <p>Todos os Pull Requests devem passar por code review de pelo menos um outro desenvolvedor antes de serem
                mesclados.</p>
        </div>
    </section>

    <section id="boas-praticas" class="manual-section">
        <h2>11. Boas Práticas</h2>

        <p>Esta seção apresenta boas práticas gerais para o desenvolvimento no projeto.</p>

        <h3>11.1. Princípios Gerais</h3>
        <ul>
            <li><strong>DRY (Don't Repeat Yourself)</strong>: Evite duplicação de código</li>
            <li><strong>KISS (Keep It Simple, Stupid)</strong>: Mantenha o código simples e direto</li>
            <li><strong>YAGNI (You Aren't Gonna Need It)</strong>: Não implemente funcionalidades que não são
                necessárias
                no momento</li>
            <li><strong>Fail Fast</strong>: Detecte e reporte erros o mais cedo possível</li>
        </ul>

        <h3>11.2. Segurança</h3>
        <ul>
            <li>Sempre valide e sanitize dados de entrada</li>
            <li>Use prepared statements para consultas SQL</li>
            <li>Implemente autenticação e autorização adequadas</li>
            <li>Não armazene senhas em texto plano</li>
            <li>Proteja-se contra ataques comuns (XSS, CSRF, SQL Injection)</li>
            <li>Não exponha informações sensíveis em logs ou mensagens de erro</li>
        </ul>

        <h3>11.3. Performance</h3>
        <ul>
            <li>Use eager loading para evitar o problema N+1</li>
            <li>Implemente paginação para grandes conjuntos de dados</li>
            <li>Use índices adequados no banco de dados</li>
            <li>Utilize cache quando apropriado</li>
            <li>Otimize consultas ao banco de dados</li>
            <li>Minimize o número de requisições HTTP</li>
        </ul>

        <div class="code-block">
            <h4>Exemplo de Eager Loading</h4>
            <pre><code>// Ruim - Problema N+1
$pedidos = Pedido::all();
foreach ($pedidos as $pedido) {
    echo $pedido->cliente->nome; // Uma consulta para cada pedido
}

// Bom - Eager Loading
$pedidos = Pedido::with('cliente')->get();
foreach ($pedidos as $pedido) {
    echo $pedido->cliente->nome; // Dados já carregados
}</code></pre>
        </div>

        <h3>11.4. Manutenibilidade</h3>
        <ul>
            <li>Escreva código legível e auto-documentado</li>
            <li>Use nomes significativos para variáveis, métodos e classes</li>
            <li>Mantenha métodos e classes pequenos e focados</li>
            <li>Escreva testes automatizados</li>
            <li>Refatore regularmente</li>
            <li>Siga os padrões de codificação do projeto</li>
        </ul>

        <div class="best-practice">
            <h4>Exemplo de Código Legível</h4>
            <pre><code>// Ruim
function p($d, $t) {
    return $d * $t;
}

// Bom
function calcularPrecoTotal(float $precoUnitario, int $quantidade): float {
    return $precoUnitario * $quantidade;
}</code></pre>
        </div>
    </section>

    <section id="criacao-microsservicos" class="manual-section">
        <h2>12. Criação de Novos Microsserviços</h2>

        <p>Esta seção fornece um guia passo a passo para criar um novo microsserviço em nosso sistema.</p>

        <h3>12.1. Estrutura de Diretórios</h3>
        <p>Primeiro, crie a estrutura de diretórios para o novo microsserviço:</p>

        <div class="code-block">
            <pre><code># Criar a estrutura de diretórios
mkdir -p app/Http/Sistema/NovoMicro/Controllers
mkdir -p app/Http/Sistema/NovoMicro/Services
mkdir -p app/Http/Sistema/NovoMicro/Repositories
mkdir -p app/Http/Sistema/NovoMicro/Requests
mkdir -p tests/Feature/Sistema/NovoMicro
mkdir -p tests/Unit/Sistema/NovoMicro</code></pre>
        </div>

        <h3>12.2. Implementação dos Componentes</h3>
        <p>Implemente os componentes básicos do microsserviço:</p>

        <div class="code-block">
            <h4>Controller</h4>
            <pre><code>namespace App\Http\Sistema\NovoMicro\Controllers;

use App\Controllers\ControllerAbstract;
use App\Http\Sistema\NovoMicro\Services\NovoService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;

class NovoController extends ControllerAbstract
{
    protected $response;

    public function __construct(NovoService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    public function index(Request $request)
    {
        // Implementação
    }

    public function store(Request $request)
    {
        // Implementação
    }

    // Outros métodos...
}</code></pre>
        </div>

        <div class="code-block">
            <h4>Service</h4>
            <pre><code>namespace App\Http\Sistema\NovoMicro\Services;

use App\Services\ServiceAbstract;
use App\Http\Sistema\NovoMicro\Repositories\NovoRepository;

class NovoService extends ServiceAbstract
{
    protected $createRules = [
        // Regras de validação
    ];

    protected $updateRules = [
        // Regras de validação
    ];

    public function __construct(NovoRepository $repository)
    {
        parent::__construct($repository);
    }

    // Métodos específicos...
}</code></pre>
        </div>

        <div class="code-block">
            <h4>Repository</h4>
            <pre><code>namespace App\Http\Sistema\NovoMicro\Repositories;

use App\Repositories\RepositoryAbstract;
use App\Models\NovoModel;

class NovoRepository extends RepositoryAbstract
{
    public function __construct(NovoModel $model)
    {
        parent::__construct($model);
    }

    // Métodos específicos...
}</code></pre>
        </div>

        <div class="code-block">
            <h4>Request</h4>
            <pre><code>namespace App\Http\Sistema\NovoMicro\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NovoStoreRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Ou implemente lógica de autorização
    }

    public function rules()
    {
        return [
            // Regras de validação
        ];
    }

    public function messages()
    {
        return [
            // Mensagens de erro personalizadas
        ];
    }
}</code></pre>
        </div>

        <h3>12.3. Definição de Rotas</h3>
        <p>Defina as rotas para o novo microsserviço:</p>

        <div class="code-block">
            <pre><code>// routes/api.php
Route::prefix('api/novo-micro')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::apiResource('recursos', 'App\Http\Sistema\NovoMicro\Controllers\NovoController');
    });
});</code></pre>
        </div>

        <h3>12.4. Testes</h3>
        <p>Implemente testes para o novo microsserviço:</p>

        <div class="code-block">
            <pre><code>namespace Tests\Feature\Sistema\NovoMicro;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NovoControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        // Configuração
    }

    public function test_can_list_resources(): void
    {
        // Implementação do teste
    }

    // Outros testes...
}</code></pre>
        </div>

        <div class="alerts-section">
            <h4>Integração com o Sistema</h4>
            <p>Ao criar um novo microsserviço, certifique-se de que ele se integra adequadamente com os microsserviços
                existentes e segue os padrões arquiteturais do projeto.</p>
        </div>
    </section>

    <section id="laravel12" class="manual-section">
        <h2>13. Novidades do Laravel 12</h2>

        <p>O Laravel 12 traz várias melhorias e novos recursos que utilizamos em nosso projeto:</p>

        <h3>13.1. Principais Novidades</h3>
        <ul>
            <li><strong>PHP 8.2 Obrigatório</strong>: Aproveitamos os recursos do PHP 8.2 como readonly classes e enums
            </li>
            <li><strong>Controladores Anônimos</strong>: Utilizamos para rotas simples</li>
            <li><strong>Injeção de Dependência Aprimorada</strong>: Aproveitamos a injeção de dependência mais flexível
            </li>
            <li><strong>Melhorias no Eloquent</strong>: Utilizamos os novos recursos do ORM</li>
            <li><strong>Melhorias no Sistema de Filas</strong>: Implementamos processamento assíncrono mais eficiente
            </li>
        </ul>

        <h3>13.2. Controladores Anônimos</h3>
        <p>Exemplo de uso de controladores anônimos para rotas simples:</p>

        <div class="code-block">
            <pre><code>// routes/api.php
Route::get('/status', function () {
    return response()->json([
        'status' => 'online',
        'version' => config('app.version'),
        'environment' => app()->environment(),
        'timestamp' => now()->toIso8601String()
    ]);
});</code></pre>
        </div>

        <h3>13.3. Readonly Classes</h3>
        <p>Utilizamos readonly classes do PHP 8.2 para DTOs imutáveis:</p>

        <div class="code-block">
            <pre><code>namespace App\DTOs;

readonly class ProdutoDTO
{
    public function __construct(
        public int $id,
        public string $nome,
        public float $preco,
        public ?string $descricao = null
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'],
            nome: $data['nome'],
            preco: $data['preco'],
            descricao: $data['descricao'] ?? null
        );
    }
}</code></pre>
        </div>

        <h3>13.4. Enums</h3>
        <p>Utilizamos enums do PHP 8.1 para representar estados e tipos:</p>

        <div class="code-block">
            <pre><code>namespace App\Enums;

enum StatusPedido: string
{
    case PENDENTE = 'pendente';
    case PAGO = 'pago';
    case ENVIADO = 'enviado';
    case ENTREGUE = 'entregue';
    case CANCELADO = 'cancelado';

    public function label(): string
    {
        return match($this) {
            self::PENDENTE => 'Pendente',
            self::PAGO => 'Pago',
            self::ENVIADO => 'Enviado',
            self::ENTREGUE => 'Entregue',
            self::CANCELADO => 'Cancelado',
        };
    }

    public function isFinalized(): bool
    {
        return $this === self::ENTREGUE || $this === self::CANCELADO;
    }
}</code></pre>
        </div>

        <div class="best-practice">
            <h4>Uso de Enums no Eloquent</h4>
            <pre><code>namespace App\Models;

use App\Enums\StatusPedido;
use Illuminate\Database\Eloquent\Model;

class Pedido extends Model
{
    protected $fillable = [
        'cliente_id',
        'valor_total',
        'status',
    ];

    protected $casts = [
        'status' => StatusPedido::class,
    ];

    public function scopeFinalizados($query)
    {
        return $query->whereIn('status', [
            StatusPedido::ENTREGUE->value,
            StatusPedido::CANCELADO->value
        ]);
    }
}</code></pre>
        </div>

        <h3>13.5. Melhorias no Sistema de Filas</h3>
        <p>Exemplo de uso do sistema de filas aprimorado:</p>

        <div class="code-block">
            <pre><code>namespace App\Jobs;

use App\Models\Pedido;
use App\Services\NotificacaoService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessarPedido implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public readonly Pedido $pedido
    ) {}

    public function handle(NotificacaoService $notificacaoService): void
    {
        // Processar o pedido
        // ...

        // Enviar notificação
        $notificacaoService->enviarConfirmacao($this->pedido);
    }
}</code></pre>
        </div>

        <div class="alerts-section">
            <h4>Compatibilidade</h4>
            <p>Ao utilizar recursos específicos do PHP 8.2 e Laravel 12, certifique-se de que todos os desenvolvedores
                da equipe estão com seus ambientes atualizados para essas versões.</p>
        </div>
    </section>

    <footer class="manual-footer">
        <p>Manual de Implementação - Versão 1.0</p>
        <p>Última atualização: Agosto 2023</p>
    </footer>

    <script>
        // Script para destacar a seção atual no menu de navegação
        document.addEventListener('DOMContentLoaded', function () {
            const sections = document.querySelectorAll('.manual-section');
            const navLinks = document.querySelectorAll('.manual-nav a');

            window.addEventListener('scroll', function () {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (pageYOffset >= (sectionTop - 100)) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href').includes(current)) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>

</html>