<?php

namespace App\Swagger;

/**clear
 * @OA\Schema(
 *     schema="Error",
 *     title="Erro",
 *     description="Resposta de erro padrão",
 *     @OA\Property(property="success", type="boolean", example=false),
 *     @OA\Property(property="message", type="string", example="Mensagem de erro"),
 *     @OA\Property(property="errors", type="object", nullable=true)
 * )
 *
 * @OA\Response(
 *     response="BadRequest",
 *     description="Requisição inválida",
 *     @OA\JsonContent(
 *         @OA\Property(property="success", type="boolean", example=false),
 *         @OA\Property(property="message", type="string", example="Requisição inválida"),
 *         @OA\Property(property="errors", type="object", nullable=true)
 *     )
 * )
 *
 * @OA\Response(
 *     response="Unauthorized",
 *     description="Não autorizado",
 *     @OA\JsonContent(
 *         @OA\Property(property="success", type="boolean", example=false),
 *         @OA\Property(property="message", type="string", example="Não autorizado"),
 *         @OA\Property(property="errors", type="object", nullable=true)
 *     )
 * )
 *
 * @OA\Response(
 *     response="Forbidden",
 *     description="Acesso proibido",
 *     @OA\JsonContent(
 *         @OA\Property(property="success", type="boolean", example=false),
 *         @OA\Property(property="message", type="string", example="Acesso proibido"),
 *         @OA\Property(property="errors", type="object", nullable=true)
 *     )
 * )
 *
 * @OA\Response(
 *     response="NotFound",
 *     description="Recurso não encontrado",
 *     @OA\JsonContent(
 *         @OA\Property(property="success", type="boolean", example=false),
 *         @OA\Property(property="message", type="string", example="Recurso não encontrado"),
 *         @OA\Property(property="errors", type="object", nullable=true)
 *     )
 * )
 *
 * @OA\Response(
 *     response="ValidationError",
 *     description="Erro de validação",
 *     @OA\JsonContent(
 *         @OA\Property(property="success", type="boolean", example=false),
 *         @OA\Property(property="message", type="string", example="Erro de validação"),
 *         @OA\Property(
 *             property="errors",
 *             type="object",
 *             example={"email": {"O campo email é obrigatório."}}
 *         )
 *     )
 * )
 *
 * @OA\Response(
 *     response="ServerError",
 *     description="Erro interno do servidor",
 *     @OA\JsonContent(
 *         @OA\Property(property="success", type="boolean", example=false),
 *         @OA\Property(property="message", type="string", example="Erro interno do servidor"),
 *         @OA\Property(property="errors", type="object", nullable=true)
 *     )
 * )
 *
 * @OA\Schema(
 *     schema="UserModel",
 *     title="User Model",
 *     description="Modelo completo de usuário",
 *     @OA\Property(property="id", type="integer", format="int64", example=1),
 *     @OA\Property(property="name", type="string", example="João Silva"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="email_verified_at", type="string", format="date-time", nullable=true),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T00:00:00.000000Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2023-01-01T00:00:00.000000Z")
 * )
 *
 * @OA\Schema(
 *     schema="ExampleModel",
 *     title="Example Model",
 *     description="Modelo de exemplo",
 *     @OA\Property(property="id", type="integer", format="int64", example=1),
 *     @OA\Property(property="title", type="string", example="Título do exemplo"),
 *     @OA\Property(property="description", type="string", example="Descrição do exemplo"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T00:00:00.000000Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2023-01-01T00:00:00.000000Z")
 * )
 */
class Components
{
    // Esta classe serve apenas como contêiner para anotações OpenAPI
}
