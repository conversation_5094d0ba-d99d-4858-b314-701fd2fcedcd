<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Logging - Laravel 12</title>
    <link rel="stylesheet" href="css/manual.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Logging</h1>
        <p>Guia para implementação e padronização de logs na aplicação Laravel 12</p>
        <p class="version">Versão 2.0 - Última atualização: Maio 2023</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#niveis">Níveis de Log</a></li>
            <li><a href="#estrutura">Estrutura de Logs</a></li>
            <li><a href="#configuracao">Configuração</a></li>
            <li><a href="#implementacao">Implementação</a></li>
            <li><a href="#monitoramento">Monitoramento</a></li>
            <li><a href="#laravel12">Laravel 12</a></li>
            <li><a href="#boas-praticas">Boas Práticas</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#niveis">2. Níveis de Log</a></li>
            <li><a href="#estrutura">3. Estrutura de Logs</a>
                <ul>
                    <li><a href="#estrutura-basica">3.1. Estrutura Básica</a></li>
                    <li><a href="#contexto">3.2. Informações de Contexto</a></li>
                    <li><a href="#formato">3.3. Formato de Mensagens</a></li>
                </ul>
            </li>
            <li><a href="#configuracao">4. Configuração do Sistema de Logs</a>
                <ul>
                    <li><a href="#config-laravel">4.1. Configuração no Laravel</a></li>
                    <li><a href="#config-canais">4.2. Canais de Log</a></li>
                    <li><a href="#config-env">4.3. Configurações por Ambiente</a></li>
                </ul>
            </li>
            <li><a href="#implementacao">5. Implementação</a>
                <ul>
                    <li><a href="#impl-facade">5.1. Uso da Facade Log</a></li>
                    <li><a href="#impl-custom-channel">5.2. Canais Personalizados</a></li>
                    <li><a href="#impl-contextual">5.3. Logging Contextual</a></li>
                    <li><a href="#impl-excecoes">5.4. Logging de Exceções</a></li>
                </ul>
            </li>
            <li><a href="#monitoramento">6. Monitoramento e Alertas</a></li>
            <li><a href="#seguranca">7. Segurança e Privacidade</a></li>
            <li><a href="#performance">8. Considerações de Performance</a></li>
            <li><a href="#laravel12">9. Recursos do Laravel 12</a>
                <ul>
                    <li><a href="#laravel12-structured">9.1. Structured Logging</a></li>
                    <li><a href="#laravel12-context">9.2. Context Enrichers</a></li>
                    <li><a href="#laravel12-channels">9.3. Canais Aprimorados</a></li>
                    <li><a href="#laravel12-opentelemetry">9.4. Integração com OpenTelemetry</a></li>
                    <li><a href="#laravel12-typed">9.5. Tipagem Estrita</a></li>
                </ul>
            </li>
            <li><a href="#boas-praticas">10. Boas Práticas</a></li>
            <li><a href="#exemplos">11. Exemplos Práticos</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>
        <div class="intro-text">
            <p>O sistema de logging é uma parte crucial de qualquer aplicação em produção, fornecendo visibilidade sobre
                o comportamento da aplicação, facilitando a detecção e resolução de problemas, e contribuindo para a
                segurança e auditoria do sistema.</p>

            <p>Este manual estabelece os padrões e práticas recomendadas para implementação de logs na nossa aplicação
                Laravel 12, garantindo que as informações registradas sejam consistentes, úteis e adequadamente
                estruturadas.</p>
        </div>

        <div class="note">
            <p><strong>Nota:</strong> Este documento é complementar ao Manual de Arquitetura e ao Manual de
                Implementação. As diretrizes aqui apresentadas devem ser seguidas por todos os componentes da aplicação.
            </p>
        </div>

        <h3>Objetivos do Logging</h3>
        <div class="key-points">
            <ul>
                <li><strong>Diagnóstico:</strong> Identificar e resolver problemas e erros</li>
                <li><strong>Monitoramento:</strong> Acompanhar o comportamento da aplicação em tempo real</li>
                <li><strong>Auditoria:</strong> Manter um registro de ações importantes para fins de segurança e
                    compliance</li>
                <li><strong>Análise:</strong> Possibilitar a análise de tendências e comportamentos para melhorias</li>
                <li><strong>Depuração:</strong> Auxiliar no processo de desenvolvimento e testes</li>
                <li><strong>Observabilidade:</strong> Fornecer dados para sistemas de observabilidade integrados</li>
            </ul>
        </div>

        <div class="alerts-section">
            <h4>Novidades no Laravel 12</h4>
            <p>O Laravel 12 introduz melhorias significativas no sistema de logging, incluindo suporte aprimorado para
                structured logging, integração com OpenTelemetry, context enrichers e tipagem estrita. Este manual foi
                atualizado para refletir essas novas funcionalidades.</p>
        </div>
    </section>

    <section id="niveis" class="manual-section">
        <h2>2. Níveis de Log</h2>
        <p>Utilizamos diferentes níveis de log para categorizar as mensagens de acordo com sua severidade e importância.
        </p>

        <div class="comparison-table">
            <table>
                <thead>
                    <tr>
                        <th>Nível</th>
                        <th>Descrição</th>
                        <th>Uso</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>EMERGENCY</td>
                        <td>Sistema inutilizável</td>
                        <td>Falhas críticas que tornam a aplicação completamente inoperante (ex: banco de dados
                            inacessível)</td>
                    </tr>
                    <tr>
                        <td>ALERT</td>
                        <td>Ação imediata necessária</td>
                        <td>Problemas graves que requerem intervenção imediata (ex: memória da aplicação quase esgotada)
                        </td>
                    </tr>
                    <tr>
                        <td>CRITICAL</td>
                        <td>Condições críticas</td>
                        <td>Componentes críticos da aplicação com falhas severas (ex: falha em serviço essencial)</td>
                    </tr>
                    <tr>
                        <td>ERROR</td>
                        <td>Condições de erro</td>
                        <td>Erros de runtime que não requerem ação imediata, mas devem ser investigados (ex: exceções
                            tratadas)</td>
                    </tr>
                    <tr>
                        <td>WARNING</td>
                        <td>Condições de alerta</td>
                        <td>Situações excepcionais que não são erros, mas podem indicar problemas potenciais (ex:
                            tentativa de login inválida)</td>
                    </tr>
                    <tr>
                        <td>NOTICE</td>
                        <td>Eventos normais significativos</td>
                        <td>Eventos normais mas significativos (ex: operações administrativas, usuário criado)</td>
                    </tr>
                    <tr>
                        <td>INFO</td>
                        <td>Mensagens informativas</td>
                        <td>Eventos normais do fluxo de execução (ex: início e fim de processos importantes)</td>
                    </tr>
                    <tr>
                        <td>DEBUG</td>
                        <td>Informações detalhadas de debug</td>
                        <td>Informações detalhadas para depuração, normalmente não ativadas em produção</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="best-practice">
            <h4>Escolhendo o Nível Adequado:</h4>
            <p>Selecione o nível de log com base no impacto do evento:</p>
            <ul>
                <li>Os níveis <code>EMERGENCY</code>, <code>ALERT</code> e <code>CRITICAL</code> devem ser reservados
                    para eventos que requerem ação imediata</li>
                <li>Use <code>ERROR</code> para eventos que representam falhas que precisam ser corrigidas, mas não
                    impedem a operação total da aplicação</li>
                <li>Use <code>WARNING</code> para situações potencialmente problemáticas que não são erros atualmente
                </li>
                <li>Use <code>NOTICE</code> e <code>INFO</code> para eventos operacionais importantes</li>
                <li>Use <code>DEBUG</code> apenas para informações detalhadas úteis durante o desenvolvimento</li>
            </ul>
        </div>

        <div class="tip">
            <h4>Dica</h4>
            <p>No Laravel 12, você pode usar enums para representar níveis de log, tornando o código mais expressivo e
                seguro:</p>
            <div class="code-block">
                <pre>
enum LogLevel: string
{
    case EMERGENCY = 'emergency';
    case ALERT = 'alert';
    case CRITICAL = 'critical';
    case ERROR = 'error';
    case WARNING = 'warning';
    case NOTICE = 'notice';
    case INFO = 'info';
    case DEBUG = 'debug';
}

// Uso
Log::log(LogLevel::ERROR->value, 'Mensagem de erro');
                </pre>
            </div>
        </div>
    </section>

    <section id="estrutura" class="manual-section">
        <h2>3. Estrutura de Logs</h2>
        <p>Para maximizar a utilidade dos logs, todas as entradas de log devem seguir uma estrutura padronizada e
            consistente.</p>

        <section id="estrutura-basica" class="subsection">
            <h3>3.1. Estrutura Básica</h3>
            <p>Cada entrada de log deve conter as seguintes informações básicas:</p>

            <div class="code-block">
                <h4>Formato JSON Estruturado</h4>
                <pre>
{
    "timestamp": "2023-09-01T10:15:30.123456Z", // Data e hora no formato ISO 8601
    "level": "ERROR", // Nível do log
    "message": "Falha ao processar pagamento", // Mensagem descritiva
    "context": { // Informações de contexto
        "request_id": "req-123456789",
        "user_id": 1001,
        "order_id": 5002,
        "error": "Gateway de pagamento indisponível"
    },
    "trace_id": "0af7651916cd43dd8448eb211c80319c", // ID de rastreamento para correlação
    "span_id": "b7ad6b7169203331", // ID de span para rastreamento detalhado
    "service": "payment-service", // Nome do serviço ou componente
    "environment": "production" // Ambiente de execução
}
                </pre>
            </div>

            <div class="tip">
                <h4>Novidade no Laravel 12</h4>
                <p>O Laravel 12 aprimorou o suporte para structured logging, permitindo que os logs sejam facilmente
                    processados por ferramentas de análise como Elasticsearch, Splunk ou CloudWatch Logs.</p>
            </div>
        </section>

        <section id="contexto" class="subsection">
            <h3>3.2. Informações de Contexto</h3>
            <p>Adicione informações de contexto relevantes para facilitar a correlação de eventos e diagnóstico de
                problemas:</p>

            <ul>
                <li><strong>Identificadores:</strong> IDs de usuário, sessão, requisição, transação, etc.</li>
                <li><strong>Origem:</strong> Módulo, classe, método ou serviço que gerou o log</li>
                <li><strong>Dados Relevantes:</strong> Parâmetros, valores ou estados importantes para entender o
                    contexto</li>
                <li><strong>Informações de Rastreamento:</strong> Trace IDs para correlacionar logs em sistemas
                    distribuídos</li>
            </ul>

            <div class="example-commits">
                <h4>Exemplo de Contexto em Diferentes Cenários</h4>
                <pre>
// Log de autenticação
Log::info('Tentativa de login bem-sucedida', [
    'user_id' => $user->id,
    'email' => $user->email,
    'ip_address' => $request->ip(),
    'user_agent' => $request->userAgent()
]);

// Log de transação
Log::info('Nova compra realizada', [
    'order_id' => $order->id,
    'user_id' => $order->user_id,
    'total_amount' => $order->total,
    'payment_method' => $order->payment_method,
    'items_count' => $order->items->count()
]);

// Log de erro em API externa
Log::error('Falha ao conectar com serviço externo', [
    'service' => 'payment_gateway',
    'endpoint' => '/api/v1/transactions',
    'method' => 'POST',
    'status_code' => $response->status(),
    'response' => $response->body(),
    'request_id' => $requestId
]);
                </pre>
            </div>

            <div class="best-practice">
                <h4>Context Enrichers no Laravel 12</h4>
                <p>O Laravel 12 introduz Context Enrichers, que permitem adicionar automaticamente informações de
                    contexto a todos os logs:</p>
                <pre>
// Em um Service Provider
public function boot(): void
{
    Log::enrichContext(function (array $context): array {
        return array_merge($context, [
            'app_version' => config('app.version'),
            'environment' => app()->environment(),
            'request_id' => request()->header('X-Request-ID') ?? (string) Str::uuid(),
            'user_id' => auth()->id() ?? 'guest',
        ]);
    });
}
                </pre>
            </div>
        </section>

        <section id="formato" class="subsection">
            <h3>3.3. Formato de Mensagens</h3>
            <p>As mensagens de log devem seguir estas diretrizes de formatação:</p>

            <ul>
                <li><strong>Clareza:</strong> A mensagem deve ser clara e auto-explicativa</li>
                <li><strong>Concisão:</strong> Evite mensagens muito longas, coloque detalhes no contexto</li>
                <li><strong>Consistência:</strong> Mantenha um padrão de formatação entre mensagens similares</li>
                <li><strong>Acionabilidade:</strong> A mensagem deve indicar claramente o que aconteceu</li>
            </ul>

            <div class="best-practice">
                <h4>Padrões para Formatação de Mensagens</h4>
                <ul>
                    <li>Use frases completas com inicial maiúscula</li>
                    <li>Para eventos, prefira voz passiva: "Pedido criado" em vez de "Criou pedido"</li>
                    <li>Para erros, seja descritivo sobre o que falhou: "Falha ao processar pagamento" em vez de "Erro
                        de pagamento"</li>
                    <li>Não inclua dados variáveis na mensagem principal, coloque-os no contexto</li>
                </ul>
            </div>

            <div class="bad-practice">
                <h4>Exemplos de Mensagens Ruins</h4>
                <pre>
// Muito genérica
Log::error('Erro');

// Dados variáveis na mensagem (em vez de no contexto)
Log::info('Usuário 1001 fez login às 14:30');

// Inconsistente e difícil de filtrar
Log::warning('Problema!!!!! Não conseguiu fazer o pagamento do pedido 12345');
                </pre>
            </div>

            <div class="best-practice">
                <h4>Exemplos de Mensagens Boas</h4>
                <pre>
// Clara e consistente, com dados no contexto
Log::error('Falha ao processar pagamento', [
    'order_id' => 12345,
    'payment_method' => 'credit_card',
    'error_code' => 'GATEWAY_TIMEOUT'
]);

// Mensagem acionável
Log::warning('Tentativa de acesso a recurso não autorizado', [
    'user_id' => $user->id,
    'resource' => 'admin_panel',
    'ip_address' => $request->ip()
]);

// Clara e concisa
Log::info('Sessão de usuário iniciada', [
    'user_id' => $user->id,
    'email' => $user->email
]);
                </pre>
            </div>
        </section>
    </section>

    <section id="configuracao" class="manual-section">
        <h2>4. Configuração do Sistema de Logs</h2>
        <p>A configuração adequada do sistema de logs é essencial para garantir que as informações certas sejam
            registradas e armazenadas de forma apropriada.</p>

        <section id="config-laravel" class="subsection">
            <h3>4.1. Configuração no Laravel</h3>
            <p>O Laravel 12 oferece um sistema de logging robusto e configurável através do arquivo
                <code>config/logging.php</code>.
            </p>

            <div class="code-block">
                <h4>config/logging.php</h4>
                <pre>
return [
    'default' => env('LOG_CHANNEL', 'stack'),

    'deprecations' => [
        'channel' => env('LOG_DEPRECATIONS_CHANNEL', 'null'),
        'trace' => env('LOG_DEPRECATIONS_TRACE', false),
    ],

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['daily', 'slack'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'replace_placeholders' => true,
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
            'replace_placeholders' => true,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => env('LOG_SLACK_LEVEL', 'critical'),
            'replace_placeholders' => true,
        ],

        'opentelemetry' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => \App\Logging\OpenTelemetryLogHandler::class,
        ],

        'structured' => [
            'driver' => 'custom',
            'via' => \App\Logging\CreateStructuredLogger::class,
            'path' => storage_path('logs/structured.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        // Outros canais como 'papertrail', 'syslog', etc.
    ],
];
                </pre>
            </div>

            <div class="tip">
                <h4>Novidade no Laravel 12</h4>
                <p>O Laravel 12 introduz suporte aprimorado para configuração de logs estruturados e integração com
                    sistemas de observabilidade como OpenTelemetry.</p>
            </div>
        </section>

        <section id="config-canais" class="subsection">
            <h3>4.2. Canais de Log</h3>
            <p>Utilizamos diferentes canais de log para diferentes propósitos:</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Canal</th>
                            <th>Uso</th>
                            <th>Configuração</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>daily</td>
                            <td>Logs gerais da aplicação, rotacionados diariamente</td>
                            <td>Retenção de 14 dias, nível debug em desenvolvimento, info em produção</td>
                        </tr>
                        <tr>
                            <td>slack</td>
                            <td>Alertas para erros críticos</td>
                            <td>Apenas níveis critical e acima são enviados para o Slack</td>
                        </tr>
                        <tr>
                            <td>audit</td>
                            <td>Log específico para eventos de auditoria</td>
                            <td>Canal separado para facilitar consulta e retenção prolongada</td>
                        </tr>
                        <tr>
                            <td>transactions</td>
                            <td>Logs específicos de transações financeiras</td>
                            <td>Canal separado com retenção estendida para rastreabilidade</td>
                        </tr>
                        <tr>
                            <td>security</td>
                            <td>Eventos relacionados à segurança</td>
                            <td>Canal com alertas configurados e retenção prolongada</td>
                        </tr>
                        <tr>
                            <td>opentelemetry</td>
                            <td>Integração com sistemas de observabilidade</td>
                            <td>Exporta logs para sistemas compatíveis com OpenTelemetry</td>
                        </tr>
                        <tr>
                            <td>structured</td>
                            <td>Logs em formato JSON estruturado</td>
                            <td>Facilita a integração com ferramentas de análise de logs</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="code-block">
                <h4>Exemplo de Configuração para Canais Personalizados</h4>
                <pre>
'audit' => [
    'driver' => 'daily',
    'path' => storage_path('logs/audit.log'),
    'level' => 'info',
    'days' => 365,
    'replace_placeholders' => true,
],

'transactions' => [
    'driver' => 'daily',
    'path' => storage_path('logs/transactions.log'),
    'level' => 'info',
    'days' => 90,
    'replace_placeholders' => true,
],

'security' => [
    'driver' => 'stack',
    'channels' => ['security_file', 'slack'],
    'ignore_exceptions' => false,
],

'security_file' => [
    'driver' => 'daily',
    'path' => storage_path('logs/security.log'),
    'level' => 'debug',
    'days' => 90,
    'replace_placeholders' => true,
],
                </pre>
            </div>
        </section>

        <section id="config-env" class="subsection">
            <h3>4.3. Configurações por Ambiente</h3>
            <p>As configurações de log devem ser ajustadas de acordo com o ambiente:</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Ambiente</th>
                            <th>Nível de Log</th>
                            <th>Canais</th>
                            <th>Considerações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>local</td>
                            <td>debug</td>
                            <td>single ou stderr</td>
                            <td>Máxima verbosidade para facilitar desenvolvimento</td>
                        </tr>
                        <tr>
                            <td>testing</td>
                            <td>debug</td>
                            <td>null ou stderr</td>
                            <td>Logs desabilitados por padrão ou redirecionados para stderr</td>
                        </tr>
                        <tr>
                            <td>staging</td>
                            <td>debug ou info</td>
                            <td>daily, structured, opentelemetry</td>
                            <td>Configuração similar à produção, mas com mais verbosidade para testes</td>
                        </tr>
                        <tr>
                            <td>production</td>
                            <td>info</td>
                            <td>stack (daily + structured + opentelemetry + alertas)</td>
                            <td>Foco em performance, apenas logs relevantes, integração com sistema de monitoramento
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="note">
                <p>As configurações específicas para cada ambiente são definidas através de variáveis de ambiente no
                    arquivo <code>.env</code> e valores padrão adequados no arquivo <code>config/logging.php</code>.</p>
            </div>
        </section>
    </section>

    <section id="implementacao" class="manual-section">
        <h2>5. Implementação</h2>
        <p>A implementação adequada do sistema de logs garante que todas as partes da aplicação registrem informações de
            maneira consistente.</p>

        <section id="impl-facade" class="subsection">
            <h3>5.1. Uso da Facade Log</h3>
            <p>A forma mais comum de registrar logs no Laravel é através da facade <code>Log</code>:</p>

            <div class="code-block">
                <h4>Exemplos de Uso da Facade Log</h4>
                <pre>
use Illuminate\Support\Facades\Log;

// Logs básicos
Log::emergency('Sistema completamente indisponível');
Log::alert('Banco de dados está quase sem espaço livre');
Log::critical('Serviço de pagamento não está respondendo');
Log::error('Falha ao processar pedido');
Log::warning('Usuário com permissões insuficientes tentou acessar recurso');
Log::notice('Usuário alterou configurações importantes');
Log::info('Pedido #12345 foi finalizado');
Log::debug('Variáveis de estado: ' . json_encode($debugData));

// Log com informações de contexto
Log::info('Novo usuário registrado', [
    'id' => $user->id,
    'email' => $user->email,
    'registration_method' => 'email',
]);

// Especificando o canal
Log::channel('audit')->info('Administrador alterou permissões de usuário', [
    'admin_id' => $admin->id,
    'user_id' => $user->id,
    'old_permissions' => $oldPermissions,
    'new_permissions' => $newPermissions
]);

// Usando stack de canais
Log::stack(['daily', 'slack'])->critical('Tentativa de invasão detectada', [
    'ip_address' => $request->ip(),
    'user_agent' => $request->userAgent(),
    'endpoint' => $request->fullUrl(),
    'payload' => $request->all()
]);
                </pre>
            </div>

            <div class="tip">
                <h4>Novidade no Laravel 12</h4>
                <p>O Laravel 12 introduz tipagem estrita para os métodos de log, melhorando a segurança e a clareza do
                    código:</p>
                <pre>
// Métodos com tipagem estrita
public function emergency(string $message, array $context = []): void;
public function alert(string $message, array $context = []): void;
public function critical(string $message, array $context = []): void;
public function error(string $message, array $context = []): void;
public function warning(string $message, array $context = []): void;
public function notice(string $message, array $context = []): void;
public function info(string $message, array $context = []): void;
public function debug(string $message, array $context = []): void;
                </pre>
            </div>
        </section>

        <section id="impl-custom-channel" class="subsection">
            <h3>5.2. Canais Personalizados</h3>
            <p>Para casos específicos, podemos criar canais de log personalizados:</p>

            <div class="code-block">
                <h4>Criação de Canal Personalizado</h4>
                <pre>
// Em um Service Provider
public function boot(): void
{
    // Registrar um canal customizado
    $this->app->make('log')->extend('cloudwatch', function ($app, array $config) {
        return new \Monolog\Logger('cloudwatch', [
            new \App\Logging\CloudWatchHandler($config),
        ]);
    });
}

// Uso do canal personalizado em config/logging.php
'channels' => [
    // ...
    'cloudwatch' => [
        'driver' => 'cloudwatch',
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
        'group' => env('CLOUDWATCH_LOG_GROUP', 'laravel'),
        'stream' => env('CLOUDWATCH_LOG_STREAM', 'application'),
        'retention' => env('CLOUDWATCH_LOG_RETENTION', 14),
        'level' => env('LOG_LEVEL', 'debug'),
    ],
]
        </pre>
            </div>

            <div class="code-block">
                <h4>Implementação de um Handler Personalizado</h4>
                <pre>
namespace App\Logging;

use Aws\CloudWatchLogs\CloudWatchLogsClient;
use Monolog\Handler\AbstractProcessingHandler;
use Monolog\LogRecord;

class CloudWatchHandler extends AbstractProcessingHandler
{
    protected CloudWatchLogsClient $client;
    protected string $group;
    protected string $stream;
    
    public function __construct(array $config, $level = 'debug', bool $bubble = true)
    {
        $this->client = new CloudWatchLogsClient([
            'version' => 'latest',
            'region' => $config['region'] ?? 'us-east-1',
            'credentials' => [
                'key' => $config['key'] ?? env('AWS_ACCESS_KEY_ID'),
                'secret' => $config['secret'] ?? env('AWS_SECRET_ACCESS_KEY'),
            ],
        ]);
        
        $this->group = $config['group'] ?? 'laravel';
        $this->stream = $config['stream'] ?? 'application';
        
        parent::__construct($level, $bubble);
    }
    
    protected function write(LogRecord $record): void
    {
        // Garantir que o grupo de logs existe
        try {
            $this->client->createLogGroup([
                'logGroupName' => $this->group,
            ]);
        } catch (\Exception $e) {
            // Grupo já existe, ignorar erro
        }
        
        // Garantir que o stream existe
        try {
            $this->client->createLogStream([
                'logGroupName' => $this->group,
                'logStreamName' => $this->stream,
            ]);
        } catch (\Exception $e) {
            // Stream já existe, ignorar erro
        }
        
        // Enviar o evento de log
        $this->client->putLogEvents([
            'logGroupName' => $this->group,
            'logStreamName' => $this->stream,
            'logEvents' => [
                [
                    'timestamp' => $record->datetime->format('U.u') * 1000,
                    'message' => $this->formatter->format($record),
                ],
            ],
        ]);
    }
}
        </pre>
            </div>

            <div class="tip">
                <h4>Novidade no Laravel 12</h4>
                <p>O Laravel 12 introduz suporte aprimorado para tipagem estrita em handlers personalizados, usando a
                    nova classe <code>LogRecord</code> do Monolog 3.x:</p>
                <pre>
// Monolog 3.x com tipagem estrita
protected function write(LogRecord $record): void
{
    // Implementação
}

// Em vez do formato antigo do Monolog 2.x
protected function write(array $record): void
{
    // Implementação antiga
}
        </pre>
            </div>

            <div class="best-practice">
                <h4>Criando um Canal Estruturado Personalizado</h4>
                <p>No Laravel 12, você pode criar facilmente um canal de log estruturado que produz saída em formato
                    JSON:</p>
                <pre>
namespace App\Logging;

use Monolog\Formatter\JsonFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class CreateStructuredLogger
{
    public function __invoke(array $config): Logger
    {
        $handler = new StreamHandler(
            $config['path'] ?? storage_path('logs/structured.log'),
            $config['level'] ?? 'debug',
            true,
            $config['permission'] ?? 0644,
            $config['bubble'] ?? true
        );
        
        // Usar o JsonFormatter para saída estruturada
        $formatter = new JsonFormatter();
        $formatter->includeStacktraces(true);
        
        $handler->setFormatter($formatter);
        
        return new Logger(
            $config['name'] ?? config('app.name'),
            [$handler]
        );
    }
}
        </pre>
            </div>

            <div class="example-commits">
                <h4>Exemplo de Uso de Canal Personalizado</h4>
                <pre>
// Registrar log em um canal personalizado
Log::channel('cloudwatch')->info('Operação de alto valor realizada', [
    'user_id' => $user->id,
    'operation' => 'funds_transfer',
    'amount' => $amount,
    'destination' => $destination,
    'transaction_id' => $transactionId
]);

// Usar um canal personalizado temporariamente
Log::stack(['daily', 'cloudwatch'])->critical('Falha crítica no sistema de pagamentos', [
    'service' => 'payment_processor',
    'error_code' => $errorCode,
    'affected_transactions' => $affectedTransactions
]);

// Usar canal estruturado para logs de auditoria
Log::channel('structured')->notice('Alteração de permissões de usuário', [
    'admin_id' => $adminUser->id,
    'target_user_id' => $targetUser->id,
    'old_roles' => $oldRoles,
    'new_roles' => $newRoles,
    'ip_address' => request()->ip()
]);

// Usar canal OpenTelemetry para rastreamento distribuído
Log::channel('opentelemetry')->info('Processamento de pedido iniciado', [
    'order_id' => $order->id,
    'trace_id' => $traceId,
    'span_id' => $spanId,
    'parent_id' => $parentSpanId
]);
    </pre>
            </div>

            <div class="tip">
                <h4>Canais Condicionais no Laravel 12</h4>
                <p>O Laravel 12 permite selecionar canais de log condicionalmente com base em critérios específicos:</p>
                <pre>
// Em um Service Provider
public function boot(): void
{
    // Registrar um seletor de canal condicional
    $this->app->make('log')->channelSelector(function (string $channel) {
        // Enviar logs de segurança para canais específicos em produção
        if ($channel === 'security' && app()->environment('production')) {
            return ['security_file', 'slack', 'monitoring'];
        }
        
        // Enviar logs de transações financeiras para canais específicos
        if ($channel === 'transactions' && app()->environment('production')) {
            return ['transactions_file', 'cloudwatch'];
        }
        
        return $channel;
    });
}

// Uso
Log::channel('security')->warning('Tentativa de acesso não autorizado detectada');
    </pre>
            </div>
        </section>

        <section id="impl-contextual" class="subsection">
            <h3>5.3. Logging Contextual</h3>
            <p>Para enriquecer logs com informações de contexto consistentes, utilize os recursos de contexto do Laravel
                12:</p>

            <div class="code-block">
                <h4>Adicionando Contexto Global</h4>
                <pre>
// Em um Service Provider
public function boot(): void
{
    // Adicionar contexto que será incluído em todos os logs
    Log::withContext([
        'request_id' => (string) Str::uuid(),
        'environment' => app()->environment(),
        'app_version' => config('app.version')
    ]);
}
        </pre>
            </div>

            <div class="code-block">
                <h4>Context Enrichers no Laravel 12</h4>
                <pre>
// Em um Service Provider
public function boot(): void
{
    // Registrar um enricher que adiciona informações de contexto dinamicamente
    Log::enrichContext(function (array $context): array {
        return array_merge($context, [
            'request_id' => request()->header('X-Request-ID') ?? (string) Str::uuid(),
            'user_id' => auth()->id() ?? 'guest',
            'ip' => request()->ip(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toIso8601String()
        ]);
    });
}
        </pre>
            </div>

            <div class="code-block">
                <h4>Middleware para Logging de Requisições</h4>
                <pre>
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class RequestLogging
{
    public function handle(Request $request, Closure $next): Response
    {
        // Gerar um ID único para a requisição
        $requestId = $request->header('X-Request-ID') ?? (string) Str::uuid();
        
        // Adicionar ao header da resposta
        $request->headers->set('X-Request-ID', $requestId);
        
        // Adicionar ao contexto de log
        Log::withContext([
            'request_id' => $requestId,
            'ip' => $request->ip(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_agent' => $request->userAgent()
        ]);
        
        // Log de início da requisição
        Log::info('Requisição HTTP recebida');
        
        // Medir tempo de resposta
        $startTime = microtime(true);
        
        // Processar a requisição
        $response = $next($request);
        
        // Calcular tempo de processamento
        $duration = round((microtime(true) - $startTime) * 1000, 2);
        
        // Log de finalização da requisição
        Log::info('Requisição HTTP completada', [
            'duration_ms' => $duration,
            'status_code' => $response->getStatusCode()
        ]);
        
        // Adicionar header com Request ID na resposta
        $response->headers->set('X-Request-ID', $requestId);
        
        return $response;
    }
}
        </pre>
            </div>

            <div class="best-practice">
                <h4>Contexto Específico para Diferentes Domínios</h4>
                <p>Crie helpers para adicionar contexto específico para diferentes domínios da aplicação:</p>
                <pre>
// Em um Service Provider ou classe de suporte
class LogContext
{
    public static function forUser($user): array
    {
        return [
            'user_id' => $user->id,
            'email' => $user->email,
            'roles' => $user->roles->pluck('name')->toArray(),
            'created_at' => $user->created_at->toIso8601String()
        ];
    }
    
    public static function forOrder($order): array
    {
        return [
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'total' => $order->total,
            'items_count' => $order->items->count(),
            'payment_method' => $order->payment_method,
            'status' => $order->status
        ];
    }
    
    public static function forPayment($payment): array
    {
        return [
            'payment_id' => $payment->id,
            'order_id' => $payment->order_id,
            'amount' => $payment->amount,
            'provider' => $payment->provider,
            'status' => $payment->status,
            'transaction_id' => $payment->transaction_id
        ];
    }
}

// Uso
Log::info('Pagamento processado com sucesso', LogContext::forPayment($payment));
        </pre>
            </div>

            <div class="tip">
                <h4>Novidade no Laravel 12</h4>
                <p>O Laravel 12 introduz o método <code>tapLog()</code> que permite modificar o logger antes de
                    registrar uma mensagem:</p>
                <pre>
// Modificar o logger temporariamente para uma operação específica
Log::tapLog(function ($logger) {
    $logger->pushProcessor(new \App\Logging\SensitiveDataMaskProcessor());
})->info('Dados do usuário processados', $userData);
        </pre>
            </div>
        </section>

        <section id="impl-excecoes" class="subsection">
            <h3>5.4. Logging de Exceções</h3>
            <p>Para registrar exceções de forma adequada:</p>

            <div class="code-block">
                <h4>Logging de Exceções em Blocos Try-Catch</h4>
                <pre>
try {
    // Código que pode gerar exceções
    $this->paymentService->processPayment($order);
} catch (\App\Exceptions\PaymentException $e) {
    // Log específico para exceção de pagamento
    Log::error('Falha ao processar pagamento', [
        'order_id' => $order->id,
        'exception' => get_class($e),
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'payment_method' => $order->payment_method
    ]);
    
    throw $e; // Re-lançar ou tratar conforme necessário
} catch (\Exception $e) {
    // Log genérico para outras exceções
    Log::error('Erro ao processar pedido', [
        'order_id' => $order->id,
        'exception' => get_class($e),
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    // Ou usando o helper report()
    report($e);
    
    throw $e;
}
        </pre>
            </div>

            <div class="code-block">
                <h4>Configuração do Exception Handler no Laravel 12</h4>
                <pre>
// Em App\Exceptions\Handler.php
namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\HttpException;

class Handler extends ExceptionHandler
{
    // ...

    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            if ($this->shouldReport($e)) {
                $context = [
                    'exception_class' => get_class($e),
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ];
                
                // Adicionar contexto específico para certos tipos de exceção
                if ($e instanceof HttpException) {
                    $context['status_code'] = $e->getStatusCode();
                }
                
                if ($e instanceof ValidationException) {
                    $context['validation_errors'] = $e->validator->errors()->toArray();
                }
                
                if ($e instanceof \App\Exceptions\BusinessException) {
                    $context['business_code'] = $e->getBusinessCode();
                    $context['business_data'] = $e->getBusinessData();
                }
                
                // Determinar o nível de log adequado baseado no tipo de exceção
                $level = match(true) {
                    $e instanceof AuthenticationException => 'warning',
                    $e instanceof HttpException && $e->getStatusCode() === 404 => 'notice',
                    $e instanceof \App\Exceptions\CriticalException => 'critical',
                    default => 'error'
                };
                
                // Registrar o log com o nível e contexto apropriados
                logger()->log($level, "Exceção: {$e->getMessage()}", $context);
            }
        });
        
        // Reportar exceções para serviços externos
        $this->reportable(function (\App\Exceptions\CriticalException $e) {
            \App\Services\AlertService::notifyTeam($e);
        })->stop();
        
        // Personalizar a resposta para exceções específicas
        $this->renderable(function (\App\Exceptions\BusinessException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'code' => $e->getBusinessCode(),
                    'data' => $e->getBusinessData()
                ], 400);
            }
        });
    }
}
        </pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas para Logging de Exceções</h4>
                <ul>
                    <li>Sempre inclua o tipo da exceção, mensagem e stack trace</li>
                    <li>Adicione informações contextuais que ajudem a identificar o problema</li>
                    <li>Use níveis de log adequados de acordo com a gravidade da exceção</li>
                    <li>Utilize mecanismos centralizados como o Exception Handler para garantir consistência</li>
                    <li>Tome cuidado para não registrar dados sensíveis no stack trace</li>
                    <li>Crie exceções personalizadas para diferentes tipos de erros de negócio</li>
                    <li>Implemente o método <code>report()</code> em exceções personalizadas para controlar como são
                        registradas</li>
                </ul>
            </div>

            <div class="tip">
                <h4>Novidade no Laravel 12</h4>
                <p>O Laravel 12 introduz várias melhorias significativas para o sistema de logging que tornam o registro
                    e a análise de logs mais poderosos e flexíveis:</p>

                <ul>
                    <li><strong>Integração com OpenTelemetry:</strong> Suporte nativo para exportar logs para sistemas
                        de observabilidade compatíveis com OpenTelemetry, permitindo correlacionar logs, métricas e
                        traces em uma única plataforma.</li>

                    <li><strong>Log Processors Tipados:</strong> Processadores de log com tipagem estrita para
                        transformar e enriquecer registros de log antes de serem armazenados.</li>
                </ul>

                <pre>
// Exemplo de Log Processor tipado
namespace App\Logging\Processors;

use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;

class RequestInfoProcessor implements ProcessorInterface
{
    public function __invoke(LogRecord $record): LogRecord
    {
        $record->extra['http_method'] = request()->method();
        $record->extra['url'] = request()->fullUrl();
        $record->extra['client_ip'] = request()->ip();
        
        return $record;
    }
}

// Registrando o processor
Log::channel('daily')->pushProcessor(new RequestInfoProcessor());
    </pre>

                <ul>
                    <li><strong>Formatadores Avançados:</strong> Novos formatadores para personalizar a saída de logs,
                        incluindo suporte aprimorado para JSON estruturado com metadados adicionais.</li>
                </ul>

                <pre>
// Configuração de formatador personalizado
'structured' => [
    'driver' => 'single',
    'path' => storage_path('logs/structured.log'),
    'level' => 'debug',
    'formatter' => \App\Logging\Formatters\CustomJsonFormatter::class,
    'formatter_with' => [
        'includeStacktraces' => true,
        'appendNewline' => true,
        'ignoreEmptyContextAndExtra' => false,
    ],
],
    </pre>

                <ul>
                    <li><strong>Logging Condicional:</strong> Métodos para registrar logs apenas quando certas condições
                        são atendidas, melhorando a performance.</li>
                </ul>

                <pre>
// Log condicional com base em uma expressão
Log::when($user->isAdmin(), function ($logger) use ($sensitiveData) {
    $logger->info('Dados sensíveis acessados', $sensitiveData);
});

// Log condicional com base no ambiente
Log::whenDebug(function ($logger) use ($debugData) {
    $logger->debug('Informações detalhadas de depuração', $debugData);
});
    </pre>

                <ul>
                    <li><strong>Canais Dinâmicos:</strong> Capacidade de criar e configurar canais de log dinamicamente
                        em tempo de execução.</li>
                </ul>

                <pre>
// Criar um canal dinâmico para um tenant específico
$tenantLogger = Log::build([
    'driver' => 'single',
    'path' => storage_path("logs/tenants/{$tenant->id}.log"),
    'level' => 'debug',
]);

$tenantLogger->info('Operação específica do tenant executada', [
    'tenant_id' => $tenant->id,
    'operation' => 'data_import'
]);
    </pre>
            </div>

            <section id="laravel12" class="manual-section">
                <h2>9. Recursos do Laravel 12</h2>
                <p>O Laravel 12 introduz diversas melhorias significativas no sistema de logging, tornando-o mais
                    poderoso, flexível e fácil de usar.</p>

                <section id="laravel12-structured" class="subsection">
                    <h3>9.1. Structured Logging</h3>
                    <p>O Laravel 12 aprimora o suporte para logs estruturados, facilitando a análise e processamento por
                        ferramentas como Elasticsearch, Splunk e CloudWatch Logs.</p>

                    <div class="code-block">
                        <h4>Configuração de Logger Estruturado</h4>
                        <pre>
// Em config/logging.php
'channels' => [
    'structured' => [
        'driver' => 'custom',
        'via' => \App\Logging\CreateStructuredLogger::class,
        'path' => storage_path('logs/structured.log'),
        'level' => env('LOG_LEVEL', 'debug'),
    ],
],

// Implementação do logger estruturado
namespace App\Logging;

use Monolog\Formatter\JsonFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class CreateStructuredLogger
{
    public function __invoke(array $config): Logger
    {
        $handler = new StreamHandler(
            $config['path'] ?? storage_path('logs/structured.log'),
            $config['level'] ?? 'debug'
        );
        
        // Configurar o formatador JSON
        $formatter = new JsonFormatter();
        $formatter->includeStacktraces(true);
        
        // Adicionar metadados padrão a cada log
        $handler->setFormatter($formatter);
        
        $logger = new Logger(
            $config['name'] ?? config('app.name'),
            [$handler]
        );
        
        // Adicionar processador para incluir metadados padrão
        $logger->pushProcessor(function ($record) {
            $record['extra']['hostname'] = gethostname();
            $record['extra']['environment'] = app()->environment();
            $record['extra']['app_version'] = config('app.version');
            
            return $record;
        });
        
        return $logger;
    }
}
            </pre>
                    </div>

                    <div class="example-commits">
                        <h4>Exemplo de Saída de Log Estruturado</h4>
                        <pre>
{
    "message": "Usuário autenticado com sucesso",
    "context": {
        "user_id": 1001,
        "email": "<EMAIL>",
        "ip_address": "***********"
    },
    "level": 200,
    "level_name": "INFO",
    "channel": "application",
    "datetime": "2023-05-15T14:30:45.123456+00:00",
    "extra": {
        "hostname": "web-server-01",
        "environment": "production",
        "app_version": "2.5.0",
        "request_id": "req-123456789",
        "memory_usage": 10485760,
        "process_id": 1234
    }
}
            </pre>
                    </div>

                    <div class="best-practice">
                        <h4>Benefícios do Logging Estruturado</h4>
                        <ul>
                            <li>Facilita a pesquisa e filtragem de logs em ferramentas de análise</li>
                            <li>Permite agregações e visualizações avançadas</li>
                            <li>Melhora a correlação entre eventos relacionados</li>
                            <li>Facilita a integração com sistemas de observabilidade</li>
                            <li>Torna os logs legíveis por máquina e processáveis automaticamente</li>
                        </ul>
                    </div>
                </section>

                <section id="laravel12-context" class="subsection">
                    <h3>9.2. Context Enrichers</h3>
                    <p>O Laravel 12 introduz Context Enrichers, que permitem adicionar automaticamente informações de
                        contexto a todos os logs da aplicação.</p>

                    <div class="code-block">
                        <h4>Configuração de Context Enrichers</h4>
                        <pre>
// Em um Service Provider
namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class LoggingServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        // Adicionar contexto global a todos os logs
        Log::withContext([
            'app_name' => config('app.name'),
            'app_env' => app()->environment(),
        ]);
        
        // Registrar um enricher que adiciona informações dinâmicas
        Log::enrichContext(function (array $context): array {
            return array_merge($context, [
                'request_id' => request()->header('X-Request-ID') ?? (string) Str::uuid(),
                'user_id' => auth()->id() ?? 'guest',
                'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
                'timestamp' => now()->toIso8601String(),
            ]);
        });
        
        // Registrar um enricher específico para logs de erro
        Log::enrichContext(function (array $context, string $level): array {
            if (in_array($level, ['error', 'critical', 'alert', 'emergency'])) {
                return array_merge($context, [
                    'git_commit' => exec('git rev-parse HEAD') ?: 'unknown',
                    'server_load' => sys_getloadavg()[0],
                    'php_version' => PHP_VERSION,
                ]);
            }
            
            return $context;
        });
    }
}
            </pre>
                    </div>

                    <div class="tip">
                        <h4>Uso Avançado de Context Enrichers</h4>
                        <p>Os Context Enrichers podem ser usados para implementar funcionalidades avançadas como:</p>
                        <ul>
                            <li>Rastreamento distribuído com propagação automática de IDs de trace</li>
                            <li>Adição de informações de performance e métricas do sistema</li>
                            <li>Enriquecimento condicional baseado no tipo de log ou ambiente</li>
                            <li>Mascaramento automático de dados sensíveis</li>
                        </ul>
                        <pre>
// Enricher para mascaramento de dados sensíveis
Log::enrichContext(function (array $context): array {
    $sensitiveFields = ['password', 'credit_card', 'ssn', 'token'];
    
    return array_map(function ($value, $key) use ($sensitiveFields) {
        if (is_string($value) && in_array($key, $sensitiveFields)) {
            return '***MASKED***';
        }
        
        return $value;
    }, $context, array_keys($context));
});
            </pre>
                    </div>
                </section>

                <section id="laravel12-channels" class="subsection">
                    <h3>9.3. Canais Aprimorados</h3>
                    <p>O Laravel 12 introduz melhorias significativas nos canais de log, incluindo canais condicionais,
                        canais dinâmicos e melhor suporte para configuração.</p>

                    <div class="code-block">
                        <h4>Canais Condicionais</h4>
                        <pre>
// Em um Service Provider
public function boot(): void
{
    // Registrar um seletor de canal condicional
    $this->app->make('log')->channelSelector(function (string $channel) {
        // Em produção, enviar logs críticos para múltiplos canais
        if (app()->environment('production') && in_array($channel, ['emergency', 'alert', 'critical'])) {
            return ['file', 'slack', 'newrelic'];
        }
        
        // Em desenvolvimento, enviar logs de depuração para um canal específico
        if (app()->environment('local') && $channel === 'debug') {
            return 'stderr';
        }
        
        // Logs de segurança sempre vão para canais específicos
        if ($channel === 'security') {
            return ['security_file', 'security_alerts'];
        }
        
        return $channel;
    });
}
            </pre>
                    </div>

                    <div class="code-block">
                        <h4>Canais Dinâmicos</h4>
                        <pre>
// Criar um canal dinamicamente em tempo de execução
$customLogger = Log::build([
    'driver' => 'daily',
    'path' => storage_path("logs/custom/{$category}.log"),
    'level' => 'debug',
    'days' => 7,
]);

// Usar o canal dinâmico
$customLogger->info('Evento específico registrado', $contextData);

// Criar um stack dinâmico
$stackLogger = Log::stack([
    Log::channel('daily'),
    $customLogger,
    Log::build([
        'driver' => 'slack',
        'url' => $slackWebhookUrl,
        'username' => 'Custom Logger',
        'emoji' => ':robot_face:',
        'level' => 'error',
    ])
]);

// Usar o stack dinâmico
$stackLogger->error('Erro importante', $errorContext);
            </pre>
                    </div>

                    <div class="best-practice">
                        <h4>Canais Especializados para Diferentes Tipos de Log</h4>
                        <p>No Laravel 12, é recomendado criar canais especializados para diferentes tipos de informação:
                        </p>
                        <ul>
                            <li><strong>audit:</strong> Para eventos de auditoria e segurança</li>
                            <li><strong>business:</strong> Para eventos de negócio importantes</li>
                            <li><strong>performance:</strong> Para métricas de performance</li>
                            <li><strong>integration:</strong> Para logs de integrações com sistemas externos</li>
                            <li><strong>queue:</strong> Para monitoramento específico de jobs em fila</li>
                        </ul>
                        <pre>
// Exemplo de uso de canais especializados
Log::channel('audit')->info('Usuário alterou permissões', [
    'admin_id' => $admin->id,
    'target_user' => $user->id,
    'permissions' => $permissions
]);

Log::channel('business')->notice('Pedido finalizado', [
    'order_id' => $order->id,
    'total' => $order->total,
    'customer' => $order->customer_id
]);

Log::channel('performance')->info('Operação de longa duração', [
    'operation' => 'import_products',
    'duration_ms' => $duration,
    'records_processed' => $count
]);
            </pre>
                    </div>
                </section>

                <section id="laravel12-opentelemetry" class="subsection">
                    <h3>9.4. Integração com OpenTelemetry</h3>
                    <p>O Laravel 12 oferece integração nativa com OpenTelemetry, permitindo correlacionar logs, métricas
                        e traces em uma única plataforma de observabilidade.</p>

                    <div class="code-block">
                        <h4>Configuração do OpenTelemetry</h4>
                        <pre>
// Em config/logging.php
'channels' => [
    // ...
    'opentelemetry' => [
        'driver' => 'monolog',
        'level' => env('LOG_LEVEL', 'debug'),
        'handler' => \App\Logging\OpenTelemetryLogHandler::class,
    ],
],

// Implementação do handler OpenTelemetry
namespace App\Logging;

use Monolog\Handler\AbstractProcessingHandler;
use Monolog\LogRecord;
use OpenTelemetry\API\Logs\LoggerInterface;
use OpenTelemetry\API\Trace\SpanContextInterface;
use OpenTelemetry\API\Trace\SpanInterface;
use OpenTelemetry\API\Trace\StatusCode;
use OpenTelemetry\Context\Context;
use OpenTelemetry\SDK\Logs\LoggerProvider;

class OpenTelemetryLogHandler extends AbstractProcessingHandler
{
    protected LoggerInterface $logger;
    
    public function __construct($level = 'debug', bool $bubble = true)
    {
        parent::__construct($level, $bubble);
        
        // Obter o logger do provider configurado
        $this->logger = app(LoggerProvider::class)->getLogger('laravel-app');
    }
    
    protected function write(LogRecord $record): void
    {
        // Extrair o contexto atual do trace, se existir
        $spanContext = Context::getCurrent()->get(SpanInterface::class);
        $traceId = null;
        $spanId = null;
        
        if ($spanContext instanceof SpanContextInterface) {
            $traceId = $spanContext->getTraceId();
            $spanId = $spanContext->getSpanId();
        }
        
        // Mapear o nível do log para a severidade do OpenTelemetry
        $severity = $this->mapSeverity($record->level->value);
        
        // Criar atributos a partir do contexto
        $attributes = [];
        foreach ($record->context as $key => $value) {
            if (is_scalar($value) || is_null($value)) {
                $attributes[$key] = $value;
            } elseif (is_array($value)) {
                $attributes[$key] = json_encode($value);
            } else {
                $attributes[$key] = (string) $value;
            }
        }
        
        // Adicionar metadados extras
        $attributes['logger.name'] = $record->channel;
        $attributes['logger.thread_name'] = getmypid();
        
        // Registrar o log no OpenTelemetry
        $this->logger->logRecord(
            timestamp: $record->datetime->getTimestamp() * 1_000_000_000, // nanoseconds
            severityText: $record->level->name,
            severityNumber: $severity,
            body: $record->message,
            attributes: $attributes,
            traceId: $traceId,
            spanId: $spanId
        );
    }
    
    private function mapSeverity(int $level): int
    {
        // Mapear níveis do Monolog para severidades do OpenTelemetry
        return match(true) {
            $level >= 600 => 17, // FATAL
            $level >= 500 => 17, // FATAL
            $level >= 400 => 13, // ERROR
            $level >= 300 => 9,  // WARN
            $level >= 200 => 5,  // INFO
            $level >= 100 => 1,  // DEBUG
            default => 1,        // DEBUG
        };
    }
}
            </pre>
                    </div>

                    <div class="tip">
                        <h4>Benefícios da Integração com OpenTelemetry</h4>
                        <p>A integração com OpenTelemetry oferece diversos benefícios:</p>
                        <ul>
                            <li>Correlação entre logs, métricas e traces em uma única plataforma</li>
                            <li>Rastreamento distribuído em sistemas complexos</li>
                            <li>Visualização de fluxos completos de requisições através de múltiplos serviços</li>
                            <li>Análise de performance e detecção de gargalos</li>
                            <li>Compatibilidade com diversas ferramentas de observabilidade como Jaeger, Zipkin,
                                Prometheus, etc.</li>
                        </ul>
                    </div>

                    <div class="code-block">
                        <h4>Uso de Traces com Logs</h4>
                        <pre>
// Em um middleware ou controller
use OpenTelemetry\API\Trace\Span;
use OpenTelemetry\API\Trace\SpanKind;
use OpenTelemetry\API\Trace\StatusCode;
use OpenTelemetry\Context\Context;
use Illuminate\Support\Facades\Log;

// Iniciar um trace
$tracer = app(\OpenTelemetry\API\Trace\TracerInterface::class);
$span = $tracer->spanBuilder('process_order')
    ->setSpanKind(SpanKind::INTERNAL)
    ->startSpan();

// Tornar o span atual no contexto
$scope = $span->activate();

try {
    // Processar o pedido
    $result = $this->orderService->process($orderId);
    
    // Adicionar atributos ao span
    $span->setAttribute('order.id', $orderId);
    $span->setAttribute('order.total', $order->total);
    
    // Registrar log que será automaticamente associado ao trace atual
    Log::channel('opentelemetry')->info('Pedido processado com sucesso', [
        'order_id' => $orderId,
        'processing_time_ms' => $processingTime
    ]);
    
    $span->setStatus(StatusCode::OK);
    
    return $result;
} catch (\Exception $e) {
    // Registrar erro no span
    $span->recordException($e);
    $span->setStatus(StatusCode::ERROR, $e->getMessage());
    
    // Log de erro será associado ao trace atual
    Log::channel('opentelemetry')->error('Falha ao processar pedido', [
        'order_id' => $orderId,
        'exception' => get_class($e),
        'message' => $e->getMessage()
    ]);
    
    throw $e;
} finally {
    // Finalizar o span e o escopo
    $scope->detach();
    $span->end();
}
            </pre>
                    </div>
                </section>

                <section id="laravel12-typed" class="subsection">
                    <h3>9.5. Tipagem Estrita</h3>
                    <p>O Laravel 12 adota tipagem estrita em todo o sistema de logging, melhorando a segurança e a
                        clareza do código.</p>

                    <div class="code-block">
                        <h4>Interface LoggerInterface com Tipagem Estrita</h4>
                        <pre>
namespace Illuminate\Log;

interface LoggerInterface
{
    /**
     * Log an emergency message to the logs.
     */
    public function emergency(string $message, array $context = []): void;

    /**
     * Log an alert message to the logs.
     */
    public function alert(string $message, array $context = []): void;

    /**
     * Log a critical message to the logs.
     */
    public function critical(string $message, array $context = []): void;

    /**
     * Log an error message to the logs.
     */
    public function error(string $message, array $context = []): void;

    /**
     * Log a warning message to the logs.
     */
    public function warning(string $message, array $context = []): void;

    /**
     * Log a notice to the logs.
     */
    public function notice(string $message, array $context = []): void;

    /**
     * Log an informational message to the logs.
     */
    public function info(string $message, array $context = []): void;

    /**
     * Log a debug message to the logs.
     */
    public function debug(string $message, array $context = []): void;

    /**
     * Log a message to the logs.
     */
    public function log(string $level, string $message, array $context = []): void;
}
            </pre>
                    </div>

                    <div class="best-practice">
                        <h4>Benefícios da Tipagem Estrita</h4>
                        <ul>
                            <li>Detecção precoce de erros durante o desenvolvimento</li>
                            <li>Melhor suporte de IDEs com autocompletar e verificação de tipos</li>
                            <li>Código mais seguro e previsível</li>
                            <li>Documentação implícita através dos tipos</li>
                            <li>Refatoração mais segura</li>
                        </ul>
                    </div>

                    <div class="code-block">
                        <h4>Exemplo de Classe de Log com Tipagem Estrita</h4>
                        <pre>
namespace App\Logging;

use Illuminate\Support\Facades\Log;
use Psr\Log\LoggerInterface;

class ApplicationLogger
{
    public function __construct(
        protected readonly LoggerInterface $logger
    ) {}
    
    /**
     * Registra uma operação de usuário.
     */
    public function logUserOperation(
        int $userId, 
        string $operation, 
        array $details = []
    ): void {
        $this->logger->info("Usuário executou operação: {$operation}", [
            'user_id' => $userId,
            'operation' => $operation,
            'details' => $details,
            'timestamp' => now()->toIso8601String()
        ]);
    }
    
    /**
     * Registra uma falha de sistema.
     */
    public function logSystemFailure(
        string $component, 
        string $errorMessage, 
        \Throwable $exception = null
    ): void {
        $context = [
            'component' => $component,
            'error' => $errorMessage,
            'timestamp' => now()->toIso8601String()
        ];
        
        if ($exception !== null) {
            $context['exception'] = [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }
        
        $this->logger->error("Falha no sistema: {$component}", $context);
    }
    
    /**
     * Registra uma métrica de performance.
     */
    public function logPerformanceMetric(
        string $operation, 
        float $durationMs, 
        int $recordsProcessed = null
    ): void {
        $context = [
            'operation' => $operation,
            'duration_ms' => $durationMs,
            'timestamp' => now()->toIso8601String()
        ];
        
        if ($recordsProcessed !== null) {
            $context['records_processed'] = $recordsProcessed;
            $context['avg_time_per_record_ms'] = $recordsProcessed > 0 
                ? round($durationMs / $recordsProcessed, 2) 
                : 0;
        }
        
        $this->logger->info("Métrica de performance: {$operation}", $context);
    }
}
            </pre>
                    </div>

                    <div class="tip">
                        <h4>Usando Enums para Níveis de Log</h4>
                        <p>Com a tipagem estrita do PHP 8.1+, você pode usar enums para representar níveis de log:</p>
                        <pre>
enum LogLevel: string
{
    case EMERGENCY = 'emergency';
    case ALERT = 'alert';
    case CRITICAL = 'critical';
    case ERROR = 'error';
    case WARNING = 'warning';
    case NOTICE = 'notice';
    case INFO = 'info';
    case DEBUG = 'debug';
    
    /**
     * Obtém a descrição do nível de log.
     */
    public function description(): string
    {
        return match($this) {
            self::EMERGENCY => 'Sistema completamente indisponível',
            self::ALERT => 'Ação imediata necessária',
            self::CRITICAL => 'Componente crítico falhou',
            self::ERROR => 'Erro operacional',
            self::WARNING => 'Situação potencialmente problemática',
            self::NOTICE => 'Evento normal significativo',
            self::INFO => 'Informação operacional',
            self::DEBUG => 'Informação detalhada para depuração'
        };
    }
    
    /**
     * Verifica se o nível é um erro ou mais grave.
     */
    public function isError(): bool
    {
        return in_array($this, [
            self::EMERGENCY,
            self::ALERT,
            self::CRITICAL,
            self::ERROR
        ]);
    }
}

// Uso
Log::log(LogLevel::ERROR->value, 'Mensagem de erro');

// Ou com uma classe helper
class AppLog
{
    public static function log(LogLevel $level, string $message, array $context = []): void
    {
        Log::log($level->value, $message, $context);
        
        // Notificar equipe para níveis críticos
        if ($level === LogLevel::EMERGENCY || $level === LogLevel::ALERT) {
            self::notifyTeam($level, $message, $context);
        }
    }
    
    private static function notifyTeam(LogLevel $level, string $message, array $context): void
    {
        // Implementação da notificação
    }
}

// Uso da classe helper
AppLog::log(LogLevel::CRITICAL, 'Serviço de pagamento indisponível');
            </pre>
                    </div>
                </section>
            </section>

            <section id="boas-praticas" class="manual-section">
                <h2>10. Boas Práticas</h2>
                <p>Resumo das melhores práticas para implementação e uso do sistema de logs no Laravel 12:</p>

                <div class="best-practice">
                    <h4>Práticas Gerais</h4>
                    <ul>
                        <li>Use níveis de log apropriados para cada tipo de mensagem</li>
                        <li>Inclua sempre informações de contexto relevantes</li>
                        <li>Mantenha uma estrutura consistente em todos os logs</li>
                        <li>Use identificadores únicos para correlacionar logs relacionados</li>
                        <li>Aproveite os Context Enrichers para adicionar informações comuns automaticamente</li>
                        <li>Configure canais especializados para diferentes tipos de logs</li>
                        <li>Utilize logs estruturados para facilitar a análise</li>
                        <li>Integre com sistemas de observabilidade como OpenTelemetry</li>
                        <li>Aproveite a tipagem estrita para tornar o código mais seguro</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo de Implementação Completa</h4>
                    <pre>
// Em um Service Provider
namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Monolog\Formatter\JsonFormatter;

class LoggingServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        // 1. Configurar contexto global
        Log::withContext([
            'app_name' => config('app.name'),
            'app_version' => config('app.version'),
            'environment' => app()->environment(),
        ]);
        
        // 2. Registrar enrichers para adicionar contexto dinâmico
        Log::enrichContext(function (array $context): array {
            return array_merge($context, [
                'request_id' => request()->header('X-Request-ID') ?? (string) Str::uuid(),
                'user_id' => auth()->id() ?? 'guest',
                'ip' => request()->ip(),
                'url' => request()->fullUrl(),
                'method' => request()->method(),
                'user_agent' => request()->userAgent(),
                'timestamp' => now()->toIso8601String(),
            ]);
        });
        
        // 3. Configurar formatador JSON para logs estruturados
        Log::channel('daily')->tap(function ($logger) {
            $handler = $logger->getHandlers()[0];
            $formatter = new JsonFormatter();
            $formatter->includeStacktraces(true);
            $handler->setFormatter($formatter);
        });
        
        // 4. Registrar processador para mascarar dados sensíveis
        Log::channel('daily')->pushProcessor(new \App\Logging\Processors\SensitiveDataMaskProcessor());
    }
}
        </pre>
                </div>

                <h3>10.1. O que Registrar</h3>
                <p>Diretrizes sobre quais eventos devem ser registrados em logs:</p>

                <div class="comparison-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Categoria</th>
                                <th>O que Registrar</th>
                                <th>Nível Recomendado</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Autenticação</td>
                                <td>Login, logout, falhas de login, alterações de senha, reset de senha</td>
                                <td>info (sucesso), warning/error (falhas)</td>
                            </tr>
                            <tr>
                                <td>Autorização</td>
                                <td>Tentativas de acesso não autorizado, alterações de permissões</td>
                                <td>warning (tentativas), notice (alterações)</td>
                            </tr>
                            <tr>
                                <td>Operações de Dados</td>
                                <td>Criação, atualização e exclusão de dados importantes</td>
                                <td>info (operações normais), notice (operações importantes)</td>
                            </tr>
                            <tr>
                                <td>Transações Financeiras</td>
                                <td>Pagamentos, reembolsos, alterações de plano, assinaturas</td>
                                <td>notice (todas as operações)</td>
                            </tr>
                            <tr>
                                <td>Integrações Externas</td>
                                <td>Chamadas a APIs, respostas, falhas de comunicação</td>
                                <td>info (sucesso), error (falhas)</td>
                            </tr>
                            <tr>
                                <td>Performance</td>
                                <td>Operações lentas, uso de recursos, gargalos</td>
                                <td>debug (detalhes), warning (problemas)</td>
                            </tr>
                            <tr>
                                <td>Erros de Sistema</td>
                                <td>Exceções, falhas de componentes, erros de banco de dados</td>
                                <td>error, critical (dependendo da gravidade)</td>
                            </tr>
                            <tr>
                                <td>Eventos de Sistema</td>
                                <td>Inicialização, desligamento, deploy, migrações</td>
                                <td>notice (eventos importantes), info (eventos normais)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>10.2. O que Evitar</h3>
                <div class="bad-practice">
                    <h4>Práticas a Evitar</h4>
                    <ul>
                        <li><strong>Dados Sensíveis:</strong> Nunca registre senhas, tokens, dados pessoais sensíveis ou
                            informações financeiras completas</li>
                        <li><strong>Logs Excessivos:</strong> Evite logging excessivo em loops ou operações de alta
                            frequência</li>
                        <li><strong>Mensagens Genéricas:</strong> Evite mensagens como "Erro" ou "Sucesso" sem contexto
                            específico</li>
                        <li><strong>Logs de Debug em Produção:</strong> Limite logs de debug em ambientes de produção
                            para evitar sobrecarga</li>
                        <li><strong>Informações Redundantes:</strong> Evite repetir informações que já estão disponíveis
                            no contexto global</li>
                        <li><strong>Operações Síncronas Lentas:</strong> Evite operações de logging que bloqueiam o
                            fluxo principal da aplicação</li>
                    </ul>
                </div>

                <h3>10.3. Estratégias para Diferentes Ambientes</h3>
                <div class="comparison-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Ambiente</th>
                                <th>Estratégia de Logging</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Local</td>
                                <td>
                                    <ul>
                                        <li>Nível: debug</li>
                                        <li>Canais: stderr, single</li>
                                        <li>Formato: detalhado e legível</li>
                                        <li>Objetivo: facilitar desenvolvimento e depuração</li>
                                    </ul>
                                </td>
                            </tr>
                            <tr>
                                <td>Testes</td>
                                <td>
                                    <ul>
                                        <li>Nível: null ou error</li>
                                        <li>Canais: null (desativado) ou memory</li>
                                        <li>Objetivo: evitar poluição de logs durante testes automatizados</li>
                                    </ul>
                                </td>
                            </tr>
                            <tr>
                                <td>Staging</td>
                                <td>
                                    <ul>
                                        <li>Nível: debug ou info</li>
                                        <li>Canais: daily, structured</li>
                                        <li>Formato: estruturado (JSON)</li>
                                        <li>Objetivo: simular produção com mais detalhes para testes</li>
                                    </ul>
                                </td>
                            </tr>
                            <tr>
                                <td>Produção</td>
                                <td>
                                    <ul>
                                        <li>Nível: info (geral), debug (específico para troubleshooting)</li>
                                        <li>Canais: daily, structured, opentelemetry, alertas</li>
                                        <li>Formato: estruturado (JSON) para análise automatizada</li>
                                        <li>Objetivo: monitoramento, troubleshooting, auditoria</li>
                                        <li>Retenção: configurada de acordo com necessidades de negócio e regulatórias
                                        </li>
                                    </ul>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>10.4. Checklist de Qualidade de Logs</h3>
                <div class="best-practice">
                    <h4>Verifique se seus logs atendem a estes critérios:</h4>
                    <ul>
                        <li>✅ Possuem timestamp preciso e em formato padronizado (ISO 8601)</li>
                        <li>✅ Incluem identificadores para correlacionamento (request_id, session_id, etc.)</li>
                        <li>✅ Mensagens são claras e descrevem precisamente o evento</li>
                        <li>✅ Contêm informações de contexto com dados relevantes</li>
                        <li>✅ Utilizam o nível de log adequado para a severidade do evento</li>
                        <li>✅ Não contêm dados sensíveis ou confidenciais</li>
                        <li>✅ São facilmente filtráveis e pesquisáveis</li>
                        <li>✅ Estão configurados com retenção apropriada</li>
                        <li>✅ Podem ser correlacionados com outros logs de componentes relacionados</li>
                        <li>✅ Aproveitam as novas funcionalidades do Laravel 12 (tipagem estrita, context enrichers)
                        </li>
                        <li>✅ São estruturados para facilitar análise automatizada</li>
                        <li>✅ Integram-se com sistemas de observabilidade</li>
                    </ul>
                </div>
            </section>

            <section id="exemplos" class="manual-section">
                <h2>11. Exemplos Práticos</h2>
                <p>Exemplos de implementação em situações comuns com Laravel 12:</p>

                <h3>11.1. Logging em API Controllers</h3>
                <div class="code-block">
                    <pre>
namespace App\Http\Controllers;

use App\Http\Resources\OrderResource;
use App\Services\OrderService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    public function __construct(
        protected readonly OrderService $orderService
    ) {}

    public function store(Request $request): JsonResponse
    {
        // Log de início da operação
        Log::info('Iniciando criação de pedido', [
            'items_count' => count($request->items)
        ]);

        try {
            $validatedData = $request->validate([
                'items' => 'required|array|min:1',
                'shipping_address_id' => 'required|exists:addresses,id',
                'payment_method_id' => 'required|exists:payment_methods,id'
            ]);

            $order = $this->orderService->createOrder($validatedData);

            // Log de sucesso com dados relevantes
            Log::info('Pedido criado com sucesso', [
                'order_id' => $order->id,
                'total_value' => $order->total_value,
                'items_count' => $order->items->count()
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Pedido criado com sucesso',
                'data' => new OrderResource($order)
            ], 201);

        } catch (\App\Exceptions\PaymentException $e) {
            // Log específico para erro de pagamento
            Log::error('Falha no processamento do pagamento', [
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'payment_method_id' => $request->payment_method_id
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Não foi possível processar o pagamento',
                'errors' => ['payment' => $e->getMessage()]
            ], 400);

        } catch (\Exception $e) {
            // Log para exceções genéricas
            Log::error('Erro ao criar pedido', [
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Ocorreu um erro ao criar o pedido'
            ], 500);
        }
    }
}
        </pre>
                </div>

                <h3>11.2. Logging em Services</h3>
                <div class="code-block">
                    <pre>
namespace App\Services;

use App\Models\Order;
use App\Repositories\Contracts\OrderRepositoryInterface;
use App\Services\PaymentService;
use Illuminate\Support\Facades\Log;

class OrderService
{
    public function __construct(
        protected readonly OrderRepositoryInterface $orderRepository,
        protected readonly PaymentService $paymentService
    ) {}

    public function createOrder(array $data): Order
    {
        // Logging do início da operação no serviço
        $logContext = [
            'user_id' => auth()->id(),
            'items_count' => count($data['items'])
        ];

        Log::info('Iniciando criação de pedido no serviço', $logContext);

        // Criar o pedido no banco de dados
        Log::debug('Salvando dados do pedido no repositório', $logContext);
        $order = $this->orderRepository->create($data);
        $logContext['order_id'] = $order->id;

        // Processar pagamento
        Log::info('Processando pagamento do pedido', $logContext + [
            'payment_method' => $data['payment_method_id'],
            'order_total' => $order->calculateTotal()
        ]);

        $paymentResult = $this->paymentService->processPayment($order, $data['payment_method_id']);

        if ($paymentResult['status'] === 'success') {
            Log::info('Pagamento processado com sucesso', $logContext + [
                'transaction_id' => $paymentResult['transaction_id']
            ]);

            $order = $this->orderRepository->updateStatus($order->id, 'paid');
        } else {
            Log::warning('Falha no processamento do pagamento', $logContext + [
                'error_code' => $paymentResult['error_code'],
                'error_message' => $paymentResult['error_message']
            ]);

            $order = $this->orderRepository->updateStatus($order->id, 'payment_failed');

            throw new \App\Exceptions\PaymentException($paymentResult['error_message']);
        }

        // Logging da finalização bem-sucedida
        Log::info('Pedido criado e processado com sucesso', $logContext + [
            'status' => $order->status
        ]);

        return $order;
    }
}
        </pre>
                </div>

                <h3>11.3. Logging em Jobs e Processos em Background</h3>
                <div class="code-block">
                    <pre>
namespace App\Jobs;

use App\Models\Order;
use App\Services\ShippingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessOrderShipment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        protected readonly Order $order
    ) {}

    public function handle(ShippingService $shippingService): void
    {
        $logContext = [
            'order_id' => $this->order->id,
            'job_id' => $this->job->getJobId(),
            'attempt' => $this->attempts()
        ];

        Log::info('Iniciando processamento de envio do pedido', $logContext);

        try {
            // Processar envio
            $shipmentResult = $shippingService->createShipment($this->order);

            // Log do resultado
            Log::info('Envio processado com sucesso', $logContext + [
                'tracking_code' => $shipmentResult['tracking_code'],
                'estimated_delivery' => $shipmentResult['estimated_delivery']
            ]);

        } catch (\Exception $e) {
            Log::error('Falha ao processar envio', $logContext + [
                'exception' => get_class($e),
                'message' => $e->getMessage()
            ]);

            // Se ainda houver tentativas restantes, liberar para retry
            if ($this->attempts() < $this->tries) {
                Log::info('Agendando nova tentativa de processamento de envio', $logContext + [
                    'next_attempt' => now()->addMinutes(5 * $this->attempts())
                ]);

                // Lançar a exceção para o Laravel processar o retry
                throw $e;
            } else {
                Log::critical('Todas as tentativas de processamento de envio falharam', $logContext);
            }
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::critical('Job de processamento de envio falhou definitivamente', [
            'order_id' => $this->order->id,
            'exception' => get_class($exception),
            'message' => $exception->getMessage()
        ]);

        // Notificar o time sobre a falha
        // ...
    }
}
        </pre>
                </div>

                <h3>11.4. Logging em Middleware</h3>
                <div class="code-block">
                    <pre>
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class RequestLogging
{
    public function handle(Request $request, Closure $next): Response
    {
        // Gerar ID único para a requisição
        $requestId = $request->header('X-Request-ID') ?? (string) Str::uuid();
        $request->headers->set('X-Request-ID', $requestId);

        // Preparar contexto básico
        $context = [
            'request_id' => $requestId,
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ];

        // Adicionar informações do usuário se autenticado
        if (auth()->check()) {
            $context['user_id'] = auth()->id();
            $context['user_email'] = auth()->user()->email;
        }

        // Log de início da requisição
        Log::info('Requisição HTTP recebida', $context);

        // Medir tempo de resposta
        $startTime = microtime(true);

        // Processar a requisição
        $response = $next($request);

        // Calcular tempo de processamento
        $duration = round((microtime(true) - $startTime) * 1000, 2);

        // Adicionar informações da resposta ao contexto
        $context['duration_ms'] = $duration;
        $context['status_code'] = $response->getStatusCode();

        // Se for erro 4xx ou 5xx, usar nível warning ou error
        if ($response->getStatusCode() >= 500) {
            Log::error('Erro interno do servidor na requisição', $context);
        } elseif ($response->getStatusCode() >= 400) {
            Log::warning('Erro de cliente na requisição', $context);
        } else {
            Log::info('Requisição HTTP completada', $context);
        }

        // Adicionar header com Request ID na resposta
        $response->headers->set('X-Request-ID', $requestId);

        return $response;
    }
}
        </pre>
                </div>

                <h3>11.5. Logging com OpenTelemetry</h3>
                <div class="code-block">
                    <pre>
namespace App\Http\Controllers;

use App\Services\ProductService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use OpenTelemetry\API\Trace\SpanKind;
use OpenTelemetry\API\Trace\StatusCode;

class ProductController extends Controller
{
    public function __construct(
        protected readonly ProductService $productService
    ) {}

    public function index(Request $request): JsonResponse
    {
        // Obter o tracer do container
        $tracer = app(\OpenTelemetry\API\Trace\TracerInterface::class);
        
        // Criar um span para esta operação
        $span = $tracer->spanBuilder('list_products')
            ->setSpanKind(SpanKind::SERVER)
            ->startSpan();
            
        // Ativar o span no contexto atual
        $scope = $span->activate();
        
        try {
            // Adicionar atributos ao span
            $span->setAttribute('http.method', $request->method());
            $span->setAttribute('http.url', $request->fullUrl());
            
            // Parâmetros de paginação e filtros
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 15);
            $category = $request->get('category');
            
            $span->setAttribute('request.page', $page);
            $span->setAttribute('request.per_page', $perPage);
            $span->setAttribute('request.category', $category);
            
            // Log com contexto de trace
            Log::channel('opentelemetry')->info('Listando produtos', [
                'page' => $page,
                'per_page' => $perPage,
                'category' => $category
            ]);
            
            // Executar a operação
            $startTime = microtime(true);
            $products = $this->productService->listProducts($page, $perPage, $category);
            $duration = microtime(true) - $startTime;
            
            // Adicionar métricas ao span
            $span->setAttribute('products.count', $products->count());
            $span->setAttribute('products.total', $products->total());
            $span->setAttribute('operation.duration_ms', $duration * 1000);
            
            // Log de sucesso
            Log::channel('opentelemetry')->info('Produtos listados com sucesso', [
                'count' => $products->count(),
                'total' => $products->total(),
                'duration_ms' => round($duration * 1000, 2)
            ]);
            
            $span->setStatus(StatusCode::OK);
            
            return response()->json([
                'status' => 'success',
                'message' => 'Produtos listados com sucesso',
                'data' => $products
            ]);
            
        } catch (\Exception $e) {
            // Registrar erro no span
            $span->recordException($e);
            $span->setStatus(StatusCode::ERROR, $e->getMessage());
            
            // Log de erro
            Log::channel('opentelemetry')->error('Erro ao listar produtos', [
                'exception' => get_class($e),
                'message' => $e->getMessage()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao listar produtos'
            ], 500);
            
        } finally {
            // Finalizar o span e o escopo
            $scope->detach();
            $span->end();
        }
    }
}
        </pre>
                </div>
            </section>

            <section id="conclusao" class="manual-section">
                <h2>12. Conclusão</h2>

                <p>O sistema de logging é uma parte fundamental de qualquer aplicação robusta, fornecendo visibilidade,
                    facilitando o diagnóstico de problemas e contribuindo para a segurança e auditoria do sistema.</p>

                <p>O Laravel 12 introduz melhorias significativas no sistema de logging, incluindo suporte aprimorado
                    para logs estruturados, integração com OpenTelemetry, context enrichers e tipagem estrita. Estas
                    funcionalidades permitem criar um sistema de logging mais poderoso, flexível e fácil de usar.</p>

                <p>Ao seguir as práticas recomendadas neste manual, você estará implementando um sistema de logging que
                    não apenas registra informações úteis, mas também se integra com sistemas modernos de
                    observabilidade, facilitando o monitoramento, diagnóstico e análise da sua aplicação Laravel 12.</p>

                <div class="best-practice">
                    <h4>Próximos Passos</h4>
                    <ul>
                        <li>Implementar logging estruturado em JSON para facilitar a análise</li>
                        <li>Configurar canais especializados para diferentes tipos de logs</li>
                        <li>Integrar com sistemas de observabilidade como OpenTelemetry</li>
                        <li>Implementar context enrichers para adicionar informações comuns automaticamente</li>
                        <li>Configurar alertas para condições críticas de log</li>
                        <li>Revisar e atualizar as políticas de retenção de logs</li>
                    </ul>
                </div>
            </section>

            <footer class="manual-footer">
                <p>Manual de Logging - Laravel 12</p>
                <p>Última atualização: <span id="current-date"></span></p>
                <script>
                    document.getElementById('current-date').textContent = new Date().toLocaleDateString();
                </script>
            </footer>

        </section>
    </section>
</body>

</html>