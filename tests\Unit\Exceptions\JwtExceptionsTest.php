<?php

namespace Tests\Unit\Exceptions;

use App\Exceptions\JWT\InvalidRefreshTokenException;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class JwtExceptionsTest extends TestCase
{
    #[Test]
    public function invalid_refresh_token_exception_has_correct_message()
    {
        $exception = new InvalidRefreshTokenException('Token inválido');
        $this->assertEquals('Token inválido', $exception->getMessage());
    }

    // Outros testes de exceções...
}
