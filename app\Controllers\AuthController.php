<?php

namespace App\Controllers;

use App\Controllers\ControllerAbstract;
use App\Responses\ResponseInterface;
use App\Services\AuthService;
use App\Services\JwtService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;

/**
 * @OA\Tag(
 *     name="Autenticação",
 *     description="Endpoints para autenticação de usuários"
 * )
 */
class AuthController extends ControllerAbstract
{

    private AuthService $authService;
    private JwtService $jwtService;

    /**
     * Construtor com injeção de dependências
     */

    public function __construct(
        AuthService $service,
        ResponseInterface $response,
        JwtService $jwtService
    ) {
        parent::__construct($service, $response);
        $this->authService = $service;
        $this->jwtService = $jwtService;
    }


    /**
     * @OA\Post(
     *     path="/api/auth/login",
     *     summary="Autenticar usuário",
     *     tags={"Autenticação"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email", "password"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", format="password", example="senha123")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Login bem-sucedido",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="token", type="string", example="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."),
     *                 @OA\Property(property="user", ref="#/components/schemas/UserModel")
     *             ),
     *             @OA\Property(property="message", type="string", example="Login realizado com sucesso")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Credenciais inválidas",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Credenciais inválidas"),
     *             @OA\Property(property="errors", type="object", nullable=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Erro de validação",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Erro de validação"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 example={"email": {"O campo email é obrigatório."}}
     *             )
     *         )
     *     )
     * )
     */
    public function login(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'password' => 'required|string'
            ]);

            if ($validator->fails()) {
                throw new ValidationException($validator);
            }

            $tokens = $this->authService->authenticate($request->only(['email', 'password']));

            if ($tokens) {
                return $this->successResponse($tokens, 'Login realizado com sucesso');
            }

            return $this->handleException(new Exception('Credenciais inválidas'), 'Credenciais inválidas');
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/auth/register",
     *     summary="Registrar novo usuário",
     *     tags={"Autenticação"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name", "email", "password", "password_confirmation"},
     *             @OA\Property(property="name", type="string", example="João Silva"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", format="password", example="senha123"),
     *             @OA\Property(property="password_confirmation", type="string", format="password", example="senha123")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Usuário registrado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="token", type="string", example="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."),
     *                 @OA\Property(property="user", ref="#/components/schemas/UserModel")
     *             ),
     *             @OA\Property(property="message", type="string", example="Usuário registrado com sucesso")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Erro de validação",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Erro de validação"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 example={"email": {"O email já está em uso."}}
     *             )
     *         )
     *     )
     * )
     */
    public function register(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users',
                'password' => 'required|string|min:8|confirmed'
            ]);

            if ($validator->fails()) {
                throw new ValidationException($validator);
            }

            $user = $this->authService->register($validator->validated());

            if (!$user) {
                return $this->handleException(new Exception('Falha ao registrar usuário'));
            }

            $tokens = $this->jwtService->generateToken($user);

            if (!isset($tokens['access_token']) || !isset($tokens['refresh_token'])) {
                Log::error('Tokens não gerados corretamente', ['user_id' => $user->id]);
                return $this->handleException(new Exception('Erro ao gerar tokens de acesso'));
            }

            return $this->createdResponse([
                'user' => $user,
                'access_token' => $tokens['access_token'],
                'refresh_token' => $tokens['refresh_token']
            ], 'Usuário registrado com sucesso');
        } catch (Exception $e) {
            Log::error('Erro no registro', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->handleException($e);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/auth/logout",
     *     summary="Encerrar sessão",
     *     tags={"Autenticação"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Logout realizado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="message", type="string", example="Logout realizado com sucesso")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Não autenticado",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Não autenticado"),
     *             @OA\Property(property="errors", type="object", nullable=true)
     *         )
     *     )
     * )
     */
    public function logout()
    {
        try {
            $this->authService->logout();
            return $this->successResponse(null, 'Logout realizado com sucesso');
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/auth/me",
     *     summary="Obter dados do usuário autenticado",
     *     tags={"Autenticação"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Dados do usuário",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", ref="#/components/schemas/UserModel"),
     *             @OA\Property(property="message", type="string", example="Dados do usuário")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Não autenticado",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Não autenticado"),
     *             @OA\Property(property="errors", type="object", nullable=true)
     *         )
     *     )
     * )
     */
    public function me()
    {
        try {
            $user = $this->authService->getAuthenticatedUser();
            return $user
                ? $this->successResponse($user, 'Dados do usuário')
                : $this->handleException(new Exception('Usuário não autenticado'), 'Usuário não autenticado');
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }
}
