<?php

namespace App\Repositories;

use App\Models\UserModel;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class UserRepository extends RepositoryAbstract
{
    public function validateCredentials(array $credentials): bool
    {
        $user = $this->model->where('email', $credentials['email'])->first();
        return $user && Hash::check($credentials['password'], $user->password);
    }

    public function createUser(array $data): UserModel
    {
        $data['password'] = Hash::make($data['password']);
        return $this->model->create($data);
    }

    public function getCurrentUser(): ?UserModel
    {
        if ($user = Auth::user()) {
            return $this->model->find($user->id);
        }
        return null;
    }
}
