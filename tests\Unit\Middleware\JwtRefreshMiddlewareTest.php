<?php

namespace Tests\Unit\Middleware;

use App\Middleware\JwtRefreshMiddleware;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use PHPOpenSourceSaver\JWTAuth\Exceptions\TokenExpiredException;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Mockery;

class JwtRefreshMiddlewareTest extends TestCase
{
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new JwtRefreshMiddleware();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    #[Test]
    public function it_allows_valid_token()
    {
        // Mock para JWTAuth
        JWTAuth::shouldReceive('parseToken')
            ->once()
            ->andReturnSelf();

        JWTAuth::shouldReceive('authenticate')
            ->once()
            ->andReturn((object)['id' => 1]);

        // Criar request
        $request = new Request();

        // Criar callback next
        $next = function ($req) {
            return new Response('OK');
        };

        $response = $this->middleware->handle($request, $next);
        $this->assertEquals('OK', $response->getContent());
    }

    #[Test]
    public function it_refreshes_expired_token()
    {
        // Mock para JWTAuth - primeiro parseToken lança exceção
        JWTAuth::shouldReceive('parseToken')
            ->once()
            ->andReturnSelf();

        JWTAuth::shouldReceive('authenticate')
            ->once()
            ->andThrow(new TokenExpiredException('Token has expired'));

        // Mock para refresh
        JWTAuth::shouldReceive('parseToken')
            ->once()
            ->andReturnSelf();

        JWTAuth::shouldReceive('refresh')
            ->once()
            ->andReturn('new-token');

        // Criar request
        $request = new Request();

        // Criar callback next que retorna uma resposta mockada
        $mockResponse = Mockery::mock(Response::class);
        $mockResponse->shouldReceive('withHeaders')
            ->once()
            ->with([
                'Authorization' => 'Bearer new-token',
                'Access-Control-Expose-Headers' => 'Authorization'
            ])
            ->andReturnSelf();

        $next = function ($req) use ($mockResponse) {
            return $mockResponse;
        };

        // Execute
        $response = $this->middleware->handle($request, $next);

        // Não precisamos afirmar nada aqui - os mocks garantirão que tudo funcionou corretamente
        $this->assertTrue(true);
    }
}
