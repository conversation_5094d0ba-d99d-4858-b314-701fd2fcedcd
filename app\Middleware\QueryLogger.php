<?php

namespace App\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class QueryLogger
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Habilitar o log de consultas SQL
        DB::enableQueryLog();

        // Continuar com a requisição
        $response = $next($request);

        // Obter o log de consultas
        $queries = DB::getQueryLog();

        // Registrar cada consulta na debugbar e no log
        foreach ($queries as $index => $query) {
            $sql = $this->formatSqlQuery($query);
            Log::info('SQL Query: ' . $sql);
        }

        return $response;
    }

    /**
     * Formata uma consulta SQL com seus parâmetros
     *
     * @param array $query
     * @return string
     */
    private function formatSqlQuery(array $query): string
    {
        $sql = $query['query'];
        $bindings = $query['bindings'];

        // Substituir parâmetros no SQL
        foreach ($bindings as $binding) {
            $value = is_numeric($binding) ? $binding : "'" . $binding . "'";
            $sql = preg_replace('/\?/', $value, $sql, 1);
        }

        return $sql . ' [' . ($query['time'] / 1000) . ' segundos]';
    }
}
