<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| System2 API Routes
|--------------------------------------------------------------------------
|
| Rotas específicas para o microserviço System2
|
*/

Route::group([
    'middleware' => ['api', 'jwt.verify'],
], function () {
    // Rotas protegidas do System2

    // Exemplo:
    // Route::apiResource('recursos', RecursoController::class);
});

// Rotas públicas do System2 (sem autenticação)
Route::group([
    'middleware' => ['api'],
    'prefix' => 'public'
], function () {
    Route::get('/health', function () {
        return response()->json([
            'status' => 'healthy',
            'service' => 'system2',
            'timestamp' => now()->toIso8601String(),
        ]);
    });

    // Outras rotas públicas
});
