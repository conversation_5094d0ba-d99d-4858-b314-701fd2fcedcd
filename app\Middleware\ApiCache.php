<?php

namespace App\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class ApiCache
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  int  $ttl  Tempo de cache em minutos
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, int $ttl = 60): Response
    {
        // Não aplicar cache para métodos não-GET ou quando autenticado
        if (!$request->isMethod('GET') || auth()->check()) {
            return $next($request);
        }

        // Gerar chave de cache baseada na URL e parâmetros de consulta
        $cacheKey = 'api_cache:' . sha1($request->fullUrl());

        // Verificar se a resposta está em cache
        if (Cache::has($cacheKey)) {
            $cachedContent = Cache::get($cacheKey);

            return response()->json(
                $cachedContent['content'],
                $cachedContent['status']
            )->withHeaders([
                'X-Cache' => 'HIT',
                'Cache-Control' => 'public, max-age=' . ($ttl * 60)
            ]);
        }

        // Processar a requisição
        $response = $next($request);

        // Armazenar em cache apenas respostas bem-sucedidas
        if ($response->getStatusCode() === 200) {
            $content = json_decode($response->getContent(), true);

            Cache::put($cacheKey, [
                'content' => $content,
                'status' => $response->getStatusCode()
            ], now()->addMinutes($ttl));

            $response->headers->set('X-Cache', 'MISS');
            $response->headers->set('Cache-Control', 'public, max-age=' . ($ttl * 60));
        }

        return $response;
    }
}
