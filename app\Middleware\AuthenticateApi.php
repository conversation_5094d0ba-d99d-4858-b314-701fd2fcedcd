<?php

namespace App\Middleware;

use Closure;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AuthenticateApi
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            throw new AuthenticationException('Unauthenticated.');
        }

        // Adicionar informações do usuário ao cabeçalho da resposta
        $response = $next($request);

        // Adicionar ID do usuário ao cabeçalho X-User-ID
        if (auth()->id()) {
            $response->headers->set('X-User-ID', auth()->id());
        }

        return $response;
    }
}
