<?php

namespace App\Responses;

use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class ApiResponse implements ResponseInterface
{
    /* Métodos de Sucesso */
    public function success($data = null, string $message = null, int $status = null): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message ?? self::MSG_SUCCESS,
            'data' => $data
        ], $status ?? self::HTTP_OK);
    }

    public function created($data = null, string $message = null): JsonResponse
    {
        return $this->success($data, $message ?? self::MSG_CREATED, self::HTTP_CREATED);
    }

    public function noContent(string $message = null): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message ?? self::MSG_NO_CONTENT
        ], self::HTTP_NO_CONTENT);
    }

    /* Redirecionamentos */
    public function redirect(string $url, bool $permanent = false): JsonResponse
    {
        return response()->json([
            'success' => true,
            'redirect' => $url,
            'permanent' => $permanent
        ], $permanent ? self::HTTP_MOVED_PERMANENTLY : self::HTTP_FOUND);
    }

    /* Métodos de Erro do Cliente */
    public function badRequest(string $message = null, $errors = null): JsonResponse
    {
        return $this->errorResponse($message ?? self::MSG_BAD_REQUEST, $errors, self::HTTP_BAD_REQUEST);
    }

    public function unauthorized(string $message = null): JsonResponse
    {
        return $this->errorResponse($message ?? self::MSG_UNAUTHORIZED, null, self::HTTP_UNAUTHORIZED);
    }

    public function forbidden(string $message = null): JsonResponse
    {
        return $this->errorResponse($message ?? self::MSG_FORBIDDEN, null, self::HTTP_FORBIDDEN);
    }

    public function notFound(string $message = null): JsonResponse
    {
        return $this->errorResponse($message ?? self::MSG_NOT_FOUND, null, self::HTTP_NOT_FOUND);
    }

    public function methodNotAllowed(string $message = null): JsonResponse
    {
        return $this->errorResponse($message ?? self::MSG_METHOD_NOT_ALLOWED, null, self::HTTP_METHOD_NOT_ALLOWED);
    }

    public function validationError($errors, string $message = null): JsonResponse
    {
        return $this->errorResponse($message ?? self::MSG_VALIDATION_ERROR, $errors, self::HTTP_UNPROCESSABLE_ENTITY);
    }

    /* Métodos de Erro do Servidor */
    public function serverError(string $message = null): JsonResponse
    {
        return $this->errorResponse($message ?? self::MSG_SERVER_ERROR, null, self::HTTP_INTERNAL_SERVER_ERROR);
    }

    public function badGateway(string $message = null): JsonResponse
    {
        return $this->errorResponse($message ?? self::MSG_BAD_GATEWAY, null, self::HTTP_BAD_GATEWAY);
    }

    public function serviceUnavailable(string $message = null): JsonResponse
    {
        return $this->errorResponse($message ?? self::MSG_SERVICE_UNAVAILABLE, null, self::HTTP_SERVICE_UNAVAILABLE);
    }

    /* Paginação */
    public function paginated($items, int $total, int $perPage, int $currentPage): JsonResponse
    {
        $lastPage = ceil($total / $perPage);

        return response()->json([
            'success' => true,
            'data' => $items,
            'meta' => [
                'pagination' => [
                    'total' => $total,
                    'per_page' => $perPage,
                    'current_page' => $currentPage,
                    'last_page' => $lastPage,
                    'from' => (($currentPage - 1) * $perPage) + 1,
                    'to' => min($currentPage * $perPage, $total)
                ]
            ]
        ]);
    }

    /* Método auxiliar privado para respostas de erro */
    private function errorResponse(string $message, $errors, int $status): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $status);
    }
}
