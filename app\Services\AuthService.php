<?php

namespace App\Services;

use App\Repositories\UserRepository;
use App\Models\UserModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class AuthService extends ServiceAbstract
{
    private JwtService $jwtService;

    public function __construct(
        UserRepository $repository,
        JwtService $jwtService
    ) {
        parent::__construct($repository);
        $this->jwtService = $jwtService;
    }

    public function authenticate(array $credentials): ?array
    {
        Log::info('Tentativa de login iniciada', ['email' => $credentials['email']]);

        try {
            $user = $this->repository->getModel()->where('email', $credentials['email'])->first();

            if (!$user) {
                Log::warning('Usuário não encontrado', ['email' => $credentials['email']]);
                return null;
            }

            Log::info('Usuário encontrado', ['user_id' => $user->id]);

            if (!password_verify($credentials['password'], $user->password)) {
                Log::warning('Credenciais inválidas', ['user_id' => $user->id]);
                return null;
            }

            Log::info('Credenciais válidas', ['user_id' => $user->id]);

            $token = $this->jwtService->generateToken($user);

            if (!$token) {
                Log::error('Falha ao gerar token JWT', ['user_id' => $user->id]);
                return null;
            }

            Log::info('Token JWT gerado com sucesso', ['user_id' => $user->id]);

            return $token;
        } catch (\Exception $e) {
            Log::error('Erro durante autenticação', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    public function register(array $data): ?UserModel
    {
        try {
            $data['password'] = bcrypt($data['password']);
            return $this->repository->getModel()->create($data);
        } catch (\Exception $e) {
            Log::error('Erro ao registrar usuário', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    public function getAuthenticatedUser(): ?UserModel
    {
        return Auth::user();
    }

    public function logout(): void
    {
        $user = Auth::user();
        if ($user) {
            $this->jwtService->invalidateAllTokens($user->id);
        }
        Auth::logout();
    }
}
