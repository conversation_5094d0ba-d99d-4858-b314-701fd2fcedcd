<!DOCTYPE html>
<html lang="pt-br">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Manual de Implementação - Laravel 12</title>
        <link rel="stylesheet" href="css/manual.css" />
        <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
        <script>
            mermaid.initialize({ startOnLoad: true });
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                flowchart: {
                    htmlLabels: true,
                    curve: 'basis'
                },
                fontSize: 18
            });
        </script>
    </head>

    <body>
        <div class="hero-section">
            <h1>Manual de Implementação</h1>
            <p>
                Guia completo para implementação de APIs RESTful com Laravel 12, abordando boas práticas, organização de código e padrões de desenvolvimento
            </p>
        </div>

        <div class="container">
            <div class="manual-content">
                <div class="manual-header">
                    <div class="manual-metadata">
                        <strong>Versão:</strong> 1.0.0 |
                        <strong>Última atualização:</strong> Julho 2023 |
                        <strong>Autor:</strong> Equipe de Desenvolvimento
                    </div>
                    <div class="tags">
                        <span class="tag">Implementação</span>
                        <span class="tag">REST</span>
                        <span class="tag">API</span>
                        <span class="tag">Laravel 12</span>
                        <span class="tag">PHP 8.2</span>
                    </div>
                </div>

                <div class="manual-toc">
                    <h3>Índice</h3>
                    <ul>
                        <li><a href="#introducao">1. Introdução</a></li>
                        <li>
                            <a href="#estrutura-projeto">2. Estrutura do Projeto</a>
                            <ul>
                                <li><a href="#organizacao-pastas">2.1. Organização de Pastas</a></li>
                                <li><a href="#namespaces">2.2. Namespaces</a></li>
                                <li><a href="#autoloading">2.3. Autoloading</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#implementacao-camadas">3. Implementação das Camadas</a>
                            <ul>
                                <li><a href="#impl-apresentacao">3.1. Camada de Apresentação</a></li>
                                <li><a href="#impl-aplicacao">3.2. Camada de Aplicação</a></li>
                                <li><a href="#impl-dominio">3.3. Camada de Domínio</a></li>
                                <li><a href="#impl-infraestrutura">3.4. Camada de Infraestrutura</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#rotas-api">4. Rotas e Endpoints</a>
                            <ul>
                                <li><a href="#organizacao-rotas">4.1. Organização de Rotas</a></li>
                                <li><a href="#versionamento-api">4.2. Versionamento de API</a></li>
                                <li><a href="#grupos-rotas">4.3. Grupos de Rotas</a></li>
                                <li><a href="#nomenclatura-rotas">4.4. Nomenclatura de Rotas</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#controllers">5. Controllers</a>
                            <ul>
                                <li><a href="#controller-crud">5.1. Controller CRUD</a></li>
                                <li><a href="#controller-resource">5.2. Resource Controllers</a></li>
                                <li><a href="#controller-single-action">5.3. Single Action Controllers</a></li>
                                <li><a href="#controller-invokable">5.4. Invokable Controllers</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#services">6. Services</a>
                            <ul>
                                <li><a href="#service-crud">6.1. Service CRUD</a></li>
                                <li><a href="#service-business-logic">6.2. Lógica de Negócio</a></li>
                                <li><a href="#service-events">6.3. Disparando Events</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#repositories">7. Repositories</a>
                            <ul>
                                <li><a href="#repository-crud">7.1. Repository CRUD</a></li>
                                <li><a href="#repository-queries">7.2. Queries Complexas</a></li>
                                <li><a href="#repository-cache">7.3. Cache em Repositories</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#models">8. Models e Relacionamentos</a>
                            <ul>
                                <li><a href="#model-attributes">8.1. Atributos e Casts</a></li>
                                <li><a href="#model-relationships">8.2. Relacionamentos</a></li>
                                <li><a href="#model-scopes">8.3. Query Scopes</a></li>
                                <li><a href="#model-observers">8.4. Observers</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#migrations">9. Migrations e Seeders</a>
                            <ul>
                                <li><a href="#migration-best-practices">9.1. Boas Práticas</a></li>
                                <li><a href="#migration-relationships">9.2. Relacionamentos</a></li>
                                <li><a href="#seeders">9.3. Seeders e Factories</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#autenticacao-implementacao">10. Autenticação</a>
                            <ul>
                                <li><a href="#jwt-implementacao">10.1. JWT</a></li>
                                <li><a href="#sanctum">10.2. Laravel Sanctum</a></li>
                                <li><a href="#oauth">10.3. OAuth 2.0</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#validacao-implementacao">11. Validação</a>
                            <ul>
                                <li><a href="#form-requests-implementacao">11.1. Form Requests</a></li>
                                <li><a href="#custom-rules">11.2. Regras Customizadas</a></li>
                            </ul>
                        </li>
                        <li>
                            <a href="#testes">12. Testes</a>
                            <ul>
                                <li><a href="#unit-tests">12.1. Testes Unitários</a></li>
                                <li><a href="#feature-tests">12.2. Testes de Feature</a></li>
                                <li><a href="#test-database">12.3. Banco de Dados para Testes</a></li>
                            </ul>
                        </li>
                        <li><a href="#conclusao-implementacao">13. Conclusão</a></li>
                    </ul>
                </div>

                <div class="manual-section" id="estrutura-projeto">
    <h2>2. Estrutura do Projeto</h2>
    <p>
        A estrutura de diretórios do projeto é fundamental para organizar o código de acordo com a arquitetura em camadas e o padrão de microsserviços. Nossa estrutura é projetada para suportar múltiplos sistemas, cada um contendo vários microsserviços.
    </p>

    <div class="manual-section" id="organizacao-pastas">
        <h3>2.1. Organização de Pastas</h3>
        <p>
            Nossa arquitetura segue uma estrutura onde os sistemas e microsserviços são organizados dentro do diretório <code>app/Http</code>, enquanto os componentes globais e abstratos estão na raiz do diretório <code>app</code>.
        </p>

        <div class="code-block-header">Estrutura de Diretórios Principal</div>
<pre><code>app/
├── Controllers/                   # Controllers globais
├── Middleware/                    # Middlewares globais
├── Requests/                      # Form Requests globais
├── Resources/                     # API Resources globais
├── Models/                        # Models globais compartilhados
├── Services/                      # Services base e abstratos
│   └── ServiceInterface.php       # Interface base para todos os services
├── Repositories/                  # Repositories base e abstratos
│   └── RepositoryInterface.php    # Interface base para todos os repositories
├── Responses/                     # Classes de resposta
│   ├── ResponseInterface.php      # Interface para respostas
│   └── ApiResponse.php            # Implementação padrão de resposta
├── Exceptions/                    # Exceções customizadas
├── Providers/                     # Service Providers
│   └── AppBindingsProvider.php    # Provider para registrar bindings automáticos
│
└── Http/                          # Diretório para sistemas e microsserviços
    ├── SystemA/                   # Sistema A
    │   ├── MicroserviceX/         # Microsserviço X do Sistema A
    │   │   ├── Controllers/       # Controllers específicos do microsserviço
    │   │   ├── Services/          # Services específicos do microsserviço
    │   │   ├── Repositories/      # Repositories específicos do microsserviço
    │   │   ├── Models/            # Models específicos do microsserviço
    │   │   ├── Requests/          # Form Requests específicos do microsserviço
    │   │   └── Resources/         # API Resources específicos do microsserviço
    │   │
    │   └── MicroserviceY/         # Microsserviço Y do Sistema A
    │       ├── Controllers/
    │       ├── Services/
    │       └── ...
    │
    └── SystemB/                   # Sistema B
        ├── MicroserviceZ/         # Microsserviço Z do Sistema B
        │   ├── Controllers/
        │   ├── Services/
        │   └── ...
        │
        └── ...
</code></pre>

        <div class="diagram">
            <div class="mermaid">
            graph TD
                classDef system fill:#f9d5e5,stroke:#333,stroke-width:1px;
                classDef microservice fill:#d0f0c0,stroke:#333,stroke-width:1px;
                classDef component fill:#b5dcff,stroke:#333,stroke-width:1px;
                classDef global fill:#ffcc99,stroke:#333,stroke-width:1px;

                App[app/] --> Controllers
                App --> Middleware
                App --> Requests
                App --> Resources
                App --> Models
                App --> Services
                App --> Repositories
                App --> Responses
                App --> Exceptions
                App --> Providers
                App --> Http

                Http[Http/] --> SystemA
                Http --> SystemB

                SystemA[SystemA/] --> MicroserviceX
                SystemA --> MicroserviceY

                MicroserviceX[MicroserviceX/] --> XControllers[Controllers/]
                MicroserviceX --> XServices[Services/]
                MicroserviceX --> XRepositories[Repositories/]
                MicroserviceX --> XModels[Models/]
                MicroserviceX --> XRequests[Requests/]
                MicroserviceX --> XResources[Resources/]

                SystemB[SystemB/] --> MicroserviceZ
                MicroserviceZ[MicroserviceZ/] --> ZComponents[...]

                class SystemA,SystemB system;
                class MicroserviceX,MicroserviceY,MicroserviceZ microservice;
                class XControllers,XServices,XRepositories,XModels,XRequests,XResources,ZComponents component;
                class Controllers,Middleware,Requests,Resources,Models,Services,Repositories,Responses,Exceptions,Providers,Http global;
            </div>
            <p class="diagram-caption">Figura 1: Visão geral da estrutura de diretórios do projeto</p>
        </div>

        <p>
            Esta estrutura permite uma clara separação entre diferentes sistemas e seus microsserviços, facilitando a manutenção e escalabilidade do projeto. Os componentes globais e abstratos estão na raiz do diretório <code>app</code>, enquanto os componentes específicos de cada sistema e microsserviço estão organizados dentro de <code>app/Http</code>.
        </p>

        <div class="tip">
            <p>
                Mantenha a consistência na organização de arquivos entre os diferentes microsserviços. Isso facilita a navegação e manutenção do código por toda a equipe.
            </p>
        </div>
    </div>

<div class="manual-section" id="namespaces">
    <h3>2.2. Namespaces</h3>
    <p>
        Os namespaces seguem a estrutura de diretórios do projeto, facilitando a localização e importação de classes. Cada componente tem seu namespace específico baseado em sua localização na estrutura de diretórios.
    </p>

    <div class="code-block-header">Exemplos de Namespaces</div>
<pre><code>// Controllers globais
namespace App\Controllers;

// Models globais
namespace App\Models;

// Services base
namespace App\Services;

// Repositories base
namespace App\Repositories;

// Controllers específicos de um microsserviço
namespace App\Http\SystemA\MicroserviceX\Controllers;

// Services específicos de um microsserviço
namespace App\Http\SystemA\MicroserviceX\Services;

// Repositories específicos de um microsserviço
namespace App\Http\SystemA\MicroserviceX\Repositories;

// Models específicos de um microsserviço
namespace App\Http\SystemA\MicroserviceX\Models;
</code></pre>

    <p>
        Seguir uma convenção consistente de namespaces facilita a autocompleção em IDEs e torna o código mais organizado e previsível.
    </p>
</div>

<div class="manual-section" id="autoloading">
    <h3>2.3. Autoloading</h3>
    <p>
        O Laravel utiliza o Composer para autoloading de classes. O arquivo <code>composer.json</code> já está configurado para carregar automaticamente as classes no diretório <code>app/</code> com o namespace <code>App</code>.
    </p>

    <p>
        Nossa estrutura de microsserviços aproveita o autoloading padrão do Laravel, não sendo necessária nenhuma configuração adicional para os namespaces dentro do diretório <code>app/</code>.
    </p>

    <div class="code-block-header">Configuração de Autoloading no composer.json</div>
<pre><code>{
    "autoload": {
        "psr-4": {
            "App\\": "app/"
        },
        "files": [
            "app/Support/Helpers/general.php"
        ]
    }
}
</code></pre>

    <div class="note">
        <p>
            Se você precisar adicionar diretórios ou namespaces personalizados, pode configurá-los no arquivo <code>composer.json</code> e executar <code>composer dump-autoload</code> para atualizar as configurações de autoloading.
        </p>
    </div>
</div>

<div class="manual-section" id="implementacao-camadas">
    <h2>3. Implementação das Camadas</h2>
    <p>
        Esta seção detalha a implementação prática de cada camada da arquitetura em nosso sistema de microsserviços, com exemplos de código e diretrizes específicas.
    </p>

    <div class="manual-section" id="impl-apresentacao">
        <h3>3.1. Camada de Apresentação</h3>
        <p>
            A camada de apresentação é responsável pela interface com o cliente, recebendo requisições HTTP e retornando respostas. Em nossa arquitetura, esta camada é implementada principalmente através de Controllers, Form Requests e Resources.
        </p>

        <h4>3.1.1. Controllers</h4>
        <p>
            Os controllers devem ser enxutos, delegando a lógica de negócio para os services. Eles são responsáveis por:
        </p>
        <ul>
            <li>Receber requisições HTTP</li>
            <li>Validar dados de entrada (via Form Requests)</li>
            <li>Delegar processamento para os services</li>
            <li>Retornar respostas formatadas</li>
        </ul>

        <div class="code-block-header">Exemplo de Controller em um Microsserviço</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Controllers;

use App\Controllers\Controller;
use App\Http\SystemA\MicroserviceX\Requests\StoreUserRequest;
use App\Http\SystemA\MicroserviceX\Requests\UpdateUserRequest;
use App\Http\SystemA\MicroserviceX\Resources\UserResource;
use App\Services\ServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Http\Response;

class UserController extends Controller
{
    protected $service;

    public function __construct(ServiceInterface $service)
    {
        $this->service = $service;
    }

    public function index(Request $request)
    {
        $result = $this->service->index($request->all());
        return UserResource::collection($result['data']);
    }

    public function store(StoreUserRequest $request)
    {
        $result = $this->service->store($request->validated());
        return new UserResource($result['data']);
    }

    public function show($id)
    {
        $result = $this->service->show($id);
        return new UserResource($result['data']);
    }

    public function update(UpdateUserRequest $request, $id)
    {
        $result = $this->service->update($id, $request->validated());
        return new UserResource($result['data']);
    }

    public function destroy($id)
    {
        $this->service->destroy($id);
        return response()->noContent();
    }
}
</code></pre>

        <h4>3.1.2. Form Requests</h4>
        <p>
            Form Requests encapsulam a lógica de validação, separando-a dos controllers. Eles são responsáveis por:
        </p>
        <ul>
            <li>Definir regras de validação</li>
            <li>Personalizar mensagens de erro</li>
            <li>Verificar autorização</li>
            <li>Pré-processar dados de entrada</li>
        </ul>

        <div class="code-block-header">Exemplo de Form Request em um Microsserviço</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreUserRequest extends FormRequest
{
    public function authorize()
    {
        return true; // ou lógica de autorização
    }

    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'role_id' => ['required', 'exists:roles,id'],
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'O nome é obrigatório',
            'email.unique' => 'Este e-mail já está em uso',
            'password.min' => 'A senha deve ter pelo menos 8 caracteres',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'name' => trim($this->name),
            'email' => strtolower(trim($this->email)),
        ]);
    }
}
</code></pre>

        <h4>3.1.3. Resources</h4>
        <p>
            Resources transformam modelos em respostas JSON estruturadas. Eles são responsáveis por:
        </p>
        <ul>
            <li>Definir a estrutura da resposta JSON</li>
            <li>Incluir ou excluir atributos</li>
            <li>Formatar dados</li>
            <li>Incluir relacionamentos</li>
        </ul>

        <div class="code-block-header">Exemplo de Resource em um Microsserviço</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
            'role' => new RoleResource($this->whenLoaded('role')),
            'permissions' => PermissionResource::collection($this->whenLoaded('permissions')),
        ];
    }
}
</code></pre>
    </div>

    <div class="manual-section" id="impl-aplicacao">
        <h3>3.2. Camada de Aplicação</h3>
        <p>
            A camada de aplicação contém a lógica de negócio e orquestra o fluxo de dados entre a camada de apresentação e a camada de domínio. Em nossa arquitetura, esta camada é implementada principalmente através de Services.
        </p>

        <h4>3.2.1. Services</h4>
        <p>
            Os services encapsulam a lógica de negócio da aplicação. Eles são responsáveis por:
        </p>
        <ul>
            <li>Implementar casos de uso</li>
            <li>Orquestrar operações entre múltiplas entidades</li>
            <li>Validar regras de negócio</li>
            <li>Disparar eventos</li>
        </ul>

        <div class="code-block-header">Interface Base de Service</div>
<pre><code>namespace App\Services;

interface ServiceInterface
{
    public function index(array $params = []);
    public function show($id);
    public function store(array $data);
    public function update($id, array $data);
    public function destroy($id);
}
</code></pre>

        <div class="code-block-header">Exemplo de Service Abstrato</div>
<pre><code>namespace App\Services;

use App\Repositories\RepositoryInterface;
use App\Responses\ResponseInterface;

abstract class ServiceAbstract implements ServiceInterface
{
    protected $repository;
    protected $response;

    public function __construct(RepositoryInterface $repository, ResponseInterface $response)
    {
        $this->repository = $repository;
        $this->response = $response;
    }

    public function index(array $params = [])
    {
        $data = $this->repository->paginate($params);
        return $this->response->success('Resources retrieved successfully', $data);
    }

    public function show($id)
    {
        $data = $this->repository->find($id);

        if (!$data) {
            return $this->response->notFound('Resource not found');
        }

        return $this->response->success('Resource retrieved successfully', $data);
    }

    public function store(array $data)
    {
        $resource = $this->repository->create($data);
        return $this->response->created('Resource created successfully', $resource);
    }

    public function update($id, array $data)
    {
        $resource = $this->repository->find($id);

        if (!$resource) {
            return $this->response->notFound('Resource not found');
        }

        $updated = $this->repository->update($id, $data);
        return $this->response->success('Resource updated successfully', $updated);
    }

    public function destroy($id)
    {
        $resource = $this->repository->find($id);

        if (!$resource) {
            return $this->response->notFound('Resource not found');
        }

        $this->repository->delete($id);
        return $this->response->success('Resource deleted successfully');
    }
}
</code></pre>

        <div class="code-block-header">Exemplo de Service em um Microsserviço</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Services;

use App\Services\ServiceAbstract;
use App\Repositories\RepositoryInterface;
use App\Responses\ResponseInterface;
use Illuminate\Support\Facades\Hash;

class UserService extends ServiceAbstract
{
    public function __construct(RepositoryInterface $repository, ResponseInterface $response)
    {
        parent::__construct($repository, $response);
    }

    public function store(array $data)
    {
        // Processar dados antes de persistir
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Chamar método pai para persistir dados
        $result = parent::store($data);

        // Processar após persistência (ex: atribuir roles)
        if ($result['success'] && isset($data['role_id'])) {
            $user = $result['data'];
            $user->roles()->attach($data['role_id']);
            $result['data'] = $user->fresh(['roles']);
        }

        return $result;
    }

    public function update($id, array $data)
    {
        // Processar dados antes de atualizar
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Chamar método pai para atualizar dados
        $result = parent::update($id, $data);

        // Processar após atualização (ex: atualizar roles)
        if ($result['success'] && isset($data['role_id'])) {
            $user = $result['data'];
            $user->roles()->sync([$data['role_id']]);
            $result['data'] = $user->fresh(['roles']);
        }

        return $result;
    }

    // Métodos específicos do serviço
    public function getUsersByRole($roleId)
    {
        $users = $this->repository->findByField('role_id', $roleId);
        return $this->response->success('Users retrieved successfully', $users);
    }
}
</code></pre>
    </div>

    <div class="manual-section" id="impl-dominio">
        <h3>3.3. Camada de Domínio</h3>
        <p>
            A camada de domínio contém as entidades de negócio e regras de negócio relacionadas às entidades. Em nossa arquitetura, esta camada é implementada principalmente através de Models.
        </p>

        <h4>3.3.1. Models</h4>
        <p>
            Os models representam entidades de negócio e encapsulam regras de negócio relacionadas à entidade. Eles podem ser específicos de um microsserviço ou compartilhados entre múltiplos microsserviços.
        </p>

        <div class="code-block-header">Exemplo de Model Global</div>
<pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'price',
        'category_id',
        'is_active'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    // Relacionamentos
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    // Regras de negócio encapsuladas no modelo
    public function isAvailableForPurchase(): bool
    {
        return $this->is_active && $this->stock > 0;
    }

    // Escopo de consulta
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
</code></pre>

        <div class="code-block-header">Exemplo de Model Específico de um Microsserviço</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SpecializedModel extends Model
{
    use HasFactory;

    protected $table = 'specialized_table';

    protected $fillable = [
        'name',
        'specific_attribute',
        'configuration'
    ];

    protected $casts = [
        'configuration' => 'array',
    ];

    // Métodos específicos deste modelo
    public function processConfiguration()
    {
        // Lógica específica para processar a configuração
    }
}
</code></pre>

        <h4>3.3.2. Enums</h4>
        <p>
            Enums representam conjuntos fixos de valores relacionados ao domínio. Eles são úteis para representar estados, tipos e outras constantes do domínio.
        </p>

        <div class="code-block-header">Exemplo de Enum</div>
<pre><code>namespace App\Enums;

enum OrderStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case CANCELLED = 'cancelled';
    case REFUNDED = 'refunded';

    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pendente',
            self::PROCESSING => 'Em Processamento',
            self::COMPLETED => 'Concluído',
            self::CANCELLED => 'Cancelado',
            self::REFUNDED => 'Reembolsado',
        };
    }

    public function isFinalized(): bool
    {
        return in_array($this, [self::COMPLETED, self::CANCELLED, self::REFUNDED]);
    }
}
</code></pre>
    </div>

    <div class="manual-section" id="impl-infraestrutura">
        <h3>3.4. Camada de Infraestrutura</h3>
        <p>
            A camada de infraestrutura implementa o acesso a recursos externos como banco de dados, serviços de e-mail, cache, etc. Em nossa arquitetura, esta camada é implementada principalmente através de Repositories.
        </p>

        <h4>3.4.1. Repositories</h4>
        <p>
            Os repositories abstraem o acesso a dados, permitindo a substituição da fonte de dados sem afetar o restante da aplicação. Eles são responsáveis por:
        </p>
        <ul>
            <li>Implementar acesso a dados</li>
            <li>Abstrair detalhes de persistência</li>
            <li>Implementar queries complexas</li>
            <li>Gerenciar transações</li>
        </ul>

        <div class="code-block-header">Interface Base de Repository</div>
<pre><code>namespace App\Repositories;

interface RepositoryInterface
{
    public function all(array $columns = ['*']);
    public function find($id, array $columns = ['*']);
    public function findByField($field, $value, array $columns = ['*']);
    public function create(array $data);
    public function update($id, array $data);
    public function delete($id);
    public function paginate($perPage = 15, array $columns = ['*']);
}
</code></pre>

        <div class="code-block-header">Exemplo de Repository Abstrato</div>
<pre><code>namespace App\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

abstract class RepositoryAbstract implements RepositoryInterface
{
    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function all(array $columns = ['*']): Collection
    {
        return $this->model->all($columns);
    }

    public function find($id, array $columns = ['*'])
    {
        return $this->model->find($id, $columns);
    }

    public function findByField($field, $value, array $columns = ['*']): Collection
    {
        return $this->model->where($field, $value)->get($columns);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update($id, array $data)
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }
        $model->update($data);
        return $model;
    }

    public function delete($id): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }
        return $model->delete();
    }

    public function paginate($perPage = 15, array $columns = ['*']): LengthAwarePaginator
    {
        return $this->model->paginate($perPage, $columns);
    }
}
</code></pre>

        <div class="code-block-header">Exemplo de Repository em um Microsserviço</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Repositories;

use App\Repositories\RepositoryAbstract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class UserRepository extends RepositoryAbstract
{
    public function __construct(Model $model)
    {
        parent::__construct($model);
    }

    // Métodos específicos do repositório
    public function findByEmail(string $email)
    {
        return $this->model->where('email', $email)->first();
    }

    public function getUsersWithRole(string $roleName): Collection
    {
        return $this->model->whereHas('roles', function($query) use ($roleName) {
            $query->where('name', $roleName);
        })->get();
    }

    public function getActiveUsers(): Collection
    {
        return $this->model->where('is_active', true)->get();
    }
}
</code></pre>

        <h4>3.4.2. Responses</h4>
        <p>
            As classes de resposta padronizam o formato das respostas da API, garantindo consistência em toda a aplicação.
        </p>

        <div class="code-block-header">Interface de Response</div>
<pre><code>namespace App\Responses;

interface ResponseInterface
{
    public function success(string $message, $data = null, int $statusCode = 200);
    public function created(string $message, $data = null);
    public function error(string $message, $errors = null, int $statusCode = 400);
    public function notFound(string $message = 'Resource not found');
    public function unauthorized(string $message = 'Unauthorized');
    public function forbidden(string $message = 'Forbidden');
    public function validationError(string $message = 'Validation failed', $errors = null);
}
</code></pre>

        <div class="code-block-header">Implementação de Response</div>
<pre><code>namespace App\Responses;

use Illuminate\Http\JsonResponse;

class ApiResponse implements ResponseInterface
{
    public function success(string $message, $data = null, int $statusCode = 200): array
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return $response;
    }

    public function created(string $message, $data = null): array
    {
        return $this->success($message, $data, 201);
    }

    public function error(string $message, $errors = null, int $statusCode = 400): array
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return $response;
    }

    public function notFound(string $message = 'Resource not found'): array
    {
        return $this->error($message, null, 404);
    }

    public function unauthorized(string $message = 'Unauthorized'): array
    {
        return $this->error($message, null, 401);
    }

    public function forbidden(string $message = 'Forbidden'): array
    {
        return $this->error($message, null, 403);
    }

    public function validationError(string $message = 'Validation failed', $errors = null): array
    {
        return $this->error($message, $errors, 422);
    }
}
</code></pre>

 <h4>3.4.3. Registro Automático de Bindings</h4>
<p>
    Para facilitar a injeção de dependências, utilizamos um Service Provider que registra automaticamente os bindings entre interfaces e implementações com base na estrutura de diretórios.
</p>

<div class="code-block-header">AppBindingsProvider.php</div>
<pre><code>namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;

class AppBindingsProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Registrar bindings base
        $this->registerBaseBindings();

        // Registrar bindings para sistemas e microserviços
        $this->registerSystemsBindings();
    }

    /**
     * Registra os bindings base da aplicação
     */
    protected function registerBaseBindings()
    {
        // Binding para ResponseInterface
        $this->app->bind(
            \App\Responses\ResponseInterface::class,
            \App\Responses\ApiResponse::class
        );

        // Outros bindings base aqui...
    }

    /**
     * Registra os bindings para todos os sistemas
     */
    protected function registerSystemsBindings()
    {
        // Caminho base para os sistemas
        $basePath = app_path('Http');

        // Verificar se o diretório existe
        if (!File::isDirectory($basePath)) {
            return;
        }

        // Obter todos os diretórios de sistemas
        $systems = File::directories($basePath);

        foreach ($systems as $systemPath) {
            $systemName = basename($systemPath);

            // Pular diretórios que não são sistemas (como Controllers, Middleware, etc)
            if (in_array($systemName, ['Controllers', 'Middleware', 'Requests', 'Resources'])) {
                continue;
            }

            $this->registerMicroservicesForSystem($systemName);
        }
    }

    /**
     * Registra os microserviços para um sistema específico
     *
     * @param string $systemName Nome do sistema
     */
    protected function registerMicroservicesForSystem($systemName)
    {
        $systemPath = app_path("Http/{$systemName}");

        if (!File::isDirectory($systemPath)) {
            return;
        }

        // Obter todos os microserviços do sistema
        $microservices = File::directories($systemPath);

        foreach ($microservices as $microservicePath) {
            $microserviceName = basename($microservicePath);
            $this->registerMicroserviceComponents($systemName, $microserviceName);
        }
    }

    /**
     * Registra os componentes de um microserviço específico
     *
     * @param string $systemName Nome do sistema
     * @param string $microserviceName Nome do microserviço
     */
    protected function registerMicroserviceComponents($systemName, $microserviceName)
    {
        // Registrar Controllers
        $this->registerControllers($systemName, $microserviceName);

        // Registrar Services
        $this->registerServices($systemName, $microserviceName);

        // Registrar Repositories
        $this->registerRepositories($systemName, $microserviceName);
    }

    /**
     * Registra os controllers de um microserviço
     *
     * @param string $systemName Nome do sistema
     * @param string $microserviceName Nome do microserviço
     */
    protected function registerControllers($systemName, $microserviceName)
    {
        $controllerPath = app_path("Http/{$systemName}/{$microserviceName}/Controllers");

        if (!File::isDirectory($controllerPath)) {
            return;
        }

        $controllers = $this->getPhpClasses($controllerPath);

        foreach ($controllers as $controller) {
            // Extrair o nome base do controller (sem namespace)
            $className = class_basename($controller);

            // Determinar o serviço correspondente
            $serviceName = str_replace('Controller', 'Service', $className);
            $serviceClass = "App\\Http\\{$systemName}\\{$microserviceName}\\Services\\{$serviceName}";

            // Verificar se o serviço existe
            if (class_exists($serviceClass)) {
                // Binding contextual para injetar o serviço correto no controller
                $this->app->when($controller)
                    ->needs(\App\Services\ServiceInterface::class)
                    ->give($serviceClass);
            }
        }
    }

    /**
     * Registra os services de um microserviço
     *
     * @param string $systemName Nome do sistema
     * @param string $microserviceName Nome do microserviço
     */
    protected function registerServices($systemName, $microserviceName)
    {
        $servicePath = app_path("Http/{$systemName}/{$microserviceName}/Services");

        if (!File::isDirectory($servicePath)) {
            return;
        }

        $services = $this->getPhpClasses($servicePath);

        foreach ($services as $service) {
            // Extrair o nome base do serviço (sem namespace)
            $className = class_basename($service);

            // Determinar o repositório correspondente
            $repoName = str_replace('Service', 'Repository', $className);
            $repoClass = "App\\Http\\{$systemName}\\{$microserviceName}\\Repositories\\{$repoName}";

            // Verificar se o repositório existe
            if (class_exists($repoClass)) {
                // Binding contextual para injetar o repositório correto no serviço
                $this->app->when($service)
                    ->needs(\App\Repositories\RepositoryInterface::class)
                    ->give($repoClass);
            }
        }
    }

    /**
     * Registra os repositories de um microserviço
     *
     * @param string $systemName Nome do sistema
     * @param string $microserviceName Nome do microserviço
     */
    protected function registerRepositories($systemName, $microserviceName)
    {
        $repoPath = app_path("Http/{$systemName}/{$microserviceName}/Repositories");

        if (!File::isDirectory($repoPath)) {
            return;
        }

        $repositories = $this->getPhpClasses($repoPath);

        foreach ($repositories as $repository) {
            // Extrair o nome base do repositório (sem namespace)
            $className = class_basename($repository);

            // Determinar o modelo correspondente (já está usando o padrão NomeModel)
            $modelName = str_replace('Repository', 'Model', $className);
            $modelClass = "App\\Http\\{$systemName}\\{$microserviceName}\\Models\\{$modelName}";

            // Verificar se o modelo existe no namespace do microserviço
            if (!class_exists($modelClass)) {
                // Tentar encontrar o modelo no namespace App\Models
                $modelClass = "App\\Models\\{$modelName}";

                // Pular para o próximo repositório se o modelo não existir em nenhum dos lugares
                if (!class_exists($modelClass)) {
                    continue;
                }
            }

            // Binding contextual para injetar o modelo correto no repositório
            $this->app->when($repository)
                ->needs(\Illuminate\Database\Eloquent\Model::class)
                ->give($modelClass);
        }
    }

    /**
     * Obtém todas as classes PHP em um diretório
     *
     * @param string $path Caminho do diretório
     * @return array Array de nomes de classes com namespace
     */
    protected function getPhpClasses($path)
    {
        $classes = [];

        if (!File::isDirectory($path)) {
            return $classes;
        }

        $files = File::allFiles($path);

        foreach ($files as $file) {
            if ($file->getExtension() === 'php') {
                $className = $this->getClassNameFromFile($file->getPathname());
                if ($className) {
                    $classes[] = $className;
                }
            }
        }

        return $classes;
    }

    /**
     * Extrai o nome da classe com namespace de um arquivo PHP
     *
     * @param string $filePath Caminho do arquivo
     * @return string|null Nome da classe com namespace ou null
     */
    protected function getClassNameFromFile($filePath)
    {
        $content = file_get_contents($filePath);

        // Extrair namespace
        $namespace = null;
        if (preg_match('/namespace\s+([^;]+);/', $content, $matches)) {
            $namespace = $matches[1];
        }

        // Extrair nome da classe
        $className = null;
        if (preg_match('/class\s+(\w+)/', $content, $matches)) {
            $className = $matches[1];
        }

        if ($namespace && $className) {
            return $namespace . '\\' . $className;
        }

        return null;
    }
}
</code></pre>

<p>
    Este Service Provider automatiza o registro de bindings entre interfaces e implementações, seguindo convenções de nomenclatura. Por exemplo:
</p>

<ul>
    <li>Para cada Controller, ele injeta o Service correspondente</li>
    <li>Para cada Service, ele injeta o Repository correspondente</li>
    <li>Para cada Repository, ele injeta o Model correspondente</li>
</ul>

<p>
    Isso reduz significativamente a quantidade de código boilerplate necessário para configurar injeção de dependência, permitindo que o sistema encontre automaticamente as implementações corretas com base na estrutura de diretórios e convenções de nomenclatura.
</p>

<div class="tip">
    <p>
        Registre o AppBindingsProvider no arquivo <code>config/app.php</code> para que ele seja carregado automaticamente durante a inicialização da aplicação.
    </p>
</div>
</div>

<div class="manual-section" id="rotas-api">
    <h2>4. Rotas e Endpoints</h2>
    <p>
        A organização adequada das rotas é essencial para manter a API escalável e de fácil manutenção. Esta seção detalha as melhores práticas para definição e organização de rotas em nossa arquitetura de microsserviços.
    </p>

    <div class="manual-section" id="organizacao-rotas">
        <h3>4.1. Organização de Rotas</h3>
        <p>
            As rotas devem ser organizadas por sistema e microsserviço, refletindo a estrutura de diretórios do projeto. Cada sistema deve ter seu próprio arquivo de rotas, e dentro dele, as rotas devem ser agrupadas por microsserviço.
        </p>

        <div class="code-block-header">Estrutura de Arquivos de Rotas</div>
<pre><code>routes/
├── api.php                # Arquivo principal de rotas da API
├── web.php                # Rotas web (se necessário)
└── api/                   # Diretório para organizar rotas da API por sistema
    ├── system-a.php       # Rotas do Sistema A
    └── system-b.php       # Rotas do Sistema B
</code></pre>

        <div class="code-block-header">Exemplo de Organização em api.php</div>
<pre><code>use Illuminate\Support\Facades\Route;

// Carregar rotas de cada sistema
require __DIR__ . '/api/system-a.php';
require __DIR__ . '/api/system-b.php';

// Rotas globais da API
Route::get('health-check', function () {
    return response()->json(['status' => 'ok', 'version' => config('app.version')]);
});
</code></pre>

        <div class="code-block-header">Exemplo de Rotas para um Sistema (system-a.php)</div>
<pre><code>use Illuminate\Support\Facades\Route;

// Prefixo para todas as rotas do Sistema A
Route::prefix('system-a')->group(function () {

    // Rotas do Microsserviço X
    Route::prefix('microservice-x')->group(function () {
        // Rotas de recursos
        Route::apiResource('users', 'App\Http\SystemA\MicroserviceX\Controllers\UserController');
        Route::apiResource('products', 'App\Http\SystemA\MicroserviceX\Controllers\ProductController');

        // Rotas personalizadas
        Route::get('users/{user}/permissions', 'App\Http\SystemA\MicroserviceX\Controllers\UserController@permissions');
    });

    // Rotas do Microsserviço Y
    Route::prefix('microservice-y')->group(function () {
        Route::apiResource('orders', 'App\Http\SystemA\MicroserviceY\Controllers\OrderController');
        Route::post('orders/{order}/process', 'App\Http\SystemA\MicroserviceY\Controllers\OrderController@process');
    });
});
</code></pre>
    </div>

    <div class="manual-section" id="versionamento-api">
        <h3>4.2. Versionamento de API</h3>
        <p>
            O versionamento de API é essencial para manter a compatibilidade com clientes existentes enquanto evolui a API. Recomendamos o versionamento via URL, que é simples e explícito.
        </p>

        <div class="code-block-header">Exemplo de Versionamento via URL</div>
<pre><code>use Illuminate\Support\Facades\Route;

// API v1
Route::prefix('api/v1')->group(function () {
    require __DIR__ . '/api/v1/system-a.php';
    require __DIR__ . '/api/v1/system-b.php';
});

// API v2
Route::prefix('api/v2')->group(function () {
    require __DIR__ . '/api/v2/system-a.php';
    require __DIR__ . '/api/v2/system-b.php';
});
</code></pre>

        <p>
            Ao implementar uma nova versão da API, você pode:
        </p>
        <ul>
            <li>Criar novos controllers para a nova versão</li>
            <li>Estender controllers existentes e sobrescrever apenas os métodos que mudaram</li>
            <li>Usar o mesmo controller com lógica condicional baseada na versão</li>
        </ul>

        <div class="code-block-header">Exemplo de Controller com Versionamento</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Controllers\V2;

use App\Http\SystemA\MicroserviceX\Controllers\UserController as BaseUserController;
use App\Http\SystemA\MicroserviceX\Requests\V2\UpdateUserRequest;
use App\Http\SystemA\MicroserviceX\Resources\V2\UserResource;

class UserController extends BaseUserController
{
    // Sobrescrever apenas os métodos que mudaram na v2
    public function update(UpdateUserRequest $request, $id)
    {
        $result = $this->service->update($id, $request->validated());
        return new UserResource($result['data']);
    }

    // Novos endpoints na v2
    public function exportData($id)
    {
        $result = $this->service->exportUserData($id);
        return response()->json($result);
    }
}
</code></pre>
    </div>

    <div class="manual-section" id="grupos-rotas">
        <h3>4.3. Grupos de Rotas</h3>
        <p>
            Agrupar rotas permite aplicar middlewares, prefixos e namespaces comuns a um conjunto de rotas. Isso é especialmente útil para aplicar autenticação, rate limiting e outras restrições.
        </p>

        <div class="code-block-header">Exemplo de Grupos de Rotas</div>
<pre><code>use Illuminate\Support\Facades\Route;

// Grupo de rotas públicas
Route::prefix('api/v1/public')->group(function () {
    Route::get('products', 'App\Http\SystemA\MicroserviceX\Controllers\ProductController@index');
    Route::get('products/{id}', 'App\Http\SystemA\MicroserviceX\Controllers\ProductController@show');
});

// Grupo de rotas autenticadas
Route::prefix('api/v1')->middleware(['auth:sanctum'])->group(function () {

    // Rotas para usuários comuns
    Route::middleware(['role:user'])->group(function () {
        Route::apiResource('orders', 'App\Http\SystemA\MicroserviceY\Controllers\OrderController');
    });

    // Rotas para administradores
    Route::middleware(['role:admin'])->prefix('admin')->group(function () {
        Route::apiResource('users', 'App\Http\SystemA\MicroserviceX\Controllers\UserController');
    });
});
</code></pre>

        <p>
            Você também pode aplicar rate limiting para proteger sua API contra abusos:
        </p>

        <div class="code-block-header">Exemplo de Rate Limiting</div>
<pre><code>use Illuminate\Support\Facades\Route;

// Limitar a 60 requisições por minuto
Route::middleware(['throttle:60,1'])->group(function () {
    Route::get('products', 'App\Http\SystemA\MicroserviceX\Controllers\ProductController@index');
});

// Limitar a 5 requisições por minuto para endpoints sensíveis
Route::middleware(['throttle:5,1'])->group(function () {
    Route::post('password/reset', 'App\Http\Auth\Controllers\PasswordResetController@reset');
});
</code></pre>
    </div>

    <div class="manual-section" id="nomenclatura-rotas">
        <h3>4.4. Nomenclatura de Rotas</h3>
        <p>
            A nomenclatura consistente de rotas melhora a usabilidade e a manutenção da API. Siga estas diretrizes:
        </p>

        <ul>
            <li>Use substantivos no plural para recursos (ex: <code>/users</code>, <code>/products</code>)</li>
            <li>Use kebab-case para URLs com múltiplas palavras (ex: <code>/order-items</code>)</li>
            <li>Use verbos HTTP apropriados (GET, POST, PUT, DELETE) em vez de verbos na URL</li>
            <li>Para ações que não se encaixam no CRUD padrão, use verbos após o recurso (ex: <code>/orders/{id}/process</code>)</li>
        </ul>

        <div class="code-block-header">Exemplos de Nomenclatura de Rotas</div>
<pre><code>// Bom: Usa substantivos no plural e verbos HTTP apropriados
Route::get('users', 'UserController@index');                // Listar usuários
Route::post('users', 'UserController@store');               // Criar usuário
Route::get('users/{id}', 'UserController@show');            // Obter usuário
Route::put('users/{id}', 'UserController@update');          // Atualizar usuário
Route::delete('users/{id}', 'UserController@destroy');      // Excluir usuário

// Bom: Ações específicas após o recurso
Route::post('orders/{id}/process', 'OrderController@process');
Route::get('users/{id}/permissions', 'UserController@permissions');

// Ruim: Verbos na URL
Route::get('get-users', 'UserController@index');            // Evite isso
Route::post('create-user', 'UserController@store');         // Evite isso
Route::get('find-user/{id}', 'UserController@show');        // Evite isso

// Ruim: Inconsistência no plural/singular
Route::get('user', 'UserController@index');                 // Evite isso
Route::get('products', 'ProductController@index');          // Inconsistente com 'user'
</code></pre>

        <div class="tip">
            <p>
                Use <code>Route::apiResource()</code> para gerar automaticamente rotas RESTful com nomenclatura consistente para seus recursos.
            </p>
        </div>

        <div class="code-block-header">Exemplo de Nomeação de Rotas</div>
<pre><code>// Nomeando rotas para facilitar a geração de URLs
Route::get('users', 'UserController@index')->name('users.index');
Route::post('users', 'UserController@store')->name('users.store');

// Usando apiResource com nomes automáticos
Route::apiResource('products', 'ProductController');
// Gera: products.index, products.store, products.show, products.update, products.destroy

// Gerando URL a partir do nome da rota
$url = route('users.show', ['id' => 1]); // Retorna: /api/v1/users/1
</code></pre>
    </div>
</div>

<div class="manual-section" id="controllers">
    <h2>5. Controllers</h2>
    <p>
        Os controllers são responsáveis por receber requisições HTTP, delegar o processamento para os services e retornar respostas formatadas. Esta seção detalha os diferentes tipos de controllers e suas implementações.
    </p>

    <div class="manual-section" id="controller-crud">
        <h3>5.1. Controller CRUD</h3>
        <p>
            Os controllers CRUD implementam as operações básicas de Create, Read, Update e Delete para um recurso. Eles devem ser enxutos, delegando a lógica de negócio para os services.
        </p>

        <div class="code-block-header">Exemplo de Controller CRUD</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Controllers;

use App\Controllers\Controller;
use App\Http\SystemA\MicroserviceX\Requests\StoreUserRequest;
use App\Http\SystemA\MicroserviceX\Requests\UpdateUserRequest;
use App\Http\SystemA\MicroserviceX\Resources\UserResource;
use App\Services\ServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Http\Response;

class UserController extends Controller
{
    protected $service;

    public function __construct(ServiceInterface $service)
    {
        $this->service = $service;
    }

    /**
     * Listar todos os recursos
     *
     * @param Request $request
     * @return ResourceCollection
     */
    public function index(Request $request)
    {
        $result = $this->service->index($request->all());
        return UserResource::collection($result['data']);
    }

    /**
     * Criar um novo recurso
     *
     * @param StoreUserRequest $request
     * @return UserResource
     */
    public function store(StoreUserRequest $request)
    {
        $result = $this->service->store($request->validated());
        return new UserResource($result['data']);
    }

    /**
     * Exibir um recurso específico
     *
     * @param int $id
     * @return UserResource
     */
    public function show($id)
    {
        $result = $this->service->show($id);
        return new UserResource($result['data']);
    }

    /**
     * Atualizar um recurso específico
     *
     * @param UpdateUserRequest $request
     * @param int $id
     * @return UserResource
     */
    public function update(UpdateUserRequest $request, $id)
    {
        $result = $this->service->update($id, $request->validated());
        return new UserResource($result['data']);
    }

    /**
     * Remover um recurso específico
     *
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        $this->service->destroy($id);
        return response()->noContent();
    }
}
</code></pre>

        <p>
            Observe que o controller:
        </p>
        <ul>
            <li>Recebe o service via injeção de dependência</li>
            <li>Usa Form Requests para validação</li>
            <li>Retorna Resources para formatar as respostas</li>
            <li>Não contém lógica de negócio, apenas delegação para o service</li>
        </ul>
    </div>

    <div class="manual-section" id="controller-resource">
        <h3>5.2. Resource Controllers</h3>
        <p>
            O Laravel oferece suporte a controllers de recursos, que seguem a convenção RESTful. Você pode usar o comando <code>php artisan make:controller</code> com a opção <code>--resource</code> para gerar um controller de recursos.
        </p>

        <div class="code-block-header">Comando para Criar um Resource Controller</div>
<pre><code>php artisan make:controller Http/SystemA/MicroserviceX/Controllers/ProductController --resource</code></pre>

        <p>
            Ao registrar rotas para um resource controller, você pode usar o método <code>apiResource</code> para gerar automaticamente as rotas RESTful:
        </p>

        <div class="code-block-header">Registrando Rotas para um Resource Controller</div>
<pre><code>Route::apiResource('products', 'App\Http\SystemA\MicroserviceX\Controllers\ProductController');</code></pre>

        <p>
            Isso gera as seguintes rotas:
        </p>

        <table class="table">
            <thead>
                <tr>
                    <th>Verbo</th>
                    <th>URI</th>
                    <th>Método</th>
                    <th>Nome da Rota</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>GET</td>
                    <td>/products</td>
                    <td>index</td>
                    <td>products.index</td>
                </tr>
                <tr>
                    <td>POST</td>
                    <td>/products</td>
                    <td>store</td>
                    <td>products.store</td>
                </tr>
                <tr>
                    <td>GET</td>
                    <td>/products/{product}</td>
                    <td>show</td>
                    <td>products.show</td>
                </tr>
                <tr>
                    <td>PUT/PATCH</td>
                    <td>/products/{product}</td>
                    <td>update</td>
                    <td>products.update</td>
                </tr>
                <tr>
                    <td>DELETE</td>
                    <td>/products/{product}</td>
                    <td>destroy</td>
                    <td>products.destroy</td>
                </tr>
            </tbody>
        </table>

        <p>
            Você pode limitar os métodos gerados usando as opções <code>only</code> e <code>except</code>:
        </p>

        <div class="code-block-header">Limitando Métodos de um Resource Controller</div>
<pre><code>// Apenas index e show
Route::apiResource('products', 'ProductController')->only(['index', 'show']);

// Todos exceto destroy
Route::apiResource('products', 'ProductController')->except(['destroy']);</code></pre>
    </div>

    <div class="manual-section" id="controller-single-action">
        <h3>5.3. Single Action Controllers</h3>
        <p>
            Para endpoints que não se encaixam no padrão CRUD, você pode criar controllers de ação única, que são dedicados a uma única responsabilidade.
        </p>

        <div class="code-block-header">Exemplo de Single Action Controller</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Controllers;

use App\Controllers\Controller;
use App\Http\SystemA\MicroserviceX\Requests\ProcessPaymentRequest;
use App\Http\SystemA\MicroserviceX\Resources\PaymentResource;
use App\Services\ServiceInterface;

class ProcessPaymentController extends Controller
{
    protected $paymentService;

    public function __construct(ServiceInterface $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Processar um pagamento
     *
     * @param ProcessPaymentRequest $request
     * @return PaymentResource
     */
    public function __invoke(ProcessPaymentRequest $request)
    {
        $result = $this->paymentService->processPayment($request->validated());
        return new PaymentResource($result['data']);
    }
}
</code></pre>

        <p>
            Registrando a rota para um single action controller:
        </p>

        <div class="code-block-header">Registrando Rota para Single Action Controller</div>
<pre><code>Route::post('payments/process', 'App\Http\SystemA\MicroserviceX\Controllers\ProcessPaymentController');</code></pre>
    </div>

    <div class="manual-section" id="controller-invokable">
        <h3>5.4. Invokable Controllers</h3>
        <p>
            Os invokable controllers são uma forma especial de single action controllers que implementam o método <code>__invoke</code>. Eles são úteis para endpoints que realizam uma única ação.
        </p>

        <div class="code-block-header">Comando para Criar um Invokable Controller</div>
<pre><code>php artisan make:controller Http/SystemA/MicroserviceX/Controllers/GenerateReportController --invokable</code></pre>

        <div class="code-block-header">Exemplo de Invokable Controller</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Controllers;

use App\Controllers\Controller;
use App\Http\SystemA\MicroserviceX\Requests\GenerateReportRequest;
use App\Services\ServiceInterface;
use Illuminate\Http\Response;

class GenerateReportController extends Controller
{
    protected $reportService;

    public function __construct(ServiceInterface $reportService)
    {
        $this->reportService = $reportService;
    }

    /**
     * Gerar um relatório
     *
     * @param GenerateReportRequest $request
     * @return Response
     */
    public function __invoke(GenerateReportRequest $request)
    {
        $result = $this->reportService->generateReport($request->validated());

        return response()->download(
            $result['data']['path'],
            $result['data']['filename'],
            ['Content-Type' => 'application/pdf']
        );
    }
}
</code></pre>

        <div class="tip">
            <p>
                Use invokable controllers quando um endpoint realiza uma ação específica que não se encaixa no padrão CRUD, como gerar relatórios, processar pagamentos ou enviar e-mails.
            </p>
        </div>
    </div>
</div>

<div class="manual-section" id="services">
    <h2>6. Services</h2>
    <p>
        Os services encapsulam a lógica de negócio da aplicação, atuando como uma camada intermediária entre os controllers e os repositories. Esta seção detalha a implementação de services em nossa arquitetura.
    </p>

    <div class="manual-section" id="service-crud">
        <h3>6.1. Service CRUD</h3>
        <p>
            Os services CRUD implementam operações básicas de Create, Read, Update e Delete, encapsulando a lógica de negócio relacionada a essas operações.
        </p>

        <div class="code-block-header">Exemplo de Service CRUD</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Services;

use App\Services\ServiceAbstract;
use App\Repositories\RepositoryInterface;
use App\Responses\ResponseInterface;
use Illuminate\Support\Facades\Hash;

class UserService extends ServiceAbstract
{
    public function __construct(RepositoryInterface $repository, ResponseInterface $response)
    {
        parent::__construct($repository, $response);
    }

    /**
     * Listar todos os recursos com filtros opcionais
     *
     * @param array $params Parâmetros de filtro e paginação
     * @return array
     */
    public function index(array $params = [])
    {
        // Processar parâmetros de filtro
        $perPage = $params['per_page'] ?? 15;
        $orderBy = $params['order_by'] ?? 'created_at';
        $orderDirection = $params['order_direction'] ?? 'desc';

        // Aplicar filtros específicos
        $filters = [];
        if (isset($params['status'])) {
            $filters['status'] = $params['status'];
        }

        if (isset($params['role_id'])) {
            $filters['role_id'] = $params['role_id'];
        }

        // Obter dados paginados com filtros
        $data = $this->repository->paginateWithFilters(
            $perPage,
            $filters,
            $orderBy,
            $orderDirection
        );

        return $this->response->success('Users retrieved successfully', $data);
    }

    /**
     * Criar um novo recurso
     *
     * @param array $data Dados para criação
     * @return array
     */
    public function store(array $data)
    {
        // Processar dados antes de persistir
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Adicionar dados padrão
        $data['is_active'] = $data['is_active'] ?? true;

        // Persistir dados
        $user = $this->repository->create($data);

        // Processar relacionamentos
        if (isset($data['role_ids']) && is_array($data['role_ids'])) {
            $user->roles()->sync($data['role_ids']);
            $user->load('roles');
        }

        return $this->response->created('User created successfully', $user);
    }

    /**
     * Obter um recurso específico
     *
     * @param int $id ID do recurso
     * @return array
     */
    public function show($id)
    {
        // Obter recurso com relacionamentos
        $user = $this->repository->find($id, ['roles', 'permissions']);

        if (!$user) {
            return $this->response->notFound('User not found');
        }

        return $this->response->success('User retrieved successfully', $user);
    }

    /**
     * Atualizar um recurso específico
     *
     * @param int $id ID do recurso
     * @param array $data Dados para atualização
     * @return array
     */
    public function update($id, array $data)
    {
        // Verificar se o recurso existe
        $user = $this->repository->find($id);

        if (!$user) {
            return $this->response->notFound('User not found');
        }

        // Processar dados antes de atualizar
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            unset($data['password']);
        }

        // Atualizar dados
        $user = $this->repository->update($id, $data);

        // Processar relacionamentos
        if (isset($data['role_ids']) && is_array($data['role_ids'])) {
            $user->roles()->sync($data['role_ids']);
            $user->load('roles');
        }

        return $this->response->success('User updated successfully', $user);
    }

    /**
     * Remover um recurso específico
     *
     * @param int $id ID do recurso
     * @return array
     */
    public function destroy($id)
    {
        // Verificar se o recurso existe
        $user = $this->repository->find($id);

        if (!$user) {
            return $this->response->notFound('User not found');
        }

        // Verificar regras de negócio antes de excluir
        if ($user->isAdmin()) {
            return $this->response->forbidden('Cannot delete admin user');
        }

        // Excluir recurso
        $this->repository->delete($id);

        return $this->response->success('User deleted successfully');
    }
}
</code></pre>

        <p>
            Observe que o service:
        </p>
        <ul>
            <li>Recebe o repository e o response via injeção de dependência</li>
            <li>Implementa lógica de negócio específica para cada operação</li>
            <li>Processa dados antes e depois das operações de persistência</li>
            <li>Gerencia relacionamentos entre entidades</li>
            <li>Retorna respostas padronizadas para o controller</li>
        </ul>
    </div>

    <div class="manual-section" id="service-business-logic">
        <h3>6.2. Lógica de Negócio</h3>
        <p>
            Os services são o local ideal para implementar a lógica de negócio da aplicação. Isso inclui validações complexas, regras de negócio, cálculos e orquestração de operações entre múltiplas entidades.
        </p>

        <div class="code-block-header">Exemplo de Service com Lógica de Negócio Complexa</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Services;

use App\Services\ServiceAbstract;
use App\Repositories\RepositoryInterface;
use App\Responses\ResponseInterface;
use App\Enums\OrderStatus;
use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessLogicException;

class OrderService extends ServiceAbstract
{
    protected $productRepository;
    protected $paymentService;

    public function __construct(
        RepositoryInterface $repository,
        ResponseInterface $response,
        RepositoryInterface $productRepository,
        PaymentService $paymentService
    ) {
        parent::__construct($repository, $response);
        $this->productRepository = $productRepository;
        $this->paymentService = $paymentService;
    }

    /**
     * Processar um pedido
     *
     * @param int $orderId ID do pedido
     * @param array $data Dados para processamento
     * @return array
     */
    public function processOrder($orderId, array $data)
    {
        // Iniciar transação
        return DB::transaction(function () use ($orderId, $data) {
            // Obter o pedido
            $order = $this->repository->find($orderId);

            if (!$order) {
                return $this->response->notFound('Order not found');
            }

            // Verificar se o pedido pode ser processado
            if ($order->status !== OrderStatus::PENDING) {
                return $this->response->error('Order cannot be processed', [
                    'reason' => 'Order is not in pending status'
                ]);
            }

            // Verificar disponibilidade de produtos
            $orderItems = $order->items;
            foreach ($orderItems as $item) {
                $product = $this->productRepository->find($item->product_id);

                if (!$product->isAvailableForPurchase()) {
                    return $this->response->error('Order cannot be processed', [
                        'reason' => "Product {$product->name} is not available",
                        'product_id' => $product->id
                    ]);
                }

                if ($product->stock < $item->quantity) {
                    return $this->response->error('Order cannot be processed', [
                        'reason' => "Insufficient stock for product {$product->name}",
                        'product_id' => $product->id,
                        'requested' => $item->quantity,
                        'available' => $product->stock
                    ]);
                }
            }

            // Processar pagamento
            $paymentResult = $this->paymentService->processPayment([
                'order_id' => $order->id,
                'amount' => $order->total_amount,
                'payment_method' => $data['payment_method'],
                'payment_details' => $data['payment_details'] ?? []
            ]);

            if (!$paymentResult['success']) {
                return $this->response->error('Payment failed', $paymentResult['errors']);
            }

            // Atualizar estoque
            foreach ($orderItems as $item) {
                $product = $this->productRepository->find($item->product_id);
                $this->productRepository->update($product->id, [
                    'stock' => $product->stock - $item->quantity
                ]);
            }

            // Atualizar status do pedido
            $this->repository->update($order->id, [
                'status' => OrderStatus::PROCESSING,
                'payment_id' => $paymentResult['data']['payment_id'],
                'processed_at' => now()
            ]);

            // Recarregar o pedido com os dados atualizados
            $order = $this->repository->find($orderId);

            return $this->response->success('Order processed successfully', $order);
        });
    }

    /**
     * Cancelar um pedido
     *
     * @param int $orderId ID do pedido
     * @param array $data Dados para cancelamento
     * @return array
     */
    public function cancelOrder($orderId, array $data)
    {
        // Implementação do cancelamento de pedido
        // ...
    }

    /**
     * Calcular frete para um pedido
     *
     * @param array $data Dados para cálculo de frete
     * @return array
     */
    public function calculateShipping(array $data)
    {
        // Validar dados
        if (!isset($data['zip_code']) || !isset($data['items'])) {
            return $this->response->validationError('Missing required fields');
        }

        // Calcular peso total
        $totalWeight = 0;
        $totalVolume = 0;

        foreach ($data['items'] as $item) {
            $product = $this->productRepository->find($item['product_id']);

            if (!$product) {
                return $this->response->notFound("Product {$item['product_id']} not found");
            }

            $totalWeight += $product->weight * $item['quantity'];
            $totalVolume += ($product->length * $product->width * $product->height) * $item['quantity'];
        }

        // Chamar serviço de frete
        $shippingOptions = $this->calculateShippingOptions(
            $data['zip_code'],
            $totalWeight,
            $totalVolume
        );

        return $this->response->success('Shipping options calculated', $shippingOptions);
    }

    /**
     * Método privado para calcular opções de frete
     */
    private function calculateShippingOptions($zipCode, $weight, $volume)
    {
        // Implementação do cálculo de opções de frete
        // ...
    }
}
</code></pre>

        <p>
            Este exemplo demonstra como implementar lógica de negócio complexa em um service, incluindo:
        </p>
        <ul>
            <li>Validações de regras de negócio</li>
            <li>Transações de banco de dados</li>
            <li>Orquestração de operações entre múltiplas entidades</li>
            <li>Integração com outros services</li>
            <li>Cálculos e processamentos específicos do domínio</li>
        </ul>

        <div class="tip">
            <p>
                Mantenha os services focados em uma única responsabilidade. Se um service estiver ficando muito grande ou complexo, considere dividi-lo em múltiplos services menores e mais especializados.
            </p>
        </div>
    </div>

    <div class="manual-section" id="service-events">
        <h3>6.3. Disparando Events</h3>
        <p>
            Os services são o local ideal para disparar eventos que podem ser tratados por listeners em outras partes da aplicação. Isso permite desacoplar o código e implementar funcionalidades como notificações, logs e processamento assíncrono.
        </p>

        <div class="code-block-header">Exemplo de Service com Events</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Services;

use App\Services\ServiceAbstract;
use App\Repositories\RepositoryInterface;
use App\Responses\ResponseInterface;
use App\Events\OrderCreated;
use App\Events\OrderStatusChanged;
use App\Events\PaymentProcessed;

class OrderService extends ServiceAbstract
{
    public function __construct(RepositoryInterface $repository, ResponseInterface $response)
    {
        parent::__construct($repository, $response);
    }

    /**
     * Criar um novo pedido
     *
     * @param array $data Dados do pedido
     * @return array
     */
    public function store(array $data)
    {
        // Criar o pedido
        $order = $this->repository->create($data);

        // Criar itens do pedido
        if (isset($data['items']) && is_array($data['items'])) {
            foreach ($data['items'] as $item) {
                $order->items()->create($item);
            }
        }

        // Disparar evento de pedido criado
        event(new OrderCreated($order));

        return $this->response->created('Order created successfully', $order);
    }

    /**
     * Atualizar status de um pedido
     *
     * @param int $id ID do pedido
     * @param string $status Novo status
     * @return array
     */
    public function updateStatus($id, $status)
    {
        $order = $this->repository->find($id);

        if (!$order) {
            return $this->response->notFound('Order not found');
        }

        $oldStatus = $order->status;

        // Atualizar status
        $order = $this->repository->update($id, ['status' => $status]);

        // Disparar evento de status alterado
        event(new OrderStatusChanged($order, $oldStatus, $status));

        return $this->response->success('Order status updated successfully', $order);
    }

    /**
     * Processar pagamento de um pedido
     *
     * @param int $id ID do pedido
     * @param array $paymentData Dados do pagamento
     * @return array
     */
    public function processPayment($id, array $paymentData)
    {
        $order = $this->repository->find($id);

        if (!$order) {
            return $this->response->notFound('Order not found');
        }

        // Processar pagamento (implementação simplificada)
        $payment = [
            'order_id' => $order->id,
            'amount' => $order->total_amount,
            'method' => $paymentData['method'],
            'status' => 'approved',
            'transaction_id' => uniqid('trans_'),
            'processed_at' => now()
        ];

        // Salvar dados do pagamento
        $order->payment()->create($payment);

        // Atualizar status do pedido
        $this->repository->update($id, ['status' => 'paid']);

        // Disparar evento de pagamento processado
        event(new PaymentProcessed($order, $payment));

        return $this->response->success('Payment processed successfully', $order);
    }
}
</code></pre>

        <div class="code-block-header">Exemplo de Event</div>
<pre><code>namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderCreated
{
    use Dispatchable, SerializesModels;

    public $order;

    /**
     * Create a new event instance.
     *
     * @param  mixed  $order
     * @return void
     */
    public function __construct($order)
    {
        $this->order = $order;
    }
}
</code></pre>

        <div class="code-block-header">Exemplo de Listener</div>
<pre><code>namespace App\Listeners;

use App\Events\OrderCreated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\NotificationService;

class SendOrderConfirmationNotification implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;

    /**
     * Create the event listener.
     *
     * @param  NotificationService  $notificationService
     * @return void
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the event.
     *
     * @param  OrderCreated  $event
     * @return void
     */
    public function handle(OrderCreated $event)
    {
        $order = $event->order;

        // Enviar e-mail de confirmação
        $this->notificationService->sendOrderConfirmation($order);

        // Enviar notificação push
        $this->notificationService->sendPushNotification(
            $order->user_id,
            'Pedido Recebido',
            "Seu pedido #{$order->id} foi recebido e está sendo processado."
        );
    }
}
</code></pre>

        <div class="code-block-header">Registrando Events e Listeners no EventServiceProvider</div>
<pre><code>namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        'App\Events\OrderCreated' => [
            'App\Listeners\SendOrderConfirmationNotification',
            'App\Listeners\UpdateInventory',
        ],
        'App\Events\OrderStatusChanged' => [
            'App\Listeners\SendOrderStatusNotification',
            'App\Listeners\LogOrderStatusChange',
        ],
        'App\Events\PaymentProcessed' => [
            'App\Listeners\SendPaymentConfirmation',
            'App\Listeners\UpdateAccountingSystem',
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
</code></pre>

        <p>
            Usar events e listeners oferece várias vantagens:
        </p>
        <ul>
            <li>Desacoplamento entre o código que dispara o evento e o código que o trata</li>
            <li>Possibilidade de adicionar novos comportamentos sem modificar o código existente</li>
            <li>Processamento assíncrono de tarefas demoradas usando filas</li>
            <li>Melhor organização do código, com cada listener tendo uma única responsabilidade</li>
        </ul>

        <div class="tip">
            <p>
                Para operações que podem ser executadas de forma assíncrona, como envio de e-mails ou notificações, implemente seus listeners com a interface <code>ShouldQueue</code> para que sejam processados em segundo plano.
            </p>
        </div>
    </div>
</div>

<div class="manual-section" id="repositories">
    <h2>7. Repositories</h2>
    <p>
        Os repositories abstraem o acesso a dados, permitindo que a lógica de negócio seja independente da fonte de dados. Esta seção detalha a implementação de repositories em nossa arquitetura.
    </p>

    <div class="manual-section" id="repository-crud">
        <h3>7.1. Repository CRUD</h3>
        <p>
            Os repositories CRUD implementam operações básicas de Create, Read, Update e Delete para uma entidade específica.
        </p>

        <div class="code-block-header">Exemplo de Repository CRUD</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Repositories;

use App\Repositories\RepositoryAbstract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class UserRepository extends RepositoryAbstract
{
    public function __construct(Model $model)
    {
        parent::__construct($model);
    }

    /**
     * Obter todos os registros
     *
     * @param array $columns Colunas a serem retornadas
     * @return Collection
     */
    public function all(array $columns = ['*']): Collection
    {
        return $this->model->all($columns);
    }

    /**
     * Encontrar um registro pelo ID
     *
     * @param int $id ID do registro
     * @param array $columns Colunas a serem retornadas
     * @param array $relations Relacionamentos a serem carregados
     * @return Model|null
     */
    public function find($id, array $columns = ['*'], array $relations = [])
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->find($id, $columns);
    }

    /**
     * Encontrar registros por um campo específico
     *
     * @param string $field Campo para busca
     * @param mixed $value Valor para busca
     * @param array $columns Colunas a serem retornadas
     * @return Collection
     */
    public function findByField($field, $value, array $columns = ['*']): Collection
    {
        return $this->model->where($field, $value)->get($columns);
    }

    /**
     * Criar um novo registro
     *
     * @param array $data Dados para criação
     * @return Model
     */
    public function create(array $data)
    {
        return $this->model->create($data);
    }

    /**
     * Atualizar um registro
     *
     * @param int $id ID do registro
     * @param array $data Dados para atualização
     * @return Model|bool
     */
    public function update($id, array $data)
    {
        $model = $this->find($id);

        if (!$model) {
            return false;
        }

        $model->update($data);
        return $model;
    }

    /**
     * Excluir um registro
     *
     * @param int $id ID do registro
     * @return bool
     */
    public function delete($id): bool
    {
        $model = $this->find($id);

        if (!$model) {
            return false;
        }

        return $model->delete();
    }

    /**
     * Paginar registros
     *
     * @param int $perPage Itens por página
     * @param array $columns Colunas a serem retornadas
     * @return LengthAwarePaginator
     */
    public function paginate($perPage = 15, array $columns = ['*']): LengthAwarePaginator
    {
        return $this->model->paginate($perPage, $columns);
    }

    /**
     * Paginar registros com filtros
     *
     * @param int $perPage Itens por página
     * @param array $filters Filtros a serem aplicados
     * @param string $orderBy Campo para ordenação
     * @param string $orderDirection Direção da ordenação
     * @param array $relations Relacionamentos a serem carregados
     * @return LengthAwarePaginator
     */
    public function paginateWithFilters(
        $perPage = 15,
        array $filters = [],
        $orderBy = 'created_at',
        $orderDirection = 'desc',
        array $relations = []
    ): LengthAwarePaginator {
        $query = $this->model->newQuery();

        // Aplicar filtros
        foreach ($filters as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }

        // Carregar relacionamentos
        if (!empty($relations)) {
            $query->with($relations);
        }

        // Ordenar e paginar
        return $query->orderBy($orderBy, $orderDirection)
                    ->paginate($perPage);
    }
}
</code></pre>

        <p>
            Observe que o repository:
        </p>
        <ul>
            <li>Recebe o model via injeção de dependência</li>
            <li>Implementa métodos CRUD básicos</li>
            <li>Adiciona métodos específicos para consultas comuns</li>
            <li>Abstrai detalhes de implementação do Eloquent</li>
        </ul>
    </div>

    <div class="manual-section" id="repository-queries">
       <h3>7.2. Queries Complexas</h3>
<p>
    Os repositories são o local ideal para implementar queries complexas, abstraindo a complexidade da camada de acesso a dados.
</p>

<div class="code-block-header">Exemplo de Repository com Queries Complexas</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Repositories;

use App\Repositories\RepositoryAbstract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProductRepository extends RepositoryAbstract
{
    public function __construct(Model $model)
    {
        parent::__construct($model);
    }

    /**
     * Obter produtos mais vendidos
     *
     * @param int $limit Limite de resultados
     * @param Carbon|null $startDate Data inicial
     * @param Carbon|null $endDate Data final
     * @return Collection
     */
    public function getMostSoldProducts($limit = 10, Carbon $startDate = null, Carbon $endDate = null): Collection
    {
        $query = $this->model
            ->select('products.*', DB::raw('SUM(order_items.quantity) as total_sold'))
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.status', 'completed')
            ->groupBy('products.id')
            ->orderBy('total_sold', 'desc');

        if ($startDate) {
            $query->where('orders.created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('orders.created_at', '<=', $endDate);
        }

        return $query->limit($limit)->get();
    }

    /**
     * Obter produtos com estoque baixo
     *
     * @param int $threshold Limite de estoque
     * @return Collection
     */
    public function getLowStockProducts($threshold = 10): Collection
    {
        return $this->model
            ->where('stock', '<=', $threshold)
            ->where('is_active', true)
            ->orderBy('stock', 'asc')
            ->get();
    }

    /**
     * Buscar produtos por texto
     *
     * @param string $searchTerm Termo de busca
     * @param int $perPage Itens por página
     * @return LengthAwarePaginator
     */
    public function searchProducts($searchTerm, $perPage = 15)
    {
        return $this->model
            ->where(function ($query) use ($searchTerm) {
                $query->where('name', 'like', "%{$searchTerm}%")
                    ->orWhere('description', 'like', "%{$searchTerm}%")
                    ->orWhere('sku', 'like', "%{$searchTerm}%");
            })
            ->where('is_active', true)
            ->paginate($perPage);
    }

    /**
     * Obter produtos por categoria com filtros
     *
     * @param int $categoryId ID da categoria
     * @param array $filters Filtros adicionais
     * @param int $perPage Itens por página
     * @return LengthAwarePaginator
     */
    public function getProductsByCategory($categoryId, array $filters = [], $perPage = 15)
    {
        $query = $this->model
            ->where('category_id', $categoryId)
            ->where('is_active', true);

        // Filtrar por preço
        if (isset($filters['min_price'])) {
            $query->where('price', '>=', $filters['min_price']);
        }

        if (isset($filters['max_price'])) {
            $query->where('price', '<=', $filters['max_price']);
        }

        // Filtrar por atributos
        if (isset($filters['attributes']) && is_array($filters['attributes'])) {
            foreach ($filters['attributes'] as $attributeId => $value) {
                $query->whereHas('attributes', function ($q) use ($attributeId, $value) {
                    $q->where('attribute_id', $attributeId)
                      ->where('value', $value);
                });
            }
        }

        // Ordenação
        $orderBy = $filters['order_by'] ?? 'created_at';
        $orderDirection = $filters['order_direction'] ?? 'desc';

        return $query->orderBy($orderBy, $orderDirection)
                    ->paginate($perPage);
    }

    /**
     * Obter produtos relacionados
     *
     * @param int $productId ID do produto
     * @param int $limit Limite de resultados
     * @return Collection
     */
    public function getRelatedProducts($productId, $limit = 5): Collection
    {
        $product = $this->find($productId);

        if (!$product) {
            return collect();
        }

        return $this->model
            ->where('id', '!=', $productId)
            ->where('category_id', $product->category_id)
            ->where('is_active', true)
            ->limit($limit)
            ->get();
    }
}
</code></pre>

<p>
    Este exemplo demonstra como implementar queries complexas em um repository, incluindo:
</p>
<ul>
    <li>Joins entre múltiplas tabelas</li>
    <li>Agregações e funções SQL</li>
    <li>Filtros dinâmicos</li>
    <li>Subconsultas com whereHas</li>
    <li>Ordenação dinâmica</li>
</ul>

<div class="tip">
    <p>
        Mantenha as queries complexas nos repositories para abstrair a complexidade da camada de acesso a dados. Isso facilita a manutenção e permite que os services se concentrem na lógica de negócio.
    </p>
</div>

<div class="manual-section" id="repository-cache">
    <h3>7.3. Cache em Repositories</h3>
    <p>
        O cache é uma técnica importante para melhorar a performance de operações de leitura frequentes. Os repositories são o local ideal para implementar estratégias de cache.
    </p>

    <div class="code-block-header">Exemplo de Repository com Cache</div>
<pre><code>namespace App\Http\SystemA\MicroserviceX\Repositories;

use App\Repositories\RepositoryAbstract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class CategoryRepository extends RepositoryAbstract
{
    protected $cacheEnabled = true;
    protected $cacheTtl = 3600; // 1 hora em segundos

    public function __construct(Model $model)
    {
        parent::__construct($model);
    }

    /**
     * Obter todas as categorias
     *
     * @param array $columns Colunas a serem retornadas
     * @return Collection
     */
    public function all(array $columns = ['*']): Collection
    {
        $cacheKey = "categories.all." . implode('.', $columns);

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $categories = $this->model->all($columns);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $categories, $this->cacheTtl);
        }

        return $categories;
    }

    /**
     * Encontrar uma categoria pelo ID
     *
     * @param int $id ID da categoria
     * @param array $columns Colunas a serem retornadas
     * @return Model|null
     */
    public function find($id, array $columns = ['*'])
    {
        $cacheKey = "categories.find.{$id}." . implode('.', $columns);

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $category = $this->model->find($id, $columns);

        if ($this->cacheEnabled && $category) {
            Cache::put($cacheKey, $category, $this->cacheTtl);
        }

        return $category;
    }

    /**
     * Obter categorias com produtos
     *
     * @return Collection
     */
    public function getCategoriesWithProducts(): Collection
    {
        $cacheKey = "categories.with_products";

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $categories = $this->model
            ->withCount('products')
            ->having('products_count', '>', 0)
            ->orderBy('name')
            ->get();

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $categories, $this->cacheTtl);
        }

        return $categories;
    }

    /**
     * Criar uma nova categoria
     *
     * @param array $data Dados para criação
     * @return Model
     */
    public function create(array $data)
    {
        $category = parent::create($data);

        if ($this->cacheEnabled) {
            $this->clearCategoryCache();
        }

        return $category;
    }

    /**
     * Atualizar uma categoria
     *
     * @param int $id ID da categoria
     * @param array $data Dados para atualização
     * @return Model|bool
     */
    public function update($id, array $data)
    {
        $category = parent::update($id, $data);

        if ($this->cacheEnabled && $category) {
            $this->clearCategoryCache($id);
        }

        return $category;
    }

    /**
     * Excluir uma categoria
     *
     * @param int $id ID da categoria
     * @return bool
     */
    public function delete($id): bool
    {
        $result = parent::delete($id);

        if ($this->cacheEnabled && $result) {
            $this->clearCategoryCache($id);
        }

        return $result;
    }

    /**
     * Limpar o cache de categorias
     *
     * @param int|null $id ID da categoria específica (opcional)
     * @return void
     */
    protected function clearCategoryCache($id = null)
    {
        // Limpar cache específico da categoria
        if ($id) {
            Cache::forget("categories.find.{$id}.*");
        }

        // Limpar cache de listagens
        Cache::forget("categories.all.*");
        Cache::forget("categories.with_products");

        // Outros caches relacionados a categorias
        // ...
    }

    /**
     * Habilitar ou desabilitar cache
     *
     * @param bool $enabled Status do cache
     * @return void
     */
    public function setCacheEnabled(bool $enabled)
    {
        $this->cacheEnabled = $enabled;
    }

    /**
     * Definir TTL do cache
     *
     * @param int $seconds Tempo em segundos
     * @return void
     */
    public function setCacheTtl(int $seconds)
    {
        $this->cacheTtl = $seconds;
    }
}
</code></pre>

    <p>
        Este exemplo demonstra como implementar cache em um repository, incluindo:
    </p>
    <ul>
        <li>Cache para operações de leitura (all, find, queries personalizadas)</li>
        <li>Invalidação de cache para operações de escrita (create, update, delete)</li>
        <li>Configuração flexível de cache (habilitação/desabilitação, TTL)</li>
        <li>Chaves de cache específicas para diferentes operações</li>
    </ul>

    <div class="note">
        <p>
            O cache deve ser implementado com cuidado, considerando a frequência de atualização dos dados e o impacto na consistência. Para dados que mudam frequentemente, use TTLs curtos ou evite o cache.
        </p>
    </div>

    <div class="tip">
        <p>
            Para sistemas distribuídos, considere usar um driver de cache distribuído como Redis ou Memcached para garantir consistência entre múltiplas instâncias da aplicação.
        </p>
    </div>
</div>

<div class="manual-section" id="models">
    <h2>8. Models e Relacionamentos</h2>
    <p>
        Os models representam as entidades de negócio e encapsulam regras de negócio relacionadas à entidade. Esta seção detalha a implementação de models em nossa arquitetura.
    </p>

    <div class="manual-section" id="model-attributes">
        <h3>8.1. Atributos e Casts</h3>
        <p>
            Os models devem definir claramente quais atributos são fillable (podem ser preenchidos em massa) e como os atributos devem ser convertidos (cast) para tipos específicos.
        </p>

        <div class="code-block-header">Exemplo de Model com Atributos e Casts</div>
<pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Atributos que podem ser preenchidos em massa
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'price',
        'stock',
        'sku',
        'category_id',
        'is_active',
        'weight',
        'dimensions',
        'metadata'
    ];

    /**
     * Atributos que devem ser escondidos em arrays/JSON
     *
     * @var array
     */
    protected $hidden = [
        'deleted_at',
        'internal_notes'
    ];

    /**
     * Atributos que devem ser convertidos para tipos específicos
     *
     * @var array
     */
    protected $casts = [
        'price' => 'decimal:2',
        'stock' => 'integer',
        'is_active' => 'boolean',
        'dimensions' => 'array',
        'metadata' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'published_at' => 'datetime'
    ];

    /**
     * Atributos que devem ser mutados para datas
     *
     * @var array
     */
    protected $dates = [
        'published_at'
    ];

    /**
     * Acessor para obter o preço formatado
     *
     * @return string
     */
    public function getFormattedPriceAttribute()
    {
        return 'R$ ' . number_format($this->price, 2, ',', '.');
    }

    /**
     * Acessor para obter o status em texto
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return $this->is_active ? 'Ativo' : 'Inativo';
    }

    /**
     * Mutator para o atributo name
     *
     * @param string $value
     * @return void
     */
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = ucwords(strtolower(trim($value)));
    }

    /**
     * Mutator para o atributo sku
     *
     * @param string $value
     * @return void
     */
    public function setSkuAttribute($value)
    {
        $this->attributes['sku'] = strtoupper(trim($value));
    }
}
</code></pre>

        <p>
            Este exemplo demonstra:
        </p>
        <ul>
            <li>Definição de atributos fillable para mass assignment</li>
            <li>Definição de atributos hidden para serialização</li>
            <li>Definição de casts para converter atributos para tipos específicos</li>
            <li>Definição de dates para tratamento de datas</li>
            <li>Implementação de acessores para formatar atributos</li>
            <li>Implementação de mutators para processar atributos antes de salvar</li>
        </ul>

        <div class="tip">
            <p>
                Use casts para garantir que os atributos sejam sempre do tipo correto. Isso evita problemas de tipo e facilita o trabalho com os dados.
            </p>
        </div>
    </div>

    <div class="manual-section" id="model-relationships">
        <h3>8.2. Relacionamentos</h3>
        <p>
            Os relacionamentos entre models são definidos como métodos que retornam o tipo de relacionamento. O Laravel suporta vários tipos de relacionamentos, como one-to-one, one-to-many, many-to-many, etc.
        </p>

        <div class="code-block-header">Exemplo de Model com Relacionamentos</div>
<pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'category_id',
        'brand_id'
    ];

    /**
     * Relacionamento com categoria (belongs to)
     *
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Relacionamento com marca (belongs to)
     *
     * @return BelongsTo
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Relacionamento com imagens (has many)
     *
     * @return HasMany
     */
    public function images(): HasMany
    {
        return $this->hasMany(ProductImage::class);
    }

    /**
     * Relacionamento com variantes (has many)
     *
     * @return HasMany
     */
    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Relacionamento com detalhes técnicos (has one)
     *
     * @return HasOne
     */
    public function technicalDetails(): HasOne
    {
        return $this->hasOne(ProductTechnicalDetail::class);
    }

    /**
     * Relacionamento com tags (many to many)
     *
     * @return BelongsToMany
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)
                    ->withTimestamps()
                    ->withPivot('relevance');
    }

    /**
     * Relacionamento com atributos (many to many)
     *
     * @return BelongsToMany
     */
    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class)
                    ->withPivot('value')
                    ->withTimestamps();
    }

    /**
     * Relacionamento com pedidos (many to many)
     *
     * @return BelongsToMany
     */
    public function orders(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'order_items')
                    ->withPivot('quantity', 'price')
                    ->withTimestamps();
    }

    /**
     * Relacionamento com avaliações (has many)
     *
     * @return HasMany
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Relacionamento polimórfico com comentários
     *
     * @return MorphMany
     */
    public function comments()
    {
        return $this->morphMany(Comment::class, 'commentable');
    }
}
</code></pre>

        <p>
            Este exemplo demonstra vários tipos de relacionamentos:
        </p>
        <ul>
            <li>belongsTo: Produto pertence a uma Categoria e a uma Marca</li>
            <li>hasMany: Produto tem muitas Imagens, Variantes e Avaliações</li>
            <li>hasOne: Produto tem um Detalhe Técnico</li>
            <li>belongsToMany: Produto tem muitas Tags, Atributos e Pedidos</li>
            <li>morphMany: Relacionamento polimórfico com Comentários</li>
        </ul>

        <div class="tip">
            <p>
                Use type hints nos métodos de relacionamento para melhorar a autocompleção em IDEs e tornar o código mais legível.
            </p>
        </div>

        <div class="code-block-header">Exemplo de Uso de Relacionamentos</div>
<pre><code>// Eager loading para evitar o problema N+1
$products = Product::with(['category', 'brand', 'images'])->get();

// Filtrar produtos por categoria
$categoryProducts = Product::whereHas('category', function ($query) {
    $query->where('name', 'Electronics');
})->get();

// Contar relacionamentos
$popularProducts = Product::withCount('reviews')
    ->having('reviews_count', '>=', 10)
    ->orderBy('reviews_count', 'desc')
    ->get();

// Acessar relacionamentos
$product = Product::find(1);
$categoryName = $product->category->name;
$imageUrls = $product->images->pluck('url');

// Criar relacionamentos
$product->reviews()->create([
    'user_id' => 1,
    'rating' => 5,
    'comment' => 'Excelente produto!'
]);

// Sincronizar relacionamentos many-to-many
$product->tags()->sync([1, 2, 3]);

// Anexar e desanexar relacionamentos many-to-many
$product->attributes()->attach(4, ['value' => 'Azul']);
$product->attributes()->detach(5);
</code></pre>
    </div>

    <div class="manual-section" id="model-scopes">
        <h3>8.3. Query Scopes</h3>
        <p>
            Query scopes permitem encapsular consultas comuns em métodos reutilizáveis, tornando o código mais limpo e expressivo.
        </p>

        <div class="code-block-header">Exemplo de Model com Query Scopes</div>
<pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class Product extends Model
{
    use HasFactory;

    /**
     * Scope para produtos ativos
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope para produtos em estoque
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeInStock(Builder $query): Builder
    {
        return $query->where('stock', '>', 0);
    }

    /**
     * Scope para produtos em promoção
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeOnSale(Builder $query): Builder
    {
        return $query->where('is_on_sale', true)
                     ->where('sale_price', '>', 0)
                     ->where('sale_ends_at', '>', Carbon::now());
    }

    /**
     * Scope para produtos de uma categoria específica
     *
     * @param Builder $query
     * @param int $categoryId
     * @return Builder
     */
    public function scopeByCategory(Builder $query, int $categoryId): Builder
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope para produtos em uma faixa de preço
     *
     * @param Builder $query
     * @param float $min
     * @param float $max
     * @return Builder
     */
    public function scopePriceRange(Builder $query, float $min, float $max): Builder
    {
        return $query->where('price', '>=', $min)
                     ->where('price', '<=', $max);
    }

    /**
     * Scope para produtos recentes
     *
     * @param Builder $query
     * @param int $days
     * @return Builder
     */
    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * Scope para produtos populares (com base em vendas)
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopePopular(Builder $query): Builder
    {
        return $query->withCount('orders')
                     ->orderBy('orders_count', 'desc');
    }

    /**
     * Scope para produtos bem avaliados
     *
     * @param Builder $query
     * @param int $minRating
     * @return Builder
     */
    public function scopeTopRated(Builder $query, int $minRating = 4): Builder
    {
        return $query->whereHas('reviews', function ($q) use ($minRating) {
            $q->select('product_id')
              ->groupBy('product_id')
              ->havingRaw('AVG(rating) >= ?', [$minRating]);
        });
    }

    /**
     * Scope para busca de produtos
     *
     * @param Builder $query
     * @param string $term
     * @return Builder
     */
    public function scopeSearch(Builder $query, string $term): Builder
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('description', 'like', "%{$term}%")
              ->orWhere('sku', 'like', "%{$term}%");
        });
    }
}
</code></pre>

        <div class="code-block-header">Exemplo de Uso de Query Scopes</div>
<pre><code>// Produtos ativos e em estoque
$availableProducts = Product::active()->inStock()->get();

// Produtos em promoção em uma categoria específica
$saleProducts = Product::onSale()->byCategory(5)->get();

// Produtos recentes em uma faixa de preço
$newProducts = Product::recent(7)->priceRange(100, 500)->get();

// Produtos populares e bem avaliados
$recommendedProducts = Product::popular()->topRated()->limit(10)->get();

// Busca de produtos ativos
$searchResults = Product::active()->search('smartphone')->paginate(15);

// Combinando múltiplos scopes
$featuredProducts = Product::active()
                          ->inStock()
                          ->topRated()
                          ->orderBy('price', 'asc')
                          ->limit(5)
                          ->get();
</code></pre>

        <p>
            Query scopes oferecem várias vantagens:
        </p>
        <ul>
            <li>Código mais limpo e expressivo</li>
            <li>Reutilização de consultas comuns</li>
            <li>Encapsulamento de lógica de consulta</li>
            <li>Facilidade de manutenção</li>
            <li>Combinação de múltiplos scopes</li>
        </ul>

        <div class="tip">
            <p>
                Use query scopes para encapsular consultas comuns e tornar o código mais expressivo. Isso facilita a manutenção e reduz a duplicação de código.
            </p>
        </div>
    </div>

    <div class="manual-section" id="model-observers">
       <h3>8.4. Observers</h3>
<p>
    Observers permitem encapsular a lógica de eventos do modelo em uma classe separada, tornando o código mais organizado e fácil de manter.
</p>

<div class="code-block-header">Exemplo de Observer</div>
<pre><code>namespace App\Observers;

use App\Models\Product;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use App\Services\SearchIndexService;

class ProductObserver
{
    protected $searchIndexService;

    public function __construct(SearchIndexService $searchIndexService)
    {
        $this->searchIndexService = $searchIndexService;
    }

    /**
     * Handle the Product "created" event.
     *
     * @param  Product  $product
     * @return void
     */
    public function created(Product $product)
    {
        // Indexar produto no serviço de busca
        $this->searchIndexService->indexProduct($product);

        // Limpar cache relacionado a produtos
        $this->clearProductCache();

        // Registrar log de criação
        activity()
            ->performedOn($product)
            ->withProperties(['action' => 'created'])
            ->log('Product created');
    }

    /**
     * Handle the Product "updated" event.
     *
     * @param  Product  $product
     * @return void
     */
    public function updated(Product $product)
    {
        // Atualizar índice de busca
        $this->searchIndexService->updateProductIndex($product);

        // Limpar cache específico do produto e caches relacionados
        $this->clearProductCache($product->id);

        // Registrar log de atualização com mudanças
        activity()
            ->performedOn($product)
            ->withProperties([
                'action' => 'updated',
                'changes' => $product->getChanges(),
                'original' => $product->getOriginal()
            ])
            ->log('Product updated');
    }

    /**
     * Handle the Product "deleted" event.
     *
     * @param  Product  $product
     * @return void
     */
    public function deleted(Product $product)
    {
        // Remover produto do índice de busca
        $this->searchIndexService->removeProductFromIndex($product->id);

        // Limpar cache
        $this->clearProductCache($product->id);

        // Registrar log de exclusão
        activity()
            ->performedOn($product)
            ->withProperties(['action' => 'deleted'])
            ->log('Product deleted');

        // Remover arquivos associados
        $this->removeProductFiles($product);
    }

    /**
     * Handle the Product "restored" event.
     *
     * @param  Product  $product
     * @return void
     */
    public function restored(Product $product)
    {
        // Reindexar produto no serviço de busca
        $this->searchIndexService->indexProduct($product);

        // Limpar cache
        $this->clearProductCache();

        // Registrar log de restauração
        activity()
            ->performedOn($product)
            ->withProperties(['action' => 'restored'])
            ->log('Product restored');
    }

    /**
     * Handle the Product "force deleted" event.
     *
     * @param  Product  $product
     * @return void
     */
    public function forceDeleted(Product $product)
    {
        // Remover produto do índice de busca
        $this->searchIndexService->removeProductFromIndex($product->id);

        // Limpar cache
        $this->clearProductCache($product->id);

        // Registrar log de exclusão permanente
        activity()
            ->performedOn($product)
            ->withProperties(['action' => 'force_deleted'])
            ->log('Product permanently deleted');

        // Remover arquivos associados
        $this->removeProductFiles($product);
    }

    /**
     * Limpar cache relacionado a produtos
     *
     * @param int|null $productId ID do produto específico (opcional)
     * @return void
     */
    protected function clearProductCache($productId = null)
    {
        if ($productId) {
            Cache::forget("products.{$productId}");
        }

        Cache::forget('products.all');
        Cache::forget('products.featured');
        Cache::forget('products.categories');
        Cache::tags(['products'])->flush();
    }

    /**
     * Remover arquivos associados ao produto
     *
     * @param Product $product
     * @return void
     */
    protected function removeProductFiles(Product $product)
    {
        // Remover imagens do produto
        foreach ($product->images as $image) {
            Storage::delete($image->path);
        }

        // Remover outros arquivos associados
        if ($product->manual_path) {
            Storage::delete($product->manual_path);
        }
    }
}
</code></pre>

<div class="code-block-header">Registrando o Observer</div>
<pre><code>namespace App\Providers;

use App\Models\Product;
use App\Observers\ProductObserver;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Product::observe(ProductObserver::class);
    }
}
</code></pre>

<p>
    Os observers oferecem várias vantagens:
</p>
<ul>
    <li>Separação de responsabilidades, mantendo o modelo focado em sua função principal</li>
    <li>Organização de código relacionado a eventos em uma única classe</li>
    <li>Facilidade de manutenção e teste</li>
    <li>Possibilidade de injeção de dependências no observer</li>
    <li>Centralização de lógica relacionada a eventos do modelo</li>
</ul>

<div class="tip">
    <p>
        Use observers para encapsular lógica relacionada a eventos do modelo, especialmente quando essa lógica é complexa ou envolve múltiplas operações.
    </p>
</div>
</div>

<div class="manual-section" id="conclusao">
    <h2>9. Conclusão</h2>
    <p>
        Este manual apresentou as diretrizes e padrões para implementação de APIs RESTful usando Laravel em nossa arquitetura de microsserviços. Seguir estas práticas garantirá um código consistente, manutenível e escalável.
    </p>

    <div class="manual-section" id="resumo-praticas">
        <h3>9.1. Resumo das Melhores Práticas</h3>
        <ul>
            <li><strong>Separação de Responsabilidades:</strong> Cada camada tem uma responsabilidade específica, mantendo o código organizado e fácil de manter.</li>
            <li><strong>Injeção de Dependência:</strong> Use injeção de dependência para desacoplar componentes e facilitar testes.</li>
            <li><strong>Convenções de Nomenclatura:</strong> Siga convenções consistentes para nomes de classes, métodos, variáveis e rotas.</li>
            <li><strong>Validação de Dados:</strong> Use Form Requests para validar dados de entrada antes de processá-los.</li>
            <li><strong>Respostas Padronizadas:</strong> Use Resources para formatar respostas de forma consistente.</li>
            <li><strong>Tratamento de Erros:</strong> Implemente tratamento de erros consistente em toda a aplicação.</li>
            <li><strong>Documentação:</strong> Documente seu código e APIs para facilitar o entendimento e uso por outros desenvolvedores.</li>
            <li><strong>Testes:</strong> Escreva testes para garantir que seu código funcione conforme esperado.</li>
        </ul>
    </div>

    <div class="manual-section" id="proximos-passos">
        <h3>9.2. Próximos Passos</h3>
        <p>
            Após compreender e aplicar os conceitos deste manual, recomendamos explorar os seguintes tópicos para aprofundar seu conhecimento:
        </p>
        <ul>
            <li><strong>Testes Automatizados:</strong> Aprenda a escrever testes unitários, de integração e de ponta a ponta para suas APIs.</li>
            <li><strong>Autenticação e Autorização:</strong> Implemente autenticação segura e controle de acesso baseado em roles e permissões.</li>
            <li><strong>Documentação de API:</strong> Use ferramentas como Swagger/OpenAPI para documentar suas APIs.</li>
            <li><strong>Monitoramento e Logging:</strong> Implemente monitoramento e logging para identificar e resolver problemas rapidamente.</li>
            <li><strong>Performance e Otimização:</strong> Aprenda técnicas para otimizar a performance de suas APIs.</li>
            <li><strong>Escalabilidade:</strong> Explore estratégias para escalar suas APIs horizontalmente e verticalmente.</li>
        </ul>
    </div>

    <div class="manual-section" id="recursos-adicionais">
        <h3>9.3. Recursos Adicionais</h3>
        <p>
            Aqui estão alguns recursos adicionais para aprofundar seu conhecimento:
        </p>
        <ul>
            <li><a href="https://laravel.com/docs">Documentação oficial do Laravel</a></li>
            <li><a href="https://laracasts.com">Laracasts - Tutoriais em vídeo sobre Laravel</a></li>
            <li><a href="https://github.com/alexeymezenin/laravel-best-practices">Laravel Best Practices</a></li>
            <li><a href="https://www.php-fig.org/psr/">PHP Standards Recommendations (PSR)</a></li>
            <li><a href="https://refactoring.guru/design-patterns">Design Patterns</a></li>
            <li><a href="https://martinfowler.com/articles/microservices.html">Microservices by Martin Fowler</a></li>
        </ul>
    </div>

    <div class="manual-section" id="feedback">
        <h3>9.4. Feedback e Contribuições</h3>
        <p>
            Este manual é um documento vivo que evolui com o tempo. Se você tiver sugestões, correções ou contribuições, por favor, entre em contato com a equipe de arquitetura ou abra um pull request no repositório do projeto.
        </p>
        <p>
            Juntos, podemos continuar melhorando nossas práticas de desenvolvimento e construindo software de alta qualidade.
        </p>
    </div>
</div>

<div class="manual-footer">
    <p>Manual de Implementação - Versão 1.0</p>
    <p>Última atualização: Outubro de 2023</p>
</div>


























</div>

        </div>

        <script>
            mermaid.initialize({ startOnLoad: true });
        </script>
    </body>
</html>
