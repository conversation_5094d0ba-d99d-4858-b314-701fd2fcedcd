Route::group([
'middleware' => 'api',
'prefix' => 'auth'
], function () {
Route::post('login', [AuthController::class, 'login']);
Route::post('register', [AuthController::class, 'register']);

Route::group([
'middleware' => 'jwt.verify'
], function () {
Route::post('logout', [AuthController::class, 'logout']);
Route::get('me', [AuthController::class, 'me']);
Route::post('refresh', [AuthController::class, 'refresh']);
});
});