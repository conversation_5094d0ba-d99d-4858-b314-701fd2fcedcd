<?php

namespace App\Repositories;

use App\Models\Example;
use Illuminate\Database\Eloquent\Model;

class ExampleRepository extends RepositoryAbstract implements RepositoryInterface
{
    public function __construct()
    {
        parent::__construct(new Example());
    }

    /**
     * Encontra um exemplo pelo nome
     */
    public function findByName(string $name): ?Model
    {
        return $this->model->where('name', $name)->first();
    }
}
