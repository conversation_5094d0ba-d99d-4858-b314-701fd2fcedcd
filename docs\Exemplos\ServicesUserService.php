<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\WelcomeEmail;

/**
 * Serviço para gerenciamento de usuários
 * 
 * Implementa lógica de negócio relacionada a usuários
 */
class UserService extends ServiceAbstract
{
    /**
     * Regras de validação para criação
     *
     * @var array
     */
    protected $createRules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:users,email',
        'password' => 'required|string|min:8|confirmed',
    ];

    /**
     * Regras de validação para atualização
     *
     * @var array
     */
    protected $updateRules = [
        'name' => 'sometimes|string|max:255',
        'email' => 'sometimes|email|unique:users,email',
        'password' => 'sometimes|nullable|string|min:8|confirmed',
    ];

    /**
     * Mensagens de validação personalizadas
     *
     * @var array
     */
    protected $validationMessages = [
        'name.required' => 'O nome é obrigatório.',
        'email.required' => 'O email é obrigatório.',
        'email.email' => 'O email deve ser um endereço válido.',
        'email.unique' => 'Este email já está em uso.',
        'password.required' => 'A senha é obrigatória.',
        'password.min' => 'A senha deve ter pelo menos 8 caracteres.',
        'password.confirmed' => 'A confirmação da senha não corresponde.',
    ];

    /**
     * Construtor
     *
     * @param UserRepository $repository
     */
    public function __construct(UserRepository $repository)
    {
        parent::__construct($repository);
    }

    /**
     * Sobrescreve o método afterCreate para enviar email de boas-vindas
     *
     * @param User $model
     * @param array $data
     * @return void
     */
    protected function afterCreate(User $model, array $data): void
    {
        parent::afterCreate($model, $data);

        try {
            // Enviar email de boas-vindas
            Mail::to($model->email)->send(new WelcomeEmail($model));
        } catch (\Exception $e) {
            Log::error('Erro ao enviar email de boas-vindas', [
                'user_id' => $model->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Verifica credenciais de login
     *
     * @param string $email
     * @param string $password
     * @return User|null
     */
    public function verifyCredentials(string $email, string $password): ?User
    {
        /** @var UserRepository $repo */
        $repo = $this->repository;
        $user = $repo->findByEmail($email);

        if ($user && \Hash::check($password, $user->password)) {
            return $user;
        }

        return null;
    }

    /**
     * Gera token de acesso para o usuário
     *
     * @param User $user
     * @param string $deviceName
     * @return string
     */
    public function createAccessToken(User $user, string $deviceName = 'API Token'): string
    {
        // Revogar tokens anteriores se necessário
        // $user->tokens()->delete();

        return $user->createToken($deviceName)->plainTextToken;
    }

    /**
     * Transforma um usuário para resposta da API
     *
     * @param User $user
     * @return array
     */
    public function transformUser(User $user): array
    {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'email_verified' => $user->email_verified_at !== null,
            'created_at' => $user->created_at->toIso8601String(),
            'updated_at' => $user->updated_at->toIso8601String(),
        ];
    }
}
