<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Manual de Componentes e Padrões de UI</title>
  <link rel="stylesheet" href="css/manual.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
  <header class="manual-header">
    <h1>Manual de Componentes e Padrões de UI</h1>
    <p>Guia completo de componentes, estilos e padrões de interface do projeto Vue.js</p>
  </header>

  <nav class="manual-nav">
    <ul>
      <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
      <li><a href="#introducao">Introdução</a></li>
      <li><a href="#design-system">Design System</a></li>
      <li><a href="#componentes-base">Componentes Base</a></li>
      <li><a href="#componentes-formulario">Formulários</a></li>
      <li><a href="#componentes-dados">Visualização de Dados</a></li>
      <li><a href="#layouts">Layouts</a></li>
      <li><a href="#responsividade">Responsividade</a></li>
      <li><a href="#acessibilidade">Acessibilidade</a></li>
      <li><a href="#boas-praticas">Boas Práticas</a></li>
      <li><a href="#conclusao">Conclusão</a></li>
    </ul>
  </nav>

  <section id="sumario" class="manual-section">
    <h2>Sumário</h2>
    <ul>
      <li><a href="#introducao">1. Introdução</a></li>
      <li><a href="#design-system">2. Design System</a>
        <ul>
          <li><a href="#cores">2.1. Cores</a></li>
          <li><a href="#tipografia">2.2. Tipografia</a></li>
          <li><a href="#espacamento">2.3. Espaçamento e Grid</a></li>
          <li><a href="#icones">2.4. Ícones</a></li>
        </ul>
      </li>
      <li><a href="#componentes-base">3. Componentes Base</a>
        <ul>
          <li><a href="#botoes">3.1. Botões</a></li>
          <li><a href="#cards">3.2. Cards</a></li>
          <li><a href="#alertas">3.3. Alertas e Notificações</a></li>
          <li><a href="#modais">3.4. Modais e Diálogos</a></li>
        </ul>
      </li>
      <li><a href="#componentes-formulario">4. Componentes de Formulário</a>
        <ul>
          <li><a href="#inputs">4.1. Inputs e Campos de Texto</a></li>
          <li><a href="#selects">4.2. Selects e Dropdowns</a></li>
          <li><a href="#checkboxes">4.3. Checkboxes e Radio Buttons</a></li>
          <li><a href="#validacao">4.4. Validação de Formulários</a></li>
        </ul>
      </li>
      <li><a href="#componentes-dados">5. Componentes de Visualização de Dados</a>
        <ul>
          <li><a href="#tabelas">5.1. Tabelas</a></li>
          <li><a href="#paginacao">5.2. Paginação</a></li>
          <li><a href="#filtros">5.3. Filtros e Ordenação</a></li>
          <li><a href="#listas">5.4. Listas e Grids</a></li>
        </ul>
      </li>
      <li><a href="#layouts">6. Layouts</a>
        <ul>
          <li><a href="#layout-principal">6.1. Layout Principal</a></li>
          <li><a href="#sidebar">6.2. Sidebar e Navegação</a></li>
          <li><a href="#header-footer">6.3. Header e Footer</a></li>
        </ul>
      </li>
      <li><a href="#responsividade">7. Responsividade</a>
        <ul>
          <li><a href="#breakpoints">7.1. Breakpoints</a></li>
          <li><a href="#grid-system">7.2. Sistema de Grid</a></li>
          <li><a href="#componentes-responsivos">7.3. Componentes Responsivos</a></li>
        </ul>
      </li>
      <li><a href="#acessibilidade">8. Acessibilidade</a>
        <ul>
          <li><a href="#contraste">8.1. Contraste e Cores</a></li>
          <li><a href="#navegacao-teclado">8.2. Navegação por Teclado</a></li>
          <li><a href="#aria">8.3. Atributos ARIA</a></li>
          <li><a href="#textos-alternativos">8.4. Textos Alternativos e Descrições</a></li>
          <li><a href="#navegacao-teclado">8.5. Navegação por Teclado</a></li>
          <li><a href="#testes-acessibilidade">8.6. Testes de Acessibilidade</a></li>
          <li><a href="#recursos-adicionais">8.7. Recursos Adicionais</a></li>
        </ul>
      </li>
      <li><a href="#boas-praticas">9. Boas Práticas</a>
        <ul>
          <li><a href="#reutilizacao">9.1. Reutilização de Componentes</a></li>
          <li><a href="#consistencia">9.2. Consistência Visual</a></li>
          <li><a href="#performance">9.3. Performance</a></li>
        </ul>
      </li>
      <li><a href="#conclusao">10. Conclusão</a></li>
    </ul>
  </section>

  <section id="introducao" class="manual-section">
    <h2>1. Introdução</h2>

    <div class="intro-text">
      <p>Este manual documenta os componentes de UI, padrões de design e diretrizes visuais utilizados em nosso
        projeto Vue.js. Ele serve como referência para garantir consistência visual e experiência do usuário em
        toda a aplicação.</p>

      <p>Nossa interface é construída com Bootstrap-Vue 3, aproveitando os componentes do Bootstrap 5 integrados
        nativamente com Vue.js 3. Além disso, implementamos componentes personalizados para atender às
        necessidades específicas do projeto.</p>
    </div>

    <div class="key-points">
      <h3>Pontos-chave</h3>
      <ul>
        <li>Biblioteca principal: Bootstrap-Vue 3 com Bootstrap 5</li>
        <li>Componentes personalizados para necessidades específicas</li>
        <li>Design responsivo para todas as resoluções de tela</li>
        <li>Foco em acessibilidade e usabilidade</li>
        <li>Consistência visual em toda a aplicação</li>
        <li>Reutilização de componentes para manutenção eficiente</li>
      </ul>
    </div>
  </section>

  <section id="design-system" class="manual-section">
    <h2>2. Design System</h2>
    <p>Nosso design system define os elementos fundamentais da interface do usuário, garantindo consistência visual
      em toda a aplicação.</p>

    <div id="cores" class="subsection">
      <h3>2.1. Cores</h3>

      <p>Nossa paleta de cores é baseada nas variáveis do Bootstrap, com algumas personalizações para refletir a
        identidade visual do projeto.</p>

      <div
        style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 1rem; margin: 1.5rem 0;">
        <div style="padding: 1.5rem; background-color: #3490dc; color: white; border-radius: 4px;">
          <strong>Primary</strong><br>
          #3490dc
        </div>
        <div style="padding: 1.5rem; background-color: #6c757d; color: white; border-radius: 4px;">
          <strong>Secondary</strong><br>
          #6c757d
        </div>
        <div style="padding: 1.5rem; background-color: #38c172; color: white; border-radius: 4px;">
          <strong>Success</strong><br>
          #38c172
        </div>
        <div style="padding: 1.5rem; background-color: #6cb2eb; color: white; border-radius: 4px;">
          <strong>Info</strong><br>
          #6cb2eb
        </div>
        <div style="padding: 1.5rem; background-color: #ffed4a; color: black; border-radius: 4px;">
          <strong>Warning</strong><br>
          #ffed4a
        </div>
        <div style="padding: 1.5rem; background-color: #e3342f; color: white; border-radius: 4px;">
          <strong>Danger</strong><br>
          #e3342f
        </div>
        <div
          style="padding: 1.5rem; background-color: #f8f9fa; color: black; border-radius: 4px; border: 1px solid #dee2e6;">
          <strong>Light</strong><br>
          #f8f9fa
        </div>
        <div style="padding: 1.5rem; background-color: #343a40; color: white; border-radius: 4px;">
          <strong>Dark</strong><br>
          #343a40
        </div>
      </div>

      <div class="code-block">
        <h4>Personalização de Cores</h4>
        <pre><code>// src/assets/scss/_variables.scss
$primary: #3490dc;
$secondary: #6c757d;
$success: #38c172;
$info: #6cb2eb;
$warning: #ffed4a;
$danger: #e3342f;
$light: #f8f9fa;
$dark: #343a40;

// Importar no main.scss
@import 'variables';
@import 'bootstrap/scss/bootstrap';</code></pre>
      </div>

      <div class="best-practice">
        <h4>Uso de Cores</h4>
        <ul>
          <li><strong>Primary</strong>: Ações principais, links, elementos de destaque</li>
          <li><strong>Secondary</strong>: Ações secundárias, elementos de suporte</li>
          <li><strong>Success</strong>: Feedback positivo, confirmações, status de sucesso</li>
          <li><strong>Info</strong>: Informações, dicas, status neutro</li>
          <li><strong>Warning</strong>: Alertas, atenção, status de aviso</li>
          <li><strong>Danger</strong>: Erros, ações destrutivas, status crítico</li>
        </ul>
      </div>
    </div>

    <div id="tipografia" class="subsection">
      <h3>2.2. Tipografia</h3>

      <p>Utilizamos a família de fontes Segoe UI/Roboto como padrão, com tamanhos e pesos específicos para
        diferentes elementos.</p>

      <div style="margin: 1.5rem 0;">
        <h1 style="margin-bottom: 0.5rem;">Título H1 (2rem)</h1>
        <h2 style="margin-bottom: 0.5rem;">Título H2 (1.75rem)</h2>
        <h3 style="margin-bottom: 0.5rem;">Título H3 (1.5rem)</h3>
        <h4 style="margin-bottom: 0.5rem;">Título H4 (1.25rem)</h4>
        <h5 style="margin-bottom: 0.5rem;">Título H5 (1.1rem)</h5>
        <h6 style="margin-bottom: 0.5rem;">Título H6 (1rem)</h6>
        <p style="margin-bottom: 0.5rem;">Texto padrão (0.9rem)</p>
        <p><small>Texto pequeno (0.8rem)</small></p>
      </div>

      <div class="code-block">
        <h4>Configuração de Tipografia</h4>
        <pre><code>// src/assets/scss/_variables.scss
$font-family-sans-serif: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-size-base: 0.9rem;
$line-height-base: 1.6;

$h1-font-size: $font-size-base * 2.22;
$h2-font-size: $font-size-base * 1.94;
$h3-font-size: $font-size-base * 1.67;
$h4-font-size: $font-size-base * 1.39;
$h5-font-size: $font-size-base * 1.22;
$h6-font-size: $font-size-base * 1.11;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes de Tipografia</h4>
        <ul>
          <li>Use H1 apenas uma vez por página, para o título principal</li>
          <li>Use H2 para seções principais</li>
          <li>Use H3 e H4 para subseções</li>
          <li>Mantenha a hierarquia de títulos (não pule de H2 para H4)</li>
          <li>Use texto em negrito para ênfase, não para títulos</li>
          <li>Limite o uso de itálico para citações ou termos específicos</li>
        </ul>
      </div>
    </div>

    <div id="espacamento" class="subsection">
      <h3>2.3. Espaçamento e Grid</h3>

      <p>Utilizamos o sistema de espaçamento e grid do Bootstrap 5, com algumas personalizações para nosso
        projeto.</p>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Classe</th>
              <th>Tamanho</th>
              <th>Uso</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>m-0, p-0</td>
              <td>0</td>
              <td>Sem margem/padding</td>
            </tr>
            <tr>
              <td>m-1, p-1</td>
              <td>0.25rem (4px)</td>
              <td>Espaçamento extra pequeno</td>
            </tr>
            <tr>
              <td>m-2, p-2</td>
              <td>0.5rem (8px)</td>
              <td>Espaçamento pequeno</td>
            </tr>
            <tr>
              <td>m-3, p-3</td>
              <td>1rem (16px)</td>
              <td>Espaçamento médio</td>
            </tr>
            <tr>
              <td>m-4, p-4</td>
              <td>1.5rem (24px)</td>
              <td>Espaçamento grande</td>
            </tr>
            <tr>
              <td>m-5, p-5</td>
              <td>3rem (48px)</td>
              <td>Espaçamento extra grande</td>
            </tr>
          </tbody>
        </table>
      </div>

      <p>O sistema de grid do Bootstrap 5 é baseado em 12 colunas, com breakpoints responsivos:</p>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Breakpoint</th>
              <th>Prefixo</th>
              <th>Largura</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Extra small</td>
              <td>xs</td>
              <td>&lt; 576px</td>
            </tr>
            <tr>
              <td>Small</td>
              <td>sm</td>
              <td>≥ 576px</td>
            </tr>
            <tr>
              <td>Medium</td>
              <td>md</td>
              <td>≥ 768px</td>
            </tr>
            <tr>
              <td>Large</td>
              <td>lg</td>
              <td>≥ 992px</td>
            </tr>
            <tr>
              <td>Extra large</td>
              <td>xl</td>
              <td>≥ 1200px</td>
            </tr>
            <tr>
              <td>Extra extra large</td>
              <td>xxl</td>
              <td>≥ 1400px</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="code-block">
        <h4>Exemplo de Grid Responsivo</h4>
        <pre><code>&lt;template&gt;
  &lt;b-container&gt;
    &lt;b-row&gt;
      &lt;b-col cols="12" md="6" lg="4"&gt;
        &lt;!-- Conteúdo da coluna 1 --&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="12" md="6" lg="4"&gt;
        &lt;!-- Conteúdo da coluna 2 --&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="12" md="12" lg="4"&gt;
        &lt;!-- Conteúdo da coluna 3 --&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
  &lt;/b-container&gt;
&lt;/template&gt;</code></pre>
      </div>
    </div>

    <div id="icones" class="subsection">
      <h3>2.4. Ícones</h3>

      <p>Utilizamos a biblioteca Font Awesome 6 para ícones, integrada ao projeto.</p>

      <div
        style="display: grid; grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); gap: 1rem; margin: 1.5rem 0; text-align: center;">
        <div>
          <i class="fas fa-user" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
          <br>
          <code>fa-user</code>
        </div>
        <div>
          <i class="fas fa-home" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
          <br>
          <code>fa-home</code>
        </div>
        <div>
          <i class="fas fa-cog" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
          <br>
          <code>fa-cog</code>
        </div>
        <div>
          <i class="fas fa-edit" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
          <br>
          <code>fa-edit</code>
        </div>
        <div>
          <i class="fas fa-trash" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
          <br>
          <code>fa-trash</code>
        </div>
        <div>
          <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
          <br>
          <code>fa-search</code>
        </div>
        <div>
          <i class="fas fa-plus" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
          <br>
          <code>fa-plus</code>
        </div>
        <div>
          <i class="fas fa-check" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
          <br>
          <code>fa-check</code>
        </div>
      </div>

      <div class="code-block">
        <h4>Uso de Ícones</h4>
        <pre><code>&lt;template&gt;
  &lt;!-- Ícone básico --&gt;
  &lt;i class="fas fa-user"&gt;&lt;/i&gt;
  
  &lt;!-- Ícone em botão --&gt;
  &lt;b-button&gt;
    &lt;i class="fas fa-save"&gt;&lt;/i&gt; Salvar
  &lt;/b-button&gt;
  
  &lt;!-- Ícone com tamanho personalizado --&gt;
  &lt;i class="fas fa-home fa-2x"&gt;&lt;/i&gt;
  
  &lt;!-- Ícone com animação --&gt;
  &lt;i class="fas fa-spinner fa-spin"&gt;&lt;/i&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Ícones</h4>
        <ul>
          <li>Use ícones para melhorar a compreensão, não como decoração</li>
          <li>Combine ícones com texto para melhor acessibilidade</li>
          <li>Mantenha consistência no uso de ícones em toda a aplicação</li>
          <li>Use o tamanho apropriado para o contexto (fa-sm, fa-lg, fa-2x, etc.)</li>
          <li>Adicione aria-label para ícones sem texto associado</li>
        </ul>
      </div>
    </div>
  </section>

  <section id="componentes-base" class="manual-section">
    <h2>3. Componentes Base</h2>
    <p>Os componentes base são os blocos fundamentais da interface do usuário, utilizados em toda a aplicação.</p>

    <div id="botoes" class="subsection">
      <h3>3.1. Botões</h3>

      <p>Utilizamos os botões do Bootstrap-Vue com algumas personalizações para nosso projeto.</p>

      <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin: 1.5rem 0;">
        <button class="btn btn-primary">Primary</button>
        <button class="btn btn-secondary">Secondary</button>
        <button class="btn btn-success">Success</button>
        <button class="btn btn-danger">Danger</button>
        <button class="btn btn-warning">Warning</button>
        <button class="btn btn-info">Info</button>
        <button class="btn btn-light">Light</button>
        <button class="btn btn-dark">Dark</button>
        <button class="btn btn-link">Link</button>
      </div>

      <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin: 1.5rem 0;">
        <button class="btn btn-outline-primary">Primary</button>
        <button class="btn btn-outline-secondary">Secondary</button>
        <button class="btn btn-outline-success">Success</button>
        <button class="btn btn-outline-danger">Danger</button>
        <button class="btn btn-outline-warning">Warning</button>
        <button class="btn btn-outline-info">Info</button>
        <button class="btn btn-outline-light">Light</button>
        <button class="btn btn-outline-dark">Dark</button>
      </div>

      <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin: 1.5rem 0;">
        <button class="btn btn-primary btn-sm">Small</button>
        <button class="btn btn-primary">Default</button>
        <button class="btn btn-primary btn-lg">Large</button>
      </div>

      <div class="code-block">
        <h4>Uso de Botões</h4>
        <pre><code>&lt;template&gt;
  &lt;!-- Botão básico --&gt;
  &lt;b-button variant="primary"&gt;Botão Primário&lt;/b-button&gt;
  
  &lt;!-- Botão outline --&gt;
  &lt;b-button variant="outline-primary"&gt;Botão Outline&lt;/b-button&gt;
  
  &lt;!-- Botão com ícone --&gt;
  &lt;b-button variant="success"&gt;
    &lt;i class="fas fa-save"&gt;&lt;/i&gt; Salvar
  &lt;/b-button&gt;
  
  &lt;!-- Botão de tamanho pequeno --&gt;
  &lt;b-button variant="info" size="sm"&gt;Botão Pequeno&lt;/b-button&gt;
  
  &lt;!-- Botão desabilitado --&gt;
  &lt;b-button variant="primary" disabled&gt;Desabilitado&lt;/b-button&gt;
  
  &lt;!-- Botão com loading --&gt;
  &lt;b-button variant="primary" :disabled="isLoading"&gt;
    &lt;b-spinner small v-if="isLoading"&gt;&lt;/b-spinner&gt;
    {{ isLoading ? 'Carregando...' : 'Carregar' }}
  &lt;/b-button&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Botões</h4>
        <ul>
          <li><strong>Botões primários</strong>: Use para a ação principal em cada tela</li>
          <li><strong>Botões secundários</strong>: Use para ações alternativas ou menos importantes</li>
          <li><strong>Botões de perigo</strong>: Use para ações destrutivas (excluir, remover)</li>
          <li><strong>Botões outline</strong>: Use para ações secundárias com menor ênfase visual</li>
          <li>Adicione ícones para melhorar a compreensão</li>
          <li>Use texto claro e conciso (verbos de ação)</li>
          <li>Mantenha consistência no posicionamento (ex: botões de confirmação sempre à direita)</li>
        </ul>
      </div>
    </div>

    <div id="cards" class="subsection">
      <h3>3.2. Cards</h3>

      <p>Cards são utilizados para agrupar conteúdo relacionado em um contêiner com estilo consistente.</p>

      <div class="code-block">
        <h4>Exemplo de Card Básico</h4>
        <pre><code>&lt;template&gt;
  &lt;b-card
    title="Título do Card"
    img-src="https://picsum.photos/600/300/?image=25"
    img-alt="Imagem"
    img-top
    tag="article"
    class="mb-3"
  &gt;
    &lt;b-card-text&gt;
      Este é um exemplo de card com imagem e conteúdo.
    &lt;/b-card-text&gt;
    
    &lt;b-button href="#" variant="primary"&gt;Saiba mais&lt;/b-button&gt;
  &lt;/b-card&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Card com Header e Footer</h4>
        <pre><code>&lt;template&gt;
  &lt;b-card no-body class="mb-3"&gt;
    &lt;b-card-header&gt;
      &lt;h5 class="mb-0"&gt;Detalhes do Usuário&lt;/h5&gt;
    &lt;/b-card-header&gt;
    
    &lt;b-card-body&gt;
      &lt;b-card-title&gt;João Silva&lt;/b-card-title&gt;
      &lt;b-card-sub-title class="mb-2"&gt;Desenvolvedor&lt;/b-card-sub-title&gt;
      
      &lt;b-card-text&gt;
        &lt;p&gt;&lt;strong&gt;Email:&lt;/strong&gt; <EMAIL>&lt;/p&gt;
        &lt;p&gt;&lt;strong&gt;Telefone:&lt;/strong&gt; (11) 98765-4321&lt;/p&gt;
      &lt;/b-card-text&gt;
    &lt;/b-card-body&gt;
    
    &lt;b-card-footer&gt;
      &lt;small class="text-muted"&gt;Última atualização: 3 dias atrás&lt;/small&gt;
    &lt;/b-card-footer&gt;
  &lt;/b-card&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Card Personalizado para Usuário</h4>
        <pre><code>&lt;template&gt;
  &lt;b-card
    no-body
    class="user-card mb-3"
    :class="{ 'inactive-user': !user.ativo }"
  &gt;
    &lt;b-card-body&gt;
      &lt;div class="d-flex align-items-center mb-3"&gt;
        &lt;div class="user-avatar"&gt;
          &lt;img :src="user.avatar || defaultAvatar" :alt="user.nome" /&gt;
          &lt;span v-if="user.online" class="status-indicator online"&gt;&lt;/span&gt;
          &lt;span v-else class="status-indicator offline"&gt;&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class="ms-3"&gt;
          &lt;h5 class="mb-1"&gt;{{ user.nome }}&lt;/h5&gt;
          &lt;p class="text-muted mb-0"&gt;{{ user.cargo }}&lt;/p&gt;
        &lt;/div&gt;
        &lt;div class="ms-auto"&gt;
          &lt;b-badge v-if="user.ativo" variant="success"&gt;Ativo&lt;/b-badge&gt;
          &lt;b-badge v-else variant="secondary"&gt;Inativo&lt;/b-badge&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div class="user-details"&gt;
        &lt;p&gt;&lt;i class="fas fa-envelope me-2"&gt;&lt;/i&gt; {{ user.email }}&lt;/p&gt;
        &lt;p&gt;&lt;i class="fas fa-phone me-2"&gt;&lt;/i&gt; {{ user.telefone }}&lt;/p&gt;
        &lt;p&gt;&lt;i class="fas fa-building me-2"&gt;&lt;/i&gt; {{ user.departamento }}&lt;/p&gt;
      &lt;/div&gt;
    &lt;/b-card-body&gt;
    
    &lt;b-card-footer&gt;
      &lt;div class="d-flex justify-content-end"&gt;
        &lt;b-button variant="outline-primary" size="sm" class="me-2" @click="$emit('edit', user.id)"&gt;
          &lt;i class="fas fa-edit"&gt;&lt;/i&gt; Editar
        &lt;/b-button&gt;
        &lt;b-button variant="outline-danger" size="sm" @click="$emit('delete', user.id)"&gt;
          &lt;i class="fas fa-trash"&gt;&lt;/i&gt; Excluir
        &lt;/b-button&gt;
      &lt;/div&gt;
    &lt;/b-card-footer&gt;
  &lt;/b-card&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  user: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['edit', 'delete']);

const defaultAvatar = '/assets/images/default-avatar.png';
&lt;/script&gt;

&lt;style scoped&gt;
.user-card {
  transition: all 0.3s ease;
}

.user-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.inactive-user {
  opacity: 0.7;
}

.user-avatar {
  position: relative;
  width: 50px;
  height: 50px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.online {
  background-color: #38c172;
}

.status-indicator.offline {
  background-color: #6c757d;
}

.user-details {
  margin-top: 1rem;
  font-size: 0.9rem;
}

.user-details p {
  margin-bottom: 0.5rem;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Cards</h4>
        <ul>
          <li>Use cards para agrupar informações relacionadas</li>
          <li>Mantenha o conteúdo conciso e focado</li>
          <li>Use headers para títulos e footers para ações ou informações adicionais</li>
          <li>Adicione sombras sutis para elevação visual</li>
          <li>Mantenha o espaçamento interno consistente</li>
          <li>Use variantes de cores para indicar diferentes tipos de conteúdo</li>
        </ul>
      </div>

      <div id="alertas" class="subsection">
        <h3>3.3. Alertas e Notificações</h3>

        <p>Alertas e notificações são utilizados para fornecer feedback ao usuário sobre ações ou eventos.</p>

        <div style="display: flex; flex-direction: column; gap: 1rem; margin: 1.5rem 0;">
          <div class="alert alert-primary" role="alert">
            Este é um alerta primário — verifique!
          </div>
          <div class="alert alert-secondary" role="alert">
            Este é um alerta secundário — verifique!
          </div>
          <div class="alert alert-success" role="alert">
            Este é um alerta de sucesso — verifique!
          </div>
          <div class="alert alert-danger" role="alert">
            Este é um alerta de perigo — verifique!
          </div>
          <div class="alert alert-warning" role="alert">
            Este é um alerta de aviso — verifique!
          </div>
          <div class="alert alert-info" role="alert">
            Este é um alerta de informação — verifique!
          </div>
        </div>

        <div class="code-block">
          <h4>Alertas Básicos</h4>
          <pre><code>&lt;template&gt;
  &lt;!-- Alerta básico --&gt;
  &lt;b-alert show variant="success"&gt;Operação realizada com sucesso!&lt;/b-alert&gt;
  
  &lt;!-- Alerta com ícone --&gt;
  &lt;b-alert show variant="danger"&gt;
    &lt;i class="fas fa-exclamation-triangle me-2"&gt;&lt;/i&gt;
    Ocorreu um erro ao processar sua solicitação.
  &lt;/b-alert&gt;
  
  &lt;!-- Alerta dispensável --&gt;
  &lt;b-alert 
    v-model="showDismissibleAlert"
    variant="warning"
    dismissible
  &gt;
    Este alerta pode ser fechado.
  &lt;/b-alert&gt;
  
  &lt;!-- Alerta com contador --&gt;
  &lt;b-alert
    :show="dismissCountDown"
    dismissible
    variant="info"
    @dismissed="dismissCountDown=0"
    @dismiss-count-down="countDownChanged"
  &gt;
    Este alerta será fechado em {{ dismissCountDown }} segundos...
  &lt;/b-alert&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const showDismissibleAlert = ref(true);
const dismissCountDown = ref(10);

const countDownChanged = (countDown) => {
  dismissCountDown.value = countDown;
};
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Componente de Toast Notification</h4>
          <pre><code>&lt;template&gt;
  &lt;div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11"&gt;
    &lt;b-toast
      v-for="(toast, index) in toasts"
      :key="index"
      :id="`toast-${index}`"
      :variant="toast.variant"
      :title="toast.title"
      :auto-hide-delay="toast.autoHideDelay || 5000"
      @hidden="removeToast(index)"
      solid
    &gt;
      {{ toast.message }}
    &lt;/b-toast&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted } from 'vue';
import { useToastStore } from '@/stores/toastStore';

const toastStore = useToastStore();
const toasts = ref([]);

// Observa mudanças na store de toasts
onMounted(() => {
  toastStore.$subscribe((mutation, state) => {
    toasts.value = [...state.toasts];
    
    // Mostra cada toast
    toasts.value.forEach((toast, index) => {
      this.$bvToast.show(`toast-${index}`);
    });
  });
});

const removeToast = (index) => {
  toastStore.removeToast(index);
};
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Store para Gerenciamento de Toasts</h4>
          <pre><code>// src/stores/toastStore.js
import { defineStore } from 'pinia';

export const useToastStore = defineStore('toast', {
  state: () => ({
    toasts: []
  }),
  
  actions: {
    addToast(toast) {
      this.toasts.push({
        variant: toast.variant || 'primary',
        title: toast.title || 'Notificação',
        message: toast.message,
        autoHideDelay: toast.autoHideDelay || 5000
      });
    },
    
    removeToast(index) {
      this.toasts.splice(index, 1);
    },
    
    // Métodos de conveniência
    success(message, title = 'Sucesso') {
      this.addToast({
        variant: 'success',
        title,
        message
      });
    },
    
    error(message, title = 'Erro') {
      this.addToast({
        variant: 'danger',
        title,
        message,
        autoHideDelay: 8000 // Erros ficam visíveis por mais tempo
      });
    },
    
    info(message, title = 'Informação') {
      this.addToast({
        variant: 'info',
        title,
        message
      });
    },
    
    warning(message, title = 'Atenção') {
      this.addToast({
        variant: 'warning',
        title,
        message,
        autoHideDelay: 7000
      });
    }
  }
});</code></pre>
        </div>

        <div class="best-practice">
          <h4>Diretrizes para Alertas e Notificações</h4>
          <ul>
            <li><strong>Alertas inline</strong>: Use para mensagens contextuais dentro de formulários ou
              páginas</li>
            <li><strong>Toasts</strong>: Use para notificações temporárias que não interrompem o fluxo</li>
            <li><strong>Cores semânticas</strong>: Use cores apropriadas para o tipo de mensagem (sucesso,
              erro, etc.)</li>
            <li><strong>Mensagens claras</strong>: Seja específico sobre o que aconteceu ou o que o usuário
              deve fazer</li>
            <li><strong>Duração apropriada</strong>: Ajuste o tempo de exibição conforme a importância da
              mensagem</li>
            <li><strong>Posicionamento</strong>: Toasts geralmente no canto superior direito ou inferior
              direito</li>
          </ul>
        </div>
      </div>

      <div id="modais" class="subsection">
        <h3>3.4. Modais e Diálogos</h3>

        <p>Modais e diálogos são utilizados para interações focadas que requerem atenção do usuário.</p>

        <div class="code-block">
          <h4>Modal Básico</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;b-button v-b-modal.modal-basic variant="primary"&gt;Abrir Modal&lt;/b-button&gt;
    
    &lt;b-modal id="modal-basic" title="Modal Básico" ok-title="Confirmar" cancel-title="Cancelar"&gt;
      &lt;p class="my-4"&gt;Conteúdo do modal aqui.&lt;/p&gt;
    &lt;/b-modal&gt;
  &lt;/div&gt;
&lt;/template&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Modal de Confirmação</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;b-button @click="showDeleteModal" variant="danger"&gt;Excluir Item&lt;/b-button&gt;
    
    &lt;b-modal
      v-model="isDeleteModalVisible"
      title="Confirmar Exclusão"
      ok-variant="danger"
      ok-title="Excluir"
      cancel-title="Cancelar"
      @ok="handleDelete"
    &gt;
      &lt;div class="d-flex align-items-center"&gt;
        &lt;div class="modal-icon warning me-3"&gt;
          &lt;i class="fas fa-exclamation-triangle fa-2x text-danger"&gt;&lt;/i&gt;
        &lt;/div&gt;
        &lt;div&gt;
          &lt;p class="mb-2"&gt;Tem certeza que deseja excluir este item?&lt;/p&gt;
          &lt;p class="text-muted mb-0"&gt;Esta ação não poderá ser desfeita.&lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/b-modal&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const isDeleteModalVisible = ref(false);
const itemToDelete = ref(null);

const showDeleteModal = (item) => {
  itemToDelete.value = item;
  isDeleteModalVisible.value = true;
};

const handleDelete = () => {
  // Lógica para excluir o item
  console.log('Excluindo item:', itemToDelete.value);
  
  // Resetar estado
  itemToDelete.value = null;
};
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Modal de Formulário</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;b-button v-b-modal.modal-form variant="primary"&gt;Adicionar Usuário&lt;/b-button&gt;
    
    &lt;b-modal
      id="modal-form"
      title="Adicionar Usuário"
      hide-footer
      @hidden="resetForm"
    &gt;
      &lt;b-form @submit.prevent="handleSubmit"&gt;
        &lt;b-form-group
          label="Nome"
          label-for="input-name"
          :invalid-feedback="errors.name"
          :state="!errors.name"
        &gt;
          &lt;b-form-input
            id="input-name"
            v-model="form.name"
            :state="!errors.name"
            trim
          &gt;&lt;/b-form-input&gt;
        &lt;/b-form-group&gt;
        
        &lt;b-form-group
          label="Email"
          label-for="input-email"
          :invalid-feedback="errors.email"
          :state="!errors.email"
        &gt;
          &lt;b-form-input
            id="input-email"
            v-model="form.email"
            type="email"
            :state="!errors.email"
            trim
          &gt;&lt;/b-form-input&gt;
        &lt;/b-form-group&gt;
        
        &lt;div class="d-flex justify-content-end mt-3"&gt;
          &lt;b-button
            variant="secondary"
            class="me-2"
            @click="$bvModal.hide('modal-form')"
          &gt;
            Cancelar
          &lt;/b-button&gt;
          
          &lt;b-button
            type="submit"
            variant="primary"
            :disabled="isSubmitting"
          &gt;
            &lt;b-spinner small v-if="isSubmitting"&gt;&lt;/b-spinner&gt;
            {{ isSubmitting ? 'Salvando...' : 'Salvar' }}
          &lt;/b-button&gt;
        &lt;/div&gt;
      &lt;/b-form&gt;
    &lt;/b-modal&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, reactive } from 'vue';

const form = reactive({
  name: '',
  email: ''
});

const errors = reactive({
  name: '',
  email: ''
});

const isSubmitting = ref(false);

const validate = () => {
  let isValid = true;
  
  // Validar nome
  if (!form.name) {
    errors.name = 'O nome é obrigatório';
    isValid = false;
  } else {
    errors.name = '';
  }
  
  // Validar email
  if (!form.email) {
    errors.email = 'O email é obrigatório';
    isValid = false;
  } else if (!/\S+@\S+\.\S+/.test(form.email)) {
    errors.email = 'Email inválido';
    isValid = false;
  } else {
    errors.email = '';
  }
  
  return isValid;
};

const handleSubmit = async () => {
  if (!validate()) return;
  
  isSubmitting.value = true;
  
  try {
    // Simulação de chamada de API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Fechar modal
    this.$bvModal.hide('modal-form');
    
    // Mostrar notificação de sucesso
    this.$toast.success('Usuário adicionado com sucesso!');
  } catch (error) {
    console.error('Erro ao adicionar usuário:', error);
    this.$toast.error('Erro ao adicionar usuário.');
  } finally {
    isSubmitting.value = false;
  }
};

const resetForm = () => {
  form.name = '';
  form.email = '';
  errors.name = '';
  errors.email = '';
  isSubmitting.value = false;
};
&lt;/script&gt;</code></pre>
        </div>

        <div class="best-practice">
          <h4>Diretrizes para Modais</h4>
          <ul>
            <li>Use modais para interações focadas que requerem atenção do usuário</li>
            <li>Mantenha títulos claros e descritivos</li>
            <li>Limite o conteúdo ao essencial para a tarefa</li>
            <li>Forneça botões de ação claros (primário à direita, secundário à esquerda)</li>
            <li>Permita que o usuário feche o modal clicando fora, pressionando ESC ou no botão de fechar
            </li>
            <li>Evite modais aninhados (modal dentro de modal)</li>
            <li>Para formulários complexos, considere uma página dedicada em vez de um modal</li>
          </ul>
        </div>

        <div class="code-block">
          <h4>Componente de Diálogo de Confirmação Reutilizável</h4>
          <pre><code>// src/components/common/ConfirmDialog.vue
&lt;template&gt;
  &lt;b-modal
    v-model="isVisible"
    :title="title"
    :ok-variant="okVariant"
    :ok-title="okTitle"
    :cancel-title="cancelTitle"
    @ok="confirm"
    @cancel="cancel"
    @hidden="onHidden"
  &gt;
    &lt;div class="d-flex align-items-center"&gt;
      &lt;div class="modal-icon me-3" :class="iconClass"&gt;
        &lt;i :class="iconClass"&gt;&lt;/i&gt;
      &lt;/div&gt;
      &lt;div&gt;
        &lt;p class="mb-2"&gt;{{ message }}&lt;/p&gt;
        &lt;p v-if="subMessage" class="text-muted mb-0"&gt;{{ subMessage }}&lt;/p&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/b-modal&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Confirmar'
  },
  message: {
    type: String,
    required: true
  },
  subMessage: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'warning', // warning, danger, info, success
    validator: (value) => ['warning', 'danger', 'info', 'success'].includes(value)
  },
  okTitle: {
    type: String,
    default: 'Confirmar'
  },
  cancelTitle: {
    type: String,
    default: 'Cancelar'
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const iconClass = computed(() => {
  const baseClass = 'fas fa-2x ';
  
  switch (props.type) {
    case 'warning':
      return baseClass + 'fa-exclamation-triangle text-warning';
    case 'danger':
      return baseClass + 'fa-exclamation-circle text-danger';
    case 'info':
      return baseClass + 'fa-info-circle text-info';
    case 'success':
      return baseClass + 'fa-check-circle text-success';
    default:
      return baseClass + 'fa-question-circle text-primary';
  }
});

const okVariant = computed(() => {
  switch (props.type) {
    case 'warning':
      return 'warning';
    case 'danger':
      return 'danger';
    case 'info':
      return 'info';
    case 'success':
      return 'success';
    default:
      return 'primary';
  }
});

const confirm = () => {
  emit('confirm');
};

const cancel = () => {
  emit('cancel');
};

const onHidden = () => {
  emit('update:modelValue', false);
};
&lt;/script&gt;

&lt;style scoped&gt;
.modal-icon {
  font-size: 1.5rem;
  width: 3rem;
  text-align: center;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Uso do Componente de Diálogo</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;b-button @click="showDeleteConfirm" variant="danger"&gt;Excluir&lt;/b-button&gt;
    
    &lt;confirm-dialog
      v-model="isConfirmVisible"
      title="Confirmar Exclusão"
      message="Tem certeza que deseja excluir este item?"
      sub-message="Esta ação não poderá ser desfeita."
      type="danger"
      ok-title="Excluir"
      @confirm="handleDelete"
    /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';

const isConfirmVisible = ref(false);
const itemToDelete = ref(null);

const showDeleteConfirm = (item) => {
  itemToDelete.value = item;
  isConfirmVisible.value = true;
};

const handleDelete = () => {
  // Lógica para excluir o item
  console.log('Excluindo item:', itemToDelete.value);
  
  // Resetar estado
  itemToDelete.value = null;
};
&lt;/script&gt;</code></pre>
        </div>
      </div>
    </div>
  </section>
  <section id="componentes-formulario" class="manual-section">
    <h2>4. Componentes de Formulário</h2>
    <p>Componentes de formulário são essenciais para a interação do usuário e coleta de dados.</p>

    <div id="inputs" class="subsection">
      <h3>4.1. Inputs e Campos de Texto</h3>

      <p>Utilizamos os componentes de formulário do Bootstrap-Vue com algumas personalizações para
        melhorar a experiência do usuário.</p>

      <div class="code-block">
        <h4>Inputs Básicos</h4>
        <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;!-- Input de texto básico --&gt;
    &lt;b-form-group
      label="Nome"
      label-for="input-name"
      description="Digite seu nome completo"
    &gt;
      &lt;b-form-input
        id="input-name"
        v-model="form.name"
        placeholder="Nome completo"
        trim
      &gt;&lt;/b-form-input&gt;
    &lt;/b-form-group&gt;
    
    &lt;!-- Input de email --&gt;
    &lt;b-form-group
      label="Email"
      label-for="input-email"
    &gt;
      &lt;b-form-input
        id="input-email"
        v-model="form.email"
        type="email"
        placeholder="<EMAIL>"
      &gt;&lt;/b-form-input&gt;
    &lt;/b-form-group&gt;
    
    &lt;!-- Input de senha --&gt;
    &lt;b-form-group
      label="Senha"
      label-for="input-password"
    &gt;
      &lt;b-form-input
        id="input-password"
        v-model="form.password"
        type="password"
        placeholder="Senha"
      &gt;&lt;/b-form-input&gt;
    &lt;/b-form-group&gt;
    
    &lt;!-- Textarea --&gt;
    &lt;b-form-group
      label="Descrição"
      label-for="input-description"
    &gt;
      &lt;b-form-textarea
        id="input-description"
        v-model="form.description"
        placeholder="Digite uma descrição..."
        rows="3"
        max-rows="6"
      &gt;&lt;/b-form-textarea&gt;
    &lt;/b-form-group&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { reactive } from 'vue';

const form = reactive({
  name: '',
  email: '',
  password: '',
  description: ''
});
&lt;/script&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Input com Validação</h4>
        <pre><code>&lt;template&gt;
  &lt;b-form-group
    label="Email"
    label-for="input-email"
    :invalid-feedback="errors.email"
    :state="emailState"
  &gt;
    &lt;b-form-input
      id="input-email"
      v-model="email"
      type="email"
      :state="emailState"
      placeholder="<EMAIL>"
      @blur="validateEmail"
    &gt;&lt;/b-form-input&gt;
  &lt;/b-form-group&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';

const email = ref('');
const errors = reactive({
  email: ''
});

const validateEmail = () => {
  if (!email.value) {
    errors.email = 'O email é obrigatório';
  } else if (!/\S+@\S+\.\S+/.test(email.value)) {
    errors.email = 'Email inválido';
  } else {
    errors.email = '';
  }
};

const emailState = computed(() => {
  if (email.value === '') return null;
  return !errors.email;
});
&lt;/script&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Input Personalizado com Ícone</h4>
        <pre><code>&lt;template&gt;
  &lt;b-form-group
    label="Pesquisar"
    label-for="input-search"
  &gt;
    &lt;div class="position-relative"&gt;
      &lt;b-form-input
        id="input-search"
        v-model="search"
        placeholder="Pesquisar..."
        class="pe-4"
      &gt;&lt;/b-form-input&gt;
      &lt;div class="position-absolute top-50 end-0 translate-middle-y pe-3"&gt;
        &lt;i class="fas fa-search text-muted"&gt;&lt;/i&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/b-form-group&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const search = ref('');
&lt;/script&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Input Monetário</h4>
        <pre><code>&lt;template&gt;
  &lt;b-form-group
    :label="label"
    :label-for="id"
    :invalid-feedback="invalidFeedback"
    :state="state"
  &gt;
    &lt;div class="position-relative"&gt;
      &lt;div class="position-absolute top-50 start-0 translate-middle-y ps-3"&gt;
        &lt;span class="text-muted"&gt;R$&lt;/span&gt;
      &lt;/div&gt;
      &lt;b-form-input
        :id="id"
        v-model="displayValue"
        type="text"
        :placeholder="placeholder"
        :state="state"
        class="ps-4"
        @blur="formatOnBlur"
        @focus="selectOnFocus"
        @input="updateValue"
      &gt;&lt;/b-form-input&gt;
    &lt;/div&gt;
  &lt;/b-form-group&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: ''
  },
  label: {
    type: String,
    default: 'Valor'
  },
  id: {
    type: String,
    default: 'input-currency'
  },
  placeholder: {
    type: String,
    default: '0,00'
  },
  invalidFeedback: {
    type: String,
    default: ''
  },
  state: {
    type: Boolean,
    default: null
  }
});

const emit = defineEmits(['update:modelValue']);

const displayValue = ref('');

// Formata o valor para exibição
const formatCurrency = (value) => {
  if (!value && value !== 0) return '';
  
  // Converte para número e formata com 2 casas decimais
  const numValue = typeof value === 'string' ? parseFloat(value.replace(/\./g, '').replace(',', '.')) : value;
  
  if (isNaN(numValue)) return '';
  
  return numValue.toLocaleString('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// Converte o valor formatado para número
const parseValue = (value) => {
  if (!value) return '';
  
  // Remove tudo exceto números e vírgula
  const cleanValue = value.replace(/[^\d,]/g, '').replace(',', '.');
  
  return parseFloat(cleanValue);
};

// Atualiza o valor quando o modelo muda
watch(() => props.modelValue, (newValue) => {
  displayValue.value = formatCurrency(newValue);
}, { immediate: true });

// Formata o valor ao perder o foco
const formatOnBlur = () => {
  displayValue.value = formatCurrency(displayValue.value);
};

// Seleciona todo o texto ao focar
const selectOnFocus = (event) => {
  event.target.select();
};

// Atualiza o valor no modelo quando o input muda
const updateValue = () => {
  const value = parseValue(displayValue.value);
  emit('update:modelValue', isNaN(value) ? '' : value);
};
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Inputs</h4>
        <ul>
          <li>Use labels claros e descritivos para todos os campos</li>
          <li>Forneça placeholders informativos</li>
          <li>Adicione descrições ou dicas quando necessário</li>
          <li>Implemente validação em tempo real com feedback visual</li>
          <li>Use tipos de input apropriados (email, number, tel, etc.)</li>
          <li>Mantenha consistência no estilo e posicionamento dos labels</li>
          <li>Agrupe campos relacionados com b-form-group</li>
          <li>Forneça feedback visual para estados de erro, sucesso e carregamento</li>
        </ul>
      </div>

      <div id="selects" class="subsection">
        <h3>4.2. Selects e Dropdowns</h3>

        <p>Componentes de seleção permitem que os usuários escolham entre várias opções predefinidas.</p>

        <div class="code-block">
          <h4>Select Básico</h4>
          <pre><code>&lt;template&gt;
  &lt;b-form-group label="Departamento" label-for="select-department"&gt;
    &lt;b-form-select
      id="select-department"
      v-model="selectedDepartment"
      :options="departmentOptions"
    &gt;&lt;/b-form-select&gt;
  &lt;/b-form-group&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const selectedDepartment = ref(null);
const departmentOptions = [
  { value: null, text: 'Selecione um departamento' },
  { value: 'ti', text: 'Tecnologia da Informação' },
  { value: 'rh', text: 'Recursos Humanos' },
  { value: 'financeiro', text: 'Financeiro' },
  { value: 'marketing', text: 'Marketing' }
];
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Select com Grupos</h4>
          <pre><code>&lt;template&gt;
  &lt;b-form-group label="Cargo" label-for="select-role"&gt;
    &lt;b-form-select
      id="select-role"
      v-model="selectedRole"
      :options="roleOptions"
    &gt;&lt;/b-form-select&gt;
  &lt;/b-form-group&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const selectedRole = ref(null);
const roleOptions = [
  { value: null, text: 'Selecione um cargo' },
  { 
    label: 'Tecnologia',
    options: [
      { value: 'dev_junior', text: 'Desenvolvedor Júnior' },
      { value: 'dev_pleno', text: 'Desenvolvedor Pleno' },
      { value: 'dev_senior', text: 'Desenvolvedor Sênior' },
      { value: 'tech_lead', text: 'Tech Lead' }
    ]
  },
  { 
    label: 'Gestão',
    options: [
      { value: 'coordinator', text: 'Coordenador' },
      { value: 'manager', text: 'Gerente' },
      { value: 'director', text: 'Diretor' }
    ]
  }
];
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Select Múltiplo</h4>
          <pre><code>&lt;template&gt;
  &lt;b-form-group label="Habilidades" label-for="select-skills"&gt;
    &lt;b-form-select
      id="select-skills"
      v-model="selectedSkills"
      :options="skillOptions"
      multiple
      :select-size="4"
    &gt;&lt;/b-form-select&gt;
    &lt;div class="mt-2"&gt;Selecionados: {{ selectedSkills.length }}&lt;/div&gt;
  &lt;/b-form-group&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const selectedSkills = ref([]);
const skillOptions = [
  { value: 'javascript', text: 'JavaScript' },
  { value: 'vue', text: 'Vue.js' },
  { value: 'react', text: 'React' },
  { value: 'angular', text: 'Angular' },
  { value: 'node', text: 'Node.js' },
  { value: 'php', text: 'PHP' },
  { value: 'python', text: 'Python' },
  { value: 'java', text: 'Java' }
];
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Componente de Select com Busca</h4>
          <pre><code>&lt;template&gt;
  &lt;b-form-group :label="label" :label-for="id"&gt;
    &lt;v-select
      :id="id"
      v-model="selected"
      :options="options"
      :reduce="option => option.value"
      :clearable="clearable"
      :searchable="searchable"
      :placeholder="placeholder"
      :multiple="multiple"
      @input="updateValue"
    &gt;
      &lt;template #no-options="{ search }"&gt;
        Nenhum resultado encontrado para &lt;em&gt;{{ search }}&lt;/em&gt;
      &lt;/template&gt;
      
      &lt;template #selected-option="{ label }"&gt;
        &lt;div class="selected-option"&gt;{{ label }}&lt;/div&gt;
      &lt;/template&gt;
      
      &lt;template #option="{ label }"&gt;
        &lt;div class="option"&gt;{{ label }}&lt;/div&gt;
      &lt;/template&gt;
    &lt;/v-select&gt;
  &lt;/b-form-group&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, watch } from 'vue';
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';

const props = defineProps({
  modelValue: {
    type: [String, Number, Array, Object],
    default: ''
  },
  options: {
    type: Array,
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: 'searchable-select'
  },
  placeholder: {
    type: String,
    default: 'Selecione uma opção'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  searchable: {
    type: Boolean,
    default: true
  },
  multiple: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

const selected = ref(props.modelValue);

watch(() => props.modelValue, (newValue) => {
  selected.value = newValue;
});

const updateValue = () => {
  emit('update:modelValue', selected.value);
};
&lt;/script&gt;

&lt;style scoped&gt;
/* Personalizações do vue-select */
:deep(.vs__dropdown-toggle) {
  padding: 0.375rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

:deep(.vs__selected) {
  margin: 0 0.25rem 0 0;
}

:deep(.vs__search) {
  padding: 0;
}

:deep(.vs__dropdown-menu) {
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

:deep(.vs__dropdown-option) {
  padding: 0.5rem 1rem;
}

:deep(.vs__dropdown-option--highlight) {
  background: #3490dc;
  color: #fff;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="best-practice">
          <h4>Diretrizes para Selects</h4>
          <ul>
            <li>Forneça uma opção padrão clara (ex: "Selecione uma opção")</li>
            <li>Ordene as opções de forma lógica (alfabética, numérica, etc.)</li>
            <li>Use grupos para organizar opções relacionadas</li>
            <li>Adicione busca para listas longas</li>
            <li>Considere usar select múltiplo para seleção de vários itens</li>
            <li>Forneça feedback visual para estados de erro e validação</li>
            <li>Considere usar componentes avançados como vue-select para funcionalidades adicionais</li>
          </ul>
        </div>
      </div>

      <div id="checkboxes" class="subsection">
        <h3>4.3. Checkboxes e Radio Buttons</h3>

        <p>Checkboxes e radio buttons permitem que os usuários selecionem uma ou várias opções de um conjunto.
        </p>

        <div class="code-block">
          <h4>Checkboxes Básicos</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;b-form-group label="Selecione as opções:"&gt;
      &lt;b-form-checkbox v-model="checked" value="aceito"&gt;
        Aceito os termos e condições
      &lt;/b-form-checkbox&gt;
      
      &lt;b-form-checkbox v-model="newsletter"&gt;
        Inscrever-se na newsletter
      &lt;/b-form-checkbox&gt;
    &lt;/b-form-group&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const checked = ref(false);
const newsletter = ref(false);
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Grupo de Checkboxes</h4>
          <pre><code>&lt;template&gt;
  &lt;b-form-group label="Selecione as tecnologias que você conhece:"&gt;
    &lt;b-form-checkbox-group
      v-model="selectedTechs"
      :options="techOptions"
      stacked
    &gt;&lt;/b-form-checkbox-group&gt;
    
    &lt;div class="mt-2"&gt;Selecionados: {{ selectedTechs }}&lt;/div&gt;
  &lt;/b-form-group&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const selectedTechs = ref([]);
const techOptions = [
  { text: 'HTML/CSS', value: 'html' },
  { text: 'JavaScript', value: 'js' },
  { text: 'Vue.js', value: 'vue' },
  { text: 'React', value: 'react' },
  { text: 'Angular', value: 'angular' }
];
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Radio Buttons</h4>
          <pre><code>&lt;template&gt;
  &lt;b-form-group label="Selecione uma opção:"&gt;
    &lt;b-form-radio-group
      v-model="selected"
      :options="options"
      stacked
    &gt;&lt;/b-form-radio-group&gt;
    
    &lt;div class="mt-2"&gt;Selecionado: {{ selected }}&lt;/div&gt;
  &lt;/b-form-group&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const selected = ref('');
const options = [
  { text: 'Opção 1', value: 'opcao1' },
  { text: 'Opção 2', value: 'opcao2' },
  { text: 'Opção 3', value: 'opcao3' }
];
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Switch</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;b-form-checkbox
      v-model="status"
      switch
    &gt;
      {{ status ? 'Ativo' : 'Inativo' }}
    &lt;/b-form-checkbox&gt;
    
    &lt;b-form-checkbox
      v-model="notifications"
      switch
      size="lg"
    &gt;
      Notificações {{ notifications ? 'ativadas' : 'desativadas' }}
    &lt;/b-form-checkbox&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const status = ref(true);
const notifications = ref(false);
&lt;/script&gt;</code></pre>
        </div>

        <div class="best-practice">
          <h4>Diretrizes para Checkboxes e Radio Buttons</h4>
          <ul>
            <li><strong>Checkboxes</strong>: Use para seleção múltipla ou opções independentes</li>
            <li><strong>Radio buttons</strong>: Use para seleção única entre opções mutuamente exclusivas
            </li>
            <li><strong>Switch</strong>: Use para alternar estados binários (ligado/desligado)</li>
            <li>Use labels claros e descritivos</li>
            <li>Agrupe opções relacionadas</li>
            <li>Considere usar a opção "stacked" para melhor legibilidade em listas longas</li>
            <li>Forneça um estado padrão apropriado</li>
            <li>Mantenha consistência no estilo e posicionamento</li>
          </ul>
        </div>
      </div>

      <div id="validacao" class="subsection">
        <h3>4.4. Validação de Formulários</h3>

        <p>A validação de formulários é essencial para garantir que os dados inseridos pelos usuários sejam
          válidos e completos.</p>

        <div class="code-block">
          <h4>Validação Básica</h4>
          <pre><code>&lt;template&gt;
  &lt;b-form @submit.prevent="handleSubmit" novalidate&gt;
    &lt;b-form-group
      label="Nome"
      label-for="input-name"
      :invalid-feedback="errors.name"
      :state="nameState"
    &gt;
      &lt;b-form-input
        id="input-name"
        v-model="form.name"
        :state="nameState"
        trim
        required
      &gt;&lt;/b-form-input&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-form-group
      label="Email"
      label-for="input-email"
      :invalid-feedback="errors.email"
      :state="emailState"
    &gt;
      &lt;b-form-input
        id="input-email"
        v-model="form.email"
        type="email"
        :state="emailState"
        trim
        required
      &gt;&lt;/b-form-input&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-button type="submit" variant="primary"&gt;Enviar&lt;/b-button&gt;
  &lt;/b-form&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { reactive, computed } from 'vue';

const form = reactive({
  name: '',
  email: ''
});

const errors = reactive({
  name: '',
  email: ''
});

const nameState = computed(() => {
  if (form.name === '') return null;
  return form.name.length >= 3;
});

const emailState = computed(() => {
  if (form.email === '') return null;
  return /\S+@\S+\.\S+/.test(form.email);
});

const validateForm = () => {
  let isValid = true;
  
  // Validar nome
  if (!form.name) {
    errors.name = 'O nome é obrigatório';
    isValid = false;
  } else if (form.name.length < 3) {
    errors.name = 'O nome deve ter pelo menos 3 caracteres';
    isValid = false;
  } else {
    errors.name = '';
  }
  
  // Validar email
  if (!form.email) {
    errors.email = 'O email é obrigatório';
    isValid = false;
  } else if (!/\S+@\S+\.\S+/.test(form.email)) {
    errors.email = 'Email inválido';
    isValid = false;
  } else {
    errors.email = '';
  }
  
  return isValid;
};

const handleSubmit = () => {
  if (validateForm()) {
    // Enviar formulário
    console.log('Formulário válido:', form);
  } else {
    console.log('Formulário inválido');
  }
};
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Validação com VeeValidate</h4>
          <pre><code>&lt;template&gt;
  &lt;Form @submit="onSubmit" v-slot="{ errors }"&gt;
    &lt;b-form-group
      label="Nome"
      label-for="input-name"
    &gt;
      &lt;Field
        name="name"
        v-slot="{ field, errorMessage, meta }"
      &gt;
        &lt;b-form-input
          id="input-name"
          v-bind="field"
          :state="meta.touched ? !errorMessage : null"
          placeholder="Nome completo"
        &gt;&lt;/b-form-input&gt;
        &lt;b-form-invalid-feedback&gt;
          {{ errorMessage }}
        &lt;/b-form-invalid-feedback&gt;
      &lt;/Field&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-form-group
      label="Email"
      label-for="input-email"
    &gt;
      &lt;Field
        name="email"
        v-slot="{ field, errorMessage, meta }"
      &gt;
        &lt;b-form-input
          id="input-email"
          v-bind="field"
          type="email"
          :state="meta.touched ? !errorMessage : null"
          placeholder="<EMAIL>"
        &gt;&lt;/b-form-input&gt;
        &lt;b-form-invalid-feedback&gt;
          {{ errorMessage }}
        &lt;/b-form-invalid-feedback&gt;
      &lt;/Field&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-form-group
      label="Senha"
      label-for="input-password"
    &gt;
      &lt;Field
        name="password"
        v-slot="{ field, errorMessage, meta }"
      &gt;
        &lt;b-form-input
          id="input-password"
          v-bind="field"
          type="password"
          :state="meta.touched ? !errorMessage : null"
          placeholder="Senha"
        &gt;&lt;/b-form-input&gt;
        &lt;b-form-invalid-feedback&gt;
          {{ errorMessage }}
        &lt;/b-form-invalid-feedback&gt;
      &lt;/Field&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-form-group
      label="Confirmar Senha"
      label-for="input-password-confirm"
    &gt;
      &lt;Field
        name="passwordConfirm"
        v-slot="{ field, errorMessage, meta }"
      &gt;
        &lt;b-form-input
          id="input-password-confirm"
          v-bind="field"
          type="password"
          :state="meta.touched ? !errorMessage : null"
          placeholder="Confirmar senha"
        &gt;&lt;/b-form-input&gt;
        &lt;b-form-invalid-feedback&gt;
          {{ errorMessage }}
        &lt;/b-form-invalid-feedback&gt;
      &lt;/Field&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-button type="submit" variant="primary" :disabled="Object.keys(errors).length > 0"&gt;
      Enviar
    &lt;/b-button&gt;
  &lt;/Form&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { Form, Field, defineRule } from 'vee-validate';
import { required, email, min, confirmed } from '@vee-validate/rules';
import { useToastStore } from '@/stores/toastStore';

// Definir regras de validação
defineRule('required', required);
defineRule('email', email);
defineRule('min', min);
defineRule('confirmed', confirmed);

const toastStore = useToastStore();

const onSubmit = (values) => {
  console.log('Formulário enviado:', values);
  toastStore.success('Formulário enviado com sucesso!');
};
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Composable para Validação de Formulário</h4>
          <pre><code>// src/composables/useFormValidation.js
import { reactive, computed } from 'vue';

export function useFormValidation(initialForm = {}) {
  const form = reactive({ ...initialForm });
  const errors = reactive({});
  const touched = reactive({});
  
  const validators = {
    required: (value, message = 'Este campo é obrigatório') => {
      return (value !== null && value !== undefined && value !== '') || message;
    },
    email: (value, message = 'Email inválido') => {
      return !value || /\S+@\S+\.\S+/.test(value) || message;
    },
    minLength: (value, min, message) => {
      return !value || value.length >= min || message || `Mínimo de ${min} caracteres`;
    },
    maxLength: (value, max, message) => {
      return !value || value.length <= max || message || `Máximo de ${max} caracteres`;
    },
    pattern: (value, pattern, message = 'Valor inválido') => {
      return !value || pattern.test(value) || message;
    },
    match: (value, otherValue, message = 'Os valores não correspondem') => {
      return !value || value === otherValue || message;
    }
  };
  
  const validate = (field, rules) => {
    touched[field] = true;
    
    for (const rule of rules) {
      const { type, param, message } = rule;
      
      if (validators[type]) {
        const result = validators[type](form[field], param, message);
        
        if (result !== true) {
          errors[field] = result;
          return false;
        }
      }
    }
    
    errors[field] = '';
    return true;
  };
  
  const validateAll = (validationRules) => {
    let isValid = true;
    
    for (const [field, rules] of Object.entries(validationRules)) {
      const fieldIsValid = validate(field, rules);
      isValid = isValid && fieldIsValid;
    }
    
    return isValid;
  };
  
  const resetForm = () => {
    for (const key in form) {
      form[key] = initialForm[key] || '';
    }
    
    for (const key in errors) {
      errors[key] = '';
    }
    
    for (const key in touched) {
      touched[key] = false;
    }
  };
  
  const touchField = (field) => {
    touched[field] = true;
  };
  
  const fieldState = (field) => {
    if (!touched[field]) return null;
    return !errors[field];
  };
  
  return {
    form,
    errors,
    touched,
    validate,
    validateAll,
    resetForm,
    touchField,
    fieldState
  };
}</code></pre>
        </div>

        <div class="code-block">
          <h4>Uso do Composable de Validação</h4>
          <pre><code>&lt;template&gt;
  &lt;b-form @submit.prevent="handleSubmit" novalidate&gt;
    &lt;b-form-group
      label="Nome"
      label-for="input-name"
      :invalid-feedback="errors.name"
      :state="fieldState('name')"
    &gt;
      &lt;b-form-input
        id="input-name"
        v-model="form.name"
        :state="fieldState('name')"
        @blur="validateField('name')"
        trim
      &gt;&lt;/b-form-input&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-form-group
      label="Email"
      label-for="input-email"
      :invalid-feedback="errors.email"
      :state="fieldState('email')"
    &gt;
      &lt;b-form-input
        id="input-email"
        v-model="form.email"
        type="email"
        :state="fieldState('email')"
        @blur="validateField('email')"
        trim
      &gt;&lt;/b-form-input&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-form-group
      label="Senha"
      label-for="input-password"
      :invalid-feedback="errors.password"
      :state="fieldState('password')"
    &gt;
      &lt;b-form-input
        id="input-password"
        v-model="form.password"
        type="password"
        :state="fieldState('password')"
        @blur="validateField('password')"
      &gt;&lt;/b-form-input&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-form-group
      label="Confirmar Senha"
      label-for="input-password-confirm"
      :invalid-feedback="errors.passwordConfirm"
      :state="fieldState('passwordConfirm')"
    &gt;
      &lt;b-form-input
        id="input-password-confirm"
        v-model="form.passwordConfirm"
        type="password"
        :state="fieldState('passwordConfirm')"
        @blur="validateField('passwordConfirm')"
      &gt;&lt;/b-form-input&gt;
    &lt;/b-form-group&gt;
    
    &lt;b-button type="submit" variant="primary"&gt;Enviar&lt;/b-button&gt;
  &lt;/b-form&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { useFormValidation } from '@/composables/useFormValidation';
import { useToastStore } from '@/stores/toastStore';

const toastStore = useToastStore();

const initialForm = {
  name: '',
  email: '',
  password: '',
  passwordConfirm: ''
};

const { form, errors, validate, validateAll, fieldState } = useFormValidation(initialForm);

const validationRules = {
  name: [
    { type: 'required' },
    { type: 'minLength', param: 3, message: 'O nome deve ter pelo menos 3 caracteres' }
  ],
  email: [
    { type: 'required' },
    { type: 'email' }
  ],
  password: [
    { type: 'required' },
    { type: 'minLength', param: 8, message: 'A senha deve ter pelo menos 8 caracteres' }
  ],
  passwordConfirm: [
    { type: 'required' },
    { type: 'match', param: form.password, message: 'As senhas não correspondem' }
  ]
};

const validateField = (field) => {
  validate(field, validationRules[field]);
};

const handleSubmit = () => {
  if (validateAll(validationRules)) {
    // Enviar formulário
    console.log('Formulário válido:', form);
    toastStore.success('Formulário enviado com sucesso!');
  } else {
    toastStore.error('Por favor, corrija os erros no formulário.');
  }
};
&lt;/script&gt;</code></pre>
        </div>

        <div class="best-practice">
          <h4>Diretrizes para Validação de Formulários</h4>
          <ul>
            <li>Valide os dados tanto no cliente quanto no servidor</li>
            <li>Forneça feedback visual imediato (cores, ícones, mensagens)</li>
            <li>Use mensagens de erro específicas e construtivas</li>
            <li>Valide os campos ao perder o foco (blur) ou ao digitar (input)</li>
            <li>Desabilite o botão de envio se o formulário for inválido</li>
            <li>Agrupe validações relacionadas</li>
            <li>Considere usar bibliotecas como VeeValidate para validações complexas</li>
            <li>Mantenha consistência nas mensagens de erro e no estilo visual</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <section id="componentes-dados" class="manual-section">
    <h2>5. Componentes de Visualização de Dados</h2>
    <p>Componentes para exibir, organizar e interagir com conjuntos de dados.</p>

    <div id="tabelas" class="subsection">
      <h3>5.1. Tabelas</h3>

      <p>Tabelas são utilizadas para exibir dados estruturados em linhas e colunas.</p>

      <div class="code-block">
        <h4>Tabela Básica</h4>
        <pre><code>&lt;template&gt;
  &lt;b-table
    :items="items"
    :fields="fields"
    striped
    hover
    responsive
  &gt;&lt;/b-table&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const fields = [
  { key: 'id', label: 'ID' },
  { key: 'name', label: 'Nome' },
  { key: 'email', label: 'Email' },
  { key: 'status', label: 'Status' }
];

const items = [
  { id: 1, name: 'João Silva', email: '<EMAIL>', status: 'Ativo' },
  { id: 2, name: 'Maria Santos', email: '<EMAIL>', status: 'Inativo' },
  { id: 3, name: 'Pedro Oliveira', email: '<EMAIL>', status: 'Ativo' }
];
&lt;/script&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Tabela com Formatação Personalizada</h4>
        <pre><code>&lt;template&gt;
  &lt;b-table
    :items="users"
    :fields="fields"
    striped
    hover
    responsive
  &gt;
    &lt;!-- Coluna de status personalizada --&gt;
    &lt;template #cell(status)="data"&gt;
      &lt;b-badge :variant="getStatusVariant(data.value)"&gt;
        {{ data.value }}
      &lt;/b-badge&gt;
    &lt;/template&gt;
    
    &lt;!-- Coluna de ações --&gt;
    &lt;template #cell(actions)="data"&gt;
      &lt;div class="d-flex gap-2"&gt;
        &lt;b-button size="sm" variant="outline-primary" @click="viewUser(data.item)"&gt;
          &lt;i class="fas fa-eye"&gt;&lt;/i&gt;
        &lt;/b-button&gt;
        &lt;b-button size="sm" variant="outline-secondary" @click="editUser(data.item)"&gt;
          &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
        &lt;/b-button&gt;
        &lt;b-button size="sm" variant="outline-danger" @click="deleteUser(data.item)"&gt;
          &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
        &lt;/b-button&gt;
      &lt;/div&gt;
    &lt;/template&gt;
  &lt;/b-table&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const fields = [
  { key: 'id', label: 'ID' },
  { key: 'name', label: 'Nome', sortable: true },
  { key: 'email', label: 'Email' },
  { key: 'department', label: 'Departamento', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'actions', label: 'Ações' }
];

const users = [
  { id: 1, name: 'João Silva', email: '<EMAIL>', department: 'TI', status: 'Ativo' },
  { id: 2, name: 'Maria Santos', email: '<EMAIL>', department: 'RH', status: 'Inativo' },
  { id: 3, name: 'Pedro Oliveira', email: '<EMAIL>', department: 'Financeiro', status: 'Pendente' }
];

const getStatusVariant = (status) => {
  switch (status) {
    case 'Ativo':
      return 'success';
    case 'Inativo':
      return 'secondary';
    case 'Pendente':
      return 'warning';
    default:
      return 'primary';
  }
};

const viewUser = (user) => {
  console.log('Visualizar usuário:', user);
};

const editUser = (user) => {
  console.log('Editar usuário:', user);
};

const deleteUser = (user) => {
  console.log('Excluir usuário:', user);
};
&lt;/script&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Tabela com Seleção e Paginação</h4>
        <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;div class="d-flex justify-content-between align-items-center mb-3"&gt;
      &lt;div&gt;
        &lt;b-button v-if="selected.length > 0" variant="danger" size="sm" @click="deleteSelected"&gt;
          &lt;i class="fas fa-trash me-1"&gt;&lt;/i&gt; Excluir selecionados ({{ selected.length }})
        &lt;/b-button&gt;
      &lt;/div&gt;
      
      &lt;div class="d-flex align-items-center"&gt;
        &lt;label class="me-2 mb-0"&gt;Itens por página:&lt;/label&gt;
        &lt;b-form-select
          v-model="perPage"
          :options="perPageOptions"
          size="sm"
          style="width: 80px;"
        &gt;&lt;/b-form-select&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;b-table
      :items="items"
      :fields="fields"
      :per-page="perPage"
      :current-page="currentPage"
      striped
      hover
      responsive
      selectable
      select-mode="multi"
      @row-selected="onRowSelected"
    &gt;
      &lt;template #cell(selected)="{ rowSelected }"&gt;
        &lt;template v-if="rowSelected"&gt;
          &lt;i class="fas fa-check-square text-primary"&gt;&lt;/i&gt;
        &lt;/template&gt;
        &lt;template v-else&gt;
          &lt;i class="far fa-square"&gt;&lt;/i&gt;
        &lt;/template&gt;
      &lt;/template&gt;
      
      &lt;template #cell(status)="data"&gt;
        &lt;b-badge :variant="getStatusVariant(data.value)"&gt;
          {{ data.value }}
        &lt;/b-badge&gt;
      &lt;/template&gt;
      
      &lt;template #cell(actions)="data"&gt;
        &lt;div class="d-flex gap-2"&gt;
          &lt;b-button size="sm" variant="outline-primary" @click="viewItem(data.item)"&gt;
            &lt;i class="fas fa-eye"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
          &lt;b-button size="sm" variant="outline-secondary" @click="editItem(data.item)"&gt;
            &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
          &lt;b-button size="sm" variant="outline-danger" @click="deleteItem(data.item)"&gt;
            &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
        &lt;/div&gt;
      &lt;/template&gt;
    &lt;/b-table&gt;
    
    &lt;div class="d-flex justify-content-between align-items-center"&gt;
      &lt;b-pagination
        v-model="currentPage"
        :total-rows="totalRows"
        :per-page="perPage"
        align="center"
        size="md"
        class="my-0"
      &gt;&lt;/b-pagination&gt;
      
      &lt;small class="text-muted"&gt;
        Mostrando {{ (currentPage - 1) * perPage + 1 }} a 
        {{ Math.min(currentPage * perPage, totalRows) }} de {{ totalRows }} registros
      &lt;/small&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';

const fields = [
  { key: 'selected', label: '' },
  { key: 'id', label: 'ID', sortable: true },
  { key: 'name', label: 'Nome', sortable: true },
  { key: 'email', label: 'Email' },
  { key: 'department', label: 'Departamento', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'actions', label: 'Ações' }
];

const items = ref([
  { id: 1, name: 'João Silva', email: '<EMAIL>', department: 'TI', status: 'Ativo' },
  { id: 2, name: 'Maria Santos', email: '<EMAIL>', department: 'RH', status: 'Inativo' },
  { id: 3, name: 'Pedro Oliveira', email: '<EMAIL>', department: 'Financeiro', status: 'Pendente' },
  { id: 4, name: 'Ana Costa', email: '<EMAIL>', department: 'Marketing', status: 'Ativo' },
  { id: 5, name: 'Carlos Souza', email: '<EMAIL>', department: 'TI', status: 'Ativo' },
  { id: 6, name: 'Fernanda Lima', email: '<EMAIL>', department: 'RH', status: 'Inativo' },
  { id: 7, name: 'Ricardo Alves', email: '<EMAIL>', department: 'Financeiro', status: 'Pendente' },
  { id: 8, name: 'Juliana Pereira', email: '<EMAIL>', department: 'Marketing', status: 'Ativo' },
  { id: 9, name: 'Roberto Santos', email: '<EMAIL>', department: 'TI', status: 'Inativo' },
  { id: 10, name: 'Camila Ferreira', email: '<EMAIL>', department: 'RH', status: 'Ativo' }
]);

const selected = ref([]);
const currentPage = ref(1);
const perPage = ref(5);
const perPageOptions = [5, 10, 15, 20];

const totalRows = computed(() => items.value.length);

const getStatusVariant = (status) => {
  switch (status) {
    case 'Ativo':
      return 'success';
    case 'Inativo':
      return 'secondary';
    case 'Pendente':
      return 'warning';
    default:
      return 'primary';
  }
};

const onRowSelected = (items) => {
  selected.value = items;
};

const viewItem = (item) => {
  console.log('Visualizar item:', item);
};

const editItem = (item) => {
  console.log('Editar item:', item);
};

const deleteItem = (item) => {
  console.log('Excluir item:', item);
};

const deleteSelected = () => {
  console.log('Excluir itens selecionados:', selected.value);
  // Implementar lógica para excluir itens selecionados
};
&lt;/script&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Tabela Reutilizável</h4>
        <pre><code>// src/components/common/DataTable.vue
&lt;template&gt;
  &lt;div class="data-table"&gt;
    &lt;div class="data-table-header d-flex justify-content-between align-items-center mb-3"&gt;
      &lt;div class="d-flex align-items-center"&gt;
        &lt;!-- Slot para ações em massa --&gt;
        &lt;slot name="bulk-actions" :selected="selected"&gt;&lt;/slot&gt;
      &lt;/div&gt;
      
      &lt;div class="d-flex align-items-center"&gt;
        &lt;!-- Slot para filtros --&gt;
        &lt;slot name="filters"&gt;&lt;/slot&gt;
        
        &lt;!-- Pesquisa --&gt;
        &lt;div v-if="searchable" class="position-relative ms-2"&gt;
          &lt;b-form-input
            v-model="searchQuery"
            placeholder="Pesquisar..."
            size="sm"
            class="pe-4"
            style="width: 200px;"
          &gt;&lt;/b-form-input&gt;
          &lt;div class="position-absolute top-50 end-0 translate-middle-y pe-3"&gt;
            &lt;i class="fas fa-search text-muted"&gt;&lt;/i&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        
        &lt;!-- Seletor de itens por página --&gt;
        &lt;div v-if="paginated" class="d-flex align-items-center ms-3"&gt;
          &lt;label class="me-2 mb-0 small"&gt;Itens:&lt;/label&gt;
          &lt;b-form-select
            v-model="localPerPage"
            :options="perPageOptions"
            size="sm"
            style="width: 70px;"
          &gt;&lt;/b-form-select&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;b-table
      :items="filteredItems"
      :fields="fields"
      :busy="busy"
      :per-page="paginated ? localPerPage : 0"
      :current-page="currentPage"
      :sort-by.sync="sortBy"
      :sort-desc.sync="sortDesc"
      :selectable="selectable"
      :select-mode="selectMode"
      :no-sort-reset="true"
      striped
      hover
      responsive
      show-empty
      @row-selected="onRowSelected"
    &gt;
      &lt;!-- Slot para estado de carregamento --&gt;
      &lt;template #table-busy&gt;
        &lt;div class="text-center my-3"&gt;
          &lt;b-spinner class="align-middle"&gt;&lt;/b-spinner&gt;
          &lt;strong class="ms-2"&gt;Carregando...&lt;/strong&gt;
        &lt;/div&gt;
      &lt;/template&gt;
      
      &lt;!-- Slot para estado vazio --&gt;
      &lt;template #empty&gt;
        &lt;div class="text-center py-4"&gt;
          &lt;i class="fas fa-search fa-2x text-muted mb-3"&gt;&lt;/i&gt;
          &lt;p class="mb-0"&gt;{{ emptyText }}&lt;/p&gt;
        &lt;/div&gt;
      &lt;/template&gt;
      
      &lt;!-- Slot para coluna de seleção --&gt;
      &lt;template #cell(selected)="{ rowSelected }"&gt;
        &lt;template v-if="rowSelected"&gt;
          &lt;i class="fas fa-check-square text-primary"&gt;&lt;/i&gt;
        &lt;/template&gt;
        &lt;template v-else&gt;
          &lt;i class="far fa-square"&gt;&lt;/i&gt;
        &lt;/template&gt;
      &lt;/template&gt;
      
      &lt;!-- Slots dinâmicos para células personalizadas --&gt;
      &lt;template v-for="field in fields" :key="field.key" #[`cell(${field.key})`]="data"&gt;
        &lt;slot :name="`cell(${field.key})`" v-bind="data"&gt;
          {{ data.value }}
        &lt;/slot&gt;
      &lt;/template&gt;
    &lt;/b-table&gt;
    
    &lt;!-- Paginação --&gt;
    &lt;div v-if="paginated && totalRows > 0" class="d-flex justify-content-between align-items-center mt-3"&gt;
      &lt;b-pagination
        v-model="currentPage"
        :total-rows="totalRows"
        :per-page="localPerPage"
        align="center"
        size="md"
        class="my-0"
      &gt;&lt;/b-pagination&gt;
      
      &lt;small class="text-muted"&gt;
        Mostrando {{ (currentPage - 1) * localPerPage + 1 }} a 
        {{ Math.min(currentPage * localPerPage, totalRows) }} de {{ totalRows }} registros
      &lt;/small&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, watch } from 'vue';

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  fields: {
    type: Array,
    required: true
  },
  busy: {
    type: Boolean,
    default: false
  },
  paginated: {
    type: Boolean,
    default: true
  },
  perPage: {
    type: Number,
    default: 10
  },
  searchable: {
    type: Boolean,
    default: true
  },
  searchKeys: {
    type: Array,
    default: () => []
  },
  selectable: {
    type: Boolean,
    default: false
  },
  selectMode: {
    type: String,
    default: 'multi',
    validator: (value) => ['single', 'multi', 'range'].includes(value)
  },
  emptyText: {
    type: String,
    default: 'Nenhum registro encontrado'
  }
});

const emit = defineEmits(['row-selected', 'update:sort-by', 'update:sort-desc', 'update:per-page']);

const selected = ref([]);
const currentPage = ref(1);
const localPerPage = ref(props.perPage);
const perPageOptions = [5, 10, 25, 50, 100];
const searchQuery = ref('');
const sortBy = ref('');
const sortDesc = ref(false);

// Observa mudanças no perPage
watch(localPerPage, (newValue) => {
  emit('update:per-page', newValue);
  currentPage.value = 1; // Volta para a primeira página ao mudar itens por página
});

// Filtra itens com base na pesquisa
const filteredItems = computed(() => {
  if (!searchQuery.value || !props.searchable) return props.items;
  
  const query = searchQuery.value.toLowerCase();
  const searchFields = props.searchKeys.length > 0 
    ? props.searchKeys 
    : props.fields.map(field => field.key).filter(key => key !== 'selected' && key !== 'actions');
  
  return props.items.filter(item => {
    return searchFields.some(key => {
      const value = item[key];
      if (value === null || value === undefined) return false;
      return String(value).toLowerCase().includes(query);
    });
  });
});

const totalRows = computed(() => filteredItems.value.length);

const onRowSelected = (items) => {
  selected.value = items;
  emit('row-selected', items);
};

// Resetar seleção quando os itens mudam
watch(() => props.items, () => {
  selected.value = [];
});

// Resetar página atual quando a pesquisa muda
watch(searchQuery, () => {
  currentPage.value = 1;
});
&lt;/script&gt;

&lt;style scoped&gt;
.data-table {
  width: 100%;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Uso do Componente DataTable</h4>
        <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;h2 class="mb-4"&gt;Usuários&lt;/h2&gt;
    
    &lt;data-table
      :items="users"
      :fields="fields"
      :busy="isLoading"
      :searchable="true"
      :search-keys="['name', 'email', 'department']"
      :selectable="true"
      @row-selected="onRowSelected"
    &gt;
      &lt;!-- Ações em massa --&gt;
      &lt;template #bulk-actions="{ selected }"&gt;
        &lt;b-button 
          v-if="selected.length > 0" 
          variant="danger" 
          size="sm" 
          @click="deleteSelected(selected)"
        &gt;
          &lt;i class="fas fa-trash me-1"&gt;&lt;/i&gt; Excluir ({{ selected.length }})
        &lt;/b-button&gt;
        
        &lt;b-button 
          v-else 
          variant="primary" 
          size="sm" 
          @click="showAddModal"
        &gt;
          &lt;i class="fas fa-plus me-1"&gt;&lt;/i&gt; Novo Usuário
        &lt;/b-button&gt;
      &lt;/template&gt;
      
      &lt;!-- Filtros --&gt;
      &lt;template #filters&gt;
        &lt;b-form-select
          v-model="departmentFilter"
          :options="departmentOptions"
          size="sm"
          class="me-2"
          style="width: 150px;"
        &gt;&lt;/b-form-select&gt;
        
        &lt;b-form-select
          v-model="statusFilter"
          :options="statusOptions"
          size="sm"
          style="width: 130px;"
        &gt;&lt;/b-form-select&gt;
      &lt;/template&gt;
      
      &lt;!-- Coluna de status personalizada --&gt;
      &lt;template #cell(status)="data"&gt;
        &lt;b-badge :variant="getStatusVariant(data.value)"&gt;
          {{ data.value }}
        &lt;/b-badge&gt;
      &lt;/template&gt;
      
      &lt;!-- Coluna de ações --&gt;
      &lt;template #cell(actions)="data"&gt;
        &lt;div class="d-flex gap-2"&gt;
          &lt;b-button size="sm" variant="outline-primary" @click="viewUser(data.item)"&gt;
            &lt;i class="fas fa-eye"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
          &lt;b-button size="sm" variant="outline-secondary" @click="editUser(data.item)"&gt;
            &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
          &lt;b-button size="sm" variant="outline-danger" @click="deleteUser(data.item)"&gt;
            &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
        &lt;/div&gt;
      &lt;/template&gt;
    &lt;/data-table&gt;
    
    &lt;!-- Modal para adicionar/editar usuário --&gt;
    &lt;b-modal
      v-model="isModalVisible"
      :title="isEditing ? 'Editar Usuário' : 'Novo Usuário'"
      hide-footer
    &gt;
      &lt;user-form
        :user="currentUser"
        @submit="saveUser"
        @cancel="isModalVisible = false"
      /&gt;
    &lt;/b-modal&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, watch } from 'vue';
import DataTable from '@/components/common/DataTable.vue';
import UserForm from '@/components/users/UserForm.vue';
import { useToastStore } from '@/stores/toastStore';

const toastStore = useToastStore();

const fields = [
  { key: 'selected', label: '' },
  { key: 'id', label: 'ID', sortable: true },
  { key: 'name', label: 'Nome', sortable: true },
  { key: 'email', label: 'Email' },
  { key: 'department', label: 'Departamento', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'actions', label: 'Ações' }
];

const users = ref([]);
const isLoading = ref(false);
const isModalVisible = ref(false);
const isEditing = ref(false);
const currentUser = ref({});
const departmentFilter = ref(null);
const statusFilter = ref(null);

const departmentOptions = [
  { value: null, text: 'Todos os departamentos' },
  { value: 'TI', text: 'TI' },
  { value: 'RH', text: 'RH' },
  { value: 'Financeiro', text: 'Financeiro' },
  { value: 'Marketing', text: 'Marketing' }
];

const statusOptions = [
  { value: null, text: 'Todos os status' },
  { value: 'Ativo', text: 'Ativo' },
  { value: 'Inativo', text: 'Inativo' },
  { value: 'Pendente', text: 'Pendente' }
];

// Carregar dados
const loadUsers = async () => {
  isLoading.value = true;
  
  try {
    // Simulação de chamada de API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    users.value = [
      { id: 1, name: 'João Silva', email: '<EMAIL>', department: 'TI', status: 'Ativo' },
      { id: 2, name: 'Maria Santos', email: '<EMAIL>', department: 'RH', status: 'Inativo' },
      { id: 3, name: 'Pedro Oliveira', email: '<EMAIL>', department: 'Financeiro', status: 'Pendente' },
      { id: 4, name: 'Ana Costa', email: '<EMAIL>', department: 'Marketing', status: 'Ativo' },
      { id: 5, name: 'Carlos Souza', email: '<EMAIL>', department: 'TI', status: 'Ativo' },
      { id: 6, name: 'Fernanda Lima', email: '<EMAIL>', department: 'RH', status: 'Inativo' },
      { id: 7, name: 'Ricardo Alves', email: '<EMAIL>', department: 'Financeiro', status: 'Pendente' },
      { id: 8, name: 'Juliana Pereira', email: '<EMAIL>', department: 'Marketing', status: 'Ativo' },
      { id: 9, name: 'Roberto Santos', email: '<EMAIL>', department: 'TI', status: 'Inativo' },
      { id: 10, name: 'Camila Ferreira', email: '<EMAIL>', department: 'RH', status: 'Ativo' }
    ];
  } catch (error) {
    console.error('Erro ao carregar usuários:', error);
    toastStore.error('Erro ao carregar usuários.');
  } finally {
    isLoading.value = false;
  }
};

// Filtrar usuários com base nos filtros selecionados
const filteredUsers = computed(() => {
  let result = [...users.value];
  
  if (departmentFilter.value) {
    result = result.filter(user => user.department === departmentFilter.value);
  }
  
  if (statusFilter.value) {
    result = result.filter(user => user.status === statusFilter.value);
  }
  
  return result;
});

// Observar mudanças nos filtros
watch([departmentFilter, statusFilter], () => {
  // Atualizar dados filtrados
});

const getStatusVariant = (status) => {
  switch (status) {
    case 'Ativo':
      return 'success';
    case 'Inativo':
      return 'secondary';
    case 'Pendente':
      return 'warning';
    default:
      return 'primary';
  }
};

const onRowSelected = (items) => {
  console.log('Itens selecionados:', items);
};

const showAddModal = () => {
  isEditing.value = false;
  currentUser.value = {};
  isModalVisible.value = true;
};

const viewUser = (user) => {
  console.log('Visualizar usuário:', user);
  // Implementar visualização detalhada
};

const editUser = (user) => {
  isEditing.value = true;
  currentUser.value = { ...user };
  isModalVisible.value = true;
};

const deleteUser = (user) => {
  // Implementar confirmação de exclusão
  if (confirm(`Deseja realmente excluir o usuário ${user.name}?`)) {
    console.log('Excluir usuário:', user);
    toastStore.success(`Usuário ${user.name} excluído com sucesso.`);
  }
};

const deleteSelected = (selected) => {
  // Implementar confirmação de exclusão em massa
  if (confirm(`Deseja realmente excluir ${selected.length} usuários selecionados?`)) {
    console.log('Excluir usuários selecionados:', selected);
    toastStore.success(`${selected.length} usuários excluídos com sucesso.`);
  }
};

const saveUser = (user) => {
  console.log('Salvar usuário:', user);
  isModalVisible.value = false;
  
  if (isEditing.value) {
    toastStore.success(`Usuário ${user.name} atualizado com sucesso.`);
  } else {
    toastStore.success(`Usuário ${user.name} adicionado com sucesso.`);
  }
};

// Carregar dados ao montar o componente
loadUsers();
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Tabelas</h4>
        <ul>
          <li>Use cabeçalhos claros e descritivos para as colunas</li>
          <li>Alinhe dados numéricos à direita e texto à esquerda</li>
          <li>Forneça paginação para tabelas com muitos registros</li>
          <li>Implemente ordenação para colunas relevantes</li>
          <li>Adicione filtros e pesquisa para facilitar a localização de dados</li>
          <li>Use cores e ícones para destacar informações importantes</li>
          <li>Forneça feedback visual para estados de carregamento e vazios</li>
          <li>Considere usar tabelas responsivas para dispositivos móveis</li>
          <li>Implemente ações contextuais (visualizar, editar, excluir)</li>
          <li>Use seleção múltipla para operações em lote quando apropriado</li>
        </ul>
      </div>

      <div id="graficos" class="subsection">
        <h3>5.2. Gráficos e Visualizações</h3>

        <p>Gráficos e visualizações são utilizados para representar dados de forma visual e
          facilitar a compreensão de informações complexas.</p>

        <div class="code-block">
          <h4>Gráfico de Barras com Chart.js</h4>
          <pre><code>&lt;template&gt;
  &lt;div class="chart-container"&gt;
    &lt;canvas ref="chartCanvas"&gt;&lt;/canvas&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, watch } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  chartOptions: {
    type: Object,
    default: () => ({})
  }
});

const chartCanvas = ref(null);
let chart = null;

const createChart = () => {
  if (chart) {
    chart.destroy();
  }
  
  const ctx = chartCanvas.value.getContext('2d');
  chart = new Chart(ctx, {
    type: 'bar',
    data: props.chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false,
        },
      },
      scales: {
        y: {
          beginAtZero: true
        }
      },
      ...props.chartOptions
    }
  });
};

onMounted(() => {
  createChart();
});

watch(() => props.chartData, () => {
  createChart();
}, { deep: true });
&lt;/script&gt;

&lt;style scoped&gt;
.chart-container {
  position: relative;
  height: 350px;
  width: 100%;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Exemplo de Uso do Gráfico</h4>
          <pre><code>&lt;template&gt;
  &lt;div class="dashboard-card"&gt;
    &lt;div class="card-header d-flex justify-content-between align-items-center"&gt;
      &lt;h5 class="mb-0"&gt;Vendas por Mês&lt;/h5&gt;
      &lt;div class="chart-controls"&gt;
        &lt;b-form-select v-model="selectedYear" :options="yearOptions" size="sm"&gt;&lt;/b-form-select&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="card-body"&gt;
      &lt;bar-chart :chart-data="chartData" /&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';
import BarChart from '@/components/charts/BarChart.vue';

const selectedYear = ref(new Date().getFullYear());
const yearOptions = [
  { value: 2021, text: '2021' },
  { value: 2022, text: '2022' },
  { value: 2023, text: '2023' }
];

// Dados simulados de vendas
const salesData = {
  2021: [12500, 15000, 18000, 16500, 21000, 22500, 24000, 21500, 19000, 23000, 25500, 28000],
  2022: [15000, 17500, 20000, 19500, 23000, 25500, 27000, 24500, 22000, 26000, 28500, 31000],
  2023: [18000, 21000, 24000, 22500, 26000, 28500, 30000, 27500, 25000, 29000, 31500, 34000]
};

const chartData = computed(() => {
  return {
    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
    datasets: [
      {
        label: `Vendas ${selectedYear.value}`,
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
        data: salesData[selectedYear.value]
      }
    ]
  };
});
&lt;/script&gt;

&lt;style scoped&gt;
.dashboard-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.card-body {
  padding: 20px;
}

.chart-controls {
  width: 100px;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Gráfico de Linha com Chart.js</h4>
          <pre><code>&lt;template&gt;
  &lt;div class="chart-container"&gt;
    &lt;canvas ref="chartCanvas"&gt;&lt;/canvas&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, watch } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  chartOptions: {
    type: Object,
    default: () => ({})
  }
});

const chartCanvas = ref(null);
let chart = null;

const createChart = () => {
  if (chart) {
    chart.destroy();
  }
  
  const ctx = chartCanvas.value.getContext('2d');
  chart = new Chart(ctx, {
    type: 'line',
    data: props.chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false,
        },
      },
      scales: {
        y: {
          beginAtZero: true
        }
      },
      elements: {
        line: {
          tension: 0.4 // Suaviza a linha
        },
        point: {
          radius: 3,
          hitRadius: 10,
          hoverRadius: 5
        }
      },
      ...props.chartOptions
    }
  });
};

onMounted(() => {
  createChart();
});

watch(() => props.chartData, () => {
  createChart();
}, { deep: true });
&lt;/script&gt;

&lt;style scoped&gt;
.chart-container {
  position: relative;
  height: 350px;
  width: 100%;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Gráfico de Pizza com Chart.js</h4>
          <pre><code>&lt;template&gt;
  &lt;div class="chart-container"&gt;
    &lt;canvas ref="chartCanvas"&gt;&lt;/canvas&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, watch } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  chartOptions: {
    type: Object,
    default: () => ({})
  }
});

const chartCanvas = ref(null);
let chart = null;

const createChart = () => {
  if (chart) {
    chart.destroy();
  }
  
  const ctx = chartCanvas.value.getContext('2d');
  chart = new Chart(ctx, {
    type: 'pie',
    data: props.chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'right',
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw || 0;
              const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
              const percentage = Math.round((value / total) * 100);
              return `${label}: ${value} (${percentage}%)`;
            }
          }
        }
      },
      ...props.chartOptions
    }
  });
};

onMounted(() => {
  createChart();
});

watch(() => props.chartData, () => {
  createChart();
}, { deep: true });
&lt;/script&gt;

&lt;style scoped&gt;
.chart-container {
  position: relative;
  height: 350px;
  width: 100%;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Dashboard com Múltiplos Gráficos</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;h2 class="mb-4"&gt;Dashboard Analítico&lt;/h2&gt;
    
    &lt;!-- Filtros --&gt;
    &lt;div class="dashboard-filters mb-4"&gt;
      &lt;div class="row align-items-end"&gt;
        &lt;div class="col-md-3"&gt;
          &lt;label class="form-label"&gt;Período&lt;/label&gt;
          &lt;b-form-select v-model="selectedPeriod" :options="periodOptions"&gt;&lt;/b-form-select&gt;
        &lt;/div&gt;
        &lt;div class="col-md-3"&gt;
          &lt;label class="form-label"&gt;Departamento&lt;/label&gt;
          &lt;b-form-select v-model="selectedDepartment" :options="departmentOptions"&gt;&lt;/b-form-select&gt;
        &lt;/div&gt;
        &lt;div class="col-md-4"&gt;
          &lt;label class="form-label"&gt;Intervalo de Datas&lt;/label&gt;
          &lt;b-form-datepicker v-model="dateRange" range&gt;&lt;/b-form-datepicker&gt;
        &lt;/div&gt;
        &lt;div class="col-md-2"&gt;
          &lt;b-button variant="primary" class="w-100" @click="applyFilters"&gt;
            Aplicar Filtros
          &lt;/b-button&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;!-- Cards de resumo --&gt;
    &lt;div class="row mb-4"&gt;
      &lt;div class="col-md-3"&gt;
        &lt;div class="summary-card bg-primary text-white"&gt;
          &lt;div class="summary-icon"&gt;
            &lt;i class="fas fa-chart-line fa-2x"&gt;&lt;/i&gt;
          &lt;/div&gt;
          &lt;div class="summary-content"&gt;
            &lt;h3 class="summary-value"&gt;R$ {{ formatNumber(totalRevenue) }}&lt;/h3&gt;
            &lt;p class="summary-label mb-0"&gt;Receita Total&lt;/p&gt;
            &lt;div class="summary-trend"&gt;
              &lt;i class="fas fa-arrow-up me-1"&gt;&lt;/i&gt; 12.5% vs período anterior
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div class="col-md-3"&gt;
        &lt;div class="summary-card bg-success text-white"&gt;
          &lt;div class="summary-icon"&gt;
            &lt;i class="fas fa-users fa-2x"&gt;&lt;/i&gt;
          &lt;/div&gt;
          &lt;div class="summary-content"&gt;
            &lt;h3 class="summary-value"&gt;{{ formatNumber(totalCustomers) }}&lt;/h3&gt;
            &lt;p class="summary-label mb-0"&gt;Clientes&lt;/p&gt;
            &lt;div class="summary-trend"&gt;
              &lt;i class="fas fa-arrow-up me-1"&gt;&lt;/i&gt; 8.3% vs período anterior
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div class="col-md-3"&gt;
        &lt;div class="summary-card bg-info text-white"&gt;
          &lt;div class="summary-icon"&gt;
            &lt;i class="fas fa-shopping-cart fa-2x"&gt;&lt;/i&gt;
          &lt;/div&gt;
          &lt;div class="summary-content"&gt;
            &lt;h3 class="summary-value"&gt;{{ formatNumber(totalOrders) }}&lt;/h3&gt;
            &lt;p class="summary-label mb-0"&gt;Pedidos&lt;/p&gt;
            &lt;div class="summary-trend"&gt;
              &lt;i class="fas fa-arrow-up me-1"&gt;&lt;/i&gt; 5.7% vs período anterior
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div class="col-md-3"&gt;
        &lt;div class="summary-card bg-warning text-white"&gt;
          &lt;div class="summary-icon"&gt;
            &lt;i class="fas fa-percentage fa-2x"&gt;&lt;/i&gt;
          &lt;/div&gt;
          &lt;div class="summary-content"&gt;
            &lt;h3 class="summary-value"&gt;{{ conversionRate }}%&lt;/h3&gt;
            &lt;p class="summary-label mb-0"&gt;Taxa de Conversão&lt;/p&gt;
            &lt;div class="summary-trend"&gt;
              &lt;i class="fas fa-arrow-down me-1"&gt;&lt;/i&gt; 2.1% vs período anterior
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;!-- Gráficos principais --&gt;
    &lt;div class="row mb-4"&gt;
      &lt;div class="col-md-8"&gt;
        &lt;div class="dashboard-card"&gt;
          &lt;div class="card-header d-flex justify-content-between align-items-center"&gt;
            &lt;h5 class="mb-0"&gt;Receita por Período&lt;/h5&gt;
            &lt;div class="chart-controls"&gt;
              &lt;b-button-group size="sm"&gt;
                &lt;b-button 
                  v-for="option in chartViewOptions" 
                  :key="option.value"
                  :variant="chartView === option.value ? 'primary' : 'outline-primary'"
                  @click="chartView = option.value"
                &gt;
                  {{ option.text }}
                &lt;/b-button&gt;
              &lt;/b-button-group&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          &lt;div class="card-body"&gt;
            &lt;line-chart :chart-data="revenueChartData" /&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div class="col-md-4"&gt;
        &lt;div class="dashboard-card"&gt;
          &lt;div class="card-header"&gt;
            &lt;h5 class="mb-0"&gt;Distribuição de Vendas&lt;/h5&gt;
          &lt;/div&gt;
          &lt;div class="card-body"&gt;
            &lt;pie-chart :chart-data="salesDistributionData" /&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;!-- Gráficos secundários --&gt;
    &lt;div class="row"&gt;
      &lt;div class="col-md-6"&gt;
        &lt;div class="dashboard-card"&gt;
          &lt;div class="card-header"&gt;
            &lt;h5 class="mb-0"&gt;Desempenho por Categoria&lt;/h5&gt;
          &lt;/div&gt;
          &lt;div class="card-body"&gt;
            &lt;bar-chart :chart-data="categoryPerformanceData" /&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div class="col-md-6"&gt;
        &lt;div class="dashboard-card"&gt;
          &lt;div class="card-header d-flex justify-content-between align-items-center"&gt;
            &lt;h5 class="mb-0"&gt;Tendência de Clientes&lt;/h5&gt;
            &lt;div class="chart-controls"&gt;
              &lt;b-form-checkbox v-model="showPrediction" switch&gt;
                Mostrar Previsão
              &lt;/b-form-checkbox&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          &lt;div class="card-body"&gt;
            &lt;line-chart :chart-data="customerTrendData" /&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';
import BarChart from '@/components/charts/BarChart.vue';
import LineChart from '@/components/charts/LineChart.vue';
import PieChart from '@/components/charts/PieChart.vue';

// Estados
const selectedPeriod = ref('month');
const selectedDepartment = ref(null);
const dateRange = ref('');
const chartView = ref('daily');
const showPrediction = ref(true);

// Opções
const periodOptions = [
  { value: 'week', text: 'Esta Semana' },
  { value: 'month', text: 'Este Mês' },
  { value: 'quarter', text: 'Este Trimestre' },
  { value: 'year', text: 'Este Ano' }
];

const departmentOptions = [
  { value: null, text: 'Todos os Departamentos' },
  { value: 'sales', text: 'Vendas' },
  { value: 'marketing', text: 'Marketing' },
  { value: 'support', text: 'Suporte' }
];

const chartViewOptions = [
  { value: 'daily', text: 'Diário' },
  { value: 'weekly', text: 'Semanal' },
  { value: 'monthly', text: 'Mensal' }
];

// Dados simulados
const totalRevenue = ref(1250000);
const totalCustomers = ref(8750);
const totalOrders = ref(12500);
const conversionRate = ref(3.8);

// Dados dos gráficos
const revenueChartData = computed(() => {
  // Simulação de dados baseados no período e visualização selecionados
  let labels = [];
  let data = [];
  
  if (chartView.value === 'daily') {
    labels = ['01/05', '02/05', '03/05', '04/05', '05/05', '06/05', '07/05', '08/05', '09/05', '10/05', '11/05', '12/05', '13/05', '14/05'];
    data = [42000, 38000, 45000, 50000, 47000, 30000, 28000, 51000, 55000, 48000, 43000, 39000, 54000, 52000];
  } else if (chartView.value === 'weekly') {
    labels = ['Semana 1', 'Semana 2', 'Semana 3', 'Semana 4', 'Semana 5'];
    data = [250000, 310000, 290000, 320000, 280000];
  } else {
    labels = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
    data = [980000, 1050000, 1150000, 1080000, 1250000, 1300000, 1220000, 1180000, 1270000, 1320000, 1400000, 1500000];
  }
  
  return {
    labels: labels,
    datasets: [
      {
        label: 'Receita',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 2,
        data: data,
        fill: true
      }
    ]
  };
});

const salesDistributionData = computed(() => {
  return {
    labels: ['Produtos Físicos', 'Serviços', 'Assinaturas', 'Downloads'],
    datasets: [
      {
        data: [45, 25, 20, 10],
        backgroundColor: [
          'rgba(75, 192, 192, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(153, 102, 255, 0.8)',
          'rgba(255, 159, 64, 0.8)'
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)'
        ],
        borderWidth: 1
      }
    ]
  };
});

const categoryPerformanceData = computed(() => {
  return {
    labels: ['Eletrônicos', 'Móveis', 'Roupas', 'Alimentos', 'Livros'],
    datasets: [
      {
        label: 'Vendas',
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
        data: [350000, 210000, 180000, 120000, 90000]
      },
      {
        label: 'Lucro',
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
        data: [120000, 70000, 50000, 30000, 40000]
      }
    ]
  };
});

const customerTrendData = computed(() => {
  const realData = [5200, 5500, 6000, 6200, 6800, 7100, 7500, 8000, 8200, 8500, 8750];
  const predictedData = showPrediction.value ? [8750, 9000, 9300, 9600, 10000, 10400] : [];
  
  return {
    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez', 'Jan', 'Fev', 'Mar', 'Abr'],
    datasets: [
      {
        label: 'Clientes Reais',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 2,
        data: realData,
        fill: true
      },
      {
        label: 'Previsão',
        backgroundColor: 'rgba(255, 206, 86, 0.2)',
        borderColor: 'rgba(255, 206, 86, 1)',
        borderWidth: 2,
        borderDash: [5, 5],
        data: [...Array(realData.length - 1).fill(null), ...predictedData],
        fill: true
      }
    ]
  };
});

// Métodos
const formatNumber = (value) => {
  return new Intl.NumberFormat('pt-BR').format(value);
};

const applyFilters = () => {
  console.log('Aplicando filtros:', {
    period: selectedPeriod.value,
    department: selectedDepartment.value,
    dateRange: dateRange.value
  });
  
  // Aqui você implementaria a lógica para atualizar os dados com base nos filtros
};
&lt;/script&gt;

&lt;style scoped&gt;
.dashboard-filters {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.summary-icon {
  margin-right: 20px;
  width: 50px;
  text-align: center;
}

.summary-content {
  flex: 1;
}

.summary-value {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.summary-trend {
  font-size: 0.8rem;
  margin-top: 5px;
  opacity: 0.9;
}

.dashboard-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  height: 100%;
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.card-body {
  padding: 20px;
}

.chart-controls {
  min-width: 120px;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="best-practice">
          <h4>Diretrizes para Gráficos e Visualizações</h4>
          <ul>
            <li>Escolha o tipo de gráfico apropriado para os dados (barras para comparações,
              linhas para tendências, pizza para proporções)</li>
            <li>Use cores consistentes e com bom contraste</li>
            <li>Forneça legendas claras e descritivas</li>
            <li>Inclua títulos informativos para cada gráfico</li>
            <li>Evite sobrecarregar gráficos com muitos dados ou séries</li>
            <li>Permita interatividade (filtros, zoom, tooltips)</li>
            <li>Considere a acessibilidade (contraste de cores, descrições alternativas)</li>
            <li>Mantenha a responsividade para diferentes tamanhos de tela</li>
            <li>Forneça contexto e comparações quando relevante</li>
            <li>Use animações com moderação para não distrair</li>
          </ul>
        </div>

        <div id="filtros" class="subsection">
          <h3>5.3. Filtros e Ordenação</h3>

          <p>Filtros e ordenação permitem que os usuários encontrem e organizem dados de acordo
            com suas necessidades.</p>

          <div class="code-block">
            <h4>Componente de Filtro Avançado</h4>
            <pre><code>&lt;template&gt;
  &lt;div class="filter-panel"&gt;
    &lt;b-collapse :visible="isExpanded" class="filter-body"&gt;
      &lt;div class="row"&gt;
        &lt;div class="col-md-3 mb-3"&gt;
          &lt;label class="form-label"&gt;Nome&lt;/label&gt;
          &lt;b-form-input 
            v-model="filters.name" 
            placeholder="Buscar por nome"
            @input="debounceSearch"
          &gt;&lt;/b-form-input&gt;
        &lt;/div&gt;
        
        &lt;div class="col-md-3 mb-3"&gt;
          &lt;label class="form-label"&gt;Status&lt;/label&gt;
          &lt;b-form-select 
            v-model="filters.status" 
            :options="statusOptions"
            @change="applyFilters"
          &gt;&lt;/b-form-select&gt;
        &lt;/div&gt;
        
        &lt;div class="col-md-3 mb-3"&gt;
          &lt;label class="form-label"&gt;Categoria&lt;/label&gt;
          &lt;b-form-select 
            v-model="filters.category" 
            :options="categoryOptions"
            @change="applyFilters"
          &gt;&lt;/b-form-select&gt;
        &lt;/div&gt;
        
        &lt;div class="col-md-3 mb-3"&gt;
          &lt;label class="form-label"&gt;Data&lt;/label&gt;
          &lt;b-form-datepicker 
            v-model="filters.date" 
            placeholder="Selecione uma data"
            @input="applyFilters"
          &gt;&lt;/b-form-datepicker&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div class="row"&gt;
        &lt;div class="col-md-3 mb-3"&gt;
          &lt;label class="form-label"&gt;Preço mínimo&lt;/label&gt;
          &lt;b-form-input 
            v-model="filters.minPrice" 
            type="number" 
            placeholder="Mínimo"
            @change="applyFilters"
          &gt;&lt;/b-form-input&gt;
        &lt;/div&gt;
        
        &lt;div class="col-md-3 mb-3"&gt;
          &lt;label class="form-label"&gt;Preço máximo&lt;/label&gt;
          &lt;b-form-input 
            v-model="filters.maxPrice" 
            type="number" 
            placeholder="Máximo"
            @change="applyFilters"
          &gt;&lt;/b-form-input&gt;
        &lt;/div&gt;
        
        &lt;div class="col-md-6 mb-3 d-flex align-items-end"&gt;
          &lt;div class="d-flex gap-2"&gt;
            &lt;b-button variant="primary" @click="applyFilters"&gt;
              &lt;i class="fas fa-filter me-1"&gt;&lt;/i&gt; Aplicar Filtros
            &lt;/b-button&gt;
            
            &lt;b-button variant="outline-secondary" @click="resetFilters"&gt;
              &lt;i class="fas fa-times me-1"&gt;&lt;/i&gt; Limpar Filtros
            &lt;/b-button&gt;
            
            &lt;b-button 
              v-if="hasActiveFilters" 
              variant="outline-info" 
              @click="saveFilter"
            &gt;
              &lt;i class="fas fa-save me-1"&gt;&lt;/i&gt; Salvar Filtro
            &lt;/b-button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;!-- Filtros salvos --&gt;
      &lt;div v-if="savedFilters.length > 0" class="saved-filters mt-3"&gt;
        &lt;div class="d-flex align-items-center mb-2"&gt;
          &lt;h6 class="mb-0 me-2"&gt;Filtros salvos:&lt;/h6&gt;
          &lt;div class="d-flex flex-wrap gap-2"&gt;
            &lt;b-badge 
              v-for="(filter, index) in savedFilters" 
              :key="index"
              variant="light"
              class="saved-filter-badge"
            &gt;
              {{ filter.name }}
              &lt;b-button 
                size="sm" 
                variant="link" 
                class="p-0 ms-2" 
                @click="applySavedFilter(filter)"
              &gt;
                &lt;i class="fas fa-check-circle text-success"&gt;&lt;/i&gt;
              &lt;/b-button&gt;
              &lt;b-button 
                size="sm" 
                variant="link" 
                class="p-0 ms-1" 
                @click="deleteSavedFilter(index)"
              &gt;
                &lt;i class="fas fa-times-circle text-danger"&gt;&lt;/i&gt;
              &lt;/b-button&gt;
            &lt;/b-badge&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/b-collapse&gt;
    
    &lt;div class="filter-toggle"&gt;
      &lt;b-button 
        variant="light" 
        size="sm" 
        @click="isExpanded = !isExpanded"
      &gt;
        &lt;i :class="['fas', isExpanded ? 'fa-chevron-up' : 'fa-chevron-down']"&gt;&lt;/i&gt;
        {{ isExpanded ? 'Ocultar Filtros' : 'Mostrar Filtros' }}
      &lt;/b-button&gt;
      
      &lt;div v-if="hasActiveFilters" class="active-filters"&gt;
        &lt;span class="me-2"&gt;Filtros ativos:&lt;/span&gt;
        &lt;b-badge 
          v-for="(value, key) in activeFiltersDisplay" 
          :key="key"
          variant="primary"
          class="me-1"
        &gt;
          {{ value }}
          &lt;b-button 
            size="sm" 
            variant="link" 
            class="p-0 ms-1 text-white" 
            @click="removeFilter(key)"
          &gt;
            &lt;i class="fas fa-times"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
        &lt;/b-badge&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;!-- Modal para salvar filtro --&gt;
    &lt;b-modal v-model="showSaveFilterModal" title="Salvar Filtro" hide-footer&gt;
      &lt;b-form @submit.prevent="confirmSaveFilter"&gt;
        &lt;b-form-group
          label="Nome do Filtro"
          label-for="filter-name"
        &gt;
          &lt;b-form-input
            id="filter-name"
            v-model="newFilterName"
            required
            placeholder="Digite um nome para este filtro"
          &gt;&lt;/b-form-input&gt;
        &lt;/b-form-group&gt;
        
        &lt;div class="d-flex justify-content-end gap-2"&gt;
          &lt;b-button variant="secondary" @click="showSaveFilterModal = false"&gt;
            Cancelar
          &lt;/b-button&gt;
          &lt;b-button type="submit" variant="primary"&gt;
            Salvar
          &lt;/b-button&gt;
        &lt;/div&gt;
      &lt;/b-form&gt;
    &lt;/b-modal&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, reactive, computed, watch } from 'vue';
import { debounce } from 'lodash';

const props = defineProps({
  initialFilters: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['filter-change']);

// Estado dos filtros
const filters = reactive({
  name: '',
  status: null,
  category: null,
  date: '',
  minPrice: '',
  maxPrice: '',
  ...props.initialFilters
});

// Opções para os selects
const statusOptions = [
  { value: null, text: 'Todos os status' },
  { value: 'active', text: 'Ativo' },
  { value: 'inactive', text: 'Inativo' },
  { value: 'pending', text: 'Pendente' }
];

const categoryOptions = [
  { value: null, text: 'Todas as categorias' },
  { value: 'electronics', text: 'Eletrônicos' },
  { value: 'furniture', text: 'Móveis' },
  { value: 'clothing', text: 'Roupas' },
  { value: 'books', text: 'Livros' }
];

// Estado do painel de filtros
const isExpanded = ref(false);
const showSaveFilterModal = ref(false);
const newFilterName = ref('');
const savedFilters = ref([]);

// Debounce para busca por texto
const debounceSearch = debounce(() => {
  applyFilters();
}, 500);

// Verifica se há filtros ativos
const hasActiveFilters = computed(() => {
  return Object.values(filters).some(value => {
    if (value === null || value === '') return false;
    if (Array.isArray(value) && value.length === 0) return false;
    return true;
  });
});

// Exibição dos filtros ativos
const activeFiltersDisplay = computed(() => {
  const result = {};
  
  if (filters.name) {
    result.name = `Nome: ${filters.name}`;
  }
  
  if (filters.status) {
    const statusOption = statusOptions.find(option => option.value === filters.status);
    result.status = `Status: ${statusOption ? statusOption.text : filters.status}`;
  }
  
  if (filters.category) {
    const categoryOption = categoryOptions.find(option => option.value === filters.category);
    result.category = `Categoria: ${categoryOption ? categoryOption.text : filters.category}`;
  }
  
  if (filters.date) {
    result.date = `Data: ${filters.date}`;
  }
  
  if (filters.minPrice) {
    result.minPrice = `Preço mín: R$ ${filters.minPrice}`;
  }
  
  if (filters.maxPrice) {
    result.maxPrice = `Preço máx: R$ ${filters.maxPrice}`;
  }
  
  return result;
});

// Aplicar filtros
const applyFilters = () => {
  emit('filter-change', { ...filters });
};

// Resetar filtros
const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = '';
  });
  
  applyFilters();
};

// Remover um filtro específico
const removeFilter = (key) => {
  filters[key] = '';
  applyFilters();
};

// Salvar filtro atual
const saveFilter = () => {
  showSaveFilterModal.value = true;
};

// Confirmar salvamento do filtro
const confirmSaveFilter = () => {
  if (!newFilterName.value) return;
  
  savedFilters.value.push({
    name: newFilterName.value,
    filters: { ...filters }
  });
  
  // Salvar no localStorage
  localStorage.setItem('savedFilters', JSON.stringify(savedFilters.value));
  
  showSaveFilterModal.value = false;
  newFilterName.value = '';
};

// Aplicar um filtro salvo
const applySavedFilter = (savedFilter) => {
  Object.keys(savedFilter.filters).forEach(key => {
    filters[key] = savedFilter.filters[key];
  });
  
  applyFilters();
};

// Excluir um filtro salvo
const deleteSavedFilter = (index) => {
  savedFilters.value.splice(index, 1);
  
  // Atualizar localStorage
  localStorage.setItem('savedFilters', JSON.stringify(savedFilters.value));
};

// Carregar filtros salvos do localStorage
const loadSavedFilters = () => {
  const storedFilters = localStorage.getItem('savedFilters');
  if (storedFilters) {
    savedFilters.value = JSON.parse(storedFilters);
  }
};

// Inicialização
loadSavedFilters();
&lt;/script&gt;

&lt;style scoped&gt;
.filter-panel {
  margin-bottom: 1.5rem;
}

.filter-body {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.filter-toggle {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.active-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.saved-filter-badge {
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}
&lt;/style&gt;</code></pre>
          </div>

          <div class="code-block">
            <h4>Uso do Componente de Filtro</h4>
            <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;h2 class="mb-4"&gt;Produtos&lt;/h2&gt;
    
    &lt;advanced-filter @filter-change="handleFilterChange" /&gt;
    
    &lt;div class="d-flex justify-content-between align-items-center mb-3"&gt;
      &lt;div&gt;
        &lt;span class="text-muted"&gt;{{ totalItems }} produtos encontrados&lt;/span&gt;
      &lt;/div&gt;
      
      &lt;div class="d-flex align-items-center"&gt;
        &lt;label class="me-2 mb-0"&gt;Ordenar por:&lt;/label&gt;
        &lt;b-form-select
          v-model="sortOption"
          :options="sortOptions"
          size="sm"
          style="width: 200px;"
          @change="handleSortChange"
        &gt;&lt;/b-form-select&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;b-table
      :items="filteredItems"
      :fields="fields"
      :busy="isLoading"
      striped
      hover
      responsive
    &gt;
      &lt;template #cell(price)="data"&gt;
        R$ {{ formatPrice(data.value) }}
      &lt;/template&gt;
      
      &lt;template #cell(status)="data"&gt;
        &lt;b-badge :variant="getStatusVariant(data.value)"&gt;
          {{ data.value }}
        &lt;/b-badge&gt;
      &lt;/template&gt;
      
      &lt;template #cell(actions)="data"&gt;
        &lt;div class="d-flex gap-2"&gt;
          &lt;b-button size="sm" variant="outline-primary"&gt;
            &lt;i class="fas fa-eye"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
          &lt;b-button size="sm" variant="outline-secondary"&gt;
            &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
          &lt;b-button size="sm" variant="outline-danger"&gt;
            &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
        &lt;/div&gt;
      &lt;/template&gt;
    &lt;/b-table&gt;
    
    &lt;div class="d-flex justify-content-center"&gt;
      &lt;b-pagination
        v-model="currentPage"
        :total-rows="totalItems"
        :per-page="perPage"
        align="center"
      &gt;&lt;/b-pagination&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';
import AdvancedFilter from '@/components/common/AdvancedFilter.vue';

// Estado
const items = ref([
  { id: 1, name: 'Smartphone XYZ', category: 'electronics', price: 1299.99, status: 'active', date: '2023-05-10' },
  { id: 2, name: 'Sofá 3 Lugares', category: 'furniture', price: 2499.99, status: 'active', date: '2023-05-08' },
  { id: 3, name: 'Camiseta Básica', category: 'clothing', price: 49.99, status: 'active', date: '2023-05-15' },
  { id: 4, name: 'Livro de Programação', category: 'books', price: 89.99, status: 'active', date: '2023-05-05' },
  { id: 5, name: 'Monitor 27"', category: 'electronics', price: 1099.99, status: 'inactive', date: '2023-04-20' },
  { id: 6, name: 'Mesa de Jantar', category: 'furniture', price: 1899.99, status: 'pending', date: '2023-05-12' },
  { id: 7, name: 'Calça Jeans', category: 'clothing', price: 129.99, status: 'active', date: '2023-05-01' },
  { id: 8, name: 'Livro de Ficção', category: 'books', price: 59.99, status: 'inactive', date: '2023-04-15' },
  { id: 9, name: 'Notebook Ultra', category: 'electronics', price: 4999.99, status: 'active', date: '2023-05-18' },
  { id: 10, name: 'Poltrona Reclinável', category: 'furniture', price: 1299.99, status: 'pending', date: '2023-05-07' }
]);

const fields = [
  { key: 'id', label: 'ID', sortable: true },
  { key: 'name', label: 'Nome', sortable: true },
  { key: 'category', label: 'Categoria', sortable: true },
  { key: 'price', label: 'Preço', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'date', label: 'Data', sortable: true },
  { key: 'actions', label: 'Ações' }
];

const isLoading = ref(false);
const currentPage = ref(1);
const perPage = ref(10);
const activeFilters = ref({});
const sortOption = ref('name_asc');

const sortOptions = [
  { value: 'name_asc', text: 'Nome (A-Z)' },
  { value: 'name_desc', text: 'Nome (Z-A)' },
  { value: 'price_asc', text: 'Preço (menor-maior)' },
  { value: 'price_desc', text: 'Preço (maior-menor)' },
  { value: 'date_desc', text: 'Data (mais recente)' },
  { value: 'date_asc', text: 'Data (mais antiga)' }
];

// Filtra os itens com base nos filtros ativos
const filteredItems = computed(() => {
  let result = [...items.value];
  
  // Aplicar filtros
  if (activeFilters.value.name) {
    const searchTerm = activeFilters.value.name.toLowerCase();
    result = result.filter(item => item.name.toLowerCase().includes(searchTerm));
  }
  
  if (activeFilters.value.status) {
    result = result.filter(item => item.status === activeFilters.value.status);
  }
  
  if (activeFilters.value.category) {
    result = result.filter(item => item.category === activeFilters.value.category);
  }
  
  if (activeFilters.value.date) {
    result = result.filter(item => item.date === activeFilters.value.date);
  }
  
  if (activeFilters.value.minPrice) {
    const minPrice = parseFloat(activeFilters.value.minPrice);
    result = result.filter(item => item.price >= minPrice);
  }
  
  if (activeFilters.value.maxPrice) {
    const maxPrice = parseFloat(activeFilters.value.maxPrice);
    result = result.filter(item => item.price <= maxPrice);
  }
  
  // Aplicar ordenação
  const [sortField, sortDirection] = sortOption.value.split('_');
  
  result.sort((a, b) => {
    let comparison = 0;
    
    if (sortField === 'name') {
      comparison = a.name.localeCompare(b.name);
    } else if (sortField === 'price') {
      comparison = a.price - b.price;
    } else if (sortField === 'date') {
      comparison = new Date(a.date) - new Date(b.date);
    }
    
    return sortDirection === 'desc' ? -comparison : comparison;
  });
  
  return result;
});

const totalItems = computed(() => filteredItems.value.length);

// Manipuladores de eventos
const handleFilterChange = (filters) => {
  activeFilters.value = filters;
  currentPage.value = 1; // Voltar para a primeira página ao filtrar
};

const handleSortChange = () => {
  // A ordenação é aplicada automaticamente pelo computed filteredItems
};

// Funções auxiliares
const formatPrice = (price) => {
  return price.toFixed(2).replace('.', ',');
};

const getStatusVariant = (status) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'inactive':
      return 'secondary';
    case 'pending':
      return 'warning';
    default:
      return 'primary';
  }
};
&lt;/script&gt;</code></pre>
          </div>

          <div class="code-block">
            <h4>Componente de Ordenação Avançada</h4>
            <pre><code>&lt;template&gt;
  &lt;div class="sort-component"&gt;
    &lt;div class="d-flex align-items-center"&gt;
      &lt;label class="me-2 mb-0"&gt;{{ label }}:&lt;/label&gt;
      &lt;b-dropdown :text="currentSortLabel" size="sm" variant="outline-secondary"&gt;
        &lt;b-dropdown-item 
          v-for="option in sortOptions" 
          :key="option.value"
          :active="modelValue === option.value"
          @click="updateSort(option.value)"
        &gt;
          &lt;div class="d-flex align-items-center"&gt;
            &lt;i 
              v-if="option.direction === 'asc'" 
              class="fas fa-sort-amount-down-alt me-2"
            &gt;&lt;/i&gt;
            &lt;i 
              v-else-if="option.direction === 'desc'" 
              class="fas fa-sort-amount-down me-2"
            &gt;&lt;/i&gt;
            {{ option.text }}
          &lt;/div&gt;
        &lt;/b-dropdown-item&gt;
        
        &lt;b-dropdown-divider&gt;&lt;/b-dropdown-divider&gt;
        
        &lt;b-dropdown-item @click="showCustomSort = true"&gt;
          &lt;i class="fas fa-sliders-h me-2"&gt;&lt;/i&gt; Ordenação personalizada
        &lt;/b-dropdown-item&gt;
      &lt;/b-dropdown&gt;
    &lt;/div&gt;
    
    &lt;!-- Modal de ordenação personalizada --&gt;
    &lt;b-modal v-model="showCustomSort" title="Ordenação Personalizada" hide-footer&gt;
      &lt;p class="mb-3"&gt;Arraste os campos para definir a ordem de prioridade:&lt;/p&gt;
      
      &lt;draggable 
        v-model="customSortFields" 
        handle=".drag-handle"
        item-key="field"
        class="custom-sort-list"
      &gt;
        &lt;template #item="{ element, index }"&gt;
          &lt;div class="custom-sort-item"&gt;
            &lt;div class="drag-handle"&gt;
              &lt;i class="fas fa-grip-vertical"&gt;&lt;/i&gt;
            &lt;/div&gt;
            
            &lt;div class="sort-field-name"&gt;
              {{ element.text }}
            &lt;/div&gt;
            
            &lt;div class="sort-direction"&gt;
              &lt;b-button-group size="sm"&gt;
                &lt;b-button 
                  :pressed="element.direction === 'asc'"
                  @click="updateCustomSortDirection(index, 'asc')"
                &gt;
                  &lt;i class="fas fa-sort-up"&gt;&lt;/i&gt; Asc
                &lt;/b-button&gt;
                &lt;b-button 
                  :pressed="element.direction === 'desc'"
                  @click="updateCustomSortDirection(index, 'desc')"
                &gt;
                  &lt;i class="fas fa-sort-down"&gt;&lt;/i&gt; Desc
                &lt;/b-button&gt;
              &lt;/b-button-group&gt;
            &lt;/div&gt;
            
            &lt;div class="remove-field"&gt;
              &lt;b-button 
                variant="link" 
                size="sm" 
                class="text-danger p-0"
                @click="removeCustomSortField(index)"
              &gt;
                &lt;i class="fas fa-times"&gt;&lt;/i&gt;
              &lt;/b-button&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/template&gt;
      &lt;/draggable&gt;
      
      &lt;!-- Adicionar campo --&gt;
      &lt;div v-if="availableFields.length > 0" class="mt-3"&gt;
        &lt;b-dropdown text="Adicionar campo" size="sm" variant="outline-primary"&gt;
          &lt;b-dropdown-item 
            v-for="field in availableFields" 
            :key="field.field"
            @click="addCustomSortField(field)"
          &gt;
            {{ field.text }}
          &lt;/b-dropdown-item&gt;
        &lt;/b-dropdown&gt;
      &lt;/div&gt;
      
      &lt;div class="d-flex justify-content-end mt-4"&gt;
        &lt;b-button variant="secondary" class="me-2" @click="showCustomSort = false"&gt;
          Cancelar
        &lt;/b-button&gt;
        &lt;b-button variant="primary" @click="applyCustomSort"&gt;
          Aplicar
        &lt;/b-button&gt;
      &lt;/div&gt;
    &lt;/b-modal&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';
import draggable from 'vuedraggable';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: 'Ordenar por'
  },
  options: {
    type: Array,
    default: () => []
  },
  fields: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue']);

// Estado
const showCustomSort = ref(false);
const customSortFields = ref([]);

// Opções de ordenação predefinidas
const sortOptions = computed(() => {
  return props.options.length > 0 ? props.options : [
    { value: 'name_asc', text: 'Nome (A-Z)', field: 'name', direction: 'asc' },
    { value: 'name_desc', text: 'Nome (Z-A)', field: 'name', direction: 'desc' },
    { value: 'date_desc', text: 'Data (mais recente)', field: 'date', direction: 'desc' },
    { value: 'date_asc', text: 'Data (mais antiga)', field: 'date', direction: 'asc' },
    { value: 'price_asc', text: 'Preço (menor-maior)', field: 'price', direction: 'asc' },
    { value: 'price_desc', text: 'Preço (maior-menor)', field: 'price', direction: 'desc' }
  ];
});

// Campos disponíveis para ordenação personalizada
const availableFields = computed(() => {
  const usedFields = customSortFields.value.map(item => item.field);
  
  const allFields = props.fields.length > 0 ? props.fields : [
    { field: 'name', text: 'Nome' },
    { field: 'date', text: 'Data' },
    { field: 'price', text: 'Preço' },
    { field: 'status', text: 'Status' },
    { field: 'category', text: 'Categoria' }
  ];
  
  return allFields.filter(field => !usedFields.includes(field.field));
});

// Label da opção de ordenação atual
const currentSortLabel = computed(() => {
  if (props.modelValue.startsWith('custom_')) {
    return 'Ordenação personalizada';
  }
  
  const option = sortOptions.value.find(opt => opt.value === props.modelValue);
  return option ? option.text : sortOptions.value[0].text;
});

// Atualizar a ordenação
const updateSort = (value) => {
  emit('update:modelValue', value);
};

// Adicionar campo à ordenação personalizada
const addCustomSortField = (field) => {
  customSortFields.value.push({
    ...field,
    direction: 'asc'
  });
};

// Remover campo da ordenação personalizada
const removeCustomSortField = (index) => {
  customSortFields.value.splice(index, 1);
};

// Atualizar direção de ordenação de um campo
const updateCustomSortDirection = (index, direction) => {
  customSortFields.value[index].direction = direction;
};

// Aplicar ordenação personalizada
const applyCustomSort = () => {
  if (customSortFields.value.length === 0) {
    return;
  }
  
  // Criar um valor de ordenação personalizado
  const sortValue = 'custom_' + customSortFields.value
    .map(field => `${field.field}_${field.direction}`)
    .join('_');
  
  emit('update:modelValue', sortValue);
  showCustomSort.value = false;
};

// Inicializar campos de ordenação personalizada se já estiver usando uma
const initCustomSort = () => {
  if (props.modelValue.startsWith('custom_')) {
    const parts = props.modelValue.substring(7).split('_');
    const fields = [];
    
    for (let i = 0; i < parts.length; i += 2) {
      const fieldName = parts[i];
      const direction = parts[i + 1];
      
      // Encontrar o campo correspondente
      const fieldInfo = (props.fields.length > 0 ? props.fields : [
        { field: 'name', text: 'Nome' },
        { field: 'date', text: 'Data' },
        { field: 'price', text: 'Preço' },
        { field: 'status', text: 'Status' },
        { field: 'category', text: 'Categoria' }
      ]).find(f => f.field === fieldName);
      
      if (fieldInfo) {
        fields.push({
          ...fieldInfo,
          direction
        });
      }
    }
    
    customSortFields.value = fields;
  }
};

// Inicializar
initCustomSort();
&lt;/script&gt;

&lt;style scoped&gt;
.custom-sort-list {
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  overflow: hidden;
}

.custom-sort-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #dee2e6;
  background-color: #fff;
}

.custom-sort-item:last-child {
  border-bottom: none;
}

.drag-handle {
  cursor: move;
  padding: 0 0.5rem;
  color: #6c757d;
}

.sort-field-name {
  flex: 1;
  font-weight: 500;
  margin: 0 1rem;
}

.sort-direction {
  margin-right: 1rem;
}

.remove-field {
  width: 20px;
  text-align: center;
}
&lt;/style&gt;</code></pre>
          </div>

          <div class="best-practice">
            <h4>Diretrizes para Filtros e Ordenação</h4>
            <ul>
              <li>Forneça filtros relevantes para o tipo de dados exibidos</li>
              <li>Permita que os usuários combinem múltiplos filtros</li>
              <li>Ofereça opções de ordenação para colunas relevantes</li>
              <li>Mantenha os filtros visíveis e acessíveis</li>
              <li>Mostre claramente quais filtros estão ativos</li>
              <li>Permita que os usuários removam filtros individualmente</li>
              <li>Forneça um botão para limpar todos os filtros</li>
              <li>Considere salvar as preferências de filtro do usuário</li>
              <li>Use debounce para campos de pesquisa para evitar muitas requisições</li>
              <li>Mantenha a interface responsiva durante a filtragem</li>
            </ul>
          </div>

          <div id="listas" class="subsection">
            <h3>5.4. Listas e Grids</h3>

            <p>Listas e grids são utilizados para exibir coleções de itens de forma organizada e
              visualmente atraente.</p>

            <div class="code-block">
              <h4>Componente de Lista de Cards</h4>
              <pre><code>&lt;template&gt;
  &lt;div class="card-list"&gt;
    &lt;div v-if="loading" class="loading-overlay"&gt;
      &lt;b-spinner variant="primary" label="Carregando..."&gt;&lt;/b-spinner&gt;
    &lt;/div&gt;
    
    &lt;div v-if="items.length === 0 && !loading" class="empty-state"&gt;
      &lt;div class="text-center py-5"&gt;
        &lt;i class="fas fa-search fa-3x text-muted mb-3"&gt;&lt;/i&gt;
        &lt;h5&gt;{{ emptyMessage }}&lt;/h5&gt;
        &lt;p class="text-muted"&gt;{{ emptyDescription }}&lt;/p&gt;
        &lt;slot name="empty-action"&gt;&lt;/slot&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div v-else class="row"&gt;
      &lt;div 
        v-for="(item, index) in items" 
        :key="item.id || index"
        :class="cardColumnClass"
      &gt;
        &lt;slot name="item" :item="item" :index="index"&gt;
          &lt;!-- Card padrão se não for fornecido um slot personalizado --&gt;
          &lt;b-card
            :title="item.title || item.name"
            :img-src="item.image"
            :img-alt="item.title || item.name"
            img-top
            class="h-100"
          &gt;
            &lt;b-card-text&gt;
              {{ item.description || 'Sem descrição' }}
            &lt;/b-card-text&gt;
            
            &lt;template #footer&gt;
              &lt;div class="d-flex justify-content-between align-items-center"&gt;
                &lt;b-button variant="primary" size="sm" @click="$emit('view', item)"&gt;
                  &lt;i class="fas fa-eye me-1"&gt;&lt;/i&gt; Ver
                &lt;/b-button&gt;
                
                &lt;div&gt;
                  &lt;b-button variant="outline-secondary" size="sm" class="me-1" @click="$emit('edit', item)"&gt;
                    &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
                  &lt;/b-button&gt;
                  &lt;b-button variant="outline-danger" size="sm" @click="$emit('delete', item)"&gt;
                    &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
                  &lt;/b-button&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/template&gt;
          &lt;/b-card&gt;
        &lt;/slot&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div v-if="paginated && items.length > 0" class="d-flex justify-content-center mt-4"&gt;
      &lt;b-pagination
        v-model="currentPage"
        :total-rows="totalItems"
        :per-page="perPage"
        align="center"
        @change="$emit('page-change', $event)"
      &gt;&lt;/b-pagination&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  columns: {
    type: [Number, Object],
    default: 3
  },
  emptyMessage: {
    type: String,
    default: 'Nenhum item encontrado'
  },
  emptyDescription: {
    type: String,
    default: 'Tente ajustar seus filtros ou adicionar novos itens.'
  },
  paginated: {
    type: Boolean,
    default: false
  },
  currentPage: {
    type: Number,
    default: 1
  },
  totalItems: {
    type: Number,
    default: 0
  },
  perPage: {
    type: Number,
    default: 12
  }
});

const emit = defineEmits(['view', 'edit', 'delete', 'page-change']);

// Calcula as classes de coluna com base na propriedade columns
const cardColumnClass = computed(() => {
  if (typeof props.columns === 'number') {
    return `col-12 col-sm-6 col-md-${12 / Math.min(props.columns, 12)} mb-4`;
  }
  
  // Se columns for um objeto, permite configuração responsiva
  const { xs = 1, sm = 2, md = 3, lg = 4, xl = 4 } = props.columns;
  
  return [
    'col-12',
    `col-sm-${12 / Math.min(sm, 12)}`,
    `col-md-${12 / Math.min(md, 12)}`,
    `col-lg-${12 / Math.min(lg, 12)}`,
    `col-xl-${12 / Math.min(xl, 12)}`,
    'mb-4'
  ].join(' ');
});
&lt;/script&gt;

&lt;style scoped&gt;
.card-list {
  position: relative;
  min-height: 200px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.empty-state {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px dashed #dee2e6;
}
&lt;/style&gt;</code></pre>
            </div>

            <div class="code-block">
              <h4>Uso do Componente de Lista de Cards</h4>
              <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;div class="d-flex justify-content-between align-items-center mb-4"&gt;
      &lt;h2&gt;Produtos&lt;/h2&gt;
      
      &lt;div class="d-flex align-items-center"&gt;
        &lt;b-button-group class="me-3"&gt;
          &lt;b-button 
            :variant="viewMode === 'grid' ? 'primary' : 'outline-primary'" 
            @click="viewMode = 'grid'"
          &gt;
            &lt;i class="fas fa-th-large"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
          &lt;b-button 
            :variant="viewMode === 'list' ? 'primary' : 'outline-primary'" 
            @click="viewMode = 'list'"
          &gt;
            &lt;i class="fas fa-list"&gt;&lt;/i&gt;
          &lt;/b-button&gt;
        &lt;/b-button-group&gt;
        
        &lt;b-button variant="success" @click="showAddModal = true"&gt;
          &lt;i class="fas fa-plus me-1"&gt;&lt;/i&gt; Novo Produto
        &lt;/b-button&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;!-- Filtros --&gt;
    &lt;div class="mb-4"&gt;
      &lt;b-form-input
        v-model="searchQuery"
        placeholder="Buscar produtos..."
        class="mb-3"
      &gt;&lt;/b-form-input&gt;
      
      &lt;div class="d-flex flex-wrap gap-2"&gt;
        &lt;b-button 
          v-for="category in categories" 
          :key="category.value"
          :variant="selectedCategory === category.value ? 'primary' : 'outline-primary'"
          @click="toggleCategory(category.value)"
          class="mb-2"
        &gt;
          {{ category.text }}
        &lt;/b-button&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;!-- Visualização em Grid --&gt;
    &lt;card-list
      v-if="viewMode === 'grid'"
      :items="filteredProducts"
      :loading="isLoading"
      :columns="{ xs: 1, sm: 2, md: 3, lg: 4 }"
      :paginated="true"
      :current-page="currentPage"
      :total-items="totalItems"
      :per-page="perPage"
      @view="viewProduct"
      @edit="editProduct"
      @delete="confirmDeleteProduct"
      @page-change="handlePageChange"
    &gt;
      &lt;template #item="{ item }"&gt;
        &lt;b-card
          :img-src="item.image || '/assets/images/product-placeholder.jpg'"
          :img-alt="item.name"
          img-top
          class="h-100 product-card"
        &gt;
          &lt;b-badge 
            v-if="item.status" 
            :variant="getStatusVariant(item.status)" 
            class="position-absolute top-0 end-0 m-2"
          &gt;
            {{ item.status }}
          &lt;/b-badge&gt;
          
          &lt;h5 class="card-title"&gt;{{ item.name }}&lt;/h5&gt;
          &lt;div class="d-flex justify-content-between align-items-center mb-2"&gt;
            &lt;span class="text-primary fw-bold"&gt;R$ {{ formatPrice(item.price) }}&lt;/span&gt;
            &lt;b-badge variant="light"&gt;{{ getCategoryName(item.category) }}&lt;/b-badge&gt;
          &lt;/div&gt;
          
          &lt;p class="card-text small text-muted"&gt;{{ truncateText(item.description, 100) }}&lt;/p&gt;
          
          &lt;template #footer&gt;
            &lt;div class="d-flex justify-content-between align-items-center"&gt;
              &lt;b-button variant="primary" size="sm" @click="viewProduct(item)"&gt;
                &lt;i class="fas fa-eye me-1"&gt;&lt;/i&gt; Detalhes
              &lt;/b-button&gt;
              
              &lt;div&gt;
                &lt;b-button variant="outline-secondary" size="sm" class="me-1" @click="editProduct(item)"&gt;
                  &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
                &lt;/b-button&gt;
                &lt;b-button variant="outline-danger" size="sm" @click="confirmDeleteProduct(item)"&gt;
                  &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
                &lt;/b-button&gt;
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/template&gt;
        &lt;/b-card&gt;
      &lt;/template&gt;
      
      &lt;template #empty-action&gt;
        &lt;b-button variant="primary" @click="showAddModal = true"&gt;
          &lt;i class="fas fa-plus me-1"&gt;&lt;/i&gt; Adicionar Produto
        &lt;/b-button&gt;
      &lt;/template&gt;
    &lt;/card-list&gt;
    
    &lt;!-- Visualização em Lista --&gt;
    &lt;div v-else-if="viewMode === 'list'"&gt;
      &lt;b-list-group v-if="filteredProducts.length > 0"&gt;
        &lt;b-list-group-item 
          v-for="item in filteredProducts" 
          :key="item.id"
          class="product-list-item"
        &gt;
          &lt;div class="d-flex align-items-center"&gt;
            &lt;div class="product-image me-3"&gt;
              &lt;img 
                :src="item.image || '/assets/images/product-placeholder.jpg'" 
                :alt="item.name"
                class="img-thumbnail"
              &gt;
            &lt;/div&gt;
            
            &lt;div class="product-details flex-grow-1"&gt;
              &lt;div class="d-flex justify-content-between align-items-start"&gt;
                &lt;h5 class="mb-1"&gt;{{ item.name }}&lt;/h5&gt;
                &lt;b-badge :variant="getStatusVariant(item.status)"&gt;{{ item.status }}&lt;/b-badge&gt;
              &lt;/div&gt;
              
              &lt;div class="d-flex mb-2"&gt;
                &lt;span class="text-primary fw-bold me-3"&gt;R$ {{ formatPrice(item.price) }}&lt;/span&gt;
                &lt;b-badge variant="light"&gt;{{ getCategoryName(item.category) }}&lt;/b-badge&gt;
              &lt;/div&gt;
              
              &lt;p class="text-muted mb-0 small"&gt;{{ truncateText(item.description, 150) }}&lt;/p&gt;
            &lt;/div&gt;
            
            &lt;div class="product-actions ms-3"&gt;
              &lt;div class="d-flex flex-column gap-2"&gt;
                &lt;b-button variant="primary" size="sm" @click="viewProduct(item)"&gt;
                  &lt;i class="fas fa-eye me-1"&gt;&lt;/i&gt; Detalhes
                &lt;/b-button&gt;
                &lt;b-button variant="outline-secondary" size="sm" @click="editProduct(item)"&gt;
                  &lt;i class="fas fa-edit me-1"&gt;&lt;/i&gt; Editar
                &lt;/b-button&gt;
                &lt;b-button variant="outline-danger" size="sm" @click="confirmDeleteProduct(item)"&gt;
                  &lt;i class="fas fa-trash me-1"&gt;&lt;/i&gt; Excluir
                &lt;/b-button&gt;
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/b-list-group-item&gt;
      &lt;/b-list-group&gt;
      
      &lt;div v-else-if="!isLoading" class="empty-state text-center py-5"&gt;
        &lt;i class="fas fa-search fa-3x text-muted mb-3"&gt;&lt;/i&gt;
        &lt;h5&gt;Nenhum produto encontrado&lt;/h5&gt;
        &lt;p class="text-muted"&gt;Tente ajustar seus filtros ou adicionar novos produtos.&lt;/p&gt;
        &lt;b-button variant="primary" @click="showAddModal = true"&gt;
          &lt;i class="fas fa-plus me-1"&gt;&lt;/i&gt; Adicionar Produto
        &lt;/b-button&gt;
      &lt;/div&gt;
      
      &lt;div v-if="isLoading" class="text-center py-5"&gt;
        &lt;b-spinner variant="primary" label="Carregando..."&gt;&lt;/b-spinner&gt;
        &lt;p class="mt-2"&gt;Carregando produtos...&lt;/p&gt;
      &lt;/div&gt;
      
      &lt;!-- Paginação para visualização em lista --&gt;
      &lt;div v-if="filteredProducts.length > 0" class="d-flex justify-content-center mt-4"&gt;
        &lt;b-pagination
          v-model="currentPage"
          :total-rows="totalItems"
          :per-page="perPage"
          align="center"
          @change="handlePageChange"
        &gt;&lt;/b-pagination&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;!-- Modal de confirmação de exclusão --&gt;
    &lt;b-modal
      v-model="showDeleteModal"
      title="Confirmar Exclusão"
      ok-variant="danger"
      ok-title="Excluir"
      cancel-title="Cancelar"
      @ok="deleteProduct"
    &gt;
      &lt;p class="my-4"&gt;Tem certeza que deseja excluir o produto &lt;strong&gt;{{ productToDelete?.name }}&lt;/strong&gt;?&lt;/p&gt;
      &lt;p class="text-danger"&gt;Esta ação não pode ser desfeita.&lt;/p&gt;
    &lt;/b-modal&gt;
    
    &lt;!-- Modal para adicionar/editar produto --&gt;
    &lt;b-modal
      v-model="showAddModal"
      :title="isEditing ? 'Editar Produto' : 'Novo Produto'"
      hide-footer
      size="lg"
    &gt;
      &lt;product-form
        :product="currentProduct"
        :is-submitting="isSubmitting"
        @submit="saveProduct"
        @cancel="showAddModal = false"
      /&gt;
    &lt;/b-modal&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, onMounted } from 'vue';
import CardList from '@/components/common/CardList.vue';
import ProductForm from '@/components/products/ProductForm.vue';
import { useToastStore } from '@/stores/toastStore';

const toastStore = useToastStore();

// Estado
const products = ref([]);
const isLoading = ref(false);
const viewMode = ref('grid');
const searchQuery = ref('');
const selectedCategory = ref(null);
const currentPage = ref(1);
const perPage = ref(12);
const showDeleteModal = ref(false);
const productToDelete = ref(null);
const showAddModal = ref(false);
const currentProduct = ref({});
const isEditing = ref(false);
const isSubmitting = ref(false);

// Categorias
const categories = [
  { value: null, text: 'Todas as Categorias' },
  { value: 'electronics', text: 'Eletrônicos' },
  { value: 'furniture', text: 'Móveis' },
  { value: 'clothing', text: 'Roupas' },
  { value: 'books', text: 'Livros' }
];

// Produtos filtrados
const filteredProducts = computed(() => {
  let result = [...products.value];
  
  // Filtrar por pesquisa
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(product => 
      product.name.toLowerCase().includes(query) || 
      product.description.toLowerCase().includes(query)
    );
  }
  
  // Filtrar por categoria
  if (selectedCategory.value) {
    result = result.filter(product => product.category === selectedCategory.value);
  }
  
  return result;
});

// Total de itens após filtragem
const totalItems = computed(() => filteredProducts.value.length);

// Carregar produtos
const loadProducts = async () => {
  isLoading.value = true;
  
  try {
    // Simulação de chamada de API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    products.value = [
      {
        id: 1,
        name: 'Smartphone XYZ',
        description: 'Um smartphone avançado com câmera de alta resolução e processador rápido.',
        price: 1299.99,
        category: 'electronics',
        status: 'active',
        image: 'https://via.placeholder.com/300x200?text=Smartphone'
      },
      {
        id: 2,
        name: 'Sofá 3 Lugares',
        description: 'Sofá confortável para sala de estar, com tecido de alta qualidade e estrutura resistente.',
        price: 2499.99,
        category: 'furniture',
        status: 'active',
        image: 'https://via.placeholder.com/300x200?text=Sofa'
      },
      {
        id: 3,
        name: 'Camiseta Básica',
        description: 'Camiseta de algodão de alta qualidade, disponível em várias cores.',
        price: 49.99,
        category: 'clothing',
        status: 'active',
        image: 'https://via.placeholder.com/300x200?text=Camiseta'
      },
      {
        id: 4,
        name: 'Livro de Programação',
        description: 'Guia completo para aprender programação do zero ao avançado.',
        price: 89.99,
        category: 'books',
        status: 'active',
        image: 'https://via.placeholder.com/300x200?text=Livro'
      },
      {
        id: 5,
        name: 'Monitor 27"',
        description: 'Monitor de alta resolução com tecnologia IPS para cores vibrantes.',
        price: 1099.99,
        category: 'electronics',
        status: 'inactive',
        image: 'https://via.placeholder.com/300x200?text=Monitor'
      },
      {
        id: 6,
        name: 'Mesa de Jantar',
        description: 'Mesa de jantar elegante para 6 pessoas, feita de madeira maciça.',
        price: 1899.99,
        category: 'furniture',
        status: 'pending',
        image: 'https://via.placeholder.com/300x200?text=Mesa'
      }
    ];
  } catch (error) {
    console.error('Erro ao carregar produtos:', error);
    toastStore.error('Erro ao carregar produtos.');
  } finally {
    isLoading.value = false;
  }
};

// Alternar categoria selecionada
const toggleCategory = (category) => {
  if (selectedCategory.value === category) {
    selectedCategory.value = null;
  } else {
    selectedCategory.value = category;
  }
  
  currentPage.value = 1; // Voltar para a primeira página ao mudar o filtro
};

// Manipuladores de eventos
const handlePageChange = (page) => {
  currentPage.value = page;
  // Aqui você implementaria a lógica para carregar a página específica da API
};

const viewProduct = (product) => {
  console.log('Visualizar produto:', product);
  // Implementar navegação para a página de detalhes do produto
};

const editProduct = (product) => {
  isEditing.value = true;
  currentProduct.value = { ...product };
  showAddModal.value = true;
};

const confirmDeleteProduct = (product) => {
  productToDelete.value = product;
  showDeleteModal.value = true;
};

const deleteProduct = async () => {
  try {
    // Simulação de chamada de API
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Remover produto da lista
    products.value = products.value.filter(p => p.id !== productToDelete.value.id);
    
    toastStore.success(`Produto "${productToDelete.value.name}" excluído com sucesso.`);
    productToDelete.value = null;
  } catch (error) {
    console.error('Erro ao excluir produto:', error);
    toastStore.error('Erro ao excluir produto.');
  }
};

const saveProduct = async (product) => {
  isSubmitting.value = true;
  
  try {
    // Simulação de chamada de API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (isEditing.value) {
      // Atualizar produto existente
      const index = products.value.findIndex(p => p.id === product.id);
      if (index !== -1) {
        products.value[index] = { ...product };
      }
      toastStore.success(`Produto "${product.name}" atualizado com sucesso.`);
    } else {
      // Adicionar novo produto
      const newProduct = {
        ...product,
        id: Math.max(0, ...products.value.map(p => p.id)) + 1
      };
      products.value.push(newProduct);
      toastStore.success(`Produto "${product.name}" adicionado com sucesso.`);
    }
    
    showAddModal.value = false;
    isEditing.value = false;
    currentProduct.value = {};
  } catch (error) {
    console.error('Erro ao salvar produto:', error);
    toastStore.error('Erro ao salvar produto.');
  } finally {
    isSubmitting.value = false;
  }
};

// Funções auxiliares
const formatPrice = (price) => {
  return price.toFixed(2).replace('.', ',');
};

const getStatusVariant = (status) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'inactive':
      return 'secondary';
    case 'pending':
      return 'warning';
    default:
      return 'primary';
  }
};

const getCategoryName = (categoryValue) => {
  const category = categories.find(c => c.value === categoryValue);
  return category ? category.text : categoryValue;
};

const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

// Inicialização
onMounted(() => {
  loadProducts();
});
&lt;/script&gt;

&lt;style scoped&gt;
.product-card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.product-list-item {
  transition: background-color 0.2s;
}

.product-list-item:hover {
  background-color: #f8f9fa;
}

.product-image {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.empty-state {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px dashed #dee2e6;
}
&lt;/style&gt;</code></pre>
            </div>

            <div class="code-block">
              <h4>Componente de Grid Masonry</h4>
              <pre><code>&lt;template&gt;
  &lt;div class="masonry-grid" :style="gridStyle"&gt;
    &lt;div 
      v-for="(item, index) in items" 
      :key="item.id || index"
      class="masonry-item"
      :style="getItemStyle(item)"
    &gt;
      &lt;slot name="item" :item="item" :index="index"&gt;
        &lt;div class="masonry-content"&gt;
          &lt;img 
            v-if="item.image" 
            :src="item.image" 
            :alt="item.title || ''"
            class="masonry-image"
            @load="onImageLoad(index)"
          &gt;
          &lt;div class="masonry-body"&gt;
            &lt;h5 v-if="item.title"&gt;{{ item.title }}&lt;/h5&gt;
            &lt;p v-if="item.description"&gt;{{ item.description }}&lt;/p&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/slot&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, onMounted, watch } from 'vue';

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  columns: {
    type: Number,
    default: 3
  },
  gap: {
    type: Number,
    default: 20
  },
  responsive: {
    type: Boolean,
    default: true
  }
});

// Estado para controlar alturas dos itens
const itemHeights = ref([]);
const loadedImages = ref(0);

// Estilo do grid
const gridStyle = computed(() => {
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${getResponsiveColumns()}, 1fr)`,
    gap: `${props.gap}px`
  };
});

// Determina o número de colunas com base na responsividade
const getResponsiveColumns = () => {
  if (!props.responsive) return props.columns;
  
  // Implementação básica de responsividade
  const width = window.innerWidth;
  
  if (width < 576) return 1;
  if (width < 768) return Math.min(2, props.columns);
  if (width < 992) return Math.min(3, props.columns);
  
  return props.columns;
};

// Estilo para cada item
const getItemStyle = (item) => {
  // Aqui você pode adicionar lógica para determinar a altura com base no conteúdo
  // ou usar uma altura fixa para cada item
  return {};
};

// Manipulador de carregamento de imagem
const onImageLoad = (index) => {
  loadedImages.value++;
  
  // Se todas as imagens foram carregadas, recalcular layout
  if (loadedImages.value === props.items.length) {
    recalculateLayout();
  }
};

// Recalcular layout do grid
const recalculateLayout = () => {
  // Esta é uma implementação simplificada
  // Em um componente real, você pode querer usar uma biblioteca como Masonry.js
  // ou implementar um algoritmo mais sofisticado para calcular alturas
  
  // Exemplo: apenas para demonstração
  itemHeights.value = props.items.map((_, index) => {
    const element = document.querySelectorAll('.masonry-item')[index];
    return element ? element.offsetHeight : 0;
  });
};

// Observar mudanças na janela para responsividade
const handleResize = () => {
  // Forçar atualização do estilo do grid
  gridStyle.value = {
    ...gridStyle.value,
    gridTemplateColumns: `repeat(${getResponsiveColumns()}, 1fr)`
  };
  
  // Recalcular layout após redimensionamento
  setTimeout(recalculateLayout, 100);
};

// Observar mudanças nos itens
watch(() => props.items, () => {
  loadedImages.value = 0;
  itemHeights.value = [];
}, { deep: true });

// Inicialização
onMounted(() => {
  window.addEventListener('resize', handleResize);
  
  // Inicializar layout após um pequeno atraso para garantir que o DOM esteja pronto
  setTimeout(recalculateLayout, 100);
});

// Limpeza
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
&lt;/script&gt;

&lt;style scoped&gt;
.masonry-grid {
  width: 100%;
}

.masonry-item {
  break-inside: avoid;
  margin-bottom: var(--gap, 20px);
}

.masonry-content {
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.masonry-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.masonry-image {
  width: 100%;
  display: block;
}

.masonry-body {
  padding: 15px;
}
&lt;/style&gt;</code></pre>
            </div>

            <div class="best-practice">
              <h4>Diretrizes para Listas e Grids</h4>
              <ul>
                <li>Ofereça múltiplas visualizações (lista, grid) para diferentes
                  necessidades</li>
                <li>Implemente paginação para grandes conjuntos de dados</li>
                <li>Forneça filtros e pesquisa para facilitar a localização de itens</li>
                <li>Use estados vazios informativos quando não houver itens</li>
                <li>Adicione feedback visual para estados de carregamento</li>
                <li>Mantenha a consistência no tamanho e espaçamento dos itens</li>
                <li>Considere a responsividade para diferentes tamanhos de tela</li>
                <li>Use animações sutis para melhorar a experiência do usuário</li>
                <li>Forneça ações contextuais para cada item</li>
                <li>Otimize o carregamento de imagens para melhor performance</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section id="layouts" class="manual-section">
    <h3>6. Layouts</h3>

    <p>Layouts definem a estrutura geral da aplicação e como os componentes são
      organizados na tela.</p>

    <div class="code-block">
      <h4>Layout Principal</h4>
      <pre><code>&lt;template&gt;
  &lt;div class="app-layout"&gt;
    &lt;app-sidebar 
      :collapsed="sidebarCollapsed"
      @toggle="toggleSidebar"
    /&gt;
    
    &lt;div class="app-main" :class="{ 'sidebar-collapsed': sidebarCollapsed }"&gt;
      &lt;app-header 
        @toggle-sidebar="toggleSidebar"
        @toggle-theme="toggleTheme"
      /&gt;
      
      &lt;div class="app-content"&gt;
        &lt;div class="container-fluid"&gt;
          &lt;router-view /&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;app-footer /&gt;
    &lt;/div&gt;
    
    &lt;!-- Overlay para dispositivos móveis --&gt;
    &lt;div 
      v-if="showMobileOverlay" 
      class="mobile-overlay"
      @click="closeSidebarOnMobile"
    &gt;&lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import AppSidebar from '@/components/layout/AppSidebar.vue';
import AppHeader from '@/components/layout/AppHeader.vue';
import AppFooter from '@/components/layout/AppFooter.vue';
import { useThemeStore } from '@/stores/themeStore';

const themeStore = useThemeStore();
const route = useRoute();

// Estado
const sidebarCollapsed = ref(false);
const isMobile = ref(window.innerWidth < 992);

// Mostrar overlay apenas em dispositivos móveis quando o sidebar estiver aberto
const showMobileOverlay = computed(() => {
  return isMobile.value && !sidebarCollapsed.value;
});

// Alternar sidebar
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
  
  // Salvar preferência do usuário
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value);
};

// Fechar sidebar em dispositivos móveis
const closeSidebarOnMobile = () => {
  if (isMobile.value) {
    sidebarCollapsed.value = true;
  }
};

// Alternar tema
const toggleTheme = () => {
  themeStore.toggleTheme();
};

// Manipulador de redimensionamento da janela
const handleResize = () => {
  isMobile.value = window.innerWidth < 992;
  
  // Fechar automaticamente o sidebar em dispositivos móveis
  if (isMobile.value && !sidebarCollapsed.value) {
    sidebarCollapsed.value = true;
  }
};

// Fechar sidebar ao mudar de rota em dispositivos móveis
watch(() => route.path, () => {
  if (isMobile.value) {
    sidebarCollapsed.value = true;
  }
});

// Inicialização
onMounted(() => {
  // Carregar preferência do usuário
  const savedCollapsed = localStorage.getItem('sidebarCollapsed');
  if (savedCollapsed !== null) {
    sidebarCollapsed.value = savedCollapsed === 'true';
  } else {
    // Colapsar automaticamente em dispositivos móveis
    sidebarCollapsed.value = isMobile.value;
  }
  
  // Adicionar listener de redimensionamento
  window.addEventListener('resize', handleResize);
});

// Limpeza
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
&lt;/script&gt;

&lt;style scoped&gt;
.app-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
  margin-left: 250px;
  width: calc(100% - 250px);
}

.app-main.sidebar-collapsed {
  margin-left: 70px;
  width: calc(100% - 70px);
}

.app-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: var(--bs-body-bg);
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1030;
}

/* Responsividade */
@media (max-width: 991.98px) {
  .app-main {
    margin-left: 0;
    width: 100%;
  }
  
  .app-main.sidebar-collapsed {
    margin-left: 0;
    width: 100%;
  }
}
&lt;/style&gt;</code></pre>
    </div>

    <div class="code-block">
      <h4>Componente de Sidebar</h4>
      <pre><code>&lt;template&gt;
  &lt;div class="app-sidebar" :class="{ 'collapsed': collapsed }"&gt;
    &lt;div class="sidebar-header"&gt;
      &lt;div class="logo-container"&gt;
        &lt;img 
          v-if="collapsed" 
          src="@/assets/images/logo-icon.svg" 
          alt="Logo" 
          class="logo-icon"
        &gt;
        &lt;img 
          v-else 
          src="@/assets/images/logo-full.svg" 
          alt="Logo" 
          class="logo-full"
        &gt;
      &lt;/div&gt;
      
      &lt;button 
        class="collapse-button" 
        @click="$emit('toggle')"
        :title="collapsed ? 'Expandir menu' : 'Recolher menu'"
      &gt;
        &lt;i :class="['fas', collapsed ? 'fa-angle-right' : 'fa-angle-left']"&gt;&lt;/i&gt;
      &lt;/button&gt;
    &lt;/div&gt;
    
    &lt;div class="sidebar-content"&gt;
      &lt;div class="user-info" v-if="!collapsed"&gt;
        &lt;div class="user-avatar"&gt;
          &lt;img :src="userAvatar" :alt="userName"&gt;
        &lt;/div&gt;
        &lt;div class="user-details"&gt;
          &lt;h6 class="user-name"&gt;{{ userName }}&lt;/h6&gt;
          &lt;p class="user-role"&gt;{{ userRole }}&lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div class="sidebar-menu"&gt;
        &lt;nav&gt;
          &lt;ul class="nav-list"&gt;
            &lt;li 
              v-for="item in menuItems" 
              :key="item.path"
              class="nav-item"
              :class="{ 
                'active': isActive(item), 
                'has-submenu': item.children && item.children.length
              }"
            &gt;
              &lt;router-link 
                v-if="!item.children || !item.children.length"
                :to="item.path"
                class="nav-link"
                :title="collapsed ? item.title : ''"
              &gt;
                &lt;i :class="['nav-icon', item.icon]"&gt;&lt;/i&gt;
                &lt;span class="nav-text"&gt;{{ item.title }}&lt;/span&gt;
              &lt;/router-link&gt;
              
              &lt;div 
                v-else
                class="nav-link submenu-toggle"
                @click="toggleSubmenu(item)"
              &gt;
                &lt;i :class="['nav-icon', item.icon]"&gt;&lt;/i&gt;
                &lt;span class="nav-text"&gt;{{ item.title }}&lt;/span&gt;
                &lt;i 
                  :class="[
                    'submenu-arrow', 
                    'fas', 
                    openSubmenus.includes(item.path) ? 'fa-angle-down' : 'fa-angle-right'
                  ]"
                &gt;&lt;/i&gt;
              &lt;/div&gt;
              
              &lt;transition name="submenu"&gt;
                &lt;ul 
                  v-if="item.children && item.children.length && (openSubmenus.includes(item.path) || !collapsed)"
                  class="submenu"
                  :class="{ 'show': openSubmenus.includes(item.path) }"
                &gt;
                  &lt;li 
                    v-for="child in item.children" 
                    :key="child.path"
                    class="submenu-item"
                    :class="{ 'active': isActive(child) }"
                  &gt;
                    &lt;router-link :to="child.path" class="submenu-link"&gt;
                      &lt;i v-if="child.icon" :class="['submenu-icon', child.icon]"&gt;&lt;/i&gt;
                      &lt;span class="submenu-text"&gt;{{ child.title }}&lt;/span&gt;
                    &lt;/router-link&gt;
                  &lt;/li&gt;
                &lt;/ul&gt;
              &lt;/transition&gt;
            &lt;/li&gt;
          &lt;/ul&gt;
        &lt;/nav&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="sidebar-footer"&gt;
      &lt;div class="footer-buttons"&gt;
        &lt;button 
          class="footer-button" 
          title="Configurações"
          @click="navigateTo('/settings')"
        &gt;
          &lt;i class="fas fa-cog"&gt;&lt;/i&gt;
          &lt;span v-if="!collapsed"&gt;Configurações&lt;/span&gt;
        &lt;/button&gt;
        
        &lt;button 
          class="footer-button" 
          title="Sair"
          @click="logout"
        &gt;
          &lt;i class="fas fa-sign-out-alt"&gt;&lt;/i&gt;
          &lt;span v-if="!collapsed"&gt;Sair&lt;/span&gt;
        &lt;/button&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/authStore';

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['toggle']);

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

// Estado
const openSubmenus = ref([]);

// Dados do usuário
const userName = computed(() => authStore.user?.name || 'Usuário');
const userRole = computed(() => authStore.user?.role || 'Convidado');
const userAvatar = computed(() => authStore.user?.avatar || '/assets/images/default-avatar.png');

// Itens do menu
const menuItems = [
  {
    title: 'Dashboard',
    path: '/dashboard',
    icon: 'fas fa-tachometer-alt'
  },
  {
    title: 'Usuários',
    path: '/users',
    icon: 'fas fa-users',
    children: [
      {
        title: 'Lista de Usuários',
        path: '/users/list',
        icon: 'fas fa-list'
      },
      {
        title: 'Novo Usuário',
        path: '/users/new',
        icon: 'fas fa-user-plus'
      },
      {
        title: 'Permissões',
        path: '/users/permissions',
        icon: 'fas fa-lock'
      }
    ]
  },
  {
    title: 'Produtos',
    path: '/products',
    icon: 'fas fa-box',
    children: [
      {
        title: 'Lista de Produtos',
        path: '/products/list',
        icon: 'fas fa-list'
      },
      {
        title: 'Novo Produto',
        path: '/products/new',
        icon: 'fas fa-plus'
      },
      {
        title: 'Categorias',
        path: '/products/categories',
        icon: 'fas fa-tags'
      }
    ]
  },
  {
    title: 'Vendas',
    path: '/sales',
    icon: 'fas fa-shopping-cart'
  },
  {
    title: 'Relatórios',
    path: '/reports',
    icon: 'fas fa-chart-bar',
    children: [
      {
        title: 'Vendas',
        path: '/reports/sales',
        icon: 'fas fa-chart-line'
      },
      {
        title: 'Estoque',
        path: '/reports/inventory',
        icon: 'fas fa-warehouse'
      },
      {
        title: 'Financeiro',
        path: '/reports/financial',
        icon: 'fas fa-dollar-sign'
      }
    ]
  },
  {
    title: 'Configurações',
    path: '/settings',
    icon: 'fas fa-cog'
  },
  {
    title: 'Ajuda',
    path: '/help',
    icon: 'fas fa-question-circle'
  }
];

// Verificar se um item está ativo
const isActive = (item) => {
  if (item.path === route.path) {
    return true;
  }
  
  if (item.children) {
    return item.children.some(child => child.path === route.path);
  }
  
  return false;
};

// Alternar submenu
const toggleSubmenu = (item) => {
  if (openSubmenus.value.includes(item.path)) {
    openSubmenus.value = openSubmenus.value.filter(path => path !== item.path);
  } else {
    openSubmenus.value.push(item.path);
  }
};

// Navegar para uma rota
const navigateTo = (path) => {
  router.push(path);
};

// Logout
const logout = () => {
  authStore.logout();
  router.push('/login');
};
&lt;/script&gt;

&lt;style scoped&gt;
.app-sidebar {
  width: 250px;
  height: 100vh;
  background-color: var(--sidebar-bg, #2c3e50);
  color: var(--sidebar-color, #fff);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1040;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.app-sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  border-bottom: 1px solid var(--sidebar-border, rgba(255, 255, 255, 0.1));
}

.logo-container {
  display: flex;
  align-items: center;
  height: 40px;
  overflow: hidden;
}

.logo-full {
  height: 30px;
}

.logo-icon {
  height: 30px;
  width: 30px;
}

.collapse-button {
  background: transparent;
  border: none;
  color: var(--sidebar-color, #fff);
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.collapse-button:hover {
  background-color: var(--sidebar-hover, rgba(255, 255, 255, 0.1));
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px 0;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--sidebar-border, rgba(255, 255, 255, 0.1));
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-name {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.user-role {
  margin: 0;
  font-size: 12px;
  opacity: 0.7;
}

.sidebar-menu {
  padding: 0 15px;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 5px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 5px;
  color: var(--sidebar-color, #fff);
  text-decoration: none;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: var(--sidebar-hover, rgba(255, 255, 255, 0.1));
}

.nav-item.active > .nav-link {
  background-color: var(--sidebar-active, rgba(255, 255, 255, 0.2));
}

.nav-icon {
  width: 20px;
  text-align: center;
  margin-right: 10px;
}

.collapsed .nav-text {
  display: none;
}

.submenu-toggle {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
}

.submenu-arrow {
  margin-left: auto;
}

.submenu {
  list-style: none;
  padding: 0;
  margin: 5px 0 0 30px;
  overflow: hidden;
}

.collapsed .submenu {
  display: none;
  position: absolute;
  left: 70px;
  top: 0;
  width: 200px;
  background-color: var(--sidebar-bg, #2c3e50);
  border-radius: 0 5px 5px 0;
  box-shadow: 5px 0 10px rgba(0, 0, 0, 0.1);
  padding: 10px;
  margin: 0;
}

.collapsed .nav-item:hover .submenu {
  display: block;
}

.submenu-item {
  margin-bottom: 5px;
}

.submenu-link {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 5px;
  color: var(--sidebar-color, #fff);
  text-decoration: none;
  transition: background-color 0.2s;
  font-size: 14px;
}

.submenu-link:hover {
  background-color: var(--sidebar-hover, rgba(255, 255, 255, 0.1));
}

.submenu-item.active .submenu-link {
  background-color: var(--sidebar-active, rgba(255, 255, 255, 0.2));
}

.submenu-icon {
  width: 16px;
  text-align: center;
  margin-right: 8px;
  font-size: 12px;
}

.sidebar-footer {
  padding: 15px;
  border-top: 1px solid var(--sidebar-border, rgba(255, 255, 255, 0.1));
}

.footer-buttons {
  display: flex;
  justify-content: space-around;
}

.footer-button {
  background: transparent;
  border: none;
  color: var(--sidebar-color, #fff);
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.footer-button:hover {
  background-color: var(--sidebar-hover, rgba(255, 255, 255, 0.1));
}

.footer-button i {
  margin-right: 8px;
}

.collapsed .footer-button span {
  display: none;
}

.collapsed .footer-buttons {
  flex-direction: column;
  align-items: center;
}

.collapsed .footer-button {
  margin-bottom: 10px;
}

/* Animações */
.submenu-enter-active,
.submenu-leave-active {
  transition: max-height 0.3s ease, opacity 0.3s ease;
  max-height: 500px;
  overflow: hidden;
  opacity: 1;
}

.submenu-enter-from,
.submenu-leave-to {
  max-height: 0;
  opacity: 0;
}

/* Responsividade */
@media (max-width: 991.98px) {
  .app-sidebar {
    transform: translateX(-100%);
    box-shadow: none;
  }
  
  .app-sidebar:not(.collapsed) {
    transform: translateX(0);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  }
  
  .app-sidebar.collapsed {
    transform: translateX(-100%);
  }
}
&lt;/style&gt;</code></pre>
    </div>

    <div class="code-block">
      <h4>Componente de Header</h4>
      <pre><code>&lt;template&gt;
  &lt;header class="app-header"&gt;
    &lt;div class="header-left"&gt;
      &lt;button 
        class="sidebar-toggle"
        @click="$emit('toggle-sidebar')"
        aria-label="Toggle sidebar"
      &gt;
        &lt;i class="fas fa-bars"&gt;&lt;/i&gt;
      &lt;/button&gt;
      
      &lt;div class="breadcrumb-wrapper d-none d-md-block"&gt;
        &lt;h1 class="page-title"&gt;{{ pageTitle }}&lt;/h1&gt;
        &lt;nav aria-label="breadcrumb"&gt;
          &lt;ol class="breadcrumb"&gt;
            &lt;li 
              v-for="(item, index) in breadcrumbs" 
              :key="index"
              class="breadcrumb-item"
              :class="{ 'active': index === breadcrumbs.length - 1 }"
            &gt;
              &lt;router-link 
                v-if="index !== breadcrumbs.length - 1" 
                :to="item.path"
              &gt;
                {{ item.title }}
              &lt;/router-link&gt;
              &lt;span v-else&gt;{{ item.title }}&lt;/span&gt;
            &lt;/li&gt;
          &lt;/ol&gt;
        &lt;/nav&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="header-right"&gt;
      &lt;div class="header-search d-none d-md-block"&gt;
        &lt;div class="search-input-wrapper"&gt;
          &lt;i class="fas fa-search search-icon"&gt;&lt;/i&gt;
          &lt;input 
            type="text" 
            class="search-input" 
            placeholder="Pesquisar..."
            v-model="searchQuery"
            @keyup.enter="handleSearch"
          &gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div class="header-actions"&gt;
        &lt;!-- Botão de tema --&gt;
        &lt;button 
          class="header-action-button"
          @click="$emit('toggle-theme')"
          :title="isDarkTheme ? 'Mudar para tema claro' : 'Mudar para tema escuro'"
        &gt;
          &lt;i :class="['fas', isDarkTheme ? 'fa-sun' : 'fa-moon']"&gt;&lt;/i&gt;
        &lt;/button&gt;
        
        &lt;!-- Notificações --&gt;
        &lt;b-dropdown 
          variant="link" 
          no-caret 
          right 
          toggle-class="header-action-button"
          menu-class="notifications-dropdown"
        &gt;
          &lt;template #button-content&gt;
            &lt;i class="fas fa-bell"&gt;&lt;/i&gt;
            &lt;span v-if="unreadNotifications.length" class="notification-badge"&gt;
              {{ unreadNotifications.length }}
            &lt;/span&gt;
          &lt;/template&gt;
          
          &lt;b-dropdown-text class="notification-header"&gt;
            &lt;div class="d-flex justify-content-between align-items-center"&gt;
              &lt;span&gt;Notificações&lt;/span&gt;
              &lt;b-button 
                v-if="unreadNotifications.length" 
                variant="link" 
                size="sm" 
                class="p-0"
                @click="markAllAsRead"
              &gt;
                Marcar todas como lidas
              &lt;/b-button&gt;
            &lt;/div&gt;
          &lt;/b-dropdown-text&gt;
          
          &lt;b-dropdown-divider&gt;&lt;/b-dropdown-divider&gt;
          
          &lt;div class="notification-list"&gt;
            &lt;template v-if="notifications.length"&gt;
              &lt;b-dropdown-item 
                v-for="notification in notifications" 
                :key="notification.id"
                :class="{ 'unread': !notification.read }"
                @click="handleNotificationClick(notification)"
              &gt;
                &lt;div class="notification-item"&gt;
                  &lt;div class="notification-icon" :class="`bg-${notification.type}`"&gt;
                    &lt;i :class="getNotificationIcon(notification.type)"&gt;&lt;/i&gt;
                  &lt;/div&gt;
                  &lt;div class="notification-content"&gt;
                    &lt;div class="notification-title"&gt;{{ notification.title }}&lt;/div&gt;
                    &lt;div class="notification-text"&gt;{{ notification.message }}&lt;/div&gt;
                    &lt;div class="notification-time"&gt;{{ formatTime(notification.time) }}&lt;/div&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/b-dropdown-item&gt;
            &lt;/template&gt;
            
            &lt;b-dropdown-text v-else class="text-center py-3"&gt;
              &lt;i class="fas fa-bell-slash text-muted mb-2"&gt;&lt;/i&gt;
              &lt;p class="mb-0"&gt;Nenhuma notificação&lt;/p&gt;
            &lt;/b-dropdown-text&gt;
          &lt;/div&gt;
          
          &lt;b-dropdown-divider&gt;&lt;/b-dropdown-divider&gt;
          
          &lt;b-dropdown-item class="text-center" href="/notifications"&gt;
            Ver todas as notificações
          &lt;/b-dropdown-item&gt;
        &lt;/b-dropdown&gt;
        
        &lt;!-- Perfil do usuário --&gt;
        &lt;b-dropdown 
          variant="link" 
          no-caret 
          right 
          toggle-class="header-user-dropdown"
        &gt;
          &lt;template #button-content&gt;
            &lt;div class="user-avatar"&gt;
              &lt;img :src="userAvatar" :alt="userName"&gt;
            &lt;/div&gt;
          &lt;/template&gt;
          
          &lt;b-dropdown-text class="user-dropdown-header"&gt;
            &lt;div class="user-info"&gt;
              &lt;h6 class="user-name"&gt;{{ userName }}&lt;/h6&gt;
              &lt;p class="user-email"&gt;{{ userEmail }}&lt;/p&gt;
            &lt;/div&gt;
          &lt;/b-dropdown-text&gt;
          
          &lt;b-dropdown-divider&gt;&lt;/b-dropdown-divider&gt;
          
          &lt;b-dropdown-item href="/profile"&gt;
            &lt;i class="fas fa-user me-2"&gt;&lt;/i&gt; Meu Perfil
          &lt;/b-dropdown-item&gt;
          
          &lt;b-dropdown-item href="/settings"&gt;
            &lt;i class="fas fa-cog me-2"&gt;&lt;/i&gt; Configurações
          &lt;/b-dropdown-item&gt;
          
          &lt;b-dropdown-divider&gt;&lt;/b-dropdown-divider&gt;
          
          &lt;b-dropdown-item @click="logout"&gt;
            &lt;i class="fas fa-sign-out-alt me-2"&gt;&lt;/i&gt; Sair
          &lt;/b-dropdown-item&gt;
        &lt;/b-dropdown&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/header&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/authStore';
import { useThemeStore } from '@/stores/themeStore';
import { useNotificationStore } from '@/stores/notificationStore';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const emit = defineEmits(['toggle-sidebar', 'toggle-theme']);

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const themeStore = useThemeStore();
const notificationStore = useNotificationStore();

// Estado
const searchQuery = ref('');

// Dados do usuário
const userName = computed(() => authStore.user?.name || 'Usuário');
const userEmail = computed(() => authStore.user?.email || '<EMAIL>');
const userAvatar = computed(() => authStore.user?.avatar || '/assets/images/default-avatar.png');

// Tema
const isDarkTheme = computed(() => themeStore.isDarkTheme);

// Notificações
const notifications = computed(() => notificationStore.notifications);
const unreadNotifications = computed(() => notificationStore.unreadNotifications);

// Título da página e breadcrumbs
const pageTitle = computed(() => {
  const matched = route.matched;
  if (matched.length > 0) {
    const lastMatched = matched[matched.length - 1];
    return lastMatched.meta.title || lastMatched.name || '';
  }
  return '';
});

const breadcrumbs = computed(() => {
  const matched = route.matched;
  const result = [];
  
  // Sempre adicionar o Dashboard como primeiro item
  result.push({
    title: 'Dashboard',
    path: '/dashboard'
  });
  
  // Adicionar os itens intermediários
  for (let i = 0; i < matched.length; i++) {
    const route = matched[i];
    if (route.meta && route.meta.breadcrumb !== false) {
      result.push({
        title: route.meta.title || route.name || '',
        path: route.path
      });
    }
  }
  
  return result;
});

// Manipuladores de eventos
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      path: '/search',
      query: { q: searchQuery.value }
    });
    searchQuery.value = '';
  }
};

const handleNotificationClick = (notification) => {
  // Marcar como lida
  notificationStore.markAsRead(notification.id);
  
  // Navegar para a página relacionada, se houver
  if (notification.link) {
    router.push(notification.link);
  }
};

const markAllAsRead = () => {
  notificationStore.markAllAsRead();
};

const logout = () => {
  authStore.logout();
  router.push('/login');
};

// Funções auxiliares
const getNotificationIcon = (type) => {
  switch (type) {
    case 'success':
      return 'fas fa-check';
    case 'warning':
      return 'fas fa-exclamation-triangle';
    case 'danger':
      return 'fas fa-times';
    case 'info':
    default:
      return 'fas fa-info';
  }
};

const formatTime = (timestamp) => {
  return formatDistanceToNow(new Date(timestamp), {
    addSuffix: true,
    locale: ptBR
  });
};
&lt;/script&gt;

&lt;style scoped&gt;
.app-header {
  height: 60px;
  background-color: var(--header-bg, #fff);
  border-bottom: 1px solid var(--header-border, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 1020;
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  background: transparent;
  border: none;
  color: var(--body-color);
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 15px;
  transition: background-color 0.2s;
}

.sidebar-toggle:hover {
  background-color: var(--hover-bg, rgba(0, 0, 0, 0.05));
}

.page-title {
  font-size: 1.25rem;
  margin: 0;
  font-weight: 600;
}

.breadcrumb {
  margin: 0;
  padding: 0;
  background: transparent;
  font-size: 0.875rem;
}

.header-right {
  display: flex;
  align-items: center;
}

.search-input-wrapper {
  position: relative;
  width: 250px;
  margin-right: 20px;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.search-input {
  width: 100%;
  padding: 8px 10px 8px 35px;
  border-radius: 20px;
  border: 1px solid var(--border-color, #ced4da);
  background-color: var(--input-bg, #f8f9fa);
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
  background-color: var(--input-focus-bg, #fff);
}

.header-actions {
  display: flex;
  align-items: center;
}

.header-action-button {
  background: transparent;
  border: none;
  color: var(--body-color);
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 5px;
  position: relative;
  transition: background-color 0.2s;
}

.header-action-button:hover {
  background-color: var(--hover-bg, rgba(0, 0, 0, 0.05));
}

.notification-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: var(--danger, #dc3545);
  color: #fff;
  font-size: 0.7rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifications-dropdown {
  width: 320px;
  padding: 0;
}

.notification-header {
  padding: 10px 15px;
  font-weight: 600;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 5px 0;
}

.notification-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  margin-right: 10px;
  flex-shrink: 0;
}

.bg-success {
  background-color: var(--success, #28a745);
}

.bg-warning {
  background-color: var(--warning, #ffc107);
}

.bg-danger {
  background-color: var(--danger, #dc3545);
}

.bg-info {
  background-color: var(--info, #17a2b8);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 2px;
}

.notification-text {
  font-size: 0.8125rem;
  color: var(--text-muted);
  margin-bottom: 2px;
}

.notification-time {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.unread {
  background-color: var(--unread-bg, rgba(13, 110, 253, 0.05));
}

.header-user-dropdown {
  background: transparent;
  border: none;
  padding: 0;
  margin-left: 10px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--border-color, #e9ecef);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-dropdown-header {
  padding: 15px;
  text-align: center;
}

.user-name {
  margin: 0;
  font-size: 1rem;
}

.user-email {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-muted);
}

/* Responsividade */
@media (max-width: 767.98px) {
  .app-header {
    padding: 0 15px;
  }
  
  .breadcrumb-wrapper {
    display: none;
  }
  
  .header-search {
    display: none;
  }
}
&lt;/style&gt;</code></pre>
    </div>

    <div class="code-block">
      <h4>Componente de Footer</h4>
      <pre><code>&lt;template&gt;
  &lt;footer class="app-footer"&gt;
    &lt;div class="container-fluid"&gt;
      &lt;div class="d-flex flex-column flex-md-row justify-content-between align-items-center"&gt;
        &lt;div class="footer-copyright"&gt;
          &lt;span&gt;&copy; {{ currentYear }} Sua Empresa. Todos os direitos reservados.&lt;/span&gt;
        &lt;/div&gt;
        
        &lt;div class="footer-links"&gt;
          &lt;ul class="list-inline mb-0"&gt;
            &lt;li class="list-inline-item"&gt;
              &lt;a href="/about"&gt;Sobre&lt;/a&gt;
            &lt;/li&gt;
            &lt;li class="list-inline-item"&gt;
              &lt;a href="/privacy"&gt;Privacidade&lt;/a&gt;
            &lt;/li&gt;
            &lt;li class="list-inline-item"&gt;
              &lt;a href="/terms"&gt;Termos&lt;/a&gt;
            &lt;/li&gt;
            &lt;li class="list-inline-item"&gt;
              &lt;a href="/help"&gt;Ajuda&lt;/a&gt;
            &lt;/li&gt;
          &lt;/ul&gt;
        &lt;/div&gt;
        
        &lt;div class="footer-version"&gt;
          &lt;span&gt;v{{ appVersion }}&lt;/span&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/footer&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';

// Ano atual para o copyright
const currentYear = computed(() => new Date().getFullYear());

// Versão da aplicação
const appVersion = '1.0.0';
&lt;/script&gt;

&lt;style scoped&gt;
.app-footer {
  padding: 15px 0;
  background-color: var(--footer-bg, #f8f9fa);
  border-top: 1px solid var(--footer-border, #e9ecef);
  font-size: 0.875rem;
  color: var(--text-muted);
}

.footer-links ul {
  margin: 0;
}

.footer-links a {
  color: var(--body-color);
  text-decoration: none;
  transition: color 0.2s;
}

.footer-links a:hover {
  color: var(--primary);
}

.list-inline-item:not(:last-child) {
  margin-right: 1rem;
}

/* Responsividade */
@media (max-width: 767.98px) {
  .app-footer {
    text-align: center;
  }
  
  .footer-copyright,
  .footer-links,
  .footer-version {
    margin-bottom: 10px;
  }
}
&lt;/style&gt;</code></pre>
    </div>

    <div class="best-practice">
      <h4>Diretrizes para Layouts</h4>
      <ul>
        <li>Mantenha a consistência em todos os layouts da aplicação</li>
        <li>Projete layouts responsivos que funcionem em diferentes tamanhos de
          tela</li>
        <li>Forneça feedback visual claro para navegação e interações</li>
        <li>Considere a acessibilidade em todos os elementos do layout</li>
        <li>Use espaçamento adequado para melhorar a legibilidade</li>
        <li>Implemente transições suaves para mudanças de estado</li>
        <li>Otimize o layout para diferentes dispositivos e orientações</li>
        <li>Mantenha a hierarquia visual clara e intuitiva</li>
        <li>Permita personalização do layout quando apropriado</li>
        <li>Teste o layout em diferentes navegadores e dispositivos</li>
      </ul>
    </div>
  </section>

  <section id="responsividade" class="manual-section">
    <h3>7. Responsividade</h3>

    <p>A responsividade garante que sua aplicação funcione bem em diferentes
      tamanhos de tela e dispositivos.</p>

    <div class="code-block">
      <h4>Componente de Grid Responsivo</h4>
      <pre><code>&lt;template&gt;
  &lt;div class="responsive-grid" :style="gridStyle"&gt;
    &lt;slot&gt;&lt;/slot&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed, onMounted, onUnmounted, ref } from 'vue';

const props = defineProps({
  // Número de colunas para diferentes breakpoints
  columns: {
    type: Object,
    default: () => ({
      xs: 1,  // < 576px
      sm: 2,  // ≥ 576px
      md: 3,  // ≥ 768px
      lg: 4,  // ≥ 992px
      xl: 5   // ≥ 1200px
    })
  },
  // Espaçamento entre os itens
  gap: {
    type: [Number, String],
    default: 20
  },
  // Altura mínima dos itens
  minHeight: {
    type: [Number, String],
    default: 'auto'
  }
});

// Estado
const currentBreakpoint = ref('md');

// Calcular o estilo do grid com base no breakpoint atual
const gridStyle = computed(() => {
  const columns = props.columns[currentBreakpoint.value] || 1;
  const gapValue = typeof props.gap === 'number' ? `${props.gap}px` : props.gap;
  const minHeightValue = typeof props.minHeight === 'number' ? `${props.minHeight}px` : props.minHeight;
  
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: gapValue,
    minHeight: minHeightValue
  };
});

// Atualizar o breakpoint com base na largura da janela
const updateBreakpoint = () => {
  const width = window.innerWidth;
  
  if (width < 576) {
    currentBreakpoint.value = 'xs';
  } else if (width < 768) {
    currentBreakpoint.value = 'sm';
  } else if (width < 992) {
    currentBreakpoint.value = 'md';
  } else if (width < 1200) {
    currentBreakpoint.value = 'xl';
  } else {
    currentBreakpoint.value = 'xl';
  }
};

// Adicionar/remover listener de redimensionamento
onMounted(() => {
  updateBreakpoint();
  window.addEventListener('resize', updateBreakpoint);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateBreakpoint);
});
&lt;/script&gt;

&lt;style scoped&gt;
.responsive-grid {
  width: 100%;
}
&lt;/style&gt;</code></pre>
    </div>

    <div class="code-block">
      <h4>Componente de Imagem Responsiva</h4>
      <pre><code>&lt;template&gt;
  &lt;div class="responsive-image" :class="classes" :style="containerStyle"&gt;
    &lt;img
      v-if="currentSrc"
      :src="currentSrc"
      :alt="alt"
      :style="imageStyle"
      @load="handleImageLoad"
      @error="handleImageError"
      ref="imageRef"
    &gt;
    
    &lt;div v-if="loading" class="image-placeholder"&gt;
      &lt;div class="spinner"&gt;&lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div v-if="error" class="image-error"&gt;
      &lt;i class="fas fa-image"&gt;&lt;/i&gt;
      &lt;p&gt;Falha ao carregar imagem&lt;/p&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, watch, onMounted } from 'vue';

const props = defineProps({
  // URL da imagem
  src: {
    type: String,
    required: true
  },
  // Texto alternativo
  alt: {
    type: String,
    default: ''
  },
  // Proporção da imagem (width:height)
  aspectRatio: {
    type: String,
    default: '16:9'
  },
  // Modo de ajuste da imagem
  objectFit: {
    type: String,
    default: 'cover' // cover, contain, fill, etc.
  },
  // Posição da imagem
  objectPosition: {
    type: String,
    default: 'center'
  },
  // Arredondamento das bordas
  rounded: {
    type: [Boolean, String],
    default: false
  },
  // Imagens responsivas para diferentes tamanhos
  srcset: {
    type: Object,
    default: () => ({})
  },
  // Largura máxima
  maxWidth: {
    type: [Number, String],
    default: '100%'
  },
  // Altura máxima
  maxHeight: {
    type: [Number, String],
    default: 'auto'
  },
  // Lazy loading
  lazy: {
    type: Boolean,
    default: true
  }
});

// Estado
const loading = ref(true);
const error = ref(false);
const imageRef = ref(null);
const currentSrc = ref('');
const imageLoaded = ref(false);

// Classes CSS
const classes = computed(() => {
  const result = [];
  
  if (props.rounded === true) {
    result.push('rounded');
  } else if (props.rounded) {
    result.push(`rounded-${props.rounded}`);
  }
  
  if (loading.value) {
    result.push('loading');
  }
  
  if (error.value) {
    result.push('error');
  }
  
  return result;
});

// Estilo do container
const containerStyle = computed(() => {
  // Calcular proporção
  let paddingBottom = '56.25%'; // Padrão 16:9
  
  if (props.aspectRatio) {
    const [width, height] = props.aspectRatio.split(':').map(Number);
    if (width && height) {
      paddingBottom = `${(height / width) * 100}%`;
    }
  }
  
  const maxWidth = typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth;
  const maxHeight = typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight;
  
  return {
    paddingBottom,
    maxWidth,
    maxHeight
  };
});

// Estilo da imagem
const imageStyle = computed(() => {
  return {
    objectFit: props.objectFit,
    objectPosition: props.objectPosition
  };
});

// Selecionar a melhor imagem com base no tamanho da tela
const selectBestImage = () => {
  if (!props.srcset || Object.keys(props.srcset).length === 0) {
    return props.src;
  }
  
  const width = window.innerWidth;
  const breakpoints = Object.keys(props.srcset)
    .map(Number)
    .sort((a, b) => a - b);
  
  // Encontrar o breakpoint mais adequado
  let selectedBreakpoint = breakpoints[0];
  
  for (const breakpoint of breakpoints) {
    if (width >= breakpoint) {
      selectedBreakpoint = breakpoint;
    } else {
      break;
    }
  }
  
  return props.srcset[selectedBreakpoint] || props.src;
};

// Manipuladores de eventos
const handleImageLoad = () => {
  loading.value = false;
  imageLoaded.value = true;
};

const handleImageError = () => {
  loading.value = false;
  error.value = true;
};

// Observar mudanças na propriedade src
watch(() => props.src, () => {
  loading.value = true;
  error.value = false;
  currentSrc.value = props.lazy ? '' : selectBestImage();
});

// Implementar lazy loading
const setupLazyLoading = () => {
  if (!props.lazy || !window.IntersectionObserver) {
    currentSrc.value = selectBestImage();
    return;
  }
  
  const observer = new IntersectionObserver((entries) => {
    if (entries[0].isIntersecting) {
      currentSrc.value = selectBestImage();
      observer.disconnect();
    }
  }, {
    rootMargin: '200px 0px', // Carregar a imagem quando estiver a 200px de entrar na viewport
    threshold: 0.01
  });
  
  if (imageRef.value) {
    observer.observe(imageRef.value.parentElement);
  }
  
  return () => {
    observer.disconnect();
  };
};

// Atualizar a imagem quando a janela for redimensionada
const handleResize = () => {
  if (imageLoaded.value) {
    const newSrc = selectBestImage();
    if (newSrc !== currentSrc.value) {
      loading.value = true;
      error.value = false;
      currentSrc.value = newSrc;
    }
  }
};

// Inicialização
onMounted(() => {
  const cleanup = setupLazyLoading();
  
  window.addEventListener('resize', handleResize);
  
  // Limpeza
  return () => {
    if (cleanup) cleanup();
    window.removeEventListener('resize', handleResize);
  };
});
&lt;/script&gt;

&lt;style scoped&gt;
.responsive-image {
  position: relative;
  width: 100%;
  height: 0;
  overflow: hidden;
}

.responsive-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
}

.responsive-image.loading img {
  opacity: 0;
}

.responsive-image.error img {
  display: none;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: spin 1s linear infinite;
}

.image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  color: #6c757d;
}

.image-error i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.image-error p {
  margin: 0;
  font-size: 0.875rem;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-circle {
  border-radius: 50%;
}

.rounded-pill {
  border-radius: 50rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
&lt;/style&gt;</code></pre>
    </div>

    <div class="code-block">
      <h4>Componente de Tabela Responsiva</h4>
      <pre><code>&lt;template&gt;
  &lt;div class="responsive-table-container"&gt;
    &lt;!-- Indicador de rolagem horizontal --&gt;
    &lt;div v-if="showScrollIndicator" class="scroll-indicator" :class="{ 'fade-out': isScrolledRight }"&gt;
      &lt;i class="fas fa-arrow-right"&gt;&lt;/i&gt;
    &lt;/div&gt;
    
    &lt;div class="table-responsive" ref="tableContainer" @scroll="handleScroll"&gt;
      &lt;table class="table" :class="tableClasses"&gt;
        &lt;thead&gt;
          &lt;tr&gt;
            &lt;th v-for="column in columns" :key="column.key" :class="getColumnClass(column)"&gt;
              &lt;div class="th-content" @click="handleSort(column)"&gt;
                {{ column.label }}
                &lt;span v-if="column.sortable" class="sort-icon"&gt;
                  &lt;i v-if="sortKey === column.key && !sortDesc" class="fas fa-sort-up"&gt;&lt;/i&gt;
                  &lt;i v-else-if="sortKey === column.key && sortDesc" class="fas fa-sort-down"&gt;&lt;/i&gt;
                  &lt;i v-else class="fas fa-sort"&gt;&lt;/i&gt;
                &lt;/span&gt;
              &lt;/div&gt;
            &lt;/th&gt;
          &lt;/tr&gt;
        &lt;/thead&gt;
        &lt;tbody&gt;
          &lt;template v-if="loading"&gt;
            &lt;tr v-for="i in loadingRows" :key="`loading-${i}`"&gt;
              &lt;td v-for="column in columns" :key="`loading-${i}-${column.key}`" :class="getColumnClass(column)"&gt;
                &lt;div class="loading-placeholder"&gt;&lt;/div&gt;
              &lt;/td&gt;
            &lt;/tr&gt;
          &lt;/template&gt;
          &lt;template v-else-if="items.length"&gt;
            &lt;tr v-for="(item, index) in sortedItems" :key="getItemKey(item, index)"&gt;
              &lt;td v-for="column in columns" :key="`${getItemKey(item, index)}-${column.key}`" :class="getColumnClass(column)"&gt;
                &lt;slot :name="`cell(${column.key})`" :value="item[column.key]" :item="item" :index="index"&gt;
                  {{ formatCellValue(item[column.key], column) }}
                &lt;/slot&gt;
              &lt;/td&gt;
            &lt;/tr&gt;
          &lt;/template&gt;
          &lt;tr v-else&gt;
            &lt;td :colspan="columns.length" class="text-center py-4"&gt;
              &lt;slot name="empty"&gt;
                &lt;div class="empty-state"&gt;
                  &lt;i class="fas fa-search fa-2x text-muted mb-2"&gt;&lt;/i&gt;
                  &lt;p class="mb-0"&gt;{{ emptyText }}&lt;/p&gt;
                &lt;/div&gt;
              &lt;/slot&gt;
            &lt;/td&gt;
          &lt;/tr&gt;
        &lt;/tbody&gt;
      &lt;/table&gt;
    &lt;/div&gt;
    
    &lt;!-- Tabela para dispositivos móveis --&gt;
    &lt;div v-if="responsiveMode === 'cards'" class="mobile-cards d-md-none"&gt;
      &lt;div 
        v-for="(item, index) in sortedItems" 
        :key="`mobile-${getItemKey(item, index)}`"
        class="mobile-card"
      &gt;
        &lt;div 
          v-for="column in visibleColumns" 
          :key="`mobile-${getItemKey(item, index)}-${column.key}`"
          class="mobile-card-row"
        &gt;
          &lt;div class="mobile-card-label"&gt;{{ column.label }}&lt;/div&gt;
          &lt;div class="mobile-card-value"&gt;
            &lt;slot :name="`cell(${column.key})`" :value="item[column.key]" :item="item" :index="index"&gt;
              {{ formatCellValue(item[column.key], column) }}
            &lt;/slot&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div v-if="$slots.actions" class="mobile-card-actions"&gt;
          &lt;slot name="actions" :item="item" :index="index"&gt;&lt;/slot&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div v-if="!items.length && !loading" class="text-center py-4"&gt;
        &lt;slot name="empty"&gt;
          &lt;div class="empty-state"&gt;
            &lt;i class="fas fa-search fa-2x text-muted mb-2"&gt;&lt;/i&gt;
            &lt;p class="mb-0"&gt;{{ emptyText }}&lt;/p&gt;
          &lt;/div&gt;
        &lt;/slot&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;!-- Paginação --&gt;
    &lt;div v-if="pagination && totalRows > 0" class="table-pagination"&gt;
      &lt;div class="d-flex justify-content-between align-items-center flex-wrap"&gt;
        &lt;div class="pagination-info"&gt;
          Mostrando {{ paginationFrom }} a {{ paginationTo }} de {{ totalRows }} registros
        &lt;/div&gt;
        
        &lt;div class="d-flex align-items-center"&gt;
          &lt;span class="me-2"&gt;Itens por página:&lt;/span&gt;
          &lt;select v-model="localPerPage" class="form-select form-select-sm" style="width: 70px;"&gt;
            &lt;option v-for="option in perPageOptions" :key="option" :value="option"&gt;
              {{ option }}
            &lt;/option&gt;
          &lt;/select&gt;
        &lt;/div&gt;
        
        &lt;nav aria-label="Paginação da tabela"&gt;
          &lt;ul class="pagination pagination-sm mb-0"&gt;
            &lt;li class="page-item" :class="{ disabled: currentPage === 1 }"&gt;
              &lt;button class="page-link" @click="changePage(1)" aria-label="Primeira página"&gt;
                &lt;i class="fas fa-angle-double-left"&gt;&lt;/i&gt;
              &lt;/button&gt;
            &lt;/li&gt;
            &lt;li class="page-item" :class="{ disabled: currentPage === 1 }"&gt;
              &lt;button class="page-link" @click="changePage(currentPage - 1)" aria-label="Página anterior"&gt;
                &lt;i class="fas fa-angle-left"&gt;&lt;/i&gt;
              &lt;/button&gt;
            &lt;/li&gt;
            
            &lt;li 
              v-for="page in visiblePages" 
              :key="page"
              class="page-item"
              :class="{ active: page === currentPage }"
            &gt;
              &lt;button class="page-link" @click="changePage(page)"&gt;
                {{ page }}
              &lt;/button&gt;
            &lt;/li&gt;
            
            &lt;li class="page-item" :class="{ disabled: currentPage === totalPages }"&gt;
              &lt;button class="page-link" @click="changePage(currentPage + 1)" aria-label="Próxima página"&gt;
                &lt;i class="fas fa-angle-right"&gt;&lt;/i&gt;
              &lt;/button&gt;
            &lt;/li&gt;
            &lt;li class="page-item" :class="{ disabled: currentPage === totalPages }"&gt;
              &lt;button class="page-link" @click="changePage(totalPages)" aria-label="Última página"&gt;
                &lt;i class="fas fa-angle-double-right"&gt;&lt;/i&gt;
              &lt;/button&gt;
            &lt;/li&gt;
          &lt;/ul&gt;
        &lt;/nav&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, watch, onMounted } from 'vue';

const props = defineProps({
  // Dados da tabela
  items: {
    type: Array,
    required: true
  },
  // Definição das colunas
  columns: {
    type: Array,
    required: true
  },
  // Chave única para cada item
  itemKey: {
    type: String,
    default: 'id'
  },
  // Estado de carregamento
  loading: {
    type: Boolean,
    default: false
  },
  // Número de linhas a mostrar durante o carregamento
  loadingRows: {
    type: Number,
    default: 5
  },
  // Texto quando não há itens
  emptyText: {
    type: String,
    default: 'Nenhum registro encontrado'
  },
  // Classes adicionais para a tabela
  tableClass: {
    type: [String, Array, Object],
    default: ''
  },
  // Habilitar listras
  striped: {
    type: Boolean,
    default: true
  },
  // Habilitar hover
  hover: {
    type: Boolean,
    default: true
  },
  // Habilitar bordas
  bordered: {
    type: Boolean,
    default: false
  },
  // Habilitar ordenação
  sortable: {
    type: Boolean,
    default: true
  },
  // Coluna de ordenação padrão
  defaultSortKey: {
    type: String,
    default: ''
  },
  // Direção de ordenação padrão
  defaultSortDesc: {
    type: Boolean,
    default: false
  },
  // Modo responsivo (scroll ou cards)
  responsiveMode: {
    type: String,
    default: 'scroll',
    validator: (value) => ['scroll', 'cards'].includes(value)
  },
  // Habilitar paginação
  pagination: {
    type: Boolean,
    default: false
  },
  // Página atual
  currentPage: {
    type: Number,
    default: 1
  },
  // Total de linhas (para paginação)
  totalRows: {
    type: Number,
    default: 0
  },
  // Itens por página
  perPage: {
    type: Number,
    default: 10
  },
  // Opções de itens por página
  perPageOptions: {
    type: Array,
    default: () => [5, 10, 25, 50, 100]
  }
});

const emit = defineEmits([
  'sort',
  'page-change',
  'per-page-change'
]);

// Estado
const tableContainer = ref(null);
const sortKey = ref(props.defaultSortKey);
const sortDesc = ref(props.defaultSortDesc);
const showScrollIndicator = ref(false);
const isScrolledRight = ref(false);
const localPerPage = ref(props.perPage);

// Classes da tabela
const tableClasses = computed(() => {
  return [
    props.tableClass,
    {
      'table-striped': props.striped,
      'table-hover': props.hover,
      'table-bordered': props.bordered
    }
  ];
});

// Itens ordenados
const sortedItems = computed(() => {
  if (!props.sortable || !sortKey.value) {
    return props.items;
  }
  
  return [...props.items].sort((a, b) => {
    const aValue = a[sortKey.value];
    const bValue = b[sortKey.value];
    
    // Lidar com valores nulos/indefinidos
    if (aValue === undefined || aValue === null) return sortDesc.value ? -1 : 1;
    if (bValue === undefined || bValue === null) return sortDesc.value ? 1 : -1;
    
    // Comparar strings ignorando maiúsculas/minúsculas
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDesc.value
        ? bValue.localeCompare(aValue)
        : aValue.localeCompare(bValue);
    }
    
    // Comparar números ou outros valores
    return sortDesc.value
      ? bValue - aValue
      : aValue - bValue;
  });
});

// Colunas visíveis para o modo de cartões móveis
const visibleColumns = computed(() => {
  return props.columns.filter(column => column.visible !== false);
});

// Informações de paginação
const paginationFrom = computed(() => {
  if (props.totalRows === 0) return 0;
  return (props.currentPage - 1) * localPerPage.value + 1;
});

const paginationTo = computed(() => {
  return Math.min(props.currentPage * localPerPage.value, props.totalRows);
});

const totalPages = computed(() => {
  return Math.ceil(props.totalRows / localPerPage.value);
});

// Páginas visíveis na paginação
const visiblePages = computed(() => {
  const maxVisiblePages = 5;
  const pages = [];
  
  if (totalPages.value <= maxVisiblePages) {
    // Mostrar todas as páginas se houver menos que o máximo
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    // Lógica para mostrar páginas ao redor da página atual
    let startPage = Math.max(1, props.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;
    
    if (endPage > totalPages.value) {
      endPage = totalPages.value;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
  }
  
  return pages;
});

// Métodos
const getItemKey = (item, index) => {
  return item[props.itemKey] || index;
};

const getColumnClass = (column) => {
  return [
    column.class,
    {
      'sortable': props.sortable && column.sortable,
      'sorted': sortKey.value === column.key
    }
  ];
};

const formatCellValue = (value, column) => {
  if (value === undefined || value === null) {
    return '';
  }
  
  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(value);
  }
  
  return value;
};

const handleSort = (column) => {
  if (!props.sortable || column.sortable === false) {
    return;
  }
  
  if (sortKey.value === column.key) {
    // Inverter direção se a mesma coluna for clicada novamente
    sortDesc.value = !sortDesc.value;
  } else {
    // Definir nova coluna de ordenação
    sortKey.value = column.key;
    sortDesc.value = false;
  }
  
  emit('sort', { key: sortKey.value, desc: sortDesc.value });
};

const handleScroll = () => {
  if (!tableContainer.value) return;
  
  const { scrollLeft, scrollWidth, clientWidth } = tableContainer.value;
  
  // Mostrar indicador de rolagem se a tabela for mais larga que o contêiner
  showScrollIndicator.value = scrollWidth > clientWidth;
  
  // Verificar se a tabela está rolada até o final
  isScrolledRight.value = Math.abs(scrollWidth - clientWidth - scrollLeft) < 5;
};

const changePage = (page) => {
  if (page < 1 || page > totalPages.value) return;
  emit('page-change', page);
};

// Observar mudanças no perPage
watch(localPerPage, (newValue) => {
  emit('per-page-change', newValue);
});

// Inicialização
onMounted(() => {
  // Verificar se a tabela precisa de rolagem horizontal
  handleScroll();
  
  // Adicionar listener de redimensionamento
  window.addEventListener('resize', handleScroll);
  
  // Limpeza
  return () => {
    window.removeEventListener('resize', handleScroll);
  };
});
&lt;/script&gt;

&lt;style scoped&gt;
.responsive-table-container {
  position: relative;
  width: 100%;
}

.table-responsive {
  overflow-x: auto;
  width: 100%;
}

.table {
  margin-bottom: 0;
  width: 100%;
}

.th-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: default;
}

.sortable .th-content {
  cursor: pointer;
}

.sort-icon {
  margin-left: 0.5rem;
  opacity: 0.5;
}

.sorted .sort-icon {
  opacity: 1;
}

.loading-placeholder {
  height: 1.2rem;
  background-color: #f0f0f0;
  border-radius: 4px;
  animation: pulse 1.5s infinite;
}

/* Indicador de rolagem */
.scroll-indicator {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInOut 1.5s infinite;
  pointer-events: none;
  z-index: 1;
}

.scroll-indicator.fade-out {
  display: none;
}

/* Cards para visualização móvel */
.mobile-cards {
  margin-top: 1rem;
}

.mobile-card {
  background-color: #fff;
  border-radius: 0.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  padding: 1rem;
}

.mobile-card-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  padding: 0.5rem 0;
}

.mobile-card-row:last-child {
  border-bottom: none;
}

.mobile-card-label {
  font-weight: 600;
  width: 40%;
  padding-right: 1rem;
}

.mobile-card-value {
  flex: 1;
}

.mobile-card-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Paginação */
.table-pagination {
  margin-top: 1rem;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Estado vazio */
.empty-state {
  padding: 2rem 0;
  color: #6c757d;
}

/* Animações */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 0.8;
  }
}

/* Responsividade */
@media (max-width: 767.98px) {
  .table-pagination {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  
  .pagination-info {
    text-align: center;
  }
}
&lt;/style&gt;</code></pre>
    </div>

    <div class="code-block">
      <h4>Uso do Componente de Tabela Responsiva</h4>
      <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;h3 class="mb-4"&gt;Lista de Usuários&lt;/h3&gt;
    
    &lt;div class="mb-4"&gt;
      &lt;div class="row g-3"&gt;
        &lt;div class="col-md-4"&gt;
          &lt;div class="input-group"&gt;
            &lt;span class="input-group-text"&gt;&lt;i class="fas fa-search"&gt;&lt;/i&gt;&lt;/span&gt;
            &lt;input 
              type="text" 
              class="form-control" 
              placeholder="Buscar usuários..." 
              v-model="searchQuery"
              @input="handleSearch"
            &gt;
          &lt;/div&gt;
        &lt;/div&gt;
        
        &lt;div class="col-md-3"&gt;
          &lt;select class="form-select" v-model="statusFilter" @change="applyFilters"&gt;
            &lt;option value=""&gt;Todos os status&lt;/option&gt;
            &lt;option value="active"&gt;Ativo&lt;/option&gt;
            &lt;option value="inactive"&gt;Inativo&lt;/option&gt;
            &lt;option value="pending"&gt;Pendente&lt;/option&gt;
          &lt;/select&gt;
        &lt;/div&gt;
        
        &lt;div class="col-md-3"&gt;
          &lt;select class="form-select" v-model="departmentFilter" @change="applyFilters"&gt;
            &lt;option value=""&gt;Todos os departamentos&lt;/option&gt;
            &lt;option value="TI"&gt;TI&lt;/option&gt;
            &lt;option value="RH"&gt;RH&lt;/option&gt;
            &lt;option value="Financeiro"&gt;Financeiro&lt;/option&gt;
            &lt;option value="Marketing"&gt;Marketing&lt;/option&gt;
          &lt;/select&gt;
        &lt;/div&gt;
        
        &lt;div class="col-md-2 d-flex justify-content-end"&gt;
          &lt;button class="btn btn-primary" @click="addNewUser"&gt;
            &lt;i class="fas fa-plus me-1"&gt;&lt;/i&gt; Novo Usuário
          &lt;/button&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;responsive-table
      :items="filteredUsers"
      :columns="columns"
      :loading="isLoading"
      :pagination="true"
      :current-page="currentPage"
      :total-rows="totalUsers"
      :per-page="perPage"
      responsive-mode="cards"
      @sort="handleSort"
      @page-change="handlePageChange"
      @per-page-change="handlePerPageChange"
    &gt;
      &lt;!-- Coluna de status personalizada --&gt;
      &lt;template #cell(status)="{ value }"&gt;
        &lt;span class="badge" :class="getStatusBadgeClass(value)"&gt;
          {{ getStatusText(value) }}
        &lt;/span&gt;
      &lt;/template&gt;
      
      &lt;!-- Coluna de ações --&gt;
      &lt;template #cell(actions)="{ item }"&gt;
        &lt;div class="d-flex gap-2 justify-content-end"&gt;
          &lt;button class="btn btn-sm btn-outline-primary" @click="viewUser(item)"&gt;
            &lt;i class="fas fa-eye"&gt;&lt;/i&gt;
          &lt;/button&gt;
          &lt;button class="btn btn-sm btn-outline-secondary" @click="editUser(item)"&gt;
            &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
          &lt;/button&gt;
          &lt;button class="btn btn-sm btn-outline-danger" @click="confirmDeleteUser(item)"&gt;
            &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
          &lt;/button&gt;
        &lt;/div&gt;
      &lt;/template&gt;
      
      &lt;!-- Ações para visualização móvel --&gt;
      &lt;template #actions="{ item }"&gt;
        &lt;button class="btn btn-sm btn-outline-primary" @click="viewUser(item)"&gt;
          &lt;i class="fas fa-eye me-1"&gt;&lt;/i&gt; Ver
        &lt;/button&gt;
        &lt;button class="btn btn-sm btn-outline-secondary" @click="editUser(item)"&gt;
          &lt;i class="fas fa-edit me-1"&gt;&lt;/i&gt; Editar
        &lt;/button&gt;
        &lt;button class="btn btn-sm btn-outline-danger" @click="confirmDeleteUser(item)"&gt;
          &lt;i class="fas fa-trash me-1"&gt;&lt;/i&gt; Excluir
        &lt;/button&gt;
      &lt;/template&gt;
      
      &lt;!-- Estado vazio personalizado --&gt;
      &lt;template #empty&gt;
        &lt;div class="text-center py-4"&gt;
          &lt;i class="fas fa-users fa-3x text-muted mb-3"&gt;&lt;/i&gt;
          &lt;h5&gt;Nenhum usuário encontrado&lt;/h5&gt;
          &lt;p class="text-muted"&gt;Tente ajustar seus filtros ou adicione um novo usuário.&lt;/p&gt;
          &lt;button class="btn btn-primary mt-2" @click="addNewUser"&gt;
            &lt;i class="fas fa-plus me-1"&gt;&lt;/i&gt; Adicionar Usuário
          &lt;/button&gt;
        &lt;/div&gt;
      &lt;/template&gt;
    &lt;/responsive-table&gt;
    
    &lt;!-- Modal de confirmação de exclusão --&gt;
    &lt;div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true"&gt;
      &lt;div class="modal-dialog"&gt;
        &lt;div class="modal-content"&gt;
          &lt;div class="modal-header"&gt;
            &lt;h5 class="modal-title" id="deleteModalLabel"&gt;Confirmar Exclusão&lt;/h5&gt;
            &lt;button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"&gt;&lt;/button&gt;
          &lt;/div&gt;
          &lt;div class="modal-body"&gt;
            &lt;p&gt;Tem certeza que deseja excluir o usuário &lt;strong&gt;{{ userToDelete?.name }}&lt;/strong&gt;?&lt;/p&gt;
            &lt;p class="text-danger"&gt;Esta ação não pode ser desfeita.&lt;/p&gt;
          &lt;/div&gt;
          &lt;div class="modal-footer"&gt;
            &lt;button type="button" class="btn btn-secondary" data-bs-dismiss="modal"&gt;Cancelar&lt;/button&gt;
            &lt;button type="button" class="btn btn-danger" @click="deleteUser"&gt;Excluir&lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, onMounted } from 'vue';
import { Modal } from 'bootstrap';
import ResponsiveTable from '@/components/common/ResponsiveTable.vue';
import { useToastStore } from '@/stores/toastStore';

const toastStore = useToastStore();

// Estado
const users = ref([]);
const isLoading = ref(false);
const searchQuery = ref('');
const statusFilter = ref('');
const departmentFilter = ref('');
const currentPage = ref(1);
const perPage = ref(10);
const userToDelete = ref(null);
let deleteModal = null;

// Definição das colunas
const columns = [
  { key: 'id', label: 'ID', sortable: true },
  { key: 'name', label: 'Nome', sortable: true },
  { key: 'email', label: 'Email' },
  { key: 'department', label: 'Departamento', sortable: true },
  { key: 'role', label: 'Cargo' },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'actions', label: 'Ações', sortable: false }
];

// Usuários filtrados
const filteredUsers = computed(() => {
  let result = [...users.value];
  
    // Aplicar filtros
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(user => 
      user.name.toLowerCase().includes(query) || 
      user.email.toLowerCase().includes(query) ||
      user.role.toLowerCase().includes(query)
    );
  }
  
  if (statusFilter.value) {
    result = result.filter(user => user.status === statusFilter.value);
  }
  
  if (departmentFilter.value) {
    result = result.filter(user => user.department === departmentFilter.value);
  }
  
  return result;
});

// Total de usuários após filtragem
const totalUsers = computed(() => filteredUsers.value.length);

// Carregar usuários
const loadUsers = async () => {
  isLoading.value = true;
  
  try {
    // Simulação de chamada de API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    users.value = [
      { id: 1, name: 'João Silva', email: '<EMAIL>', department: 'TI', role: 'Desenvolvedor', status: 'active' },
      { id: 2, name: 'Maria Santos', email: '<EMAIL>', department: 'RH', role: 'Gerente', status: 'active' },
      { id: 3, name: 'Pedro Oliveira', email: '<EMAIL>', department: 'Financeiro', role: 'Analista', status: 'inactive' },
      { id: 4, name: 'Ana Costa', email: '<EMAIL>', department: 'Marketing', role: 'Designer', status: 'active' },
      { id: 5, name: 'Carlos Souza', email: '<EMAIL>', department: 'TI', role: 'Arquiteto', status: 'pending' },
      { id: 6, name: 'Fernanda Lima', email: '<EMAIL>', department: 'RH', role: 'Recrutadora', status: 'active' },
      { id: 7, name: 'Ricardo Alves', email: '<EMAIL>', department: 'Financeiro', role: 'Contador', status: 'inactive' },
      { id: 8, name: 'Juliana Pereira', email: '<EMAIL>', department: 'Marketing', role: 'Analista', status: 'active' },
      { id: 9, name: 'Roberto Santos', email: '<EMAIL>', department: 'TI', role: 'Suporte', status: 'pending' },
      { id: 10, name: 'Camila Ferreira', email: '<EMAIL>', department: 'RH', role: 'Assistente', status: 'active' },
      { id: 11, name: 'Lucas Martins', email: '<EMAIL>', department: 'TI', role: 'Desenvolvedor', status: 'active' },
      { id: 12, name: 'Amanda Rodrigues', email: '<EMAIL>', department: 'Marketing', role: 'Coordenadora', status: 'inactive' }
    ];
  } catch (error) {
    console.error('Erro ao carregar usuários:', error);
    toastStore.error('Erro ao carregar usuários.');
  } finally {
    isLoading.value = false;
  }
};

// Manipuladores de eventos
const handleSearch = () => {
  currentPage.value = 1; // Voltar para a primeira página ao pesquisar
};

const applyFilters = () => {
  currentPage.value = 1; // Voltar para a primeira página ao filtrar
};

const handleSort = ({ key, desc }) => {
  console.log(`Ordenando por ${key} em ordem ${desc ? 'decrescente' : 'crescente'}`);
  // Aqui você implementaria a lógica para ordenação no servidor, se necessário
};

const handlePageChange = (page) => {
  currentPage.value = page;
  // Aqui você implementaria a lógica para carregar a página específica do servidor
};

const handlePerPageChange = (newPerPage) => {
  perPage.value = newPerPage;
  currentPage.value = 1; // Voltar para a primeira página ao mudar itens por página
  // Aqui você implementaria a lógica para atualizar a paginação no servidor
};

const addNewUser = () => {
  // Implementar navegação para página de adição de usuário
  console.log('Adicionar novo usuário');
};

const viewUser = (user) => {
  // Implementar navegação para página de detalhes do usuário
  console.log('Visualizar usuário:', user);
};

const editUser = (user) => {
  // Implementar navegação para página de edição do usuário
  console.log('Editar usuário:', user);
};

const confirmDeleteUser = (user) => {
  userToDelete.value = user;
  deleteModal.show();
};

const deleteUser = async () => {
  try {
    // Simulação de chamada de API
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Remover usuário da lista
    users.value = users.value.filter(u => u.id !== userToDelete.value.id);
    
    toastStore.success(`Usuário ${userToDelete.value.name} excluído com sucesso.`);
    deleteModal.hide();
  } catch (error) {
    console.error('Erro ao excluir usuário:', error);
    toastStore.error('Erro ao excluir usuário.');
  }
};

// Funções auxiliares
const getStatusBadgeClass = (status) => {
  switch (status) {
    case 'active':
      return 'bg-success';
    case 'inactive':
      return 'bg-secondary';
    case 'pending':
      return 'bg-warning text-dark';
    default:
      return 'bg-primary';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 'active':
      return 'Ativo';
    case 'inactive':
      return 'Inativo';
    case 'pending':
      return 'Pendente';
    default:
      return status;
  }
};

// Inicialização
onMounted(() => {
  loadUsers();
  
  // Inicializar modal de exclusão
  deleteModal = new Modal(document.getElementById('deleteModal'));
});
&lt;/script&gt;</code></pre>
    </div>

    <div class="best-practice">
      <h4>Diretrizes para Tabelas Responsivas</h4>
      <ul>
        <li>Use tabelas responsivas para exibir dados estruturados em linhas
          e colunas</li>
        <li>Forneça alternativas para visualização em dispositivos móveis
          (cards, listas)</li>
        <li>Priorize as colunas mais importantes em visualizações móveis
        </li>
        <li>Implemente ordenação para facilitar a localização de dados</li>
        <li>Adicione paginação para conjuntos de dados grandes</li>
        <li>Forneça filtros e pesquisa para ajudar os usuários a encontrar
          informações</li>
        <li>Use estados de carregamento para feedback visual durante
          operações assíncronas</li>
        <li>Personalize células para diferentes tipos de dados (texto,
          números, datas, status)</li>
        <li>Adicione ações contextuais para cada linha</li>
        <li>Mantenha a acessibilidade com marcação semântica adequada</li>
      </ul>
    </div>

    <div id="breakpoints" class="subsection">
      <h3>7.1. Breakpoints</h3>

      <p>Breakpoints são pontos específicos de largura de tela onde o layout
        da aplicação se adapta para proporcionar a melhor experiência ao
        usuário em diferentes dispositivos.</p>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Nome</th>
              <th>Prefixo</th>
              <th>Dimensão</th>
              <th>Dispositivos típicos</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Extra small</td>
              <td><code>xs</code></td>
              <td>&lt; 576px</td>
              <td>Smartphones em modo retrato</td>
            </tr>
            <tr>
              <td>Small</td>
              <td><code>sm</code></td>
              <td>≥ 576px</td>
              <td>Smartphones em modo paisagem</td>
            </tr>
            <tr>
              <td>Medium</td>
              <td><code>md</code></td>
              <td>≥ 768px</td>
              <td>Tablets</td>
            </tr>
            <tr>
              <td>Large</td>
              <td><code>lg</code></td>
              <td>≥ 992px</td>
              <td>Desktops, laptops</td>
            </tr>
            <tr>
              <td>Extra large</td>
              <td><code>xl</code></td>
              <td>≥ 1200px</td>
              <td>Desktops grandes</td>
            </tr>
            <tr>
              <td>Extra extra large</td>
              <td><code>xxl</code></td>
              <td>≥ 1400px</td>
              <td>Monitores de alta resolução</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="code-block">
        <h4>Uso de Media Queries com SCSS</h4>
        <pre><code>// src/assets/scss/_mixins.scss

// Mixins para breakpoints responsivos
@mixin xs {
  @media (max-width: 575.98px) {
    @content;
  }
}

@mixin sm {
  @media (min-width: 576px) and (max-width: 767.98px) {
    @content;
  }
}

@mixin md {
  @media (min-width: 768px) and (max-width: 991.98px) {
    @content;
  }
}

@mixin lg {
  @media (min-width: 992px) and (max-width: 1199.98px) {
    @content;
  }
}

@mixin xl {
  @media (min-width: 1200px) and (max-width: 1399.98px) {
    @content;
  }
}

@mixin xxl {
  @media (min-width: 1400px) {
    @content;
  }
}

// Mixins para breakpoints e acima
@mixin sm-and-up {
  @media (min-width: 576px) {
    @content;
  }
}

@mixin md-and-up {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin lg-and-up {
  @media (min-width: 992px) {
    @content;
  }
}

@mixin xl-and-up {
  @media (min-width: 1200px) {
    @content;
  }
}

// Mixins para breakpoints e abaixo
@mixin sm-and-down {
  @media (max-width: 767.98px) {
    @content;
  }
}

@mixin md-and-down {
  @media (max-width: 991.98px) {
    @content;
  }
}

@mixin lg-and-down {
  @media (max-width: 1199.98px) {
    @content;
  }
}

@mixin xl-and-down {
  @media (max-width: 1399.98px) {
    @content;
  }
}

// Mixin para orientação
@mixin landscape {
  @media (orientation: landscape) {
    @content;
  }
}

@mixin portrait {
  @media (orientation: portrait) {
    @content;
  }
}</code></pre>
      </div>

      <div class="code-block">
        <h4>Exemplo de Uso dos Mixins</h4>
        <pre><code>// src/components/Header.vue
&lt;style lang="scss"&gt;
@import '@/assets/scss/mixins';

.header {
  padding: 1rem;
  
  @include sm-and-up {
    padding: 1.5rem;
  }
  
  @include lg-and-up {
    padding: 2rem;
  }
  
  .logo {
    width: 120px;
    
    @include xs {
      width: 80px;
    }
    
    @include xl-and-up {
      width: 150px;
    }
  }
  
  .navigation {
    display: none;
    
    @include md-and-up {
      display: flex;
    }
  }
  
  .mobile-menu {
    display: block;
    
    @include md-and-up {
      display: none;
    }
  }
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Composable para Detecção de Breakpoints</h4>
        <pre><code>// src/composables/useBreakpoints.js
import { ref, onMounted, onUnmounted } from 'vue';

export function useBreakpoints() {
  const windowWidth = ref(window.innerWidth);
  
  const breakpoints = {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1400
  };
  
  // Atualizar largura da janela quando redimensionar
  const updateWidth = () => {
    windowWidth.value = window.innerWidth;
  };
  
  // Adicionar/remover listener de redimensionamento
  onMounted(() => {
    window.addEventListener('resize', updateWidth);
  });
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth);
  });
  
  // Verificar se a largura atual está dentro de um breakpoint específico
  const isBreakpoint = (breakpoint) => {
    if (breakpoint === 'xs') {
      return windowWidth.value < breakpoints.sm;
    }
    
    if (breakpoint === 'sm') {
      return windowWidth.value >= breakpoints.sm && windowWidth.value < breakpoints.md;
    }
    
    if (breakpoint === 'md') {
      return windowWidth.value >= breakpoints.md && windowWidth.value < breakpoints.lg;
    }
    
    if (breakpoint === 'lg') {
      return windowWidth.value >= breakpoints.lg && windowWidth.value < breakpoints.xl;
    }
    
    if (breakpoint === 'xl') {
      return windowWidth.value >= breakpoints.xl && windowWidth.value < breakpoints.xxl;
    }
    
    if (breakpoint === 'xxl') {
      return windowWidth.value >= breakpoints.xxl;
    }
    
    return false;
  };
  
  // Verificar se a largura atual é menor que um breakpoint específico
  const isLessThan = (breakpoint) => {
    return windowWidth.value < breakpoints[breakpoint];
  };
  
  // Verificar se a largura atual é maior que um breakpoint específico
  const isGreaterThan = (breakpoint) => {
    return windowWidth.value >= breakpoints[breakpoint];
  };
  
  // Obter o breakpoint atual
  const current = () => {
    if (windowWidth.value < breakpoints.sm) return 'xs';
    if (windowWidth.value < breakpoints.md) return 'sm';
    if (windowWidth.value < breakpoints.lg) return 'md';
    if (windowWidth.value < breakpoints.xl) return 'lg';
    if (windowWidth.value < breakpoints.xxl) return 'xl';
    return 'xxl';
  };
  
  return {
    windowWidth,
    breakpoints,
    isBreakpoint,
    isLessThan,
    isGreaterThan,
    current
  };
}</code></pre>
      </div>

      <div class="code-block">
        <h4>Uso do Composable de Breakpoints</h4>
        <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;div v-if="isLessThan('md')" class="mobile-view"&gt;
      &lt;!-- Layout para dispositivos móveis --&gt;
      &lt;h2&gt;Visualização Mobile&lt;/h2&gt;
      &lt;p&gt;Este conteúdo é otimizado para telas pequenas.&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div v-else class="desktop-view"&gt;
      &lt;!-- Layout para desktop --&gt;
      &lt;h2&gt;Visualização Desktop&lt;/h2&gt;
      &lt;p&gt;Este conteúdo é otimizado para telas maiores.&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div class="breakpoint-indicator"&gt;
      Breakpoint atual: {{ currentBreakpoint }}
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';
import { useBreakpoints } from '@/composables/useBreakpoints';

const { isLessThan, current } = useBreakpoints();

const currentBreakpoint = computed(() => current());
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Breakpoints</h4>
        <ul>
          <li>Use os breakpoints padrão do Bootstrap para manter a
            consistência</li>
          <li>Projete primeiro para dispositivos móveis (mobile-first)
          </li>
          <li>Teste seu layout em todos os breakpoints principais</li>
          <li>Considere não apenas a largura, mas também a altura da tela
          </li>
          <li>Use mixins ou composables para facilitar o trabalho com
            breakpoints</li>
          <li>Evite criar muitos breakpoints personalizados</li>
          <li>Considere a orientação do dispositivo (retrato vs. paisagem)
          </li>
          <li>Adapte não apenas o layout, mas também o comportamento da
            interface</li>
          <li>Teste em dispositivos reais, não apenas em emuladores</li>
          <li>Documente os breakpoints e seu uso para a equipe</li>
        </ul>
      </div>
    </div>

    <div id="grid-system" class="subsection">
      <h3>7.2. Sistema de Grid</h3>

      <p>O sistema de grid do Bootstrap 5 é uma ferramenta poderosa para criar
        layouts responsivos, baseado em um sistema de 12 colunas que se
        adapta a diferentes tamanhos de tela.</p>

      <div class="code-block">
        <h4>Exemplo Básico de Grid</h4>
        <pre><code>&lt;template&gt;
  &lt;b-container&gt;
    &lt;b-row&gt;
      &lt;b-col cols="12" md="6" lg="4"&gt;
        &lt;div class="p-3 bg-light border"&gt;Coluna 1&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="12" md="6" lg="4"&gt;
        &lt;div class="p-3 bg-light border"&gt;Coluna 2&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="12" md="12" lg="4"&gt;
        &lt;div class="p-3 bg-light border"&gt;Coluna 3&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
  &lt;/b-container&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Grid com Alinhamento</h4>
        <pre><code>&lt;template&gt;
  &lt;b-container&gt;
    &lt;!-- Alinhamento vertical --&gt;
    &lt;b-row class="bg-light mb-3" style="height: 100px;" align-v="center"&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Centralizado verticalmente&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Centralizado verticalmente&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
    
    &lt;!-- Alinhamento horizontal --&gt;
    &lt;b-row align-h="center" class="mb-3"&gt;
      &lt;b-col cols="4"&gt;
        &lt;div class="p-2 border"&gt;Centralizado horizontalmente&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
    
    &lt;!-- Alinhamento individual --&gt;
    &lt;b-row class="bg-light mb-3" style="height: 100px;"&gt;
      &lt;b-col align-self="start"&gt;
        &lt;div class="p-2 border"&gt;Alinhado ao topo&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col align-self="center"&gt;
        &lt;div class="p-2 border"&gt;Alinhado ao centro&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col align-self="end"&gt;
        &lt;div class="p-2 border"&gt;Alinhado à base&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
  &lt;/b-container&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Grid com Offset e Ordem</h4>
        <pre><code>&lt;template&gt;
  &lt;b-container&gt;
    &lt;!-- Offset (deslocamento) --&gt;
    &lt;b-row class="mb-3"&gt;
      &lt;b-col cols="4"&gt;
        &lt;div class="p-2 border"&gt;Coluna 1&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="4" offset="4"&gt;
        &lt;div class="p-2 border"&gt;Coluna 2 (com offset)&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
    
    &lt;!-- Ordem --&gt;
    &lt;b-row class="mb-3"&gt;
      &lt;b-col order="3"&gt;
        &lt;div class="p-2 border"&gt;Primeira no código, terceira na exibição&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col order="1"&gt;
        &lt;div class="p-2 border"&gt;Segunda no código, primeira na exibição&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col order="2"&gt;
        &lt;div class="p-2 border"&gt;Terceira no código, segunda na exibição&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
    
    &lt;!-- Ordem responsiva --&gt;
    &lt;b-row class="mb-3"&gt;
      &lt;b-col order="2" order-md="1"&gt;
        &lt;div class="p-2 border"&gt;Segunda em mobile, primeira em desktop&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col order="1" order-md="2"&gt;
        &lt;div class="p-2 border"&gt;Primeira em mobile, segunda em desktop&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
  &lt;/b-container&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Grid com Gutters Personalizados</h4>
        <pre><code>&lt;template&gt;
  &lt;b-container&gt;
    &lt;!-- Sem gutters --&gt;
    &lt;b-row no-gutters class="mb-3"&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Sem espaçamento&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Sem espaçamento&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
    
    &lt;!-- Gutters personalizados --&gt;
    &lt;b-row class="mb-3 g-5"&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Espaçamento grande&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Espaçamento grande&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
    
    &lt;!-- Gutters horizontais e verticais diferentes --&gt;
    &lt;b-row class="mb-3 gx-1 gy-4"&gt;
      &lt;b-col cols="6"&gt;
        &lt;div class="p-2 border"&gt;Gutter-x pequeno, gutter-y grande&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="6"&gt;
        &lt;div class="p-2 border"&gt;Gutter-x pequeno, gutter-y grande&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="6"&gt;
        &lt;div class="p-2 border"&gt;Gutter-x pequeno, gutter-y grande&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="6"&gt;
        &lt;div class="p-2 border"&gt;Gutter-x pequeno, gutter-y grande&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
  &lt;/b-container&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Grid Aninhado</h4>
        <pre><code>&lt;template&gt;
  &lt;b-container&gt;
    &lt;b-row&gt;
      &lt;b-col cols="6"&gt;
        &lt;div class="p-2 border"&gt;
          Coluna principal 1
          
          &lt;!-- Grid aninhado --&gt;
          &lt;b-row class="mt-2"&gt;
            &lt;b-col&gt;
              &lt;div class="p-2 border border-primary"&gt;Aninhada 1&lt;/div&gt;
            &lt;/b-col&gt;
            &lt;b-col&gt;
              &lt;div class="p-2 border border-primary"&gt;Aninhada 2&lt;/div&gt;
            &lt;/b-col&gt;
          &lt;/b-row&gt;
        &lt;/div&gt;
      &lt;/b-col&gt;
      
      &lt;b-col cols="6"&gt;
        &lt;div class="p-2 border"&gt;
          Coluna principal 2
          
          &lt;!-- Grid aninhado --&gt;
          &lt;b-row class="mt-2"&gt;
            &lt;b-col cols="4"&gt;
              &lt;div class="p-2 border border-primary"&gt;Aninhada 3&lt;/div&gt;
            &lt;/b-col&gt;
            &lt;b-col cols="8"&gt;
              &lt;div class="p-2 border border-primary"&gt;Aninhada 4&lt;/div&gt;
            &lt;/b-col&gt;
          &lt;/b-row&gt;
        &lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
  &lt;/b-container&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Grid com Auto-Layout</h4>
        <pre><code>&lt;template&gt;
  &lt;b-container&gt;
    &lt;!-- Colunas com largura igual --&gt;
    &lt;b-row class="mb-3"&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Auto&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Auto&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Auto&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
    
    &lt;!-- Uma coluna com largura fixa, outras automáticas --&gt;
    &lt;b-row class="mb-3"&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Auto&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="6"&gt;
        &lt;div class="p-2 border"&gt;Largura fixa (6/12)&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col&gt;
        &lt;div class="p-2 border"&gt;Auto&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
    
    &lt;!-- Colunas com largura variável --&gt;
    &lt;b-row class="mb-3"&gt;
      &lt;b-col cols="12" sm="auto" md="4" lg="6"&gt;
        &lt;div class="p-2 border"&gt;Responsiva&lt;/div&gt;
      &lt;/b-col&gt;
      &lt;b-col cols="12" sm="auto" md="8" lg="6"&gt;
        &lt;div class="p-2 border"&gt;Responsiva&lt;/div&gt;
      &lt;/b-col&gt;
    &lt;/b-row&gt;
  &lt;/b-container&gt;
&lt;/template&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para o Sistema de Grid</h4>
        <ul>
          <li>Use o sistema de grid para criar layouts responsivos e
            consistentes</li>
          <li>Adote a abordagem mobile-first (defina primeiro para telas
            pequenas)</li>
          <li>Utilize os breakpoints padrão (xs, sm, md, lg, xl, xxl) para
            consistência</li>
          <li>Aproveite os recursos de alinhamento vertical e horizontal
          </li>
          <li>Use offset para criar espaços entre colunas quando
            necessário</li>
          <li>Utilize a ordem para reorganizar o conteúdo em diferentes
            tamanhos de tela</li>
          <li>Ajuste os gutters (espaçamentos) conforme necessário para o
            design</li>
          <li>Combine com utilitários de espaçamento e flexbox para
            layouts mais complexos</li>
          <li>Teste o layout em todos os breakpoints para garantir uma boa
            experiência</li>
          <li>Evite aninhar muitos níveis de grid para manter o código
            limpo</li>
        </ul>
      </div>

      <div id="componentes-responsivos" class="subsection">
        <h3>7.3. Componentes Responsivos</h3>

        <p>Componentes responsivos são projetados para se adaptar a
          diferentes tamanhos de tela, proporcionando uma experiência
          consistente em todos os dispositivos.</p>

        <div class="code-block">
          <h4>Componente de Card Responsivo</h4>
          <pre><code>&lt;template&gt;
  &lt;div class="responsive-card" :class="classes"&gt;
    &lt;div v-if="imagePosition === 'top' && image" class="card-image-container"&gt;
      &lt;img :src="image" :alt="imageAlt || title" class="card-image"&gt;
    &lt;/div&gt;
    
    &lt;div class="card-body-container" :class="{ 'has-image': image && imagePosition === 'left' }"&gt;
      &lt;div v-if="imagePosition === 'left' && image" class="card-image-left"&gt;
        &lt;img :src="image" :alt="imageAlt || title" class="card-image"&gt;
      &lt;/div&gt;
      
      &lt;div class="card-content"&gt;
        &lt;div v-if="badge" class="card-badge" :class="`bg-${badgeVariant}`"&gt;{{ badge }}&lt;/div&gt;
        
        &lt;h3 v-if="title" class="card-title"&gt;{{ title }}&lt;/h3&gt;
        &lt;h4 v-if="subtitle" class="card-subtitle"&gt;{{ subtitle }}&lt;/h4&gt;
        
        &lt;div v-if="$slots.default" class="card-text"&gt;
          &lt;slot&gt;&lt;/slot&gt;
        &lt;/div&gt;
        
        &lt;div v-if="$slots.footer || footerText" class="card-footer"&gt;
          &lt;slot name="footer"&gt;{{ footerText }}&lt;/slot&gt;
        &lt;/div&gt;
        
        &lt;div v-if="$slots.actions" class="card-actions"&gt;
          &lt;slot name="actions"&gt;&lt;/slot&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  image: {
    type: String,
    default: ''
  },
  imageAlt: {
    type: String,
    default: ''
  },
  imagePosition: {
    type: String,
    default: 'top',
    validator: (value) => ['top', 'left'].includes(value)
  },
  badge: {
    type: String,
    default: ''
  },
  badgeVariant: {
    type: String,
    default: 'primary'
  },
  footerText: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'secondary', 'outline'].includes(value)
  },
  hover: {
    type: Boolean,
    default: true
  },
  clickable: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click']);

// Classes CSS
const classes = computed(() => {
  return [
    `card-variant-${props.variant}`,
    {
      'card-hover': props.hover,
      'card-clickable': props.clickable,
      [`card-image-${props.imagePosition}`]: props.image
    }
  ];
});
&lt;/script&gt;

&lt;style scoped&gt;
.responsive-card {
  background-color: var(--card-bg, #fff);
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-clickable {
  cursor: pointer;
}

.card-image-container {
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  position: relative;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.card-body-container {
  display: flex;
  flex: 1;
}

.card-body-container.has-image {
  flex-direction: row;
}

.card-image-left {
  width: 120px;
  flex-shrink: 0;
  overflow: hidden;
}

.card-image-left .card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-content {
  padding: 1.25rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #fff;
  border-radius: 0.25rem;
  margin-bottom: 0.75rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--card-title-color, #212529);
}

.card-subtitle {
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 0.75rem 0;
  color: var(--card-subtitle-color, #6c757d);
}

.card-text {
  color: var(--card-text-color, #495057);
  margin-bottom: 1rem;
  flex: 1;
}

.card-footer {
  font-size: 0.875rem;
  color: var(--card-footer-color, #6c757d);
  margin-top: auto;
  padding-top: 0.75rem;
  border-top: 1px solid var(--card-border-color, #f0f0f0);
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1rem;
}

/* Variantes */
.card-variant-primary {
  background-color: var(--primary, #007bff);
  color: #fff;
}

.card-variant-primary .card-title,
.card-variant-primary .card-subtitle,
.card-variant-primary .card-text {
  color: #fff;
}

.card-variant-primary .card-footer {
  color: rgba(255, 255, 255, 0.8);
  border-top-color: rgba(255, 255, 255, 0.2);
}

.card-variant-secondary {
  background-color: var(--secondary, #6c757d);
  color: #fff;
}

.card-variant-secondary .card-title,
.card-variant-secondary .card-subtitle,
.card-variant-secondary .card-text {
  color: #fff;
}

.card-variant-secondary .card-footer {
  color: rgba(255, 255, 255, 0.8);
  border-top-color: rgba(255, 255, 255, 0.2);
}

.card-variant-outline {
  background-color: transparent;
  border: 1px solid var(--card-border-color, #dee2e6);
  box-shadow: none;
}

/* Responsividade */
@media (max-width: 767.98px) {
  .card-body-container.has-image {
    flex-direction: column;
  }
  
  .card-image-left {
    width: 100%;
    height: 180px;
  }
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Uso do Card Responsivo</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;h3 class="mb-4"&gt;Cards Responsivos&lt;/h3&gt;
    
    &lt;div class="row g-4"&gt;
      &lt;!-- Card básico --&gt;
      &lt;div class="col-12 col-md-6 col-lg-4"&gt;
        &lt;responsive-card
          title="Card Básico"
          subtitle="Com imagem no topo"
          image="https://via.placeholder.com/800x450"
          badge="Novo"
          footer-text="Última atualização: 2 dias atrás"
        &gt;
          &lt;p&gt;Este é um exemplo de card responsivo com imagem no topo, título, subtítulo e rodapé.&lt;/p&gt;
          
          &lt;template #actions&gt;
            &lt;button class="btn btn-sm btn-primary"&gt;Detalhes&lt;/button&gt;
            &lt;button class="btn btn-sm btn-outline-secondary"&gt;Compartilhar&lt;/button&gt;
          &lt;/template&gt;
        &lt;/responsive-card&gt;
      &lt;/div&gt;
      
      &lt;!-- Card com imagem à esquerda --&gt;
      &lt;div class="col-12 col-lg-8"&gt;
        &lt;responsive-card
          title="Card com Imagem à Esquerda"
          subtitle="Layout horizontal em desktop, vertical em mobile"
          image="https://via.placeholder.com/400x400"
          image-position="left"
          badge="Destaque"
          badge-variant="success"
        &gt;
          &lt;p&gt;Este card usa um layout horizontal em telas maiores, com a imagem à esquerda. Em dispositivos móveis, ele se adapta para um layout vertical.&lt;/p&gt;
          &lt;p&gt;Isso demonstra como os componentes podem se adaptar a diferentes tamanhos de tela.&lt;/p&gt;
          
          &lt;template #actions&gt;
            &lt;button class="btn btn-sm btn-primary"&gt;Saiba mais&lt;/button&gt;
          &lt;/template&gt;
        &lt;/responsive-card&gt;
      &lt;/div&gt;
      
      &lt;!-- Card com variante de cor --&gt;
      &lt;div class="col-12 col-md-6 col-lg-4"&gt;
        &lt;responsive-card
          title="Card Primário"
          subtitle="Com fundo colorido"
          variant="primary"
        &gt;
          &lt;p&gt;Este card usa a variante de cor primária, alterando o esquema de cores para criar destaque.&lt;/p&gt;
          
          &lt;template #footer&gt;
            &lt;div class="d-flex justify-content-between align-items-center"&gt;
              &lt;span&gt;4.5 &lt;i class="fas fa-star"&gt;&lt;/i&gt;&lt;/span&gt;
              &lt;span&gt;42 avaliações&lt;/span&gt;
            &lt;/div&gt;
          &lt;/template&gt;
          
          &lt;template #actions&gt;
            &lt;button class="btn btn-sm btn-light"&gt;Ação&lt;/button&gt;
          &lt;/template&gt;
        &lt;/responsive-card&gt;
      &lt;/div&gt;
      
      &lt;!-- Card outline --&gt;
      &lt;div class="col-12 col-md-6 col-lg-4"&gt;
        &lt;responsive-card
          title="Card Outline"
          subtitle="Apenas com borda"
          variant="outline"
          badge="Opcional"
          badge-variant="info"
        &gt;
          &lt;p&gt;Este card usa a variante outline, que remove o fundo e a sombra, deixando apenas a borda.&lt;/p&gt;
          
          &lt;template #actions&gt;
            &lt;button class="btn btn-sm btn-outline-primary"&gt;Editar&lt;/button&gt;
            &lt;button class="btn btn-sm btn-outline-danger"&gt;Remover&lt;/button&gt;
          &lt;/template&gt;
        &lt;/responsive-card&gt;
      &lt;/div&gt;
      
      &lt;!-- Card clicável --&gt;
      &lt;div class="col-12 col-md-6 col-lg-4"&gt;
        &lt;responsive-card
          title="Card Clicável"
          subtitle="Clique para mais detalhes"
          image="https://via.placeholder.com/800x450"
          clickable
          @click="handleCardClick"
        &gt;
          &lt;p&gt;Este card inteiro é clicável, funcionando como um link para outra página ou para abrir um modal.&lt;/p&gt;
          
          &lt;template #footer&gt;
            &lt;div class="d-flex align-items-center"&gt;
              &lt;i class="fas fa-info-circle me-2"&gt;&lt;/i&gt; Clique para detalhes
            &lt;/div&gt;
          &lt;/template&gt;
        &lt;/responsive-card&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import ResponsiveCard from '@/components/common/ResponsiveCard.vue';

const handleCardClick = () => {
  alert('Card clicado! Aqui você pode navegar para outra página ou abrir um modal.');
};
&lt;/script&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Componente de Menu Responsivo</h4>
          <pre><code>&lt;template&gt;
  &lt;div class="responsive-menu" :class="{ 'menu-open': isOpen }"&gt;
    &lt;!-- Cabeçalho do menu (visível apenas em mobile) --&gt;
    &lt;div class="menu-header d-md-none"&gt;
      &lt;h5 class="menu-title"&gt;{{ title }}&lt;/h5&gt;
      &lt;button class="menu-close" @click="closeMenu"&gt;
        &lt;i class="fas fa-times"&gt;&lt;/i&gt;
      &lt;/button&gt;
    &lt;/div&gt;
    
    &lt;!-- Conteúdo do menu --&gt;
    &lt;div class="menu-content"&gt;
      &lt;slot&gt;&lt;/slot&gt;
    &lt;/div&gt;
    
    &lt;!-- Rodapé do menu (opcional) --&gt;
    &lt;div v-if="$slots.footer" class="menu-footer"&gt;
      &lt;slot name="footer"&gt;&lt;/slot&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  
  &lt;!-- Overlay para fechar o menu em dispositivos móveis --&gt;
  &lt;div 
    v-if="isOpen" 
    class="menu-overlay d-md-none" 
    @click="closeMenu"
  &gt;&lt;/div&gt;
  
  &lt;!-- Botão para abrir o menu em dispositivos móveis --&gt;
  &lt;button 
    v-if="showToggle" 
    class="menu-toggle d-md-none" 
    :class="toggleButtonClass"
    @click="toggleMenu"
  &gt;
    &lt;i :class="toggleIconClass"&gt;&lt;/i&gt;
    &lt;span v-if="toggleText" class="toggle-text"&gt;{{ toggleText }}&lt;/span&gt;
  &lt;/button&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { useBreakpoints } from '@/composables/useBreakpoints';

const props = defineProps({
  title: {
    type: String,
    default: 'Menu'
  },
  showToggle: {
    type: Boolean,
    default: true
  },
  toggleText: {
    type: String,
    default: ''
  },
  toggleButtonClass: {
    type: String,
    default: 'btn btn-primary'
  },
  toggleIconClass: {
    type: String,
    default: 'fas fa-bars'
  },
  closeOnRouteChange: {
    type: Boolean,
    default: true
  },
  closeOnClickOutside: {
    type: Boolean,
    default: true
  },
  position: {
    type: String,
    default: 'left',
    validator: (value) => ['left', 'right'].includes(value)
  }
});

const emit = defineEmits(['open', 'close']);

// Estado
const isOpen = ref(false);

// Composable para detectar breakpoints
const { isGreaterThan } = useBreakpoints();

// Abrir/fechar menu
const toggleMenu = () => {
  isOpen.value = !isOpen.value;
  
  if (isOpen.value) {
    emit('open');
    document.body.classList.add('menu-open-body');
  } else {
    emit('close');
    document.body.classList.remove('menu-open-body');
  }
};

const openMenu = () => {
  if (!isOpen.value) {
    isOpen.value = true;
    emit('open');
    document.body.classList.add('menu-open-body');
  }
};

const closeMenu = () => {
  if (isOpen.value) {
    isOpen.value = false;
    emit('close');
    document.body.classList.remove('menu-open-body');
  }
};

// Fechar menu quando a rota muda
watch(() => props.closeOnRouteChange, (newValue) => {
  if (newValue) {
    // Aqui você pode adicionar um watcher para a rota atual
    // Exemplo com vue-router:
    // watch(() => router.currentRoute.value.path, () => {
    //   closeMenu();
    // });
  }
});

// Fechar menu quando clicar fora dele
const handleClickOutside = (event) => {
  if (props.closeOnClickOutside && isOpen.value) {
    const menu = document.querySelector('.responsive-menu');
    const toggle = document.querySelector('.menu-toggle');
    
    if (menu && !menu.contains(event.target) && toggle && !toggle.contains(event.target)) {
      closeMenu();
    }
  }
};

// Fechar menu em telas grandes
const handleResize = () => {
  if (isGreaterThan('md') && isOpen.value) {
    closeMenu();
  }
};

// Inicialização
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  window.addEventListener('resize', handleResize);
});

// Limpeza
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('resize', handleResize);
  
  // Garantir que a classe seja removida do body
  document.body.classList.remove('menu-open-body');
});
&lt;/script&gt;

&lt;style scoped&gt;
.responsive-menu {
  background-color: var(--menu-bg, #fff);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--menu-border, #f0f0f0);
}

.menu-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.menu-close {
  background: transparent;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.menu-close:hover {
  background-color: var(--menu-hover, rgba(0, 0, 0, 0.05));
}

.menu-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.menu-footer {
  padding: 1rem;
  border-top: 1px solid var(--menu-border, #f0f0f0);
}

.menu-toggle {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  z-index: 1030;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  border-radius: 50%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.toggle-text {
  margin-left: 0.5rem;
}

.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040;
}

/* Responsividade */
@media (max-width: 767.98px) {
  .responsive-menu {
    position: fixed;
    top: 0;
    width: 80%;
    max-width: 320px;
    z-index: 1050;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .responsive-menu.menu-open {
    transform: translateX(0);
  }
  
  /* Posicionamento */
  .responsive-menu[position="left"] {
    left: 0;
    transform: translateX(-100%);
  }
  
  .responsive-menu[position="right"] {
    right: 0;
    transform: translateX(100%);
  }
  
  .responsive-menu.menu-open[position="left"] {
    transform: translateX(0);
  }
  
  .responsive-menu.menu-open[position="right"] {
    transform: translateX(0);
  }
}

/* Estilo para o body quando o menu está aberto */
:global(.menu-open-body) {
  overflow: hidden;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Uso do Menu Responsivo</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;h3 class="mb-4"&gt;Menu Responsivo&lt;/h3&gt;
    
    &lt;div class="row"&gt;
      &lt;div class="col-md-3"&gt;
        &lt;responsive-menu
          title="Navegação"
          toggle-text="Menu"
          toggle-button-class="btn btn-primary rounded-pill"
          position="left"
        &gt;
          &lt;div class="nav-menu"&gt;
            &lt;div class="nav-header"&gt;Categorias&lt;/div&gt;
            
            &lt;ul class="nav-list"&gt;
              &lt;li class="nav-item"&gt;
                &lt;a href="#" class="nav-link active"&gt;
                  &lt;i class="fas fa-home me-2"&gt;&lt;/i&gt; Início
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li class="nav-item"&gt;
                &lt;a href="#" class="nav-link"&gt;
                  &lt;i class="fas fa-box me-2"&gt;&lt;/i&gt; Produtos
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li class="nav-item"&gt;
                &lt;a href="#" class="nav-link"&gt;
                  &lt;i class="fas fa-tag me-2"&gt;&lt;/i&gt; Ofertas
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li class="nav-item"&gt;
                &lt;a href="#" class="nav-link"&gt;
                  &lt;i class="fas fa-heart me-2"&gt;&lt;/i&gt; Favoritos
                &lt;/a&gt;
              &lt;/li&gt;
            &lt;/ul&gt;
            
            &lt;div class="nav-header"&gt;Minha Conta&lt;/div&gt;
            
            &lt;ul class="nav-list"&gt;
              &lt;li class="nav-item"&gt;
                &lt;a href="#" class="nav-link"&gt;
                  &lt;i class="fas fa-user me-2"&gt;&lt;/i&gt; Perfil
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li class="nav-item"&gt;
                &lt;a href="#" class="nav-link"&gt;
                  &lt;i class="fas fa-shopping-cart me-2"&gt;&lt;/i&gt; Pedidos
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li class="nav-item"&gt;
                &lt;a href="#" class="nav-link"&gt;
                  &lt;i class="fas fa-cog me-2"&gt;&lt;/i&gt; Configurações
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li class="nav-item"&gt;
                &lt;a href="#" class="nav-link text-danger"&gt;
                  &lt;i class="fas fa-sign-out-alt me-2"&gt;&lt;/i&gt; Sair
                &lt;/a&gt;
              &lt;/li&gt;
            &lt;/ul&gt;
          &lt;/div&gt;
          
          &lt;template #footer&gt;
            &lt;div class="d-flex justify-content-center"&gt;
              &lt;button class="btn btn-sm btn-outline-secondary"&gt;
                &lt;i class="fas fa-question-circle me-1"&gt;&lt;/i&gt; Ajuda
              &lt;/button&gt;
            &lt;/div&gt;
          &lt;/template&gt;
        &lt;/responsive-menu&gt;
      &lt;/div&gt;
      
      &lt;div class="col-md-9"&gt;
        &lt;div class="content-area p-4 bg-light rounded"&gt;
          &lt;h4&gt;Conteúdo Principal&lt;/h4&gt;
          &lt;p&gt;Este é o conteúdo principal da página. Em dispositivos móveis, o menu lateral se transforma em um menu deslizante que pode ser aberto através do botão flutuante.&lt;/p&gt;
          &lt;p&gt;Experimente redimensionar a janela para ver como o menu se adapta a diferentes tamanhos de tela.&lt;/p&gt;
          
          &lt;div class="alert alert-info mt-3"&gt;
            &lt;i class="fas fa-info-circle me-2"&gt;&lt;/i&gt; Em dispositivos móveis, o menu é ocultado e pode ser acessado através do botão flutuante no canto inferior direito.
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import ResponsiveMenu from '@/components/common/ResponsiveMenu.vue';
&lt;/script&gt;

&lt;style scoped&gt;
.nav-menu {
  display: flex;
  flex-direction: column;
}

.nav-header {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: #6c757d;
  margin-bottom: 0.5rem;
  padding: 0.5rem 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  color: #495057;
  text-decoration: none;
  transition: all 0.2s;
}

.nav-link:hover {
  background-color: #f8f9fa;
  color: #0d6efd;
}

.nav-link.active {
  background-color: #e9ecef;
  color: #0d6efd;
  font-weight: 500;
}

.content-area {
  min-height: 400px;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Componente de Navbar Responsiva</h4>
          <pre><code>&lt;template&gt;
  &lt;nav class="responsive-navbar" :class="{ 'navbar-expanded': isExpanded }"&gt;
    &lt;div class="container"&gt;
      &lt;div class="navbar-header"&gt;
        &lt;div class="navbar-brand" @click="onBrandClick"&gt;
          &lt;img v-if="logo" :src="logo" :alt="brandName" class="brand-logo"&gt;
          &lt;span v-if="brandName" class="brand-name"&gt;{{ brandName }}&lt;/span&gt;
        &lt;/div&gt;
        
        &lt;button class="navbar-toggler" @click="toggleNavbar" aria-label="Toggle navigation"&gt;
          &lt;i :class="isExpanded ? 'fas fa-times' : 'fas fa-bars'"&gt;&lt;/i&gt;
        &lt;/button&gt;
      &lt;/div&gt;
      
      &lt;div class="navbar-collapse"&gt;
        &lt;ul class="navbar-nav"&gt;
          &lt;slot name="nav-items"&gt;&lt;/slot&gt;
        &lt;/ul&gt;
        
        &lt;div class="navbar-actions"&gt;
          &lt;slot name="actions"&gt;&lt;/slot&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/nav&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const props = defineProps({
  brandName: {
    type: String,
    default: ''
  },
  logo: {
    type: String,
    default: ''
  },
  sticky: {
    type: Boolean,
    default: false
  },
  variant: {
    type: String,
    default: 'light',
    validator: (value) => ['light', 'dark', 'primary', 'transparent'].includes(value)
  }
});

const emit = defineEmits(['brand-click']);

// Estado
const isExpanded = ref(false);

// Alternar menu em dispositivos móveis
const toggleNavbar = () => {
  isExpanded.value = !isExpanded.value;
  
  if (isExpanded.value) {
    document.body.classList.add('navbar-open');
  } else {
    document.body.classList.remove('navbar-open');
  }
};

// Manipulador de clique no brand
const onBrandClick = () => {
  emit('brand-click');
};

// Fechar menu ao clicar em um item
const closeNavbar = () => {
  if (isExpanded.value) {
    isExpanded.value = false;
    document.body.classList.remove('navbar-open');
  }
};

// Expor métodos para o componente pai
defineExpose({
  closeNavbar
});
&lt;/script&gt;

&lt;style scoped&gt;
.responsive-navbar {
  background-color: var(--navbar-bg, #fff);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  padding: 0.75rem 0;
  position: relative;
  z-index: 1030;
}

.responsive-navbar.sticky {
  position: sticky;
  top: 0;
}

.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.navbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.navbar-brand {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.brand-logo {
  height: 32px;
  margin-right: 0.5rem;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--navbar-brand-color, #212529);
}

.navbar-toggler {
  display: none;
  background: transparent;
  border: none;
  width: 40px;
  height: 40px;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--navbar-toggler-color, #212529);
}

.navbar-collapse {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

/* Variantes */
.responsive-navbar.variant-dark {
  background-color: var(--dark, #343a40);
  color: #fff;
}

.responsive-navbar.variant-dark .brand-name,
.responsive-navbar.variant-dark .navbar-toggler {
  color: #fff;
}

.responsive-navbar.variant-primary {
  background-color: var(--primary, #007bff);
  color: #fff;
}

.responsive-navbar.variant-primary .brand-name,
.responsive-navbar.variant-primary .navbar-toggler {
  color: #fff;
}

.responsive-navbar.variant-transparent {
  background-color: transparent;
  box-shadow: none;
}

/* Responsividade */
@media (max-width: 991.98px) {
  .navbar-toggler {
    display: block;
  }
  
  .navbar-collapse {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--navbar-mobile-bg, #fff);
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
    z-index: 1030;
  }
  
  .navbar-expanded .navbar-collapse {
    transform: translateX(0);
  }
  
  .navbar-nav {
    flex-direction: column;
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .navbar-actions {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

/* Estilo para o body quando o navbar está expandido */
:global(.navbar-open) {
  overflow: hidden;
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Componente de Item de Navbar</h4>
          <pre><code>&lt;template&gt;
  &lt;li class="nav-item" :class="{ 'dropdown': hasDropdown, 'active': isActive }"&gt;
    &lt;!-- Link simples --&gt;
    &lt;a 
      v-if="!hasDropdown" 
      :href="href" 
      class="nav-link"
      :class="{ 'active': isActive }"
      @click="handleClick"
    &gt;
      &lt;i v-if="icon" :class="['nav-icon', icon]"&gt;&lt;/i&gt;
      &lt;span class="nav-text"&gt;{{ text }}&lt;/span&gt;
      &lt;span v-if="badge" class="nav-badge" :class="`bg-${badgeVariant}`"&gt;{{ badge }}&lt;/span&gt;
    &lt;/a&gt;
    
    &lt;!-- Dropdown --&gt;
    &lt;template v-else&gt;
      &lt;a 
        href="#" 
        class="nav-link dropdown-toggle"
        :class="{ 'active': isActive, 'show': dropdownOpen }"
        @click.prevent="toggleDropdown"
      &gt;
        &lt;i v-if="icon" :class="['nav-icon', icon]"&gt;&lt;/i&gt;
        &lt;span class="nav-text"&gt;{{ text }}&lt;/span&gt;
        &lt;i class="dropdown-arrow fas fa-chevron-down"&gt;&lt;/i&gt;
      &lt;/a&gt;
      
      &lt;ul class="dropdown-menu" :class="{ 'show': dropdownOpen }"&gt;
        &lt;slot&gt;&lt;/slot&gt;
      &lt;/ul&gt;
    &lt;/template&gt;
  &lt;/li&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  text: {
    type: String,
    required: true
  },
  href: {
    type: String,
    default: '#'
  },
  icon: {
    type: String,
    default: ''
  },
  active: {
    type: Boolean,
    default: false
  },
  badge: {
    type: String,
    default: ''
  },
  badgeVariant: {
    type: String,
    default: 'primary'
  }
});

const emit = defineEmits(['click']);

// Estado
const dropdownOpen = ref(false);

// Verificar se tem dropdown (baseado na presença de slot default)
const hasDropdown = computed(() => !!slots.default);

// Verificar se o item está ativo
const isActive = computed(() => {
  if (props.active) return true;
  
  // Verificar se a URL atual corresponde ao href
  if (typeof window !== 'undefined') {
    return window.location.pathname === props.href;
  }
  
  return false;
});

// Alternar dropdown
const toggleDropdown = () => {
  dropdownOpen.value = !dropdownOpen.value;
};

// Fechar dropdown ao clicar fora
const handleClickOutside = (event) => {
  const navItem = event.target.closest('.nav-item');
  
  if (!navItem || navItem !== el.value) {
    dropdownOpen.value = false;
  }
};

// Manipulador de clique no link
const handleClick = (event) => {
  emit('click', event);
  
  // Se for um link de âncora (#), prevenir comportamento padrão
  if (props.href === '#') {
    event.preventDefault();
  }
};

// Referência ao elemento
const el = ref(null);

// Slots
const slots = useSlots();

// Inicialização
onMounted(() => {
  el.value = document.querySelector('.nav-item');
  document.addEventListener('click', handleClickOutside);
});

// Limpeza
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
&lt;/script&gt;

&lt;style scoped&gt;
.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  color: var(--navbar-link-color, #495057);
  text-decoration: none;
  transition: color 0.2s;
}

.nav-link:hover {
  color: var(--navbar-link-hover-color, #0d6efd);
}

.nav-link.active {
  color: var(--navbar-link-active-color, #0d6efd);
  font-weight: 500;
}

.nav-icon {
  margin-right: 0.5rem;
}

.nav-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 10rem;
  margin-left: 0.5rem;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  cursor: pointer;
}

.dropdown-arrow {
  margin-left: 0.5rem;
  font-size: 0.75rem;
  transition: transform 0.2s;
}

.dropdown-toggle.show .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  background-color: #fff;
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  list-style: none;
}

.dropdown-menu.show {
  display: block;
}

/* Responsividade */
@media (max-width: 991.98px) {
  .nav-link {
    padding: 0.75rem 0;
  }
  
  .dropdown-menu {
    position: static;
    box-shadow: none;
    padding-left: 1.5rem;
    background-color: transparent;
  }
  
  .dropdown-menu.show {
    display: block;
  }
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="code-block">
          <h4>Uso da Navbar Responsiva</h4>
          <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;responsive-navbar 
      brand-name="Minha Aplicação" 
      logo="/assets/images/logo.svg"
      variant="light"
      sticky
      @brand-click="goToHome"
      ref="navbar"
    &gt;
      &lt;template #nav-items&gt;
        &lt;nav-item 
          text="Início" 
          href="/" 
          icon="fas fa-home"
          :active="currentRoute === '/'"
          @click="navigateTo('/')"
        /&gt;
        
        &lt;nav-item 
          text="Produtos" 
          icon="fas fa-box"
          :active="currentRoute.startsWith('/products')"
        &gt;
          &lt;li&gt;&lt;a href="/products" class="dropdown-item"&gt;Todos os Produtos&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="/products/new" class="dropdown-item"&gt;Novidades&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="/products/sale" class="dropdown-item"&gt;Promoções&lt;/a&gt;&lt;/li&gt;
          &lt;li&gt;&lt;hr class="dropdown-divider"&gt;&lt;/li&gt;
          &lt;li&gt;&lt;a href="/products/categories" class="dropdown-item"&gt;Categorias&lt;/a&gt;&lt;/li&gt;
        &lt;/nav-item&gt;
        
        &lt;nav-item 
          text="Sobre" 
          href="/about" 
          icon="fas fa-info-circle"
          :active="currentRoute === '/about'"
          @click="navigateTo('/about')"
        /&gt;
        
        &lt;nav-item 
          text="Contato" 
          href="/contact" 
          icon="fas fa-envelope"
          :active="currentRoute === '/contact'"
          @click="navigateTo('/contact')"
        /&gt;
      &lt;/template&gt;
      
      &lt;template #actions&gt;
        &lt;div class="search-box me-3"&gt;
          &lt;div class="input-group"&gt;
            &lt;input 
              type="text" 
              class="form-control" 
              placeholder="Pesquisar..." 
              v-model="searchQuery"
              @keyup.enter="search"
            &gt;
            &lt;button class="btn btn-outline-secondary" type="button" @click="search"&gt;
              &lt;i class="fas fa-search"&gt;&lt;/i&gt;
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        
        &lt;div class="action-buttons"&gt;
          &lt;button class="btn btn-outline-primary me-2"&gt;
            &lt;i class="fas fa-user me-1"&gt;&lt;/i&gt; Entrar
          &lt;/button&gt;
          
          &lt;button class="btn btn-primary"&gt;
            &lt;i class="fas fa-shopping-cart me-1"&gt;&lt;/i&gt; Carrinho
            &lt;span class="badge bg-light text-dark ms-1"&gt;3&lt;/span&gt;
          &lt;/button&gt;
        &lt;/div&gt;
      &lt;/template&gt;
    &lt;/responsive-navbar&gt;
    
    &lt;div class="content-container p-4"&gt;
      &lt;h2&gt;Conteúdo da Página&lt;/h2&gt;
      &lt;p&gt;Esta é uma demonstração da navbar responsiva. Redimensione a janela para ver como ela se adapta a diferentes tamanhos de tela.&lt;/p&gt;
      
      &lt;div class="alert alert-info"&gt;
        &lt;i class="fas fa-info-circle me-2"&gt;&lt;/i&gt; Em dispositivos móveis, a navbar se transforma em um menu hambúrguer que pode ser expandido.
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';
import ResponsiveNavbar from '@/components/common/ResponsiveNavbar.vue';
import NavItem from '@/components/common/NavItem.vue';

// Estado
const searchQuery = ref('');
const currentRoute = ref('/');
const navbar = ref(null);

// Métodos
const goToHome = () => {
  console.log('Navegando para a página inicial');
  currentRoute.value = '/';
};

const navigateTo = (route) => {
  console.log(`Navegando para: ${route}`);
  currentRoute.value = route;
  
  // Fechar navbar em dispositivos móveis
  if (navbar.value) {
    navbar.value.closeNavbar();
  }
};

const search = () => {
  if (searchQuery.value.trim()) {
    console.log(`Pesquisando por: ${searchQuery.value}`);
    // Implementar lógica de pesquisa
  }
};
&lt;/script&gt;

&lt;style scoped&gt;
.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 2rem;
}

/* Responsividade */
@media (max-width: 991.98px) {
  .search-box {
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .action-buttons {
    display: flex;
    width: 100%;
    justify-content: center;
    gap: 0.5rem;
  }
}
&lt;/style&gt;</code></pre>
        </div>

        <div class="best-practice">
          <h4>Diretrizes para Componentes Responsivos</h4>
          <ul>
            <li>Projete para dispositivos móveis primeiro (mobile-first)
            </li>
            <li>Use breakpoints consistentes em toda a aplicação</li>
            <li>Adapte não apenas o layout, mas também o comportamento
              dos componentes</li>
            <li>Considere diferentes padrões de interação para desktop e
              mobile</li>
            <li>Teste em dispositivos reais, não apenas em emuladores
            </li>
            <li>Otimize imagens e recursos para diferentes tamanhos de
              tela</li>
            <li>Use unidades relativas (rem, em, %) em vez de pixels
              fixos</li>
            <li>Implemente menus alternativos para dispositivos móveis
            </li>
            <li>Considere a acessibilidade em todos os tamanhos de tela
            </li>
            <li>Mantenha a consistência visual em todas as resoluções
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <section id="acessibilidade" class="manual-section">
    <h2>8. Acessibilidade</h2>
    <p>A acessibilidade é essencial para garantir que nossa aplicação possa ser usada por pessoas com diferentes
      habilidades e necessidades. Seguimos as diretrizes WCAG 2.1 para criar uma experiência inclusiva.</p>

    <div id="contraste" class="subsection">
      <h3>8.1. Contraste e Cores</h3>

      <p>O contraste adequado entre texto e fundo é fundamental para garantir a legibilidade para usuários com
        baixa visão ou daltonismo.</p>

      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>Elemento</th>
              <th>Requisito de Contraste</th>
              <th>Exemplo</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Texto normal</td>
              <td>Mínimo 4.5:1</td>
              <td>
                <div style="background-color: #f8f9fa; color: #212529; padding: 0.5rem;">
                  Texto com contraste adequado
                </div>
              </td>
            </tr>
            <tr>
              <td>Texto grande (18pt ou 14pt bold)</td>
              <td>Mínimo 3:1</td>
              <td>
                <div style="background-color: #f8f9fa; color: #495057; padding: 0.5rem; font-size: 18px;">
                  Texto grande com contraste adequado
                </div>
              </td>
            </tr>
            <tr>
              <td>Elementos de interface e gráficos</td>
              <td>Mínimo 3:1 contra adjacentes</td>
              <td>
                <div style="background-color: #f8f9fa; padding: 0.5rem;">
                  <button
                    style="background-color: #0d6efd; color: white; border: none; padding: 0.375rem 0.75rem; border-radius: 0.25rem;">Botão
                    com contraste adequado</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="code-block">
        <h4>Variáveis de Cores Acessíveis</h4>
        <pre><code>// src/assets/scss/_variables.scss

// Cores primárias com contraste adequado
$primary: #0d6efd;      // Azul com contraste adequado contra branco
$secondary: #6c757d;    // Cinza médio
$success: #198754;      // Verde com contraste adequado
$danger: #dc3545;       // Vermelho com contraste adequado
$warning: #ffc107;      // Amarelo (usar texto escuro)
$info: #0dcaf0;         // Ciano (usar texto escuro)
$light: #f8f9fa;        // Cinza claro (usar texto escuro)
$dark: #212529;         // Cinza escuro

// Cores de texto
$body-color: #212529;           // Texto principal
$text-muted: #6c757d;           // Texto secundário
$link-color: #0d6efd;           // Links
$link-hover-color: #0a58ca;     // Links hover

// Garantir que as cores de texto tenham contraste adequado
$input-placeholder-color: #6c757d;
$input-disabled-bg: #e9ecef;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Alternância de Tema</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="theme-toggle"&gt;
    &lt;button 
      class="theme-toggle-button" 
      @click="toggleTheme"
      :aria-label="isDarkTheme ? 'Mudar para tema claro' : 'Mudar para tema escuro'"
    &gt;
      &lt;i :class="['fas', isDarkTheme ? 'fa-sun' : 'fa-moon']"&gt;&lt;/i&gt;
      &lt;span class="visually-hidden"&gt;
        {{ isDarkTheme ? 'Mudar para tema claro' : 'Mudar para tema escuro' }}
      &lt;/span&gt;
    &lt;/button&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';
import { useThemeStore } from '@/stores/themeStore';

const themeStore = useThemeStore();

const isDarkTheme = computed(() => themeStore.isDarkTheme);

const toggleTheme = () => {
  themeStore.toggleTheme();
};
&lt;/script&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Store para Gerenciamento de Tema</h4>
        <pre><code>// src/stores/themeStore.js
import { defineStore } from 'pinia';

export const useThemeStore = defineStore('theme', {
  state: () => ({
    isDarkTheme: false,
    highContrastMode: false
  }),
  
  actions: {
    toggleTheme() {
      this.isDarkTheme = !this.isDarkTheme;
      this.applyTheme();
      localStorage.setItem('darkTheme', this.isDarkTheme);
    },
    
    toggleHighContrast() {
      this.highContrastMode = !this.highContrastMode;
      this.applyTheme();
      localStorage.setItem('highContrast', this.highContrastMode);
    },
    
    initTheme() {
      // Verificar preferência do usuário
      const savedDarkTheme = localStorage.getItem('darkTheme');
      const savedHighContrast = localStorage.getItem('highContrast');
      
      // Verificar preferência do sistema
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      
      // Aplicar tema
      this.isDarkTheme = savedDarkTheme !== null 
        ? savedDarkTheme === 'true' 
        : prefersDark;
        
      this.highContrastMode = savedHighContrast === 'true';
      
      this.applyTheme();
    },
    
    applyTheme() {
      // Aplicar classes ao elemento HTML
      document.documentElement.classList.toggle('dark-theme', this.isDarkTheme);
      document.documentElement.classList.toggle('high-contrast', this.highContrastMode);
      
      // Atualizar meta tag de tema para dispositivos móveis
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute(
          'content', 
          this.isDarkTheme ? '#212529' : '#ffffff'
        );
      }
    }
  }
});</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Contraste e Cores</h4>
        <ul>
          <li>Mantenha uma relação de contraste mínima de 4.5:1 para texto normal</li>
          <li>Use uma relação de contraste mínima de 3:1 para texto grande (18pt ou 14pt bold)</li>
          <li>Garanta que elementos interativos tenham contraste suficiente contra elementos adjacentes</li>
          <li>Não use apenas cor para transmitir informações importantes</li>
          <li>Forneça um modo de alto contraste para usuários com deficiência visual</li>
          <li>Respeite as preferências de tema do sistema do usuário</li>
          <li>Teste suas cores com ferramentas de verificação de contraste</li>
          <li>Considere usuários com daltonismo ao escolher combinações de cores</li>
          <li>Use ícones ou padrões junto com cores para melhorar a compreensão</li>
          <li>Documente suas escolhas de cores e seus valores de contraste</li>
        </ul>
      </div>
    </div>

    <div id="navegacao-teclado" class="subsection">
      <h3>8.2. Navegação por Teclado</h3>

      <p>A navegação por teclado é essencial para usuários que não podem ou preferem não usar um mouse. Todos os
        elementos interativos devem ser acessíveis e operáveis via teclado.</p>

      <div class="code-block">
        <h4>Componente de Botão Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;component
    :is="computedTag"
    class="a11y-button"
    :class="buttonClasses"
    :type="type"
    :disabled="disabled || loading"
    :aria-disabled="disabled || loading"
    :aria-busy="loading"
    :aria-label="ariaLabel"
    :aria-pressed="pressed"
    :aria-expanded="expanded"
    :aria-controls="ariaControls"
    :aria-describedby="ariaDescribedby"
    :href="computedTag === 'a' ? href : undefined"
    :target="computedTag === 'a' ? target : undefined"
    :rel="computedTag === 'a' && target === '_blank' ? 'noopener noreferrer' : undefined"
    @click="handleClick"
    @keydown.space.prevent="handleKeydown"
    @keyup.space.prevent="handleClick"
    @keydown.enter="handleKeydown"
    @keyup.enter="handleClick"
  &gt;
    &lt;span v-if="loading" class="button-loader" aria-hidden="true"&gt;
      &lt;span class="loader-dot"&gt;&lt;/span&gt;
      &lt;span class="loader-dot"&gt;&lt;/span&gt;
      &lt;span class="loader-dot"&gt;&lt;/span&gt;
    &lt;/span&gt;
    
    &lt;span v-if="$slots.icon || icon" class="button-icon" :class="{ 'icon-only': !hasContent }"&gt;
      &lt;slot name="icon"&gt;
        &lt;i :class="icon"&gt;&lt;/i&gt;
      &lt;/slot&gt;
    &lt;/span&gt;
    
    &lt;span v-if="hasContent" class="button-content"&gt;
      &lt;slot&gt;{{ label }}&lt;/slot&gt;
    &lt;/span&gt;
  &lt;/component&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'primary'
  },
  size: {
    type: String,
    default: 'md'
  },
  type: {
    type: String,
    default: 'button'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  icon: {
    type: String,
    default: ''
  },
  href: {
    type: String,
    default: ''
  },
  target: {
    type: String,
    default: ''
  },
  ariaLabel: {
    type: String,
    default: ''
  },
  pressed: {
    type: Boolean,
    default: null
  },
  expanded: {
    type: Boolean,
    default: null
  },
  ariaControls: {
    type: String,
    default: ''
  },
  ariaDescribedby: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['click']);

// Determinar se o botão deve ser um link ou um botão
const computedTag = computed(() => props.href ? 'a' : 'button');

// Verificar se o botão tem conteúdo além do ícone
const hasContent = computed(() => {
  return !!props.label || !!slots.default;
});

// Classes CSS do botão
const buttonClasses = computed(() => {
  return [
    `btn-${props.variant}`,
    `btn-${props.size}`,
    {
      'btn-icon-only': !hasContent.value,
      'btn-loading': props.loading
    }
  ];
});

// Manipuladores de eventos
const handleClick = (event) => {
  if (props.disabled || props.loading) {
    event.preventDefault();
    return;
  }
  
  emit('click', event);
};

const handleKeydown = (event) => {
  // Adicionar feedback visual para navegação por teclado
  if (event.key === 'Enter' || event.key === ' ') {
    event.target.classList.add('keyboard-focus');
  }
};

// Slots
const slots = useSlots();
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, 
              border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Foco visível para navegação por teclado */
.a11y-button:focus-visible {
  outline: 2px solid var(--focus-ring-color, #0d6efd);
  outline-offset: 2px;
}

/* Classe adicionada durante navegação por teclado */
.a11y-button.keyboard-focus {
  outline: 2px solid var(--focus-ring-color, #0d6efd);
  outline-offset: 2px;
}

/* Remover outline padrão do navegador */
.a11y-button:focus:not(:focus-visible):not(.keyboard-focus) {
  outline: none;
}

/* Estilos para botão desabilitado */
.a11y-button[disabled],
.a11y-button[aria-disabled="true"] {
  opacity: 0.65;
  pointer-events: none;
}

/* Estilos para botão com ícone */
.button-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.button-icon:not(.icon-only) {
  margin-right: 0.5rem;
}

/* Estilos para botão apenas com ícone */
.btn-icon-only {
  padding: 0.375rem;
  width: 2.25rem;
  height: 2.25rem;
}

.btn-icon-only.btn-sm {
  padding: 0.25rem;
  width: 1.75rem;
  height: 1.75rem;
}

.btn-icon-only.btn-lg {
  padding: 0.5rem;
  width: 3rem;
  height: 3rem;
}

/* Estilos para botão em carregamento */
.btn-loading {
  color: transparent !important;
}

.button-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
}

.loader-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: currentColor;
  margin: 0 0.15rem;
  animation: loader-dot 1s infinite ease-in-out;
}

.loader-dot:nth-child(1) {
  animation-delay: 0s;
}

.loader-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loader-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loader-dot {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Variantes de tamanho */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

.btn-lg {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.3rem;
}

/* Variantes de cor (exemplo) */
.btn-primary {
  color: #fff;
  background-color: var(--primary, #0d6efd);
  border-color: var(--primary, #0d6efd);
}

.btn-primary:hover {
  background-color: var(--primary-dark, #0b5ed7);
  border-color: var(--primary-darker, #0a58ca);
}

/* Outras variantes seriam definidas de forma semelhante */
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Menu Dropdown Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="a11y-dropdown" :class="{ 'show': isOpen }"&gt;
    &lt;button
      class="dropdown-toggle"
      :class="toggleClass"
      @click="toggle"
      @keydown.esc="close"
      @keydown.down.prevent="focusFirstItem"
      :aria-expanded="isOpen"
      :aria-controls="menuId"
      :id="toggleId"
    &gt;
      &lt;slot name="toggle"&gt;{{ toggleText }}&lt;/slot&gt;
    &lt;/button&gt;
    
    &lt;div
      class="dropdown-menu"
      :class="{ 'show': isOpen, 'dropdown-menu-end': alignRight }"
      :id="menuId"
      role="menu"
      :aria-labelledby="toggleId"
      ref="menuRef"
      @keydown.esc="close"
      @keydown.up.prevent="focusPrevItem"
      @keydown.down.prevent="focusNextItem"
      @keydown.home.prevent="focusFirstItem"
      @keydown.end.prevent="focusLastItem"
    &gt;
      &lt;slot&gt;&lt;/slot&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { useId } from '@/composables/useId';

const props = defineProps({
  toggleText: {
    type: String,
    default: 'Toggle dropdown'
  },
  toggleClass: {
    type: String,
    default: 'btn btn-secondary'
  },
  alignRight: {
    type: Boolean,
    default: false
  },
  closeOnClickOutside: {
    type: Boolean,
    default: true
  },
  closeOnItemClick: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['show', 'hide']);

// Estado
const isOpen = ref(false);
const menuRef = ref(null);

// IDs únicos para acessibilidade
const { id: toggleId } = useId('dropdown-toggle');
const { id: menuId } = useId('dropdown-menu');

// Abrir/fechar dropdown
const toggle = () => {
  isOpen.value = !isOpen.value;
  
  if (isOpen.value) {
    emit('show');
    nextTick(() => {
      focusFirstItem();
    });
  } else {
    emit('hide');
  }
};

const open = () => {
  if (!isOpen.value) {
    isOpen.value = true;
    emit('show');
  }
};

const close = () => {
  if (isOpen.value) {
    isOpen.value = false;
    emit('hide');
  }
};

// Navegação por teclado
const focusFirstItem = () => {
  const items = getMenuItems();
  if (items.length > 0) {
    items[0].focus();
  }
};

const focusLastItem = () => {
  const items = getMenuItems();
  if (items.length > 0) {
    items[items.length - 1].focus();
  }
};

const focusNextItem = () => {
  const items = getMenuItems();
  const currentIndex = items.findIndex(item => item === document.activeElement);
  
  if (currentIndex > -1 && currentIndex < items.length - 1) {
    items[currentIndex + 1].focus();
  } else {
    focusFirstItem();
  }
};

const focusPrevItem = () => {
  const items = getMenuItems();
  const currentIndex = items.findIndex(item => item === document.activeElement);
  
  if (currentIndex > 0) {
    items[currentIndex - 1].focus();
  } else if (currentIndex === 0) {
    focusLastItem();
  } else {
    focusLastItem();
  }
};

// Obter itens do menu
const getMenuItems = () => {
  if (!menuRef.value) return [];
  
  return Array.from(menuRef.value.querySelectorAll(
    'a.dropdown-item:not(.disabled):not([disabled]), ' +
    'button.dropdown-item:not(.disabled):not([disabled])'
  ));
};

// Fechar ao clicar fora
const handleClickOutside = (event) => {
  if (!props.closeOnClickOutside) return;
  
  const dropdown = event.target.closest('.a11y-dropdown');
  if (!dropdown && isOpen.value) {
    close();
  }
};

// Fechar ao clicar em um item
const handleItemClick = (event) => {
  if (!props.closeOnItemClick) return;
  
  const item = event.target.closest('.dropdown-item');
  if (item && isOpen.value) {
    close();
  }
};

// Inicialização
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  document.addEventListener('click', handleItemClick);
});

// Limpeza
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
  document.removeEventListener('click', handleItemClick);
});
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: inline-flex;
  align-items: center;
}

.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

.dropdown-menu.show {
  display: block;
}

.dropdown-menu-end {
  right: 0;
  left: auto;
}

/* Estilos para foco visível */
.dropdown-item:focus-visible {
  outline: 2px solid var(--focus-ring-color, #0d6efd);
  outline-offset: -1px;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Composable para Geração de IDs Únicos</h4>
        <pre><code>// src/composables/useId.js
import { ref } from 'vue';

// Contador para garantir IDs únicos
let idCounter = 0;

export function useId(prefix = 'id') {
  const id = ref(`${prefix}-${++idCounter}`);
  
  return {
    id
  };
}</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Navegação por Teclado</h4>
        <ul>
          <li>Garanta que todos os elementos interativos sejam acessíveis via teclado</li>
          <li>Mantenha uma ordem de tabulação lógica (tab index)</li>
          <li>Forneça indicadores visuais claros para o foco do teclado</li>
          <li>Implemente atalhos de teclado para ações comuns</li>
          <li>Garanta que modais e dropdowns possam ser fechados com a tecla ESC</li>
          <li>Implemente navegação por setas em menus e listas</li>
          <li>Evite armadilhas de foco (exceto em modais)</li>
          <li>Teste a navegação por teclado em todos os componentes</li>
          <li>Use atributos ARIA para melhorar a navegação</li>
          <li>Documente os atalhos de teclado disponíveis</li>
        </ul>
      </div>
    </div>

    <div id="aria" class="subsection">
      <h3>8.3. Atributos ARIA</h3>

      <p>Os atributos ARIA (Accessible Rich Internet Applications) ajudam a tornar o conteúdo web mais acessível
        para pessoas que usam tecnologias assistivas.</p>

      <div class="code-block">
        <h4>Componente de Tabs Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="a11y-tabs"&gt;
    &lt;div class="tabs-header" role="tablist" :aria-label="ariaLabel"&gt;
      &lt;button
        v-for="(tab, index) in tabs"
        :key="tab.id"
        class="tab-button"
        :class="{ 'active': activeTab === index }"
        :id="`tab-${tab.id}`"
        role="tab"
        :aria-selected="activeTab === index"
        :aria-controls="`panel-${tab.id}`"
        :tabindex="activeTab === index ? 0 : -1"
        @click="activateTab(index)"
        @keydown.right.prevent="focusNextTab"
        @keydown.left.prevent="focusPrevTab"
        @keydown.home.prevent="focusFirstTab"
        @keydown.end.prevent="focusLastTab"
      &gt;
        &lt;span v-if="tab.icon" class="tab-icon"&gt;
          &lt;i :class="tab.icon"&gt;&lt;/i&gt;
        &lt;/span&gt;
        {{ tab.title }}
      &lt;/button&gt;
    &lt;/div&gt;
    
    &lt;div class="tabs-content"&gt;
      &lt;div
        v-for="(tab, index) in tabs"
        :key="tab.id"
        class="tab-panel"
        :class="{ 'active': activeTab === index }"
        :id="`panel-${tab.id}`"
        role="tabpanel"
        :aria-labelledby="`tab-${tab.id}`"
        :tabindex="0"
        :hidden="activeTab !== index"
      &gt;
        &lt;slot :name="tab.id" :tab="tab"&gt;
          &lt;p&gt;Conteúdo da aba {{ tab.title }}&lt;/p&gt;
        &lt;/slot&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, watch, nextTick } from 'vue';

const props = defineProps({
  tabs: {
    type: Array,
    required: true,
    validator: (tabs) => {
      return tabs.every(tab => tab.id && tab.title);
    }
  },
  initialTab: {
    type: [Number, String],
    default: 0
  },
  ariaLabel: {
    type: String,
    default: 'Abas de navegação'
  }
});

const emit = defineEmits(['tab-change']);

// Estado
const activeTab = ref(0);
const tabButtons = ref([]);

// Ativar aba
const activateTab = (index) => {
  activeTab.value = index;
  emit('tab-change', { index, tab: props.tabs[index] });
  
  // Focar no botão da aba ativa
  nextTick(() => {
    tabButtons.value[index].focus();
  });
};

// Navegação por teclado
const focusNextTab = () => {
  const nextIndex = activeTab.value < props.tabs.length - 1 ? activeTab.value + 1 : 0;
  activateTab(nextIndex);
};

const focusPrevTab = () => {
  const prevIndex = activeTab.value > 0 ? activeTab.value - 1 : props.tabs.length - 1;
  activateTab(prevIndex);
};

const focusFirstTab = () => {
  activateTab(0);
};

const focusLastTab = () => {
  activateTab(props.tabs.length - 1);
};

// Inicialização
onMounted(() => {
  // Obter referências aos botões de aba
  tabButtons.value = Array.from(document.querySelectorAll('.tab-button'));
  
  // Definir aba inicial
  if (typeof props.initialTab === 'number') {
    activeTab.value = Math.min(Math.max(props.initialTab, 0), props.tabs.length - 1);
  } else {
    // Se initialTab for uma string (ID), encontrar o índice correspondente
    const index = props.tabs.findIndex(tab => tab.id === props.initialTab);
    activeTab.value = index >= 0 ? index : 0;
  }
});
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-tabs {
  width: 100%;
}

.tabs-header {
  display: flex;
  border-bottom: 1px solid #dee2e6;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  font-weight: 500;
  color: #6c757d;
  display: flex;
  align-items: center;
}

.tab-button:hover {
  color: #495057;
  border-bottom-color: #dee2e6;
}

.tab-button.active {
  color: #0d6efd;
  border-bottom-color: #0d6efd;
}

.tab-button:focus-visible {
  outline: 2px solid #0d6efd;
  outline-offset: -2px;
  border-radius: 0.25rem 0.25rem 0 0;
}

.tab-icon {
  margin-right: 0.5rem;
}

.tabs-content {
  padding: 1rem 0;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
}

/* Responsividade */
@media (max-width: 767.98px) {
  .tabs-header {
    flex-wrap: wrap;
  }
  
  .tab-button {
    flex: 1 0 auto;
    text-align: center;
    padding: 0.5rem;
  }
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Accordion Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="a11y-accordion"&gt;
    &lt;div 
      v-for="(item, index) in items" 
      :key="item.id"
      class="accordion-item"
    &gt;
      &lt;h3 class="accordion-header"&gt;
        &lt;button
          class="accordion-button"
          :class="{ 'collapsed': !isItemOpen(index) }"
          :id="`accordion-header-${item.id}`"
          :aria-expanded="isItemOpen(index)"
          :aria-controls="`accordion-panel-${item.id}`"
          @click="toggleItem(index)"
        &gt;
          {{ item.title }}
        &lt;/button&gt;
      &lt;/h3&gt;
      
      &lt;div
        :id="`accordion-panel-${item.id}`"
        class="accordion-collapse"
        :class="{ 'show': isItemOpen(index) }"
        role="region"
        :aria-labelledby="`accordion-header-${item.id}`"
        v-show="isItemOpen(index)"
      &gt;
        &lt;div class="accordion-body"&gt;
          &lt;slot :name="item.id" :item="item"&gt;
            {{ item.content }}
          &lt;/slot&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';

const props = defineProps({
  items: {
    type: Array,
    required: true,
    validator: (items) => {
      return items.every(item => item.id && item.title);
    }
  },
  multiple: {
    type: Boolean,
    default: false
  },
  initialOpen: {
    type: [Number, Array, String],
    default: () => []
  }
});

const emit = defineEmits(['item-toggle']);

// Estado
const openItems = ref([]);

// Inicializar itens abertos
if (Array.isArray(props.initialOpen)) {
  openItems.value = [...props.initialOpen];
} else if (typeof props.initialOpen === 'number') {
  openItems.value = [props.initialOpen];
} else if (typeof props.initialOpen === 'string') {
  const index = props.items.findIndex(item => item.id === props.initialOpen);
  if (index !== -1) {
    openItems.value = [index];
  }
}

// Verificar se um item está aberto
const isItemOpen = (index) => {
  return openItems.value.includes(index);
};

// Alternar estado de um item
const toggleItem = (index) => {
  if (isItemOpen(index)) {
    // Fechar item
    openItems.value = openItems.value.filter(i => i !== index);
  } else {
    // Abrir item
    if (props.multiple) {
      openItems.value = [...openItems.value, index];
    } else {
      openItems.value = [index];
    }
  }
  
  emit('item-toggle', {
    index,
    item: props.items[index],
    isOpen: isItemOpen(index)
  });
};
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-accordion {
  width: 100%;
}

.accordion-item {
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
  overflow: hidden;
}

.accordion-header {
  margin: 0;
}

.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 1rem 1.25rem;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  background-color: #f8f9fa;
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
  cursor: pointer;
}

.accordion-button:not(.collapsed) {
  color: #0c63e4;
  background-color: #e7f1ff;
}

.accordion-button::after {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  margin-left: auto;
  content: "";
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: 1.25rem;
  transition: transform 0.2s ease-in-out;
}

.accordion-button.collapsed::after {
  transform: rotate(-90deg);
}

.accordion-button:focus {
  z-index: 3;
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.accordion-collapse {
  transition: height 0.35s ease;
}

.accordion-body {
  padding: 1rem 1.25rem;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Modal Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;teleport to="body"&gt;
    &lt;transition name="modal-fade"&gt;
      &lt;div 
        v-if="modelValue" 
        class="a11y-modal-backdrop"
        @click="handleBackdropClick"
      &gt;
        &lt;div 
          class="a11y-modal-dialog"
          :class="[sizeClass, { 'modal-centered': centered }]"
          role="dialog"
          :aria-labelledby="titleId"
          :aria-describedby="bodyId"
          aria-modal="true"
          @click.stop
          ref="modalRef"
        &gt;
          &lt;div class="a11y-modal-content"&gt;
            &lt;div class="a11y-modal-header"&gt;
              &lt;h3 :id="titleId" class="a11y-modal-title"&gt;
                &lt;slot name="title"&gt;{{ title }}&lt;/slot&gt;
              &lt;/h3&gt;
              
              &lt;button 
                type="button" 
                class="a11y-modal-close" 
                aria-label="Fechar"
                @click="close"
              &gt;
                &lt;span aria-hidden="true"&gt;&times;&lt;/span&gt;
              &lt;/button&gt;
            &lt;/div&gt;
            
            &lt;div :id="bodyId" class="a11y-modal-body"&gt;
              &lt;slot&gt;&lt;/slot&gt;
            &lt;/div&gt;
            
            &lt;div v-if="$slots.footer" class="a11y-modal-footer"&gt;
              &lt;slot name="footer"&gt;&lt;/slot&gt;
            &lt;/div&gt;
            
            &lt;div v-else-if="!hideDefaultFooter" class="a11y-modal-footer"&gt;
              &lt;button 
                type="button" 
                class="btn btn-secondary" 
                @click="close"
              &gt;
                {{ cancelText }}
              &lt;/button&gt;
              
              &lt;button 
                type="button" 
                class="btn btn-primary" 
                @click="confirm"
              &gt;
                {{ okText }}
              &lt;/button&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/transition&gt;
  &lt;/teleport&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useId } from '@/composables/useId';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Modal'
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
  },
  centered: {
    type: Boolean,
    default: false
  },
  closeOnEsc: {
    type: Boolean,
    default: true
  },
  closeOnBackdrop: {
    type: Boolean,
    default: true
  },
  hideDefaultFooter: {
    type: Boolean,
    default: false
  },
  okText: {
    type: String,
    default: 'OK'
  },
  cancelText: {
    type: String,
    default: 'Cancelar'
  },
  preventScroll: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'ok', 'cancel', 'close']);

// Estado
const modalRef = ref(null);
const previouslyFocusedElement = ref(null);

// IDs únicos para acessibilidade
const { id: titleId } = useId('modal-title');
const { id: bodyId } = useId('modal-body');

// Classes CSS
const sizeClass = computed(() => `modal-${props.size}`);

// Métodos
const close = () => {
  emit('update:modelValue', false);
  emit('close');
  emit('cancel');
};

const confirm = () => {
  emit('ok');
  close();
};

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    close();
  }
};

const handleEscKey = (event) => {
  if (props.closeOnEsc && props.modelValue && event.key === 'Escape') {
    close();
  }
};

// Gerenciar foco e armadilha de foco
const focusFirstElement = () => {
  nextTick(() => {
    if (!modalRef.value) return;
    
    // Salvar elemento atualmente focado
    previouslyFocusedElement.value = document.activeElement;
    
    // Encontrar elementos focáveis dentro do modal
    const focusableElements = modalRef.value.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    } else {
      // Se não houver elementos focáveis, focar no próprio modal
      modalRef.value.focus();
    }
  });
};

const handleTabKey = (event) => {
  if (!modalRef.value || !props.modelValue) return;
  
  const focusableElements = modalRef.value.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  if (focusableElements.length === 0) return;
  
  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];
  
  // Se pressionou Shift+Tab e está no primeiro elemento, mover para o último
  if (event.shiftKey && document.activeElement === firstElement) {
    event.preventDefault();
    lastElement.focus();
  } 
  // Se pressionou Tab e está no último elemento, mover para o primeiro
  else if (!event.shiftKey && document.activeElement === lastElement) {
    event.preventDefault();
    firstElement.focus();
  }
};

// Gerenciar scroll do body
const preventBodyScroll = () => {
  if (props.preventScroll) {
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
    document.body.style.overflow = 'hidden';
    document.body.style.paddingRight = `${scrollbarWidth}px`;
  }
};

const restoreBodyScroll = () => {
  if (props.preventScroll) {
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
  }
};

// Observar mudanças no modelValue
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    preventBodyScroll();
    focusFirstElement();
  } else {
    restoreBodyScroll();
    
    // Restaurar foco para o elemento anterior
    if (previouslyFocusedElement.value) {
      previouslyFocusedElement.value.focus();
    }
  }
});

// Inicialização
onMounted(() => {
  document.addEventListener('keydown', handleEscKey);
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
      handleTabKey(e);
    }
  });
});

// Limpeza
onUnmounted(() => {
  document.removeEventListener('keydown', handleEscKey);
  document.removeEventListener('keydown', handleTabKey);
  restoreBodyScroll();
});
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.a11y-modal-dialog {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 1.75rem auto;
  pointer-events: none;
}

.modal-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 3.5rem);
}

.modal-sm {
  max-width: 300px;
}

.modal-md {
  max-width: 500px;
}

.modal-lg {
  max-width: 800px;
}

.modal-xl {
  max-width: 1140px;
}

.a11y-modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.a11y-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.a11y-modal-title {
  margin: 0;
  font-size: 1.25rem;
  line-height: 1.5;
}

.a11y-modal-close {
  padding: 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
  background-color: transparent;
  border: 0;
  font-size: 1.5rem;
  cursor: pointer;
  color: #000;
  opacity: 0.5;
  transition: opacity 0.15s;
}

.a11y-modal-close:hover {
  opacity: 1;
}

.a11y-modal-close:focus {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}

.a11y-modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.a11y-modal-footer {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
  gap: 0.5rem;
}

/* Animações */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

/* Responsividade */
@media (max-width: 575.98px) {
  .a11y-modal-dialog {
    margin: 0.5rem;
  }
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Tooltip Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="a11y-tooltip-container" ref="container"&gt;
    &lt;div 
      ref="triggerRef"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown.escape="hide"
      aria-describedby="tooltip"
    &gt;
      &lt;slot&gt;&lt;/slot&gt;
    &lt;/div&gt;
    
    &lt;teleport to="body"&gt;
      &lt;transition name="tooltip-fade"&gt;
        &lt;div 
          v-if="isVisible"
          :id="tooltipId"
          role="tooltip"
          class="a11y-tooltip"
          :class="[`tooltip-${placement}`, { 'tooltip-dark': dark }]"
          :style="tooltipStyle"
          ref="tooltipRef"
        &gt;
          &lt;div class="tooltip-arrow" :style="arrowStyle"&gt;&lt;/div&gt;
          &lt;div class="tooltip-content"&gt;{{ content }}&lt;/div&gt;
        &lt;/div&gt;
      &lt;/transition&gt;
    &lt;/teleport&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue';
import { useId } from '@/composables/useId';
import { computePosition, autoUpdate, offset, flip, shift, arrow } from '@floating-ui/dom';

const props = defineProps({
  content: {
    type: String,
    required: true
  },
  placement: {
    type: String,
    default: 'top',
    validator: (value) => ['top', 'right', 'bottom', 'left'].includes(value)
  },
  trigger: {
    type: String,
    default: 'hover',
    validator: (value) => ['hover', 'focus', 'both'].includes(value)
  },
  delay: {
    type: Number,
    default: 200
  },
  dark: {
    type: Boolean,
    default: false
  }
});

// Estado
const isVisible = ref(false);
const container = ref(null);
const triggerRef = ref(null);
const tooltipRef = ref(null);
const arrowRef = ref(null);
const tooltipStyle = ref({});
const arrowStyle = ref({});
const showTimeout = ref(null);
const hideTimeout = ref(null);
const cleanup = ref(null);

// ID único para acessibilidade
const { id: tooltipId } = useId('tooltip');

// Mostrar tooltip
const show = () => {
  clearTimeout(hideTimeout.value);
  
  showTimeout.value = setTimeout(() => {
    isVisible.value = true;
    
    // Configurar posicionamento após o tooltip ser renderizado
    nextTick(() => {
      updatePosition();
    });
  }, props.delay);
};

// Esconder tooltip
const hide = () => {
  clearTimeout(showTimeout.value);
  
  hideTimeout.value = setTimeout(() => {
    isVisible.value = false;
    
    // Limpar função de atualização automática
    if (cleanup.value) {
      cleanup.value();
      cleanup.value = null;
    }
  }, props.delay);
};

// Atualizar posição do tooltip
const updatePosition = () => {
  if (!triggerRef.value || !tooltipRef.value) return;
  
  // Limpar função de atualização automática anterior
  if (cleanup.value) {
    cleanup.value();
  }
  
  // Configurar atualização automática de posição
  cleanup.value = autoUpdate(
    triggerRef.value,
    tooltipRef.value,
    () => {
      computePosition(triggerRef.value, tooltipRef.value, {
        placement: props.placement,
        middleware: [
          offset(8),
          flip(),
          shift({ padding: 5 }),
          arrow({ element: arrowRef.value })
        ]
      }).then(({ x, y, placement, middlewareData }) => {
        // Posicionar tooltip
        tooltipStyle.value = {
          left: `${x}px`,
          top: `${y}px`
        };
        
        // Posicionar seta
        if (middlewareData.arrow) {
          const { x: arrowX, y: arrowY } = middlewareData.arrow;
          
          const staticSide = {
            top: 'bottom',
            right: 'left',
            bottom: 'top',
            left: 'right'
          }[placement.split('-')[0]];
          
          arrowStyle.value = {
            left: arrowX != null ? `${arrowX}px` : '',
            top: arrowY != null ? `${arrowY}px` : '',
            [staticSide]: '-4px'
          };
        }
      });
    }
  );
};

// Manipuladores de eventos
const handleMouseEnter = () => {
  if (props.trigger === 'hover' || props.trigger === 'both') {
    show();
  }
};

const handleMouseLeave = () => {
  if (props.trigger === 'hover' || props.trigger === 'both') {
    hide();
  }
};

const handleFocus = () => {
  if (props.trigger === 'focus' || props.trigger === 'both') {
    show();
  }
};

const handleBlur = () => {
  if (props.trigger === 'focus' || props.trigger === 'both') {
    hide();
  }
};

// Limpeza
onUnmounted(() => {
  clearTimeout(showTimeout.value);
  clearTimeout(hideTimeout.value);
  
  if (cleanup.value) {
    cleanup.value();
  }
});
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-tooltip-container {
  display: inline-block;
}

.a11y-tooltip {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1070;
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
  pointer-events: none;
}

.tooltip-dark {
  background-color: #212529;
}

.tooltip-content {
  position: relative;
  z-index: 1;
}

.tooltip-arrow {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
  visibility: hidden;
}

.tooltip-arrow::before {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
  visibility: visible;
  content: '';
  transform: rotate(45deg);
}

/* Animações */
.tooltip-fade-enter-active,
.tooltip-fade-leave-active {
  transition: opacity 0.2s ease;
}

.tooltip-fade-enter-from,
.tooltip-fade-leave-to {
  opacity: 0;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Uso de Atributos ARIA em Formulários</h4>
        <pre><code>&lt;template&gt;
  &lt;form @submit.prevent="submitForm" novalidate&gt;
    &lt;div class="form-group"&gt;
      &lt;label :for="emailId" class="form-label"&gt;Email&lt;/label&gt;
      &lt;input
        :id="emailId"
        type="email"
        class="form-control"
        :class="{ 'is-invalid': emailError }"
        v-model="email"
        aria-required="true"
        :aria-describedby="emailError ? emailErrorId : emailHelpId"
        :aria-invalid="!!emailError"
      &gt;
      &lt;div :id="emailHelpId" class="form-text" v-if="!emailError"&gt;
        Insira seu endereço de email.
      &lt;/div&gt;
      &lt;div :id="emailErrorId" class="invalid-feedback" v-if="emailError"&gt;
        {{ emailError }}
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="form-group"&gt;
      &lt;label :for="passwordId" class="form-label"&gt;Senha&lt;/label&gt;
      &lt;div class="input-group"&gt;
        &lt;input
          :id="passwordId"
          :type="showPassword ? 'text' : 'password'"
          class="form-control"
          :class="{ 'is-invalid': passwordError }"
          v-model="password"
          aria-required="true"
          :aria-describedby="passwordError ? passwordErrorId : passwordHelpId"
          :aria-invalid="!!passwordError"
        &gt;
        &lt;button 
          type="button" 
          class="btn btn-outline-secondary"
          @click="togglePasswordVisibility"
          :aria-label="showPassword ? 'Ocultar senha' : 'Mostrar senha'"
          :aria-pressed="showPassword"
        &gt;
          &lt;i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"&gt;&lt;/i&gt;
        &lt;/button&gt;
      &lt;/div&gt;
      &lt;div :id="passwordHelpId" class="form-text" v-if="!passwordError"&gt;
        A senha deve ter pelo menos 8 caracteres.
      &lt;/div&gt;
      &lt;div :id="passwordErrorId" class="invalid-feedback" v-if="passwordError"&gt;
        {{ passwordError }}
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="form-group form-check"&gt;
      &lt;input 
        :id="rememberMeId" 
        type="checkbox" 
        class="form-check-input" 
        v-model="rememberMe"
      &gt;
      &lt;label :for="rememberMeId" class="form-check-label"&gt;
        Lembrar-me
      &lt;/label&gt;
    &lt;/div&gt;
    
    &lt;div class="form-group" role="status" aria-live="polite"&gt;
      &lt;div v-if="formError" class="alert alert-danger"&gt;
        {{ formError }}
      &lt;/div&gt;
      &lt;div v-if="formSuccess" class="alert alert-success"&gt;
        {{ formSuccess }}
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;button 
      type="submit" 
      class="btn btn-primary"
      :disabled="isSubmitting"
      aria-busy="isSubmitting"
    &gt;
      &lt;span v-if="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"&gt;&lt;/span&gt;
      {{ isSubmitting ? 'Enviando...' : 'Entrar' }}
    &lt;/button&gt;
  &lt;/form&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';
import { useId } from '@/composables/useId';

// Estado do formulário
const email = ref('');
const password = ref('');
const rememberMe = ref(false);
const showPassword = ref(false);
const isSubmitting = ref(false);

// Erros
const emailError = ref('');
const passwordError = ref('');
const formError = ref('');
const formSuccess = ref('');

// IDs únicos para acessibilidade
const { id: emailId } = useId('email');
const { id: emailHelpId } = useId('email-help');
const { id: emailErrorId } = useId('email-error');
const { id: passwordId } = useId('password');
const { id: passwordHelpId } = useId('password-help');
const { id: passwordErrorId } = useId('password-error');
const { id: rememberMeId } = useId('remember-me');

// Alternar visibilidade da senha
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// Validar formulário
const validateForm = () => {
  let isValid = true;
  
  // Validar email
  if (!email.value) {
    emailError.value = 'Email é obrigatório';
    isValid = false;
  } else if (!/\S+@\S+\.\S+/.test(email.value)) {
    emailError.value = 'Email inválido';
    isValid = false;
  } else {
    emailError.value = '';
  }
  
  // Validar senha
  if (!password.value) {
    passwordError.value = 'Senha é obrigatória';
    isValid = false;
  } else if (password.value.length < 8) {
    passwordError.value = 'A senha deve ter pelo menos 8 caracteres';
    isValid = false;
  } else {
    passwordError.value = '';
  }
  
  return isValid;
};

// Enviar formulário
const submitForm = async () => {
  formError.value = '';
  formSuccess.value = '';
  
  if (!validateForm()) {
    formError.value = 'Por favor, corrija os erros no formulário';
    return;
  }
  
  try {
    isSubmitting.value = true;
    
    // Simulação de envio
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    formSuccess.value = 'Login realizado com sucesso!';
    
    // Limpar formulário
    email.value = '';
    password.value = '';
    rememberMe.value = false;
  } catch (error) {
    formError.value = 'Ocorreu um erro ao processar o login. Tente novamente.';
  } finally {
    isSubmitting.value = false;
  }
};
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Uso de Atributos ARIA</h4>
        <ul>
          <li>Use atributos ARIA apenas quando necessário - HTML semântico é preferível</li>
          <li>Mantenha os atributos ARIA atualizados com o estado da aplicação</li>
          <li>Use <code>aria-live</code> para anunciar atualizações dinâmicas</li>
          <li>Forneça rótulos descritivos com <code>aria-label</code> ou <code>aria-labelledby</code></li>
          <li>Use <code>aria-describedby</code> para associar descrições a elementos</li>
          <li>Indique estados com <code>aria-expanded</code>, <code>aria-pressed</code>,
            <code>aria-selected</code>
          </li>
          <li>Marque campos obrigatórios com <code>aria-required="true"</code></li>
          <li>Indique erros de validação com <code>aria-invalid="true"</code></li>
          <li>Use <code>role</code> para esclarecer a função de elementos não semânticos</li>
          <li>Teste com leitores de tela para garantir que os atributos ARIA funcionem corretamente</li>
        </ul>
      </div>

      <div class="code-block">
        <h4>Tabela de Atributos ARIA Comuns</h4>
        <table class="table">
          <thead>
            <tr>
              <th>Atributo</th>
              <th>Descrição</th>
              <th>Exemplo</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>role</code></td>
              <td>Define a função do elemento</td>
              <td><code>&lt;div role="button"&gt;Clique aqui&lt;/div&gt;</code></td>
            </tr>
            <tr>
              <td><code>aria-label</code></td>
              <td>Fornece um rótulo acessível</td>
              <td><code>&lt;button aria-label="Fechar"&gt;X&lt;/button&gt;</code></td>
            </tr>
            <tr>
              <td><code>aria-labelledby</code></td>
              <td>Associa um elemento a outro que serve como seu rótulo</td>
              <td>
                <code>&lt;div id="title"&gt;Título&lt;/div&gt;<br>&lt;section aria-labelledby="title"&gt;...&lt;/section&gt;</code>
              </td>
            </tr>
            <tr>
              <td><code>aria-describedby</code></td>
              <td>Associa um elemento a outro que fornece uma descrição</td>
              <td>
                <code>&lt;input aria-describedby="help-text"&gt;<br>&lt;div id="help-text"&gt;Instruções&lt;/div&gt;</code>
              </td>
            </tr>
            <tr>
              <td><code>aria-expanded</code></td>
              <td>Indica se um elemento expansível está expandido</td>
              <td><code>&lt;button aria-expanded="false"&gt;Expandir&lt;/button&gt;</code></td>
            </tr>
            <tr>
              <td><code>aria-hidden</code></td>
              <td>Oculta elementos decorativos de tecnologias assistivas</td>
              <td><code>&lt;div aria-hidden="true"&gt;Decorativo&lt;/div&gt;</code></td>
            </tr>
            <tr>
              <td><code>aria-live</code></td>
              <td>Define como as atualizações devem ser anunciadas</td>
              <td><code>&lt;div aria-live="polite"&gt;Mensagem&lt;/div&gt;</code></td>
            </tr>
            <tr>
              <td><code>aria-required</code></td>
              <td>Indica que um campo é obrigatório</td>
              <td><code>&lt;input aria-required="true"&gt;</code></td>
            </tr>
            <tr>
              <td><code>aria-invalid</code></td>
              <td>Indica que um campo tem um valor inválido</td>
              <td><code>&lt;input aria-invalid="true"&gt;</code></td>
            </tr>
            <tr>
              <td><code>aria-controls</code></td>
              <td>Associa um elemento ao que ele controla</td>
              <td>
                <code>&lt;button aria-controls="panel1"&gt;Abrir&lt;/button&gt;<br>&lt;div id="panel1"&gt;...&lt;/div&gt;</code>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div id="textos-alternativos" class="subsection">
      <h3>8.4. Textos Alternativos e Descrições</h3>

      <p>Textos alternativos e descrições são essenciais para garantir que usuários com deficiência visual possam
        compreender o conteúdo não textual.</p>

      <div class="code-block">
        <h4>Componente de Imagem Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;figure class="a11y-image" :class="figureClass"&gt;
    &lt;img
      :src="src"
      :alt="alt"
      :width="width"
      :height="height"
      :class="imgClass"
      :loading="loading"
      @error="handleError"
    &gt;
    
    &lt;figcaption v-if="caption || $slots.caption" class="image-caption"&gt;
      &lt;slot name="caption"&gt;{{ caption }}&lt;/slot&gt;
    &lt;/figcaption&gt;
    
    &lt;div v-if="longDescription" class="visually-hidden"&gt;
      {{ longDescription }}
    &lt;/div&gt;
  &lt;/figure&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  alt: {
    type: String,
    required: true
  },
  width: {
    type: [Number, String],
    default: null
  },
  height: {
    type: [Number, String],
    default: null
  },
  caption: {
    type: String,
    default: ''
  },
  longDescription: {
    type: String,
    default: ''
  },
  figureClass: {
    type: String,
    default: ''
  },
  imgClass: {
    type: String,
    default: ''
  },
  loading: {
    type: String,
    default: 'lazy',
    validator: (value) => ['eager', 'lazy'].includes(value)
  }
});

const emit = defineEmits(['error']);

const handleError = (event) => {
  emit('error', event);
};
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-image {
  margin: 0;
  max-width: 100%;
}

.a11y-image img {
  max-width: 100%;
  height: auto;
}

.image-caption {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Ícone Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;span 
    class="a11y-icon" 
    :class="iconClass"
    :aria-hidden="!label"
  &gt;
    &lt;i :class="name"&gt;&lt;/i&gt;
    &lt;span v-if="label" class="visually-hidden"&gt;{{ label }}&lt;/span&gt;
  &lt;/span&gt;
&lt;/template&gt;

&lt;script setup&gt;
const props = defineProps({
  name: {
    type: String,
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
  },
  color: {
    type: String,
    default: ''
  }
});

const iconClass = computed(() => {
  return [
    `icon-${props.size}`,
    props.color ? `text-${props.color}` : ''
  ];
});
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-sm {
  font-size: 0.875rem;
}

.icon-md {
  font-size: 1rem;
}

.icon-lg {
  font-size: 1.25rem;
}

.icon-xl {
  font-size: 1.5rem;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Gráfico Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="a11y-chart"&gt;
    &lt;h3 :id="titleId" class="chart-title"&gt;{{ title }}&lt;/h3&gt;
    
    &lt;div v-if="description" :id="descriptionId" class="chart-description"&gt;
      {{ description }}
    &lt;/div&gt;
    
    &lt;div 
      class="chart-container" 
      :aria-labelledby="titleId"
      :aria-describedby="description ? descriptionId : undefined"
    &gt;
      &lt;canvas ref="chartCanvas"&gt;&lt;/canvas&gt;
    &lt;/div&gt;
    
    &lt;div v-if="showTable" class="chart-table-container"&gt;
      &lt;h4 class="visually-hidden"&gt;{{ title }} - Dados em formato de tabela&lt;/h4&gt;
      &lt;table class="chart-table"&gt;
        &lt;caption v-if="tableCaption"&gt;{{ tableCaption }}&lt;/caption&gt;
        &lt;thead&gt;
          &lt;tr&gt;
            &lt;th scope="col"&gt;{{ labelColumn }}&lt;/th&gt;
            &lt;template v-for="(dataset, index) in chartData.datasets" :key="index"&gt;
              &lt;th scope="col"&gt;{{ dataset.label }}&lt;/th&gt;
            &lt;/template&gt;
          &lt;/tr&gt;
        &lt;/thead&gt;
        &lt;tbody&gt;
          &lt;tr v-for="(label, labelIndex) in chartData.labels" :key="labelIndex"&gt;
            &lt;th scope="row"&gt;{{ label }}&lt;/th&gt;
            &lt;template v-for="(dataset, datasetIndex) in chartData.datasets" :key="datasetIndex"&gt;
              &lt;td&gt;{{ dataset.data[labelIndex] }}&lt;/td&gt;
            &lt;/template&gt;
          &lt;/tr&gt;
        &lt;/tbody&gt;
      &lt;/table&gt;
    &lt;/div&gt;
    
    &lt;div v-if="showSummary" class="chart-summary" aria-live="polite"&gt;
      &lt;h4&gt;Resumo dos dados&lt;/h4&gt;
      &lt;p&gt;{{ summary }}&lt;/p&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, computed, watch } from 'vue';
import { Chart, registerables } from 'chart.js';
import { useId } from '@/composables/useId';

// Registrar componentes do Chart.js
Chart.register(...registerables);

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  chartData: {
    type: Object,
    required: true
  },
  chartType: {
    type: String,
    default: 'bar',
    validator: (value) => ['bar', 'line', 'pie', 'doughnut', 'radar'].includes(value)
  },
  chartOptions: {
    type: Object,
    default: () => ({})
  },
  showTable: {
    type: Boolean,
    default: true
  },
  tableCaption: {
    type: String,
    default: ''
  },
  labelColumn: {
    type: String,
    default: 'Categoria'
  },
  showSummary: {
    type: Boolean,
    default: true
  },
  summary: {
    type: String,
    default: ''
  }
});

// Referências
const chartCanvas = ref(null);
const chart = ref(null);

// IDs únicos para acessibilidade
const { id: titleId } = useId('chart-title');
const { id: descriptionId } = useId('chart-description');

// Inicializar gráfico
const initChart = () => {
  if (!chartCanvas.value) return;
  
  // Destruir gráfico existente se houver
  if (chart.value) {
    chart.value.destroy();
  }
  
  // Criar novo gráfico
  chart.value = new Chart(chartCanvas.value, {
    type: props.chartType,
    data: props.chartData,
    options: {
      ...props.chartOptions,
      // Opções padrão para acessibilidade
      plugins: {
        ...props.chartOptions.plugins,
        tooltip: {
          ...props.chartOptions.plugins?.tooltip,
          enabled: true,
          mode: 'index',
          intersect: false
        },
        legend: {
          ...props.chartOptions.plugins?.legend,
          display: true,
          labels: {
            ...props.chartOptions.plugins?.legend?.labels,
            generateLabels: (chart) => {
              const defaultLabels = Chart.defaults.plugins.legend.labels.generateLabels(chart);
              return defaultLabels.map(label => {
                return {
                  ...label,
                  text: `${label.text} (${label.datasetIndex !== undefined ? 
                    props.chartData.datasets[label.datasetIndex].label : ''})`,
                };
              });
            }
          }
        }
      },
      // Garantir que o gráfico seja responsivo
      responsive: true,
      maintainAspectRatio: true
    }
  });
};

// Gerar resumo automático se não for fornecido
const generatedSummary = computed(() => {
  if (props.summary) return props.summary;
  
  // Gerar resumo básico baseado no tipo de gráfico e dados
  const datasets = props.chartData.datasets;
  const labels = props.chartData.labels;
  
  if (!datasets || !labels || datasets.length === 0 || labels.length === 0) {
    return 'Não há dados disponíveis para este gráfico.';
  }
  
  let summary = `Este gráfico ${props.chartType} mostra `;
  
  if (datasets.length === 1) {
    const data = datasets[0].data;
    const max = Math.max(...data);
    const maxIndex = data.indexOf(max);
    const min = Math.min(...data);
    const minIndex = data.indexOf(min);
    const sum = data.reduce((a, b) => a + b, 0);
    const avg = sum / data.length;
    
    summary += `${datasets[0].label || 'dados'} para ${labels.length} categorias. `;
    summary += `O valor mais alto é ${max} (${labels[maxIndex]}), `;
    summary += `o valor mais baixo é ${min} (${labels[minIndex]}), `;
    summary += `e a média é ${avg.toFixed(2)}.`;
  } else {
    summary += `${datasets.length} séries de dados: `;
    summary += datasets.map(d => d.label || 'Sem rótulo').join(', ');
    summary += ` para ${labels.length} categorias: `;
    summary += labels.join(', ') + '.';
  }
  
  return summary;
});

// Observar mudanças nos dados do gráfico
watch(() => props.chartData, () => {
  initChart();
}, { deep: true });

// Observar mudanças no tipo de gráfico
watch(() => props.chartType, () => {
  initChart();
});

// Inicialização
onMounted(() => {
  initChart();
});
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-chart {
  width: 100%;
  margin-bottom: 2rem;
}

.chart-title {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.chart-description {
  margin-bottom: 1rem;
  color: #6c757d;
}

.chart-container {
  position: relative;
  margin: auto;
  height: 60vh;
  max-height: 500px;
  margin-bottom: 1.5rem;
}

.chart-table-container {
  margin-top: 1.5rem;
  overflow-x: auto;
}

.chart-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.chart-table caption {
  padding: 0.5rem;
  font-style: italic;
  text-align: left;
  caption-side: top;
}

.chart-table th,
.chart-table td {
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  text-align: left;
}

.chart-table thead th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.chart-summary {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.chart-summary h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Vídeo Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="a11y-video" :class="{ 'video-fullscreen': isFullscreen }"&gt;
    &lt;div class="video-container" ref="videoContainer"&gt;
      &lt;video
        ref="videoRef"
        :src="src"
        :poster="poster"
        :width="width"
        :height="height"
        :autoplay="autoplay"
        :loop="loop"
        :muted="muted"
        :playsinline="playsinline"
        :preload="preload"
        :controls="false"
        @timeupdate="onTimeUpdate"
        @loadedmetadata="onLoadedMetadata"
        @ended="onEnded"
        @click="togglePlay"
      &gt;
        &lt;source v-for="source in sources" :key="source.src" :src="source.src" :type="source.type"&gt;
        &lt;track 
          v-for="track in tracks" 
          :key="track.src" 
          :kind="track.kind" 
          :src="track.src" 
          :srclang="track.srclang" 
          :label="track.label"
          :default="track.default"
        &gt;
        Seu navegador não suporta o elemento de vídeo.
      &lt;/video&gt;
      
      &lt;div class="video-overlay" v-if="!isPlaying && !controlsVisible"&gt;
        &lt;button 
          class="play-button" 
          @click="togglePlay"
          aria-label="Reproduzir vídeo"
        &gt;
          &lt;i class="fas fa-play"&gt;&lt;/i&gt;
        &lt;/button&gt;
      &lt;/div&gt;
      
      &lt;div 
        class="video-controls" 
        :class="{ 'controls-visible': controlsVisible }"
        @mouseover="showControls"
        @mouseleave="hideControlsDelayed"
      &gt;
        &lt;div class="progress-container"&gt;
          &lt;div 
            class="progress-bar" 
            ref="progressBar"
            @click="seek"
            @keydown.left="seekBackward"
            @keydown.right="seekForward"
            tabindex="0"
            role="slider"
            :aria-label="'Posição do vídeo: ' + formatTime(currentTime) + ' de ' + formatTime(duration)"
            :aria-valuemin="0"
            :aria-valuemax="duration"
            :aria-valuenow="currentTime"
            :aria-valuetext="formatTime(currentTime) + ' de ' + formatTime(duration)"
          &gt;
            &lt;div class="progress-fill" :style="{ width: progressPercentage + '%' }"&gt;&lt;/div&gt;
            &lt;div class="progress-handle" :style="{ left: progressPercentage + '%' }"&gt;&lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        
        &lt;div class="controls-main"&gt;
          &lt;div class="controls-left"&gt;
            &lt;button 
              class="control-button" 
              @click="togglePlay"
              :aria-label="isPlaying ? 'Pausar' : 'Reproduzir'"
            &gt;
              &lt;i :class="['fas', isPlaying ? 'fa-pause' : 'fa-play']"&gt;&lt;/i&gt;
            &lt;/button&gt;
            
            &lt;div class="volume-control"&gt;
              &lt;button 
                class="control-button" 
                @click="toggleMute"
                :aria-label="isMuted ? 'Ativar som' : 'Desativar som'"
              &gt;
                &lt;i :class="['fas', volumeIcon]"&gt;&lt;/i&gt;
              &lt;/button&gt;
              
              &lt;div 
                class="volume-slider" 
                ref="volumeSlider"
                @click="changeVolume"
                role="slider"
                aria-label="Volume"
                :aria-valuemin="0"
                :aria-valuemax="100"
                :aria-valuenow="volume * 100"
                :aria-valuetext="Math.round(volume * 100) + '%'"
                tabindex="0"
                @keydown.left="decreaseVolume"
                @keydown.right="increaseVolume"
                @keydown.up="increaseVolume"
                @keydown.down="decreaseVolume"
              &gt;
                &lt;div class="volume-fill" :style="{ width: volume * 100 + '%' }"&gt;&lt;/div&gt;
                &lt;div class="volume-handle" :style="{ left: volume * 100 + '%' }"&gt;&lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
            
            &lt;div class="time-display"&gt;
              &lt;span class="current-time"&gt;{{ formatTime(currentTime) }}&lt;/span&gt;
              &lt;span class="time-separator"&gt;/&lt;/span&gt;
              &lt;span class="total-time"&gt;{{ formatTime(duration) }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          
          &lt;div class="controls-right"&gt;
            &lt;div v-if="hasCaptions" class="caption-control"&gt;
              &lt;button 
                class="control-button" 
                @click="toggleCaptions"
                :aria-label="captionsEnabled ? 'Desativar legendas' : 'Ativar legendas'"
                :aria-pressed="captionsEnabled"
              &gt;
                &lt;i class="fas fa-closed-captioning" :class="{ 'active': captionsEnabled }"&gt;&lt;/i&gt;
              &lt;/button&gt;
            &lt;/div&gt;
            
            &lt;div class="playback-speed"&gt;
              &lt;button 
                class="control-button speed-button" 
                @click="toggleSpeedMenu"
                aria-label="Velocidade de reprodução"
                :aria-expanded="speedMenuVisible"
              &gt;
                {{ playbackRate }}x
              &lt;/button&gt;
              
              &lt;div v-if="speedMenuVisible" class="speed-menu"&gt;
                &lt;button 
                  v-for="speed in playbackSpeeds" 
                  :key="speed" 
                  class="speed-option"
                  :class="{ 'active': playbackRate === speed }"
                  @click="setPlaybackRate(speed)"
                &gt;
                  {{ speed }}x
                &lt;/button&gt;
              &lt;/div&gt;
            &lt;/div&gt;
            
            &lt;button 
              class="control-button" 
              @click="toggleFullscreen"
              :aria-label="isFullscreen ? 'Sair da tela cheia' : 'Tela cheia'"
            &gt;
              &lt;i :class="['fas', isFullscreen ? 'fa-compress' : 'fa-expand']"&gt;&lt;/i&gt;
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div v-if="title || description" class="video-info"&gt;
      &lt;h3 v-if="title" class="video-title"&gt;{{ title }}&lt;/h3&gt;
      &lt;p v-if="description" class="video-description"&gt;{{ description }}&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div v-if="transcript" class="video-transcript"&gt;
      &lt;h4 class="transcript-title"&gt;
        Transcrição
        &lt;button 
          class="transcript-toggle" 
          @click="toggleTranscript"
          :aria-expanded="transcriptVisible"
        &gt;
          {{ transcriptVisible ? 'Ocultar' : 'Mostrar' }}
        &lt;/button&gt;
      &lt;/h4&gt;
      
      &lt;div v-if="transcriptVisible" class="transcript-content"&gt;
        {{ transcript }}
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  sources: {
    type: Array,
    default: () => []
  },
  tracks: {
    type: Array,
    default: () => []
  },
  poster: {
    type: String,
    default: ''
  },
  width: {
    type: [Number, String],
    default: '100%'
  },
  height: {
    type: [Number, String],
    default: 'auto'
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  loop: {
    type: Boolean,
    default: false
  },
  muted: {
    type: Boolean,
    default: false
  },
  playsinline: {
    type: Boolean,
    default: true
  },
  preload: {
    type: String,
    default: 'metadata',
    validator: (value) => ['none', 'metadata', 'auto'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  transcript: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['play', 'pause', 'timeupdate', 'ended']);

// Referências
const videoRef = ref(null);
const videoContainer = ref(null);
const progressBar = ref(null);
const volumeSlider = ref(null);

// Estado
const isPlaying = ref(false);
const isMuted = ref(props.muted);
const volume = ref(1);
const currentTime = ref(0);
const duration = ref(0);
const controlsVisible = ref(false);
const controlsTimeout = ref(null);
const isFullscreen = ref(false);
const captionsEnabled = ref(false);
const playbackRate = ref(1);
const speedMenuVisible = ref(false);
const transcriptVisible = ref(false);

// Valores computados
const progressPercentage = computed(() => {
  return duration.value ? (currentTime.value / duration.value) * 100 : 0;
});

const volumeIcon = computed(() => {
  if (isMuted.value || volume.value === 0) {
    return 'fa-volume-mute';
  } else if (volume.value < 0.5) {
    return 'fa-volume-down';
  } else {
    return 'fa-volume-up';
  }
});

const hasCaptions = computed(() => {
  return props.tracks.some(track => track.kind === 'subtitles' || track.kind === 'captions');
});

const playbackSpeeds = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];

// Métodos
const togglePlay = () => {
  if (videoRef.value) {
    if (isPlaying.value) {
      videoRef.value.pause();
    } else {
      videoRef.value.play();
    }
  }
};

const onTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;
    emit('timeupdate', currentTime.value);
  }
};

const onLoadedMetadata = () => {
  if (videoRef.value) {
    duration.value = videoRef.value.duration;
    volume.value = videoRef.value.volume;
  }
};

const onEnded = () => {
  isPlaying.value = false;
  emit('ended');
};

const seek = (event) => {
  if (!progressBar.value || !videoRef.value) return;
  
  const rect = progressBar.value.getBoundingClientRect();
  const pos = (event.clientX - rect.left) / rect.width;
  videoRef.value.currentTime = pos * duration.value;
};

const seekForward = () => {
  if (videoRef.value) {
    videoRef.value.currentTime = Math.min(videoRef.value.currentTime + 5, duration.value);
  }
};

const seekBackward = () => {
  if (videoRef.value) {
    videoRef.value.currentTime = Math.max(videoRef.value.currentTime - 5, 0);
  }
};

const toggleMute = () => {
  if (videoRef.value) {
    videoRef.value.muted = !videoRef.value.muted;
    isMuted.value = videoRef.value.muted;
  }
};

const changeVolume = (event) => {
  if (!volumeSlider.value || !videoRef.value) return;
  
  const rect = volumeSlider.value.getBoundingClientRect();
  const pos = (event.clientX - rect.left) / rect.width;
  setVolume(Math.max(0, Math.min(1, pos)));
};

const setVolume = (value) => {
  if (videoRef.value) {
    volume.value = value;
    videoRef.value.volume = value;
    videoRef.value.muted = value === 0;
    isMuted.value = value === 0;
  }
};

const increaseVolume = () => {
  setVolume(Math.min(1, volume.value + 0.1));
};

const decreaseVolume = () => {
  setVolume(Math.max(0, volume.value - 0.1));
};

const formatTime = (seconds) => {
  if (isNaN(seconds) || seconds === Infinity) return '0:00';
  
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

const showControls = () => {
  controlsVisible.value = true;
  clearTimeout(controlsTimeout.value);
};

const hideControlsDelayed = () => {
  controlsTimeout.value = setTimeout(() => {
    if (isPlaying.value) {
      controlsVisible.value = false;
    }
  }, 2000);
};

const toggleFullscreen = () => {
  if (!videoContainer.value) return;
  
  if (!isFullscreen.value) {
    if (videoContainer.value.requestFullscreen) {
      videoContainer.value.requestFullscreen();
    } else if (videoContainer.value.mozRequestFullScreen) {
      videoContainer.value.mozRequestFullScreen();
    } else if (videoContainer.value.webkitRequestFullscreen) {
      videoContainer.value.webkitRequestFullscreen();
    } else if (videoContainer.value.msRequestFullscreen) {
      videoContainer.value.msRequestFullscreen();
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
  }
};

const toggleCaptions = () => {
  if (!videoRef.value) return;
  
  captionsEnabled.value = !captionsEnabled.value;
  
  const tracks = videoRef.value.textTracks;
  for (let i = 0; i < tracks.length; i++) {
    if (tracks[i].kind === 'subtitles' || tracks[i].kind === 'captions') {
      tracks[i].mode = captionsEnabled.value ? 'showing' : 'hidden';
    }
  }
};

const toggleSpeedMenu = () => {
  speedMenuVisible.value = !speedMenuVisible.value;
};

const setPlaybackRate = (rate) => {
  if (videoRef.value) {
    videoRef.value.playbackRate = rate;
    playbackRate.value = rate;
    speedMenuVisible.value = false;
  }
};

const toggleTranscript = () => {
  transcriptVisible.value = !transcriptVisible.value;
};

// Manipuladores de eventos
const handleFullscreenChange = () => {
  isFullscreen.value = !!(
    document.fullscreenElement ||
    document.mozFullScreenElement ||
    document.webkitFullscreenElement ||
    document.msFullscreenElement
  );
};

const handleKeydown = (event) => {
  if (!videoRef.value) return;
  
  switch (event.key) {
    case ' ':
      event.preventDefault();
      togglePlay();
      break;
    case 'ArrowLeft':
      seekBackward();
      break;
    case 'ArrowRight':
      seekForward();
      break;
    case 'ArrowUp':
      increaseVolume();
      break;
    case 'ArrowDown':
      decreaseVolume();
      break;
    case 'f':
      toggleFullscreen();
      break;
    case 'm':
      toggleMute();
      break;
    case 'c':
      if (hasCaptions.value) {
        toggleCaptions();
      }
      break;
  }
};

// Observar mudanças no estado de reprodução
watch(() => videoRef.value?.paused, (paused) => {
  if (paused !== undefined) {
    isPlaying.value = !paused;
    
    if (isPlaying.value) {
      emit('play');
      hideControlsDelayed();
    } else {
      emit('pause');
      showControls();
    }
  }
});

// Inicialização
onMounted(() => {
  if (videoRef.value) {
    // Definir volume inicial
    videoRef.value.volume = volume.value;
    videoRef.value.muted = isMuted.value;
    
    // Verificar se há legendas disponíveis
    if (hasCaptions.value) {
      const tracks = videoRef.value.textTracks;
      for (let i = 0; i < tracks.length; i++) {
        if (tracks[i].kind === 'subtitles' || tracks[i].kind === 'captions') {
          // Definir modo inicial das legendas
          tracks[i].mode = captionsEnabled.value ? 'showing' : 'hidden';
          
          // Ativar legendas padrão se especificado
          if (props.tracks.some(t => t.default)) {
            captionsEnabled.value = true;
            tracks[i].mode = 'showing';
          }
        }
      }
    }
  }
  
  // Adicionar ouvintes de eventos
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('mozfullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('MSFullscreenChange', handleFullscreenChange);
  document.addEventListener('keydown', handleKeydown);
});

// Limpeza
onUnmounted(() => {
  clearTimeout(controlsTimeout.value);
  
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
  document.removeEventListener('keydown', handleKeydown);
});
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-video {
  width: 100%;
  margin-bottom: 1.5rem;
}

.video-container {
  position: relative;
  width: 100%;
  background-color: #000;
  overflow: hidden;
}

.video-container video {
  width: 100%;
  height: auto;
  display: block;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  cursor: pointer;
}

.play-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  color: white;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.play-button:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

.play-button:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
  padding: 10px;
  opacity: 0;
  transition: opacity 0.3s;
}

.video-controls.controls-visible {
  opacity: 1;
}

.progress-container {
  width: 100%;
  margin-bottom: 10px;
}

.progress-bar {
  position: relative;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  border-radius: 2.5px;
}

.progress-bar:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

.progress-fill {
  position: absolute;
  height: 100%;
  background-color: #ff0000;
  border-radius: 2.5px;
}

.progress-handle {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background-color: #ff0000;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  display: none;
}

.progress-bar:hover .progress-handle,
.progress-bar:focus .progress-handle {
  display: block;
}

.controls-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controls-left,
.controls-right {
  display: flex;
  align-items: center;
}

.control-button {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  padding: 5px 10px;
  cursor: pointer;
  transition: color 0.2s;
}

.control-button:hover {
  color: #ff0000;
}

.control-button:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

.volume-control {
  display: flex;
  align-items: center;
  position: relative;
}

.volume-slider {
  width: 0;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2.5px;
  margin-left: 5px;
  overflow: hidden;
  transition: width 0.3s;
  cursor: pointer;
}

.volume-control:hover .volume-slider,
.volume-slider:focus {
  width: 60px;
}

.volume-fill {
  height: 100%;
  background-color: white;
  border-radius: 2.5px;
}

.volume-handle {
  position: absolute;
  top: 50%;
  width: 10px;
  height: 10px;
  background-color: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  display: none;
}

.volume-slider:hover .volume-handle,
.volume-slider:focus .volume-handle {
  display: block;
}

.time-display {
  color: white;
  font-size: 14px;
  margin-left: 10px;
}

.caption-control {
  margin-right: 10px;
}

.caption-control .active {
  color: #ff0000;
}

.playback-speed {
  position: relative;
  margin-right: 10px;
}

.speed-button {
  min-width: 40px;
  text-align: center;
}

.speed-menu {
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: rgba(0, 0, 0, 0.9);
  border-radius: 4px;
  padding: 5px 0;
  margin-bottom: 5px;
  display: flex;
  flex-direction: column;
  z-index: 10;
}

.speed-option {
  background: none;
  border: none;
  color: white;
  padding: 5px 15px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.speed-option:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.speed-option.active {
  color: #ff0000;
}

.video-info {
  margin-top: 1rem;
}

.video-title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.video-description {
  color: #6c757d;
  margin-bottom: 1rem;
}

.video-transcript {
  margin-top: 1rem;
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
}

.transcript-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.transcript-toggle {
  background: none;
  border: none;
  color: #0d6efd;
  cursor: pointer;
  padding: 0;
  font-size: 0.875rem;
}

.transcript-content {
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  white-space: pre-line;
}

/* Estilos para tela cheia */
.video-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background-color: #000;
}

.video-fullscreen .video-container {
  height: 100%;
}

.video-fullscreen video {
  height: 100%;
  object-fit: contain;
}

/* Responsividade */
@media (max-width: 767.98px) {
  .controls-main {
    flex-wrap: wrap;
  }
  
  .controls-left {
    margin-bottom: 5px;
  }
  
  .time-display {
    font-size: 12px;
  }
  
  .control-button {
    padding: 5px;
  }
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Textos Alternativos e Descrições</h4>
        <ul>
          <li>Forneça textos alternativos descritivos para todas as imagens informativas</li>
          <li>Use atributo <code>alt</code> vazio para imagens decorativas (<code>alt=""</code>)</li>
          <li>Descreva a função da imagem, não apenas o que ela mostra</li>
          <li>Mantenha textos alternativos concisos (geralmente menos de 125 caracteres)</li>
          <li>Não use "imagem de" ou "foto de" no texto alternativo</li>
          <li>Para imagens complexas, forneça uma descrição longa além do texto alternativo</li>
          <li>Adicione legendas para imagens quando apropriado</li>
          <li>Forneça transcrições para conteúdo de áudio e vídeo</li>
          <li>Adicione legendas e audiodescrição para vídeos</li>
          <li>Use <code>aria-label</code> para ícones que transmitem significado</li>
          <li>Teste seus textos alternativos com leitores de tela</li>
        </ul>
      </div>

      <div class="code-block">
        <h4>Exemplos de Bons Textos Alternativos</h4>
        <table class="table">
          <thead>
            <tr>
              <th>Tipo de Imagem</th>
              <th>Bom Texto Alternativo</th>
              <th>Texto Alternativo Ruim</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Logo da empresa</td>
              <td><code>alt="Logo da TechCorp"</code></td>
              <td><code>alt="Imagem de logo"</code></td>
            </tr>
            <tr>
              <td>Botão de fechar</td>
              <td><code>alt="Fechar"</code> ou <code>aria-label="Fechar"</code></td>
              <td><code>alt="X"</code></td>
            </tr>
            <tr>
              <td>Gráfico de vendas</td>
              <td><code>alt="Gráfico mostrando aumento de vendas de 15% no último trimestre"</code></td>
              <td><code>alt="Gráfico de vendas"</code></td>
            </tr>
            <tr>
              <td>Foto decorativa</td>
              <td><code>alt=""</code> (vazio)</td>
              <td><code>alt="Imagem decorativa"</code></td>
            </tr>
            <tr>
              <td>Ícone de notificação</td>
              <td><code>alt="3 novas notificações"</code> ou
                <code>aria-label="3 novas notificações"</code>
              </td>
              <td><code>alt="Sino"</code></td>
            </tr>
            <tr>
              <td>Foto de perfil</td>
              <td><code>alt="Foto de perfil de João Silva"</code></td>
              <td><code>alt="Imagem de usuário"</code></td>
            </tr>
            <tr>
              <td>Imagem com legenda</td>
              <td><code>alt="Sede da empresa em São Paulo"</code> (com legenda visível)</td>
              <td><code>alt="Sede da empresa em São Paulo, um prédio moderno de vidro com 20 andares"</code>
                (informação duplicada na legenda)</td>
            </tr>
            <tr>
              <td>Imagem em link</td>
              <td><code>alt="Documentação do Vue.js"</code></td>
              <td><code>alt="Clique aqui"</code></td>
            </tr>
            <tr>
              <td>Infográfico complexo</td>
              <td><code>alt="Ciclo de vida dos componentes Vue.js"</code> (com descrição longa separada)
              </td>
              <td>
                <code>alt="Infográfico detalhando todas as etapas do ciclo de vida dos componentes Vue.js, incluindo created, mounted, updated e destroyed, com explicações de cada hook e seus usos comuns..."</code>
                (muito longo)
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div id="navegacao-teclado" class="subsection">
      <h3>8.5. Navegação por Teclado</h3>

      <p>A navegação por teclado é essencial para usuários que não podem ou preferem não usar um mouse.
        Implementar uma navegação por teclado eficiente melhora a acessibilidade para todos os usuários.</p>

      <div class="code-block">
        <h4>Composable para Gerenciamento de Foco</h4>
        <pre><code>// useFocusTrap.js
import { ref, onMounted, onBeforeUnmount } from 'vue';

export function useFocusTrap(options = {}) {
  const rootElement = ref(null);
  const firstFocusableElement = ref(null);
  const lastFocusableElement = ref(null);
  
  const {
    autoFocus = true,
    returnFocusOnDeactivate = true,
    escapeDeactivates = true,
    onActivate = () => {},
    onDeactivate = () => {},
  } = options;
  
  const previouslyFocusedElement = ref(null);
  const isActive = ref(false);
  
  const focusableSelectors = [
    'a[href]:not([tabindex="-1"])',
    'button:not([disabled]):not([tabindex="-1"])',
    'input:not([disabled]):not([tabindex="-1"])',
    'select:not([disabled]):not([tabindex="-1"])',
    'textarea:not([disabled]):not([tabindex="-1"])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]:not([tabindex="-1"])'
  ];
  
  const getFocusableElements = () => {
    if (!rootElement.value) return [];
    
    const elements = rootElement.value.querySelectorAll(focusableSelectors.join(','));
    return Array.from(elements).filter(el => {
      return el.offsetWidth > 0 && el.offsetHeight > 0 && window.getComputedStyle(el).visibility !== 'hidden';
    });
  };
  
  const updateFocusableElements = () => {
    const focusableElements = getFocusableElements();
    firstFocusableElement.value = focusableElements[0] || null;
    lastFocusableElement.value = focusableElements[focusableElements.length - 1] || null;
  };
  
  const handleKeyDown = (event) => {
    if (!isActive.value) return;
    
    // Escape key
    if (escapeDeactivates && event.key === 'Escape') {
      deactivate();
      return;
    }
    
    // Tab key
    if (event.key === 'Tab') {
      // If there are no focusable elements, prevent tab
      if (!firstFocusableElement.value && !lastFocusableElement.value) {
        event.preventDefault();
        return;
      }
      
      // Shift + Tab
      if (event.shiftKey) {
        // If focus is on first element, move to last
        if (document.activeElement === firstFocusableElement.value) {
          event.preventDefault();
          lastFocusableElement.value?.focus();
        }
      } 
      // Tab
      else {
        // If focus is on last element, move to first
        if (document.activeElement === lastFocusableElement.value) {
          event.preventDefault();
          firstFocusableElement.value?.focus();
        }
      }
    }
  };
  
  const activate = () => {
    if (isActive.value) return;
    
    // Store currently focused element
    previouslyFocusedElement.value = document.activeElement;
    
    // Update focusable elements
    updateFocusableElements();
    
    // Add event listener
    document.addEventListener('keydown', handleKeyDown);
    
    // Auto focus first element if enabled
    if (autoFocus && firstFocusableElement.value) {
      firstFocusableElement.value.focus();
    }
    
    isActive.value = true;
    onActivate();
  };
  
  const deactivate = () => {
    if (!isActive.value) return;
    
    // Remove event listener
    document.removeEventListener('keydown', handleKeyDown);
    
    // Return focus to previously focused element
    if (returnFocusOnDeactivate && previouslyFocusedElement.value) {
      previouslyFocusedElement.value.focus();
    }
    
    isActive.value = false;
    onDeactivate();
  };
  
  onMounted(() => {
    if (rootElement.value) {
      updateFocusableElements();
    }
  });
  
  onBeforeUnmount(() => {
    deactivate();
  });
  
  return {
    rootElement,
    activate,
    deactivate,
    isActive,
    updateFocusableElements
  };
}
</code></pre>
      </div>

      <div class="code-block">
        <h4>Diretiva para Foco Visível Personalizado</h4>
        <pre><code>// v-focus-visible.js
export const focusVisible = {
  mounted(el, binding) {
    const options = binding.value || {};
    const focusClass = options.class || 'focus-visible';
    const outlineColor = options.outlineColor || '#1976d2';
    const outlineWidth = options.outlineWidth || '2px';
    const outlineStyle = options.outlineStyle || 'solid';
    const outlineOffset = options.outlineOffset || '2px';
    
    // Detectar se o usuário está navegando com teclado
    let usingKeyboard = false;
    
    const handleKeyDown = () => {
      usingKeyboard = true;
    };
    
    const handleMouseDown = () => {
      usingKeyboard = false;
    };
    
    const handleFocus = () => {
      if (usingKeyboard) {
        if (options.useClass) {
          el.classList.add(focusClass);
        } else {
          el.style.outline = `${outlineWidth} ${outlineStyle} ${outlineColor}`;
          el.style.outlineOffset = outlineOffset;
        }
      }
    };
    
    const handleBlur = () => {
      if (options.useClass) {
        el.classList.remove(focusClass);
      } else {
        el.style.outline = '';
        el.style.outlineOffset = '';
      }
    };
    
    // Adicionar ouvintes de eventos
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);
    el.addEventListener('focus', handleFocus);
    el.addEventListener('blur', handleBlur);
    
    // Armazenar funções de limpeza
    el._focusVisibleCleanup = () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
      el.removeEventListener('focus', handleFocus);
      el.removeEventListener('blur', handleBlur);
    };
  },
  
  unmounted(el) {
    // Limpar ouvintes de eventos
    if (el._focusVisibleCleanup) {
      el._focusVisibleCleanup();
      delete el._focusVisibleCleanup;
    }
  }
};

// Uso:
// app.directive('focus-visible', focusVisible);
</code></pre>
      </div>

      <div class="code-block">
        <h4>Componente de Menu de Navegação Acessível</h4>
        <pre><code>&lt;template&gt;
  &lt;nav class="a11y-nav" :aria-label="ariaLabel"&gt;
    &lt;ul class="nav-list" role="menubar"&gt;
      &lt;li 
        v-for="(item, index) in items" 
        :key="item.id"
        class="nav-item"
        :class="{ 'has-submenu': item.children?.length }"
        role="none"
      &gt;
        &lt;a 
          v-if="!item.children?.length"
          :href="item.url"
          class="nav-link"
          :class="{ 'active': isActive(item) }"
          role="menuitem"
          :tabindex="index === 0 ? 0 : -1"
          @keydown.down="focusNextItem(index)"
          @keydown.up="focusPrevItem(index)"
          @keydown.home="focusFirstItem"
          @keydown.end="focusLastItem"
          v-focus-visible
        &gt;
          {{ item.label }}
        &lt;/a&gt;
        
        &lt;button
          v-else
          type="button"
          class="nav-link has-dropdown"
          :class="{ 'active': isActive(item), 'expanded': expandedMenus.includes(item.id) }"
          role="menuitem"
          aria-haspopup="true"
          :aria-expanded="expandedMenus.includes(item.id)"
          :tabindex="index === 0 ? 0 : -1"
          @click="toggleSubmenu(item.id)"
          @keydown.down="focusNextItem(index)"
          @keydown.up="focusPrevItem(index)"
          @keydown.right="expandAndFocusSubmenu(item.id)"
          @keydown.left="collapseSubmenu(item.id)"
          @keydown.home="focusFirstItem"
          @keydown.end="focusLastItem"
          @keydown.enter="toggleSubmenu(item.id)"
          @keydown.space="toggleSubmenu(item.id)"
          v-focus-visible
        &gt;
          {{ item.label }}
          &lt;span class="dropdown-icon" aria-hidden="true"&gt;
            &lt;i :class="expandedMenus.includes(item.id) ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"&gt;&lt;/i&gt;
          &lt;/span&gt;
        &lt;/button&gt;
        
        &lt;transition name="submenu"&gt;
          &lt;ul 
            v-if="item.children?.length && expandedMenus.includes(item.id)"
            class="submenu"
            role="menu"
            :aria-label="item.label"
          &gt;
            &lt;li 
              v-for="(child, childIndex) in item.children" 
              :key="child.id"
              class="submenu-item"
              role="none"
            &gt;
              &lt;a 
                :href="child.url"
                class="submenu-link"
                :class="{ 'active': isActive(child) }"
                role="menuitem"
                tabindex="-1"
                @keydown.down="focusNextSubmenuItem(item.id, childIndex)"
                @keydown.up="focusPrevSubmenuItem(item.id, childIndex)"
                @keydown.right="expandAndFocusSubmenu(child.id)"
                @keydown.left="focusParentMenuItem(item.id)"
                @keydown.home="focusFirstSubmenuItem(item.id)"
                @keydown.end="focusLastSubmenuItem(item.id)"
                @keydown.esc="focusParentMenuItem(item.id)"
                v-focus-visible
              &gt;
                {{ child.label }}
              &lt;/a&gt;
            &lt;/li&gt;
          &lt;/ul&gt;
        &lt;/transition&gt;
      &lt;/li&gt;
    &lt;/ul&gt;
  &lt;/nav&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import { focusVisible } from '@/directives/v-focus-visible';

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  ariaLabel: {
    type: String,
    default: 'Menu de navegação principal'
  }
});

const route = useRoute();
const expandedMenus = ref([]);
const navLinks = ref([]);
const submenuLinks = ref({});

// Verificar se um item está ativo
const isActive = (item) => {
  return item.url === route.path || 
         (item.children?.some(child => child.url === route.path));
};

// Alternar submenu
const toggleSubmenu = (id) => {
  const index = expandedMenus.value.indexOf(id);
  if (index === -1) {
    expandedMenus.value.push(id);
  } else {
    expandedMenus.value.splice(index, 1);
  }
};

// Expandir submenu e focar no primeiro item
const expandAndFocusSubmenu = (id) => {
  if (!expandedMenus.value.includes(id)) {
    expandedMenus.value.push(id);
    
    // Aguardar a renderização do submenu
    setTimeout(() => {
      const firstSubmenuItem = submenuLinks.value[id]?.[0];
      if (firstSubmenuItem) {
        firstSubmenuItem.focus();
      }
    }, 100);
  }
};

// Recolher submenu
const collapseSubmenu = (id) => {
  const index = expandedMenus.value.indexOf(id);
  if (index !== -1) {
    expandedMenus.value.splice(index, 1);
  }
};

// Focar no item pai do submenu
const focusParentMenuItem = (parentId) => {
  collapseSubmenu(parentId);
  
  // Encontrar o botão do menu pai
  const parentIndex = props.items.findIndex(item => item.id === parentId);
  if (parentIndex !== -1 && navLinks.value[parentIndex]) {
    navLinks.value[parentIndex].focus();
  }
};

// Navegação por teclado no menu principal
const focusNextItem = (currentIndex) => {
  const nextIndex = (currentIndex + 1) % navLinks.value.length;
  navLinks.value[nextIndex]?.focus();
};

const focusPrevItem = (currentIndex) => {
  const prevIndex = (currentIndex - 1 + navLinks.value.length) % navLinks.value.length;
  navLinks.value[prevIndex]?.focus();
};

const focusFirstItem = () => {
  navLinks.value[0]?.focus();
};

const focusLastItem = () => {
  navLinks.value[navLinks.value.length - 1]?.focus();
};

// Navegação por teclado nos submenus
const focusNextSubmenuItem = (parentId, currentIndex) => {
  const submenuItems = submenuLinks.value[parentId] || [];
  const nextIndex = (currentIndex + 1) % submenuItems.length;
  submenuItems[nextIndex]?.focus();
};

const focusPrevSubmenuItem = (parentId, currentIndex) => {
  const submenuItems = submenuLinks.value[parentId] || [];
  const prevIndex = (currentIndex - 1 + submenuItems.length) % submenuItems.length;
  submenuItems[prevIndex]?.focus();
};

const focusFirstSubmenuItem = (parentId) => {
  const submenuItems = submenuLinks.value[parentId] || [];
  submenuItems[0]?.focus();
};

const focusLastSubmenuItem = (parentId) => {
  const submenuItems = submenuLinks.value[parentId] || [];
  submenuItems[submenuItems.length - 1]?.focus();
};

// Fechar submenus ao clicar fora
const handleClickOutside = (event) => {
  if (expandedMenus.value.length === 0) return;
  
  const nav = document.querySelector('.a11y-nav');
  if (nav && !nav.contains(event.target)) {
    expandedMenus.value = [];
  }
};

// Inicialização
onMounted(() => {
  // Obter referências aos links de navegação
  navLinks.value = Array.from(document.querySelectorAll('.nav-link'));
  
  // Configurar referências para links de submenu
  props.items.forEach(item => {
    if (item.children?.length) {
      submenuLinks.value[item.id] = Array.from(
        document.querySelectorAll(`.submenu[aria-label="${item.label}"] .submenu-link`)
      );
    }
  });
  
  // Adicionar ouvinte de clique para fechar submenus
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
&lt;/script&gt;

&lt;style scoped&gt;
.a11y-nav {
  width: 100%;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #212529;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
  cursor: pointer;
  border: none;
  background: none;
  font-size: 1rem;
  text-align: left;
  width: 100%;
}

.nav-link:hover {
  background-color: #e9ecef;
  color: #0d6efd;
}

.nav-link.active {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
}

.dropdown-icon {
  margin-left: 0.5rem;
  font-size: 0.75rem;
}

.submenu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  list-style: none;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.submenu-item {
  width: 100%;
}

.submenu-link {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  color: #212529;
  text-decoration: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.submenu-link:hover {
  background-color: #f8f9fa;
  color: #0d6efd;
}

.submenu-link.active {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
}

/* Animações */
.submenu-enter-active,
.submenu-leave-active {
  transition: opacity 0.2s, transform 0.2s;
}

.submenu-enter-from,
.submenu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Responsividade */
@media (max-width: 767.98px) {
  .nav-list {
    flex-direction: column;
  }
  
  .submenu {
    position: static;
    box-shadow: none;
    border: none;
    border-left: 2px solid #dee2e6;
    margin-left: 1rem;
    padding-left: 0.5rem;
  }
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Diretrizes para Navegação por Teclado</h4>
        <ul>
          <li>Garanta que todos os elementos interativos sejam acessíveis via teclado</li>
          <li>Mantenha uma ordem de tabulação lógica (use <code>tabindex</code> com cuidado)</li>
          <li>Forneça indicadores visuais claros para o foco do teclado</li>
          <li>Implemente atalhos de teclado para ações comuns</li>
          <li>Evite armadilhas de foco (exceto em modais e diálogos)</li>
          <li>Permita que usuários pulem blocos de conteúdo repetitivo</li>
          <li>Teste a navegação usando apenas o teclado</li>
          <li>Implemente padrões de design de interação por teclado consistentes</li>
          <li>Forneça feedback visual e auditivo para ações do teclado</li>
          <li>Documente os atalhos de teclado disponíveis</li>
        </ul>
      </div>

      <div class="code-block">
        <h4>Componente de Link para Pular Navegação</h4>
        <pre><code>&lt;template&gt;
  &lt;a 
    href="#main-content" 
    class="skip-link"
    @focus="isVisible = true"
    @blur="isVisible = false"
    @click="handleClick"
  &gt;
    {{ text }}
  &lt;/a&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';

const props = defineProps({
  text: {
    type: String,
    default: 'Pular para o conteúdo principal'
  },
  targetId: {
    type: String,
    default: 'main-content'
  }
});

const isVisible = ref(false);

const handleClick = (event) => {
  // Prevenir comportamento padrão
  event.preventDefault();
  
  // Encontrar o elemento alvo
  const target = document.getElementById(props.targetId);
  
  if (target) {
    // Focar no elemento alvo
    target.setAttribute('tabindex', '-1');
    target.focus();
    
    // Remover tabindex após a transição
    setTimeout(() => {
      target.removeAttribute('tabindex');
    }, 1000);
    
    // Atualizar URL com hash
    history.pushState(null, null, `#${props.targetId}`);
  }
};
&lt;/script&gt;

&lt;style scoped&gt;
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  padding: 8px 16px;
  background-color: #0d6efd;
  color: white;
  font-weight: 500;
  text-decoration: none;
  z-index: 9999;
  transition: top 0.2s ease;
}

.skip-link:focus {
  top: 0;
  outline: 2px solid white;
  outline-offset: 2px;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Tabela de Atalhos de Teclado Comuns</h4>
        <table class="table">
          <thead>
            <tr>
              <th>Tecla</th>
              <th>Função</th>
              <th>Contexto</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><kbd>Tab</kbd></td>
              <td>Navegar para o próximo elemento focável</td>
              <td>Global</td>
            </tr>
            <tr>
              <td><kbd>Shift</kbd> + <kbd>Tab</kbd></td>
              <td>Navegar para o elemento focável anterior</td>
              <td>Global</td>
            </tr>
            <tr>
              <td><kbd>Enter</kbd> / <kbd>Space</kbd></td>
              <td>Ativar elemento focado (botão, link, etc.)</td>
              <td>Elementos interativos</td>
            </tr>
            <tr>
              <td><kbd>Esc</kbd></td>
              <td>Fechar, cancelar ou sair</td>
              <td>Modais, menus, diálogos</td>
            </tr>
            <tr>
              <td><kbd>↑</kbd> / <kbd>↓</kbd></td>
              <td>Navegar entre itens verticalmente</td>
              <td>Menus, listas, seletores</td>
            </tr>
            <tr>
              <td><kbd>←</kbd> / <kbd>→</kbd></td>
              <td>Navegar entre itens horizontalmente</td>
              <td>Menus, abas, controles deslizantes</td>
            </tr>
            <tr>
              <td><kbd>Home</kbd></td>
              <td>Ir para o primeiro item</td>
              <td>Listas, menus, tabelas</td>
            </tr>
            <tr>
              <td><kbd>End</kbd></td>
              <td>Ir para o último item</td>
              <td>Listas, menus, tabelas</td>
            </tr>
            <tr>
              <td><kbd>Page Up</kbd> / <kbd>Page Down</kbd></td>
              <td>Rolar uma página para cima/baixo</td>
              <td>Conteúdo rolável</td>
            </tr>
            <tr>
              <td><kbd>Alt</kbd> + <kbd>↓</kbd></td>
              <td>Abrir menu suspenso</td>
              <td>Menus, seletores</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div id="testes-acessibilidade" class="subsection">
      <h3>8.6. Testes de Acessibilidade</h3>

      <p>Testar a acessibilidade de seus componentes Vue é essencial para garantir que todos os usuários possam
        interagir com sua aplicação. Aqui estão algumas ferramentas e métodos para testar a acessibilidade.</p>

      <div class="code-block">
        <h4>Configuração de Testes de Acessibilidade com Jest e Testing Library</h4>
        <pre><code>// jest.config.js
module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  transform: {
    '^.+\\.vue$': 'vue-jest'
  },
  setupFilesAfterEnv: ['./jest.setup.js']
};

// jest.setup.js
import '@testing-library/jest-dom';
import { configure } from '@testing-library/vue';

// Configurar Testing Library
configure({
  testIdAttribute: 'data-test-id'
});

// Exemplo de teste de acessibilidade para um componente de botão
// ButtonA11y.spec.js
import { render, fireEvent } from '@testing-library/vue';
import { axe, toHaveNoViolations } from 'jest-axe';
import Button from '@/components/Button.vue';

expect.extend(toHaveNoViolations);

describe('Button Accessibility', () => {
  it('should not have accessibility violations', async () => {
    const { container } = render(Button, {
      props: {
        label: 'Enviar',
        type: 'primary'
      }
    });
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  
  it('should be focusable and activate with keyboard', async () => {
    const handleClick = jest.fn();
    
    const { getByRole } = render(Button, {
      props: {
        label: 'Enviar',
        type: 'primary',
        onClick: handleClick
      }
    });
    
    const button = getByRole('button', { name: /enviar/i });
    
    // Verificar se o botão pode receber foco
    button.focus();
    expect(button).toHaveFocus();
    
    // Verificar se o botão pode ser ativado com Enter
    await fireEvent.keyDown(button, { key: 'Enter', code: 'Enter' });
    expect(handleClick).toHaveBeenCalledTimes(1);
    
    // Verificar se o botão pode ser ativado com Space
    await fireEvent.keyDown(button, { key: ' ', code: 'Space' });
    expect(handleClick).toHaveBeenCalledTimes(2);
  });
  
  it('should have appropriate ARIA attributes when disabled', () => {
    const { getByRole } = render(Button, {
      props: {
        label: 'Enviar',
        type: 'primary',
        disabled: true
      }
    });
    
    const button = getByRole('button', { name: /enviar/i });
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute('aria-disabled', 'true');
  });
  
  it('should render with correct accessible name when using icon', () => {
    const { getByRole } = render(Button, {
      props: {
        icon: 'save',
        label: 'Salvar',
        type: 'primary'
      }
    });
    
    const button = getByRole('button', { name: /salvar/i });
    expect(button).toBeInTheDocument();
    
    // Verificar se o ícone tem aria-hidden
    const icon = button.querySelector('.icon');
    expect(icon).toHaveAttribute('aria-hidden', 'true');
  });
});</code></pre>
      </div>

      <div class="code-block">
        <h4>Testes de Acessibilidade para Componentes Complexos</h4>
        <pre><code>// ModalA11y.spec.js
import { render, fireEvent, waitFor } from '@testing-library/vue';
import { axe } from 'jest-axe';
import Modal from '@/components/Modal.vue';

describe('Modal Accessibility', () => {
  it('should not have accessibility violations when open', async () => {
    const { container } = render(Modal, {
      props: {
        title: 'Confirmação',
        isOpen: true,
        content: 'Tem certeza que deseja continuar?'
      },
      slots: {
        footer: `
          <button data-test-id="cancel-btn">Cancelar</button>
          <button data-test-id="confirm-btn">Confirmar</button>
        `
      }
    });
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  
  it('should trap focus within the modal when open', async () => {
    const { getByRole, getByTestId } = render(Modal, {
      props: {
        title: 'Confirmação',
        isOpen: true,
        content: 'Tem certeza que deseja continuar?'
      },
      slots: {
        footer: `
          <button data-test-id="cancel-btn">Cancelar</button>
          <button data-test-id="confirm-btn">Confirmar</button>
        `
      }
    });
    
    const modal = getByRole('dialog');
    const closeButton = getByRole('button', { name: /fechar/i });
    const cancelButton = getByTestId('cancel-btn');
    const confirmButton = getByTestId('confirm-btn');
    
    // Verificar se o modal tem os atributos ARIA corretos
    expect(modal).toHaveAttribute('aria-labelledby');
    expect(modal).toHaveAttribute('aria-modal', 'true');
    
    // Verificar se o foco inicial está no botão de fechar
    expect(closeButton).toHaveFocus();
    
    // Simular Tab para navegar para o próximo elemento
    await fireEvent.keyDown(closeButton, { key: 'Tab', code: 'Tab' });
    expect(cancelButton).toHaveFocus();
    
    // Simular Tab novamente
    await fireEvent.keyDown(cancelButton, { key: 'Tab', code: 'Tab' });
    expect(confirmButton).toHaveFocus();
    
    // Simular Tab novamente - deve voltar para o primeiro elemento (armadilha de foco)
    await fireEvent.keyDown(confirmButton, { key: 'Tab', code: 'Tab' });
    expect(closeButton).toHaveFocus();
    
    // Simular Shift+Tab para voltar ao último elemento
    await fireEvent.keyDown(closeButton, { key: 'Tab', code: 'Tab', shiftKey: true });
    expect(confirmButton).toHaveFocus();
  });
  
  it('should close on Escape key press', async () => {
    const handleClose = jest.fn();
    
    const { getByRole } = render(Modal, {
      props: {
        title: 'Confirmação',
        isOpen: true,
        content: 'Tem certeza que deseja continuar?',
        onClose: handleClose
      }
    });
    
    const modal = getByRole('dialog');
    
    // Simular pressionar Escape
    await fireEvent.keyDown(modal, { key: 'Escape', code: 'Escape' });
    expect(handleClose).toHaveBeenCalledTimes(1);
  });
  
  it('should return focus to trigger element when closed', async () => {
    // Criar um elemento de gatilho e adicioná-lo ao DOM
    const triggerButton = document.createElement('button');
    triggerButton.textContent = 'Abrir Modal';
    document.body.appendChild(triggerButton);
    triggerButton.focus();
    
    const handleClose = jest.fn();
    
    const { getByRole, rerender } = render(Modal, {
      props: {
        title: 'Confirmação',
        isOpen: true,
        content: 'Tem certeza que deseja continuar?',
        onClose: handleClose,
        triggerRef: triggerButton
      }
    });
    
    // Verificar se o foco foi movido para o modal
    const closeButton = getByRole('button', { name: /fechar/i });
    expect(closeButton).toHaveFocus();
    
    // Fechar o modal
    await rerender({ isOpen: false });
    
    // Verificar se o foco retornou ao elemento de gatilho
    await waitFor(() => {
      expect(triggerButton).toHaveFocus();
    });
    
    // Limpar
    document.body.removeChild(triggerButton);
  });
});</code></pre>
      </div>

      <div class="code-block">
        <h4>Integração com Cypress para Testes E2E de Acessibilidade</h4>
        <pre><code>// cypress/plugins/index.js
const { lighthouse, prepareAudit } = require('cypress-audit');

module.exports = (on, config) => {
  on('before:browser:launch', (browser, launchOptions) => {
    prepareAudit(launchOptions);
  });

  on('task', {
    lighthouse: lighthouse(),
  });
};

// cypress/support/commands.js
import 'cypress-axe';

// Comando personalizado para verificar acessibilidade
Cypress.Commands.add('checkA11y', (context, options) => {
  cy.injectAxe();
  cy.checkA11y(context, options);
});

// cypress/integration/accessibility.spec.js
describe('Accessibility Tests', () => {
  beforeEach(() => {
    cy.visit('/');
    cy.injectAxe();
  });

  it('should have no accessibility violations on home page', () => {
    cy.checkA11y();
  });

  it('should have no accessibility violations in navigation menu', () => {
    cy.get('nav').checkA11y();
  });

  it('should have no accessibility violations in form', () => {
    cy.visit('/contact');
    cy.get('form').checkA11y({
      rules: {
        'color-contrast': { enabled: true },
        'label': { enabled: true },
        'aria-required-attr': { enabled: true }
      }
    });
  });

  it('should have no accessibility violations in modal dialog', () => {
    cy.visit('/products');
    cy.contains('button', 'Detalhes').click();
    cy.get('[role="dialog"]').should('be.visible');
    cy.get('[role="dialog"]').checkA11y();
  });

  it('should run Lighthouse audit', () => {
    cy.lighthouse({
      accessibility: 90,
      'best-practices': 85,
      seo: 80,
      performance: 70
    });
  });

  it('should navigate using keyboard only', () => {
    // Verificar se o link "Pular para o conteúdo" funciona
    cy.get('body').tab();
    cy.focused().should('have.text', 'Pular para o conteúdo');
    cy.focused().type('{enter}');
    cy.focused().should('have.attr', 'id', 'main-content');

    // Navegar pelo menu usando teclado
    cy.get('body').tab();
    cy.focused().should('be.visible').and('have.attr', 'role', 'menuitem');
    cy.focused().type('{enter}');
    cy.url().should('include', '/home');

    // Navegar para um formulário e preenchê-lo usando apenas teclado
    cy.visit('/contact');
    cy.get('body').tab(); // Pular para o conteúdo
    cy.focused().type('{enter}');
    cy.tab(); // Primeiro campo do formulário
    cy.focused().should('have.attr', 'name', 'name');
    cy.focused().type('John Doe');
    cy.tab();
    cy.focused().should('have.attr', 'name', 'email');
    cy.focused().type('<EMAIL>');
    cy.tab();
    cy.focused().should('have.attr', 'name', 'message');
    cy.focused().type('This is a test message');
    cy.tab();
    cy.focused().should('have.attr', 'type', 'submit');
    cy.focused().type('{enter}');
    cy.contains('Mensagem enviada com sucesso').should('be.visible');
  });
});</code></pre>
      </div>

      <div class="best-practice">
        <h4>Ferramentas de Teste de Acessibilidade</h4>
        <ul>
          <li><strong>Ferramentas de Análise Estática:</strong>
            <ul>
              <li><a href="https://github.com/vue-a11y/eslint-plugin-vuejs-accessibility"
                  target="_blank">eslint-plugin-vuejs-accessibility</a> - Regras ESLint para
                acessibilidade em Vue.js</li>
              <li><a href="https://github.com/dequelabs/axe-core" target="_blank">axe-core</a> -
                Biblioteca para testes automatizados de acessibilidade</li>
            </ul>
          </li>
          <li><strong>Ferramentas de Teste Automatizado:</strong>
            <ul>
              <li><a href="https://github.com/nickcolley/jest-axe" target="_blank">jest-axe</a> -
                Integração do axe com Jest</li>
              <li><a href="https://github.com/component-driven/cypress-axe" target="_blank">cypress-axe</a> - Integração
                do axe com Cypress</li>
              <li><a href="https://github.com/testing-library/vue-testing-library"
                  target="_blank">@testing-library/vue</a> - Biblioteca para testar componentes Vue de
                forma acessível</li>
            </ul>
          </li>
          <li><strong>Extensões de Navegador:</strong>
            <ul>
              <li><a
                  href="https://chrome.google.com/webstore/detail/axe-devtools-web-accessib/lhdoppojpmngadmnindnejefpokejbdd"
                  target="_blank">axe DevTools</a> - Extensão do Chrome para testes de acessibilidade
              </li>
              <li><a
                  href="https://chrome.google.com/webstore/detail/wave-evaluation-tool/jbbplnpkjmmeebjpijfedlgcdilocofh"
                  target="_blank">WAVE</a> - Ferramenta de avaliação de acessibilidade web</li>
              <li><a href="https://chrome.google.com/webstore/detail/lighthouse/blipmdconlkpinefehnmjammfjpmpbjk"
                  target="_blank">Lighthouse</a> - Ferramenta de auditoria que inclui acessibilidade
              </li>
            </ul>
          </li>
          <li><strong>Leitores de Tela:</strong>
            <ul>
              <li><a href="https://www.nvaccess.org/download/" target="_blank">NVDA</a> (Windows) - Leitor
                de tela gratuito</li>
              <li><a href="https://www.apple.com/accessibility/mac/vision/" target="_blank">VoiceOver</a>
                (macOS/iOS) - Leitor de tela integrado</li>
              <li><a href="https://www.freedomscientific.com/products/software/jaws/" target="_blank">JAWS</a> (Windows)
                - Leitor de tela profissional</li>
            </ul>
          </li>
        </ul>
      </div>

      <div class="best-practice">
        <h4>Checklist de Testes de Acessibilidade</h4>
        <ul>
          <li><strong>Semântica HTML:</strong>
            <ul>
              <li>Uso apropriado de elementos de cabeçalho (h1-h6)</li>
              <li>Uso de elementos semânticos (nav, main, article, etc.)</li>
              <li>Formulários com labels associados corretamente</li>
            </ul>
          </li>
          <li><strong>Navegação por Teclado:</strong>
            <ul>
              <li>Todos os elementos interativos são acessíveis via teclado</li>
              <li>Ordem de tabulação lógica</li>
              <li>Indicadores de foco visíveis</li>
              <li>Sem armadilhas de foco (exceto em modais)</li>
            </ul>
          </li>
          <li><strong>Atributos ARIA:</strong>
            <ul>
              <li>Uso correto de roles, states e properties</li>
              <li>Regiões dinâmicas com aria-live apropriado</li>
              <li>Elementos personalizados com roles adequados</li>
            </ul>
          </li>
          <li><strong>Textos Alternativos:</strong>
            <ul>
              <li>Imagens com alt text apropriado</li>
              <li>Ícones com aria-label ou texto alternativo</li>
              <li>Elementos decorativos com alt="" ou aria-hidden="true"</li>
            </ul>
          </li>
          <li><strong>Contraste e Cores:</strong>
            <ul>
              <li>Contraste de texto suficiente (4.5:1 para texto normal, 3:1 para texto grande)</li>
              <li>Informações não transmitidas apenas por cor</li>
            </ul>
          </li>
          <li><strong>Conteúdo Multimídia:</strong>
            <ul>
              <li>Vídeos com legendas</li>
              <li>Áudio com transcrições</li>
              <li>Controles de mídia acessíveis por teclado</li>
            </ul>
          </li>
          <li><strong>Responsividade:</strong>
            <ul>
              <li>Conteúdo legível em zoom de 200%</li>
              <li>Funcionalidade preservada em diferentes tamanhos de tela</li>
              <li>Orientação não restrita</li>
            </ul>
          </li>
          <li><strong>Interações Complexas:</strong>
            <ul>
              <li>Modais e diálogos implementados corretamente</li>
              <li>Menus suspensos e tooltips acessíveis</li>
              <li>Arrastar e soltar com alternativas por teclado</li>
            </ul>
          </li>
        </ul>
      </div>
    </div>

    <div id="recursos-adicionais" class="subsection">
      <h3>8.7. Recursos Adicionais</h3>

      <div class="resource-list">
        <h4>Documentação e Guias</h4>
        <ul>
          <li><a href="https://www.w3.org/WAI/standards-guidelines/wcag/" target="_blank">Web Content
              Accessibility Guidelines (WCAG)</a> - Diretrizes oficiais de acessibilidade web</li>
          <li><a href="https://www.w3.org/WAI/ARIA/apg/" target="_blank">ARIA Authoring Practices Guide</a> -
            Guia de práticas para implementação de ARIA</li>
          <li><a href="https://vue-a11y.com/" target="_blank">Vue A11y Project</a> - Recursos de
            acessibilidade específicos para Vue.js</li>
          <li><a href="https://a11y-style-guide.com/style-guide/" target="_blank">A11Y Style Guide</a> - Guia
            de estilo para componentes acessíveis</li>
          <li><a href="https://inclusive-components.design/" target="_blank">Inclusive Components</a> - Blog
            sobre design de componentes acessíveis</li>
        </ul>
      </div>

      <div class="resource-list">
        <h4>Bibliotecas e Componentes</h4>
        <ul>
          <li><a href="https://github.com/vue-a11y/vue-announcer" target="_blank">vue-announcer</a> -
            Componente para anúncios de leitores de tela</li>
          <li><a href="https://github.com/vue-a11y/vue-skip-to" target="_blank">vue-skip-to</a> - Componente
            "pular para o conteúdo"</li>
          <li><a href="https://github.com/vue-a11y/vue-axe" target="_blank">vue-axe</a> - Ferramenta de teste
            de acessibilidade para Vue</li>
          <li><a href="https://github.com/vue-a11y/vue-focus-trap" target="_blank">vue-focus-trap</a> -
            Componente para armadilha de foco</li>
          <li><a href="https://github.com/vue-a11y/vue-dark-mode" target="_blank">vue-dark-mode</a> -
            Implementação de modo escuro acessível</li>
        </ul>
      </div>

      <div class="resource-list">
        <h4>Ferramentas de Teste</h4>
        <ul>
          <li><a href="https://wave.webaim.org/" target="_blank">WAVE</a> - Ferramenta de avaliação de
            acessibilidade web</li>
          <li><a href="https://accessibilityinsights.io/" target="_blank">Accessibility Insights</a> -
            Ferramenta da Microsoft para testes de acessibilidade</li>
          <li><a href="https://color.a11y.com/" target="_blank">Color Contrast Checker</a> - Verificador de
            contraste de cores</li>
          <li><a href="https://pa11y.org/" target="_blank">Pa11y</a> - Ferramenta de linha de comando para
            testes de acessibilidade</li>
          <li><a href="https://www.deque.com/axe/" target="_blank">axe</a> - Conjunto de ferramentas para
            testes de acessibilidade</li>
        </ul>
      </div>

      <div class="resource-list">
        <h4>Cursos e Tutoriais</h4>
        <ul>
          <li><a href="https://www.udacity.com/course/web-accessibility--ud891" target="_blank">Web
              Accessibility by Google (Udacity)</a> - Curso gratuito sobre acessibilidade web</li>
          <li><a href="https://egghead.io/courses/start-building-accessible-web-applications-today"
              target="_blank">Start Building Accessible Web Applications Today (egghead.io)</a> - Curso
            sobre desenvolvimento web acessível</li>
          <li><a href="https://www.w3.org/WAI/tutorials/" target="_blank">W3C Web Accessibility Tutorials</a>
            - Tutoriais oficiais do W3C</li>
          <li><a href="https://webaim.org/articles/" target="_blank">WebAIM Articles</a> - Artigos sobre
            acessibilidade web</li>
          <li><a href="https://a11ycasts.com/" target="_blank">A11ycasts</a> - Série de vídeos sobre
            acessibilidade web</li>
        </ul>
      </div>

      <div class="resource-list">
        <h4>Comunidades</h4>
        <ul>
          <li><a href="https://discord.gg/vue" target="_blank">Vue.js Discord</a> - Comunidade oficial do Vue.js com
            canais dedicados a componentes e UI</li>
          <li><a href="https://forum.vuejs.org/" target="_blank">Fórum Vue.js</a> - Fórum oficial para discussões sobre
            Vue.js</li>
          <li><a href="https://www.reddit.com/r/vuejs/" target="_blank">r/vuejs</a> - Subreddit dedicado ao Vue.js</li>
          <li><a href="https://stackoverflow.com/questions/tagged/vue.js" target="_blank">Stack Overflow</a> - Perguntas
            e respostas sobre Vue.js</li>
          <li><a href="https://twitter.com/vuejs" target="_blank">Vue.js no Twitter</a> - Atualizações e notícias sobre
            Vue.js</li>
        </ul>
      </div>

      <div class="resource-list">
        <h4>Ferramentas de Design</h4>
        <ul>
          <li><a href="https://www.figma.com/" target="_blank">Figma</a> - Ferramenta de design colaborativo</li>
          <li><a href="https://www.sketch.com/" target="_blank">Sketch</a> - Ferramenta de design para macOS</li>
          <li><a href="https://www.adobe.com/products/xd.html" target="_blank">Adobe XD</a> - Ferramenta de design e
            prototipagem</li>
          <li><a href="https://zeplin.io/" target="_blank">Zeplin</a> - Ferramenta de colaboração entre designers e
            desenvolvedores</li>
          <li><a href="https://www.invisionapp.com/" target="_blank">InVision</a> - Plataforma de design digital</li>
        </ul>
      </div>
    </div>
  </section>

  <section id="boas-praticas" class="manual-section">
    <h2>9. Boas Práticas</h2>
    <p>Seguir boas práticas de desenvolvimento é essencial para criar componentes Vue.js de alta qualidade,
      reutilizáveis e fáceis de manter.</p>

    <div id="reutilizacao" class="subsection">
      <h3>9.1. Reutilização de Componentes</h3>
      <p>Componentes reutilizáveis são a base de uma aplicação Vue.js bem estruturada. Eles permitem consistência visual
        e comportamental, além de reduzir a duplicação de código.</p>

      <div class="best-practice">
        <h4>Princípios para Componentes Reutilizáveis</h4>
        <ul>
          <li><strong>Responsabilidade única</strong>: Cada componente deve ter uma única responsabilidade</li>
          <li><strong>Props bem definidas</strong>: Use props com tipos e valores padrão claros</li>
          <li><strong>Eventos personalizados</strong>: Emita eventos para comunicação com componentes pai</li>
          <li><strong>Slots flexíveis</strong>: Use slots para permitir personalização do conteúdo</li>
          <li><strong>Estilos encapsulados</strong>: Use scoped styles ou CSS modules para evitar conflitos</li>
          <li><strong>Documentação clara</strong>: Documente props, eventos e exemplos de uso</li>
        </ul>
      </div>

      <div class="code-block">
        <h4>Exemplo de Componente Reutilizável</h4>
        <pre><code>&lt;template&gt;
  &lt;div 
    class="custom-card" 
    :class="[`card-${variant}`, { 'card-hover': hover }]"
  &gt;
    &lt;div v-if="$slots.header || title" class="card-header"&gt;
      &lt;slot name="header"&gt;
        &lt;h3 class="card-title"&gt;{{ title }}&lt;/h3&gt;
      &lt;/slot&gt;
    &lt;/div&gt;
    
    &lt;div class="card-body"&gt;
      &lt;slot&gt;&lt;/slot&gt;
    &lt;/div&gt;
    
    &lt;div v-if="$slots.footer" class="card-footer"&gt;
      &lt;slot name="footer"&gt;&lt;/slot&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { defineProps } from 'vue';

/**
 * Card component for displaying content in a boxed format
 * @displayName CustomCard
 */
const props = defineProps({
  /**
   * Card title (used if no header slot is provided)
   */
  title: {
    type: String,
    default: ''
  },
  /**
   * Card visual variant
   * @values default, primary, secondary, outline
   */
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'secondary', 'outline'].includes(value)
  },
  /**
   * Whether to show hover effect
   */
  hover: {
    type: Boolean,
    default: false
  }
});
&lt;/script&gt;

&lt;style scoped&gt;
.custom-card {
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.card-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.card-body {
  padding: 1rem;
}

.card-footer {
  padding: 1rem;
  border-top: 1px solid #eee;
}

/* Variants */
.card-primary {
  border-top: 3px solid #3490dc;
}

.card-secondary {
  border-top: 3px solid #6c757d;
}

.card-outline {
  box-shadow: none;
  border: 1px solid #eee;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Uso do Componente Reutilizável</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="container"&gt;
    &lt;h2&gt;Exemplos de Cards&lt;/h2&gt;
    
    &lt;div class="row"&gt;
      &lt;!-- Card básico --&gt;
      &lt;div class="col-md-4"&gt;
        &lt;custom-card title="Card Básico"&gt;
          &lt;p&gt;Este é um card básico com título e conteúdo.&lt;/p&gt;
        &lt;/custom-card&gt;
      &lt;/div&gt;
      
      &lt;!-- Card com slots --&gt;
      &lt;div class="col-md-4"&gt;
        &lt;custom-card variant="primary" hover&gt;
          &lt;template #header&gt;
            &lt;div class="d-flex justify-content-between align-items-center"&gt;
              &lt;h3 class="card-title"&gt;Card com Slots&lt;/h3&gt;
              &lt;span class="badge bg-primary"&gt;Novo&lt;/span&gt;
            &lt;/div&gt;
          &lt;/template&gt;
          
          &lt;p&gt;Este card usa slots para personalizar o cabeçalho e rodapé.&lt;/p&gt;
          
          &lt;template #footer&gt;
            &lt;div class="d-flex justify-content-end"&gt;
              &lt;button class="btn btn-sm btn-primary"&gt;Ação&lt;/button&gt;
            &lt;/div&gt;
          &lt;/template&gt;
        &lt;/custom-card&gt;
      &lt;/div&gt;
      
      &lt;!-- Card outline --&gt;
      &lt;div class="col-md-4"&gt;
        &lt;custom-card variant="outline" hover&gt;
          &lt;template #header&gt;
            &lt;h3 class="card-title"&gt;Card Outline&lt;/h3&gt;
          &lt;/template&gt;
          
          &lt;p&gt;Este é um card com estilo outline e efeito hover.&lt;/p&gt;
          
          &lt;template #footer&gt;
            &lt;small class="text-muted"&gt;Última atualização: 2 dias atrás&lt;/small&gt;
          &lt;/template&gt;
        &lt;/custom-card&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;</code></pre>
      </div>
    </div>

    <div id="consistencia" class="subsection">
      <h3>9.2. Consistência Visual</h3>
      <p>A consistência visual é fundamental para criar uma experiência de usuário coesa e profissional. Ela ajuda os
        usuários a entender e prever como a interface funciona.</p>

      <div class="best-practice">
        <h4>Princípios de Consistência Visual</h4>
        <ul>
          <li><strong>Sistema de design</strong>: Siga um sistema de design bem definido</li>
          <li><strong>Componentes padronizados</strong>: Use os mesmos componentes para funções similares</li>
          <li><strong>Espaçamento consistente</strong>: Mantenha margens e paddings consistentes</li>
          <li><strong>Hierarquia visual</strong>: Mantenha uma hierarquia clara de elementos</li>
          <li><strong>Feedback visual</strong>: Forneça feedback visual consistente para interações</li>
          <li><strong>Terminologia</strong>: Use termos consistentes em toda a interface</li>
        </ul>
      </div>

      <div class="code-block">
        <h4>Exemplo de Sistema de Tokens de Design</h4>
        <pre><code>// src/styles/tokens.scss

// Cores
$color-primary: #3490dc;
$color-secondary: #6c757d;
$color-success: #38c172;
$color-danger: #e3342f;
$color-warning: #ffed4a;
$color-info: #6cb2eb;
$color-light: #f8f9fa;
$color-dark: #343a40;

// Variações de cores primárias
$color-primary-light: #64a8e6;
$color-primary-dark: #2779bd;

// Cores de texto
$text-default: #212529;
$text-muted: #6c757d;
$text-light: #f8f9fa;

// Espaçamento
$spacing-xs: 0.25rem;  // 4px
$spacing-sm: 0.5rem;   // 8px
$spacing-md: 1rem;     // 16px
$spacing-lg: 1.5rem;   // 24px
$spacing-xl: 2rem;     // 32px
$spacing-xxl: 3rem;    // 48px

// Tipografia
$font-family-base: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-size-base: 0.9rem;
$font-size-sm: 0.8rem;
$font-size-lg: 1rem;
$font-size-xl: 1.25rem;
$font-size-xxl: 1.5rem;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;

// Bordas
$border-radius-sm: 0.2rem;
$border-radius-md: 0.25rem;
$border-radius-lg: 0.5rem;
$border-radius-xl: 1rem;
$border-radius-pill: 50rem;

$border-width: 1px;
$border-color: #dee2e6;

// Sombras
$shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
$shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
$shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

// Transições
$transition-base: all 0.2s ease-in-out;
$transition-slow: all 0.3s ease-in-out;
$transition-fast: all 0.1s ease-in-out;

// Breakpoints
$breakpoint-xs: 0;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1400px;</code></pre>
      </div>

      <div class="code-block">
        <h4>Uso de Tokens de Design em Componentes</h4>
        <pre><code>// src/components/Button.vue
&lt;template&gt;
  &lt;button 
    class="btn" 
    :class="[`btn-${variant}`, `btn-${size}`, { 'btn-block': block }]"
    :disabled="disabled"
  &gt;
    &lt;slot&gt;&lt;/slot&gt;
  &lt;/button&gt;
&lt;/template&gt;

&lt;script setup&gt;
defineProps({
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark', 'link'].includes(value)
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  block: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
});
&lt;/script&gt;

&lt;style lang="scss" scoped&gt;
@import '@/styles/tokens.scss';

.btn {
  display: inline-block;
  font-weight: $font-weight-medium;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  border: $border-width solid transparent;
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-base;
  line-height: 1.5;
  border-radius: $border-radius-md;
  transition: $transition-base;
  
  &:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba($color-primary, 0.25);
  }
  
  &:disabled {
    opacity: 0.65;
    pointer-events: none;
  }
}

// Variantes
.btn-primary {
  background-color: $color-primary;
  border-color: $color-primary;
  color: $text-light;
  
  &:hover, &:focus {
    background-color: $color-primary-dark;
    border-color: darken($color-primary-dark, 5%);
  }
}

.btn-secondary {
  background-color: $color-secondary;
  border-color: $color-secondary;
  color: $text-light;
  
  &:hover, &:focus {
    background-color: darken($color-secondary, 10%);
    border-color: darken($color-secondary, 15%);
  }
}

// Tamanhos
.btn-sm {
  padding: $spacing-xs $spacing-sm;
  font-size: $font-size-sm;
  border-radius: $border-radius-sm;
}

.btn-md {
  // Já definido no .btn
}

.btn-lg {
  padding: $spacing-md $spacing-lg;
  font-size: $font-size-lg;
  border-radius: $border-radius-lg;
}

// Block
.btn-block {
  display: block;
  width: 100%;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Dicas para Manter Consistência Visual</h4>
        <ul>
          <li>Crie e mantenha uma biblioteca de componentes</li>
          <li>Use variáveis CSS ou tokens de design para valores recorrentes</li>
          <li>Implemente um sistema de grid consistente</li>
          <li>Documente padrões visuais e de interação</li>
          <li>Realize revisões regulares de design para identificar inconsistências</li>
          <li>Use ferramentas de linting para CSS/SCSS para garantir padrões consistentes</li>
          <li>Considere usar ferramentas como Storybook para documentar componentes visuais</li>
        </ul>
      </div>
    </div>

    <div id="performance" class="subsection">
      <h3>9.3. Performance</h3>
      <p>Otimizar a performance dos componentes Vue.js é essencial para criar uma experiência de usuário fluida e
        responsiva, especialmente em aplicações complexas.</p>

      <div class="best-practice">
        <h4>Estratégias de Otimização de Performance</h4>
        <ul>
          <li><strong>Lazy loading</strong>: Carregue componentes apenas quando necessário</li>
          <li><strong>Virtualização</strong>: Renderize apenas os itens visíveis em listas longas</li>
          <li><strong>Memoização</strong>: Use computed properties e v-memo para evitar recálculos desnecessários</li>
          <li><strong>Renderização condicional</strong>: Use v-if e v-show apropriadamente</li>
          <li><strong>Otimização de reatividade</strong>: Estruture dados para minimizar atualizações em cascata</li>
          <li><strong>Debounce e throttle</strong>: Limite a frequência de eventos como scroll e resize</li>
        </ul>
      </div>

      <div class="code-block">
        <h4>Lazy Loading de Componentes</h4>
        <pre><code>// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import(/* webpackChunkName: "home" */ '../views/Home.vue')
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import(/* webpackChunkName: "dashboard" */ '../views/Dashboard.vue'),
    children: [
      {
        path: 'analytics',
        name: 'Analytics',
        component: () => import(/* webpackChunkName: "analytics" */ '../views/Analytics.vue')
      },
      {
        path: 'reports',
        name: 'Reports',
        component: () => import(/* webpackChunkName: "reports" */ '../views/Reports.vue')
      }
    ]
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import(/* webpackChunkName: "users" */ '../views/Users.vue')
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;</code></pre>
      </div>

      <div class="code-block">
        <h4>Virtualização de Lista</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="virtual-list-container" ref="container"&gt;
    &lt;div 
      class="virtual-list" 
      :style="{ height: totalHeight + 'px', paddingTop: offsetY + 'px' }"
    &gt;
      &lt;div 
        v-for="item in visibleItems" 
        :key="item.id" 
        class="list-item"
        :style="{ height: itemHeight + 'px' }"
      &gt;
        &lt;slot name="item" :item="item"&gt;
          {{ item.text }}
        &lt;/slot&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  itemHeight: {
    type: Number,
    default: 50
  },
  buffer: {
    type: Number,
    default: 5
  }
});

const container = ref(null);
const scrollTop = ref(0);
const viewportHeight = ref(0);

// Calcular altura total da lista
const totalHeight = computed(() => {
  return props.items.length * props.itemHeight;
});

// Calcular índice do primeiro item visível
const startIndex = computed(() => {
  return Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - props.buffer);
});

// Calcular índice do último item visível
const endIndex = computed(() => {
  return Math.min(
    props.items.length - 1,
    Math.ceil((scrollTop.value + viewportHeight.value) / props.itemHeight) + props.buffer
  );
});

// Calcular deslocamento Y para posicionar corretamente os itens visíveis
const offsetY = computed(() => {
  return startIndex.value * props.itemHeight;
});

// Filtrar apenas os itens visíveis
const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value + 1);
});

// Atualizar scrollTop quando o usuário rola
const handleScroll = () => {
  if (container.value) {
    scrollTop.value = container.value.scrollTop;
  }
};

// Atualizar altura do viewport quando a janela é redimensionada
const handleResize = () => {
  if (container.value) {
    viewportHeight.value = container.value.clientHeight;
  }
};

onMounted(() => {
  if (container.value) {
    viewportHeight.value = container.value.clientHeight;
    container.value.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);
  }
});

onUnmounted(() => {
  if (container.value) {
    container.value.removeEventListener('scroll', handleScroll);
    window.removeEventListener('resize', handleResize);
  }
});
&lt;/script&gt;

&lt;style scoped&gt;
.virtual-list-container {
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.virtual-list {
  position: relative;
  width: 100%;
}

.list-item {
  width: 100%;
  box-sizing: border-box;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
}
&lt;/style&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Otimização de Reatividade com v-once e v-memo</h4>
        <pre><code>&lt;template&gt;
  &lt;div class="dashboard"&gt;
    &lt;!-- Conteúdo estático renderizado apenas uma vez --&gt;
    &lt;header v-once&gt;
      &lt;h1&gt;{{ appTitle }}&lt;/h1&gt;
      &lt;p&gt;Dashboard atualizado em tempo real&lt;/p&gt;
    &lt;/header&gt;
    
    &lt;!-- Componente que só rerenderiza quando userId muda --&gt;
    &lt;user-profile v-memo="[userId]" :user-id="userId" /&gt;
    
    &lt;!-- Lista otimizada que só rerenderiza itens quando necessário --&gt;
    &lt;ul&gt;
      &lt;li 
        v-for="item in items" 
        :key="item.id"
        v-memo="[item.id, item.isComplete]"
      &gt;
        &lt;span :class="{ 'completed': item.isComplete }"&gt;{{ item.text }}&lt;/span&gt;
        &lt;button @click="toggleItem(item.id)"&gt;Toggle&lt;/button&gt;
      &lt;/li&gt;
    &lt;/ul&gt;
    
    &lt;!-- Dados que mudam frequentemente --&gt;
    &lt;div class="stats"&gt;
      &lt;p&gt;Itens ativos: {{ activeItemsCount }}&lt;/p&gt;
      &lt;p&gt;Última atualização: {{ lastUpdated }}&lt;/p&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue';
import UserProfile from './UserProfile.vue';

const appTitle = 'Dashboard App';
const userId = ref(42);
const lastUpdated = ref(new Date().toLocaleTimeString());
const items = ref([
  { id: 1, text: 'Item 1', isComplete: false },
  { id: 2, text: 'Item 2', isComplete: true },
  { id: 3, text: 'Item 3', isComplete: false }
]);

// Computed property - só recalcula quando items muda
const activeItemsCount = computed(() => {
  console.log('Recalculando itens ativos');
  return items.value.filter(item => !item.isComplete).length;
});

// Método para alternar o estado de um item
const toggleItem = (id) => {
  const item = items.value.find(item => item.id === id);
  if (item) {
    item.isComplete = !item.isComplete;
    lastUpdated.value = new Date().toLocaleTimeString();
  }
};
&lt;/script&gt;</code></pre>
      </div>

      <div class="code-block">
        <h4>Debounce e Throttle para Eventos Frequentes</h4>
        <pre><code>// src/composables/useDebounce.js
import { ref } from 'vue';

export function useDebounce() {
  const debounceTimeout = ref(null);
  
  const debounce = (fn, delay) => {
    return (...args) => {
      if (debounceTimeout.value) {
        clearTimeout(debounceTimeout.value);
      }
      
      debounceTimeout.value = setTimeout(() => {
        fn(...args);
        debounceTimeout.value = null;
      }, delay);
    };
  };
  
  const throttle = (fn, limit) => {
    let inThrottle = false;
    
    return (...args) => {
      if (!inThrottle) {
        fn(...args);
        inThrottle = true;
        setTimeout(() => {
          inThrottle = false;
        }, limit);
      }
    };
  };
  
  return {
    debounce,
    throttle
  };
}

// Uso em um componente
&lt;template&gt;
  &lt;div&gt;
    &lt;input 
      type="text" 
      v-model="searchQuery" 
      @input="debouncedSearch" 
      placeholder="Pesquisar..."
    &gt;
    
    &lt;div 
      class="scroll-container" 
      @scroll="throttledHandleScroll"
      ref="scrollContainer"
    &gt;
      &lt;!-- Conteúdo rolável --&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';
import { useDebounce } from '@/composables/useDebounce';

const { debounce, throttle } = useDebounce();
const searchQuery = ref('');
const scrollContainer = ref(null);

// Debounce para pesquisa (espera o usuário parar de digitar)
const debouncedSearch = debounce(() => {
  console.log('Pesquisando por:', searchQuery.value);
  // Implementar lógica de pesquisa aqui
}, 300);

// Throttle para evento de scroll (limita a frequência de execução)
const throttledHandleScroll = throttle(() => {
  if (!scrollContainer.value) return;
  
  const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value;
  
  // Detectar quando o usuário está próximo do final
  if (scrollTop + clientHeight >= scrollHeight - 50) {
    console.log('Próximo do final, carregando mais itens...');
    // Implementar carregamento de mais itens
  }
}, 200);
&lt;/script&gt;</code></pre>
      </div>

      <div class="best-practice">
        <h4>Dicas Adicionais de Performance</h4>
        <ul>
          <li>Use <code>shallowRef</code> e <code>shallowReactive</code> para objetos grandes quando a reatividade
            profunda não for necessária</li>
          <li>Evite observar objetos muito grandes ou profundamente aninhados</li>
          <li>Divida componentes complexos em componentes menores</li>
          <li>Use <code>defineAsyncComponent</code> para componentes pesados que não são necessários imediatamente</li>
          <li>Otimize imagens e assets para carregamento rápido</li>
          <li>Implemente code-splitting para reduzir o tamanho do bundle inicial</li>
          <li>Use ferramentas de análise de performance como Vue DevTools e Lighthouse</li>
          <li>Considere o uso de SSR (Server-Side Rendering) ou SSG (Static Site Generation) para melhorar o tempo de
            carregamento inicial</li>
        </ul>
      </div>
    </div>
  </section>

  <section id="conclusao" class="manual-section">
    <h2>10. Conclusão</h2>
    <p>Este manual fornece uma base sólida para o desenvolvimento de componentes Vue.js consistentes, acessíveis e de
      alta qualidade. Seguindo estas diretrizes e padrões, você poderá criar interfaces de usuário robustas e
      agradáveis.</p>

    <div class="conclusion-points">
      <h3>Pontos-chave a lembrar</h3>
      <ul>
        <li><strong>Consistência</strong>: Mantenha um design system coeso e aplique-o consistentemente</li>
        <li><strong>Reutilização</strong>: Projete componentes para serem reutilizáveis e compostos</li>
        <li><strong>Acessibilidade</strong>: Torne sua aplicação utilizável por todos os usuários</li>
        <li><strong>Responsividade</strong>: Garanta que a interface funcione bem em todos os dispositivos</li>
        <li><strong>Performance</strong>: Otimize seus componentes para uma experiência fluida</li>
        <li><strong>Documentação</strong>: Documente seus componentes para facilitar o uso pela equipe</li>
      </ul>
    </div>
  </section>

  <footer class="manual-footer">
    <p>&copy; 2023 - Sistema de Gestão - Todos os direitos reservados</p>
    <p>Versão 1.0 - Última atualização: Outubro/2023</p>
  </footer>
</body>

</html>