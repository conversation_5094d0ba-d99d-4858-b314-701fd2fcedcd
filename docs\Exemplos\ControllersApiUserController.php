<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\ControllerAbstract;
use App\Models\User;
use App\Services\UserService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

/**
 * Controlador de usuários para API
 * 
 * Gerencia endpoints relacionados a usuários
 */
class UserController extends ControllerAbstract
{
    /**
     * Instância da classe de resposta
     *
     * @var ApiResponse
     */
    protected $response;

    /**
     * Construtor
     *
     * @param UserService $service
     * @param ApiResponse $response
     */
    public function __construct(UserService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    /**
     * Lista todos os usuários
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', User::class);

        $perPage = $request->get('per_page', 15);
        $sortField = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Obter filtros da requisição
        $filters = $request->only(['name_like', 'email_like']);

        if ($request->has('paginate') && $request->paginate === 'false') {
            // Retornar todos os resultados filtrados
            $users = $this->service->getFiltered($filters, $sortField, $sortDirection);
            $transformedUsers = collect($users)->map(function ($user) {
                return $this->service->transformUser($user);
            })->all();

            return $this->response->success($transformedUsers);
        } else {
            // Retornar resultados paginados
            $paginator = $this->service->getPaginatedWithFilters(
                $perPage,
                $filters,
                $sortField,
                $sortDirection
            );

            $transformedItems = collect($paginator->items())->map(function ($user) {
                return $this->service->transformUser($user);
            })->all();

            return $this->response->paginated(
                $transformedItems,
                [
                    'current_page' => $paginator->currentPage(),
                    'last_page' => $paginator->lastPage(),
                    'per_page' => $paginator->perPage(),
                    'total' => $paginator->total()
                ]
            );
        }
    }

    /**
     * Exibe um usuário específico
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id, Request $request)
    {
        $user = $this->service->findWithRelations($id, $request->get('with', []));

        if (!$user) {
            return $this->response->notFound('Usuário não encontrado');
        }

        $this->authorize('view', $user);

        return $this->response->success(
            $this->service->transformUser($user)
        );
    }

    /**
     * Cria um novo usuário
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $this->authorize('create', User::class);

        try {
            $user = $this->service->createWithValidation($request->all());

            return $this->response->created(
                $this->service->transformUser($user),
                'Usuário criado com sucesso'
            );
        } catch (ValidationException $e) {
            return $this->response->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->response->error(
                'Erro ao criar usuário',
                500,
                ['exception' => $e->getMessage()]
            );
        }
    }

    /**
     * Atualiza um usuário existente
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update($id, Request $request)
    {
        $user = $this->service->find($id);

        if (!$user) {
            return $this->response->notFound('Usuário não encontrado');
        }

        $this->authorize('update', $user);

        try {
            $updatedUser = $this->service->updateWithValidation($id, $request->all());

            return $this->response->success(
                $this->service->transformUser($updatedUser),
                'Usuário atualizado com sucesso'
            );
        } catch (ValidationException $e) {
            return $this->response->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->response->error(
                'Erro ao atualizar usuário',
                500,
                ['exception' => $e->getMessage()]
            );
        }
    }

    /**
     * Remove um usuário
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $user = $this->service->find($id);

        if (!$user) {
            return $this->response->notFound('Usuário não encontrado');
        }

        $this->authorize('delete', $user);

        try {
            $this->service->delete($id);
            return $this->response->success(null, 'Usuário excluído com sucesso');
        } catch (\Exception $e) {
            return $this->response->error('Erro ao excluir usuário', 500);
        }
    }

    /**
     * Restaura um usuário excluído
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function restore($id)
    {
        $this->authorize('restore', User::class);

        try {
            if ($this->service->restore($id)) {
                $user = $this->service->find($id);
                return $this->response->success(
                    $this->service->transformUser($user),
                    'Usuário restaurado com sucesso'
                );
            }

            return $this->response->notFound('Usuário não encontrado ou não pode ser restaurado');
        } catch (\Exception $e) {
            return $this->response->error('Erro ao restaurar usuário', 500);
        }
    }
}
