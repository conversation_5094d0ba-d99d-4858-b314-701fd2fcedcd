<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Boas Práticas para Commits Git - Laravel 12</title>
    <link rel="stylesheet" href="css/manual.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Boas Práticas para Commits Git</h1>
        <p>Para projetos Laravel 12</p>
        <p class="version">Versão 2.0 - Última atualização: Maio 2023</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#mensagens">Mensagens de Commit</a></li>
            <li><a href="#estrutura">Estrutura de Commits</a></li>
            <li><a href="#workflow">Workflow</a></li>
            <li><a href="#ferramentas">Ferramentas</a></li>
            <li><a href="#problemas">Problemas Comuns</a></li>
            <li><a href="#laravel12">Laravel 12</a></li>
            <li><a href="#checklist">Checklist</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#mensagens">2. Mensagens de Commit</a>
                <ul>
                    <li><a href="#mensagens-anatomia">2.1. Anatomia de uma Boa Mensagem de Commit</a></li>
                    <li><a href="#mensagens-tipos">2.2. Tipos de Commit</a></li>
                    <li><a href="#mensagens-exemplos">2.3. Exemplos Comparativos</a></li>
                </ul>
            </li>
            <li><a href="#estrutura">3. Estrutura de Commits</a>
                <ul>
                    <li><a href="#estrutura-tamanho">3.1. Tamanho e Escopo</a></li>
                    <li><a href="#estrutura-feature">3.2. Estruturando Commits em Uma Feature</a></li>
                </ul>
            </li>
            <li><a href="#workflow">4. Workflow</a>
                <ul>
                    <li><a href="#workflow-branches">4.1. Commits e Branches</a></li>
                    <li><a href="#workflow-equipe">4.2. Commits em Equipe</a></li>
                </ul>
            </li>
            <li><a href="#ferramentas">5. Ferramentas</a>
                <ul>
                    <li><a href="#ferramentas-praticas">5.1. Automatizando Boas Práticas</a></li>
                    <li><a href="#ferramentas-analise">5.2. Análise de Commits</a></li>
                </ul>
            </li>
            <li><a href="#problemas">6. Problemas Comuns</a>
                <ul>
                    <li><a href="#problemas-situacoes">6.1. Situações Problemáticas e Soluções</a></li>
                    <li><a href="#problemas-prevencao">6.2. Prevenção de Problemas</a></li>
                </ul>
            </li>
            <li><a href="#laravel12">7. Commits para Laravel 12</a>
                <ul>
                    <li><a href="#laravel12-padroes">7.1. Padrões Específicos para Laravel 12</a></li>
                    <li><a href="#laravel12-estrategia">7.2. Estratégia de Migração</a></li>
                    <li><a href="#laravel12-exemplos">7.3. Exemplos de Sequência de Commits</a></li>
                </ul>
            </li>
            <li><a href="#checklist">8. Checklist de Boas Práticas</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>

        <div class="intro-text">
            <p>Commits são o alicerce do controle de versão. Cada commit representa um snapshot do seu código em um
                determinado
                momento e, quando bem feitos, contam a história do seu projeto de forma clara e organizada.</p>

            <p>Este manual apresenta as melhores práticas para a criação de commits efetivos que facilitam a
                colaboração, revisão de código e manutenção de projetos Laravel 12 a longo prazo.</p>
        </div>

        <div class="key-points">
            <h3>Por que commits bem feitos são importantes?</h3>
            <ul>
                <li>Facilitam o entendimento da evolução do código</li>
                <li>Permitem rastrear bugs e regressões mais facilmente</li>
                <li>Simplificam o processo de revisão de código</li>
                <li>Possibilitam a geração automática de changelogs</li>
                <li>Melhoram a colaboração em equipe</li>
                <li>Facilitam a manutenção de projetos complexos como aplicações Laravel</li>
            </ul>
        </div>

        <div class="alerts-section">
            <h4>Atualização Laravel 12</h4>
            <p>O Laravel 12 introduziu mudanças significativas na estrutura do framework, tornando ainda mais importante
                manter um histórico de commits claro e organizado para facilitar migrações e atualizações futuras.</p>
        </div>
    </section>

    <section id="mensagens" class="manual-section">
        <h2>2. Mensagens de Commit</h2>

        <div id="mensagens-anatomia" class="subsection">
            <h3>2.1. Anatomia de uma Boa Mensagem de Commit</h3>

            <div class="content-block">
                <p>Uma mensagem de commit completa segue uma estrutura específica que maximiza sua utilidade:</p>

                <div class="code-block">
                    <pre>
tipo(escopo): resumo conciso

Descrição detalhada do que foi feito e por quê.
Pode conter múltiplas linhas e até parágrafos.

- Pontos específicos podem ser listados
- Utilizando marcadores para facilitar a leitura

Resolve: #123
Relacionado: #456
                    </pre>
                </div>

                <div class="explanation">
                    <ul>
                        <li><strong>Tipo</strong>: Categoriza o commit (feat, fix, docs, style, refactor, test, chore)
                        </li>
                        <li><strong>Escopo</strong>: Opcional, indica a parte do código afetada (módulo, componente)
                        </li>
                        <li><strong>Resumo</strong>: Descrição breve e concisa, no imperativo presente (até 50
                            caracteres)</li>
                        <li><strong>Corpo</strong>: Detalhes do que e por que foi alterado (não o como)</li>
                        <li><strong>Rodapé</strong>: Referências a issues, breaking changes, etc.</li>
                    </ul>
                </div>
            </div>
        </div>

        <div id="mensagens-tipos" class="subsection">
            <h3>2.2. Tipos de Commit</h3>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Tipo</th>
                            <th>Descrição</th>
                            <th>Exemplo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>feat</td>
                            <td>Nova funcionalidade adicionada</td>
                            <td>feat(auth): adiciona autenticação por biometria</td>
                        </tr>
                        <tr>
                            <td>fix</td>
                            <td>Correção de bug</td>
                            <td>fix(api): corrige erro 500 ao enviar payload vazio</td>
                        </tr>
                        <tr>
                            <td>docs</td>
                            <td>Alterações na documentação</td>
                            <td>docs: atualiza README com instruções de instalação</td>
                        </tr>
                        <tr>
                            <td>style</td>
                            <td>Mudanças que não alteram o comportamento (formatação, espaços)</td>
                            <td>style: aplica padrão de formatação do linter</td>
                        </tr>
                        <tr>
                            <td>refactor</td>
                            <td>Alterações no código que não corrigem bugs nem adicionam features</td>
                            <td>refactor(orders): reorganiza estrutura do serviço de pedidos</td>
                        </tr>
                        <tr>
                            <td>test</td>
                            <td>Adições ou correções de testes</td>
                            <td>test(cart): adiciona testes para regras de desconto</td>
                        </tr>
                        <tr>
                            <td>chore</td>
                            <td>Tarefas de manutenção, ajustes de build, etc.</td>
                            <td>chore: atualiza dependências para as últimas versões</td>
                        </tr>
                        <tr>
                            <td>perf</td>
                            <td>Melhorias de performance</td>
                            <td>perf(queries): otimiza consulta na listagem de produtos</td>
                        </tr>
                        <tr>
                            <td>build</td>
                            <td>Alterações no sistema de build ou dependências externas</td>
                            <td>build: atualiza Vite para versão 5.0</td>
                        </tr>
                        <tr>
                            <td>ci</td>
                            <td>Alterações em configurações de CI/CD</td>
                            <td>ci: adiciona workflow de testes no GitHub Actions</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="best-practice">
                <h4>Para que o versionamento semântico funcione bem:</h4>
                <ul>
                    <li>Prefixe breaking changes com <code>BREAKING CHANGE:</code> no corpo do commit</li>
                    <li>Use <code>!</code> após o tipo para indicar uma mudança significativa:
                        <code>feat!: remove suporte a API legada</code>
                    </li>
                    <li>Seja consistente com os tipos de commit em todo o projeto</li>
                </ul>
            </div>
        </div>

        <div id="mensagens-exemplos" class="subsection">
            <h3>2.3. Exemplos Comparativos</h3>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Ruim</th>
                            <th>Bom</th>
                            <th>Explicação</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>correções</td>
                            <td>fix(checkout): corrige cálculo de frete para CEPs internacionais</td>
                            <td>A boa mensagem indica o escopo e descreve especificamente o que foi corrigido</td>
                        </tr>
                        <tr>
                            <td>Adicionei login com Google</td>
                            <td>feat(auth): implementa autenticação via Google OAuth</td>
                            <td>A segunda opção usa o tipo correto e o tempo verbal imperativo</td>
                        </tr>
                        <tr>
                            <td>Atualizei os testes porque estavam quebrados</td>
                            <td>test: corrige testes de integração após mudanças na API</td>
                            <td>A boa mensagem explica o que foi feito de forma concisa</td>
                        </tr>
                        <tr>
                            <td>Atualização para Laravel 12</td>
                            <td>chore!: atualiza framework para Laravel 12</td>
                            <td>A boa mensagem indica que é uma mudança significativa (!) e usa o tipo correto</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="alerts-section">
                <h4>Exemplos específicos para Laravel 12</h4>
                <ul>
                    <li><code>refactor(bootstrap): adapta bootstrap/app.php para novo formato do Laravel 12</code></li>
                    <li><code>fix(types): adiciona tipagem estrita em controllers conforme padrão Laravel 12</code></li>
                    <li><code>feat(enums): converte constantes de status para PHP Enums</code></li>
                    <li><code>chore(deps): atualiza dependências para compatibilidade com Laravel 12</code></li>
                </ul>
            </div>
        </div>
    </section>

    <section id="estrutura" class="manual-section">
        <h2>3. Estrutura de Commits</h2>

        <div id="estrutura-tamanho" class="subsection">
            <h3>3.1. Tamanho e Escopo</h3>

            <div class="content-block">
                <p>Commits devem ser atômicos: representar uma única alteração lógica que pode ser entendida
                    isoladamente, testada e revertida se necessário.</p>

                <div class="comparison-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Práticas Ruins</th>
                                <th>Boas Práticas</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <ul>
                                        <li>Commits muito grandes que incluem múltiplas alterações não relacionadas</li>
                                        <li>Commits com mensagens como "WIP" ou "Fix stuff"</li>
                                        <li>Múltiplos commits para uma única alteração lógica</li>
                                        <li>Commits que quebram a build ou os testes</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li>Cada commit representa uma alteração lógica completa</li>
                                        <li>O sistema continua funcionando após cada commit</li>
                                        <li>Testes passam para cada commit</li>
                                        <li>Fácil de entender o propósito do commit apenas pela mensagem</li>
                                    </ul>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="estrutura-feature" class="subsection">
            <h3>3.2. Estruturando Commits em Uma Feature</h3>

            <div class="content-block">
                <p>Ao implementar uma funcionalidade completa, estruture seus commits para contar uma história lógica:
                </p>

                <div class="example-commits">
                    <pre>
feat(models): adiciona schema do modelo de Pedido
test(models): adiciona testes unitários para modelo de Pedido
feat(controllers): implementa endpoints básicos de listagem de Pedidos
feat(controllers): adiciona filtros avançados na API de Pedidos 
docs(api): atualiza documentação da API com novos endpoints de Pedidos
test(api): adiciona testes de integração para API de Pedidos
                    </pre>
                </div>

                <div class="tip">
                    <h4>Dica para manter commits atômicos:</h4>
                    <p>Use <code>git add -p</code> para selecionar partes específicas de arquivos modificados.
                        Isso permite criar commits coesos mesmo quando você modificou diferentes aspectos em um mesmo
                        arquivo.</p>
                </div>
            </div>

            <div class="alerts-section">
                <h4>Exemplo de sequência de commits para migração para Laravel 12</h4>
                <div class="example-commits">
                    <pre>
chore(deps): atualiza requisitos para PHP 8.2 e Laravel 12
refactor(bootstrap): adapta bootstrap/app.php para novo formato do Laravel 12
fix(types): adiciona tipagem estrita em controllers
fix(types): adiciona tipagem estrita em services
fix(types): adiciona tipagem estrita em repositories
refactor(enums): converte constantes de status para PHP Enums
test: atualiza testes para compatibilidade com Laravel 12
docs: atualiza documentação com mudanças do Laravel 12
                    </pre>
                </div>
            </div>
        </div>
    </section>

    <section id="workflow" class="manual-section">
        <h2>4. Workflow</h2>

        <div id="workflow-branches" class="subsection">
            <h3>4.1. Commits e Branches</h3>

            <div class="content-block">
                <p>Um bom workflow de Git inclui uma estratégia clara para branches e commits:</p>

                <div class="best-practice">
                    <h4>Gitflow Workflow</h4>
                    <ul>
                        <li><strong>main/master</strong>: Contém código estável e pronto para produção</li>
                        <li><strong>develop</strong>: Branch de integração para novas features</li>
                        <li><strong>feature/*</strong>: Branches para desenvolvimento de novas funcionalidades</li>
                        <li><strong>hotfix/*</strong>: Branches para correções urgentes em produção</li>
                        <li><strong>release/*</strong>: Branches para preparação de releases</li>
                    </ul>

                    <p>Com o Gitflow, os commits em feature branches podem ser mais granulares, enquanto os merges para
                        develop e main são mais consolidados via squash ou rebase interativo.</p>
                </div>

                <div class="best-practice">
                    <h4>Trunk-Based Development</h4>
                    <ul>
                        <li>Uso de branch principal como base principal de desenvolvimento</li>
                        <li>Branches de feature de curta duração</li>
                        <li>Integração contínua com merges frequentes ao trunk</li>
                        <li>Uso de feature flags para funcionalidades incompletas</li>
                    </ul>

                    <p>No trunk-based development, commits precisam ser ainda mais atômicos e não quebrar a build, já
                        que são
                        integrados frequentemente à branch principal.</p>
                </div>
            </div>
        </div>

        <div id="workflow-equipe" class="subsection">
            <h3>4.2. Commits em Equipe</h3>

            <div class="content-block">
                <div class="best-practice">
                    <h4>Boas Práticas</h4>
                    <ul>
                        <li>Estabeleça um padrão de commits e o documente (ex: Conventional Commits)</li>
                        <li>Utilize hooks de pre-commit para validar mensagens de commit</li>
                        <li>Faça commits frequentes em sua branch local</li>
                        <li>Antes de pushar, considere usar rebase interativo para organizar commits</li>
                        <li>Não reescreva histórico já publicado (commits que outros já baixaram)</li>
                        <li>Cada Pull Request deve ser fácil de revisar, com commits bem organizados</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Reescrevendo histórico local para limpeza:</h4>
                    <pre>
# Reorganizar os últimos 3 commits
git rebase -i HEAD~3

# No editor interativo, você pode:
# - reordenar commits (mudando a ordem das linhas)
# - combinar commits (usando 'squash' ou 'fixup')
# - editar mensagens de commit (usando 'reword')
# - dividir commits (usando 'edit')
                    </pre>
                </div>
            </div>
        </div>
    </section>

    <section id="ferramentas" class="manual-section">
        <h2>5. Ferramentas</h2>

        <div id="ferramentas-praticas" class="subsection">
            <h3>5.1. Automatizando Boas Práticas</h3>

            <div class="tools-list">
                <h4>Ferramentas Recomendadas</h4>
                <ul>
                    <li>
                        <strong>commitlint</strong>
                        <p>Verifica se suas mensagens de commit aderem a uma convenção específica.</p>
                        <pre>npm install -g @commitlint/cli @commitlint/config-conventional</pre>
                    </li>
                    <li>
                        <strong>commitizen</strong>
                        <p>CLI interativo para criar mensagens de commit padronizadas.</p>
                        <pre>npm install -g commitizen cz-conventional-changelog</pre>
                    </li>
                    <li>
                        <strong>husky</strong>
                        <p>Facilita a configuração de Git hooks para executar scripts antes de commits/pushes.</p>
                        <pre>npm install husky --save-dev</pre>
                    </li>
                    <li>
                        <strong>standard-version</strong>
                        <p>Utilitário para versionamento e geração de CHANGELOG baseado nas mensagens de commit.</p>
                        <pre>npm install -g standard-version</pre>
                    </li>
                    <li>
                        <strong>git-cz</strong>
                        <p>Interface alternativa para o commitizen.</p>
                        <pre>npm install -g git-cz</pre>
                    </li>
                    <li>
                        <strong>laravel-pint</strong>
                        <p>Formatador de código PHP para projetos Laravel.</p>
                        <pre>composer require laravel/pint --dev</pre>
                    </li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo: Configuração de husky com commitlint</h4>
                <pre>
// .husky/commit-msg
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx --no -- commitlint --edit ${1}

// commitlint.config.js
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'scope-enum': [2, 'always', [
      'auth', 'api', 'models', 'controllers', 'services',
      'repositories', 'views', 'routes', 'config', 'bootstrap',
      'types', 'enums', 'middleware', 'providers', 'database'
    ]]
  }
};
                </pre>
            </div>
        </div>

        <div id="ferramentas-analise" class="subsection">
            <h3>5.2. Análise de Commits</h3>

            <div class="tools-list">
                <h4>Ferramentas de Visualização e Análise</h4>
                <ul>
                    <li>
                        <strong>git log</strong>
                        <p>Visualização padrão do histórico de commits no terminal.</p>
                        <pre>git log --oneline --graph --decorate</pre>
                    </li>
                    <li>
                        <strong>GitKraken/Sourcetree</strong>
                        <p>Clientes Git gráficos para visualizar histórico de commits e branches.</p>
                    </li>
                    <li>
                        <strong>git-standup</strong>
                        <p>Mostra seus commits recentes, útil para standups.</p>
                        <pre>npm install -g git-standup</pre>
                    </li>
                    <li>
                        <strong>git-quick-stats</strong>
                        <p>Estatísticas rápidas sobre o repositório e contribuições.</p>
                        <pre>npm install -g git-quick-stats</pre>
                    </li>
                </ul>
            </div>

            <div class="tip">
                <h4>Alias útil para visualização de logs:</h4>
                <pre>
git config --global alias.lg "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit"

# Use com:
git lg
                </pre>
            </div>
        </div>
    </section>

    <section id="problemas" class="manual-section">
        <h2>6. Problemas Comuns</h2>

        <div id="problemas-situacoes" class="subsection">
            <h3>6.1. Situações Problemáticas e Soluções</h3>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Problema</th>
                            <th>Solução</th>
                            <th>Comando</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Commit feito na branch errada</td>
                            <td>Crie uma nova branch a partir da atual, volte a branch original para o estado anterior
                            </td>
                            <td>
                                <pre>
git branch nova-branch
git reset --hard HEAD~1
git checkout nova-branch
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Mensagem de commit errada no último commit</td>
                            <td>Amende o último commit com nova mensagem</td>
                            <td>
                                <pre>
git commit --amend -m "Nova mensagem"
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Commit tem arquivos demais</td>
                            <td>Resete o último commit, adicione os arquivos corretos e commite novamente</td>
                            <td>
                                <pre>
git reset HEAD~1
git add arquivo-correto1 arquivo-correto2
git commit -m "Mensagem correta"
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Commit está faltando arquivos</td>
                            <td>Adicione os arquivos faltantes e amende o último commit</td>
                            <td>
                                <pre>
git add arquivo-faltante
git commit --amend --no-edit
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Precisa reorganizar os últimos commits</td>
                            <td>Use rebase interativo para reordenar, combinar ou ajustar commits</td>
                            <td>
                                <pre>
git rebase -i HEAD~5
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Commit de segredos ou dados sensíveis</td>
                            <td>Remova completamente do histórico usando git-filter-branch ou BFG</td>
                            <td>
                                <pre>
# Use com extrema cautela
bfg --delete-files arquivo-sensível.txt
                                </pre>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="problemas-prevencao" class="subsection">
            <h3>6.2. Prevenção de Problemas</h3>

            <div class="best-practice">
                <h4>Prevenção de Commits Problemáticos</h4>
                <ul>
                    <li>Sempre revise o <code>git status</code> e <code>git diff --staged</code> antes de commitar</li>
                    <li>Use <code>git add -p</code> para adicionar alterações seletivamente</li>
                    <li>Configure .gitignore adequadamente para evitar commits de arquivos temporários ou gerados</li>
                    <li>Utilize ferramentas como git-secrets ou git-leaks para detectar informações sensíveis</li>
                    <li>Implemente hooks de pre-commit para validações automáticas</li>
                    <li>Faça uma pausa antes de pushar para revisar seus commits</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Hook de pre-commit para prevenir commits de dados sensíveis:</h4>
                <pre>
#!/bin/sh
# .git/hooks/pre-commit

PATTERN="API_KEY|SECRET|PASSWORD|TOKEN"

if git diff --cached | grep -i "$PATTERN"; then
  echo "ERRO: Possível vazamento de informações sensíveis detectado."
  echo "Revise o commit e remova qualquer segredo, senha ou token."
  exit 1
fi
                </pre>
            </div>
        </div>
    </section>

    <section id="laravel12" class="manual-section">
        <h2>7. Commits para Laravel 12</h2>

        <div id="laravel12-padroes" class="subsection">
            <h3>7.1. Padrões Específicos para Laravel 12</h3>

            <p>O Laravel 12 introduziu várias mudanças significativas que devem ser refletidas em seus commits:</p>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Área de Mudança</th>
                            <th>Exemplo de Commit</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Sistema de Inicialização</td>
                            <td>refactor(bootstrap): adapta bootstrap/app.php para novo formato do Laravel 12</td>
                        </tr>
                        <tr>
                            <td>Tipagem Estrita</td>
                            <td>fix(types): adiciona tipagem estrita em controllers e services</td>
                        </tr>
                        <tr>
                            <td>Enums PHP</td>
                            <td>refactor(enums): converte constantes de status para PHP Enums</td>
                        </tr>
                        <tr>
                            <td>Vite 5</td>
                            <td>build(assets): atualiza configuração do Vite para versão 5</td>
                        </tr>
                        <tr>
                            <td>Requisitos PHP</td>
                            <td>chore(deps): atualiza requisitos para PHP 8.2+</td>
                        </tr>
                        <tr>
                            <td>Novas APIs</td>
                            <td>refactor(cache): utiliza nova API de cache com tags</td>
                        </tr>
                        <tr>
                            <td>Classes Readonly</td>
                            <td>refactor(dtos): converte DTOs para classes readonly</td>
                        </tr>
                        <tr>
                            <td>Validação</td>
                            <td>refactor(validation): atualiza regras para nova API fluente</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="alerts-section">
                <h4>Dica de Organização</h4>
                <p>Ao trabalhar com Laravel 12, considere adicionar escopos específicos para as novas funcionalidades do
                    framework, como <code>bootstrap</code>, <code>enums</code>, <code>types</code> e
                    <code>readonly</code> para facilitar a identificação de mudanças relacionadas à migração.</p>
            </div>
        </div>

        <div id="laravel12-estrategia" class="subsection">
            <h3>7.2. Estratégia de Migração</h3>

            <p>Ao migrar um projeto para Laravel 12, recomenda-se uma abordagem gradual com commits bem organizados:</p>

            <ol>
                <li><strong>Preparação</strong>: Comece com um commit que atualize as dependências e requisitos</li>
                <li><strong>Estrutura</strong>: Adapte a estrutura de bootstrap em um commit separado</li>
                <li><strong>Tipagem</strong>: Divida a adição de tipagem estrita por camadas (controllers, services,
                    etc.)</li>
                <li><strong>Modernização</strong>: Implemente Enums, classes readonly e outras funcionalidades modernas
                </li>
                <li><strong>APIs</strong>: Atualize o uso de APIs que foram modificadas no Laravel 12</li>
                <li><strong>Testes</strong>: Atualize os testes para refletir as mudanças</li>
                <li><strong>Documentação</strong>: Finalize com atualizações de documentação</li>
            </ol>

            <div class="best-practice">
                <h4>Boas Práticas para Migração</h4>
                <ul>
                    <li>Mantenha commits pequenos e focados em uma única área de mudança</li>
                    <li>Execute os testes após cada commit para identificar problemas rapidamente</li>
                    <li>Documente breaking changes no corpo do commit com <code>BREAKING CHANGE:</code></li>
                    <li>Use branches específicas para a migração (ex: <code>feature/laravel-12-upgrade</code>)</li>
                    <li>Considere usar Pull Requests separados para cada fase da migração em projetos grandes</li>
                </ul>
            </div>
        </div>

        <div id="laravel12-exemplos" class="subsection">
            <h3>7.3. Exemplos de Sequência de Commits</h3>

            <p>Aqui está uma sequência de commits recomendada para migrar um projeto Laravel 11 para Laravel 12:</p>

            <div class="example-commits">
                <pre>
# Fase 1: Preparação
chore(deps): atualiza requisitos para PHP 8.2 e Laravel 12
chore(composer): atualiza dependências para compatibilidade com Laravel 12
build(assets): atualiza configuração do Vite para versão 5

# Fase 2: Estrutura
refactor(bootstrap): adapta bootstrap/app.php para novo formato do Laravel 12
refactor(config): atualiza arquivos de configuração para Laravel 12
refactor(providers): ajusta service providers para novo sistema de inicialização

# Fase 3: Tipagem
fix(types): adiciona tipagem estrita em controllers
fix(types): adiciona tipagem estrita em services
fix(types): adiciona tipagem estrita em repositories
fix(types): adiciona tipagem estrita em models
fix(types): adiciona tipagem estrita em middleware

# Fase 4: Modernização
refactor(enums): converte constantes de status para PHP Enums
refactor(dtos): converte DTOs para classes readonly
refactor(models): implementa casts tipados para atributos

# Fase 5: APIs
refactor(cache): utiliza nova API de cache com tags
refactor(validation): atualiza regras de validação para nova API fluente
refactor(http): atualiza middleware para nova estrutura
refactor(database): atualiza queries para usar novas funcionalidades do Eloquent

# Fase 6: Testes
test: atualiza testes de unidade para compatibilidade com Laravel 12
test: atualiza testes de feature para compatibilidade com Laravel 12
test: adiciona testes para novas funcionalidades do Laravel 12

# Fase 7: Documentação
docs: atualiza README com requisitos do Laravel 12
docs: atualiza documentação de instalação e configuração
docs: adiciona notas sobre breaking changes e migrações
                </pre>
            </div>

            <div class="tip">
                <h4>Dica para Projetos em Produção</h4>
                <p>Para projetos em produção, considere criar uma tag ou release antes de iniciar a migração para
                    Laravel 12. Isso facilita a reversão caso surjam problemas inesperados durante o processo de
                    migração.</p>
            </div>
        </div>
    </section>

    <section id="checklist" class="manual-section">
        <h2>8. Checklist de Boas Práticas</h2>

        <p>Use esta checklist para garantir que seus commits sigam as melhores práticas:</p>

        <div class="checklist-container">
            <div class="checklist-group">
                <h3>Mensagem de Commit</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Usa um dos tipos padrão (feat, fix, docs, etc.)</li>
                    <li><input type="checkbox"> Inclui um escopo opcional entre parênteses</li>
                    <li><input type="checkbox"> Tem um resumo conciso (até 50 caracteres)</li>
                    <li><input type="checkbox"> Usa o tempo verbal imperativo no resumo</li>
                    <li><input type="checkbox"> Separa o resumo do corpo com uma linha em branco</li>
                    <li><input type="checkbox"> Explica o "o quê" e o "por quê" no corpo (não o "como")</li>
                    <li><input type="checkbox"> Referencia issues relacionadas no rodapé</li>
                    <li><input type="checkbox"> Indica breaking changes quando apropriado</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Conteúdo do Commit</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Representa uma única alteração lógica</li>
                    <li><input type="checkbox"> Não inclui alterações não relacionadas</li>
                    <li><input type="checkbox"> Não quebra a build ou os testes</li>
                    <li><input type="checkbox"> Não contém código comentado desnecessário</li>
                    <li><input type="checkbox"> Não inclui arquivos temporários ou de IDE</li>
                    <li><input type="checkbox"> Não contém dados sensíveis (senhas, tokens)</li>
                    <li><input type="checkbox"> Segue os padrões de código do projeto</li>
                    <li><input type="checkbox"> Inclui testes quando apropriado</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Workflow</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Está na branch correta</li>
                    <li><input type="checkbox"> Foi revisado antes de ser enviado</li>
                    <li><input type="checkbox"> Não reescreve histórico já publicado</li>
                    <li><input type="checkbox"> Mantém commits relacionados juntos</li>
                    <li><input type="checkbox"> Usa rebase interativo para limpar histórico local</li>
                    <li><input type="checkbox"> Mantém commits pequenos e focados</li>
                    <li><input type="checkbox"> Segue a convenção de commits do projeto</li>
                    <li><input type="checkbox"> Usa ferramentas de validação quando disponíveis</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Laravel 12 Específico</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> Usa escopos específicos para mudanças do Laravel 12</li>
                    <li><input type="checkbox"> Documenta breaking changes no corpo do commit</li>
                    <li><input type="checkbox"> Mantém commits de migração pequenos e focados</li>
                    <li><input type="checkbox"> Testa cada mudança relacionada ao Laravel 12</li>
                    <li><input type="checkbox"> Atualiza documentação para refletir mudanças</li>
                    <li><input type="checkbox"> Segue a sequência recomendada de migração</li>
                    <li><input type="checkbox"> Usa tipagem estrita em novo código</li>
                    <li><input type="checkbox"> Aproveita novas funcionalidades do PHP 8.2+</li>
                </ul>
            </div>
        </div>

        <div class="conclusion">
            <h3>Conclusão</h3>
            <p>Seguir estas boas práticas para commits não apenas melhora a qualidade do seu histórico Git, mas também
                facilita a colaboração em equipe, a manutenção do código e a migração para novas versões do Laravel.
                Commits bem estruturados são uma forma de documentação viva que conta a história do seu projeto e ajuda
                novos desenvolvedores a entenderem as decisões tomadas ao longo do tempo.</p>
            <p>Ao migrar para o Laravel 12, commits claros e bem organizados são ainda mais importantes devido às
                mudanças significativas introduzidas nesta versão. Uma estratégia de migração bem planejada, com commits
                atômicos e mensagens descritivas, pode transformar um processo potencialmente complexo em uma série de
                passos gerenciáveis e bem documentados.</p>
        </div>
    </section>

    <footer class="manual-footer">
        <p>Manual de Boas Práticas para Commits Git - Laravel 12</p>
        <p>Última atualização: <span id="current-date"></span></p>
        <script>
            document.getElementById('current-date').textContent = new Date().toLocaleDateString();
        </script>
    </footer>
</body>

</html>