<?php

namespace Tests\Unit\Controllers;

use App\Controllers\AuthController;
use App\Services\AuthService;
use App\Responses\ResponseInterface;
use App\Services\JwtService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Mockery;

class AuthControllerTest extends TestCase
{
    protected $controller;
    protected $authService;
    protected $response;
    protected $jwtService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->authService = Mockery::mock(AuthService::class);
        $this->response = Mockery::mock(ResponseInterface::class);
        $this->jwtService = Mockery::mock(JwtService::class);

        $this->controller = new AuthController(
            $this->authService,
            $this->response,
            $this->jwtService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    #[Test]
    public function it_can_register_user()
    {
        // Criar uma requisição simulada
        $request = Request::create('/api/auth/register', 'POST', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ]);

        // Mockando Validator
        Validator::shouldReceive('make')
            ->once()
            ->andReturn(Mockery::mock(['fails' => false]));

        // Mockando o serviço de autenticação para o método register
        $userData = [
            'id' => 1,
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'created_at' => now(),
            'updated_at' => now()
        ];

        $this->authService->shouldReceive('register')
            ->once()
            ->andReturn($userData);

        // Mockando o serviço de autenticação para o método login
        $tokenData = [
            'access_token' => 'test-token',
            'refresh_token' => 'test-refresh-token',
            'token_type' => 'bearer',
            'expires_in' => 3600
        ];

        $this->authService->shouldReceive('login')
            ->once()
            ->andReturn($tokenData);

        // Mockando a resposta
        $this->response->shouldReceive('created')
            ->once()
            ->andReturn(new JsonResponse([
                'data' => [
                    'user' => $userData,
                    'authorization' => [
                        'token' => $tokenData['access_token'],
                        'refresh_token' => $tokenData['refresh_token'],
                        'type' => $tokenData['token_type'],
                        'expires_in' => $tokenData['expires_in']
                    ]
                ],
                'message' => 'Usuário registrado com sucesso'
            ], 201));

        // Executar o método
        $response = $this->controller->register($request);

        // Verificar o resultado
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(201, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $this->assertArrayHasKey('user', $content['data']);
        $this->assertArrayHasKey('authorization', $content['data']);
        $this->assertEquals('test-token', $content['data']['authorization']['token']);
        $this->assertEquals('Usuário registrado com sucesso', $content['message']);
    }
}
