<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Auth\AuthenticationException;
use App\Responses\ResponseInterface;
use App\Mail\ExceptionOccurred;
use Illuminate\Support\Facades\Mail;
use Throwable;

class Handler extends ExceptionHandler
{
    protected $response;
    protected $dontReport = [
        AuthenticationException::class,
        // Adicione outras exceções que você não quer reportar
    ];

    public function __construct(ResponseInterface $response)
    {
        $this->response = $response;
        parent::__construct(app());
    }

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            $this->sendExceptionEmail($e);
        });

        // Registra um callback específico para exceções de autenticação
        $this->renderable(function (AuthenticationException $e, $request) {
            if ($request->is('api/*') || $request->is('auth/*')) {
                return $this->response->unauthorized('Não autenticado');
            }
        });
    }

    /**
     * Envia email com detalhes da exceção
     */
    protected function sendExceptionEmail(Throwable $exception): void
    {
        try {
            if ($this->shouldReport($exception)) {
                $context = [
                    'url' => request()->fullUrl(),
                    'service' => get_class($exception),
                    'environment' => app()->environment(),
                    'server' => gethostname(),
                    'timestamp' => now()->toIso8601String(),
                ];

                // Log a exceção primeiro
                logger()->channel('exceptions')->error(
                    $exception->getMessage(),
                    array_merge($context, ['trace' => $exception->getTraceAsString()])
                );

                // Envia o email
                Mail::to(config('mail.admin_address'))
                    ->queue(new ExceptionOccurred($exception, $context['url'], $context['service']));
            }
        } catch (Throwable $e) {
            logger()->error('Falha ao processar exceção', [
                'original_exception' => $exception->getMessage(),
                'handler_exception' => $e->getMessage(),
            ]);
        }
    }
}
