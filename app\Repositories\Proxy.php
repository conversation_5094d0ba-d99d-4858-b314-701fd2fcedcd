<?php

namespace App\Repositories;

use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\LazyCollection;
use Illuminate\Database\Eloquent\Builder;

class Proxy implements RepositoryInterface
{
    protected RepositoryInterface $origin;
    protected int $ttl;

    public function __construct(RepositoryInterface $origin, int $ttl = 60)
    {
        $this->origin = $origin;
        $this->ttl = $ttl;
    }

    /* Implementação completa da interface */
    public function find($id): ?Model
    {
        $key = $this->cacheKey(__FUNCTION__, [$id]);
        return Cache::remember($key, $this->ttl, function () use ($id) {
            return $this->origin->find($id);
        });
    }

    public function findOrFail($id): Model
    {
        $key = $this->cacheKey(__FUNCTION__, [$id]);
        return Cache::remember($key, $this->ttl, function () use ($id) {
            return $this->origin->findOrFail($id);
        });
    }

    public function findAll(): Collection
    {
        $key = $this->cacheKey(__FUNCTION__, []);
        return Cache::remember($key, $this->ttl, function () {
            return $this->origin->findAll();
        });
    }

    public function create(array $data): Model
    {
        $model = $this->origin->create($data);
        $this->flushCache();
        return $model;
    }

    public function update($id, array $data): ?Model
    {
        $model = $this->origin->update($id, $data);
        $this->flushCache();
        return $model;
    }

    public function delete($id): bool
    {
        $result = $this->origin->delete($id);
        $this->flushCache();
        return $result;
    }

    public function forceDelete($id): bool
    {
        $result = $this->origin->forceDelete($id);
        $this->flushCache();
        return $result;
    }

    public function paginate(int $perPage = 15, array $columns = ['*']): LengthAwarePaginator
    {
        $key = $this->cacheKey(__FUNCTION__, [$perPage, $columns]);
        return Cache::remember($key, $this->ttl, function () use ($perPage, $columns) {
            return $this->origin->paginate($perPage, $columns);
        });
    }

    public function cursorPaginate(int $perPage = 15, array $columns = ['*'])
    {
        $key = $this->cacheKey(__FUNCTION__, [$perPage, $columns]);
        return Cache::remember($key, $this->ttl, function () use ($perPage, $columns) {
            return $this->origin->cursorPaginate($perPage, $columns);
        });
    }

    public function lazy(): LazyCollection
    {
        return $this->origin->lazy();
    }

    public function findBy(array $criteria): Collection
    {
        $key = $this->cacheKey(__FUNCTION__, $criteria);
        return Cache::remember($key, $this->ttl, function () use ($criteria) {
            return $this->origin->findBy($criteria);
        });
    }

    public function findOneBy(array $criteria): ?Model
    {
        $key = $this->cacheKey(__FUNCTION__, $criteria);
        return Cache::remember($key, $this->ttl, function () use ($criteria) {
            return $this->origin->findOneBy($criteria);
        });
    }

    public function findWhereIn(string $field, array $values): Collection
    {
        $key = $this->cacheKey(__FUNCTION__, [$field, $values]);
        return Cache::remember($key, $this->ttl, function () use ($field, $values) {
            return $this->origin->findWhereIn($field, $values);
        });
    }

    public function bulkInsert(array $data): bool
    {
        $result = $this->origin->bulkInsert($data);
        $this->flushCache();
        return $result;
    }

    public function bulkUpdate(array $ids, array $data): int
    {
        $result = $this->origin->bulkUpdate($ids, $data);
        $this->flushCache();
        return $result;
    }

    public function getModel(): Model
    {
        return $this->origin->getModel();
    }

    public function newQuery(): Builder
    {
        return $this->origin->newQuery();
    }

    public function count(): int
    {
        $key = $this->cacheKey(__FUNCTION__, []);
        return Cache::remember($key, $this->ttl, function () {
            return $this->origin->count();
        });
    }

    public function withCriteria(array $criteria): self
    {
        $this->origin->withCriteria($criteria);
        return $this;
    }

    public function withCache(int $ttl = 60): RepositoryInterface
    {
        return new self($this->origin, $ttl);
    }

    /* Métodos auxiliares */
    protected function cacheKey(string $method, array $args): string
    {
        return 'repo_' . $this->origin->getModel()->getTable() . '_' . $method . '_' . md5(json_encode($args));
    }

    protected function flushCache(): void
    {
        Cache::tags([$this->origin->getModel()->getTable()])->flush();
    }
}
