<?php

namespace App\Repositories;

use App\Repositories\Proxy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\LazyCollection;

abstract class RepositoryAbstract implements RepositoryInterface
{
    protected Model $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function find($id): ?Model
    {
        return $this->model->find($id);
    }

    public function findOrFail($id): Model
    {
        return $this->model->findOrFail($id);
    }

    public function findAll(): Collection
    {
        return $this->model->all();
    }

    public function create(array $data): Model
    {
        return DB::transaction(function () use ($data) {
            return $this->model->create($data);
        });
    }

    public function update($id, array $data): ?Model
    {
        return DB::transaction(function () use ($id, $data) {
            $model = $this->find($id);
            if ($model) {
                $model->update($data);
            }
            return $model;
        });
    }

    public function delete($id): bool
    {
        return DB::transaction(function () use ($id) {
            return $this->model->destroy($id) > 0;
        });
    }

    public function forceDelete($id): bool
    {
        return DB::transaction(function () use ($id) {
            return $this->find($id)?->forceDelete() ?? false;
        });
    }

    public function paginate(int $perPage = 15, array $columns = ['*']): LengthAwarePaginator
    {
        return $this->model->paginate($perPage, $columns);
    }

    public function cursorPaginate(int $perPage = 15, array $columns = ['*'])
    {
        return $this->model->simplePaginate($perPage, $columns);
    }

    public function lazy(): LazyCollection
    {
        return $this->model->cursor();
    }

    public function findBy(array $criteria): Collection
    {
        $query = $this->model->newQuery();
        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }
        return $query->get();
    }

    public function findOneBy(array $criteria): ?Model
    {
        return $this->findBy($criteria)->first();
    }

    public function findWhereIn(string $field, array $values): Collection
    {
        return $this->model->whereIn($field, $values)->get();
    }

    public function bulkInsert(array $data): bool
    {
        return DB::transaction(function () use ($data) {
            return $this->model->insert($data);
        });
    }

    public function bulkUpdate(array $ids, array $data): int
    {
        return DB::transaction(function () use ($ids, $data) {
            return $this->model->whereIn('id', $ids)->update($data);
        });
    }

    public function getModel(): Model
    {
        return $this->model;
    }

    public function newQuery(): Builder
    {
        return $this->model->newQuery();
    }

    public function count(): int
    {
        return $this->model->count();
    }

    public function withCriteria(array $criteria): self
    {
        // Implementação base - pode ser sobrescrita
        return $this;
    }

    public function withCache(int $ttl = 60): RepositoryInterface
    {
        return new Proxy($this, $ttl);
    }
}
