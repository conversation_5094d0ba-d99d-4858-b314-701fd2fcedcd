<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Performance para APIs</title>
    <link rel="stylesheet" href="css/manual.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Performance para APIs PHP</h1>
        <p class="version">Versão 1.0 - Última atualização: Agosto 2023</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#metricas">Métricas de Performance</a></li>
            <li><a href="#db-otimizacao">Otimização de Banco de Dados</a></li>
            <li><a href="#caching">Estratégias de Cache</a></li>
            <li><a href="#codigo-otimizacao">Otimização de Código</a></li>
            <li><a href="#arquitetura">Arquitetura para Performance</a></li>
            <li><a href="#monitoramento">Monitoramento</a></li>
            <li><a href="#checklist">Checklist de Performance</a></li>
        </ul>
    </nav>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>

        <div class="intro-text">
            <p>A performance é um aspecto crítico no desenvolvimento de APIs. Uma API lenta não apenas prejudica a
                experiência do usuário, mas também pode aumentar custos operacionais, reduzir a escalabilidade e limitar
                a adoção de seus serviços.</p>

            <p>Este manual apresenta as melhores práticas para otimização de performance em APIs PHP, com foco especial
                no framework Laravel, mas com princípios aplicáveis a qualquer implementação PHP.</p>
        </div>

        <div class="key-points">
            <h3>Pontos-chave sobre Performance</h3>
            <ul>
                <li>Performance impacta diretamente a experiência do usuário e o SEO (para APIs públicas)</li>
                <li>APIs rápidas podem reduzir custos de infraestrutura significativamente</li>
                <li>A otimização de performance deve começar desde o planejamento, não apenas no final</li>
                <li>Métricas e monitoramento são essenciais para identificar gargalos</li>
            </ul>
        </div>
    </section>

    <section id="metricas" class="manual-section">
        <h2>2. Métricas de Performance</h2>

        <div class="subsection">
            <h3>2.1. Principais Métricas</h3>

            <div class="metrics-table">
                <table>
                    <thead>
                        <tr>
                            <th>Métrica</th>
                            <th>Descrição</th>
                            <th>Valor Ideal</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Tempo de Resposta</td>
                            <td>Tempo entre requisição e resposta completa</td>
                            <td>&lt; 200ms</td>
                        </tr>
                        <tr>
                            <td>Tempo até o Primeiro Byte (TTFB)</td>
                            <td>Tempo até o primeiro byte ser recebido pelo cliente</td>
                            <td>&lt; 100ms</td>
                        </tr>
                        <tr>
                            <td>Requisições por Segundo (RPS)</td>
                            <td>Número máximo de requisições que a API pode processar por segundo</td>
                            <td>Depende do requisito de negócio</td>
                        </tr>
                        <tr>
                            <td>Uso de CPU</td>
                            <td>Percentual de uso de CPU durante operação</td>
                            <td>&lt; 70% em carga normal</td>
                        </tr>
                        <tr>
                            <td>Uso de Memória</td>
                            <td>Quantidade de RAM consumida pelo processo</td>
                            <td>Estável, sem vazamentos</td>
                        </tr>
                        <tr>
                            <td>Tempo de Consulta DB</td>
                            <td>Tempo para execução de consultas ao banco de dados</td>
                            <td>&lt; 50ms</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="subsection">
            <h3>2.2. Ferramentas de Medição</h3>

            <div class="tools-list">
                <ul>
                    <li>
                        <strong>Laravel Telescope</strong>
                        <p>Ferramenta de debug e monitoramento integrada ao Laravel, ideal para ambiente de
                            desenvolvimento.</p>
                    </li>
                    <li>
                        <strong>Laravel Debugbar</strong>
                        <p>Barra de debug que mostra consultas SQL, uso de memória, tempo de requisição e mais.</p>
                    </li>
                    <li>
                        <strong>Blackfire.io</strong>
                        <p>Profiler para PHP que oferece insights detalhados sobre performance em produção.</p>
                    </li>
                    <li>
                        <strong>New Relic</strong>
                        <p>Plataforma de monitoramento para aplicações em produção.</p>
                    </li>
                    <li>
                        <strong>Prometheus + Grafana</strong>
                        <p>Combinação de ferramentas open-source para coleta e visualização de métricas.</p>
                    </li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo: Medição Manual de Performance</h4>
                <pre>
// Medir tempo de execução
$startTime = microtime(true);

// Código que deseja medir...
$result = $this->someService->performOperation();

// Calcular tempo decorrido
$executionTime = microtime(true) - $startTime;
Log::debug("Operação executada em {$executionTime} segundos");

// Medir uso de memória
$memoryUsage = memory_get_usage(true) / 1024 / 1024;
Log::debug("Uso de memória: {$memoryUsage} MB");
                </pre>
            </div>
        </div>
    </section>

    <section id="db-otimizacao" class="manual-section">
        <h2>3. Otimização de Banco de Dados</h2>

        <div class="intro-text">
            <p>Consultas ao banco de dados são frequentemente o principal gargalo de performance em APIs. Otimizar essas
                operações pode trazer ganhos substanciais.</p>
        </div>

        <div class="subsection">
            <h3>3.1. Indexação Eficiente</h3>

            <div class="content-block">
                <p>Índices são fundamentais para consultas rápidas, mas seu uso excessivo pode prejudicar operações de
                    escrita.</p>

                <div class="best-practice">
                    <h4>Boas Práticas de Indexação</h4>
                    <ul>
                        <li>Crie índices para colunas frequentemente usadas em cláusulas WHERE, ORDER BY e JOIN</li>
                        <li>Use índices compostos para consultas que filtram por múltiplas colunas</li>
                        <li>Evite indexação excessiva - cada índice aumenta o tempo de operações de escrita</li>
                        <li>Monitore índices não utilizados e remova-os</li>
                        <li>Prefira índices focados nas consultas mais frequentes e críticas</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Criação de Índices em Laravel</h4>
                    <pre>
// Em uma migration Laravel
Schema::table('products', function (Blueprint $table) {
    // Índice simples
    $table->index('category_id');
    
    // Índice composto
    $table->index(['status', 'created_at']);
    
    // Índice único
    $table->unique('sku');
    
    // Índice fulltext (MySQL 5.7+)
    $table->fullText('description');
});
                    </pre>
                </div>
            </div>
        </div>

        <div class="subsection">
            <h3>3.2. Otimização de Queries</h3>

            <div class="comparison-table">
                <h4>Práticas Recomendadas vs. Práticas Problemáticas</h4>
                <table>
                    <thead>
                        <tr>
                            <th>Problema</th>
                            <th>Prática Ruim</th>
                            <th>Prática Recomendada</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>N+1 Queries</td>
                            <td>
                                <pre>
$orders = Order::all();
foreach ($orders as $order) {
    echo $order->customer->name;  // Query adicional para cada ordem
}
                                </pre>
                            </td>
                            <td>
                                <pre>
$orders = Order::with('customer')->get();
foreach ($orders as $order) {
    echo $order->customer->name;  // Dados já carregados
}
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Selecionando Muitas Colunas</td>
                            <td>
                                <pre>
$users = User::all();  // SELECT * FROM users
                                </pre>
                            </td>
                            <td>
                                <pre>
$users = User::select('id', 'name', 'email')->get();  // Apenas as colunas necessárias
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Não Usar Paginação</td>
                            <td>
                                <pre>
$products = Product::all();  // Pode retornar milhares de registros
                                </pre>
                            </td>
                            <td>
                                <pre>
$products = Product::paginate(25);  // Apenas 25 registros por vez
                                </pre>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="best-practice">
                <h4>Técnicas Avançadas de Otimização de Queries</h4>
                <ul>
                    <li>Use subconsultas com cautela - elas podem ser mais lentas que JOINs</li>
                    <li>Prefira COUNT(*) a COUNT(1) ou COUNT(coluna) para contagem total de registros</li>
                    <li>Utilize chunking para processamento de grandes conjuntos de dados</li>
                    <li>Considere views materializadas para consultas complexas frequentemente executadas</li>
                    <li>Implemente lazy loading apenas quando necessário e eager loading como padrão</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo: Chunking para Processamento de Grandes Datasets</h4>
                <pre>
// Processar 100.000 registros em lotes de 1.000
User::chunk(1000, function ($users) {
    foreach ($users as $user) {
        // Processar cada usuário
    }
});

// Alternativa: usar Lazy Collection (Laravel 8+)
foreach (User::cursor() as $user) {
    // Processar cada usuário sem carregar todos na memória
}
                </pre>
            </div>
        </div>

        <div class="subsection">
            <h3>3.3. Query Caching</h3>

            <div class="content-block">
                <p>Caching de consultas frequentes pode reduzir drasticamente a carga no banco de dados.</p>

                <div class="code-block">
                    <h4>Exemplo: Cache de Consultas em Laravel</h4>
                    <pre>
// Consulta com cache por 60 minutos
$users = Cache::remember('users.active', 60*60, function () {
    return User::where('active', true)
        ->select('id', 'name', 'email')
        ->get();
});

// Cache com tags (requer Redis ou Memcached)
$products = Cache::tags(['products', 'homepage'])->remember('products.featured', 60*60, function () {
    return Product::where('featured', true)->get();
});
                    </pre>
                </div>

                <div class="tip">
                    <p>Implemente invalidação automática de cache ao atualizar registros para manter a consistência dos
                        dados:</p>
                    <pre>
// Em um model Observer
public function saved(Product $product)
{
    Cache::tags(['products'])->flush();
}
                    </pre>
                </div>
            </div>
        </div>
    </section>

    <section id="caching" class="manual-section">
        <h2>4. Estratégias de Cache</h2>

        <div class="subsection">
            <h3>4.1. Níveis de Cache</h3>

            <div class="content-block">
                <p>Uma estratégia eficiente de cache deve considerar múltiplos níveis:</p>

                <div class="layers-diagram">
                    <div class="layer">
                        <h4>Cache de Aplicação</h4>
                        <p>Armazena resultados de operações custosas, como consultas complexas. Implementado com Redis,
                            Memcached ou similar.</p>
                    </div>
                    <div class="layer">
                        <h4>Cache de Rota/HTTP</h4>
                        <p>Armazena respostas completas da API para rotas específicas, normalmente implementado usando
                            HTTP Cache.</p>
                    </div>
                    <div class="layer">
                        <h4>Cache de Frontend/CDN</h4>
                        <p>Cache no lado do cliente ou em CDN, controlado por cabeçalhos HTTP.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="subsection">
            <h3>4.2. Implementação de Cache</h3>

            <div class="code-block">
                <h4>Cache de Dados em Laravel</h4>
                <pre>
// Cache de dados calculados
public function getStatistics()
{
    return Cache::remember('statistics', 3600, function () {
        return $this->calculateExpensiveStatistics();
    });
}

// Cache por usuário
public function getUserDashboard($userId)
{
    return Cache::remember("user.{$userId}.dashboard", 1800, function () use ($userId) {
        return $this->dashboardService->generate($userId);
    });
}
                </pre>
            </div>

            <div class="code-block">
                <h4>Cache HTTP em Laravel</h4>
                <pre>
// Em um middleware ou controller
public function index()
{
    $data = $this->service->getData();
    
    return response($data)
        ->header('Cache-Control', 'public, max-age=60')
        ->header('Etag', md5(json_encode($data)));
}

// Usando pacote spatie/laravel-responsecache
class ProductsController
{
    public function index()
    {
        return ResponseCache::onTag('products')->maxAge(60)->json(
            Product::paginate(20)
        );
    }
}
                </pre>
            </div>

            <div class="best-practice">
                <h4>Boas Práticas de Cache</h4>
                <ul>
                    <li>Use TTL (Time To Live) apropriado para cada tipo de dado</li>
                    <li>Implemente tags de cache para facilitar a invalidação seletiva</li>
                    <li>Considere estratégias de cache específicas para cada endpoint</li>
                    <li>Use cache condicional (ETag, If-Modified-Since) para APIs com muitas requisições</li>
                    <li>Evite cache para dados personalizados ou sensíveis</li>
                </ul>
            </div>
        </div>
    </section>

    <section id="codigo-otimizacao" class="manual-section">
        <h2>5. Otimização de Código</h2>

        <div class="subsection">
            <h3>5.1. Otimizações PHP</h3>

            <div class="content-block">
                <p>Otimizações específicas para código PHP podem melhorar significativamente a performance:</p>

                <div class="comparison-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Prática Ruim</th>
                                <th>Prática Boa</th>
                                <th>Explicação</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <pre>
foreach ($array as $key => $value) {
    // Operação que não usa $key
}
                                    </pre>
                                </td>
                                <td>
                                    <pre>
foreach ($array as $value) {
    // Operação sem $key
}
                                    </pre>
                                </td>
                                <td>Evita alocação desnecessária de memória</td>
                            </tr>
                            <tr>
                                <td>
                                    <pre>
for ($i = 0; $i < count($array); $i++) {
    // count() chamado a cada iteração
}
                                    </pre>
                                </td>
                                <td>
                                    <pre>
$count = count($array);
for ($i = 0; $i < $count; $i++) {
    // count() chamado apenas uma vez
}
                                    </pre>
                                </td>
                                <td>Evita recalcular o tamanho do array a cada iteração</td>
                            </tr>
                            <tr>
                                <td>
                                    <pre>
$string = '';
for ($i = 0; $i < 1000; $i++) {
    $string .= $data[$i];
}
                                    </pre>
                                </td>
                                <td>
                                    <pre>
$parts = [];
for ($i = 0; $i < 1000; $i++) {
    $parts[] = $data[$i];
}
$string = implode('', $parts);
                                    </pre>
                                </td>
                                <td>Melhora a eficiência da concatenação de strings</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="tip">
                    <p>Use o opcache do PHP em produção para melhorar significativamente o desempenho:</p>
                    <pre>
# php.ini
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=20000
opcache.validate_timestamps=0
opcache.save_comments=1
opcache.fast_shutdown=1
                    </pre>
                </div>
            </div>
        </div>

        <div class="subsection">
            <h3>5.2. Otimizações Laravel</h3>

            <div class="best-practice">
                <h4>Boas Práticas para Laravel</h4>
                <ul>
                    <li>Use o comando <code>php artisan optimize</code> em produção</li>
                    <li>Configure o cache de rotas: <code>php artisan route:cache</code></li>
                    <li>Cache de configuração: <code>php artisan config:cache</code></li>
                    <li>Cache de views: <code>php artisan view:cache</code></li>
                    <li>Use Eager Loading para evitar o problema N+1</li>
                    <li>Prefira Collections do Laravel apenas quando necessário</li>
                    <li>Utilize Jobs em filas para operações pesadas ou assíncronas</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo: Jobs para Processamento Assíncrono</h4>
                <pre>
// Em um controller
public function sendNotifications(Request $request)
{
    // Em vez de processar no controller
    dispatch(new SendBulkNotificationsJob($request->userIds));
    
    return response()->json(['status' => 'processing']);
}

// Job classe
class SendBulkNotificationsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    protected $userIds;
    
    public function __construct(array $userIds)
    {
        $this->userIds = $userIds;
    }
    
    public function handle()
    {
        foreach ($this->userIds as $userId) {
            // Processamento que pode ser pesado
            $this->notificationService->sendToUser($userId);
        }
    }
}
                </pre>
            </div>
        </div>
    </section>

    <section id="arquitetura" class="manual-section">
        <h2>6. Arquitetura para Performance</h2>

        <div class="subsection">
            <h3>6.1. Arquitetura em Camadas</h3>

            <div class="content-block">
                <p>Uma arquitetura bem planejada pode facilitar a otimização de performance:</p>

                <div class="architecture-diagram">
                    <div class="arch-layer">
                        <h4>Cliente</h4>
                        <p>Navegador, App Mobile, Cliente API</p>
                    </div>
                    <div class="arch-layer">
                        <h4>CDN / Cache de Borda</h4>
                        <p>Cloudflare, Fastly, AWS CloudFront</p>
                    </div>
                    <div class="arch-layer">
                        <h4>Load Balancer</h4>
                        <p>Nginx, HAProxy, AWS ELB</p>
                    </div>
                    <div class="arch-layer">
                        <h4>API Gateway</h4>
                        <p>Gerenciamento de Tráfego, Segurança, Rate Limiting</p>
                    </div>
                    <div class="arch-layer">
                        <h4>API Servers (Horizontally Scaled)</h4>
                        <p>Múltiplas instâncias conforme necessidade</p>
                    </div>
                    <div class="arch-layer split">
                        <div class="half">
                            <h4>Database Cluster</h4>
                            <p>Master/Slaves, Sharding</p>
                        </div>
                        <div class="half">
                            <h4>Cache Layer</h4>
                            <p>Redis, Memcached</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="subsection">
            <h3>6.2. Técnicas Arquiteturais</h3>

            <div class="best-practice">
                <h4>Técnicas para APIs de Alta Performance</h4>
                <ul>
                    <li><strong>Microserviços</strong>: Dividir APIs monolíticas em serviços menores e especializados
                    </li>
                    <li><strong>CQRS (Command Query Responsibility Segregation)</strong>: Separar operações de leitura
                        das de escrita</li>
                    <li><strong>Event-Driven Architecture</strong>: Usar eventos para comunicação assíncrona entre
                        serviços</li>
                    <li><strong>API Gateway</strong>: Centralizar funcionalidades como cache, autenticação e rate
                        limiting</li>
                    <li><strong>Read Replicas</strong>: Usar réplicas de banco de dados dedicadas para consultas</li>
                    <li><strong>Sharding</strong>: Particionar dados para distribuir carga entre múltiplos servidores
                    </li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo: Implementação de CQRS Simples</h4>
                <pre>
// QueryBus para operações de leitura
class UserQueryService 
{
    public function findById($id)
    {
        // Pode utilizar um banco de dados otimizado para leitura
        return Cache::remember("user.$id", 3600, function () use ($id) {
            return DB::connection('read_replica')
                ->table('users')
                ->find($id);
        });
    }
}

// CommandBus para operações de escrita
class UserCommandService
{
    public function create(array $data)
    {
        // Escreve no banco master
        $user = User::create($data);
        
        // Dispara evento para sistemas dependentes
        event(new UserCreated($user));
        
        // Limpa caches relacionados
        Cache::tags(['users'])->flush();
        
        return $user;
    }
}
                </pre>
            </div>
        </div>
    </section>

    <section id="monitoramento" class="manual-section">
        <h2>7. Monitoramento</h2>

        <div class="subsection">
            <h3>7.1. Ferramentas de Monitoramento</h3>

            <div class="tools-list">
                <ul>
                    <li>
                        <strong>New Relic</strong>
                        <p>Solução completa de APM (Application Performance Monitoring) que oferece monitoramento
                            detalhado em tempo real.</p>
                    </li>
                    <li>
                        <strong>Datadog</strong>
                        <p>Plataforma de monitoramento com dashboards personalizáveis, alertas e integração com diversas
                            ferramentas.</p>
                    </li>
                    <li>
                        <strong>Prometheus + Grafana</strong>
                        <p>Combinação open-source para coleta e visualização de métricas.</p>
                    </li>
                    <li>
                        <strong>Laravel Telescope</strong>
                        <p>Ferramenta de debug e monitoramento integrada ao Laravel.</p>
                    </li>
                    <li>
                        <strong>Scout APM</strong>
                        <p>APM focado em identificar gargalos e otimizar consultas.</p>
                    </li>
                </ul>
            </div>
        </div>

        <div class="subsection">
            <h3>7.2. Métricas a Monitorar</h3>

            <div class="metrics-list">
                <table>
                    <thead>
                        <tr>
                            <th>Categoria</th>
                            <th>Métricas</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Infraestrutura</td>
                            <td>
                                <ul>
                                    <li>CPU Usage</li>
                                    <li>Memory Usage</li>
                                    <li>Disk I/O</li>
                                    <li>Network Traffic</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Aplicação</td>
                            <td>
                                <ul>
                                    <li>Response Time</li>
                                    <li>Error Rate</li>
                                    <li>Request Rate</li>
                                    <li>Throughput</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Banco de Dados</td>
                            <td>
                                <ul>
                                    <li>Query Time</li>
                                    <li>Connection Pool Usage</li>
                                    <li>Slow Queries Count</li>
                                    <li>Index Usage</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Cache</td>
                            <td>
                                <ul>
                                    <li>Hit Rate</li>
                                    <li>Miss Rate</li>
                                    <li>Eviction Rate</li>
                                    <li>Memory Usage</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Business</td>
                            <td>
                                <ul>
                                    <li>API Call Volume por Endpoint</li>
                                    <li>Conversion Rates</li>
                                    <li>Error Rates por Feature</li>
                                    <li>User Engagement</li>
                                </ul>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="subsection">
            <h3>7.3. Implementação de Monitoramento</h3>

            <div class="code-block">
                <h4>Exemplo: Instrumentação Personalizada em Laravel</h4>
                <pre>
        // Middleware para capturar métricas de requisição
        class PerformanceMonitorMiddleware
        {
            public function handle($request, Closure $next)
            {
                $startTime = microtime(true);
                
                // Executa a requisição
                $response = $next($request);
                
                // Calcula tempo de processamento
                $requestTime = microtime(true) - $startTime;
                
                // Registra métricas
                $endpoint = $request->route()->getName() ?? $request->path();
                $this->recordMetric("api.response_time.$endpoint", $requestTime);
                $this->recordMetric("api.request_count.$endpoint", 1);
                
                // Adiciona headers de performance (opcional)
                $response->headers->set('X-Response-Time', round($requestTime * 1000) . 'ms');
                
                return $response;
            }
            
            protected function recordMetric($name, $value)
            {
                // Integrar com seu sistema de métricas (Prometheus, StatsD, etc.)
                if (app()->bound('metrics')) {
                    app('metrics')->record($name, $value);
                }
                
                // Alternativa: Log para posterior análise
                Log::channel('metrics')->info($name, [
                    'value' => $value,
                    'timestamp' => microtime(true),
                ]);
            }
        }
                </pre>
            </div>

            <div class="best-practice">
                <h4>Implementando Tracing Distribuído</h4>
                <p>Para APIs que fazem parte de um ecossistema maior, implemente tracing distribuído para visualizar
                    toda a cadeia de requisições:</p>
                <pre>
        // Usando o pacote OpenTracing para PHP
        $tracer = app('tracer');
        
        $span = $tracer->startSpan('process_order');
        $span->setTag('order_id', $orderId);
        
        try {
            // Processar ordem
            $result = $this->orderService->process($orderId);
            $span->setTag('status', 'success');
            return $result;
        } catch (Exception $e) {
            $span->setTag('error', true);
            $span->setTag('error.message', $e->getMessage());
            throw $e;
        } finally {
            $span->finish();
        }
                </pre>
            </div>

            <div class="alerts-section">
                <h4>Configuração de Alertas</h4>
                <p>Configure alertas para notificar sua equipe quando métricas críticas ultrapassarem limites
                    predefinidos:</p>
                <ul>
                    <li>Alerta quando tempo de resposta médio > 500ms por 5 minutos</li>
                    <li>Alerta quando taxa de erros > 1% das requisições</li>
                    <li>Alerta quando uso de CPU > 80% por 10 minutos</li>
                    <li>Alerta quando memória disponível < 20%</li>
                    <li>Alerta quando cache hit rate < 70%</li>
                </ul>
            </div>
        </div>
    </section>

    <section id="checklist" class="manual-section">
        <h2>8. Checklist de Performance</h2>

        <div class="intro-text">
            <p>Use este checklist como referência para garantir que sua API esteja otimizada para performance. Realize
                revisões periódicas para identificar novas oportunidades de melhoria.</p>
        </div>

        <div class="checklist-container">
            <div class="checklist-group">
                <h3>Banco de Dados</h3>
                <ul class="checklist">
                    <li>
                        <input type="checkbox" id="check-db-1">
                        <label for="check-db-1">Todas as colunas frequentemente usadas em WHERE, ORDER BY e JOIN estão
                            indexadas</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-db-2">
                        <label for="check-db-2">Problemas N+1 foram identificados e corrigidos com eager loading</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-db-3">
                        <label for="check-db-3">Queries complexas foram otimizadas e têm explicações de execução
                            aceitáveis</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-db-4">
                        <label for="check-db-4">Todas as consultas selecionam apenas as colunas necessárias</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-db-5">
                        <label for="check-db-5">Paginação implementada em endpoints que retornam múltiplos
                            registros</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-db-6">
                        <label for="check-db-6">Conexões de banco de dados são corretamente fechadas após o uso</label>
                    </li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Caching</h3>
                <ul class="checklist">
                    <li>
                        <input type="checkbox" id="check-cache-1">
                        <label for="check-cache-1">Dados frequentemente acessados e raramente modificados são
                            cacheados</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-cache-2">
                        <label for="check-cache-2">Sistema de invalidação de cache implementado para manter dados
                            atualizados</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-cache-3">
                        <label for="check-cache-3">TTLs apropriados configurados para diferentes tipos de dados</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-cache-4">
                        <label for="check-cache-4">Cache distribuído configurado para ambientes com múltiplos
                            servidores</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-cache-5">
                        <label for="check-cache-5">Cabeçalhos HTTP de cache configurados para respostas
                            apropriadas</label>
                    </li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Código</h3>
                <ul class="checklist">
                    <li>
                        <input type="checkbox" id="check-code-1">
                        <label for="check-code-1">Comandos de otimização do Laravel executados em produção</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-code-2">
                        <label for="check-code-2">Operações pesadas executadas de forma assíncrona usando filas</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-code-3">
                        <label for="check-code-3">Opcache PHP ativado e configurado adequadamente</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-code-4">
                        <label for="check-code-4">Middleware otimizado e desnecessário removido</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-code-5">
                        <label for="check-code-5">Carregamento de recursos externos minimizado</label>
                    </li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Infraestrutura</h3>
                <ul class="checklist">
                    <li>
                        <input type="checkbox" id="check-infra-1">
                        <label for="check-infra-1">Servidor web devidamente otimizado (Nginx, Apache)</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-infra-2">
                        <label for="check-infra-2">Compressão GZIP/Brotli ativada</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-infra-3">
                        <label for="check-infra-3">Estratégia de escalonamento horizontal implementada</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-infra-4">
                        <label for="check-infra-4">CDN configurado para recursos estáticos ou respostas
                            cacheáveis</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-infra-5">
                        <label for="check-infra-5">Sistema de monitoramento em tempo real implementado</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-infra-6">
                        <label for="check-infra-6">Rate limiting configurado para evitar sobrecarga</label>
                    </li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Monitoramento e Testes</h3>
                <ul class="checklist">
                    <li>
                        <input type="checkbox" id="check-monitor-1">
                        <label for="check-monitor-1">Testes de carga realizados regularmente</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-monitor-2">
                        <label for="check-monitor-2">Alertas configurados para métricas críticas</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-monitor-3">
                        <label for="check-monitor-3">Profiling realizado em endpoints críticos</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-monitor-4">
                        <label for="check-monitor-4">Dashboards de performance acessíveis à equipe</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-monitor-5">
                        <label for="check-monitor-5">Procedimento documentado para análise de gargalos</label>
                    </li>
                </ul>
            </div>
        </div>

        <div class="conclusion">
            <h3>Próximos Passos</h3>
            <p>Performance é um processo contínuo, não um estado final. Após implementar as melhorias seguindo este
                manual:</p>
            <ol>
                <li>Estabeleça uma linha de base de performance para comparações futuras</li>
                <li>Realize testes de carga regulares para identificar novos gargalos</li>
                <li>Revise periodicamente suas métricas de performance</li>
                <li>Mantenha-se atualizado com novas técnicas e ferramentas de otimização</li>
                <li>Documente as melhorias implementadas e seus resultados</li>
            </ol>
        </div>
    </section>

    <footer class="manual-footer">
        <div class="footer-content">
            <p>Manual de Performance para APIs PHP © 2023</p>
            <p>Para atualizações e mais recursos, visite nossa <a href="#">documentação online</a>.</p>
        </div>
    </footer>

</body>

</html>