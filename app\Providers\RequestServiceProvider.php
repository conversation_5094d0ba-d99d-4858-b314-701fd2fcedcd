<?php

namespace App\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;

class RequestServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Request::macro('id', function () {
            $requestId = $this->header('X-Request-ID');

            if (!$requestId) {
                $requestId = (string) Str::uuid();
                $this->headers->set('X-Request-ID', $requestId);
            }

            return $requestId;
        });
    }
}
