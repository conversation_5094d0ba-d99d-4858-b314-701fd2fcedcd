<?php

namespace Tests\Unit\Integration;

use App\Models\UserModel;
use App\Services\JwtService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\Group;
use Tests\TestCase;

#[Group('integration')]
class JwtServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $jwtService;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Configuração do banco de dados em memória
        $this->app['config']->set('database.default', 'sqlite');
        $this->app['config']->set('database.connections.sqlite', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]);

        // Criar tabela de usuários manualmente
        \Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();
            $table->timestamps();
        });

        // Criar tabela de roles (para evitar erros de relacionamento)
        \Schema::create('roles', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->nullable();
            $table->string('description')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Tabela pivot user_roles
        \Schema::create('user_roles', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users');
            $table->foreignId('role_id')->constrained('roles');
            $table->timestamps();
        });

        // Criar usuário para teste
        $this->user = UserModel::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $this->jwtService = new JwtService();
    }

    #[Test]
    public function it_can_generate_token()
    {
        // Configura TTL para teste
        config(['jwt.ttl' => 60]);

        // Execute com usuário real do banco de dados
        $result = $this->jwtService->generateToken($this->user);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('token_type', $result);
        $this->assertArrayHasKey('expires_in', $result);
        $this->assertArrayHasKey('refresh_token', $result);

        // Verificando a estrutura do token
        $this->assertEquals('bearer', $result['token_type']);
        $this->assertIsInt($result['expires_in']);
        $this->assertNotEmpty($result['access_token']);
    }

    #[Test]
    public function it_can_refresh_token()
    {
        // Configurar para teste
        config(['jwt.ttl' => 60]);
        config(['jwt.refresh_ttl' => 20160]);

        // Primeiro gere um token
        $tokenData = $this->jwtService->generateToken($this->user);
        $refreshToken = $tokenData['refresh_token'];

        // Agora tente atualizar o token
        $result = $this->jwtService->refreshToken($refreshToken);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('token_type', $result);
        $this->assertArrayHasKey('expires_in', $result);
        $this->assertArrayHasKey('refresh_token', $result);

        // Verificando a estrutura
        $this->assertEquals('bearer', $result['token_type']);

        // Token deve ser diferente
        $this->assertNotEquals($tokenData['access_token'], $result['access_token']);
    }

    protected function tearDown(): void
    {
        // Limpar esquema
        \Schema::dropIfExists('user_roles');
        \Schema::dropIfExists('roles');
        \Schema::dropIfExists('users');

        parent::tearDown();
    }
}
