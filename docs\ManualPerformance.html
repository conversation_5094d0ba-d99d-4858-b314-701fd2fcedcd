<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Performance para APIs</title>
    <link rel="stylesheet" href="css/manual.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Performance para APIs PHP</h1>
        <p>Guia completo para otimização de performance em APIs Laravel</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="index.html" title="Página Inicial"><i class="fas fa-home"></i></a></li>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#fundamentos">Fundamentos</a></li>
            <li><a href="#otimizacao-backend">Backend</a></li>
            <li><a href="#otimizacao-frontend">Frontend</a></li>
            <li><a href="#infraestrutura">Infraestrutura</a></li>
            <li><a href="#monitoramento">Monitoramento</a></li>
            <li><a href="#escala">Escalabilidade</a></li>
            <li><a href="#performance-testing">Testes</a></li>
            <li><a href="#laravel-12-performance">Laravel 12</a></li>
            <li><a href="#case-studies">Estudos de Caso</a></li>
            <li><a href="#checklist">Checklist</a></li>
            <li><a href="#conclusao">Conclusão</a></li>
        </ul>
    </nav>

    <section id="sumario" class="manual-section">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a></li>
            <li><a href="#fundamentos">2. Fundamentos de Performance</a>
                <ul>
                    <li><a href="#metricas-chave">2.1. Métricas-Chave de Performance</a></li>
                    <li><a href="#fatores-impacto">2.2. Fatores que Impactam a Performance</a></li>
                    <li><a href="#objetivos-performance">2.3. Definindo Objetivos de Performance</a></li>
                </ul>
            </li>
            <li><a href="#otimizacao-backend">3. Otimização de Backend</a>
                <ul>
                    <li><a href="#queries-db">3.1. Otimização de Queries e Banco de Dados</a></li>
                    <li><a href="#eloquent-optimization">3.2. Otimizando o Eloquent</a></li>
                    <li><a href="#caching">3.3. Estratégias de Cache</a></li>
                    <li><a href="#queues">3.4. Filas e Processamento Assíncrono</a></li>
                </ul>
            </li>
            <li><a href="#otimizacao-frontend">4. Otimização de Frontend</a>
                <ul>
                    <li><a href="#assets-optimization">4.1. Otimização de Assets</a></li>
                    <li><a href="#lazy-loading">4.2. Lazy Loading e Carregamento Progressivo</a></li>
                    <li><a href="#js-optimization">4.3. Otimização de JavaScript</a></li>
                    <li><a href="#css-optimization">4.4. Otimização de CSS</a></li>
                </ul>
            </li>
            <li><a href="#infraestrutura">5. Otimização de Infraestrutura</a>
                <ul>
                    <li><a href="#servidor-web">5.1. Configuração de Servidor Web</a></li>
                    <li><a href="#php-fpm">5.2. Otimização de PHP-FPM</a></li>
                    <li><a href="#opcache">5.3. Configuração de OPCache</a></li>
                    <li><a href="#cdn">5.4. Implementação de CDN</a></li>
                </ul>
            </li>
            <li><a href="#monitoramento">6. Monitoramento e Profiling</a>
                <ul>
                    <li><a href="#ferramentas-monitoramento">6.1. Ferramentas de Monitoramento</a></li>
                    <li><a href="#profiling">6.2. Profiling de Aplicação</a></li>
                    <li><a href="#logs-performance">6.3. Logs e Alertas de Performance</a></li>
                </ul>
            </li>
            <li><a href="#escala">7. Escalabilidade</a>
                <ul>
                    <li><a href="#horizontal-scaling">7.1. Escalabilidade Horizontal</a></li>
                    <li><a href="#vertical-scaling">7.2. Escalabilidade Vertical</a></li>
                    <li><a href="#microservices">7.3. Arquitetura de Microsserviços</a></li>
                </ul>
            </li>
            <li><a href="#performance-testing">8. Testes de Performance</a>
                <ul>
                    <li><a href="#load-testing">8.1. Testes de Carga</a></li>
                    <li><a href="#stress-testing">8.2. Testes de Estresse</a></li>
                    <li><a href="#benchmark">8.3. Benchmarking</a></li>
                </ul>
            </li>
            <li><a href="#laravel-12-performance">9. Recursos de Performance do Laravel 12</a>
                <ul>
                    <li><a href="#octane">9.1. Laravel Octane</a></li>
                    <li><a href="#parallel-testing">9.2. Testes Paralelos</a></li>
                    <li><a href="#performance-improvements">9.3. Melhorias de Performance no Laravel 12</a></li>
                </ul>
            </li>
            <li><a href="#case-studies">10. Estudos de Caso</a>
                <ul>
                    <li><a href="#case-study-1">10.1. Otimização de API de Alto Tráfego</a></li>
                    <li><a href="#case-study-2">10.2. Melhorando Performance de E-commerce</a></li>
                </ul>
            </li>
            <li><a href="#checklist">11. Checklist de Performance</a></li>
            <li><a href="#conclusao">12. Conclusão e Melhores Práticas</a></li>
        </ul>
    </section>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>

        <div class="intro-text">
            <p>A performance é um aspecto crítico no desenvolvimento de APIs. Uma API lenta não apenas prejudica a
                experiência do usuário, mas também pode aumentar custos operacionais, reduzir a escalabilidade e limitar
                a adoção de seus serviços.</p>

            <p>Este manual apresenta as melhores práticas para otimização de performance em APIs PHP, com foco especial
                no framework Laravel 12, aproveitando suas novas funcionalidades e melhorias de desempenho.</p>
        </div>

        <div class="key-points">
            <h3>Pontos-chave sobre Performance</h3>
            <ul>
                <li>Performance impacta diretamente a experiência do usuário e o SEO (para APIs públicas)</li>
                <li>APIs rápidas podem reduzir custos de infraestrutura significativamente</li>
                <li>A otimização de performance deve começar desde o planejamento, não apenas no final</li>
                <li>Métricas e monitoramento são essenciais para identificar gargalos</li>
                <li>Laravel 12 traz melhorias significativas de performance que devem ser aproveitadas</li>
            </ul>
        </div>
    </section>

    <section id="fundamentos" class="manual-section">
        <h2>2. Fundamentos de Performance</h2>

        <section id="metricas-chave" class="subsection">
            <h3>2.1. Métricas-Chave de Performance</h3>

            <div class="metrics-table">
                <table>
                    <thead>
                        <tr>
                            <th>Métrica</th>
                            <th>Descrição</th>
                            <th>Valor Ideal</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Tempo de Resposta</td>
                            <td>Tempo entre requisição e resposta completa</td>
                            <td>&lt; 200ms</td>
                        </tr>
                        <tr>
                            <td>Tempo até o Primeiro Byte (TTFB)</td>
                            <td>Tempo até o primeiro byte ser recebido pelo cliente</td>
                            <td>&lt; 100ms</td>
                        </tr>
                        <tr>
                            <td>Requisições por Segundo (RPS)</td>
                            <td>Número máximo de requisições que a API pode processar por segundo</td>
                            <td>Depende do requisito de negócio</td>
                        </tr>
                        <tr>
                            <td>Uso de CPU</td>
                            <td>Percentual de uso de CPU durante operação</td>
                            <td>&lt; 70% em carga normal</td>
                        </tr>
                        <tr>
                            <td>Uso de Memória</td>
                            <td>Quantidade de RAM consumida pelo processo</td>
                            <td>Estável, sem vazamentos</td>
                        </tr>
                        <tr>
                            <td>Tempo de Consulta DB</td>
                            <td>Tempo para execução de consultas ao banco de dados</td>
                            <td>&lt; 50ms</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="tools-list">
                <h4>Ferramentas de Medição</h4>
                <ul>
                    <li>
                        <strong>Laravel Telescope</strong>
                        <p>Ferramenta de debug e monitoramento integrada ao Laravel, ideal para ambiente de
                            desenvolvimento.</p>
                    </li>
                    <li>
                        <strong>Laravel Pulse</strong>
                        <p>Nova ferramenta de monitoramento em tempo real introduzida no Laravel, oferecendo insights
                            sobre requisições, jobs, e mais.</p>
                    </li>
                    <li>
                        <strong>Laravel Debugbar</strong>
                        <p>Barra de debug que mostra consultas SQL, uso de memória, tempo de requisição e mais.</p>
                    </li>
                    <li>
                        <strong>Blackfire.io</strong>
                        <p>Profiler para PHP que oferece insights detalhados sobre performance em produção.</p>
                    </li>
                    <li>
                        <strong>New Relic</strong>
                        <p>Plataforma de monitoramento para aplicações em produção.</p>
                    </li>
                    <li>
                        <strong>Prometheus + Grafana</strong>
                        <p>Combinação de ferramentas open-source para coleta e visualização de métricas.</p>
                    </li>
                </ul>
            </div>
        </section>

        <section id="fatores-impacto" class="subsection">
            <h3>2.2. Fatores que Impactam a Performance</h3>

            <div class="content-block">
                <p>Diversos fatores podem impactar a performance de uma API. Entender esses fatores é o primeiro passo
                    para otimizá-los:</p>

                <ul>
                    <li><strong>Consultas ao Banco de Dados</strong> - Frequentemente o maior gargalo em aplicações web
                    </li>
                    <li><strong>Processamento de Dados</strong> - Operações complexas de transformação ou cálculo</li>
                    <li><strong>Chamadas a Serviços Externos</strong> - APIs de terceiros, microsserviços, etc.</li>
                    <li><strong>Configuração de Servidor</strong> - Configurações inadequadas de PHP, servidor web, etc.
                    </li>
                    <li><strong>Arquitetura da Aplicação</strong> - Design que não considera performance desde o início
                    </li>
                    <li><strong>Volume de Dados</strong> - Quantidade de dados processados e transferidos</li>
                    <li><strong>Concorrência</strong> - Número de requisições simultâneas</li>
                    <li><strong>Infraestrutura</strong> - Recursos de hardware disponíveis</li>
                </ul>

                <div class="code-block">
                    <h4>Exemplo: Medição Manual de Performance</h4>
                    <pre>
// Medir tempo de execução
$startTime = microtime(true);

// Código que deseja medir...
$result = $this->someService->performOperation();

// Calcular tempo decorrido
$executionTime = microtime(true) - $startTime;
Log::debug("Operação executada em {$executionTime} segundos");

// Medir uso de memória
$memoryUsage = memory_get_usage(true) / 1024 / 1024;
Log::debug("Uso de memória: {$memoryUsage} MB");
                    </pre>
                </div>
            </div>
        </section>

        <section id="objetivos-performance" class="subsection">
            <h3>2.3. Definindo Objetivos de Performance</h3>

            <div class="content-block">
                <p>Antes de iniciar otimizações, é importante definir objetivos claros e mensuráveis:</p>

                <div class="best-practice">
                    <h4>Estabelecendo Objetivos de Performance</h4>
                    <ul>
                        <li><strong>Específicos</strong> - "Reduzir tempo médio de resposta para menos de 200ms" em vez
                            de "Melhorar a velocidade"</li>
                        <li><strong>Mensuráveis</strong> - Defina métricas que podem ser medidas objetivamente</li>
                        <li><strong>Alcançáveis</strong> - Considere as limitações técnicas e de recursos</li>
                        <li><strong>Relevantes</strong> - Foque em melhorias que realmente importam para os usuários
                        </li>
                        <li><strong>Temporais</strong> - Estabeleça prazos para atingir os objetivos</li>
                    </ul>
                </div>

                <div class="metrics-table">
                    <h4>Exemplos de Objetivos de Performance</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Objetivo</th>
                                <th>Métrica</th>
                                <th>Valor Alvo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Tempo de resposta rápido</td>
                                <td>Tempo médio de resposta</td>
                                <td>&lt; 200ms para 95% das requisições</td>
                            </tr>
                            <tr>
                                <td>Alta capacidade</td>
                                <td>Requisições por segundo</td>
                                <td>&gt; 1000 RPS com latência estável</td>
                            </tr>
                            <tr>
                                <td>Eficiência de banco de dados</td>
                                <td>Tempo médio de consulta</td>
                                <td>&lt; 50ms para 99% das consultas</td>
                            </tr>
                            <tr>
                                <td>Uso eficiente de recursos</td>
                                <td>Uso de memória por requisição</td>
                                <td>&lt; 50MB por processo PHP</td>
                            </tr>
                            <tr>
                                <td>Disponibilidade</td>
                                <td>Uptime</td>
                                <td>&gt; 99.9% (menos de 43 minutos de downtime por mês)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </section>

    <section id="otimizacao-backend" class="manual-section">
        <h2>3. Otimização de Backend</h2>

        <section id="queries-db" class="subsection">
            <h3>3.1. Otimização de Queries e Banco de Dados</h3>

            <div class="content-block">
                <p>Consultas ao banco de dados são frequentemente o principal gargalo de performance em APIs. Otimizar
                    essas
                    operações pode trazer ganhos substanciais.</p>

                <h4>3.1.1. Indexação Eficiente</h4>
                <p>Índices são fundamentais para consultas rápidas, mas seu uso excessivo pode prejudicar operações de
                    escrita.</p>

                <div class="best-practice">
                    <h4>Boas Práticas de Indexação</h4>
                    <ul>
                        <li>Crie índices para colunas frequentemente usadas em cláusulas WHERE, ORDER BY e JOIN</li>
                        <li>Use índices compostos para consultas que filtram por múltiplas colunas</li>
                        <li>Evite indexação excessiva - cada índice aumenta o tempo de operações de escrita</li>
                        <li>Monitore índices não utilizados e remova-os</li>
                        <li>Prefira índices focados nas consultas mais frequentes e críticas</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Criação de Índices em Laravel 12</h4>
                    <pre>
// Em uma migration Laravel
Schema::table('products', function (Blueprint $table) {
    // Índice simples
    $table->index('category_id');
    
    // Índice composto
    $table->index(['status', 'created_at']);
    
    // Índice único
    $table->unique('sku');
    
    // Índice fulltext (MySQL 5.7+)
    $table->fullText('description');
    
    // Índice espacial (MySQL 5.7+, PostgreSQL 9.6+)
    $table->spatialIndex('location');
});
                    </pre>
                </div>

                <h4>3.1.2. Otimização de Queries</h4>

                <div class="comparison-table">
                    <h4>Práticas Recomendadas vs. Práticas Problemáticas</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Problema</th>
                                <th>Prática Ruim</th>
                                <th>Prática Recomendada</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>N+1 Queries</td>
                                <td>
                                    <pre>
$orders = Order::all();
foreach ($orders as $order) {
    echo $order->customer->name;  // Query adicional para cada ordem
}
                                    </pre>
                                </td>
                                <td>
                                    <pre>
$orders = Order::with('customer')->get();
foreach ($orders as $order) {
    echo $order->customer->name;  // Dados já carregados
}
                                    </pre>
                                </td>
                            </tr>
                            <tr>
                                <td>Selecionando Muitas Colunas</td>
                                <td>
                                    <pre>
$users = User::all();  // SELECT * FROM users
                                    </pre>
                                </td>
                                <td>
                                    <pre>
$users = User::select('id', 'name', 'email')->get();  // Apenas as colunas necessárias
                                    </pre>
                                </td>
                            </tr>
                            <tr>
                                <td>Não Usar Paginação</td>
                                <td>
                                    <pre>
$products = Product::all();  // Pode retornar milhares de registros
                                    </pre>
                                </td>
                                <td>
                                    <pre>
$products = Product::paginate(25);  // Apenas 25 registros por vez

// Ou usando cursor para processamento eficiente de grandes conjuntos
foreach (Product::cursor() as $product) {
    // Processa um produto por vez sem carregar todos na memória
}
                                    </pre>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="best-practice">
                    <h4>Técnicas Avançadas de Otimização de Queries</h4>
                    <ul>
                        <li>Use subconsultas com cautela - elas podem ser mais lentas que JOINs</li>
                        <li>Prefira COUNT(*) a COUNT(1) ou COUNT(coluna) para contagem total de registros</li>
                        <li>Utilize chunking para processamento de grandes conjuntos de dados</li>
                        <li>Considere views materializadas para consultas complexas frequentemente executadas</li>
                        <li>Implemente lazy loading apenas quando necessário e eager loading como padrão</li>
                        <li>Aproveite as novas otimizações de query do Laravel 12 para relacionamentos</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Chunking para Processamento de Grandes Datasets</h4>
                    <pre>
// Processar 100.000 registros em lotes de 1.000
User::chunk(1000, function ($users) {
    foreach ($users as $user) {
        // Processar cada usuário
    }
});

// Alternativa: usar Lazy Collection (Laravel 8+)
foreach (User::cursor() as $user) {
    // Processar cada usuário sem carregar todos na memória
}

// Nova funcionalidade no Laravel 12: Lazy By ID
User::lazyById(1000, $column = 'id')->each(function ($user) {
    // Processa cada usuário de forma eficiente
});
                    </pre>
                </div>
            </div>
        </section>

        <section id="eloquent-optimization" class="subsection">
            <h3>3.2. Otimizando o Eloquent</h3>

            <div class="content-block">
                <p>O Eloquent ORM do Laravel oferece grande produtividade, mas pode introduzir overhead se não for usado
                    corretamente.</p>

                <div class="best-practice">
                    <h4>Otimizações do Eloquent</h4>
                    <ul>
                        <li>Use eager loading (<code>with()</code>) para evitar o problema N+1</li>
                        <li>Selecione apenas as colunas necessárias com <code>select()</code></li>
                        <li>Use <code>chunk()</code> ou <code>cursor()</code> para grandes conjuntos de dados</li>
                        <li>Considere <code>DB::raw()</code> para consultas complexas que o Eloquent não otimiza bem
                        </li>
                        <li>Utilize <code>whereHas()</code> com closure para consultas relacionadas eficientes</li>
                        <li>Aproveite <code>withCount()</code>, <code>withSum()</code>, etc. para agregações</li>
                        <li>Use <code>toBase()</code> quando não precisar de instâncias completas do modelo</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Otimizações do Eloquent</h4>
                    <pre>
// Ruim: Carrega todos os campos e causa N+1 queries
$posts = Post::all();
foreach ($posts as $post) {
    echo $post->user->name;
    echo count($post->comments);
}

// Bom: Carrega apenas campos necessários, usa eager loading e withCount
$posts = Post::select('id', 'title', 'user_id', 'created_at')
    ->with(['user:id,name'])
    ->withCount('comments')
    ->get();

foreach ($posts as $post) {
    echo $post->user->name;
    echo $post->comments_count;
}

// Melhor ainda: Adiciona paginação para grandes conjuntos
$posts = Post::select('id', 'title', 'user_id', 'created_at')
    ->with(['user:id,name'])
    ->withCount('comments')
    ->latest()
    ->paginate(20);
                    </pre>
                </div>

                <div class="tip">
                    <h4>Dica: Query Builder vs. Eloquent</h4>
                    <p>Para operações muito intensivas onde a performance é crítica, considere usar o Query Builder
                        diretamente em vez do Eloquent:</p>
                    <pre>
// Eloquent (mais conveniente, mas com overhead)
$users = User::where('active', true)
    ->where('last_login_at', '>=', now()->subDays(30))
    ->get();

// Query Builder (mais rápido para operações simples de leitura)
$users = DB::table('users')
    ->where('active', true)
    ->where('last_login_at', '>=', now()->subDays(30))
    ->get();
                    </pre>
                </div>
            </div>
        </section>

        <section id="caching" class="subsection">
            <h3>3.3. Estratégias de Cache</h3>

            <div class="content-block">
                <p>O cache é uma das estratégias mais eficazes para melhorar a performance de APIs.</p>

                <h4>3.3.1. Níveis de Cache</h4>
                <div class="layers-diagram">
                    <div class="layer">
                        <h4>Cache de Aplicação</h4>
                        <p>Armazena resultados de operações custosas, como consultas complexas. Implementado com Redis,
                            Memcached ou similar.</p>
                    </div>
                    <div class="layer">
                        <h4>Cache de Rota/HTTP</h4>
                        <p>Armazena respostas completas da API para rotas específicas, normalmente implementado usando
                            HTTP Cache.</p>
                    </div>
                    <div class="layer">
                        <h4>Cache de Frontend/CDN</h4>
                        <p>Cache no lado do cliente ou em CDN, controlado por cabeçalhos HTTP.</p>
                    </div>
                </div>

                <div class="code-block">
                    <h4>Cache de Dados em Laravel 12</h4>
                    <pre>
// Cache de dados calculados
public function getStatistics()
{
    return Cache::remember('statistics', 3600, function () {
        return $this->calculateExpensiveStatistics();
    });
}

// Cache por usuário
public function getUserDashboard($userId)
{
    return Cache::remember("user.{$userId}.dashboard", 1800, function () use ($userId) {
        return $this->dashboardService->generate($userId);
    });
}

// Novo em Laravel 12: Cache com TTL dinâmico
public function getCachedData($key, $ttl = null)
{
    $ttl = $ttl ?? (app()->environment('production') ? 3600 : 60);
    
    return Cache::remember($key, $ttl, function () {
        return $this->expensiveOperation();
    });
}
                    </pre>
                </div>

                <div class="code-block">
                    <h4>Cache HTTP em Laravel 12</h4>
                    <pre>
// Em um middleware ou controller
public function index()
{
    $data = $this->service->getData();
    
    return response($data)
        ->header('Cache-Control', 'public, max-age=60')
        ->header('Etag', md5(json_encode($data)));
}

// Usando pacote spatie/laravel-responsecache
class ProductsController
{
    public function index()
    {
        return ResponseCache::onTag('products')->maxAge(60)->json(
            Product::paginate(20)
        );
    }
}

// Usando o novo middleware de cache HTTP do Laravel 12
Route::get('/products', [ProductController::class, 'index'])
    ->middleware('cache.headers:public;max_age=60;etag');
                    </pre>
                </div>

                <div class="best-practice">
                    <h4>Boas Práticas de Cache</h4>
                    <ul>
                        <li>Use TTL (Time To Live) apropriado para cada tipo de dado</li>
                        <li>Implemente tags de cache para facilitar a invalidação seletiva</li>
                        <li>Considere estratégias de cache específicas para cada endpoint</li>
                        <li>Use cache condicional (ETag, If-Modified-Since) para APIs com muitas requisições</li>
                        <li>Evite cache para dados personalizados ou sensíveis</li>
                        <li>Aproveite o driver de cache otimizado do Laravel 12</li>
                        <li>Considere usar o Redis como driver de cache para melhor performance</li>
                    </ul>
                </div>

                <div class="tip">
                    <p>Implemente invalidação automática de cache ao atualizar registros para manter a consistência dos
                        dados:</p>
                    <pre>
// Em um model Observer
public function saved(Product $product)
{
    Cache::tags(['products'])->flush();
}

// Ou usando eventos do modelo
Product::saved(function ($product) {
    Cache::tags(['products'])->flush();
});
                    </pre>
                </div>
            </div>
        </section>

        <section id="queues" class="subsection">
            <h3>3.4. Filas e Processamento Assíncrono</h3>

            <div class="content-block">
                <p>Processar tarefas pesadas de forma assíncrona é uma estratégia fundamental para melhorar a
                    performance de APIs. Isso permite que a API responda rapidamente ao cliente enquanto processa
                    operações demoradas em segundo plano.</p>

                <div class="best-practice">
                    <h4>Operações que devem ser processadas em filas</h4>
                    <ul>
                        <li>Envio de emails e notificações</li>
                        <li>Processamento de arquivos (uploads, geração de relatórios)</li>
                        <li>Sincronização com serviços externos</li>
                        <li>Operações em lote ou que consomem muitos recursos</li>
                        <li>Tarefas agendadas e recorrentes</li>
                        <li>Processamento de webhooks</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Implementação de Filas no Laravel 12</h4>
                    <pre>
// Criando um Job
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessPdfReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reportData;
    protected $userId;

    public function __construct(array $reportData, int $userId)
    {
        $this->reportData = $reportData;
        $this->userId = $userId;
    }

    public function handle(): void
    {
        // Lógica para gerar o PDF
        $pdf = $this->generatePdf($this->reportData);
        
        // Salvar o arquivo
        Storage::put("reports/{$this->userId}/report.pdf", $pdf->output());
        
        // Notificar o usuário
        $user = User::find($this->userId);
        $user->notify(new ReportReadyNotification("reports/{$this->userId}/report.pdf"));
    }
    
    // Novas funcionalidades do Laravel 12
    public function retryUntil(): \DateTime
    {
        return now()->addHours(12); // Tentar por até 12 horas
    }
    
    public function backoff(): array
    {
        return [10, 60, 300]; // Esperar 10s, 60s, 300s entre tentativas
    }
}

// Despachando o job em um controller
public function generateReport(Request $request)
{
    // Validar e preparar dados
    $reportData = $this->prepareReportData($request);
    
    // Despachar o job para processamento em segundo plano
    ProcessPdfReport::dispatch($reportData, auth()->id())
        ->onQueue('reports'); // Especificar a fila
    
    return $this->response->success([
        'message' => 'Seu relatório está sendo gerado e será enviado em breve.'
    ]);
}</pre>
                </div>

                <h4>3.4.1. Configuração de Filas</h4>
                <p>O Laravel 12 suporta vários drivers de filas, cada um com suas vantagens:</p>

                <table>
                    <thead>
                        <tr>
                            <th>Driver</th>
                            <th>Vantagens</th>
                            <th>Desvantagens</th>
                            <th>Recomendado para</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Redis</td>
                            <td>Rápido, confiável, suporta filas com prioridade</td>
                            <td>Requer servidor Redis</td>
                            <td>Produção, alta carga</td>
                        </tr>
                        <tr>
                            <td>Database</td>
                            <td>Fácil configuração, não requer serviços adicionais</td>
                            <td>Mais lento, pode causar contenção no banco de dados</td>
                            <td>Desenvolvimento, baixa carga</td>
                        </tr>
                        <tr>
                            <td>SQS</td>
                            <td>Altamente escalável, gerenciado pela AWS</td>
                            <td>Custo adicional, latência maior</td>
                            <td>Aplicações na AWS</td>
                        </tr>
                        <tr>
                            <td>Beanstalkd</td>
                            <td>Leve, rápido</td>
                            <td>Menos recursos que Redis</td>
                            <td>Cargas médias</td>
                        </tr>
                    </tbody>
                </table>

                <div class="code-block">
                    <h4>Configuração de Workers</h4>
                    <pre>
# Iniciar um worker que processa todas as filas
php artisan queue:work

# Iniciar um worker para uma fila específica
php artisan queue:work --queue=high,default

# Configurar tentativas e tempo limite
php artisan queue:work --tries=3 --timeout=90

# Processar apenas um job e sair
php artisan queue:work --once

# Configuração de supervisor para manter workers rodando
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/project/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/path/to/worker.log
stopwaitsecs=3600</pre>
                </div>

                <h4>3.4.2. Monitoramento de Filas</h4>
                <p>Monitorar filas é essencial para garantir que os jobs estão sendo processados corretamente:</p>

                <div class="code-block">
                    <pre>
// Verificar o status das filas
php artisan queue:monitor

// Limpar jobs falhos
php artisan queue:flush

// Tentar novamente jobs falhos
php artisan queue:retry all

// Usando Laravel Horizon para monitoramento avançado (recomendado para produção)
composer require laravel/horizon

// Publicar configuração do Horizon
php artisan horizon:install

// Iniciar o Horizon
php artisan horizon</pre>
                </div>

                <div class="best-practice">
                    <h4>Boas Práticas para Filas</h4>
                    <ul>
                        <li>Divida as filas por tipo de trabalho (emails, relatórios, etc.)</li>
                        <li>Configure prioridades para jobs críticos</li>
                        <li>Implemente mecanismo de retry com backoff exponencial</li>
                        <li>Monitore o tamanho das filas e o tempo de processamento</li>
                        <li>Configure alertas para falhas de jobs</li>
                        <li>Use o Laravel Horizon em produção para monitoramento avançado</li>
                        <li>Implemente idempotência nos jobs para evitar processamento duplicado</li>
                        <li>Considere o uso de batching para jobs relacionados</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Batching de Jobs no Laravel 12</h4>
                    <pre>
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Throwable;

// Em um controller ou service
public function processLargeDataset(array $dataset)
{
    $jobs = [];
    
    // Dividir o dataset em chunks para processamento paralelo
    foreach (array_chunk($dataset, 100) as $chunk) {
        $jobs[] = new ProcessDataChunk($chunk);
    }
    
    // Criar um batch de jobs
    $batch = Bus::batch($jobs)
        ->then(function (Batch $batch) {
            // Todos os jobs foram processados com sucesso
            Log::info('Batch completo: ' . $batch->id);
            
            // Notificar usuário
            $user = User::find($this->userId);
            $user->notify(new DataProcessingCompleteNotification($batch->id));
        })
        ->catch(function (Batch $batch, Throwable $e) {
            // Um job falhou
            Log::error('Batch falhou: ' . $batch->id, [
                'exception' => $e->getMessage()
            ]);
            
            // Notificar administrador
            Notification::route('mail', '<EMAIL>')
                ->notify(new BatchFailedNotification($batch->id, $e->getMessage()));
        })
        ->finally(function (Batch $batch) {
            // Executado independentemente do resultado
            Log::info('Batch finalizado: ' . $batch->id, [
                'processed' => $batch->processedJobs(),
                'failed' => $batch->failedJobs
            ]);
        })
        ->allowFailures()
        ->onQueue('processing')
        ->dispatch();
    
    return $batch->id;
}</pre>
                </div>

                <div class="tip">
                    <h4>Novidades do Laravel 12 para Filas</h4>
                    <p>O Laravel 12 introduz várias melhorias no sistema de filas:</p>
                    <ul>
                        <li>Melhor suporte para tipagem estrita em jobs</li>
                        <li>Novos métodos para controle de retry e backoff</li>
                        <li>Melhor integração com o sistema de eventos</li>
                        <li>Suporte aprimorado para batching de jobs</li>
                        <li>Melhor desempenho no processamento de filas</li>
                        <li>Horizon com novas métricas e visualizações</li>
                    </ul>
                </div>
            </div>
        </section>
    </section>
    <section id="otimizacao-frontend" class="manual-section">
        <h2>4. Otimização de Frontend</h2>

        <p>Embora este manual foque principalmente em APIs, a otimização do frontend é importante para aplicações
            que servem conteúdo HTML ou que têm uma camada de apresentação.</p>

        <section id="assets-optimization" class="subsection">
            <h3>4.1. Otimização de Assets</h3>

            <div class="content-block">
                <p>A otimização de assets (JavaScript, CSS, imagens) é fundamental para melhorar o tempo de
                    carregamento da aplicação:</p>

                <div class="best-practice">
                    <h4>Estratégias de Otimização de Assets</h4>
                    <ul>
                        <li>Minificação de JavaScript e CSS</li>
                        <li>Concatenação de arquivos para reduzir requisições HTTP</li>
                        <li>Compressão de imagens e uso de formatos modernos (WebP)</li>
                        <li>Implementação de cache de assets com expiração longa</li>
                        <li>Uso de versionamento de assets para invalidação de cache</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Configuração do Vite no Laravel 12</h4>
                    <pre>
// vite.config.js
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: true,
        }),
    ],
    build: {
        // Configurações de build para produção
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: true,
            },
        },
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['vue', 'axios'],
                },
            },
        },
        // Habilitar source maps em desenvolvimento
        sourcemap: process.env.NODE_ENV !== 'production',
    },
});</pre>
                </div>

                <div class="code-block">
                    <h4>Uso de Assets no Blade</h4>
                    <pre>
&lt;!-- Em um template Blade -->
&lt;head>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
&lt;/head>

&lt;!-- Ou para assets específicos -->
&lt;img src="{{ Vite::asset('resources/images/logo.png') }}" alt="Logo">

&lt;!-- Preload de assets críticos -->
&lt;link rel="preload" href="{{ Vite::asset('resources/fonts/main-font.woff2') }}" as="font" type="font/woff2" crossorigin></pre>
                </div>

                <div class="tip">
                    <h4>Dica: Otimização de Imagens</h4>
                    <p>Use o pacote <code>intervention/image</code> para otimizar imagens dinamicamente:</p>
                    <pre>
// Instalação
composer require intervention/image

// Uso em um controller
use Intervention\Image\Facades\Image;

public function optimizeAndStoreImage($imageFile)
{
    // Redimensionar e otimizar
    $img = Image::make($imageFile)
        ->resize(800, null, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        })
        ->encode('webp', 85); // Converter para WebP com 85% de qualidade
    
    // Salvar
    $path = 'images/' . time() . '.webp';
    Storage::put('public/' . $path, $img);
    
    return $path;
}</pre>
                </div>
            </div>
        </section>

        <section id="lazy-loading" class="subsection">
            <h3>4.2. Lazy Loading e Carregamento Progressivo</h3>

            <div class="content-block">
                <p>O carregamento preguiçoso (lazy loading) e progressivo melhora a percepção de velocidade e reduz
                    o tempo de carregamento inicial:</p>

                <div class="code-block">
                    <h4>Lazy Loading de Imagens</h4>
                    <pre>
&lt;!-- HTML nativo -->
&lt;img src="placeholder.jpg" 
     data-src="imagem-real.jpg" 
     loading="lazy" 
     alt="Descrição da imagem">

&lt;!-- Com JavaScript -->
&lt;script>
document.addEventListener('DOMContentLoaded', function() {
    const lazyImages = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                observer.unobserve(img);
            }
        });
    });
    
    lazyImages.forEach(img => imageObserver.observe(img));
});
&lt;/script></pre>
                </div>

                <div class="code-block">
                    <h4>Carregamento Progressivo de Componentes</h4>
                    <pre>
// Em um aplicativo Vue.js
const Dashboard = () => import(/* webpackChunkName: "dashboard" */ './components/Dashboard.vue');
const Reports = () => import(/* webpackChunkName: "reports" */ './components/Reports.vue');

// Rotas com carregamento sob demanda
const routes = [
    { path: '/dashboard', component: Dashboard },
    { path: '/reports', component: Reports }
];</pre>
                </div>

                <div class="best-practice">
                    <h4>Estratégias de Carregamento Progressivo</h4>
                    <ul>
                        <li>Priorize o carregamento de conteúdo acima da dobra</li>
                        <li>Use placeholders ou skeletons enquanto o conteúdo carrega</li>
                        <li>Implemente carregamento sob demanda para rotas e componentes</li>
                        <li>Considere técnicas de streaming SSR para aplicações complexas</li>
                        <li>Use prefetching para recursos que provavelmente serão necessários em breve</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="js-optimization" class="subsection">
            <h3>4.3. Otimização de JavaScript</h3>

            <div class="content-block">
                <p>O JavaScript pode ser um dos principais gargalos de performance no frontend:</p>

                <div class="best-practice">
                    <h4>Boas Práticas para JavaScript</h4>
                    <ul>
                        <li>Evite bloqueio do thread principal com operações pesadas</li>
                        <li>Use Web Workers para processamento intensivo</li>
                        <li>Implemente code splitting para carregar apenas o necessário</li>
                        <li>Otimize event listeners e manipulação do DOM</li>
                        <li>Minimize o uso de bibliotecas externas</li>
                        <li>Utilize técnicas de memoização para operações repetitivas</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Web Worker para Processamento Pesado</h4>
                    <pre>
// main.js
const worker = new Worker('worker.js');

// Enviar dados para o worker
worker.postMessage({
    action: 'processData',
    data: largeDataset
});

// Receber resultados do worker
worker.onmessage = function(e) {
    const result = e.data;
    updateUI(result);
};

// worker.js
self.onmessage = function(e) {
    const { action, data } = e.data;
    
    if (action === 'processData') {
        // Processamento pesado que não bloqueia a UI
        const result = processLargeDataset(data);
        
        // Enviar resultado de volta para o thread principal
        self.postMessage(result);
    }
};

function processLargeDataset(data) {
    // Implementação do processamento intensivo
    // ...
    return processedData;
}
                        </pre>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Memoização para Operações Caras</h4>
                    <pre>
// Função de memoização
function memoize(fn) {
    const cache = new Map();
    return function(...args) {
        const key = JSON.stringify(args);
        if (cache.has(key)) {
            return cache.get(key);
        }
        const result = fn.apply(this, args);
        cache.set(key, result);
        return result;
    };
}

// Uso
const calculateExpensiveValue = memoize((a, b) => {
    console.log('Calculando...');
    // Simulação de operação cara
    let result = 0;
    for (let i = 0; i < 1000000; i++) {
        result += a * b;
    }
    return result;
});

// Primeira chamada (lenta)
console.time('First call');
calculateExpensiveValue(42, 99);
console.timeEnd('First call');

// Segunda chamada com mesmos parâmetros (rápida, usa cache)
console.time('Second call');
calculateExpensiveValue(42, 99);
console.timeEnd('Second call');
                        </pre>
                </div>
            </div>
        </section>

        <section id="css-optimization" class="subsection">
            <h3>4.4. Otimização de CSS</h3>

            <div class="content-block">
                <p>CSS mal otimizado pode causar renderização lenta e problemas de performance:</p>

                <div class="best-practice">
                    <h4>Boas Práticas para CSS</h4>
                    <ul>
                        <li>Minimize o uso de seletores complexos</li>
                        <li>Evite animações que causam reflow</li>
                        <li>Use CSS crítico inline para conteúdo acima da dobra</li>
                        <li>Prefira flexbox e grid a layouts antigos</li>
                        <li>Utilize will-change para otimizar animações</li>
                        <li>Considere abordagens como CSS-in-JS ou Utility-first CSS</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: CSS Crítico Inline</h4>
                    <pre>
&lt;!-- Em um template Blade --&gt;
&lt;head&gt;
    &lt;style&gt;
        /* CSS crítico para renderização inicial */
        body { margin: 0; font-family: sans-serif; }
        header { background: #fff; box-shadow: 0 2px 4px rgba(0,0,0,.1); }
        .hero { padding: 2rem; background: #f5f5f5; }
        /* ... */
    &lt;/style&gt;
    
    &lt;!-- CSS não crítico carregado de forma assíncrona --&gt;
    &lt;link rel="preload" href="/css/app.css" as="style" onload="this.onload=null;this.rel='stylesheet'"&gt;
    &lt;noscript&gt;&lt;link rel="stylesheet" href="/css/app.css"&gt;&lt;/noscript&gt;
&lt;/head&gt;
                        </pre>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Otimização de Animações</h4>
                    <pre>
/* Ruim: Causa reflow */
.element {
    animation: move 1s ease infinite;
}
@keyframes move {
    0% { top: 0; left: 0; }
    100% { top: 100px; left: 100px; }
}

/* Bom: Usa transform (apenas compositing) */
.element {
    will-change: transform;
    animation: move 1s ease infinite;
}
@keyframes move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(100px, 100px); }
}
                        </pre>
                </div>

                <div class="tip">
                    <h4>Dica: Análise de CSS Não Utilizado</h4>
                    <p>Use ferramentas como PurgeCSS para remover CSS não utilizado:</p>
                    <pre>
// Instalação
npm install laravel-mix-purgecss --save-dev

// webpack.mix.js
const mix = require('laravel-mix');
require('laravel-mix-purgecss');

mix.js('resources/js/app.js', 'public/js')
   .postCss('resources/css/app.css', 'public/css', [
       require('tailwindcss'),
   ])
   .purgeCss({
       enabled: mix.inProduction(),
       safelist: {
           standard: ['html', 'body', /^modal-/],
           deep: [/^tooltip-/],
       }
   });
                        </pre>
                </div>
            </div>
        </section>
    </section>
    <section id="infraestrutura" class="manual-section">
        <h2>5. Otimização de Infraestrutura</h2>

        <p>A configuração adequada da infraestrutura é fundamental para maximizar a performance da API.</p>

        <section id="servidor-web" class="subsection">
            <h3>5.1. Configuração de Servidor Web</h3>

            <div class="content-block">
                <p>O servidor web é a primeira camada que recebe as requisições e pode ser otimizado para
                    melhorar significativamente a performance:</p>

                <div class="comparison-table">
                    <h4>Comparação de Servidores Web</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Servidor</th>
                                <th>Vantagens</th>
                                <th>Desvantagens</th>
                                <th>Recomendado para</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Nginx</td>
                                <td>Leve, eficiente com conexões concorrentes, excelente para servir conteúdo
                                    estático</td>
                                <td>Configuração mais complexa</td>
                                <td>Produção, alta carga</td>
                            </tr>
                            <tr>
                                <td>Apache</td>
                                <td>Familiar, bem documentado, suporte a .htaccess</td>
                                <td>Maior consumo de memória, menos eficiente com muitas conexões</td>
                                <td>Desenvolvimento, compatibilidade</td>
                            </tr>
                            <tr>
                                <td>Caddy</td>
                                <td>Configuração simples, HTTPS automático</td>
                                <td>Menos maduro que Nginx/Apache</td>
                                <td>Projetos pequenos a médios</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="code-block">
                    <h4>Configuração Otimizada do Nginx para Laravel 12</h4>
                    <pre>
server {
    listen 80;
    server_name example.com;
    root /var/www/html/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";

    index index.php;

    charset utf-8;

    # Compressão gzip
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy;

    # Cache de assets estáticos
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    # Configuração do PHP-FPM
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_read_timeout 600;
    }

    # Negar acesso a arquivos sensíveis
    location ~ /\.(?!well-known).* {
        deny all;
    }
}
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Boas Práticas para Servidores Web</h4>
                    <ul>
                        <li>Habilite compressão gzip/brotli para reduzir o tamanho das respostas</li>
                        <li>Configure cache adequado para assets estáticos</li>
                        <li>Otimize buffers e timeouts para sua carga específica</li>
                        <li>Implemente HTTP/2 ou HTTP/3 para melhor multiplexação</li>
                        <li>Configure SSL/TLS corretamente com ciphers modernos</li>
                        <li>Considere usar um CDN para conteúdo estático</li>
                        <li>Monitore e ajuste os limites de conexões simultâneas</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="php-fpm" class="subsection">
            <h3>5.2. Otimização de PHP-FPM</h3>

            <div class="content-block">
                <p>O PHP-FPM (FastCGI Process Manager) é responsável por executar o código PHP e pode ser
                    otimizado para melhorar significativamente a performance:</p>

                <div class="code-block">
                    <h4>Configuração Otimizada do PHP-FPM para Laravel 12</h4>
                    <pre>
; /etc/php/8.2/fpm/pool.d/www.conf

; Configuração básica do pool
[www]
user = www-data
group = www-data
listen = /run/php/php8.2-fpm.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

; Gerenciamento de processos
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 15
pm.max_requests = 500

; Configurações de timeout
request_terminate_timeout = 60s
request_slowlog_timeout = 5s
slowlog = /var/log/php-fpm/slowlog.log

; Configurações de memória
php_admin_value[memory_limit] = 256M
php_admin_value[upload_max_filesize] = 20M
php_admin_value[post_max_size] = 20M
php_admin_value[max_execution_time] = 60

; Configurações de log
php_admin_flag[log_errors] = on
php_admin_value[error_log] = /var/log/php-fpm/www-error.log

; Otimizações específicas para Laravel
php_value[session.save_handler] = redis
php_value[session.save_path] = "tcp://127.0.0.1:6379?database=2"
                            </pre>
                </div>

                <div class="tip">
                    <h4>Cálculo de pm.max_children</h4>
                    <p>Uma fórmula comum para calcular o número máximo de processos PHP-FPM:</p>
                    <pre>
pm.max_children = (Total RAM - RAM para outros serviços) / Tamanho médio de um processo PHP

# Exemplo:
# Servidor com 8GB RAM
# 2GB reservados para sistema, MySQL, etc.
# Cada processo PHP usa aproximadamente 50MB
pm.max_children = (8192MB - 2048MB) / 50MB = 123
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Boas Práticas para PHP-FPM</h4>
                    <ul>
                        <li>Use o modo <code>dynamic</code> ou <code>ondemand</code> para gerenciamento de
                            processos</li>
                        <li>Ajuste <code>pm.max_children</code> com base na memória disponível</li>
                        <li>Configure <code>pm.max_requests</code> para reciclar processos e evitar vazamentos
                            de memória</li>
                        <li>Monitore o uso de recursos e ajuste conforme necessário</li>
                        <li>Use sockets Unix em vez de TCP para comunicação local</li>
                        <li>Configure limites de memória adequados para sua aplicação</li>
                        <li>Habilite logs de processos lentos para identificar gargalos</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="opcache" class="subsection">
            <h3>5.3. Configuração de OPCache</h3>

            <div class="content-block">
                <p>O OPCache do PHP armazena scripts pré-compilados em memória, eliminando a necessidade de
                    carregar e analisar o código a cada requisição:</p>

                <div class="code-block">
                    <h4>Configuração Otimizada do OPCache para Laravel 12</h4>
                    <pre>
; /etc/php/8.2/fpm/conf.d/10-opcache.ini

[opcache]
; Habilitar o OPCache
opcache.enable=1
opcache.enable_cli=1

; Configurações de memória
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000

; Otimizações de performance
opcache.validate_timestamps=0
opcache.revalidate_freq=0
opcache.save_comments=1
opcache.fast_shutdown=1
opcache.enable_file_override=1

; JIT (PHP 8.0+)
opcache.jit=1255
opcache.jit_buffer_size=100M

; Preload (PHP 7.4+)
opcache.preload=/var/www/html/preload.php
opcache.preload_user=www-data
                            </pre>
                </div>

                <div class="code-block">
                    <h4>Exemplo de Arquivo de Preload para Laravel 12</h4>
                    <pre>
&lt;?php
// preload.php

// Carregar o autoloader do Composer
require_once __DIR__ . '/vendor/autoload.php';

// Preload classes do framework
$files = [
    // Classes do framework Laravel
    __DIR__ . '/vendor/laravel/framework/src/Illuminate/Collections/Collection.php',
    __DIR__ . '/vendor/laravel/framework/src/Illuminate/Container/Container.php',
    __DIR__ . '/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php',
    __DIR__ . '/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php',
    __DIR__ . '/vendor/laravel/framework/src/Illuminate/Http/Request.php',
    __DIR__ . '/vendor/laravel/framework/src/Illuminate/Http/Response.php',
    __DIR__ . '/vendor/laravel/framework/src/Illuminate/Routing/Router.php',
    
    // Classes principais da aplicação
    __DIR__ . '/app/Models/User.php',
    __DIR__ . '/app/Http/Controllers/Controller.php',
    __DIR__ . '/app/Providers/AppServiceProvider.php',
];

foreach ($files as $file) {
    if (file_exists($file)) {
        opcache_compile_file($file);
    }
}
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Boas Práticas para OPCache</h4>
                    <ul>
                        <li>Em produção, desative <code>validate_timestamps</code> e use um processo de deploy
                            que limpe o cache</li>
                        <li>Ajuste <code>memory_consumption</code> com base no tamanho da sua aplicação</li>
                        <li>Configure <code>max_accelerated_files</code> para cobrir todos os arquivos PHP do
                            projeto</li>
                        <li>Use o recurso de preload para carregar classes frequentemente utilizadas</li>
                        <li>Habilite o JIT (Just-In-Time) para PHP 8+ em cargas de trabalho adequadas</li>
                        <li>Monitore o uso do OPCache e ajuste conforme necessário</li>
                    </ul>
                </div>

                <div class="tip">
                    <h4>Script para Limpar OPCache após Deploy</h4>
                    <pre>
&lt;?php
// opcache-reset.php

// Verificar token de segurança
if ($_GET['token'] !== 'seu_token_secreto') {
    header('HTTP/1.1 403 Forbidden');
    exit('Acesso negado');
}

// Limpar o cache
if (opcache_reset()) {
    echo json_encode(['status' => 'success', 'message' => 'OPCache limpo com sucesso']);
} else {
    echo json_encode(['status' => 'error', 'message' => 'Falha ao limpar OPCache']);
}

// No script de deploy
// curl https://seu-site.com/opcache-reset.php?token=seu_token_secreto
                            </pre>
                </div>
            </div>
        </section>

        <section id="cdn" class="subsection">
            <h3>5.4. Implementação de CDN</h3>

            <div class="content-block">
                <p>Uma Rede de Distribuição de Conteúdo (CDN) pode melhorar significativamente o tempo de
                    carregamento distribuindo conteúdo estático em servidores geograficamente próximos aos
                    usuários:</p>

                <div class="best-practice">
                    <h4>Benefícios de usar CDN</h4>
                    <ul>
                        <li>Redução da latência para usuários geograficamente distantes</li>
                        <li>Diminuição da carga no servidor de origem</li>
                        <li>Proteção contra picos de tráfego e DDoS</li>
                        <li>Otimizações automáticas de imagens e compressão</li>
                        <li>Implementação simplificada de HTTPS</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Configuração de CDN no Laravel 12</h4>
                    <pre>
// config/filesystems.php
'disks' => [
    // ...
    's3' => [
        'driver' => 's3',
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION'),
        'bucket' => env('AWS_BUCKET'),
        'url' => env('AWS_URL'),
        'endpoint' => env('AWS_ENDPOINT'),
        'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
    ],
],

// config/app.php
'asset_url' => env('ASSET_URL', null),

// .env
ASSET_URL=https://cdn.example.com

// Uso em templates Blade
&lt;img src="{{ asset('images/logo.png') }}" alt="Logo">
                            </pre>
                </div>

                <div class="code-block">
                    <h4>Configuração de Cloudflare com Laravel</h4>
                    <pre>
// Middleware para confiar nos proxies do Cloudflare
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class TrustProxies extends \Illuminate\Http\Middleware\TrustProxies
{
    /**
     * IPs do Cloudflare
     * @see https://www.cloudflare.com/ips/
     */
    protected $proxies = [
        // IPv4
        '************/20',
        '************/22',
        '************/22',
        '**********/22',
        '************/18',
        '*************/18',
        '************/20',
        '************/20',
        '*************/22',
        '************/17',
        '***********/15',
        '**********/13',
        '**********/14',
        '**********/13',
        '**********/22',
        
        // IPv6
        '2400:cb00::/32',
        '2606:4700::/32',
        '2803:f800::/32',
        '2405:b500::/32',
        '2405:8100::/32',
        '2a06:98c0::/29',
        '2c0f:f248::/32',
    ];

    /**
     * Headers confiáveis
     */
    protected $headers = Request::HEADER_X_FORWARDED_ALL;
}
                            </pre>
                </div>

                <div class="comparison-table">
                    <h4>Comparação de Provedores de CDN</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Provedor</th>
                                <th>Vantagens</th>
                                <th>Desvantagens</th>
                                <th>Melhor para</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Cloudflare</td>
                                <td>Plano gratuito, fácil configuração, proteção DDoS</td>
                                <td>Menos controle sobre configurações avançadas no plano gratuito</td>
                                <td>Maioria dos sites, especialmente os que precisam de segurança</td>
                            </tr>
                            <tr>
                                <td>AWS CloudFront</td>
                                <td>Integração com AWS, personalização avançada</td>
                                <td>Mais complexo, preço baseado em uso</td>
                                <td>Aplicações já hospedadas na AWS</td>
                            </tr>
                            <tr>
                                <td>Bunny.net</td>
                                <td>Preços competitivos, foco em performance</td>
                                <td>Menos recursos de segurança que Cloudflare</td>
                                <td>Sites com foco em mídia e performance</td>
                            </tr>
                            <tr>
                                <td>Fastly</td>
                                <td>Configuração programática, edge computing</td>
                                <td>Mais caro, curva de aprendizado maior</td>
                                <td>Aplicações enterprise com necessidades específicas</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </section>

    <section id="monitoramento" class="manual-section">
        <h2>6. Monitoramento e Profiling</h2>

        <p>O monitoramento contínuo é essencial para identificar problemas de performance e garantir que as
            otimizações estão funcionando conforme esperado.</p>

        <section id="ferramentas-monitoramento" class="subsection">
            <h3>6.1. Ferramentas de Monitoramento</h3>

            <div class="content-block">
                <p>Existem diversas ferramentas para monitorar a performance de aplicações Laravel:</p>

                <div class="tools-list">
                    <ul>
                        <li>
                            <strong>Laravel Telescope</strong>
                            <p>Ferramenta oficial de debug e monitoramento do Laravel, ideal para ambiente de
                                desenvolvimento.</p>
                            <pre>
# Instalação
composer require laravel/telescope --dev

# Publicação dos assets
php artisan telescope:install
php artisan migrate

# Acesso: /telescope
                                    </pre>
                        </li>
                        <li>
                            <strong>Laravel Pulse (Laravel 10+)</strong>
                            <p>Ferramenta de monitoramento em tempo real para Laravel, focada em métricas de
                                produção.</p>
                            <pre>
# Instalação
composer require laravel/pulse

# Publicação dos assets
php artisan pulse:install
php artisan migrate

# Acesso: /pulse
                                    </pre>
                        </li>
                        <li>
                            <strong>Laravel Debugbar</strong>
                            <p>Barra de debug que mostra consultas SQL, uso de memória, tempo de requisição e
                                mais.</p>
                            <pre>
# Instalação
composer require barryvdh/laravel-debugbar --dev

# Configuração automática via auto-discovery
                                    </pre>
                        </li>
                        <li>
                            <strong>New Relic</strong>
                            <p>Plataforma de monitoramento APM (Application Performance Monitoring) para
                                produção.</p>
                            <pre>
# Instalação do agente PHP
curl -L https://download.newrelic.com/php_agent/release/newrelic-php5-10.x.x.x-linux.tar.gz | tar -C /tmp -zx
cd /tmp/newrelic-php5-*
sudo ./newrelic-install install

# Configuração no php.ini
newrelic.license_key = "sua_chave_de_licença"
newrelic.appname = "Nome da Sua Aplicação"
                                    </pre>
                        </li>
                        <li>
                            <strong>Prometheus + Grafana</strong>
                            <p>Combinação open-source para coleta e visualização de métricas.</p>
                            <pre>
# Instalação do pacote para Laravel
composer require spatie/laravel-prometheus

# Publicação da configuração
php artisan vendor:publish --provider="Spatie\Prometheus\PrometheusServiceProvider"
                                    </pre>
                        </li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Métricas Personalizadas com Prometheus</h4>
                    <pre>
// Em um Service Provider
use Prometheus\CollectorRegistry;

public function boot()
{
    // Contador de requisições por rota
    $this->app['router']->matched(function ($route, $request) {
        $registry = app(CollectorRegistry::class);
        $counter = $registry->getOrRegisterCounter(
            'app',
            'http_requests_total',
            'Total de requisições HTTP',
            ['route', 'method']
        );
        
        $routeName = $route->getName() ?: $route->uri();
        $counter->inc([
            $routeName,
            $request->method()
        ]);
    });
    
    // Histograma de tempo de resposta
    $this->app->terminating(function ($request, $response) {
        $registry = app(CollectorRegistry::class);
        $histogram = $registry->getOrRegisterHistogram(
            'app',
            'http_request_duration_seconds',
            'Duração das requisições HTTP em segundos',
            ['route'],
            [0.01, 0.05, 0.1, 0.5, 1, 2, 5]
        );
        
        $route = $request->route();
        $routeName = $route ? ($route->getName() ?: $route->uri()) : 'unknown';
        
        $startTime = defined('LARAVEL_START') ? LARAVEL_START : $request->server('REQUEST_TIME_FLOAT');
        $duration = microtime(true) - $startTime;
        
        $histogram->observe($duration, [$routeName]);
    });
}
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Métricas Essenciais para Monitorar</h4>
                    <ul>
                        <li><strong>Tempo de Resposta</strong>: Média, percentis (p95, p99) e distribuição</li>
                        <li><strong>Taxa de Erros</strong>: Percentual de respostas com código 4xx e 5xx</li>
                        <li><strong>Throughput</strong>: Requisições por segundo</li>
                        <li><strong>Uso de Recursos</strong>: CPU, memória, disco, conexões de banco de dados
                        </li>
                        <li><strong>Tempo de Consultas</strong>: Duração média e máxima de consultas SQL</li>
                        <li><strong>Tamanho de Filas</strong>: Número de jobs pendentes e taxa de processamento
                        </li>
                        <li><strong>Cache Hit Ratio</strong>: Eficiência do sistema de cache</li>
                        <li><strong>Latência de Serviços Externos</strong>: Tempo de resposta de APIs de
                            terceiros</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="profiling" class="subsection">
            <h3>6.2. Profiling de Aplicação</h3>

            <div class="content-block">
                <p>O profiling permite identificar gargalos específicos no código, analisando o tempo de
                    execução de cada função ou método:</p>

                <div class="code-block">
                    <h4>Profiling com Blackfire.io</h4>
                    <pre>
# Instalação do agente Blackfire
curl -s https://packagecloud.io/gpg.key | sudo apt-key add -
echo "deb http://packages.blackfire.io/debian any main" | sudo tee /etc/apt/sources.list.d/blackfire.list
sudo apt-get update
sudo apt-get install blackfire-agent blackfire-php

# Configuração
sudo blackfire-agent --register --server-id=YOUR_SERVER_ID --server-token=YOUR_SERVER_TOKEN

# Uso em linha de comando
blackfire curl https://seu-site.com/api/endpoint

# Uso com o cliente PHP
composer require blackfire/php-sdk

// Em um controller ou middleware
$blackfire = new \Blackfire\Client();
$probe = $blackfire->createProbe();

// Código a ser perfilado
$result = $this->service->performOperation();

$blackfire->endProbe($probe);
                            </pre>
                </div>

                <div class="code-block">
                    <h4>Profiling Manual com Laravel</h4>
                    <pre>
// Em um controller ou middleware
$startTime = microtime(true);
$startMemory = memory_get_usage();

// Código a ser perfilado
$result = $this->service->performOperation();

$endTime = microtime(true);
$endMemory = memory_get_usage();

Log::debug('Performance', [
    'execution_time' => ($endTime - $startTime) * 1000 . 'ms',
    'memory_usage' => ($endMemory - $startMemory) / 1024 / 1024 . 'MB',
    'endpoint' => request()->path(),
]);
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Quando Realizar Profiling</h4>
                    <ul>
                        <li>Durante o desenvolvimento de novas funcionalidades</li>
                        <li>Após identificar endpoints lentos através de monitoramento</li>
                        <li>Antes e depois de otimizações para medir o impacto</li>
                        <li>Periodicamente em ambiente de produção (com impacto mínimo)</li>
                        <li>Após atualizações significativas de framework ou bibliotecas</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="logs-performance" class="subsection">
            <h3>6.3. Logs e Alertas de Performance</h3>

            <div class="content-block">
                <p>Configurar logs e alertas específicos para performance ajuda a identificar problemas
                    rapidamente:</p>

                <div class="code-block">
                    <h4>Middleware para Logging de Requisições Lentas</h4>
                    <pre>
namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;

class PerformanceMonitoring
{
    protected $threshold = 500; // milissegundos

    public function handle($request, Closure $next)
    {
        // Marcar o início da requisição
        $start = microtime(true);
        
        // Processar a requisição
        $response = $next($request);
        
        // Calcular o tempo de execução
        $duration = (microtime(true) - $start) * 1000;
        
        // Registrar requisições lentas
        if ($duration > $this->threshold) {
            Log::warning('Requisição lenta detectada', [
                'duration' => $duration . 'ms',
                'uri' => $request->path(),
                'method' => $request->method(),
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
            
            // Adicionar header de debug (apenas em ambiente não-produção)
            if (!app()->environment('production')) {
                $response->header('X-Request-Duration', $duration . 'ms');
            }
        }
        
        return $response;
    }
}

// Registrar no Kernel.php
protected $middleware = [
    // ...
    \App\Http\Middleware\PerformanceMonitoring::class,
];
                            </pre>
                </div>

                <div class="code-block">
                    <h4>Configuração de Alertas com Laravel Pulse</h4>
                    <pre>
// Em app/Providers/PulseServiceProvider.php
use Laravel\Pulse\Facades\Pulse;
use Laravel\Pulse\Recorders\SlowQueries;

public function register(): void
{
    // Configurar alertas para consultas lentas
    Pulse::slow(function (SlowQueries $recorder) {
        $recorder->threshold(500); // milissegundos
        
        // Enviar alerta para Slack quando houver consultas muito lentas
        $recorder->alert(
            threshold: 2000, // 2 segundos
            channels: ['slack'],
            frequency: 'hourly'
        );
    });
}
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Estratégia de Alertas</h4>
                    <ul>
                        <li><strong>Alertas de Urgência</strong>: Para problemas críticos que exigem ação
                            imediata (downtime, erros 500 em massa)</li>
                        <li><strong>Alertas de Degradação</strong>: Para problemas que afetam a performance mas
                            não causam falha total</li>
                        <li><strong>Relatórios Periódicos</strong>: Resumos diários ou semanais de métricas de
                            performance</li>
                        <li><strong>Alertas de Tendência</strong>: Notificações quando métricas mostram
                            degradação gradual ao longo do tempo</li>
                    </ul>
                </div>

                <div class="tip">
                    <h4>Integração com Serviços de Alerta</h4>
                    <p>Configure integrações com serviços como PagerDuty, OpsGenie ou Slack para notificações em
                        tempo real:</p>
                    <pre>
// Exemplo de integração com Slack usando Laravel Notifications
namespace App\Notifications;

use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;

class PerformanceAlertNotification extends Notification
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function via($notifiable)
    {
        return ['slack'];
    }

    public function toSlack($notifiable)
    {
        $message = (new SlackMessage)
            ->error()
            ->content('⚠️ Alerta de Performance')
            ->attachment(function ($attachment) {
                $attachment->title('Detalhes do Problema')
                    ->fields([
                        'Endpoint' => $this->data['endpoint'],
                        'Duração' => $this->data['duration'] . 'ms',
                        'Limite' => $this->data['threshold'] . 'ms',
                        'Ambiente' => app()->environment(),
                        'Timestamp' => now()->toDateTimeString(),
                    ]);
            });

        return $message;
    }
}

// Uso
Notification::route('slack', config('services.slack.webhook_url'))
    ->notify(new PerformanceAlertNotification([
        'endpoint' => '/api/users',
        'duration' => 3500,
        'threshold' => 1000,
    ]));
                            </pre>
                </div>
            </div>
        </section>
    </section>

    <section id="escala" class="manual-section">
        <h2>7. Escalabilidade</h2>

        <p>À medida que sua aplicação cresce, a escalabilidade torna-se um fator crítico para manter a
            performance.</p>

        <section id="horizontal-scaling" class="subsection">
            <h3>7.1. Escalabilidade Horizontal</h3>

            <div class="content-block">
                <p>A escalabilidade horizontal envolve adicionar mais instâncias da sua aplicação para
                    distribuir a carga:</p>

                <div class="diagram" align="center">
                    <svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Load Balancer -->
                        <rect x="250" y="50" width="100" height="60" rx="5" ry="5" fill="#3498db" stroke="#2980b9"
                            stroke-width="2" />
                        <text x="300" y="85" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Load
                            Balancer</text>

                        <!-- App Servers -->
                        <rect x="100" y="180" width="100" height="60" rx="5" ry="5" fill="#2ecc71" stroke="#27ae60"
                            stroke-width="2" />
                        <text x="150" y="215" font-family="Arial" font-size="14" text-anchor="middle" fill="white">App
                            Server 1</text>

                        <rect x="250" y="180" width="100" height="60" rx="5" ry="5" fill="#2ecc71" stroke="#27ae60"
                            stroke-width="2" />
                        <text x="300" y="215" font-family="Arial" font-size="14" text-anchor="middle" fill="white">App
                            Server 2</text>

                        <rect x="400" y="180" width="100" height="60" rx="5" ry="5" fill="#2ecc71" stroke="#27ae60"
                            stroke-width="2" />
                        <text x="450" y="215" font-family="Arial" font-size="14" text-anchor="middle" fill="white">App
                            Server 3</text>

                        <!-- Shared Resources -->
                        <rect x="175" y="310" width="100" height="60" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad"
                            stroke-width="2" />
                        <text x="225" y="345" font-family="Arial" font-size="14" text-anchor="middle"
                            fill="white">Database</text>

                        <rect x="325" y="310" width="100" height="60" rx="5" ry="5" fill="#e74c3c" stroke="#c0392b"
                            stroke-width="2" />
                        <text x="375" y="345" font-family="Arial" font-size="14" text-anchor="middle"
                            fill="white">Redis/Cache</text>

                        <!-- Connections -->
                        <line x1="300" y1="110" x2="150" y2="180" stroke="#2c3e50" stroke-width="2" />
                        <line x1="300" y1="110" x2="300" y2="180" stroke="#2c3e50" stroke-width="2" />
                        <line x1="300" y1="110" x2="450" y2="180" stroke="#2c3e50" stroke-width="2" />

                        <line x1="150" y1="240" x2="225" y2="310" stroke="#2c3e50" stroke-width="2" />
                        <line x1="300" y1="240" x2="225" y2="310" stroke="#2c3e50" stroke-width="2" />
                        <line x1="450" y1="240" x2="225" y2="310" stroke="#2c3e50" stroke-width="2" />

                        <line x1="150" y1="240" x2="375" y2="310" stroke="#2c3e50" stroke-width="2" />
                        <line x1="300" y1="240" x2="375" y2="310" stroke="#2c3e50" stroke-width="2" />
                        <line x1="450" y1="240" x2="375" y2="310" stroke="#2c3e50" stroke-width="2" />
                    </svg>
                </div>

                <div class="best-practice">
                    <h4>Requisitos para Escalabilidade Horizontal</h4>
                    <ul>
                        <li><strong>Aplicação Stateless</strong>: Não armazenar estado na instância da aplicação
                        </li>
                        <li><strong>Sessões Compartilhadas</strong>: Usar Redis ou outro mecanismo para
                            compartilhar sessões</li>
                        <li><strong>Cache Distribuído</strong>: Implementar cache compartilhado entre instâncias
                        </li>
                        <li><strong>Armazenamento Centralizado</strong>: Para uploads e arquivos gerados</li>
                        <li><strong>Balanceamento de Carga</strong>: Distribuir requisições entre instâncias
                        </li>
                        <li><strong>Filas Centralizadas</strong>: Sistema de filas compartilhado entre
                            instâncias</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Configuração para Aplicação Stateless</h4>
                    <pre>
// config/session.php
'driver' => env('SESSION_DRIVER', 'redis'),

// config/cache.php
'default' => env('CACHE_DRIVER', 'redis'),

// config/queue.php
'default' => env('QUEUE_CONNECTION', 'redis'),

// config/filesystems.php
'default' => env('FILESYSTEM_DISK', 's3'),
'disks' => [
    's3' => [
        'driver' => 's3',
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION'),
        'bucket' => env('AWS_BUCKET'),
    ],
],
                            </pre>
                </div>

                <div class="tip">
                    <h4>Dica: Containers e Orquestração</h4>
                    <p>Use Docker e Kubernetes para facilitar a escalabilidade horizontal:</p>
                    <pre>
# Exemplo de Dockerfile para Laravel 12
FROM php:8.2-fpm

# Instalar dependências
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# Instalar extensões PHP
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Instalar Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configurar diretório de trabalho
WORKDIR /var/www/html

# Copiar arquivos do projeto
COPY . .

# Instalar dependências
RUN composer install --optimize-autoloader --no-dev

# Otimizar para produção
RUN php artisan config:cache && \
    php artisan route:cache && \
    php artisan view:cache

# Configurar permissões
RUN chown -R www-data:www-data /var/www/html

# Expor porta
EXPOSE 9000

CMD ["php-fpm"]
                            </pre>
                </div>
            </div>
        </section>

        <section id="vertical-scaling" class="subsection">
            <h3>7.2. Escalabilidade Vertical</h3>

            <div class="content-block">
                <p>A escalabilidade vertical envolve aumentar os recursos (CPU, memória) de uma única instância:
                </p>

                <div class="best-practice">
                    <h4>Quando Escalar Verticalmente</h4>
                    <ul>
                        <li>Quando a aplicação não pode ser facilmente distribuída</li>
                        <li>Para componentes que exigem alta performance em uma única instância (banco de dados)
                        </li>
                        <li>Como solução temporária antes de implementar escalabilidade horizontal</li>
                        <li>Para operações que exigem grande quantidade de memória ou CPU</li>
                    </ul>
                </div>

                <div class="comparison-table">
                    <h4>Comparação: Escalabilidade Horizontal vs. Vertical</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Aspecto</th>
                                <th>Escalabilidade Horizontal</th>
                                <th>Escalabilidade Vertical</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Custo</td>
                                <td>Geralmente mais econômico a longo prazo</td>
                                <td>Pode ser mais caro devido a hardware especializado</td>
                            </tr>
                            <tr>
                                <td>Complexidade</td>
                                <td>Maior complexidade de configuração e manutenção</td>
                                <td>Configuração mais simples</td>
                            </tr>
                            <tr>
                                <td>Limite</td>
                                <td>Praticamente ilimitado</td>
                                <td>Limitado pelo hardware disponível</td>
                            </tr>
                            <tr>
                                <td>Disponibilidade</td>
                                <td>Alta disponibilidade e tolerância a falhas</td>
                                <td>Ponto único de falha</td>
                            </tr>
                            <tr>
                                <td>Adequado para</td>
                                <td>Aplicações web, APIs, microsserviços</td>
                                <td>Bancos de dados, sistemas de cache, tarefas intensivas</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="tip">
                    <h4>Dica: Otimização para Instâncias Maiores</h4>
                    <p>Ao escalar verticalmente, ajuste as configurações para aproveitar os recursos adicionais:
                    </p>
                    <pre>
# Ajustes para PHP-FPM em servidor com mais recursos
pm = dynamic
pm.max_children = 150       # Mais processos para servidores com mais RAM
pm.start_servers = 20
pm.min_spare_servers = 10
pm.max_spare_servers = 30

# Ajustes para OPCache
opcache.memory_consumption=512      # Mais memória para cache
opcache.interned_strings_buffer=32
opcache.max_accelerated_files=20000
opcache.jit_buffer_size=256M        # Buffer maior para JIT

# Ajustes para MySQL em servidor com mais recursos
innodb_buffer_pool_size = 4G        # 50-70% da RAM disponível
innodb_log_file_size = 512M
innodb_flush_log_at_trx_commit = 2  # Melhor performance, ligeiramente menos durabilidade
max_connections = 500               # Mais conexões simultâneas
                            </pre>
                </div>
            </div>
        </section>

        <section id="microservices" class="subsection">
            <h3>7.3. Arquitetura de Microsserviços</h3>

            <div class="content-block">
                <p>A arquitetura de microsserviços divide a aplicação em serviços menores e independentes, cada
                    um responsável por uma funcionalidade específica:</p>

                <div class="diagram" align="center">
                    <svg width="700" height="400" xmlns="http://www.w3.org/2000/svg">
                        <!-- API Gateway -->
                        <rect x="300" y="50" width="100" height="60" rx="5" ry="5" fill="#3498db" stroke="#2980b9"
                            stroke-width="2" />
                        <text x="350" y="85" font-family="Arial" font-size="14" text-anchor="middle" fill="white">API
                            Gateway</text>

                        <!-- Microservices -->
                        <rect x="100" y="180" width="100" height="60" rx="5" ry="5" fill="#2ecc71" stroke="#27ae60"
                            stroke-width="2" />
                        <text x="150" y="215" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Auth
                            Service</text>

                        <rect x="250" y="180" width="100" height="60" rx="5" ry="5" fill="#2ecc71" stroke="#27ae60"
                            stroke-width="2" />
                        <text x="300" y="215" font-family="Arial" font-size="14" text-anchor="middle" fill="white">User
                            Service</text>

                        <rect x="400" y="180" width="100" height="60" rx="5" ry="5" fill="#2ecc71" stroke="#27ae60"
                            stroke-width="2" />
                        <text x="450" y="215" font-family="Arial" font-size="14" text-anchor="middle"
                            fill="white">Product Service</text>

                        <rect x="550" y="180" width="100" height="60" rx="5" ry="5" fill="#2ecc71" stroke="#27ae60"
                            stroke-width="2" />
                        <text x="600" y="215" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Order
                            Service</text>

                        <!-- Databases -->
                        <rect x="100" y="310" width="100" height="60" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad"
                            stroke-width="2" />
                        <text x="150" y="345" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Auth
                            DB</text>

                        <rect x="250" y="310" width="100" height="60" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad"
                            stroke-width="2" />
                        <text x="300" y="345" font-family="Arial" font-size="14" text-anchor="middle" fill="white">User
                            DB</text>

                        <rect x="400" y="310" width="100" height="60" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad"
                            stroke-width="2" />
                        <text x="450" y="345" font-family="Arial" font-size="14" text-anchor="middle"
                            fill="white">Product DB</text>

                        <rect x="550" y="310" width="100" height="60" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad"
                            stroke-width="2" />
                        <text x="600" y="345" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Order
                            DB</text>

                        <!-- Connections -->
                        <line x1="350" y1="110" x2="150" y2="180" stroke="#2c3e50" stroke-width="2" />
                        <line x1="350" y1="110" x2="300" y2="180" stroke="#2c3e50" stroke-width="2" />
                        <line x1="350" y1="110" x2="450" y2="180" stroke="#2c3e50" stroke-width="2" />
                        <line x1="350" y1="110" x2="600" y2="180" stroke="#2c3e50" stroke-width="2" />

                        <line x1="150" y1="240" x2="150" y2="310" stroke="#2c3e50" stroke-width="2" />
                        <line x1="300" y1="240" x2="300" y2="310" stroke="#2c3e50" stroke-width="2" />
                        <line x1="450" y1="240" x2="450" y2="310" stroke="#2c3e50" stroke-width="2" />
                        <line x1="600" y1="240" x2="600" y2="310" stroke="#2c3e50" stroke-width="2" />

                        <!-- Service-to-Service Communication -->
                        <line x1="200" y1="210" x2="250" y2="210" stroke="#e74c3c" stroke-width="2"
                            stroke-dasharray="5,5" />
                        <line x1="350" y1="210" x2="400" y2="210" stroke="#e74c3c" stroke-width="2"
                            stroke-dasharray="5,5" />
                        <line x1="500" y1="210" x2="550" y2="210" stroke="#e74c3c" stroke-width="2"
                            stroke-dasharray="5,5" />
                    </svg>
                </div>

                <div class="best-practice">
                    <h4>Benefícios dos Microsserviços</h4>
                    <ul>
                        <li>Escalabilidade independente de componentes</li>
                        <li>Isolamento de falhas</li>
                        <li>Desenvolvimento e deploy independentes</li>
                        <li>Possibilidade de usar tecnologias diferentes para cada serviço</li>
                        <li>Equipes menores e mais focadas</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Comunicação entre Microsserviços com Laravel</h4>
                    <pre>
// Em OrderService: Comunicação com ProductService
namespace App\Services;

use Illuminate\Support\Facades\Http;

class ProductServiceClient
{
    protected $baseUrl;
    protected $apiKey;

    public function __construct()
    {
        $this->baseUrl = config('services.product_service.url');
        $this->apiKey = config('services.product_service.key');
    }

    public function getProduct($productId)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Accept' => 'application/json',
            ])->get("{$this->baseUrl}/api/products/{$productId}");
            
            if ($response->successful()) {
                return $response->json();
            }
            
            throw new \Exception("Failed to get product: " . $response->body());
        } catch (\Exception $e) {
            // Implementar circuit breaker, retry, fallback
            report($e);
            return null;
        }
    }

    public function checkInventory($productId, $quantity)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Accept' => 'application/json',
            ])->post("{$this->baseUrl}/api/inventory/check", [
                'product_id' => $productId,
                'quantity' => $quantity
            ]);
            
            return $response->successful() && $response->json('available');
        } catch (\Exception $e) {
            report($e);
            return false;
        }
    }
}
                            </pre>
                </div>

                <div class="tip">
                    <h4>Padrões para Microsserviços</h4>
                    <p>Implemente estes padrões para tornar sua arquitetura de microsserviços mais resiliente:
                    </p>
                    <ul>
                        <li><strong>Circuit Breaker</strong>: Evita chamadas repetidas a serviços com falha</li>
                        <li><strong>Service Discovery</strong>: Localização dinâmica de serviços</li>
                        <li><strong>API Gateway</strong>: Ponto único de entrada para clientes</li>
                        <li><strong>Event-Driven Architecture</strong>: Comunicação assíncrona via eventos</li>
                        <li><strong>CQRS</strong>: Separação de operações de leitura e escrita</li>
                        <li><strong>Saga Pattern</strong>: Gerenciamento de transações distribuídas</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Circuit Breaker com Laravel</h4>
                    <pre>
composer require guzzlehttp/guzzle

// CircuitBreaker.php
namespace App\Services;

use Illuminate\Support\Facades\Cache;

class CircuitBreaker
{
    protected $service;
    protected $failureThreshold = 5;
    protected $resetTimeout = 30; // segundos

    public function __construct(string $service)
    {
        $this->service = $service;
    }

    public function isAvailable(): bool
    {
        $state = Cache::get("circuit_breaker.{$this->service}.state", 'closed');
        
        if ($state === 'open') {
            $lastFailure = Cache::get("circuit_breaker.{$this->service}.last_failure");
            
            // Verificar se o tempo de reset passou
            if (time() - $lastFailure > $this->resetTimeout) {
                Cache::put("circuit_breaker.{$this->service}.state", 'half-open');
                return true;
            }
            
            return false;
        }
        
        return true;
    }

    public function recordSuccess(): void
    {
        Cache::put("circuit_breaker.{$this->service}.failures", 0);
        
        if (Cache::get("circuit_breaker.{$this->service}.state") === 'half-open') {
            Cache::put("circuit_breaker.{$this->service}.state", 'closed');
        }
    }

    public function recordFailure(): void
    {
        $failures = Cache::increment("circuit_breaker.{$this->service}.failures", 1, 0);
        Cache::put("circuit_breaker.{$this->service}.last_failure", time());
        
        if ($failures >= $this->failureThreshold) {
            Cache::put("circuit_breaker.{$this->service}.state", 'open');
        }
    }

    public function execute(callable $action, callable $fallback = null)
    {
        if (!$this->isAvailable()) {
            return $fallback ? $fallback() : null;
        }
        
        try {
            $result = $action();
            $this->recordSuccess();
            return $result;
        } catch (\Exception $e) {
            $this->recordFailure();
            
            if ($fallback) {
                return $fallback($e);
            }
            
            throw $e;
        }
    }
}

// Uso do Circuit Breaker
$circuitBreaker = new CircuitBreaker('product-service');

$product = $circuitBreaker->execute(
    function() use ($productId, $productService) {
        return $productService->getProduct($productId);
    },
    function() use ($productId) {
        // Fallback: retornar dados em cache ou valores padrão
        return Cache::get("product.{$productId}", [
            'id' => $productId,
            'name' => 'Produto Temporariamente Indisponível',
            'price' => 0,
            'available' => false
        ]);
    }
);
                            </pre>
                </div>
            </div>
        </section>
    </section>

    <section id="performance-testing" class="manual-section">
        <h2>8. Testes de Performance</h2>

        <p>Testes de performance são essenciais para identificar gargalos e garantir que a aplicação atenda aos
            requisitos de desempenho.</p>

        <section id="load-testing" class="subsection">
            <h3>8.1. Testes de Carga</h3>

            <div class="content-block">
                <p>Testes de carga simulam o uso normal da aplicação com um número esperado de usuários
                    concorrentes:</p>

                <div class="best-practice">
                    <h4>Ferramentas de Teste de Carga</h4>
                    <ul>
                        <li><strong>k6</strong>: Ferramenta moderna de teste de carga baseada em JavaScript</li>
                        <li><strong>JMeter</strong>: Ferramenta tradicional e completa para testes de carga</li>
                        <li><strong>Artillery</strong>: Ferramenta de teste de carga baseada em Node.js</li>
                        <li><strong>Locust</strong>: Ferramenta de teste de carga baseada em Python</li>
                        <li><strong>Gatling</strong>: Ferramenta de teste de carga baseada em Scala</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Teste de Carga com k6</h4>
                    <pre>
// load-test.js
import http from 'k6/http';
import { sleep, check } from 'k6';
import { Counter } from 'k6/metrics';

// Métricas personalizadas
const errors = new Counter('errors');

// Configuração do teste
export const options = {
    stages: [
        { duration: '1m', target: 50 },   // Rampa até 50 usuários em 1 minuto
        { duration: '3m', target: 50 },   // Manter 50 usuários por 3 minutos
        { duration: '1m', target: 100 },  // Rampa até 100 usuários em 1 minuto
        { duration: '5m', target: 100 },  // Manter 100 usuários por 5 minutos
        { duration: '1m', target: 0 },    // Rampa para baixo até 0 usuários
    ],
    thresholds: {
        http_req_duration: ['p(95)<500'], // 95% das requisições devem completar em menos de 500ms
        errors: ['count<10'],             // Menos de 10 erros no total
    },
};

// Função principal executada para cada usuário virtual
export default function() {
    // Autenticação
    const loginRes = http.post('https://api.example.com/login', {
        username: 'testuser',
        password: 'password123'
    }, {
        headers: { 'Content-Type': 'application/json' },
    });

    check(loginRes, {
        'login status is 200': (r) => r.status === 200,
        'has access token': (r) => r.json('access_token') !== undefined,
    }) || errors.add(1);

    const token = loginRes.json('access_token');

    // Requisição para listar recursos
    const listRes = http.get('https://api.example.com/api/resources', {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
    });

    check(listRes, {
        'list status is 200': (r) => r.status === 200,
        'has items': (r) => r.json('data').length > 0,
    }) || errors.add(1);

    // Requisição para obter um recurso específico
    const resourceId = listRes.json('data.0.id');
    const getRes = http.get(`https://api.example.com/api/resources/${resourceId}`, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
    });

    check(getRes, {
        'get status is 200': (r) => r.status === 200,
        'has correct id': (r) => r.json('data.id') === resourceId,
    }) || errors.add(1);

    // Pausa entre iterações
    sleep(3);
}
                            </pre>
                </div>

                <div class="tip">
                    <h4>Executando o Teste com k6</h4>
                    <pre>
# Instalar k6
# Linux: https://k6.io/docs/getting-started/installation/
# macOS: brew install k6
# Windows: choco install k6

# Executar o teste
k6 run load-test.js

# Executar com saída para InfluxDB + Grafana
k6 run --out influxdb=http://localhost:8086/k6 load-test.js
                            </pre>
                </div>
            </div>
        </section>

        <section id="stress-testing" class="subsection">
            <h3>8.2. Testes de Estresse</h3>

            <div class="content-block">
                <p>Testes de estresse determinam os limites da aplicação, aumentando gradualmente a carga até
                    que a performance se degrade:</p>

                <div class="code-block">
                    <h4>Exemplo: Teste de Estresse com k6</h4>
                    <pre>
// stress-test.js
import http from 'k6/http';
import { sleep } from 'k6';

export const options = {
    stages: [
        { duration: '2m', target: 100 },   // Rampa até 100 usuários
        { duration: '5m', target: 100 },   // Manter 100 usuários
        { duration: '2m', target: 200 },   // Rampa até 200 usuários
        { duration: '5m', target: 200 },   // Manter 200 usuários
        { duration: '2m', target: 300 },   // Rampa até 300 usuários
        { duration: '5m', target: 300 },   // Manter 300 usuários
        { duration: '2m', target: 400 },   // Rampa até 400 usuários
        { duration: '5m', target: 400 },   // Manter 400 usuários
        { duration: '10m', target: 0 },    // Rampa para baixo até 0 usuários
    ],
};

export default function() {
    http.get('https://api.example.com/api/resources');
    sleep(1);
}
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Métricas a Monitorar Durante Testes de Estresse</h4>
                    <ul>
                        <li><strong>Tempo de Resposta</strong>: Quando começa a aumentar significativamente</li>
                        <li><strong>Taxa de Erros</strong>: Quando começam a ocorrer erros 5xx</li>
                        <li><strong>Uso de CPU</strong>: Quando atinge níveis críticos (>90%)</li>
                        <li><strong>Uso de Memória</strong>: Quando ocorrem vazamentos ou uso excessivo</li>
                        <li><strong>Conexões de Banco de Dados</strong>: Quando o pool de conexões se esgota
                        </li>
                        <li><strong>Throughput</strong>: Quando para de aumentar proporcionalmente aos usuários
                        </li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="benchmark" class="subsection">
            <h3>8.3. Benchmarking</h3>

            <div class="content-block">
                <p>Benchmarking compara a performance de diferentes implementações ou configurações:</p>

                <div class="code-block">
                    <h4>Exemplo: Benchmark de Implementações com PHP</h4>
                    <pre>
// benchmark.php
<?php

// Configurações
$iterations = 1000;
$data = range(1, 10000);

// Implementação 1: Foreach loop
function implementation1($data) {
    $result = 0;
    foreach ($data as $value) {
        $result += $value;
    }
    return $result;
}

// Implementação 2: Array reduce
function implementation2($data) {
    return array_reduce($data, function($carry, $item) {
        return $carry + $item;
    }, 0);
}

// Implementação 3: Array sum
function implementation3($data) {
    return array_sum($data);
}

// Função de benchmark
function benchmark($func, $data, $iterations) {
    $start = microtime(true);
    
    for ($i = 0; $i < $iterations; $i++) {
        $func($data);
    }
    
    $end = microtime(true);
    return ($end - $start) * 1000; // Tempo em ms
}

// Executar benchmarks
$time1 = benchmark('implementation1', $data, $iterations);
$time2 = benchmark('implementation2', $data, $iterations);
$time3 = benchmark('implementation3', $data, $iterations);

// Exibir resultados
echo "Benchmark Results (lower is better):\n";
echo "Implementation 1 (foreach): {$time1} ms\n";
echo "Implementation 2 (array_reduce): {$time2} ms\n";
echo "Implementation 3 (array_sum): {$time3} ms\n";

// Determinar o mais rápido
$fastest = min($time1, $time2, $time3);
echo "\nFastest implementation: ";

if ($fastest === $time1) echo "Implementation 1 (foreach)\n";
elseif ($fastest === $time2) echo "Implementation 2 (array_reduce)\n";
else echo "Implementation 3 (array_sum)\n";

echo "Performance gain over slowest: " . 
     round(max($time1, $time2, $time3) / $fastest * 100 - 100, 2) . "%\n";
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Boas Práticas para Benchmarking</h4>
                    <ul>
                        <li>Execute benchmarks em um ambiente controlado e consistente</li>
                        <li>Realize múltiplas execuções e calcule a média para resultados mais precisos</li>
                        <li>Isole o código a ser testado de fatores externos (I/O, rede, etc.)</li>
                        <li>Compare implementações com dados realistas</li>
                        <li>Considere não apenas o tempo de execução, mas também o uso de memória</li>
                        <li>Documente as condições e configurações do ambiente de teste</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Benchmark de Endpoints de API</h4>
                    <pre>
// api-benchmark.js (usando autocannon)
const autocannon = require('autocannon');
const { promisify } = require('util');

const run = promisify(autocannon);

async function benchmark() {
    console.log('Running benchmarks...');
    
    // Benchmark para endpoint 1
    const result1 = await run({
        url: 'http://localhost:8000/api/endpoint1',
        connections: 100,
        duration: 10,
        headers: {
            'Authorization': 'Bearer YOUR_TOKEN',
            'Content-Type': 'application/json'
        }
    });
    
    // Benchmark para endpoint 2
    const result2 = await run({
        url: 'http://localhost:8000/api/endpoint2',
        connections: 100,
        duration: 10,
        headers: {
            'Authorization': 'Bearer YOUR_TOKEN',
            'Content-Type': 'application/json'
        }
    });
    
    // Exibir resultados
    console.log('Endpoint 1 Results:');
    console.log(`Requests/sec: ${result1.requests.average}`);
    console.log(`Latency (avg): ${result1.latency.average} ms`);
    console.log(`Latency (p99): ${result1.latency.p99} ms`);
    
    console.log('\nEndpoint 2 Results:');
    console.log(`Requests/sec: ${result2.requests.average}`);
    console.log(`Latency (avg): ${result2.latency.average} ms`);
    console.log(`Latency (p99): ${result2.latency.p99} ms`);
    
    // Comparação
    const throughputDiff = ((result1.requests.average / result2.requests.average) * 100 - 100).toFixed(2);
    const latencyDiff = ((result2.latency.average / result1.latency.average) * 100 - 100).toFixed(2);
    
    console.log('\nComparison:');
    console.log(`Endpoint 1 is ${throughputDiff}% ${throughputDiff > 0 ? 'faster' : 'slower'} in throughput`);
    console.log(`Endpoint 1 is ${latencyDiff}% ${latencyDiff > 0 ? 'faster' : 'slower'} in latency`);
}

benchmark().catch(console.error);
                            </pre>
                </div>
            </div>
        </section>
    </section>

    <section id="laravel-12-performance" class="manual-section">
        <h2>9. Recursos de Performance do Laravel 12</h2>

        <p>O Laravel 12 introduz várias melhorias de performance que podem ser aproveitadas para otimizar sua
            aplicação.</p>

        <section id="octane" class="subsection">
            <h3>9.1. Laravel Octane</h3>

            <div class="content-block">
                <p>O Laravel Octane aumenta significativamente a performance da aplicação mantendo-a em memória
                    entre requisições:</p>

                <div class="code-block">
                    <h4>Instalação e Configuração do Octane</h4>
                    <pre>
# Instalação
composer require laravel/octane

# Publicação da configuração
php artisan octane:install

# Iniciar com Swoole (requer extensão swoole)
php artisan octane:start --server=swoole

# Iniciar com RoadRunner
php artisan octane:start --server=roadrunner
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Boas Práticas para Octane</h4>
                    <ul>
                        <li>Evite armazenar estado em propriedades estáticas ou variáveis globais</li>
                        <li>Use o método <code>withoutOverlapping()</code> para evitar race conditions em
                            tarefas agendadas</li>
                        <li>Reinicie o servidor Octane após alterações no código</li>
                        <li>Configure o número de workers com base nos recursos disponíveis</li>
                        <li>Use o middleware <code>PreventRequestsDuringMaintenance</code> para manutenção</li>
                        <li>Considere usar o Octane com Vapor para aplicações serverless</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Configuração Avançada do Octane</h4>
                    <pre>
// config/octane.php
return [
    'server' => env('OCTANE_SERVER', 'swoole'),

    'swoole' => [
        'options' => [
            'worker_num' => env('OCTANE_WORKERS', cpu_count()),
            'task_worker_num' => env('OCTANE_TASK_WORKERS', cpu_count()),
            'max_request' => env('OCTANE_MAX_REQUESTS', 500),
            'buffer_output_size' => 1024 * 1024 * 50,
        ],
    ],

    'roadrunner' => [
        'workers' => [
            'http' => [
                'command' => 'php artisan octane:roadrunner',
                'pool' => [
                    'max_jobs' => 0,
                    'num_workers' => env('OCTANE_WORKERS', cpu_count()),
                    'allocate_timeout' => 60,
                    'destroy_timeout' => 60,
                ],
            ],
        ],
    ],

    'listeners' => [
        WorkerStarting::class => [
            EnsureUploadedFilesAreValid::class,
            EnsureUploadedFilesCanBeMoved::class,
        ],
        
        RequestReceived::class => [
            DisableSessionDriver::class,
            EnsureRequestHasValidSignature::class,
            EnsureUploadedFilesAreValid::class,
        ],
        
        RequestHandled::class => [
            FlushTemporaryContainerInstances::class,
        ],
        
        WorkerStopping::class => [
            // Listeners que são executados quando um worker está parando...
        ],
    ],

    'warm' => [
        // Classes para pré-carregar em cada worker
        App\Models\User::class,
        App\Services\ImportantService::class,
    ],

    'garbage' => [
        'interval' => 50, // Coleta de lixo a cada 50 requisições
    ],

    'max_execution_time' => 30,
];
                            </pre>
                </div>

                <div class="comparison-table">
                    <h4>Comparação: Laravel Tradicional vs. Octane</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Aspecto</th>
                                <th>Laravel Tradicional (PHP-FPM)</th>
                                <th>Laravel Octane</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Modelo de Execução</td>
                                <td>Inicia e encerra a aplicação a cada requisição</td>
                                <td>Mantém a aplicação em memória entre requisições</td>
                            </tr>
                            <tr>
                                <td>Performance</td>
                                <td>Mais lento devido ao bootstrap repetido</td>
                                <td>Significativamente mais rápido (2-10x)</td>
                            </tr>
                            <tr>
                                <td>Uso de Memória</td>
                                <td>Menor por requisição, mas ineficiente no total</td>
                                <td>Maior por worker, mas mais eficiente no total</td>
                            </tr>
                            <tr>
                                <td>Concorrência</td>
                                <td>Limitada pelo número de processos PHP-FPM</td>
                                <td>Alta, especialmente com Swoole (assíncrono)</td>
                            </tr>
                            <tr>
                                <td>Complexidade</td>
                                <td>Mais simples de configurar e depurar</td>
                                <td>Requer atenção a estado compartilhado e race conditions</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <section id="parallel-testing" class="subsection">
            <h3>9.2. Testes Paralelos</h3>

            <div class="content-block">
                <p>O Laravel 12 aprimora o suporte para testes paralelos, reduzindo significativamente o tempo
                    de execução da suíte de testes:</p>

                <div class="code-block">
                    <h4>Configuração de Testes Paralelos</h4>
                    <pre>
// phpunit.xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">./app</directory>
        </include>
    </coverage>
    <php>
        <server name="APP_ENV" value="testing"/>
        <server name="BCRYPT_ROUNDS" value="4"/>
        <server name="CACHE_DRIVER" value="array"/>
        <server name="DB_CONNECTION" value="sqlite"/>
        <server name="DB_DATABASE" value=":memory:"/>
        <server name="MAIL_MAILER" value="array"/>
        <server name="QUEUE_CONNECTION" value="sync"/>
        <server name="SESSION_DRIVER" value="array"/>
        <server name="TELESCOPE_ENABLED" value="false"/>
    </php>
    <!-- Configuração para testes paralelos -->
    <parallel>
        <processes>4</processes>
        <testsuite>Unit</testsuite>
        <testsuite>Feature</testsuite>
    </parallel>
</phpunit>
                            </pre>
                </div>

                <div class="code-block">
                    <h4>Executando Testes em Paralelo</h4>
                    <pre>
# Executar testes em paralelo
php artisan test --parallel

# Especificar número de processos
php artisan test --parallel --processes=8

# Executar apenas testes específicos em paralelo
php artisan test --parallel tests/Feature/UserTest.php tests/Feature/OrderTest.php
                            </pre>
                </div>

                <div class="best-practice">
                    <h4>Boas Práticas para Testes Paralelos</h4>
                    <ul>
                        <li>Garanta que os testes sejam independentes e não compartilhem estado</li>
                        <li>Use bancos de dados em memória (SQLite :memory:) para testes mais rápidos</li>
                        <li>Evite dependências de ordem de execução entre testes</li>
                        <li>Considere usar traits como <code>RefreshDatabase</code> em vez de
                            <code>DatabaseTransactions</code>
                        </li>
                        <li>Ajuste o número de processos com base nos recursos disponíveis</li>
                        <li>Agrupe testes relacionados em suítes para melhor organização</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="performance-improvements" class="subsection">
            <h3>9.3. Melhorias de Performance no Laravel 12</h3>

            <div class="content-block">
                <p>O Laravel 12 traz várias melhorias de performance que podem ser aproveitadas:</p>

                <div class="best-practice">
                    <h4>Principais Melhorias de Performance no Laravel 12</h4>
                    <ul>
                        <li><strong>Lazy Collections Aprimoradas</strong>: Processamento mais eficiente de
                            grandes conjuntos de dados</li>
                        <li><strong>Otimizações no Eloquent</strong>: Consultas mais eficientes e menor overhead
                        </li>
                        <li><strong>Melhorias no Sistema de Cache</strong>: Novos drivers e estratégias de
                            invalidação</li>
                        <li><strong>Otimizações no Container</strong>: Resolução de dependências mais rápida
                        </li>
                        <li><strong>Melhorias no Sistema de Filas</strong>: Processamento mais eficiente de jobs
                        </li>
                        <li><strong>Suporte Aprimorado para PHP 8.2</strong>: Aproveitamento de recursos de
                            performance do PHP 8.2</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Lazy Collections para Processamento Eficiente</h4>
                    <pre>
// Processamento de grandes conjuntos de dados com Lazy Collections
use Illuminate\Support\LazyCollection;

// Processar um arquivo grande linha por linha sem carregar tudo na memória
LazyCollection::make(function () {
    $handle = fopen('large-file.csv', 'r');
    
    while (($line = fgets($handle)) !== false) {
        yield $line;
    }
    
    fclose($handle);
})
->map(function ($line) {
    return str_getcsv($line);
})
->filter(function ($row) {
    return $row[2] > 100; // Filtrar apenas registros relevantes
})
->each(function ($row) {
    // Processar cada linha filtrada
    ProcessCsvRow::dispatch($row);
});

// Processamento eficiente de grandes conjuntos do banco de dados
User::query()
    ->where('active', true)
    ->lazyById(1000) // Processa em chunks de 1000 usando o ID como cursor
    ->each(function ($user) {
        // Processar cada usuário sem carregar todos na memória
        $user->sendNewsletterEmail();
    });
                            </pre>
                </div>

                <div class="code-block">
                    <h4>Exemplo: Otimizações de Consultas Eloquent</h4>
                    <pre>
// Uso de subqueries para consultas mais eficientes
$users = User::addSelect(['last_order_date' => 
    Order::select('created_at')
        ->whereColumn('user_id', 'users.id')
        ->latest()
        ->limit(1)
])->get();

// Uso de joins em vez de consultas separadas
$users = User::select('users.*')
    ->join('orders', 'users.id', '=', 'orders.user_id')
    ->where('orders.status', 'completed')
    ->groupBy('users.id')
    ->having(DB::raw('COUNT(orders.id)'), '>', 5)
    ->get();

// Uso de agregações eficientes
$userStats = User::withCount([
    'orders',
    'orders as completed_orders_count' => function ($query) {
        $query->where('status', 'completed');
    }
])
->withSum('orders', 'total')
->withAvg('reviews', 'rating')
->get();
                            </pre>
                </div>

                <div class="tip">
                    <h4>Dica: Aproveite os Recursos do PHP 8.2</h4>
                    <p>O Laravel 12 é otimizado para PHP 8.2, que traz melhorias significativas de performance:
                    </p>
                    <ul>
                        <li>JIT (Just-In-Time) Compilation para código de alta performance</li>
                        <li>Readonly properties para objetos imutáveis mais eficientes</li>
                        <li>Melhorias no gerenciamento de memória</li>
                        <li>Otimizações no engine do PHP</li>
                    </ul>
                    <pre>
// Exemplo de classe com readonly properties (PHP 8.2+)
readonly class ProductData
{
    public function __construct(
        public int $id,
        public string $name,
        public float $price,
        public ?string $description = null,
        public array $categories = []
    )
    {
        // Construtor vazio - propriedades são inicializadas pelos parâmetros
    }
    
    // Métodos para manipular dados sem alterar as propriedades
    public function withDiscount(float $percentage): self
    {
        return new self(
            $this->id,
            $this->name,
            $this->price * (1 - $percentage / 100),
            $this->description,
            $this->categories
        );
    }
    
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'price' => $this->price,
            'description' => $this->description,
            'categories' => $this->categories,
        ];
    }
}
                    </pre>
                </div>
            </div>
        </section>
    </section>

    <section id="case-studies" class="manual-section">
        <h2>10. Estudos de Caso</h2>

        <p>Exemplos reais de otimização de performance em aplicações Laravel.</p>

        <section id="case-study-1" class="subsection">
            <h3>10.1. Otimização de API de Alto Tráfego</h3>

            <div class="content-block">
                <p>Este estudo de caso apresenta a otimização de uma API que precisava lidar com mais de 1.000
                    requisições por segundo.</p>

                <div class="case-study">
                    <h4>Contexto</h4>
                    <p>Uma API de pagamentos que processava transações para um marketplace com milhares de lojas. A API
                        estava enfrentando tempos de resposta lentos e ocasionalmente falhas durante picos de tráfego.
                    </p>

                    <h4>Problemas Identificados</h4>
                    <ul>
                        <li>Consultas N+1 em endpoints críticos</li>
                        <li>Falta de cache para dados frequentemente acessados</li>
                        <li>Processamento síncrono de operações pesadas</li>
                        <li>Configuração inadequada de PHP-FPM e OPCache</li>
                        <li>Falta de escalabilidade horizontal</li>
                    </ul>

                    <h4>Soluções Implementadas</h4>
                    <ol>
                        <li><strong>Otimização de Consultas</strong>: Implementação de eager loading e refatoração de
                            consultas complexas</li>
                        <li><strong>Estratégia de Cache</strong>: Cache em múltiplas camadas (Redis) com invalidação
                            seletiva</li>
                        <li><strong>Processamento Assíncrono</strong>: Migração de operações pesadas para jobs em fila
                        </li>
                        <li><strong>Otimização de Infraestrutura</strong>: Ajuste de PHP-FPM, OPCache e implementação de
                            Laravel Octane</li>
                        <li><strong>Escalabilidade Horizontal</strong>: Implementação de balanceamento de carga com
                            múltiplas instâncias</li>
                    </ol>

                    <h4>Resultados</h4>
                    <ul>
                        <li>Redução do tempo médio de resposta de 850ms para 120ms (85% de melhoria)</li>
                        <li>Aumento da capacidade de 300 RPS para 2.000 RPS</li>
                        <li>Redução do uso de CPU em 60%</li>
                        <li>Eliminação de falhas durante picos de tráfego</li>
                        <li>Redução de custos de infraestrutura em 30%</li>
                    </ul>

                    <h4>Código-chave: Implementação de Cache</h4>
                    <pre>
// Antes: Consulta direta ao banco de dados em cada requisição
public function getTransactionStats($merchantId)
{
    return Transaction::where('merchant_id', $merchantId)
        ->whereBetween('created_at', [now()->startOfDay(), now()])
        ->selectRaw('
            COUNT(*) as total_count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount,
            SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_count
        ')
        ->first();
}

// Depois: Implementação com cache e invalidação seletiva
public function getTransactionStats($merchantId)
{
    $cacheKey = "merchant.{$merchantId}.stats." . now()->format('Y-m-d');
    
    return Cache::remember($cacheKey, 300, function () use ($merchantId) {
        return Transaction::where('merchant_id', $merchantId)
            ->whereBetween('created_at', [now()->startOfDay(), now()])
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount,
                SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_count
            ')
            ->first();
    });
}

// Invalidação seletiva quando uma nova transação é criada
public function afterTransactionCreated(Transaction $transaction)
{
    Cache::forget("merchant.{$transaction->merchant_id}.stats." . now()->format('Y-m-d'));
}
                    </pre>
                </div>
            </div>
        </section>

        <section id="case-study-2" class="subsection">
            <h3>10.2. Melhorando Performance de E-commerce</h3>

            <div class="content-block">
                <p>Este estudo de caso apresenta a otimização de uma plataforma de e-commerce com milhares de produtos e
                    alto tráfego.</p>

                <div class="case-study">
                    <h4>Contexto</h4>
                    <p>Uma plataforma de e-commerce com mais de 50.000 produtos e picos de tráfego durante promoções. Os
                        usuários relatavam lentidão, especialmente na busca e filtragem de produtos.</p>

                    <h4>Problemas Identificados</h4>
                    <ul>
                        <li>Busca de produtos ineficiente</li>
                        <li>Carregamento lento de páginas de categoria</li>
                        <li>Carrinho de compras com performance inadequada</li>
                        <li>Checkout lento e propenso a erros</li>
                        <li>Geração de relatórios bloqueando recursos</li>
                    </ul>

                    <h4>Soluções Implementadas</h4>
                    <ol>
                        <li><strong>Implementação de Elasticsearch</strong>: Para busca e filtragem rápida de produtos
                        </li>
                        <li><strong>Cache de Páginas de Categoria</strong>: Com invalidação seletiva quando produtos são
                            atualizados</li>
                        <li><strong>Migração do Carrinho para Redis</strong>: Armazenamento em memória para operações
                            rápidas</li>
                        <li><strong>Otimização do Checkout</strong>: Processamento assíncrono de etapas não críticas
                        </li>
                        <li><strong>Geração de Relatórios em Background</strong>: Usando filas para processar relatórios
                            pesados</li>
                    </ol>

                    <h4>Resultados</h4>
                    <ul>
                        <li>Redução do tempo de busca de 2s para 150ms</li>
                        <li>Carregamento de páginas de categoria 4x mais rápido</li>
                        <li>Operações de carrinho 10x mais rápidas</li>
                        <li>Redução de 70% no tempo de checkout</li>
                        <li>Aumento de 25% na taxa de conversão</li>
                    </ul>

                    <h4>Código-chave: Implementação de Elasticsearch</h4>
                    <pre>
// Instalação do pacote
composer require elasticsearch/elasticsearch laravel/scout

// Configuração do modelo
namespace App\Models;

use Laravel\Scout\Searchable;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use Searchable;

    protected $fillable = [
        'name', 'description', 'price', 'category_id', 'stock', 'status'
    ];

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $array = $this->toArray();

        // Customizar os dados indexados
        $array = [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'category_id' => $this->category_id,
            'category_name' => $this->category->name,
            'status' => $this->status,
            'stock' => $this->stock,
            'tags' => $this->tags->pluck('name')->toArray(),
            'created_at' => $this->created_at->timestamp,
        ];

        return $array;
    }
}

// Uso no controller
public function search(Request $request)
{
    // Parâmetros de busca
    $query = $request->input('q');
    $category = $request->input('category');
    $minPrice = $request->input('min_price');
    $maxPrice = $request->input('max_price');
    $sort = $request->input('sort', 'relevance');
    $perPage = $request->input('per_page', 24);

    // Construir a busca
    $searchQuery = Product::search($query);

    // Aplicar filtros
    if ($category) {
        $searchQuery->where('category_id', $category);
    }

    // Filtro de preço
    if ($minPrice && $maxPrice) {
        $searchQuery->where('price', '>=', $minPrice)
                   ->where('price', '<=', $maxPrice);
    }

    // Ordenação
    switch ($sort) {
        case 'price_asc':
            $searchQuery->orderBy('price', 'asc');
            break;
        case 'price_desc':
            $searchQuery->orderBy('price', 'desc');
            break;
        case 'newest':
            $searchQuery->orderBy('created_at', 'desc');
            break;
        // Relevância é o padrão do Elasticsearch
    }

    // Executar a busca
    $products = $searchQuery->paginate($perPage);

    // Cache dos resultados por 5 minutos
    $cacheKey = 'search_' . md5(json_encode($request->all()));
    return Cache::remember($cacheKey, 300, function() use ($products) {
        return response()->json([
            'products' => $products->items(),
            'pagination' => [
                'total' => $products->total(),
                'per_page' => $products->perPage(),
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage()
            ]
        ]);
    });
}
                    </pre>
                </div>
            </div>
        </section>
    </section>

    <section id="checklist" class="manual-section">
        <h2>11. Checklist de Performance</h2>

        <p>Use esta checklist para verificar se sua aplicação está seguindo as melhores práticas de performance:</p>

        <div class="checklist-container">
            <div class="checklist-group">
                <h3>Banco de Dados</h3>
                <ul>
                    <li><input type="checkbox"> Índices criados para colunas frequentemente usadas em WHERE, ORDER BY e
                        JOIN
                    </li>
                    <li><input type="checkbox"> Eager loading implementado para evitar problema N+1</li>
                    <li><input type="checkbox"> Consultas complexas otimizadas (uso de joins, subconsultas, etc.)</li>
                    <li><input type="checkbox"> Paginação implementada para grandes conjuntos de dados</li>
                    <li><input type="checkbox"> Seleção apenas das colunas necessárias (evitar SELECT *)</li>
                    <li><input type="checkbox"> Uso de chunking ou cursor para processamento de grandes datasets</li>
                    <li><input type="checkbox"> Transações utilizadas adequadamente</li>
                </ul>
            </div>


            <div class="checklist-group">
                <h3>Cache</h3>
                <ul>
                    <li><input type="checkbox"> Cache implementado para dados frequentemente acessados</li>
                    <li><input type="checkbox"> Estratégia de invalidação de cache definida</li>
                    <li><input type="checkbox"> Cache em múltiplas camadas (aplicação, HTTP, etc.)</li>
                    <li><input type="checkbox"> TTL apropriado para cada tipo de dado</li>
                    <li><input type="checkbox"> Cache condicional (ETag, If-Modified-Since) para APIs</li>
                    <li><input type="checkbox"> Redis ou outro driver de alta performance configurado</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Processamento Assíncrono</h3>
                <ul>
                    <li><input type="checkbox"> Operações pesadas movidas para filas</li>
                    <li><input type="checkbox"> Workers configurados adequadamente</li>
                    <li><input type="checkbox"> Retry e backoff configurados para jobs</li>
                    <li><input type="checkbox"> Monitoramento de filas implementado</li>
                    <li><input type="checkbox"> Priorização de filas para operações críticas</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Otimização de Código</h3>
                <ul>
                    <li><input type="checkbox"> Código refatorado para eliminar redundâncias</li>
                    <li><input type="checkbox"> Algoritmos e estruturas de dados eficientes</li>
                    <li><input type="checkbox"> Uso de coleções e métodos de array otimizados</li>
                    <li><input type="checkbox"> Lazy loading de recursos quando apropriado</li>
                    <li><input type="checkbox"> Uso de recursos do PHP 8.2 (readonly, enums, etc.)</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Infraestrutura</h3>
                <ul>
                    <li><input type="checkbox"> OPCache configurado e otimizado</li>
                    <li><input type="checkbox"> PHP-FPM ajustado para a carga esperada</li>
                    <li><input type="checkbox"> Servidor web otimizado (gzip, cache, etc.)</li>
                    <li><input type="checkbox"> CDN implementado para assets estáticos</li>
                    <li><input type="checkbox"> Laravel Octane configurado (se aplicável)</li>
                    <li><input type="checkbox"> Escalabilidade horizontal implementada (se necessário)</li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Monitoramento</h3>
                <ul>
                    <li><input type="checkbox"> Ferramentas de monitoramento implementadas</li>
                    <li><input type="checkbox"> Alertas configurados para problemas de performance</li>
                    <li><input type="checkbox"> Logging de requisições lentas</li>
                    <li><input type="checkbox"> Profiling periódico da aplicação</li>
                    <li><input type="checkbox"> Métricas-chave sendo coletadas e analisadas</li>
                </ul>
            </div>

            <div class="checklist-container">
                <div class="checklist-group">
                    <h3>Testes</h3>
                    <ul>
                        <li><input type="checkbox"> Testes de carga realizados</li>
                        <li><input type="checkbox"> Testes de estresse para identificar limites</li>
                        <li><input type="checkbox"> Benchmarks de operações críticas</li>
                        <li><input type="checkbox"> Testes de performance automatizados</li>
                        <li><input type="checkbox"> Testes paralelos configurados</li>
                    </ul>
                </div>
            </div>
    </section>

    <section id="conclusao" class="manual-section">
        <h2>12. Conclusão e Melhores Práticas</h2>

        <p>A otimização de performance é um processo contínuo que deve ser integrado ao ciclo de desenvolvimento.
            Seguindo as práticas recomendadas neste manual, você pode construir APIs Laravel que são rápidas, escaláveis
            e eficientes.</p>

        <div class="best-practice">
            <h3>Princípios Fundamentais</h3>
            <ul>
                <li><strong>Medir Primeiro, Otimizar Depois</strong>: Sempre baseie suas otimizações em métricas reais
                </li>
                <li><strong>Otimização Incremental</strong>: Faça pequenas melhorias e meça o impacto</li>
                <li><strong>Foco nos Gargalos</strong>: Concentre-se nos problemas que causam maior impacto</li>
                <li><strong>Monitoramento Contínuo</strong>: Implemente monitoramento para detectar problemas
                    precocemente</li>
                <li><strong>Testes Automatizados</strong>: Garanta que otimizações não quebrem funcionalidades</li>
            </ul>
        </div>

        <p>Lembre-se que a performance não é apenas sobre velocidade, mas também sobre escalabilidade, eficiência de
            recursos e experiência do usuário. Uma API bem otimizada não apenas responde rapidamente, mas também é
            confiável, escalável e econômica para operar.</p>

        <p>O Laravel 12 oferece muitas ferramentas e recursos para construir APIs de alta performance. Aproveite esses
            recursos e continue aprendendo e adaptando suas estratégias de otimização à medida que sua aplicação evolui.
        </p>
    </section>

    <footer class="manual-footer">
        <p>© 2023 - Manual de Performance para APIs PHP - Laravel 12</p>
        <p>Última atualização: Agosto 2023</p>
    </footer>
</body>

</html>