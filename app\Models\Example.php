<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @OA\Schema(
 *     schema="ExampleModel",
 *     title="Example Model",
 *     description="Modelo de exemplo",
 *     @OA\Property(property="id", type="integer", format="int64", example=1),
 *     @OA\Property(property="name", type="string", example="Nome do exemplo"),
 *     @OA\Property(property="description", type="string", example="Descrição do exemplo"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T00:00:00.000000Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2023-01-01T00:00:00.000000Z")
 * )
 *
 * @OA\Schema(
 *     schema="ExampleRequest",
 *     title="Example Request",
 *     description="Dados para criação/atualização de exemplo",
 *     required={"name"},
 *     @OA\Property(property="name", type="string", example="Nome do exemplo"),
 *     @OA\Property(property="description", type="string", example="Descrição do exemplo")
 * )
 */
class Example extends Model
{
    use HasFactory;

    /**
     * Os atributos que são atribuíveis em massa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
    ];
}
