<?php

namespace App\Repositories;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Collection;

/**
 * Repositório para o modelo User
 * 
 * Implementa operações específicas para manipulação de usuários
 */
class UserRepository extends RepositoryAbstract
{
    /**
     * Construtor
     *
     * @param User $model
     */
    public function __construct(User $model)
    {
        parent::__construct($model);
    }

    /**
     * Sobrescreve o método create para tratar a senha
     *
     * @param array $data
     * @return User
     */
    public function create(array $data): User
    {
        // Se a senha estiver presente, garante que seja hasheada
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        return parent::create($data);
    }

    /**
     * Sobrescreve o método update para tratar a senha
     *
     * @param mixed $id
     * @param array $data
     * @return User
     */
    public function update($id, array $data): User
    {
        // Se a senha estiver presente, garante que seja hasheada
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } elseif (isset($data['password']) && empty($data['password'])) {
            // Se a senha estiver vazia, remove do array para não atualizar
            unset($data['password']);
        }

        return parent::update($id, $data);
    }

    /**
     * Busca usuário por email
     *
     * @param string $email
     * @return User|null
     */
    public function findByEmail(string $email): ?User
    {
        return $this->model->where('email', $email)->first();
    }

    /**
     * Busca usuários ativos (com email verificado)
     *
     * @return Collection
     */
    public function findActive(): Collection
    {
        return $this->model->whereNotNull('email_verified_at')->get();
    }
}
