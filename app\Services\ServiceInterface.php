<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Model;
use App\Repositories\RepositoryInterface;

interface ServiceInterface
{
    // Métodos de Acesso
    public function getRepository(): RepositoryInterface;
    public function getModel(): Model;

    // Validação
    public function validate(array $data, array $rules): array;
    public function getCreateRules(): array;
    public function getUpdateRules(): array;

    // Hooks
    public function beforeCreate(array $data): array;
    public function afterCreate(Model $model, array $data): void;
    public function beforeUpdate(array $data): array;
    public function afterUpdate(Model $model, array $data): void;
    public function afterDelete($id): void;

    // Operações Seguras
    public function executeSafely(callable $operation, string $errorMessage): mixed;
}
