<?php

namespace App\Providers;

use App\Swagger\CustomLogger;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\ServiceProvider;
use OpenApi\Annotations\OpenApi;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void {}

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Habilitar o carregamento automático de relacionamentos
        Model::automaticallyEagerLoadRelationships();

        // Registra o logger personalizado para o Swagger
        if (class_exists(OpenApi::class)) {
            $openApi = new OpenApi([]);
            $openApi->_context->logger = new CustomLogger();
        }

        // Habilitar a debugbar
        if (app()->bound('debugbar')) {
            app('debugbar')->enable();
        }
    }
}
