<?php

namespace Tests\Feature;

use App\Models\UserModel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ApiEndpointTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $token;

    protected function setUp(): void
    {
        parent::setUp();

        // Criar usuário para testes
        $this->user = UserModel::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);

        // Obter token de autenticação para os testes
        $loginResponse = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $this->token = $loginResponse->json('data.authorization.token');
    }

    #[Test]
    public function show_endpoint()
    {
        // Fazer requisição autenticada para o endpoint protegido
        $response = $this->withHeader('Authorization', 'Bearer ' . $this->token)
            ->getJson('/api/auth/me');

        // Verificar que a resposta é 200 OK
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'user' => [
                        'id',
                        'name',
                        'email'
                    ]
                ]
            ]);
    }

    #[Test]
    public function show_endpoint_with_invalid_id()
    {
        // Teste de requisição sem autenticação
        $response = $this->getJson('/api/auth/me');

        // Deve retornar 401 Unauthorized
        $response->assertStatus(401);
    }

    #[Test]
    public function health_check_endpoint_works()
    {
        // Teste do endpoint de health check que deve ser público
        $response = $this->getJson('/api/health');

        // Verificar que a resposta é 200 OK
        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'timestamp',
                'version',
                'environment'
            ]);
    }

    #[Test]
    public function it_handles_refresh_token()
    {
        // Primeiro obtemos um refresh token válido
        $loginResponse = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $refreshToken = $loginResponse->json('data.authorization.refresh_token');

        // Tentar refresh
        $response = $this->postJson('/api/auth/refresh', [
            'refresh_token' => $refreshToken
        ]);

        // Verificar que obtemos um novo token
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'authorization' => [
                        'token',
                        'refresh_token',
                        'type',
                        'expires_in'
                    ]
                ]
            ]);
    }

    #[Test]
    public function it_handles_logout()
    {
        // Fazer logout
        $response = $this->withHeader('Authorization', 'Bearer ' . $this->token)
            ->postJson('/api/auth/logout');

        // Verificar que o logout foi bem-sucedido
        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Logout realizado com sucesso'
            ]);

        // Como mencionado anteriormente, não testamos o acesso após logout
        // porque o token pode continuar válido por um período devido ao grace period
    }
}
