<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Testes</title>
    <link rel="stylesheet" href="css/manual.css">
    <!--
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #2980b9;
            margin-top: 40px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        h3 {
            color: #3498db;
            margin-top: 25px;
        }

        .code-section {
            margin-bottom: 50px;
        }

        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', Courier, monospace;
            overflow-x: auto;
        }

        .example {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .example h4 {
            margin-top: 0;
            color: #2980b9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        table,
        th,
        td {
            border: 1px solid #ddd;
        }

        th {
            background-color: #f2f2f2;
            padding: 12px;
            text-align: left;
        }

        td {
            padding: 10px;
        }

        .best-practice {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }

        .bad-practice {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }

        .note {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
            padding: 10px;
        }

        footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }

        #sumario {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }

        #sumario ul {
            list-style-type: none;
            padding-left: 20px;
        }

        #sumario ul li {
            margin-bottom: 8px;
        }

        #sumario a {
            color: #2980b9;
            text-decoration: none;
        }

        #sumario a:hover {
            text-decoration: underline;
        }
    </style>
    -->
</head>

<body>
    <header>
        <h1 style="color: white; border-bottom: none;">Manual de Testes</h1>
        <p>Guia completo para implementação, execução e manutenção de testes</p>
    </header>

    <section id="sumario">
        <h2>Sumário</h2>
        <ul>
            <li><a href="#introducao">1. Introdução</a>
                <ul>
                    <li><a href="#objetivos">1.1. Objetivos</a></li>
                    <li><a href="#principios">1.2. Princípios de Teste</a></li>
                    <li><a href="#estrategia-geral">1.3. Estratégia Geral de Testes</a></li>
                </ul>
            </li>
            <li><a href="#tipos-testes">2. Tipos de Testes</a>
                <ul>
                    <li><a href="#testes-unitarios">2.1. Testes Unitários</a></li>
                    <li><a href="#testes-integracao">2.2. Testes de Integração</a></li>
                    <li><a href="#testes-api">2.3. Testes de API</a></li>
                    <li><a href="#testes-browser">2.4. Testes de Browser</a></li>
                    <li><a href="#testes-performance">2.5. Testes de Performance</a></li>
                    <li><a href="#testes-seguranca">2.6. Testes de Segurança</a></li>
                </ul>
            </li>
            <li><a href="#frameworks">3. Frameworks e Ferramentas</a>
                <ul>
                    <li><a href="#phpunit">3.1. PHPUnit</a></li>
                    <li><a href="#pest">3.2. Pest</a></li>
                    <li><a href="#jest">3.3. Jest</a></li>
                    <li><a href="#dusk">3.4. Laravel Dusk</a></li>
                    <li><a href="#postman">3.5. Postman/Newman</a></li>
                    <li><a href="#outras-ferramentas">3.6. Outras Ferramentas</a></li>
                </ul>
            </li>
            <li><a href="#config-ambiente">4. Configuração do Ambiente de Testes</a>
                <ul>
                    <li><a href="#config-phpunit">4.1. Configuração do PHPUnit</a></li>
                    <li><a href="#banco-dados-teste">4.2. Banco de Dados de Teste</a></li>
                    <li><a href="#mocks-stubs">4.3. Mocks e Stubs</a></li>
                    <li><a href="#factories-seeders">4.4. Factories e Seeders</a></li>
                    <li><a href="#env-teste">4.5. Arquivo .env.testing</a></li>
                </ul>
            </li>
            <li><a href="#escrevendo-testes">5. Escrevendo Testes</a>
                <ul>
                    <li><a href="#estrutura">5.1. Estrutura de Testes</a></li>
                    <li><a href="#nomenclatura">5.2. Nomenclatura</a></li>
                    <li><a href="#assertions">5.3. Assertions</a></li>
                    <li><a href="#fixtures">5.4. Fixtures</a></li>
                    <li><a href="#testes-parametrizados">5.5. Testes Parametrizados</a></li>
                </ul>
            </li>
            <li><a href="#bdd">6. Desenvolvimento Orientado a Comportamento (BDD)</a>
                <ul>
                    <li><a href="#gherkin">6.1. Sintaxe Gherkin</a></li>
                    <li><a href="#behat">6.2. Behat/Cucumber</a></li>
                    <li><a href="#integracao-bdd">6.3. Integração com o Processo de Desenvolvimento</a></li>
                </ul>
            </li>
            <li><a href="#tdd">7. Desenvolvimento Orientado a Testes (TDD)</a>
                <ul>
                    <li><a href="#ciclo-tdd">7.1. Ciclo Red-Green-Refactor</a></li>
                    <li><a href="#beneficios-tdd">7.2. Benefícios do TDD</a></li>
                    <li><a href="#praticas-tdd">7.3. Práticas Recomendadas</a></li>
                </ul>
            </li>
            <li><a href="#execucao-testes">8. Execução de Testes</a>
                <ul>
                    <li><a href="#exec-local">8.1. Execução Local</a></li>
                    <li><a href="#exec-integracao">8.2. Integração Contínua</a></li>
                    <li><a href="#paralelizacao">8.3. Paralelização de Testes</a></li>
                    <li><a href="#filtragem">8.4. Filtragem de Testes</a></li>
                </ul>
            </li>
            <li><a href="#cobertura">9. Cobertura de Testes</a>
                <ul>
                    <li><a href="#ferramentas-cobertura">9.1. Ferramentas de Cobertura</a></li>
                    <li><a href="#metricas">9.2. Métricas e Análise</a></li>
                    <li><a href="#limites-cobertura">9.3. Definindo Limites de Cobertura</a></li>
                </ul>
            </li>
            <li><a href="#boas-praticas">10. Boas Práticas</a>
                <ul>
                    <li><a href="#testes-legiveis">10.1. Testes Legíveis e Manuteníveis</a></li>
                    <li><a href="#anti-padroes">10.2. Anti-padrões em Testes</a></li>
                    <li><a href="#testes-isolados">10.3. Isolamento de Testes</a></li>
                    <li><a href="#revisao-testes">10.4. Revisão de Código de Testes</a></li>
                </ul>
            </li>
            <li><a href="#troubleshooting">11. Troubleshooting</a>
                <ul>
                    <li><a href="#erros-comuns">11.1. Erros Comuns e Soluções</a></li>
                    <li><a href="#testes-lentos">11.2. Lidando com Testes Lentos</a></li>
                    <li><a href="#testes-intermitentes">11.3. Testes Intermitentes</a></li>
                </ul>
            </li>
        </ul>
    </section>

    <section id="introducao">
        <h2>1. Introdução</h2>
        <p>Este manual fornece diretrizes abrangentes para testes de aplicações, abordando diferentes tipos de testes,
            ferramentas, metodologias e boas práticas. O objetivo é estabelecer um padrão de teste consistente que
            garanta a qualidade e a robustez do software.</p>

        <section id="objetivos">
            <h3>1.1. Objetivos</h3>
            <p>Os principais objetivos da nossa estratégia de testes são:</p>
            <ul>
                <li>Garantir que o software atenda aos requisitos funcionais e não-funcionais</li>
                <li>Identificar e corrigir defeitos o mais cedo possível no ciclo de desenvolvimento</li>
                <li>Proporcionar confiança para refatoração e evolução do código</li>
                <li>Documentar o comportamento esperado do sistema</li>
                <li>Reduzir o custo total de manutenção do software</li>
                <li>Prevenir regressões durante a evolução do produto</li>
                <li>Melhorar a qualidade geral do código e da arquitetura</li>
            </ul>
        </section>

        <section id="principios">
            <h3>1.2. Princípios de Teste</h3>
            <div class="best-practice">
                <ul>
                    <li><strong>Teste Cedo e Frequentemente:</strong> Quanto mais cedo um defeito for encontrado, menor
                        o custo para corrigi-lo.</li>
                    <li><strong>Independência:</strong> Os testes devem ser independentes e não devem depender uns dos
                        outros.</li>
                    <li><strong>Repetibilidade:</strong> Os testes devem produzir os mesmos resultados quando executados
                        nas mesmas condições.</li>
                    <li><strong>Determinismo:</strong> Testes não devem apresentar comportamento aleatório ou depender
                        de condições externas não controladas.</li>
                    <li><strong>Isolamento:</strong> Os testes devem isolar as funcionalidades testadas, controlando
                        todas as dependências externas.</li>
                    <li><strong>Objetividade:</strong> Cada teste deve ter um propósito claro e verificar um único
                        conceito.</li>
                    <li><strong>Manutenibilidade:</strong> Os testes devem ser fáceis de entender e manter.</li>
                </ul>
            </div>
        </section>

        <section id="estrategia-geral">
            <h3>1.3. Estratégia Geral de Testes</h3>
            <p>Nossa estratégia de testes segue a <a
                    href="https://martinfowler.com/articles/practical-test-pyramid.html" target="_blank">Pirâmide de
                    Testes</a>, que sugere:</p>

            <ul>
                <li><strong>Base:</strong> Muitos testes unitários de execução rápida</li>
                <li><strong>Meio:</strong> Testes de integração em quantidade moderada</li>
                <li><strong>Topo:</strong> Poucos testes de ponta a ponta (end-to-end)</li>
            </ul>

            <div class="example">
                <h4>Distribuição Recomendada</h4>
                <ul>
                    <li>~70% Testes Unitários</li>
                    <li>~20% Testes de Integração</li>
                    <li>~10% Testes End-to-End / UI</li>
                </ul>
            </div>

            <div class="note">
                <p>Esta distribuição não é rígida e pode variar conforme as necessidades específicas do projeto. O
                    importante é manter uma base sólida de testes rápidos e confiáveis, complementados por testes mais
                    abrangentes para validar o sistema como um todo.</p>
            </div>
        </section>
    </section>

    <section id="tipos-testes">
        <h2>2. Tipos de Testes</h2>
        <p>Esta seção descreve os diferentes tipos de testes que devem ser implementados no projeto.</p>

        <section id="testes-unitarios">
            <h3>2.1. Testes Unitários</h3>
            <p>Testes unitários verificam o funcionamento isolado de componentes individuais do código, como classes ou
                métodos.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam uma única unidade de código</li>
                    <li>São rápidos (milissegundos)</li>
                    <li>Não dependem de sistemas externos (banco de dados, API, sistema de arquivos)</li>
                    <li>Utilizam mocks ou stubs para simular dependências</li>
                    <li>Não requerem configurações complexas</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste Unitário</h4>
                <div class="code-block">
                    namespace Tests\Unit;

                    use App\Services\CalculatorService;
                    use PHPUnit\Framework\TestCase;

                    class CalculatorServiceTest extends TestCase
                    {
                    public function test_it_sums_two_numbers_correctly()
                    {
                    // Arrange
                    $calculator = new CalculatorService();

                    // Act
                    $result = $calculator->add(5, 3);

                    // Assert
                    $this->assertEquals(8, $result);
                    }

                    public function test_it_subtracts_two_numbers_correctly()
                    {
                    $calculator = new CalculatorService();
                    $result = $calculator->subtract(10, 4);
                    $this->assertEquals(6, $result);
                    }
                    }
                </div>
            </div>

            <p>Os testes unitários devem focar na lógica de negócios, validações, transformações de dados e outros
                comportamentos específicos da aplicação.</p>
        </section>

        <section id="testes-integracao">
            <h3>2.2. Testes de Integração</h3>
            <p>Testes de integração verificam se diferentes componentes do sistema funcionam juntos corretamente.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam a interação entre múltiplos componentes</li>
                    <li>Podem incluir acesso a banco de dados real ou simulado</li>
                    <li>São mais lentos que os testes unitários</li>
                    <li>Podem verificar fluxos de trabalho completos</li>
                    <li>Geralmente requerem configuração mais elaborada</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Integração</h4>
                <div class="code-block">
                    namespace Tests\Integration;

                    use App\Models\User;
                    use App\Models\Order;
                    use App\Services\PaymentService;
                    use App\Repositories\OrderRepository;
                    use Tests\TestCase;
                    use Illuminate\Foundation\Testing\RefreshDatabase;

                    class OrderProcessingTest extends TestCase
                    {
                    use RefreshDatabase;

                    public function test_order_is_processed_and_saved_to_database()
                    {
                    // Arrange
                    $user = User::factory()->create();
                    $orderData = [
                    'product_id' => 1,
                    'quantity' => 2,
                    'total' => 100.00
                    ];

                    $orderRepo = app(OrderRepository::class);
                    $paymentService = app(PaymentService::class);

                    // Act
                    $result = $orderRepo->createOrder($user->id, $orderData);
                    $paymentProcessed = $paymentService->processPayment($result->id, $result->total);

                    // Assert
                    $this->assertInstanceOf(Order::class, $result);
                    $this->assertTrue($paymentProcessed);
                    $this->assertDatabaseHas('orders', [
                    'id' => $result->id,
                    'user_id' => $user->id,
                    'total' => 100.00,
                    'status' => 'paid'
                    ]);
                    }
                    }
                </div>
            </div>

            <p>Os testes de integração são particularmente importantes para verificar operações relacionadas ao banco de
                dados, interações entre serviços internos e pontos de integração com sistemas externos.</p>
        </section>

        <section id="testes-api">
            <h3>2.3. Testes de API</h3>
            <p>Os testes de API verificam se os endpoints da aplicação retornam as respostas esperadas para diferentes
                solicitações.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam as interfaces públicas da aplicação</li>
                    <li>Verificam códigos de status HTTP, estrutura de resposta e cabeçalhos</li>
                    <li>Validam tanto caminhos felizes quanto tratamento de erros</li>
                    <li>Podem testar autorização e autenticação</li>
                    <li>Geralmente utilizam banco de dados de teste</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de API</h4>
                <div class="code-block">
                    namespace Tests\Feature\Api;

                    use App\Models\User;
                    use App\Models\Product;
                    use Tests\TestCase;
                    use Laravel\Sanctum\Sanctum;
                    use Illuminate\Foundation\Testing\RefreshDatabase;

                    class ProductApiTest extends TestCase
                    {
                    use RefreshDatabase;

                    public function test_it_returns_list_of_products()
                    {
                    // Arrange
                    Product::factory()->count(3)->create();

                    // Act
                    $response = $this->getJson('/api/products');

                    // Assert
                    $response->assertStatus(200)
                    ->assertJsonCount(3, 'data')
                    ->assertJsonStructure([
                    'data' => [
                    '*' => ['id', 'name', 'price', 'description', 'created_at']
                    ],
                    'links',
                    'meta'
                    ]);
                    }

                    public function test_it_returns_product_details()
                    {
                    // Arrange
                    $product = Product::factory()->create();

                    // Act
                    $response = $this->getJson("/api/products/{$product->id}");

                    // Assert
                    $response->assertStatus(200)
                    ->assertJson([
                    'data' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $product->price,
                    ]
                    ]);
                    }

                    public function test_it_returns_404_for_nonexistent_product()
                    {
                    $response = $this->getJson("/api/products/999");

                    $response->assertStatus(404);
                    }

                    public function test_authenticated_user_can_create_product()
                    {
                    // Arrange
                    Sanctum::actingAs(User::factory()->create(['is_admin' => true]));

                    $productData = [
                    'name' => 'New Product',
                    'price' => 99.99,
                    'description' => 'Product description'
                    ];

                    // Act
                    $response = $this->postJson('/api/products', $productData);

                    // Assert
                    $response->assertStatus(201)
                    ->assertJsonFragment(['name' => 'New Product']);

                    $this->assertDatabaseHas('products', ['name' => 'New Product']);
                    }
                    }
                </div>
            </div>

            <p>Os testes de API são essenciais para garantir que a interface pública da aplicação funciona conforme
                esperado e que as mudanças no código interno não quebrem essa interface.</p>
        </section>

        <section id="testes-browser">
            <h3>2.4. Testes de Browser</h3>
            <p>Testes de browser (ou end-to-end) simulam a interação do usuário com a aplicação através da interface do
                navegador.</p>

            <div class="best-practice">
                <h4>Características</h4>
                <ul>
                    <li>Testam o sistema completo em um ambiente similar ao de produção</li>
                    <li>Verificam a integração frontend-backend</li>
                    <li>Simulam interações reais do usuário (cliques, preenchimento de formulários)</li>
                    <li>São mais lentos e mais frágeis que outros tipos de testes</li>
                    <li>Geralmente requerem um navegador real ou headless</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Browser com Laravel Dusk</h4>
                <div class="code-block">
                    namespace Tests\Browser;

                    use App\Models\User;
                    use Tests\DuskTestCase;
                    use Laravel\Dusk\Browser;

                    class LoginTest extends DuskTestCase
                    {
                    public function test_user_can_login()
                    {
                    $user = User::factory()->create([
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password123')
                    ]);

                    $this->browse(function (Browser $browser) {
                    $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->press('Login')
                    ->assertPathIs('/dashboard')
                    ->assertSee('Welcome');
                    });
                    }

                    public function test_user_cannot_login_with_invalid_credentials()
                    {
                    $this->browse(function (Browser $browser) {
                    $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'wrong-password')
                    ->press('Login')
                    ->assertPathIs('/login')
                    ->assertSee('These credentials do not match our records');
                    });
                    }

                    public function test_user_can_register_and_login()
                    {
                    $this->browse(function (Browser $browser) {
                    $browser->visit('/register')
                    ->type('name', 'Test User')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->type('password_confirmation', 'password123')
                    ->press('Register')
                    ->assertPathIs('/dashboard')
                    ->assertSee('Welcome');
                    });
                    }
                    }
                </div>
            </div>

            <p>Os testes de browser são valiosos para validar fluxos de usuário críticos, como registro, login,
                processos de pagamento e outras interações complexas com a interface de usuário.</p>
        </section>

        <section id="testes-performance">
            <h3>2.5. Testes de Performance</h3>
            <p>Testes de performance avaliam como o sistema se comporta sob carga e se ele atende aos requisitos de
                desempenho.</p>

            <div class="best-practice">
                <h4>Tipos de Testes de Performance</h4>
                <ul>
                    <li><strong>Testes de carga:</strong> Verificam se o sistema funciona adequadamente sob carga
                        esperada</li>
                    <li><strong>Testes de estresse:</strong> Avaliam os limites do sistema aumentando a carga além do
                        esperado</li>
                    <li><strong>Testes de resistência:</strong> Verificam estabilidade e comportamento durante longos
                        períodos</li>
                    <li><strong>Testes de pico:</strong> Simulam picos repentinos de carga</li>
                    <li><strong>Testes de escalabilidade:</strong> Avaliam como o sistema escala com o aumento da carga
                    </li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Performance com k6</h4>
                <div class="code-block">
                    // tests/performance/api_load_test.js
                    import http from 'k6/http';
                    import { check, sleep } from 'k6';

                    export const options = {
                    stages: [
                    { duration: '30s', target: 20 }, // ramp up to 20 users
                    { duration: '1m', target: 20 }, // stay at 20 users for 1 minute
                    { duration: '30s', target: 0 }, // ramp down to 0 users
                    ],
                    thresholds: {
                    http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms http_req_failed:
                        ['rate<0.01'], // less than 1% can fail }, }; export default function() { const
                        BASE_URL='http://app.test/api' ; // Get products listing const
                        productsResponse=http.get(`${BASE_URL}/products`); check(productsResponse,
                        { 'products status 200' : (r)=> r.status === 200,
                        'products response time < 200ms': (r)=> r.timings.duration < 200, 'products returns data' :
                                (r)=> r.json('data').length > 0,
                                });

                                // Get a single product
                                const productId = 1;
                                const productResponse = http.get(`${BASE_URL}/products/${productId}`);
                                check(productResponse, {
                                'product status 200': (r) => r.status === 200,
                                'product response time < 150ms': (r)=> r.timings.duration < 150, }); sleep(1); } </div>
                </div>

                <p>Os testes de performance são cruciais para garantir uma boa experiência do usuário e devem ser
                    executados regularmente, especialmente antes de releases importantes ou após mudanças significativas
                    na arquitetura.</p>
        </section>

        <section id="testes-seguranca">
            <h3>2.6. Testes de Segurança</h3>
            <p>Testes de segurança identificam vulnerabilidades e garantem que a aplicação proteja adequadamente dados e
                funcionalidades sensíveis.</p>

            <div class="best-practice">
                <h4>Áreas de Foco em Testes de Segurança</h4>
                <ul>
                    <li><strong>Autenticação e Autorização:</strong> Verificar controles de acesso e proteção de rotas
                    </li>
                    <li><strong>Injeção de SQL:</strong> Garantir que consultas ao banco de dados estejam protegidas
                    </li>
                    <li><strong>Cross-Site Scripting (XSS):</strong> Verificar se a aplicação sanitiza corretamente
                        entradas de usuário</li>
                    <li><strong>Cross-Site Request Forgery (CSRF):</strong> Confirmar que formulários estão protegidos
                        com tokens CSRF</li>
                    <li><strong>Exposição de dados sensíveis:</strong> Verificar se dados confidenciais são protegidos
                        adequadamente</li>
                    <li><strong>Configurações de segurança:</strong> Validar cabeçalhos HTTP de segurança e
                        configurações</li>
                    <li><strong>Vulnerabilidades em bibliotecas:</strong> Verificar dependências contra bases de
                        vulnerabilidades conhecidas</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste de Segurança</h4>
                <div class="code-block">
                    namespace Tests\Security;

                    use Tests\TestCase;
                    use Illuminate\Foundation\Testing\RefreshDatabase;
                    use App\Models\User;

                    class AuthorizationTest extends TestCase
                    {
                    use RefreshDatabase;

                    public function test_non_admin_cannot_access_admin_panel()
                    {
                    // Arrange
                    $regularUser = User::factory()->create();

                    // Act
                    $this->actingAs($regularUser)
                    ->get('/admin/dashboard')
                    // Assert
                    ->assertStatus(403);
                    }

                    public function test_user_cannot_access_another_users_data()
                    {
                    // Arrange
                    $user1 = User::factory()->create();
                    $user2 = User::factory()->create();

                    // Act: user1 tries to access user2's profile
                    $response = $this->actingAs($user1)
                    ->get("/users/{$user2->id}/profile");

                    // Assert
                    $response->assertForbidden();
                    }

                    public function test_csrf_protection_is_working()
                    {
                    // Arrange
                    $user = User::factory()->create();

                    // Act: attempt to post without CSRF token
                    $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);

                    $response = $this->post('/update-profile', [
                    'name' => 'New Name'
                    ]);

                    // Assert
                    $response->assertStatus(419); // CSRF token mismatch
                    }

                    public function test_sql_injection_protection()
                    {
                    // Attempt SQL injection in query parameter
                    $response = $this->get('/search?q=test%27%20OR%20%271%27=%271');

                    // Should not expose any SQL errors or unexpected results
                    $response->assertStatus(200);
                    $response->assertDontSee('syntax error');
                    $response->assertDontSee('mysql');
                    $response->assertDontSee('SQL');
                    }
                    }
                </div>
            </div>

            <p>Além dos testes automáticos, considere usar ferramentas especializadas como OWASP ZAP, Burp Suite, ou
                Snyk para análises mais completas de segurança. Integrá-las ao pipeline de CI/CD pode ajudar a
                identificar problemas de segurança mais cedo no ciclo de desenvolvimento.</p>
        </section>
    </section>

    <section id="frameworks">
        <h2>3. Frameworks e Ferramentas</h2>
        <p>Esta seção detalha os frameworks e ferramentas recomendados para a implementação de testes na aplicação.</p>

        <section id="phpunit">
            <h3>3.1. PHPUnit</h3>
            <p>PHPUnit é o framework de teste padrão para PHP e vem integrado com o Laravel.</p>

            <div class="best-practice">
                <h4>Prós do PHPUnit</h4>
                <ul>
                    <li>Integração nativa com Laravel</li>
                    <li>Ampla documentação e comunidade</li>
                    <li>Suporte extensivo para mocks e stubs</li>
                    <li>Assertions poderosas para diversos cenários</li>
                    <li>Facilidade de integração com ferramentas de cobertura de código</li>
                </ul>
            </div>

            <div class="example">
                <h4>Estrutura Básica de um Teste PHPUnit</h4>
                <div class="code-block">
                    namespace Tests\Unit;

                    use PHPUnit\Framework\TestCase;
                    use App\Services\TaxCalculator;

                    class TaxCalculatorTest extends TestCase
                    {
                    protected TaxCalculator $calculator;

                    protected function setUp(): void
                    {
                    parent::setUp();
                    $this->calculator = new TaxCalculator();
                    }

                    public function test_calculates_tax_correctly_for_standard_rate()
                    {
                    // Arrange
                    $amount = 100;
                    $rate = 0.1; // 10%

                    // Act
                    $result = $this->calculator->calculate($amount, $rate);

                    // Assert
                    $this->assertEquals(10, $result);
                    }

                    public function test_throws_exception_for_negative_amount()
                    {
                    $this->expectException(\InvalidArgumentException::class);
                    $this->calculator->calculate(-50, 0.1);
                    }

                    public function test_returns_zero_for_zero_amount()
                    {
                    $this->assertEquals(0, $this->calculator->calculate(0, 0.2));
                    }
                    }
                </div>
            </div>
        </section>

        <section id="pest">
            <h3>3.2. Pest</h3>
            <p>Pest é um framework de teste elegante construído sobre o PHPUnit, oferecendo uma experiência mais fluente
                e expressiva.</p>

            <div class="best-practice">
                <h4>Prós do Pest</h4>
                <ul>
                    <li>Sintaxe mais limpa e expressiva</li>
                    <li>Compatível com todas as funcionalidades do PHPUnit</li>
                    <li>Melhor legibilidade de testes</li>
                    <li>Expectativas encadeáveis fluentes</li>
                    <li>Hooks de teste mais intuitivos</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste com Pest</h4>
                <div class="code-block">
                    // tests/Unit/TaxCalculatorTest.php

                    use App\Services\TaxCalculator;

                    // Setup
                    beforeEach(function () {
                    $this->calculator = new TaxCalculator();
                    });

                    // Testes
                    test('calculates tax correctly for standard rate', function () {
                    $result = $this->calculator->calculate(100, 0.1);

                    expect($result)->toBe(10);
                    });

                    test('throws exception for negative amount', function () {
                    $this->expectException(InvalidArgumentException::class);

                    $this->calculator->calculate(-50, 0.1);
                    });

                    test('returns zero for zero amount', function () {
                    $result = $this->calculator->calculate(0, 0.2);

                    expect($result)->toBe(0);
                    });

                    // Testes agrupados
                    describe('special tax calculations', function () {
                    test('applies discount for large amounts', function () {
                    $result = $this->calculator->calculateWithDiscount(1000, 0.1);

                    expect($result)->toBeLessThan(100);
                    });

                    test('respects minimum tax amount', function () {
                    $result = $this->calculator->calculate(10, 0.05);

                    expect($result)->toBeGreaterThanOrEqual(1);
                    });
                    });
                </div>
            </div>
        </section>

        <section id="jest">
            <h3>3.3. Jest</h3>
            <p>Jest é um framework de teste JavaScript mantido pelo Facebook, adequado para testar componentes frontend.
            </p>

            <div class="best-practice">
                <h4>Prós do Jest</h4>
                <ul>
                    <li>Configuração mínima necessária</li>
                    <li>Execução paralela e rápida dos testes</li>
                    <li>Mocking simples e poderoso</li>
                    <li>Snapshots para testes de UI</li>
                    <li>Cobertura de código integrada</li>
                    <li>Watch mode para desenvolvimento iterativo</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste com Jest</h4>
                <div class="code-block">
                    // tests/js/utils/formatter.test.js

                    import { formatCurrency, formatDate } from '@/utils/formatter';

                    describe('Formatter Utils', () => {
                    describe('formatCurrency', () => {
                    test('formats zero value correctly', () => {
                    expect(formatCurrency(0)).toBe('R$ 0,00');
                    });

                    test('formats positive integers correctly', () => {
                    expect(formatCurrency(1234)).toBe('R$ 1.234,00');
                    });

                    test('formats decimals correctly', () => {
                    expect(formatCurrency(1234.56)).toBe('R$ 1.234,56');
                    });

                    test('formats negative values correctly', () => {
                    expect(formatCurrency(-1234.56)).toBe('-R$ 1.234,56');
                    });

                    test('accepts string numbers', () => {
                    expect(formatCurrency('1234.56')).toBe('R$ 1.234,56');
                    });
                    });

                    describe('formatDate', () => {
                    test('formats date in short format by default', () => {
                    const date = new Date(2023, 0, 15); // 15/01/2023
                    expect(formatDate(date)).toBe('15/01/2023');
                    });

                    test('formats date in long format when specified', () => {
                    const date = new Date(2023, 0, 15);
                    expect(formatDate(date, 'long')).toBe('15 de Janeiro de 2023');
                    });

                    test('throws error for invalid date', () => {
                    expect(() => formatDate('invalid-date')).toThrow();
                    });
                    });
                    });
                </div>
            </div>

            <h4>Configuração do Jest com Vue</h4>
            <div class="code-block">
                // jest.config.js
                module.exports = {
                testEnvironment: 'jsdom',
                moduleFileExtensions: [
                'js',
                'json',
                'vue'
                ],
                transform: {
                '^.+\\.vue$': '@vue/vue3-jest',
                '^.+\\.js$': 'babel-jest'
                },
                moduleNameMapper: {
                '^@/(.*)$': '<rootDir>/resources/js/$1'
                    },
                    testMatch: [
                    '**/tests/js/**/*.spec.[jt]s?(x)',
                    '**/tests/js/**/*.test.[jt]s?(x)'
                    ],
                    testEnvironmentOptions: {
                    customExportConditions: ['node', 'node-addons']
                    },
                    coverageDirectory: '<rootDir>/tests/js/coverage',
                        collectCoverageFrom: [
                        'resources/js/**/*.{js,vue}',
                        '!resources/js/vendor/**',
                        '!**/node_modules/**'
                        ]
                        };
            </div>
        </section>

        <section id="dusk">
            <h3>3.4. Laravel Dusk</h3>
            <p>Laravel Dusk fornece uma API expressiva para teste de browser, permitindo automatizar e testar
                comportamentos de usuário em sua aplicação Laravel.</p>

            <div class="best-practice">
                <h4>Prós do Laravel Dusk</h4>
                <ul>
                    <li>API expressiva e fácil de usar</li>
                    <li>Integração nativa com Laravel</li>
                    <li>Não requer instalação do Selenium</li>
                    <li>Suporta headless testing</li>
                    <li>Capacidade de capturar screenshots de falhas</li>
                    <li>Teste de aplicações JavaScript e SPA</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste com Dusk</h4>
                <div class="code-block">
                    namespace Tests\Browser;

                    use App\Models\User;
                    use Tests\DuskTestCase;
                    use Laravel\Dusk\Browser;
                    use Illuminate\Foundation\Testing\DatabaseMigrations;

                    class ShoppingCartTest extends DuskTestCase
                    {
                    use DatabaseMigrations;

                    public function test_user_can_add_product_to_cart()
                    {
                    $user = User::factory()->create();

                    $this->browse(function (Browser $browser) use ($user) {
                    $browser->loginAs($user)
                    ->visit('/products')
                    ->click('.product-card:first-child .add-to-cart')
                    ->waitFor('.cart-count')
                    ->assertSee('1')
                    ->click('.cart-icon')
                    ->waitForRoute('cart.index')
                    ->assertPathIs('/cart')
                    ->assertSee('Shopping Cart (1)')
                    ->assertPresent('.cart-item');
                    });
                    }

                    public function test_user_can_update_product_quantity_in_cart()
                    {
                    $user = User::factory()->create();

                    $this->browse(function (Browser $browser) use ($user) {
                    $browser->loginAs($user)
                    ->visit('/products')
                    ->click('.product-card:first-child .add-to-cart')
                    ->waitFor('.cart-count')
                    ->visit('/cart')
                    ->type('.quantity-input', '2')
                    ->press('Update Cart')
                    ->waitForText('Cart updated')
                    ->assertSee('Cart updated')
                    ->assertValue('.quantity-input', '2')
                    ->assertSee('R$ 159,80'); // Valor total atualizado
                    });
                    }

                    public function test_cart_persists_after_login()
                    {
                    // Adiciona produto ao carrinho sem estar logado
                    $this->browse(function (Browser $browser) {
                    $browser->visit('/products')
                    ->click('.product-card:first-child .add-to-cart')
                    ->waitFor('.cart-count')
                    ->assertSee('1');

                    // Salva o nome do produto para verificação posterior
                    $productName = $browser->text('.product-card:first-child .product-title');

                    // Faz login
                    $user = User::factory()->create();

                    $browser->visit('/login')
                    ->type('email', $user->email)
                    ->type('password', 'password')
                    ->press('Login')
                    ->waitForRoute('dashboard')
                    ->assertSee('1') // Verifica se o contador do carrinho persiste
                    ->visit('/cart')
                    ->assertSee($productName); // Verifica se o produto é o mesmo
                    });
                    }
                    }
                </div>
            </div>

            <h4>Configuração do Dusk</h4>
            <div class="code-block">
                // Instalação
                composer require --dev laravel/dusk
                php artisan dusk:install

                // DuskTestCase.php (configuração personalizada)
                namespace Tests;

                use Facebook\WebDriver\Chrome\ChromeOptions;
                use Facebook\WebDriver\Remote\DesiredCapabilities;
                use Facebook\WebDriver\Remote\RemoteWebDriver;
                use Laravel\Dusk\TestCase as BaseTestCase;

                abstract class DuskTestCase extends BaseTestCase
                {
                use CreatesApplication;

                protected function driver()
                {
                $options = (new ChromeOptions)->addArguments([
                '--disable-gpu',
                '--headless',
                '--window-size=1920,1080',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                ]);

                return RemoteWebDriver::create(
                'http://localhost:9515',
                DesiredCapabilities::chrome()->setCapability(
                ChromeOptions::CAPABILITY, $options
                )
                );
                }

                protected function setUp(): void
                {
                parent::setUp();
                $this->artisan('migrate:fresh --seed');
                }
                }
            </div>
        </section>

        <section id="postman">
            <h3>3.5. Postman/Newman</h3>
            <p>Postman é uma ferramenta popular para testar APIs, enquanto Newman é seu runner de linha de comando que
                permite automatizar esses testes.</p>

            <div class="best-practice">
                <h4>Prós do Postman/Newman</h4>
                <ul>
                    <li>Interface gráfica intuitiva para construção de testes API</li>
                    <li>Exportação de coleções para automação via Newman</li>
                    <li>Suporte a variáveis de ambiente</li>
                    <li>Scripts pré-requisição e pós-requisição</li>
                    <li>Documentação de API automática</li>
                    <li>Monitoramento de endpoints</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Coleção do Postman (formato JSON)</h4>
                <div class="code-block">
                    {
                    "info": {
                    "name": "API Tests",
                    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
                    },
                    "item": [
                    {
                    "name": "Authentication",
                    "item": [
                    {
                    "name": "Login",
                    "request": {
                    "method": "POST",
                    "url": "{{base_url}}/api/login",
                    "header": [
                    {
                    "key": "Content-Type",
                    "value": "application/json"
                    }
                    ],
                    "body": {
                    "mode": "raw",
                    "raw": "{\n \"email\": \"<EMAIL>\",\n \"password\": \"password\"\n}"
                    }
                    },
                    "event": [
                    {
                    "listen": "test",
                    "script": {
                    "type": "text/javascript",
                    "exec": [
                    "pm.test(\"Status code is 200\", function() {",
                    " pm.response.to.have.status(200);",
                    "});",
                    "",
                    "pm.test(\"Response contains token\", function() {",
                    " var jsonData = pm.response.json();",
                    " pm.expect(jsonData.data).to.have.property('token');",
                    " pm.environment.set('auth_token', jsonData.data.token);",
                    "});"
                    ]
                    }
                    }
                    ]
                    },
                    {
                    "name": "Invalid Login",
                    "request": {
                    "method": "POST",
                    "url": "{{base_url}}/api/login",
                    "header": [
                    {
                    "key": "Content-Type",
                    "value": "application/json"
                    }
                    ],
                    "body": {
                    "mode": "raw",
                    "raw": "{\n \"email\": \"<EMAIL>\",\n \"password\": \"wrong-password\"\n}"
                    }
                    },
                    "event": [
                    {
                    "listen": "test",
                    "script": {
                    "type": "text/javascript",
                    "exec": [
                    "pm.test(\"Status code is 401\", function() {",
                    " pm.response.to.have.status(401);",
                    "});",
                    "",
                    "pm.test(\"Response contains error message\", function() {",
                    " var jsonData = pm.response.json();",
                    " pm.expect(jsonData).to.have.property('message');",
                    " pm.expect(jsonData.message).to.equal('Invalid credentials');",
                    "});"
                    ]
                    }
                    }
                    ]
                    }
                    ]
                    },
                    {
                    "name": "Products",
                    "item": [
                    {
                    "name": "Get Products",
                    "request": {
                    "method": "GET",
                    "url": "{{base_url}}/api/products",
                    "header": []
                    },
                    "event": [
                    {
                    "listen": "test",
                    "script": {
                    "type": "text/javascript",
                    "exec": [
                    "pm.test(\"Status code is 200\", function() {",
                    " pm.response.to.have.status(200);",
                    "});",
                    "",
                    "pm.test(\"Response has data array\", function() {",
                    " var jsonData = pm.response.json();",
                    " pm.expect(jsonData).to.have.property('data');",
                    " pm.expect(jsonData.data).to.be.an('array');",
                    "});"
                    ]
                    }
                    }
                    ]
                    },
                    {
                    "name": "Create Product",
                    "request": {
                    "method": "POST",
                    "url": "{{base_url}}/api/products",
                    "header": [
                    {
                    "key": "Authorization",
                    "value": "Bearer {{auth_token}}"
                    },
                    {
                    "key": "Content-Type",
                    "value": "application/json"
                    }
                    ],
                    "body": {
                    "mode": "raw",
                    "raw": "{\n \"name\": \"New Product\",\n \"price\": 99.99,\n \"description\": \"Test product\"\n}"
                    }
                    },
                    "event": [
                    {
                    "listen": "test",
                    "script": {
                    "type": "text/javascript",
                    "exec": [
                    "pm.test(\"Status code is 201\", function() {",
                    " pm.response.to.have.status(201);",
                    "});",
                    "",
                    "pm.test(\"Response contains created product\", function() {",
                    " var jsonData = pm.response.json();",
                    " pm.expect(jsonData.data).to.have.property('id');",
                    " pm.expect(jsonData.data.name).to.equal('New Product');",
                    " pm.environment.set('product_id', jsonData.data.id);",
                    "});"
                    ]
                    }
                    }
                    ]
                    }
                    ]
                    }
                    ]
                    }
                </div>
            </div>

            <h4>Execução via Newman</h4>
            <div class="code-block">
                // Instalação do Newman
                npm install -g newman

                // Execução dos testes
                newman run collection.json -e environment.json --reporters cli,htmlextra --reporter-htmlextra-export
                ./reports/report.html

                // Integração com CI/CD (exemplo para GitHub Actions)
                name: API Tests

                on:
                push:
                branches: [ main, develop ]
                pull_request:
                branches: [ main, develop ]

                jobs:
                api-tests:
                runs-on: ubuntu-latest
                steps:
                - uses: actions/checkout@v3

                - name: Install Newman
                run: npm install -g newman newman-reporter-htmlextra

                - name: Run API tests
                run: |
                newman run ./tests/postman/collection.json \
                -e ./tests/postman/environment.json \
                --reporters cli,htmlextra \
                --reporter-htmlextra-export ./reports/report.html

                - name: Upload test results
                uses: actions/upload-artifact@v3
                if: always()
                with:
                name: api-test-results
                path: ./reports/report.html
            </div>
        </section>

        <section id="outras-ferramentas">
            <h3>3.6. Outras Ferramentas</h3>
            <p>Além das ferramentas principais, existem outras que podem complementar a sua estratégia de testes:</p>

            <table>
                <thead>
                    <tr>
                        <th>Ferramenta</th>
                        <th>Propósito</th>
                        <th>Quando usar</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Cypress</td>
                        <td>Testes end-to-end modernos</td>
                        <td>Quando precisar de testes de frontend mais robustos e com melhor depuração que o Dusk</td>
                    </tr>
                    <tr>
                        <td>PHPStan</td>
                        <td>Análise estática de código</td>
                        <td>Para detectar erros sem executar o código</td>
                    </tr>
                    <tr>
                        <td>Mockery</td>
                        <td>Biblioteca de mock para PHP</td>
                        <td>Para criar mocks mais complexos que os nativos do PHPUnit</td>
                    </tr>
                    <tr>
                        <td>JMeter</td>
                        <td>Testes de carga avançados</td>
                        <td>Para testes de performance sofisticados</td>
                    </tr>
                    <tr>
                        <td>Phan</td>
                        <td>Análise estática de código PHP</td>
                        <td>Alternativa ou complemento ao PHPStan</td>
                    </tr>
                    <tr>
                        <td>Codeception</td>
                        <td>Framework de testes PHP tudo-em-um</td>
                        <td>Quando precisar de um framework que abranja vários tipos de testes</td>
                    </tr>
                    <tr>
                        <td>Infection</td>
                        <td>Testes de mutação</td>
                        <td>Para verificar a eficácia dos seus testes</td>
                    </tr>
                </tbody>
            </table>

            <div class="best-practice">
                <h4>Escolhendo as Ferramentas Certas</h4>
                <p>Considere estes fatores ao selecionar ferramentas de teste:</p>
                <ul>
                    <li>Compatibilidade com o ecossistema existente</li>
                    <li>Curva de aprendizado da equipe</li>
                    <li>Manutenibilidade a longo prazo</li>
                    <li>Integração com o pipeline de CI/CD</li>
                    <li>Desempenho e velocidade de execução</li>
                    <li>Documentação e suporte da comunidade</li>
                </ul>
            </div>
        </section>
    </section>

    <section id="config-ambiente">
        <h2>4. Configuração do Ambiente de Testes</h2>
        <p>Um ambiente de teste bem configurado é essencial para testes confiáveis e eficientes.</p>

        <section id="config-phpunit">
            <h3>4.1. Configuração do PHPUnit</h3>
            <p>A configuração adequada do PHPUnit é fundamental para um ambiente de testes eficaz.</p>

            <div class="example">
                <h4>Arquivo phpunit.xml</h4>
                <div class="code-block">
                    <?xml version="1.0" encoding="UTF-8"?>
                    <phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                        xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
                        bootstrap="vendor/autoload.php" colors="true">
                        <testsuites>
                            <testsuite name="Unit">
                                <directory suffix="Test.php">./tests/Unit</directory>
                            </testsuite>
                            <testsuite name="Feature">
                                <directory suffix="Test.php">./tests/Feature</directory>
                            </testsuite>
                            <testsuite name="Integration">
                                <directory suffix="Test.php">./tests/Integration</directory>
                            </testsuite>
                            <testsuite name="API">
                                <directory suffix="Test.php">./tests/API</directory>
                            </testsuite>
                        </testsuites>
                        <coverage processUncoveredFiles="true">
                            <include>
                                <directory suffix=".php">./app</directory>
                            </include>
                            <exclude>
                                <directory suffix=".php">./app/Console</directory>
                                <directory suffix=".php">./app/Exceptions</directory>
                                <file>./app/Http/Middleware/Authenticate.php</file>
                                <file>./app/Http/Middleware/RedirectIfAuthenticated.php</file>
                                <file>./app/Http/Middleware/TrustHosts.php</file>
                            </exclude>
                            <report>
                                <html outputDirectory="tests/coverage" lowUpperBound="50" highLowerBound="90" />
                                <clover outputFile="tests/coverage/clover.xml" />
                            </report>
                        </coverage>
                        <php>
                            <server name="APP_ENV" value="testing" />
                            <server name="BCRYPT_ROUNDS" value="4" />
                            <server name="CACHE_DRIVER" value="array" />
                            <server name="DB_CONNECTION" value="sqlite" />
                            <server name="DB_DATABASE" value=":memory:" />
                            <server name="MAIL_MAILER" value="array" />
                            <server name="QUEUE_CONNECTION" value="sync" />
                            <server name="SESSION_DRIVER" value="array" />
                            <server name="TELESCOPE_ENABLED" value="false" />
                            <server name="MAIL_FROM_ADDRESS" value="<EMAIL>" />
                        </php>
                        <listeners>
                            <listener class="Tests\TestListener" />
                        </listeners>
                    </phpunit>
                </div>
            </div>

            <div class="best-practice">
                <h4>Configurações Recomendadas</h4>
                <ul>
                    <li>Use SQLite em memória para testes mais rápidos</li>
                    <li>Desative serviços pesados como Telescope em testes</li>
                    <li>Configure drivers leves (array) para cache, sessão e filas</li>
                    <li>Reduza as rodadas de bcrypt para senhas mais rápidas</li>
                    <li>Agrupe testes por tipo em testsuites separadas</li>
                    <li>Configure relatórios de cobertura adequados</li>
                </ul>
            </div>
        </section>

        <section id="banco-dados-teste">
            <h3>4.2. Banco de Dados de Teste</h3>
            <p>A configuração adequada do banco de dados de teste é crucial para testes confiáveis e isolados.</p>

            <div class="best-practice">
                <h4>Opções para Banco de Dados de Teste</h4>
                <ul>
                    <li><strong>SQLite em memória:</strong> Mais rápido, ideal para maioria dos testes</li>
                    <li><strong>MySQL/PostgreSQL de teste:</strong> Mais próximo da produção, necessário para testar
                        recursos específicos</li>
                    <li><strong>Banco de dados truncado:</strong> Usar o mesmo schema mas limpar dados entre testes</li>
                    <li><strong>Bancos de dados dedicados por teste:</strong> Maior isolamento, mais recursos
                        necessários</li>
                </ul>
            </div>

            <div class="example">
                <h4>Configuração para Diferentes Bancos de Dados de Teste</h4>
                <div class="code-block">
                    // .env.testing com SQLite em memória
                    DB_CONNECTION=sqlite
                    DB_DATABASE=:memory:

                    // .env.testing.mysql para testes específicos com MySQL
                    DB_CONNECTION=mysql
                    DB_HOST=127.0.0.1
                    DB_PORT=3306
                    DB_DATABASE=app_testing
                    DB_USERNAME=test_user
                    DB_PASSWORD=test_password

                    // Uso em teste específico
                    class SpecificDatabaseFeatureTest extends TestCase
                    {
                    protected function setUp(): void
                    {
                    $this->loadEnvironmentFrom('.env.testing.mysql');
                    parent::setUp();
                    }

                    // testes...
                    }
                </div>
            </div>

            <div class="best-practice">
                <h4>Traits para Testes de Banco de Dados</h4>
                <p>O Laravel fornece várias traits úteis para trabalhar com banco de dados em testes:</p>
                <ul>
                    <li><code>RefreshDatabase</code>: Recria o banco a cada teste (ideal para SQLite em memória)</li>
                    <li><code>DatabaseMigrations</code>: Executa migrações antes e revert depois de cada teste</li>
                    <li><code>DatabaseTransactions</code>: Executa cada teste em uma transação e faz rollback no final
                    </li>
                    <li><code>WithoutMiddleware</code>: Desativa middleware quando não for relevante para o teste</li>
                </ul>
            </div>

            <div class="code-block">
                namespace Tests\Feature;

                use Tests\TestCase;
                use App\Models\User;
                use Illuminate\Foundation\Testing\RefreshDatabase;

                class UserManagementTest extends TestCase
                {
                use RefreshDatabase;

                public function test_admin_can_create_user()
                {
                // Arrange
                $admin = User::factory()->admin()->create();
                $userData = [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                ];

                // Act
                $response = $this->actingAs($admin)->post('/users', $userData);

                // Assert
                $response->assertRedirect('/users');
                $this->assertDatabaseHas('users', [
                'email' => '<EMAIL>',
                'name' => 'Test User'
                ]);
                }
                }
            </div>
        </section>

        <section id="mocks-stubs">
            <h3>4.3. Mocks e Stubs</h3>
            <p>Mocks e stubs são fundamentais para isolar o código que está sendo testado de suas dependências externas.
            </p>

            <div class="best-practice">
                <h4>Quando usar Mocks e Stubs</h4>
                <ul>
                    <li><strong>Use mocks</strong> quando precisar verificar interações/comportamentos</li>
                    <li><strong>Use stubs</strong> quando precisar apenas de valores de retorno predefinidos</li>
                    <li>Substitua APIs externas, serviços de pagamento, e outros sistemas externos</li>
                    <li>Simule condições difíceis de reproduzir (ex: erros de rede)</li>
                    <li>Acelere testes eliminando operações lentas</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo com PHPUnit Mock</h4>
                <div class="code-block">
                    namespace Tests\Unit;

                    use App\Services\PaymentGateway;
                    use App\Services\OrderProcessor;
                    use PHPUnit\Framework\TestCase;

                    class OrderProcessorTest extends TestCase
                    {
                    public function test_order_is_marked_as_paid_when_payment_succeeds()
                    {
                    // Create mock for PaymentGateway
                    $paymentGateway = $this->createMock(PaymentGateway::class);

                    // Set expectations on the mock
                    $paymentGateway->expects($this->once())
                    ->method('processPayment')
                    ->with($this->equalTo(100.00), $this->equalTo('****************'))
                    ->willReturn(true);

                    // Create the OrderProcessor with the mock
                    $orderProcessor = new OrderProcessor($paymentGateway);

                    // Act
                    $result = $orderProcessor->process([
                    'amount' => 100.00,
                    'card_number' => '****************',
                    'items' => [/* ... */]
                    ]);

                    // Assert
                    $this->assertTrue($result->isPaid());
                    $this->assertEquals('completed', $result->status);
                    }

                    public function test_order_is_marked_as_failed_when_payment_fails()
                    {
                    // Create mock for PaymentGateway that returns false
                    $paymentGateway = $this->createMock(PaymentGateway::class);
                    $paymentGateway->method('processPayment')->willReturn(false);

                    $orderProcessor = new OrderProcessor($paymentGateway);
                    $result = $orderProcessor->process([
                    'amount' => 100.00,
                    'card_number' => '****************',
                    'items' => [/* ... */]
                    ]);

                    $this->assertFalse($result->isPaid());
                    $this->assertEquals('payment_failed', $result->status);
                    }
                    }
                </div>
            </div>

            <div class="example">
                <h4>Exemplo com Mockery</h4>
                <div class="code-block">
                    namespace Tests\Unit;

                    use Mockery;
                    use App\Services\NotificationService;
                    use App\Services\UserNotifier;
                    use Tests\TestCase;

                    class UserNotifierTest extends TestCase
                    {
                    public function test_notification_is_sent_to_user()
                    {
                    // Criar mock
                    $notificationService = Mockery::mock(NotificationService::class);

                    // Configurar expectativas
                    $notificationService->shouldReceive('send')
                    ->once()
                    ->with('<EMAIL>', 'Welcome', Mockery::any())
                    ->andReturn(true);

                    // Injetar mock
                    $userNotifier = new UserNotifier($notificationService);

                    // Act
                    $result = $userNotifier->welcomeUser([
                    'email' => '<EMAIL>',
                    'name' => 'Test User'
                    ]);

                    // Assert
                    $this->assertTrue($result);
                    }

                    public function test_retries_sending_when_first_attempt_fails()
                    {
                    $notificationService = Mockery::mock(NotificationService::class);

                    // Configurar sequência de retornos
                    $notificationService->shouldReceive('send')
                    ->ordered()
                    ->once()
                    ->andReturn(false);

                    $notificationService->shouldReceive('send')
                    ->ordered()
                    ->once()
                    ->andReturn(true);

                    $userNotifier = new UserNotifier($notificationService);
                    $result = $userNotifier->welcomeUser([
                    'email' => '<EMAIL>',
                    'name' => 'Test User'
                    ]);

                    $this->assertTrue($result);
                    }

                    protected function tearDown(): void
                    {
                    Mockery::close();
                    parent::tearDown();
                    }
                    }
                </div>
            </div>

            <div class="note">
                <p>Para interfaces complexas, considere criar classes stub dedicadas em vez de usar mocks gerados
                    dinamicamente. Isso torna os testes mais legíveis e manuteníveis.</p>
            </div>
        </section>

        <section id="factories-seeders">
            <h3>4.4. Factories e Seeders</h3>
            <p>Factories e seeders são essenciais para criar dados de teste consistentes e realistas.</p>

            <div class="best-practice">
                <h4>Práticas para Factories</h4>
                <ul>
                    <li>Crie factories para todos os modelos principais</li>
                    <li>Use estados para representar variações específicas</li>
                    <li>Defina relações entre factories quando apropriado</li>
                    <li>Mantenha os dados gerados próximos da realidade</li>
                    <li>Use Faker para gerar dados aleatórios mas consistentes</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Factory com Estados e Relações</h4>
                <div class="code-block">
                    namespace Database\Factories;

                    use App\Models\User;
                    use App\Models\Team;
                    use Illuminate\Database\Eloquent\Factories\Factory;
                    use Illuminate\Support\Str;

                    class UserFactory extends Factory
                    {
                    protected $model = User::class;

                    public function definition()
                    {
                    return [
                    'name' => $this->faker->name(),
                    'email' => $this->faker->unique()->safeEmail(),
                    'email_verified_at' => now(),
                    'password' => bcrypt('password'), // senha padrão para testes
                    'remember_token' => Str::random(10),
                    'is_admin' => false,
                    'status' => 'active',
                    'last_login_at' => $this->faker->dateTimeThisMonth(),
                    ];
                    }

                    // Estado para usuários admin
                    public function admin()
                    {
                    return $this->state(function (array $attributes) {
                    return [
                    'is_admin' => true,
                    'role' => 'admin',
                    ];
                    });
                    }

                    // Estado para usuários premium
                    public function premium()
                    {
                    return $this->state(function (array $attributes) {
                    return [
                    'subscription_type' => 'premium',
                    'subscription_ends_at' => now()->addMonths(3),
                    ];
                    });
                    }

                    // Estado para usuários inativos
                    public function inactive()
                    {
                    return $this->state(function (array $attributes) {
                    return [
                    'status' => 'inactive',
                    'last_login_at' => now()->subMonths(2),
                    ];
                    });
                    }

                    // Configuração para criar um usuário com endereço e time
                    public function withTeamAndAddress()
                    {
                    return $this->has(
                    Team::factory()
                    ->count(1)
                    ->state(function (array $attributes, User $user) {
                    return ['owner_id' => $user->id];
                    })
                    )->afterCreating(function (User $user) {
                    $user->address()->create([
                    'street' => $this->faker->streetAddress(),
                    'city' => $this->faker->city(),
                    'state' => $this->faker->state(),
                    'postal_code' => $this->faker->postcode(),
                    ]);
                    });
                    }
                    }
                </div>
            </div>

            <div class="example">
                <h4>Uso de Factories em Testes</h4>
                <div class="code-block">
                    namespace Tests\Feature;

                    use App\Models\User;
                    use App\Models\Order;
                    use App\Models\Product;
                    use Tests\TestCase;
                    use Illuminate\Foundation\Testing\RefreshDatabase;

                    class OrderManagementTest extends TestCase
                    {
                    use RefreshDatabase;

                    public function test_user_can_see_their_orders()
                    {
                    // Arrange
                    $user = User::factory()->create();
                    $otherUser = User::factory()->create();

                    // Cria 3 pedidos para o usuário principal
                    $userOrders = Order::factory()
                    ->count(3)
                    ->for($user)
                    ->has(Product::factory()->count(2))
                    ->create();

                    // Cria 2 pedidos para outro usuário
                    $otherOrders = Order::factory()
                    ->count(2)
                    ->for($otherUser)
                    ->create();

                    // Act
                    $response = $this->actingAs($user)->get('/orders');

                    // Assert
                    $response->assertStatus(200);

                    // Verifica se vê apenas seus próprios pedidos
                    foreach ($userOrders as $order) {
                    $response->assertSee($order->reference_number);
                    }

                    foreach ($otherOrders as $order) {
                    $response->assertDontSee($order->reference_number);
                    }
                    }

                    public function test_admin_can_see_all_orders()
                    {
                    // Arrange
                    $admin = User::factory()->admin()->create();
                    $orders = Order::factory()->count(5)->create();

                    // Act
                    $response = $this->actingAs($admin)->get('/admin/orders');

                    // Assert
                    $response->assertStatus(200);

                    foreach ($orders as $order) {
                    $response->assertSee($order->reference_number);
                    }
                    }
                    }
                </div>
            </div>

            <div class="best-practice">
                <h4>Seeders para Testes</h4>
                <p>Além de factories, seeders específicos para o ambiente de teste podem ser úteis:</p>

                <div class="code-block">
                    namespace Database\Seeders;

                    use App\Models\User;
                    use App\Models\Category;
                    use App\Models\Product;
                    use Illuminate\Database\Seeder;

                    class TestDatabaseSeeder extends Seeder
                    {
                    public function run()
                    {
                    // Criar usuários padrão para teste
                    $admin = User::factory()->admin()->create([
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password')
                    ]);

                    $user = User::factory()->create([
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password')
                    ]);

                    // Criar categorias
                    $categories = Category::factory()->count(5)->create();

                    // Criar produtos em cada categoria
                    $categories->each(function ($category) {
                    Product::factory()
                    ->count(5)
                    ->for($category)
                    ->create();
                    });
                    }
                    }
                </div>

                <p>Execute o seeder específico para testes:</p>

                <div class="code-block">
                    // Em TestCase.php ou no próprio teste
                    protected function setUp(): void
                    {
                    parent::setUp();

                    // Seed apenas dados específicos para testes
                    $this->seed(TestDatabaseSeeder::class);
                    }
                </div>
            </div>
        </section>

        <section id="env-teste">
            <h3>4.5. Arquivo .env.testing</h3>
            <p>Um arquivo .env.testing separado ajuda a isolar o ambiente de teste de outros ambientes.</p>

            <div class="example">
                <h4>Exemplo de .env.testing</h4>
                <div class="code-block">
                    APP_ENV=testing
                    APP_KEY=base64:sua_chave_de_aplicativo_aqui
                    APP_DEBUG=true
                    APP_URL=http://localhost

                    LOG_CHANNEL=stack
                    LOG_LEVEL=debug

                    DB_CONNECTION=sqlite
                    DB_DATABASE=:memory:

                    BROADCAST_DRIVER=log
                    CACHE_DRIVER=array
                    FILESYSTEM_DRIVER=local
                    QUEUE_CONNECTION=sync
                    SESSION_DRIVER=array
                    SESSION_LIFETIME=120

                    MAIL_MAILER=array
                    MAIL_FROM_ADDRESS=<EMAIL>
                    MAIL_FROM_NAME="Test Application"

                    # Desativar serviços externos
                    STRIPE_KEY=test_stripe_key
                    STRIPE_SECRET=test_stripe_secret
                    STRIPE_WEBHOOK_SECRET=test_stripe_webhook_secret
                    STRIPE_TEST_MODE=true

                    # APIs externas
                    EXTERNAL_API_URL=https://mockapi.test/api
                    EXTERNAL_API_KEY=test_api_key

                    # Desativar recursos pesados
                    TELESCOPE_ENABLED=false
                    DEBUGBAR_ENABLED=false
                </div>
            </div>

            <div class="best-practice">
                <h4>Dicas para o Ambiente de Teste</h4>
                <ul>
                    <li>Mantenha consistência entre ambientes (local, testing, CI)</li>
                    <li>Use drivers em memória sempre que possível (array, log)</li>
                    <li>Mantenha serviços externos em modo de teste ou mock</li>
                    <li>Desative funcionalidades desnecessárias para melhorar a velocidade</li>
                    <li>Evite conexões de rede reais em testes automatizados</li>
                    <li>Defina dados de teste consistentes (emails, chaves API)</li>
                </ul>
            </div>

            <div class="note">
                <p>Para garantir que o arquivo .env.testing seja carregado automaticamente em seus testes, execute-os
                    com o comando <code>php artisan test</code>, que já configura o ambiente de teste. Se você executar
                    os testes diretamente com o PHPUnit, defina a variável de ambiente <code>APP_ENV=testing</code> ou
                    utilize o método <code>$this->loadEnvironmentFrom('.env.testing')</code> em sua classe TestCase.</p>
            </div>
        </section>
    </section>

    <section id="escrevendo-testes">
        <h2>5. Escrevendo Testes</h2>
        <p>Esta seção apresenta práticas e convenções para escrever testes claros, eficazes e manuteníveis.</p>

        <section id="estrutura">
            <h3>5.1. Estrutura de Testes</h3>
            <p>Organizar seus testes de forma lógica e consistente facilita a manutenção e o entendimento do código.</p>

            <div class="best-practice">
                <h4>Estrutura de Diretórios Recomendada</h4>
                <pre>
                    tests/
                    ├── Unit/                   # Testes unitários isolados
                    │   ├── Services/           # Testes de serviços
                    │   ├── Models/             # Testes de modelos
                    │   └── Helpers/            # Testes de helpers
                    ├── Feature/                # Testes de funcionalidades
                    │   ├── Auth/               # Testes de autenticação
                    │   ├── Admin/              # Testes de painel administrativo
                    │   └── User/               # Testes de funcionalidades de usuário
                    ├── Integration/            # Testes de integração entre componentes
                    │   ├── Database/           # Testes de integração com banco
                    │   └── Services/           # Testes de integração entre serviços
                    ├── API/                    # Testes de API
                    │   ├── v1/                 # Testes da API v1
                    │   └── v2/                 # Testes da API v2
                    ├── Browser/                # Testes Dusk de navegador
                    │   ├── Auth/               # Testes de navegador para autenticação
                    │   └── Checkout/           # Testes de navegador para checkout
                    ├── Performance/            # Testes de performance
                    ├── Fixtures/               # Arquivos de apoio para testes
                    └── TestCase.php            # Classe base para testes
                    </pre>
            </div>

            <div class="best-practice">
                <h4>Padrão de Escrita de Teste</h4>
                <p>Estruture cada teste seguindo o padrão AAA (Arrange-Act-Assert):</p>
                <ul>
                    <li><strong>Arrange:</strong> Configure o cenário de teste</li>
                    <li><strong>Act:</strong> Execute a ação que está sendo testada</li>
                    <li><strong>Assert:</strong> Verifique os resultados esperados</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplo de Teste com Padrão AAA</h4>
                <div class="code-block">
                    public function test_user_can_update_profile()
                    {
                    // Arrange
                    $user = User::factory()->create([
                    'name' => 'Original Name',
                    'email' => '<EMAIL>'
                    ]);

                    // Act
                    $response = $this->actingAs($user)->put('/profile', [
                    'name' => 'Updated Name',
                    'email' => '<EMAIL>'
                    ]);

                    // Assert
                    $response->assertRedirect('/profile');
                    $this->assertDatabaseHas('users', [
                    'id' => $user->id,
                    'name' => 'Updated Name',
                    'email' => '<EMAIL>'
                    ]);
                    }
                </div>
            </div>

            <div class="best-practice">
                <h4>Um Conceito por Teste</h4>
                <p>Cada teste deve verificar um único conceito ou comportamento. Isso melhora a legibilidade, facilita o
                    diagnóstico de falhas e mantém os testes focados.</p>

                <p>Bom exemplo:</p>
                <div class="code-block">
                    public function test_user_cannot_register_with_existing_email()
                    {
                    // Teste focado em um único conceito
                    }

                    public function test_user_cannot_register_with_weak_password()
                    {
                    // Outro teste focado em um único conceito
                    }
                </div>

                <p>Evitar:</p>
                <div class="code-block">
                    public function test_user_registration_validations()
                    {
                    // Evite combinar múltiplos conceitos em um único teste
                    // ...validar email existente
                    // ...validar senha fraca
                    // ...validar nome em branco
                    // etc.
                    }
                </div>
            </div>
        </section>

        <section id="nomenclatura">
            <h3>5.2. Nomenclatura</h3>
            <p>Nomes de teste claros e descritivos são essenciais para documentar o comportamento do código e facilitar
                a depuração.</p>

            <div class="best-practice">
                <h4>Convenções de Nomenclatura</h4>
                <ul>
                    <li>Use nomes que descrevam o que está sendo testado</li>
                    <li>Prefixe métodos com <code>test_</code> ou use anotações <code>@test</code></li>
                    <li>Use verbos para indicar a ação ou verificação realizada</li>
                    <li>Inclua contextos relevantes no nome</li>
                    <li>Prefira nomes longos e descritivos a nomes curtos e ambíguos</li>
                </ul>
            </div>

            <div class="example">
                <h4>Exemplos de Boa Nomenclatura</h4>
                <ul>
                    <li><code>test_user_can_login_with_valid_credentials()</code></li>
                    <li><code>test_order_is_marked_paid_when_payment_succeeds()</code></li>
                    <li><code>test_email_is_sent_to_admin_when_order_is_cancelled()</code></li>
                    <li><code>test_throws_exception_when_product_not_found()</code></li>
                    <li><code>test_premium_user_sees_exclusive_content()</code></li>
                </ul>
            </div>

            <div class="bad-practice">
                <h4>Nomenclatura a Evitar</h4>
                <ul>
                    <li><code>test_login()</code> - Muito vago</li>
                    <li><code>test_user_1()</code> - Não descreve o comportamento</li>
                    <li><code>test_database()</code> - Não específico</li>
                    <li><code>test_the_system_should_validate_the_input_and_return_appropriate_error_message_when_validation_fails()</code>
                        - Excessivamente longo</li>
                </ul>
            </div>

            <div class="best-practice">
                <h4>Nomeando Classes de Teste</h4>
                <p>Siga estas convenções para nomear classes de teste:</p>
                <ul>
                    <li>Classe testada + sufixo "Test" (ex: <code>UserServiceTest</code>)</li>
                    <li>Para testes de funcionalidade, use nomes descritivos (ex: <code>UserRegistrationTest</code>)
                    </li>
                    <li>Agrupe testes relacionados em classes relevantes</li>
                </ul>
            </div>
        </section>

        <section id="assertions">
            <h3>5.3. Assertions</h3>
            <p>Escolha as assertions adequadas para tornar seus testes claros e expressivos.</p>

            <div class="example">
                <h4>Assertions Comuns do PHPUnit</h4>
                <div class="code-block">
                    // Básicas
                    $this->assertEquals($expected, $actual);
                    $this->assertSame($expected, $actual); // Comparação com ===
                    $this->assertTrue($condition);
                    $this->assertFalse($condition);

                    // Comparações
                    $this->assertGreaterThan($value, $actual);
                    $this->assertLessThanOrEqual($value, $actual);

                    // Arrays
                    $this->assertCount(3, $array);
                    $this->assertContains('value', $array);
                    $this->assertArrayHasKey('key', $array);

                    // Strings
                    $this->assertStringContainsString('needle', 'haystack');
                    $this->assertStringStartsWith('prefix', $string);
                    $this->assertMatchesRegularExpression('/pattern/', $string);

                    // Objetos
                    $this->assertInstanceOf(ExpectedClass::class, $object);
                    $this->assertObjectHasAttribute('property', $object);

                    // Banco de dados (Laravel)
                    $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
                    $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
                    $this->assertDatabaseCount('users', 5);

                    // Exceções
                    $this->expectException(NotFoundException::class);
                    $this->expectExceptionMessage('User not found');

                    // Soft Assertions
                    $this->assertThat(
                    $value,
                    $this->logicalAnd(
                    $this->greaterThan(0),
                    $this->lessThan(100)
                    )
                    );
                </div>
            </div>

            <div class="example">
                <h4>Assertions do Laravel para Testes HTTP</h4>
                <div class="code-block">
                    // Código de status
                    $response->assertStatus(200);
                    $response->assertOk(); // 200
                    $response->assertCreated(); // 201
                    $response->assertNoContent(); // 204
                    $response->assertRedirect('/dashboard');
                    $response->assertForbidden(); // 403
                    $response->assertNotFound(); // 404

                    // Conteúdo
                    $response->assertSee('Welcome');
                    $response->assertDontSee('Admin Panel');
                    $response->assertSeeInOrder(['First', 'Second', 'Third']);

                    // JSON
                    $response->assertJson(['success' => true]);
                    $response->assertJsonPath('user.email', '<EMAIL>');
                    $response->assertJsonCount(3, 'data');
                    $response->assertJsonStructure([
                    'data' => [
                    '*' => ['id', 'name', 'email']
                    ]
                    ]);

                    // Validação
                    $response->assertValid(['name', 'email']);
                    $response->assertInvalid(['password']);
                    $response->assertInvalid(['password' => 'The password must be at least 8 characters.']);

                    // Views
                    $response->assertViewIs('welcome');
                    $response->assertViewHas('user');
                    $response->assertViewHas('user', function ($user) {
                    return $user->id === 1;
                    });

                    // Headers
                    $response->assertHeader('Content-Type', 'application/json');
                    $response->assertHeaderMissing('X-CSRF-TOKEN');

                    // Cookies
                    $response->assertCookie('remember');
                    $response->assertCookieExpired('temp-access');
                </div>
            </div>

            <div class="best-practice">
                <h4>Escolhendo a Assertion Correta</h4>
                <ul>
                    <li>Use assertions específicas em vez de verificações manuais</li>
                    <li>Escolha assertions que comuniquem claramente o que está sendo verificado</li>
                    <li>Prefira <code>assertSame</code> a <code>assertEquals</code> para comparações mais estritas</li>
                    <li>Use assertions de banco de dados do Laravel em vez de buscar e comparar manualmente</li>
                    <li>Para verificações complexas, considere assertions personalizadas</li>
                </ul>
            </div>

            <div class="example">
                <h4>Criando Assertions Personalizadas</h4>
                <div class="code-block">
                    // Em tests/TestCase.php
                    protected function assertModelEquals($expected, $actual, array $attributes = [])
                    {
                    if (empty($attributes)) {
                    $attributes = array_keys($expected->getAttributes());
                    }

                    foreach ($attributes as $attribute) {
                    $this->assertSame(
                    $expected->$attribute,
                    $actual->$attribute,
                    "Attribute [{$attribute}] does not match."
                    );
                    }

                    return $this;
                    }

                    // Uso no teste
                    public function test_user_is_created_correctly()
                    {
                    $userData = [
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    ];

                    $user = User::create($userData);
                    $foundUser = User::find($user->id);

                    $this->assertModelEquals($user, $foundUser, ['name', 'email']);
                    }
                </div>
            </div>
        </section>

        <section id="fixtures">
            <h3>5.4. Fixtures</h3>
            <p>Fixtures são dados ou estados predefinidos usados para criar um ambiente consistente para testes.</p>

            <div class="best-practice">
                <h4>Tipos de Fixtures</h4>
                <ul>
                    <li><strong>Fixture Setup:</strong> Criar dados necessários para o teste</li>
                    <li><strong>Test Doubles:</strong> Mocks, stubs, fakes, etc.</li>
                    <li><strong>Arquivos de Fixture:</strong> JSON, XML ou outros arquivos usados em testes</li>
                    <li><strong>Fixtures Compartilhadas:</strong> Dados reutilizáveis entre testes</li>
                </ul>
            </div>

            <div class="example">
                <h4>Setup e Teardown</h4>
                <div class="code-block">
                    namespace Tests\Feature;

                    use Tests\TestCase;
                    use App\Models\User;
                    use App\Models\Project;
                    use Illuminate\Foundation\Testing\RefreshDatabase;

                    class ProjectManagementTest extends TestCase
                    {
                    use RefreshDatabase;

                    private $user;
                    private $project;

                    protected function setUp(): void
                    {
                    parent::setUp();

                    // Fixture setup comum para todos os testes
                    $this->user = User::factory()->create();
                    $this->project = Project::factory()
                    ->for($this->user)
                    ->create();
                    }

                    protected function tearDown(): void
                    {
                    // Cleanup após cada teste, se necessário
                    // Por exemplo, limpar arquivos temporários
                    if (file_exists(storage_path('app/tmp/test_file.txt'))) {
                    unlink(storage_path('app/tmp/test_file.txt'));
                    }

                    parent::tearDown();
                    }

                    public function test_user_can_view_own_project()
                    {
                    $response = $this->actingAs($this->user)
                    ->get("/projects/{$this->project->id}");

                    $response->assertOk();
                    $response->assertSee($this->project->name);
                    }

                    public function test_user_can_update_own_project()
                    {
                    $response = $this->actingAs($this->user)
                    ->put("/projects/{$this->project->id}", [
                    'name' => 'Updated Project',
                    'description' => 'New description'
                    ]);

                    $response->assertRedirect("/projects/{$this->project->id}");
                    $this->assertDatabaseHas('projects', [
                    'id' => $this->project->id,
                    'name' => 'Updated Project'
                    ]);
                    }
                    }
                </div>
            </div>

            <div class="example">
                <h4>Usando Arquivos de Fixture</h4>
                <div class="code-block">
                    namespace Tests\Integration;

                    use Tests\TestCase;
                    use App\Services\ReportGenerator;

                    class ReportGeneratorTest extends TestCase
                    {
                    public function test_generates_correct_report_from_json_data()
                    {
                    // Carregar dados de teste de um arquivo JSON
                    $testData = json_decode(
                    file_get_contents(__DIR__ . '/../Fixtures/sample_report_data.json'),
                    true
                    );

                    $generator = new ReportGenerator();
                    $report = $generator->generateFromArray($testData);

                    $this->assertEquals('Monthly Sales', $report->title);
                    $this->assertEquals(12500, $report->totalSales);
                    $this->assertCount(5, $report->topProducts);
                    }

                    public function test_parses_csv_data_correctly()
                    {
                    $csvPath = __DIR__ . '/../Fixtures/sample_data.csv';
                    $generator = new ReportGenerator();

                    $data = $generator->parseCSV($csvPath);

                    $this->assertCount(10, $data);
                    $this->assertEquals('Product 1', $data[0]['name']);
                    $this->assertEquals(199.99, $data[0]['price']);
                    }
                    }
                </div>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas para Fixtures</h4>
                <ul>
                    <li>Mantenha as fixtures simples e focadas no cenário de teste</li>
                    <li>Evite dependências entre fixtures de diferentes testes</li>
                    <li>Use factories para criar objetos de teste de forma consistente</li>
                    <li>Coloque dados de teste em arquivos separados quando complexos</li>
                    <li>Organize fixtures por domínio ou funcionalidade</li>
                    <li>Considere usar traits para fixture setup compartilhado</li>
                </ul>
            </div>

            <div class="example">
                <h4>Trait para Fixture Compartilhada</h4>
                <div class="code-block">
                    namespace Tests\Traits;

                    use App\Models\User;
                    use App\Models\Team;

                    trait CreatesTeamEnvironment
                    {
                    protected $team;
                    protected $owner;
                    protected $members = [];

                    protected function setUpTeamEnvironment($memberCount = 3)
                    {
                    $this->owner = User::factory()->create();

                    $this->team = Team::factory()
                    ->for($this->owner, 'owner')
                    ->create();

                    // Criar membros da equipe
                    $this->members = User::factory()
                    ->count($memberCount)
                    ->create()
                    ->each(function ($user) {
                    $this->team->members()->attach($user->id);
                    });

                    return $this;
                    }

                    protected function createTeamProject($attributes = [])
                    {
                    return $this->team->projects()->create(array_merge([
                    'name' => 'Test Project',
                    'description' => 'This is a test project'
                    ], $attributes));
                    }
                    }

                    // Uso em um teste
                    class TeamProjectsTest extends TestCase
                    {
                    use RefreshDatabase, CreatesTeamEnvironment;

                    public function setUp(): void
                    {
                    parent::setUp();
                    $this->setUpTeamEnvironment();
                    }

                    public function test_team_owner_can_create_project()
                    {
                    $response = $this->actingAs($this->owner)
                    ->post("/teams/{$this->team->id}/projects", [
                    'name' => 'New Project',
                    'description' => 'Project description'
                    ]);

                    $response->assertRedirect();
                    $this->assertDatabaseHas('projects', [
                    'team_id' => $this->team->id,
                    'name' => 'New Project'
                    ]);
                    }
                    }
                </div>
            </div>
        </section>

        <section id="testes-parametrizados">
            <h3>5.5. Testes Parametrizados</h3>
            <p>Testes parametrizados permitem executar o mesmo teste com diferentes conjuntos de dados, reduzindo a
                duplicação de código.</p>

            <div class="example">
                <h4>PHPUnit Data Providers</h4>
                <div class="code-block">
                    namespace Tests\Unit;

                    use App\Services\TaxCalculator;
                    use PHPUnit\Framework\TestCase;

                    class TaxCalculatorTest extends TestCase
                    {
                    /**
                    * @dataProvider taxRateProvider
                    */
                    public function test_calculates_correct_tax_amount($amount, $rate, $expected)
                    {
                    $calculator = new TaxCalculator();
                    $result = $calculator->calculate($amount, $rate);
                    $this->assertEquals($expected, $result);
                    }

                    public function taxRateProvider()
                    {
                    return [
                    'zero amount' => [0, 0.1, 0],
                    'standard rate' => [100, 0.1, 10],
                    'high value' => [1000, 0.1, 100],
                    'zero rate' => [100, 0, 0],
                    'high rate' => [100, 0.25, 25],
                    'fractional amount' => [33.33, 0.1, 3.33],
                    ];
                    }

                    /**
                    * @dataProvider invalidInputProvider
                    */
                    public function test_throws_exception_for_invalid_input($amount, $rate)
                    {
                    $this->expectException(\InvalidArgumentException::class);
                    $calculator = new TaxCalculator();
                    $calculator->calculate($amount, $rate);
                    }

                    public function invalidInputProvider()
                    {
                    return [
                    'negative amount' => [-100, 0.1],
                    'negative rate' => [100, -0.1],
                    'excessive rate' => [100, 1.5],
                    ];
                    }
                    }
                </div>
            </div>

            <div class="example">
                <h4>Testes Parametrizados com Pest</h4>
                <div class="code-block">
                    use App\Services\TaxCalculator;
                    use InvalidArgumentException;

                    beforeEach(function () {
                    $this->calculator = new TaxCalculator();
                    });

                    dataset('validTaxCases', [
                    'zero amount' => [0, 0.1, 0],
                    'standard rate' => [100, 0.1, 10],
                    'high value' => [1000, 0.1, 100],
                    'zero rate' => [100, 0, 0],
                    'high rate' => [100, 0.25, 25],
                    'fractional amount' => [33.33, 0.1, 3.33],
                    ]);

                    test('calculates correct tax amount', function ($amount, $rate, $expected) {
                    $result = $this->calculator->calculate($amount, $rate);
                    expect($result)->toBe($expected);
                    })->with('validTaxCases');

                    dataset('invalidTaxCases', [
                    'negative amount' => [-100, 0.1],
                    'negative rate' => [100, -0.1],
                    'excessive rate' => [100, 1.5],
                    ]);

                    test('throws exception for invalid input', function ($amount, $rate) {
                    $this->expectException(InvalidArgumentException::class);
                    $this->calculator->calculate($amount, $rate);
                    })->with('invalidTaxCases');
                </div>
            </div>

            <div class="best-practice">
                <h4>Quando Usar Testes Parametrizados</h4>
                <ul>
                    <li>Quando o mesmo teste precisa ser executado com diferentes entradas e saídas</li>
                    <li>Para testar valores de borda e casos especiais</li>
                    <li>Para aumentar a cobertura sem duplicar código</li>
                    <li>Para testar sistematicamente várias possibilidades</li>
                </ul>
            </div>

            <div class="note">
                <p>Testes parametrizados são especialmente úteis para testar funções de validação, cálculos,
                    formatadores e outras funções puras que produzem resultados determinísticos baseados apenas em suas
                    entradas.</p>
            </div>
        </section>
    </section>

    <section id="bdd">
        <h2>6. Desenvolvimento Orientado a Comportamento (BDD)</h2>
        <p>O BDD (Behavior-Driven Development) foca em definir o comportamento de um sistema em uma linguagem que tanto
            desenvolvedores quanto stakeholders podem entender.</p>

        <section id="gherkin">
            <h3>6.1. Sintaxe Gherkin</h3>
            <p>Gherkin é uma linguagem específica de domínio que permite escrever especificações de comportamento de
                forma legível por humanos.</p>

            <div class="example">
                <h4>Exemplo de Especificação Gherkin</h4>
                <div class="code-block">
                    Feature: Gerenciamento de carrinho de compras
                    Como um cliente da loja online
                    Eu quero gerenciar os itens no meu carrinho de compras
                    Para que eu possa comprar os produtos que desejo

                    Background:
                    Given eu estou logado como cliente
                    And existem produtos disponíveis na loja

                    Scenario: Adicionar um produto ao carrinho
                    When eu adiciono o produto "Smartphone XYZ" ao carrinho
                    Then o carrinho deve conter 1 item
                    And o carrinho deve exibir o produto "Smartphone XYZ"
                    And o valor total do carrinho deve ser atualizado

                    Scenario: Aumentar a quantidade de um produto no carrinho
                    Given eu tenho o produto "Smartphone XYZ" no meu carrinho com quantidade 1
                    When eu aumento a quantidade para 2
                    Then o carrinho deve exibir o produto "Smartphone XYZ" com quantidade 2
                    And o valor total do carrinho deve ser atualizado para refletir 2 unidades

                    Scenario Outline: Aplicar cupons de desconto
                    Given eu tenho produtos no carrinho com valor total de "<valor_inicial>"
                        When eu aplico o cupom "<cupom>"
                            Then eu devo ver uma mensagem de cupom aplicado com sucesso
                            And o valor total após o desconto deve ser "<valor_final>"

                                Examples:
                                | valor_inicial | cupom | valor_final |
                                | 100.00 | SAVE10 | 90.00 |
                                | 200.00 | SAVE10 | 180.00 |
                                | 100.00 | FREESHIP | 100.00 |
                                | 500.00 | SAVE20PLUS | 400.00 |
                </div>
            </div>

            <div class="best-practice">
                <h4>Estrutura Gherkin</h4>
                <ul>
                    <li><strong>Feature:</strong> Descreve a funcionalidade em teste</li>
                    <li><strong>Background:</strong> Define o contexto comum para todos os cenários</li>
                    <li><strong>Scenario/Scenario Outline:</strong> Um caso de teste específico</li>
                    <li><strong>Given:</strong> O contexto inicial (pré-condições)</li>
                    <li><strong>When:</strong> A ação ou evento que ocorre</li>
                    <li><strong>Then:</strong> O resultado esperado</li>
                    <li><strong>And/But:</strong> Passos adicionais nas seções anteriores</li>
                    <li><strong>Examples:</strong> Tabela de dados para Scenario Outline</li>
                </ul>
            </div>

            <div class="best-practice">
                <h4>Dicas para Escrever Boas Especificações Gherkin</h4>
                <ul>
                    <li>Mantenha os cenários focados em um único comportamento</li>
                    <li>Escreva em uma linguagem que stakeholders possam entender</li>
                    <li>Evite detalhes de implementação</li>
                    <li>Descreva comportamentos, não funcionalidades</li>
                    <li>Use regra "Dado-Quando-Então" para estruturar os testes</li>
                    <li>Reutilize passos entre cenários</li>
                    <li>Use dados relevantes para o negócio nos exemplos</li>
                </ul>
            </div>
        </section>

        <section id="behat">
            <h3>6.2. Behat/Cucumber</h3>
            <p>Behat é um framework PHP para testes BDD que permite executar especificações escritas em Gherkin.</p>

            <div class="example">
                <h4>Instalação e Configuração do Behat</h4>
                <div class="code-block">
                    # Instalação via Composer
                    composer require --dev behat/behat

                    # Inicializar Behat
                    vendor/bin/behat --init

                    # Estrutura resultante
                    features/
                    ├── bootstrap/
                    │ └── FeatureContext.php # Classe de contexto
                    └── example.feature # Arquivo de definição de feature
                </div>
            </div>

            <div class="example">
                <h4>Implementação de Steps no Behat</h4>
                <div class="code-block">
                    // features/bootstrap/FeatureContext.php

                    use Behat\Behat\Context\Context;
                    use Behat\Gherkin\Node\PyStringNode;
                    use Behat\Gherkin\Node\TableNode;
                    use PHPUnit\Framework\Assert;

                    class FeatureContext implements Context
                    {
                    private $user;
                    private $cart = [];
                    private $cartTotal = 0;
                    private $products = [];

                    /**
                    * @Given eu estou logado como cliente
                    */
                    public function euEstouLogadoComoCliente()
                    {
                    $this->user = [
                    'id' => 1,
                    'name' => 'Customer',
                    'is_logged_in' => true
                    ];
                    }

                    /**
                    * @Given existem produtos disponíveis na loja
                    */
                    public function existemProdutosDisponiveisNaLoja()
                    {
                    $this->products = [
                    'Smartphone XYZ' => ['price' => 1000, 'stock' => 10],
                    'Laptop ABC' => ['price' => 2000, 'stock' => 5],
                    'Headphone 123' => ['price' => 200, 'stock' => 20]
                    ];
                    }

                    /**
                    * @When eu adiciono o produto :product ao carrinho
                    */
                    public function euAdicionoOProdutoAoCarrinho($product)
                    {
                    if (!isset($this->products[$product])) {
                    throw new Exception("Produto '$product' não encontrado");
                    }

                    if (!isset($this->cart[$product])) {
                    $this->cart[$product] = [
                    'quantity' => 0,
                    'price' => $this->products[$product]['price']
                    ];
                    }

                    $this->cart[$product]['quantity']++;
                    $this->cartTotal += $this->products[$product]['price'];
                    }

                    /**
                    * @Then o carrinho deve conter :count item(s)
                    */
                    public function oCarrinhoDeveConterItem($count)
                    {
                    $total = array_sum(array_column($this->cart, 'quantity'));
                    Assert::assertEquals($count, $total);
                    }

                    /**
                    * @Then o carrinho deve exibir o produto :product
                    */
                    public function oCarrinhoDeveExibirOProduto($product)
                    {
                    Assert::assertArrayHasKey($product, $this->cart);
                    }

                    /**
                    * @Then o valor total do carrinho deve ser atualizado
                    */
                    public function oValorTotalDoCarrinhoDeveSerAtualizado()
                    {
                    $calculatedTotal = 0;
                    foreach ($this->cart as $item) {
                    $calculatedTotal += $item['price'] * $item['quantity'];
                    }

                    Assert::assertEquals($calculatedTotal, $this->cartTotal);
                    }

                    /**
                    * @Given eu tenho o produto :product no meu carrinho com quantidade :qty
                    */
                    public function euTenhoOProdutoNoMeuCarrinhoComQuantidade($product, $qty)
                    {
                    $this->cart[$product] = [
                    'quantity' => (int)$qty,
                    'price' => $this->products[$product]['price']
                    ];
                    $this->cartTotal = $this->products[$product]['price'] * (int)$qty;
                    }

                    /**
                    * @When eu aumento a quantidade para :qty
                    */
                    public function euAumentoAQuantidadePara($qty)
                    {
                    $product = array_key_first($this->cart);
                    $oldQty = $this->cart[$product]['quantity'];
                    $this->cart[$product]['quantity'] = (int)$qty;

                    // Atualiza o total
                    $this->cartTotal += ($qty - $oldQty) * $this->cart[$product]['price'];
                    }

                    /**
                    * @Then o carrinho deve exibir o produto :product com quantidade :qty
                    */
                    public function oCarrinhoDeveExibirOProdutoComQuantidade($product, $qty)
                    {
                    Assert::assertArrayHasKey($product, $this->cart);
                    Assert::assertEquals((int)$qty, $this->cart[$product]['quantity']);
                    }
                    }
                </div>
            </div>

            <div class="best-practice">
                <h4>Integração do Behat com Laravel</h4>
                <p>Para integrar o Behat com projetos Laravel:</p>

                <div class="code-block">
                    # Instalação das dependências
                    composer require --dev behat/behat
                    composer require --dev laracasts/behat-laravel-extension

                    # Configuração behat.yml
                    default:
                    extensions:
                    Laracasts\Behat\ServiceContainer\LaravelExtension:
                    env_path: .env.behat
                    Behat\MinkExtension:
                    default_session: laravel
                    base_url: http://localhost:8000
                    laravel: ~

                    suites:
                    default:
                    contexts:
                    - FeatureContext
                    - Laracasts\Behat\Context\DatabaseTransactionContext
                    - Laracasts\Behat\Context\MinkContext
                </div>
            </div>
        </section>

        <section id="integracao-bdd">
            <h3>6.3. Integração com o Processo de Desenvolvimento</h3>
            <p>A integração efetiva do BDD no processo de desenvolvimento requer colaboração e alinhamento entre
                diferentes papéis na equipe.</p>

            <div class="best-practice">
                <h4>Fluxo de Trabalho BDD</h4>
                <ol>
                    <li><strong>Descoberta:</strong> Workshops para explorar requisitos e comportamentos</li>
                    <li><strong>Formulação:</strong> Escrita dos cenários Gherkin</li>
                    <li><strong>Automação:</strong> Implementação dos steps em código</li>
                    <li><strong>Desenvolvimento:</strong> Implementação da funcionalidade</li>
                    <li><strong>Verificação:</strong> Execução dos testes</li>
                </ol>
            </div>

            <div class="example">
                <h4>Arquivos de Features em Sistema de Versionamento</h4>
                <p>As especificações Gherkin devem ser tratadas como parte do código-fonte e versionadas junto com o
                    projeto:</p>

                <div class="code-block">
                    project/
                    ├── app/
                    ├── config/
                    ├── database/
                    ├── features/ # Diretório de especificações BDD
                    │ ├── bootstrap/
                    │ │ └── FeatureContext.php
                    │ ├── authentication/ # Organização por domínios
                    │ │ ├── login.feature
                    │ │ └── registration.feature
                    │ ├── cart/
                    │ │ ├── add_to_cart.feature
                    │ │ └── checkout.feature
                    │ └── admin/
                    │ └── manage_products.feature
                    ├── tests/
                    └── vendor/
                </div>
            </div>

            <div class="best-practice">
                <h4>Colaboração Efetiva em BDD</h4>
                <ul>
                    <li>Envolva Product Owners, QA e desenvolvedores na escrita de cenários</li>
                    <li>Use linguagem ubíqua do domínio nos cenários</li>
                    <li>Mantenha exemplos simples e significativos para o negócio</li>
                    <li>Revise cenários antes da implementação</li>
                    <li>Execute cenários BDD em CI/CD</li>
                    <li>Gere documentação viva a partir das especificações</li>
                </ul>
            </div>

            <div class="note">
                <p>O BDD não é apenas uma técnica de teste, mas uma abordagem de desenvolvimento que promove comunicação
                    e entendimento compartilhado. Para aproveitar ao máximo o BDD, é importante que todos os membros da
                    equipe entendam seu valor e participem ativamente do processo.</p>
            </div>

            <div class="example">
                <h4>Relatórios BDD</h4>
                <p>Gerar relatórios legíveis a partir das execuções de testes BDD:</p>

                <div class="code-block">
                    # Execução com relatório HTML
                    vendor/bin/behat --format html --out reports/behat.html

                    # Configuração de múltiplos formatos em behat.yml
                    default:
                    formatters:
                    pretty: true
                    html:
                    output_path: '%paths.base%/reports/behat'
                </div>
            </div>
        </section>
    </section>

    <section id="tdd">
        <h2>7. Desenvolvimento Orientado a Testes (TDD)</h2>
        <p>O TDD (Test-Driven Development) é uma abordagem de desenvolvimento de software em que o desenvolvedor escreve
            testes antes de escrever o código de produção.</p>

        <section id="ciclo-tdd">
            <h3>7.1. Ciclo Red-Green-Refactor</h3>
            <p>O TDD segue um ciclo de desenvolvimento específico conhecido como Red-Green-Refactor.</p>

            <div class="best-practice">
                <h4>O Ciclo TDD</h4>
                <ol>
                    <li><strong>Red:</strong> Escreva um teste que falha (inicialmente todos os testes falham)</li>
                    <li><strong>Green:</strong> Escreva o código mínimo necessário para fazer o teste passar</li>
                    <li><strong>Refactor:</strong> Melhore o código, mantendo-o funcionando</li>
                    <li>Repita para cada nova funcionalidade ou comportamento</li>
                </ol>
            </div>

            <div class="example">
                <h4>Exemplo de Ciclo TDD</h4>
                <div class="code-block">
                    // Ciclo 1: Implementar validação básica de email

                    // RED: Escreva um teste que falha
                    public function test_validates_email_format()
                    {
                    $validator = new EmailValidator();
                    $this->assertFalse($validator->isValid('invalid-email'));
                    $this->assertTrue($validator->isValid('<EMAIL>'));
                    }

                    // GREEN: Implementação mínima para passar no teste
                    class EmailValidator
                    {
                    public function isValid($email)
                    {
                    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
                    }
                    }

                    // REFATOR: Não há necessidade de refatorar nesta etapa

                    // Ciclo 2: Adicionar validação de domínio

                    // RED: Novo teste
                    public function test_validates_domain_exists()
                    {
                    $validator = new EmailValidator();
                    $this->assertTrue($validator->isValid('<EMAIL>'));
                    $this->assertFalse($validator->isValid('<EMAIL>'));
                    }

                    // GREEN: Expandir implementação
                    class EmailValidator
                    {
                    public function isValid($email)
                    {
                    if (filter_var($email, FILTER_VALIDATE_EMAIL) === false) {
                    return false;
                    }

                    $domain = substr(strrchr($email, "@"), 1);
                    return checkdnsrr($domain, 'MX');
                    }
                    }

                    // REFATOR: Melhorar a legibilidade
                    class EmailValidator
                    {
                    public function isValid($email)
                    {
                    if (!$this->hasValidFormat($email)) {
                    return false;
                    }

                    return $this->hasValidDomain($email);
                    }

                    private function hasValidFormat($email)
                    {
                    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
                    }

                    private function hasValidDomain($email)
                    {
                    $domain = substr(strrchr($email, "@"), 1);
                    return checkdnsrr($domain, 'MX');
                    }
                    }
                </div>
            </div>
        </section>

        <section id="beneficios-tdd">
            <h3>7.2. Benefícios do TDD</h3>
            <div class="best-practice">
                <ul>
                    <li><strong>Design orientado ao uso:</strong> Escrever os testes primeiro leva a interfaces mais
                        usáveis</li>
                    <li><strong>Código mais manutenível:</strong> Código desenvolvido com TDD tende a ser mais modular e
                        desacoplado</li>
                    <li><strong>Refatoração segura:</strong> Ter testes garante que melhorias no código não quebrem a
                        funcionalidade</li>
                    <li><strong>Documentação viva:</strong> Testes servem como documentação executável do comportamento
                        do código</li>
                    <li><strong>Menos bugs:</strong> Problemas são identificados mais cedo no ciclo de desenvolvimento
                    </li>
                    <li><strong>Foco em requisitos:</strong> Ajuda a entender o problema antes de implementar a solução
                    </li>
                    <li><strong>Menor tempo de depuração:</strong> Quando um teste falha, o escopo do problema é bem
                        definido</li>
                </ul>
            </div>

            <div class="quote">
                <p>"TDD não é sobre testar. TDD é sobre design. TDD é sobre documentação. TDD é sobre feedback. E sim,
                    você também obtém testes." - Kent Beck</p>
            </div>
        </section>

        <section id="praticas-tdd">
            <h3>7.3. Práticas Recomendadas</h3>
            <div class="best-practice">
                <h4>Dicas para TDD Efetivo</h4>
                <ul>
                    <li><strong>Comece simples:</strong> Escreva o teste mais simples primeiro e evolua gradualmente
                    </li>
                    <li><strong>Pequenos incrementos:</strong> Dê passos pequenos entre ciclos de Red-Green-Refactor
                    </li>
                    <li><strong>Teste apenas um conceito:</strong> Cada teste deve verificar apenas um comportamento
                    </li>
                    <li><strong>Evite dependências:</strong> O código sob teste deve ter o mínimo de dependências
                        externas</li>
                    <li><strong>Não pule refatoração:</strong> A fase Refactor é crucial para manter a qualidade do
                        código</li>
                    <li><strong>Execute todos os testes:</strong> Garanta que novos testes não quebrem os existentes
                    </li>
                    <li><strong>Use mocks para isolar:</strong> Isole o código sob teste de suas dependências</li>
                </ul>
            </div>

            <div class="example">
                <h4>Outside-In TDD com Laravel</h4>
                <p>Uma abordagem Outside-In (também conhecida como London School ou Mockist) começa pelos testes de
                    aceitação e vai descendo até os testes unitários:</p>

                <div class="code-block">
                    // 1. Teste de Feature (comportamento externo)
                    public function test_user_can_create_a_task()
                    {
                    $user = User::factory()->create();

                    $response = $this->actingAs($user)
                    ->post('/tasks', [
                    'title' => 'New Task',
                    'description' => 'Task details',
                    'due_date' => '2023-12-31'
                    ]);

                    $response->assertRedirect('/tasks');
                    $this->assertDatabaseHas('tasks', [
                    'title' => 'New Task',
                    'user_id' => $user->id
                    ]);
                    }

                    // 2. Implementar o controller (usando mocks)
                    namespace Tests\Unit\Http\Controllers;

                    use App\Http\Controllers\TaskController;
                    use App\Http\Requests\TaskRequest;
                    use App\Services\TaskService;
                    use Mockery;
                    use Tests\TestCase;

                    class TaskControllerTest extends TestCase
                    {
                    public function test_store_method_creates_task_using_service()
                    {
                    // Criar mocks
                    $taskService = Mockery::mock(TaskService::class);
                    $request = Mockery::mock(TaskRequest::class);

                    // Definir dados e expectativas
                    $taskData = [
                    'title' => 'New Task',
                    'description' => 'Task details',
                    'due_date' => '2023-12-31'
                    ];

                    $request->shouldReceive('validated')
                    ->once()
                    ->andReturn($taskData);

                    $taskService->shouldReceive('createTask')
                    ->once()
                    ->with($taskData)
                    ->andReturn((object)array_merge($taskData, ['id' => 1]));

                    // Injetar mocks
                    $controller = new TaskController($taskService);

                    // Executar método
                    $response = $controller->store($request);

                    // Verificar resultado
                    $this->assertEquals(redirect('/tasks'), $response);
                    }
                    }

                    // 3. Implementação do controller
                    namespace App\Http\Controllers;

                    use App\Http\Requests\TaskRequest;
                    use App\Services\TaskService;

                    class TaskController extends Controller
                    {
                    protected $taskService;

                    public function __construct(TaskService $taskService)
                    {
                    $this->taskService = $taskService;
                    }

                    public function store(TaskRequest $request)
                    {
                    $this->taskService->createTask($request->validated());
                    return redirect('/tasks');
                    }
                    }

                    // 4. Teste unitário do serviço
                    namespace Tests\Unit\Services;

                    use App\Models\Task;
                    use App\Repositories\TaskRepository;
                    use App\Services\TaskService;
                    use Tests\TestCase;
                    use Mockery;

                    class TaskServiceTest extends TestCase
                    {
                    public function test_create_task_delegates_to_repository()
                    {
                    // Arranjo
                    $taskData = [
                    'title' => 'New Task',
                    'description' => 'Task details',
                    'due_date' => '2023-12-31',
                    'user_id' => 1
                    ];

                    $repository = Mockery::mock(TaskRepository::class);
                    $repository->shouldReceive('create')
                    ->once()
                    ->with($taskData)
                    ->andReturn(new Task($taskData));

                    $service = new TaskService($repository);

                    // Ação
                    $task = $service->createTask($taskData);

                    // Asserção
                    $this->assertInstanceOf(Task::class, $task);
                    $this->assertEquals('New Task', $task->title);
                    }
                    }

                    // 5. Implementação do serviço
                    namespace App\Services;

                    use App\Repositories\TaskRepository;

                    class TaskService
                    {
                    protected $taskRepository;

                    public function __construct(TaskRepository $taskRepository)
                    {
                    $this->taskRepository = $taskRepository;
                    }

                    public function createTask(array $data)
                    {
                    return $this->taskRepository->create($data);
                    }
                    }

                    // 6. Teste de integração do repositório
                    namespace Tests\Integration\Repositories;

                    use App\Models\User;
                    use App\Repositories\TaskRepository;
                    use Tests\TestCase;
                    use Illuminate\Foundation\Testing\RefreshDatabase;

                    class TaskRepositoryTest extends TestCase
                    {
                    use RefreshDatabase;

                    public function test_create_stores_task_in_database()
                    {
                    // Arranjo
                    $user = User::factory()->create();
                    $taskData = [
                    'title' => 'New Task',
                    'description' => 'Task details',
                    'due_date' => '2023-12-31',
                    'user_id' => $user->id
                    ];

                    $repository = new TaskRepository();

                    // Ação
                    $task = $repository->create($taskData);

                    // Asserção
                    $this->assertDatabaseHas('tasks', ['id' => $task->id]);
                    $this->assertEquals('New Task', $task->title);
                    $this->assertEquals($user->id, $task->user_id);
                    }
                    }
                </div>
            </div>

            <div class="best-practice">
                <h4>Adotando TDD na Equipe</h4>
                <ul>
                    <li>Comece gradualmente, com projetos pequenos ou partes menos críticas</li>
                    <li>Faça programação em pares para compartilhar conhecimento de TDD</li>
                    <li>Invista tempo em treinamento e mentoria</li>
                    <li>Celebre pequenas vitórias e compartilhe aprendizados</li>
                    <li>Use ferramentas que facilitam o TDD (como Pest para PHP)</li>
                    <li>Considere kata de codificação e dojos para praticar TDD</li>
                    <li>Não espere perfeição imediata; TDD é uma habilidade que melhora com o tempo</li>
                </ul>
            </div>

            <div class="warning">
                <p>TDD pode inicialmente parecer mais lento, pois há mais código para escrever. No entanto, o tempo
                    economizado em depuração, manutenção e correção de bugs geralmente compensa esse investimento
                    inicial.</p>
            </div>
        </section>
    </section>

    <section id="execucao-testes">
        <h2>8. Execução de Testes</h2>
        <p>Esta seção aborda as diferentes formas de executar testes e integrá-los ao fluxo de desenvolvimento.</p>

        <section id="exec-local">
            <h3>8.1. Execução Local</h3>
            <p>A execução local de testes é fundamental durante o desenvolvimento para rápido feedback.</p>

            <div class="example">
                <h4>Comandos Básicos</h4>
                <div class="code-block">
                    # Executar todos os testes
                    php artisan test

                    # Executar grupo específico de testes
                    php artisan test --testsuite=Unit
                    php artisan test --testsuite=Feature

                    # Executar arquivo específico
                    php artisan test tests/Feature/UserTest.php

                    # Executar método específico
                    php artisan test tests/Feature/UserTest.php --filter=test_user_can_login

                    # Executar com cobertura de código
                    php artisan test --coverage

                    # Executar com cobertura mínima
                    php artisan test --min-coverage=80

                    # Executar testes Pest
                    ./vendor/bin/pest

                    # Executar testes específicos com Pest
                    ./vendor/bin/pest tests/Feature/Auth
                </div>
            </div>

            <div class="best-practice">
                <h4>Opções Úteis para Execução Local</h4>
                <ul>
                    <li><code>--stop-on-failure</code>: Para quando o primeiro teste falha</li>
                    <li><code>--parallel</code>: Executa os testes em paralelo</li>
                    <li><code>--coverage-html</code>: Gera relatório de cobertura HTML</li>
                    <li><code>--verbose</code>: Exibe informações detalhadas</li>
                    <li><code>--repeat=N</code>: Executa testes múltiplas vezes</li>
                    <li><code>--testdox</code>: Saída em formato testdox legível</li>
                    <li><code>--color</code>: Saída colorida</li>
                </ul>
            </div>

            <div class="example">
                <h4>Automatizando Testes no VSCode</h4>
                <p>Arquivo tasks.json para o Visual Studio Code:</p>

                <div class="code-block">
                    {
                    "version": "2.0.0",
                    "tasks": [
                    {
                    "label": "Run All Tests",
                    "type": "shell",
                    "command": "php artisan test",
                    "group": {
                    "kind": "test",
                    "isDefault": true
                    },
                    "presentation": {
                    "reveal": "always",
                    "panel": "new"
                    },
                    "problemMatcher": []
                    },
                    {
                    "label": "Run Current Test File",
                    "type": "shell",
                    "command": "php artisan test ${relativeFile}",
                    "group": "test",
                    "presentation": {
                    "reveal": "always",
                    "panel": "new"
                    },
                    "problemMatcher": []
                    },
                    {
                    "label": "Run Unit Tests",
                    "type": "shell",
                    "command": "php artisan test --testsuite=Unit",
                    "group": "test",
                    "presentation": {
                    "reveal": "always",
                    "panel": "new"
                    },
                    "problemMatcher": []
                    },
                    {
                    "label": "Run Feature Tests",
                    "type": "shell",
                    "command": "php artisan test --testsuite=Feature",
                    "group": "test",
                    "presentation": {
                    "reveal": "always",
                    "panel": "new"
                    },
                    "problemMatcher": []
                    },
                    {
                    "label": "Run Pest Tests",
                    "type": "shell",
                    "command": "./vendor/bin/pest",
                    "group": "test",
                    "presentation": {
                    "reveal": "always",
                    "panel": "new"
                    },
                    "problemMatcher": []
                    }
                    ]
                    }
                </div>
            </div>

            <div class="best-practice">
                <h4>Dicas para Execução Local</h4>
                <ul>
                    <li>Use o banco de dados em memória para maior velocidade</li>
                    <li>Execute testes relacionados antes de fazer commit</li>
                    <li>Configure atalhos de teclado para executar testes rapidamente</li>
                    <li>Mantenha os testes mais rápidos no topo da pirâmide</li>
                    <li>Use PHPUnit watcher para execução automática ao salvar arquivos</li>
                    <li>Configure editor para destacar falhas de teste claramente</li>
                </ul>
            </div>
        </section>

        <section id="exec-integracao">
            <h3>8.2. Integração Contínua</h3>
            <p>A integração contínua garante que os testes sejam executados automaticamente a cada alteração de código.
            </p>

            <div class="example">
                <h4>Configuração GitHub Actions</h4>
                <div class="code-block">
                    # .github/workflows/tests.yml
                    name: Tests

                    on:
                    push:
                    branches: [ main, develop ]
                    pull_request:
                    branches: [ main, develop ]

                    jobs:
                    tests:
                    runs-on: ubuntu-latest

                    services:
                    mysql:
                    image: mysql:8.0
                    env:
                    MYSQL_ROOT_PASSWORD: password
                    MYSQL_DATABASE: test
                    ports:
                    - 3306:3306
                    options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

                    steps:
                    - uses: actions/checkout@v3

                    - name: Setup PHP
                    uses: shivammathur/setup-php@v2
                    with:
                    php-version: '8.2'
                    extensions: mbstring, dom, fileinfo, mysql
                    coverage: xdebug

                    - name: Copy .env
                    run: cp .env.example .env.testing

                    - name: Install Composer dependencies
                    run: composer install --prefer-dist --no-progress

                    - name: Generate key
                    run: php artisan key:generate --env=testing

                    - name: Run migrations
                    run: php artisan migrate --env=testing
                    env:
                    DB_CONNECTION: mysql
                    DB_HOST: 127.0.0.1
                    DB_PORT: 3306
                    DB_DATABASE: test
                    DB_USERNAME: root
                    DB_PASSWORD: password

                    - name: Run PHPUnit tests
                    run: vendor/bin/phpunit --coverage-clover=coverage.xml
                    env:
                    DB_CONNECTION: mysql
                    DB_HOST: 127.0.0.1
                    DB_PORT: 3306
                    DB_DATABASE: test
                    DB_USERNAME: root
                    DB_PASSWORD: password

                    - name: Upload coverage to Codecov
                    uses: codecov/codecov-action@v3
                    with:
                    files: ./coverage.xml
                </div>
            </div>

            <div class="example">
                <h4>Configuração GitLab CI</h4>
                <div class="code-block">
                    # .gitlab-ci.yml
                    stages:
                    - test

                    variables:
                    MYSQL_DATABASE: test
                    MYSQL_ROOT_PASSWORD: password
                    DB_HOST: mysql
                    DB_USERNAME: root
                    DB_PASSWORD: password

                    tests:
                    stage: test
                    image: php:8.2-cli
                    services:
                    - name: mysql:8.0
                    alias: mysql
                    before_script:
                    - apt-get update && apt-get install -y git libzip-dev zip unzip
                    - docker-php-ext-install pdo pdo_mysql zip
                    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin
                    --filename=composer
                    - cp .env.example .env.testing
                    - composer install --prefer-dist --no-ansi --no-interaction --no-progress
                    - php artisan key:generate --env=testing
                    - php artisan migrate --env=testing
                    script:
                    - vendor/bin/phpunit --coverage-text --colors=never
                    artifacts:
                    paths:
                    - ./storage/logs
                    expire_in: 1 day
                    when: always
                </div>
            </div>

            <div class="best-practice">
                <h4>Melhores Práticas de CI para Testes</h4>
                <ul>
                    <li>Execute todos os níveis de teste (unitários, integração, feature)</li>
                    <li>Crie jobs separados para diferentes conjuntos de testes</li>
                    <li>Configure caching de dependências para builds mais rápidos</li>
                    <li>Garanta um ambiente de teste consistente e reproduzível</li>
                    <li>Armazene relatórios de teste como artefatos</li>
                    <li>Publique métricas de cobertura de código</li>
                    <li>Configure notificações para falhas de teste</li>
                    <li>Falhe a build se a cobertura de código cair abaixo do limite</li>
                </ul>
            </div>

            <div class="example">
                <h4>Matriz de Testes</h4>
                <p>Teste em múltiplas versões de PHP e dependências:</p>

                <div class="code-block">
                    # .github/workflows/test-matrix.yml
                    name: Test Matrix

                    on:
                    push:
                    branches: [ main, develop ]
                    pull_request:
                    branches: [ main, develop ]

                    jobs:
                    test:
                    runs-on: ubuntu-latest

                    strategy:
                    fail-fast: true
                    matrix:
                    php: [8.0, 8.1, 8.2]
                    laravel: [9.*, 10.*]
                    dependency-version: [prefer-lowest, prefer-stable]
                    exclude:
                    - php: 8.0
                    laravel: 10.*

                    name: P${{ matrix.php }} - L${{ matrix.laravel }} - ${{ matrix.dependency-version }}

                    steps:
                    - uses: actions/checkout@v3

                    - name: Setup PHP
                    uses: shivammathur/setup-php@v2
                    with:
                    php-version: ${{ matrix.php }}
                    extensions: mbstring, dom, fileinfo, mysql
                    coverage: none

                    - name: Install dependencies
                    run: |
                    composer require "laravel/framework:${{ matrix.laravel }}" --no-interaction --no-update
                    composer update --${{ matrix.dependency-version }} --prefer-dist --no-interaction

                    - name: Execute tests
                    run: vendor/bin/phpunit
                </div>
            </div>
        </section>

        <section id="paralelizacao">
            <h3>8.3. Paralelização de Testes</h3>
            <p>A paralelização de testes pode reduzir significativamente o tempo de execução, especialmente em conjuntos
                de testes grandes.</p>

            <div class="example">
                <h4>Paralelização com Laravel</h4>
                <div class="code-block">
                    # Executar testes em paralelo
                    php artisan test --parallel

                    # Especificar número de processos
                    php artisan test --parallel --processes=4

                    # Agrupar por testsuite
                    php artisan test --parallel --processes=4 --group=unit
                </div>
            </div>

            <div class="best-practice">
                <h4>Considerações para Paralelização</h4>
                <ul>
                    <li>Certifique-se de que os testes sejam independentes entre si</li>
                    <li>Use bancos de dados separados para cada processo (geralmente configurado automaticamente)</li>
                    <li>Evite estados compartilhados entre testes</li>
                    <li>Considere o número de CPUs disponíveis</li>
                    <li>Tenha cuidado com recursos compartilhados (arquivos, caches)</li>
                    <li>Monitore o uso de memória</li>
                </ul>
            </div>

            <div class="example">
                <h4>Configuração Personalizada para Paralelização</h4>
                <div class="code-block">
                    // Configuração em phpunit.xml
                    <phpunit>
                        <!-- ... outras configurações ... -->

                        <coverage>
                            <include>
                                <directory suffix=".php">./app</directory>
                            </include>
                        </coverage>

                        <php>
                            <env name="CACHE_DRIVER" value="array" />
                            <env name="DB_CONNECTION" value="mysql" />
                            <env name="DB_DATABASE" value="test" />
                            <server name="LARAVEL_PARALLEL_TESTING" value="1" />
                        </php>
                    </phpunit>

                    // ParallelTestingServiceProvider.php
                    namespace App\Providers;

                    use Illuminate\Support\Facades\ParallelTesting;
                    use Illuminate\Support\ServiceProvider;

                    class ParallelTestingServiceProvider extends ServiceProvider
                    {
                    public function boot()
                    {
                    // Ao iniciar processo de teste
                    ParallelTesting::setUpProcess(function ($token) {
                    // O $token é um identificador único para esse processo

                    // Usar um banco de dados específico por processo
                    $url = parse_url(config('database.connections.mysql.url'));

                    config()->set(
                    'database.connections.mysql.database',
                    $url['path'] . '_' . $token
                    );
                    });

                    // Ao iniciar teste
                    ParallelTesting::setUpTestCase(function ($testCase) {
                    // Configurar cada teste
                    });
                    }
                    }
                </div>
            </div>

            <div class="note">
                <p>A paralelização pode não beneficiar conjuntos de testes pequenos devido à sobrecarga de inicializar
                    processos múltiplos. É mais eficaz para conjuntos de testes grandes ou testes que são naturalmente
                    lentos.</p>
            </div>
        </section>

        <section id="filtragem">
            <h3>8.4. Filtragem de Testes</h3>
            <p>A filtragem de testes permite focar em subconjuntos específicos de testes, economizando tempo durante o
                desenvolvimento.</p>

            <div class="example">
                <h4>Métodos de Filtragem</h4>
                <div class="code-block">
                    # Por nome de método
                    php artisan test --filter=test_user_can_login

                    # Por nome de classe
                    php artisan test --filter=UserTest

                    # Por grupo (usando anotações)
                    php artisan test --group=auth

                    # Por caminho
                    php artisan test tests/Feature/Auth

                    # Combinando filtros
                    php artisan test --filter=UserTest::test_user_can_login

                    # Usando o Pest
                    ./vendor/bin/pest --group=auth
                    ./vendor/bin/pest --filter="login"
                </div>
            </div>

            <div class="example">
                <h4>Definindo Grupos de Testes</h4>
                <div class="code-block">
                    // Com PHPUnit
                    /**
                    * @group auth
                    * @group slow
                    */
                    public function test_user_can_reset_password()
                    {
                    // ...
                    }

                    // Com Pest
                    test('user can reset password', function () {
                    // ...
                    })->group('auth', 'slow');
                </div>
            </div>

            <div class="best-practice">
                <h4>Estratégias de Agrupamento</h4>
                <ul>
                    <li>Agrupe por área funcional (auth, admin, api)</li>
                    <li>Agrupe por velocidade (fast, slow)</li>
                    <li>Agrupe por dependências (database, api, email)</li>
                    <li>Use grupos para testes de regressão importantes</li>
                    <li>Marque testes que exigem configuração especial</li>
                </ul>
            </div>

            <div class="example">
                <h4>Pular Testes</h4>
                <div class="code-block">
                    // Com PHPUnit
                    /**
                    * @group slow
                    * @skip Temporarily skipped due to API changes
                    */
                    public function test_integration_with_external_api()
                    {
                    // ...
                    }

                    // Ou programaticamente
                    public function test_feature_under_development()
                    {
                    $this->markTestSkipped('This feature is still under development');

                    // O resto do teste não será executado
                    }

                    // Com Pest
                    test('integration with external API', function () {
                    // ...
                    })->skip('Temporarily skipped due to API changes', true);
                </div>
            </div>

            <div class="best-practice">
                <h4>Quando Pular Testes</h4>
                <ul>
                    <li>Durante o desenvolvimento de uma nova funcionalidade</li>
                    <li>Quando um serviço externo está temporariamente indisponível</li>
                    <li>Para testes lentos que você não quer executar frequentemente</li>
                    <li>Em ambientes onde certas funcionalidades não estão disponíveis</li>
                </ul>
            </div>

            <div class="warning">
                <p>Use a funcionalidade de pular testes com moderação. Testes ignorados devem ser revisados regularmente
                    para evitar que problemas permaneçam invisíveis por muito tempo.</p>
            </div>
        </section>
    </section>

    <section id="cobertura">
        <h2>9. Cobertura de Testes</h2>
        <p>A cobertura de testes mede quanto do seu código é executado durante os testes, ajudando a identificar áreas
            que precisam de mais testes.</p>

        <section id="ferramentas-cobertura">
            <h3>9.1. Ferramentas de Cobertura</h3>
            <p>Várias ferramentas estão disponíveis para medir e analisar a cobertura de código.</p>

            <div class="best-practice">
                <h4>Ferramentas Populares</h4>
                <ul>
                    <li><strong>PHPUnit + Xdebug:</strong> Solução nativa para cobertura de código</li>
                    <li><strong>PCOV:</strong> Alternativa mais rápida que Xdebug para cobertura</li>
                    <li><strong>Infection:</strong> Ferramenta de testes de mutação</li>
                    <li><strong>Codecov:</strong> Serviço para análise e visualização de cobertura</li>
                    <li><strong>SonarQube/SonarCloud:</strong> Análise de código incluindo cobertura</li>
                </ul>
            </div>

            <div class="example">
                <h4>Configuração do PHPUnit para Cobertura</h4>
                <div class="code-block">
                    <!-- phpunit.xml -->
                    <phpunit>
                        <coverage processUncoveredFiles="true">
                            <include>
                                <directory suffix=".php">./app</directory>
                            </include>
                            <exclude>
                                <directory suffix=".php">./app/Console</directory>
                                <directory suffix=".php">./app/Exceptions</directory>
                                <directory suffix=".php">./app/Providers</directory>
                            </exclude>
                            <report>
                                <html outputDirectory="tests/coverage" lowUpperBound="50" highLowerBound="90" />
                                <clover outputFile="tests/coverage/clover.xml" />
                                <text outputFile="tests/coverage/coverage.txt" showUncoveredFiles="true"
                                    showOnlySummary="true" />
                            </report>
                        </coverage>
                    </phpunit>
                </div>
            </div>

            <div class="example">
                <h4>Executando Testes com Cobertura</h4>
                <div class="code-block">
                    # Com PHPUnit diretamente
                    XDEBUG_MODE=coverage ./vendor/bin/phpunit --coverage-html tests/coverage

                    # Com Laravel
                    php artisan test --coverage

                    # Gerar relatório em vários formatos
                    php artisan test --coverage-clover=coverage.xml --coverage-html=tests/coverage

                    # Com Pest
                    XDEBUG_MODE=coverage ./vendor/bin/pest --coverage --min=80
                </div>
            </div>

            <div class="best-practice">
                <h4>Configurando PCOV (alternativa mais rápida)</h4>
                <div class="code-block">
                    # Instalar PCOV
                    pecl install pcov

                    # Habilitar PCOV no php.ini
                    extension=pcov.so
                    pcov.enabled=1

                    # Executar testes com PCOV
                    php -d pcov.enabled=1 artisan test --coverage
                </div>
            </div>
        </section>

        <section id="metricas">
            <h3>9.2. Métricas e Análise</h3>
            <p>Entender e interpretar métricas de cobertura é essencial para melhorar a qualidade dos testes.</p>

            <div class="best-practice">
                <h4>Principais Métricas de Cobertura</h4>
                <ul>
                    <li><strong>Line Coverage:</strong> Percentual de linhas de código executadas</li>
                    <li><strong>Branch Coverage:</strong> Percentual de branches condicionais exercitados</li>
                    <li><strong>Function Coverage:</strong> Percentual de funções/métodos chamados</li>
                    <li><strong>Class Coverage:</strong> Percentual de classes usadas</li>
                    <li><strong>Path Coverage:</strong> Percentual de caminhos de execução possíveis cobertos</li>
                    <li><strong>Mutation Score:</strong> Percentual de mutações detectadas pelos testes</li>
                </ul>
            </div>

            <div class="example">
                <h4>Testes de Mutação com Infection</h4>
                <div class="code-block">
                    # Instalação
                    composer require --dev infection/infection

                    # Configuração infection.json.dist
                    {
                    "source": {
                    "directories": [
                    "app"
                    ],
                    "excludes": [
                    "app/Console",
                    "app/Exceptions"
                    ]
                    },
                    "logs": {
                    "text": "infection.log",
                    "summary": "summary.log",
                    "debug": "debug.log",
                    "perMutator": "per-mutator.md",
                    "badge": {
                    "branch": "master"
                    }
                    },
                    "mutators": {
                    "@default": true
                    }
                    }

                    # Execução
                    ./vendor/bin/infection --threads=4
                </div>
            </div>

            <div class="best-practice">
                <h4>Interpretando Resultados de Cobertura</h4>
                <ul>
                    <li>Alta cobertura não garante testes de qualidade</li>
                    <li>Foque em áreas críticas do sistema</li>
                    <li>Analise os caminhos não cobertos</li>
                    <li>Verifique a qualidade das assertivas, não apenas a execução</li>
                    <li>Use testes de mutação para verificar a eficácia dos testes</li>
                    <li>Equilibre esforço e benefício (lei dos retornos decrescentes)</li>
                </ul>
            </div>

            <div class="note">
                <p>O teste de mutação é uma técnica avançada que introduz pequenas alterações (mutações) no código e
                    verifica se os testes detectam essas alterações. É uma forma de "testar seus testes" e complementa
                    métricas tradicionais de cobertura.</p>
            </div>
        </section>

        <section id="limites-cobertura">
            <h3>9.3. Definindo Limites de Cobertura</h3>
            <p>Estabelecer limites mínimos de cobertura ajuda a manter a qualidade do código ao longo do tempo.</p>

            <div class="best-practice">
                <h4>Diretrizes para Limites</h4>
                <ul>
                    <li>Comece com limites realistas baseados na cobertura atual</li>
                    <li>Aumente gradualmente os limites ao longo do tempo</li>
                    <li>Defina limites mais altos para código crítico</li>
                    <li>Considere diferentes limites para diferentes tipos de código</li>
                    <li>Documente exceções e justificativas</li>
                </ul>
            </div>

            <div class="example">
                <h4>Configurando Limites</h4>
                <div class="code-block">
                    # Com Laravel
                    php artisan test --coverage --min=80

                    # No CI com GitHub Actions
                    - name: Run tests with coverage
                    run: php artisan test --coverage-clover=coverage.xml --min=80

                    # Configuração para diferentes diretórios com PHPUnit
                    <phpunit>
                        <coverage>
                            <report>
                                <clover outputFile="clover.xml" />
                                <html outputDirectory="html-coverage" lowUpperBound="50" highLowerBound="90" />
                            </report>
                        </coverage>
                    </phpunit>

                    # Usando ferramentas externas como clover-threshold para verificação
                    composer require --dev scriptfusion/clover-threshold
                    vendor/bin/clover-threshold clover.xml 85
                </div>
            </div>

            <div class="example">
                <h4>Lidando com Limites em Projetos Existentes</h4>
                <div class="code-block">
                    #!/bin/bash
                    # enforce-coverage-threshold.sh

                    COVERAGE_FILE="coverage.xml"
                    CURRENT_THRESHOLD=$(xmllint --xpath "string(//metrics/@statements-covered-percent)" $COVERAGE_FILE)
                    THRESHOLD=80

                    if (( $(echo "$CURRENT_THRESHOLD < $THRESHOLD" | bc -l) )); then
                        echo "Cobertura de código abaixo do limite: $CURRENT_THRESHOLD% (mínimo: $THRESHOLD%)" exit 1
                        else echo "Cobertura de código atendida: $CURRENT_THRESHOLD% (mínimo: $THRESHOLD%)" exit 0 fi
                        </div>
                </div>

                <div class="best-practice">
                    <h4>Políticas de Cobertura</h4>
                    <ul>
                        <li>Nova funcionalidade requer testes com alta cobertura</li>
                        <li>Correções de bugs devem incluir testes que demonstrem o problema</li>
                        <li>Código legado pode ter limites mais baixos inicialmente</li>
                        <li>Código crítico de negócio deve ter limites mais altos</li>
                        <li>Code reviews devem verificar a cobertura de testes</li>
                        <li>Implemente um sistema "não piorar" (não permitir queda na cobertura)</li>
                    </ul>
                </div>
        </section>

        <section id="ignorando-codigo">
            <h3>9.4. Ignorando Código na Cobertura</h3>
            <p>Nem todo código precisa ou deve ser testado. Aprender a excluir certos códigos da análise de cobertura é
                importante.</p>

            <div class="example">
                <h4>Anotações para Ignorar Código</h4>
                <div class="code-block">
                    // Ignorar uma linha
                    $value = $this->riskyOperation(); // @codeCoverageIgnore

                    // Ignorar um bloco
                    // @codeCoverageIgnoreStart
                    public function renderDebugInfo()
                    {
                    if (!$this->isDebugMode()) {
                    return;
                    }

                    echo "Debug information:";
                    var_dump($this->debugData);
                    }
                    // @codeCoverageIgnoreEnd

                    // Ignorar uma classe ou método
                    /**
                    * @codeCoverageIgnore
                    */
                    class DebugHelper
                    {
                    // Esta classe inteira será ignorada na cobertura
                    }
                </div>
            </div>

            <div class="best-practice">
                <h4>O que Excluir da Cobertura</h4>
                <ul>
                    <li>Código gerado automaticamente</li>
                    <li>Código de bootstrap e configuração</li>
                    <li>Código de tratamento de erros raros ou catastróficos</li>
                    <li>Funções de debug e logging</li>
                    <li>Wrappers simples para bibliotecas externas</li>
                    <li>Código de UI com lógica mínima</li>
                </ul>
            </div>

            <div class="warning">
                <p>Tenha cuidado para não abusar das exclusões de cobertura. Cada exclusão deve ter uma justificativa
                    clara e documentada. Use com moderação para evitar esconder código problemático.</p>
            </div>

            <div class="example">
                <h4>Excluindo na Configuração</h4>
                <div class="code-block">
                    <!-- phpunit.xml -->
                    <phpunit>
                        <coverage>
                            <include>
                                <directory suffix=".php">./app</directory>
                            </include>
                            <exclude>
                                <directory suffix=".php">./app/Console</directory>
                                <directory suffix=".php">./app/Exceptions</directory>
                                <file>./app/Http/Middleware/Authenticate.php</file>
                            </exclude>
                        </coverage>
                    </phpunit>
                </div>
            </div>
        </section>
    </section>

    <section id="conclusao">
        <h2>10. Conclusão</h2>
        <p>Testes são um investimento no futuro do seu projeto. Embora possa haver um custo inicial maior em termos de
            tempo e esforço, os benefícios a longo prazo são significativos em termos de qualidade, confiabilidade e
            facilidade de manutenção.</p>

        <div class="best-practice">
            <h4>Pontos-Chave a Lembrar</h4>
            <ul>
                <li>Teste o que é importante: foque em funcionalidades críticas e complexas</li>
                <li>Escolha a abordagem certa para cada componente (unidade, integração, etc.)</li>
                <li>Automatize seus testes e integre-os ao seu workflow diário</li>
                <li>Use TDD quando possível para melhorar o design e a qualidade</li>
                <li>Mantenha testes limpos, organizados e manuteníveis</li>
                <li>Monitore a cobertura, mas não a transforme em um fim em si mesma</li>
                <li>Invista em testes como uma prática de equipe, não uma responsabilidade individual</li>
                <li>Evolua sua estratégia de testes continuamente com base em lições aprendidas</li>
            </ul>
        </div>

        <div class="next-steps">
            <h4>Próximos Passos</h4>
            <ul>
                <li>Avalie o estado atual de testes em seus projetos</li>
                <li>Identifique áreas críticas sem cobertura adequada</li>
                <li>Implemente testes automatizados em seu pipeline CI/CD</li>
                <li>Treine a equipe em práticas de teste e TDD</li>
                <li>Estabeleça padrões e diretrizes claras para testes</li>
                <li>Comece pequeno e expanda gradualmente sua cobertura</li>
                <li>Celebre melhorias e compartilhe sucessos na equipe</li>
            </ul>
        </div>

        <div class="quote">
            <p>"Testes não são sobre encontrar bugs. Testes são sobre reduzir riscos." - Martin Fowler</p>
        </div>
    </section>

    <section id="recursos">
        <h2>11. Recursos Adicionais</h2>

        <div class="resource-list">
            <h3>Documentação Oficial</h3>
            <ul>
                <li><a href="https://laravel.com/docs/testing" target="_blank">Laravel Testing</a></li>
                <li><a href="https://phpunit.de/documentation.html" target="_blank">PHPUnit Documentation</a></li>
                <li><a href="https://pestphp.com/docs/installation" target="_blank">Pest PHP</a></li>
                <li><a href="https://laravel.com/docs/dusk" target="_blank">Laravel Dusk</a></li>
                <li><a href="https://docs.behat.org/en/latest/" target="_blank">Behat Documentation</a></li>
            </ul>
        </div>

        <div class="resource-list">
            <h3>Livros</h3>
            <ul>
                <li>Test-Driven Development: By Example - Kent Beck</li>
                <li>Working Effectively with Legacy Code - Michael Feathers</li>
                <li>Clean Code: A Handbook of Agile Software Craftsmanship - Robert C. Martin</li>
                <li>Laravel Testing Decoded - Jeffrey Way</li>
                <li>Practical Test-Driven Development using C# - John Callaway, Clayton Hunt</li>
            </ul>
        </div>

        <div class="resource-list">
            <h3>Cursos Online</h3>
            <ul>
                <li><a href="https://laracasts.com/series/phpunit-testing-in-laravel" target="_blank">PHPUnit Testing in
                        Laravel - Laracasts</a></li>
                <li><a href="https://laracasts.com/series/pest-from-scratch" target="_blank">Pest From Scratch -
                        Laracasts</a></li>
                <li><a href="https://course.testdrivenlaravel.com/" target="_blank">Test-Driven Laravel - Adam
                        Wathan</a></li>
                <li><a href="https://www.pluralsight.com/courses/phpunit-testing-php-applications"
                        target="_blank">Testing PHP Applications with PHPUnit - Pluralsight</a></li>
            </ul>
        </div>

        <div class="resource-list">
            <h3>Ferramentas Úteis</h3>
            <ul>
                <li><a href="https://github.com/nunomaduro/collision" target="_blank">Collision - Beautiful error
                        reporting</a></li>
                <li><a href="https://github.com/spatie/phpunit-watcher" target="_blank">PHPUnit Watcher - Spatie</a>
                </li>
                <li><a href="https://github.com/infection/infection" target="_blank">Infection - PHP Mutation
                        Testing</a></li>
                <li><a href="https://github.com/mockery/mockery" target="_blank">Mockery - Mock Object Framework</a>
                </li>
                <li><a href="https://github.com/sebastianbergmann/php-code-coverage" target="_blank">PHP Code
                        Coverage</a></li>
                <li><a href="https://github.com/pestphp/pest-plugin-laravel" target="_blank">Pest Laravel Plugin</a>
                </li>
            </ul>
        </div>
    </section>
    </main>

    <footer>
        <p>&copy; 2023 Sistema de Gestão Empresarial. Manual de Testes Versão 1.0</p>
    </footer>
</body>

</html>