<?php

namespace App\Providers;

use App\Helpers\Util;
use Illuminate\Support\ServiceProvider;

class UtilServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('util', function ($app) {
            return new Util();
        });

        // Registrar o alias global
        if (!class_exists('Util')) {
            class_alias(\App\Helpers\Facades\Util::class, 'Util');
        }
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
