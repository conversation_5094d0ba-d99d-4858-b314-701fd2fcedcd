<?php

namespace App\Swagger;

use Psr\Log\AbstractLogger;
use Psr\Log\LogLevel;

/**
 * Logger personalizado para o Swagger
 */
class CustomLogger extends AbstractLogger
{
    /**
     * Registra mensagens de log
     */
    public function log($level, $message, array $context = []): void
    {
        // Ignora avisos sobre referências não encontradas
        if (strpos($message, 'not found for @OA') !== false) {
            return;
        }

        // Para outros erros, usa o comportamento padrão
        $error_level = in_array($level, [LogLevel::NOTICE, LogLevel::INFO])
            ? E_USER_NOTICE
            : E_USER_WARNING;

        trigger_error($message, $error_level);
    }
}
