<?php

namespace App\Http\Controllers\Api;

use App\Controllers\ControllerAbstract;
use App\Services\UserService;
use App\Responses\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Controlador de autenticação para API
 * 
 * Gerencia endpoints relacionados a autenticação
 */
class AuthController extends ControllerAbstract
{
    /**
     * Instância da classe de resposta
     *
     * @var ApiResponse
     */
    protected $response;

    /**
     * Construtor
     *
     * @param UserService $service
     * @param ApiResponse $response
     */
    public function __construct(UserService $service, ApiResponse $response)
    {
        parent::__construct($service);
        $this->response = $response;
    }

    /**
     * Realiza login e retorna token de acesso
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
            'device_name' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->response->validationErrorResponse($validator->errors());
        }

        /** @var UserService $service */
        $service = $this->service;
        $user = $service->verifyCredentials(
            $request->email,
            $request->password
        );

        if (!$user) {
            return $this->response->authenticationError('Credenciais inválidas');
        }

        $deviceName = $request->device_name ?? 'API Client';
        $token = $service->createAccessToken($user, $deviceName);

        return $this->response->loginSuccess(
            $service->transformUser($user),
            $token
        );
    }

    /**
     * Realiza logout (revoga tokens)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $user = $request->user();

        if ($user) {
            // Revogar o token atual
            $user->currentAccessToken()->delete();
        }

        return $this->response->logoutSuccess();
    }

    /**
     * Retorna informações do usuário autenticado
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function me(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return $this->response->unauthorizedResponse();
        }

        /** @var UserService $service */
        $service = $this->service;

        return $this->response->successResponse(
            $service->transformUser($user)
        );
    }
}
