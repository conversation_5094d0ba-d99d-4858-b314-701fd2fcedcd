<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Swagger (OpenAPI) para Laravel</title>
    <link rel="stylesheet" href="css/manual.css">
    <link rel="stylesheet" href="css/prism.css">
    <script src="js/prism.js"></script>
    <!-- CSS do Prism (pode manter seu arquivo prism.css personalizado) -->
    <link rel="stylesheet" href="css/prism.css">

    <!-- JavaScript do Prism (core e linguagens usadas no manual) -->
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-php.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-bash.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-yaml.min.js"></script>

</head>

<body>
    <header class="manual-header">
        <h1>Manual de Swagger (OpenAPI) para Laravel</h1>
        <p>Guia completo para documentação de APIs RESTful usando Swagger/OpenAPI</p>
    </header>

    <div class="container">
        <aside class="manual-nav">
            <div class="nav-header">
                <h2>Navegação</h2>
            </div>
            <nav>
                <ul>
                    <li><a href="#introducao">Introdução ao Swagger/OpenAPI</a></li>
                    <li><a href="#instalacao">Instalação e Configuração</a></li>
                    <li><a href="#anotacoes-basicas">Anotações Básicas</a></li>
                    <li><a href="#documentando-endpoints">Documentando Endpoints</a></li>
                    <li><a href="#modelos">Documentando Modelos e Esquemas</a></li>
                    <li><a href="#autenticacao">Documentando Autenticação</a></li>
                    <li><a href="#exemplos">Exemplos de Requisições e Respostas</a></li>
                    <li><a href="#interface">Interface UI do Swagger</a></li>
                    <li><a href="#automacao">Automação da Documentação</a></li>
                    <li><a href="#boas-praticas">Boas Práticas</a></li>
                    <li><a href="#conclusao">Conclusão</a></li>
                </ul>
            </nav>
        </aside>

        <main class="manual-content">
            <section class="manual-section" id="introducao">
                <h2>Introdução ao Swagger/OpenAPI</h2>
                <p>Swagger (agora oficialmente chamado OpenAPI) é uma especificação para descrever, produzir, consumir e
                    visualizar APIs REST. A documentação de API é crucial para qualquer projeto, especialmente em
                    arquiteturas orientadas a serviços ou microsserviços.</p>

                <div class="info-box">
                    <div class="info-icon">ℹ️</div>
                    <div class="info-content">
                        <p><strong>Por que documentar APIs?</strong></p>
                        <ul>
                            <li>Facilita o entendimento da API para novos desenvolvedores</li>
                            <li>Serve como contrato entre front-end e back-end</li>
                            <li>Permite testes interativos de endpoints</li>
                            <li>Pode gerar automaticamente SDKs e clientes</li>
                        </ul>
                    </div>
                </div>

                <p>O Swagger/OpenAPI consiste em:</p>
                <ul>
                    <li><strong>Especificação OpenAPI:</strong> Um formato padrão para descrever APIs REST</li>
                    <li><strong>Swagger UI:</strong> Uma interface visual para explorar e testar sua API</li>
                    <li><strong>Swagger Editor:</strong> Um editor para criar e validar documentação OpenAPI</li>
                    <li><strong>Swagger Codegen:</strong> Ferramenta para gerar clientes e servidores a partir das
                        especificações</li>
                </ul>
            </section>

            <section class="manual-section" id="instalacao">
                <h2>Instalação e Configuração</h2>
                <p>Para Laravel, usamos o pacote <code>darkaonline/l5-swagger</code>, que é baseado no
                    <code>swagger-php</code> e fornece integração com o framework.
                </p>

                <h3>Passo 1: Instalação</h3>
                <div class="code-block">
                    <pre><code class="language-bash">composer require darkaonline/l5-swagger</code></pre>
                </div>

                <h3>Passo 2: Publicar configuração</h3>
                <div class="code-block">
                    <pre><code class="language-bash">php artisan vendor:publish --provider "L5Swagger\L5SwaggerServiceProvider"</code></pre>
                </div>

                <p>Isso irá publicar:</p>
                <ul>
                    <li>Arquivo de configuração: <code>config/l5-swagger.php</code></li>
                    <li>Views para o Swagger UI</li>
                </ul>

                <h3>Passo 3: Configuração inicial</h3>
                <p>Edite o arquivo <code>config/l5-swagger.php</code> conforme necessário. Algumas configurações
                    importantes:</p>

                <div class="code-block">
                    <pre><code class="language-php">'documentations' => [
    'default' => [
        'api' => [
            'title' => 'API Documentation',
        ],
        'routes' => [
            'api' => 'api/documentation',
        ],
        'paths' => [
            'use_absolute_path' => env('L5_SWAGGER_USE_ABSOLUTE_PATH', true),
            'docs_json' => 'api-docs.json',
            'docs_yaml' => 'api-docs.yaml',
            'format_to_use_for_docs' => env('L5_FORMAT_TO_USE_FOR_DOCS', 'json'),
            'annotations' => [
                base_path('app'),
            ],
        ],
    ],
],</code></pre>
                </div>

                <div class="warning-box">
                    <div class="warning-icon">⚠️</div>
                    <div class="warning-content">
                        <p>Certifique-se de incluir todos os diretórios que contêm suas controllers, models, requests,
                            resources, etc. no array <code>annotations</code>.</p>
                    </div>
                </div>
            </section>

            <section class="manual-section" id="anotacoes-basicas">
                <h2>Anotações Básicas</h2>
                <p>O Swagger usa anotações PHPDoc para gerar a documentação. Vamos começar com as anotações mais
                    básicas.</p>

                <h3>Configuração geral da API</h3>
                <p>Crie um arquivo <code>app/Http/Controllers/Controller.php</code> ou outro arquivo para armazenar a
                    configuração geral:</p>

                <div class="code-block">
                    <pre><code class="language-php">/**
 * @OA\Info(
 *     title="Nome da sua API",
 *     version="1.0.0",
 *     description="Descrição da API",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="Equipe de API"
 *     ),
 *     @OA\License(
 *         name="Apache 2.0",
 *         url="http://www.apache.org/licenses/LICENSE-2.0.html"
 *     )
 * )
 * 
 * @OA\Server(
 *     description="Servidor de Desenvolvimento",
 *     url=L5_SWAGGER_CONST_HOST
 * )
 * 
 * @OA\Server(
 *     description="Servidor de Produção",
 *     url="https://api.seudominio.com.br"
 * )
 * 
 * @OA\SecurityScheme(
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     securityScheme="bearerAuth"
 * )
 */</code></pre>
                </div>

                <div class="info-box">
                    <div class="info-icon">ℹ️</div>
                    <div class="info-content">
                        <p>O uso de <code>L5_SWAGGER_CONST_HOST</code> permite configurar o URL base da API através do
                            arquivo .env.</p>
                    </div>
                </div>
            </section>

            <section class="manual-section" id="documentando-endpoints">
                <h2>Documentando Endpoints</h2>
                <p>Agora vamos documentar endpoints específicos em nossos controllers.</p>

                <h3>Exemplo de um endpoint GET</h3>

                <div class="code-block">
                    <pre><code class="language-php">/**
 * @OA\Get(
 *     path="/api/users",
 *     operationId="getUsersList",
 *     tags={"Usuários"},
 *     summary="Lista todos os usuários",
 *     description="Retorna uma lista paginada de todos os usuários do sistema",
 *     @OA\Parameter(
 *         name="page",
 *         in="query",
 *         description="Número da página",
 *         required=false,
 *         @OA\Schema(type="integer", default=1)
 *     ),
 *     @OA\Parameter(
 *         name="per_page",
 *         in="query",
 *         description="Resultados por página",
 *         required=false,
 *         @OA\Schema(type="integer", default=15)
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Listagem de usuários bem-sucedida",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(property="data", type="array", 
 *                 @OA\Items(ref="#/components/schemas/User")
 *             ),
 *             @OA\Property(property="meta", type="object", 
 *                 @OA\Property(property="current_page", type="integer", example=1),
 *                 @OA\Property(property="last_page", type="integer", example=5),
 *                 @OA\Property(property="per_page", type="integer", example=15),
 *                 @OA\Property(property="total", type="integer", example=75)
 *             ),
 *             @OA\Property(property="links", type="object")
 *         )
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Não autorizado",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="Unauthenticated.")
 *         )
 *     ),
 *     security={{"bearerAuth": {}}}
 * )
 */
public function index()
{
    // Código do controller
}</code></pre>
                </div>

                <h3>Exemplo de um endpoint POST</h3>

                <div class="code-block">
                    <pre><code class="language-php">/**
 * @OA\Post(
 *     path="/api/users",
 *     operationId="storeUser",
 *     tags={"Usuários"},
 *     summary="Criar um novo usuário",
 *     description="Cria um novo usuário no sistema",
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\JsonContent(
 *             required={"name","email","password"},
 *             @OA\Property(property="name", type="string", example="João Silva"),
 *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *             @OA\Property(property="password", type="string", format="password", example="senha123"),
 *             @OA\Property(property="password_confirmation", type="string", format="password", example="senha123"),
 *         )
 *     ),
 *     @OA\Response(
 *         response=201,
 *         description="Usuário criado com sucesso",
 *         @OA\JsonContent(ref="#/components/schemas/User")
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Erro de validação",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="The given data was invalid."),
 *             @OA\Property(
 *                 property="errors",
 *                 type="object",
 *                 @OA\Property(property="email", type="array", @OA\Items(type="string", example="O campo email já está sendo utilizado."))
 *             )
 *         )
 *     ),
 *     security={{"bearerAuth": {}}}
 * )
 */
public function store(Request $request)
{
    // Código do controller
}</code></pre>
                </div>

                <div class="tip-box">
                    <div class="tip-icon">💡</div>
                    <div class="tip-content">
                        <p>Use o <code>operationId</code> para identificar unicamente cada operação. Isso é útil para
                            ferramentas que geram clientes a partir da documentação.</p>
                    </div>
                </div>
            </section>

            <section class="manual-section" id="modelos">
                <h2>Documentando Modelos e Esquemas</h2>
                <p>É uma boa prática definir modelos (schemas) reutilizáveis para suas entidades de dados. Isso evita
                    duplicação e facilita a manutenção.</p>

                <div class="code-block">
                    <pre><code class="language-php">/**
 * @OA\Schema(
 *     schema="User",
 *     required={"id", "name", "email"},
 *     @OA\Property(property="id", type="integer", format="int64", example=1),
 *     @OA\Property(property="name", type="string", example="João Silva"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="email_verified_at", type="string", format="date-time", nullable=true),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time")
 * )
 */
class User extends Model
{
    // Código do modelo
}</code></pre>
                </div>

                <p>Você também pode definir esquemas para resposta de erros e outros componentes reutilizáveis:</p>

                <div class="code-block">
                    <pre><code class="language-php">/**
 * @OA\Schema(
 *     schema="ValidationError",
 *     @OA\Property(property="message", type="string", example="The given data was invalid."),
 *     @OA\Property(
 *         property="errors",
 *         type="object",
 *         @OA\Property(
 *             property="field_name",
 *             type="array",
 *             @OA\Items(type="string", example="Mensagem de erro para o campo")
 *         )
 *     )
 * )
 */</code></pre>
                </div>
            </section>

            <section class="manual-section" id="autenticacao">
                <h2>Documentando Autenticação</h2>
                <p>A documentação adequada da autenticação é crucial para que outros desenvolvedores entendam como
                    acessar sua API.</p>

                <h3>Definindo esquemas de segurança</h3>
                <p>Já definimos um esquema de segurança JWT no início do manual. Vamos adicionar mais exemplos:</p>

                <div class="code-block">
                    <pre><code class="language-php">/**
 * @OA\SecurityScheme(
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     securityScheme="bearerAuth"
 * )
 * 
 * @OA\SecurityScheme(
 *     type="apiKey",
 *     in="header",
 *     name="X-API-KEY",
 *     securityScheme="apiKeyAuth"
 * )
 */</code></pre>
                </div>

                <h3>Endpoint de autenticação</h3>

                <div class="code-block">
                    <pre><code class="language-php">/**
 * @OA\Post(
 *     path="/api/auth/login",
 *     operationId="login",
 *     tags={"Autenticação"},
 *     summary="Login de usuário",
 *     description="Retorna um token JWT para autenticação",
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\JsonContent(
 *             required={"email","password"},
 *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *             @OA\Property(property="password", type="string", format="password", example="senhasegura123"),
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Login bem-sucedido",
 *         @OA\JsonContent(
 *             @OA\Property(property="token", type="string", example="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."),
 *             @OA\Property(property="token_type", type="string", example="bearer"),
 *             @OA\Property(property="expires_in", type="integer", example=3600)
 *         )
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Credenciais inválidas",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="As credenciais fornecidas estão incorretas")
 *         )
 *     )
 * )
 */</code></pre>
                </div>

                <div class="warning-box">
                    <div class="warning-icon">⚠️</div>
                    <div class="warning-content">
                        <p>Evite expor informações sensíveis nos exemplos de resposta, como tokens reais ou senhas.</p>
                    </div>
                </div>
            </section>

            <section class="manual-section" id="exemplos">
                <h2>Exemplos de Requisições e Respostas</h2>
                <p>Fornecer exemplos detalhados de requisições e respostas melhora significativamente a experiência dos
                    desenvolvedores que utilizam sua API.</p>

                <h3>Exemplos complexos de respostas</h3>

                <div class="code-block">
                    <pre><code class="language-php">/**
 * @OA\Get(
 *     path="/api/products",
 *     operationId="getProducts",
 *     tags={"Produtos"},
 *     summary="Lista produtos com filtros",
 *     @OA\Parameter(name="category", in="query", @OA\Schema(type="string")),
 *     @OA\Parameter(name="price_min", in="query", @OA\Schema(type="number")),
 *     @OA\Parameter(name="price_max", in="query", @OA\Schema(type="number")),
 *     @OA\Response(
 *         response=200,
 *         description="Lista de produtos",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/Product")),
 *             @OA\Property(
 *                 property="meta",
 *                 type="object",
 *                 @OA\Property(property="current_page", type="integer", example=1),
 *                 @OA\Property(property="last_page", type="integer", example=3),
 *                 @OA\Property(property="per_page", type="integer", example=15),
 *                 @OA\Property(property="total", type="integer", example=45)
 *             )
 *         )
 *     )
 * )
 */</code></pre>
                </div>

                <h3>Exemplos múltiplos para um único endpoint</h3>
                <p>Você pode fornecer vários exemplos para diferentes cenários:</p>

                <div class="code-block">
                    <pre><code class="language-php">/**
 * @OA\Get(
 *     path="/api/orders/{id}",
 *     operationId="getOrder",
 *     tags={"Pedidos"},
 *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
 *     @OA\Response(
 *         response=200,
 *         description="Detalhes do pedido",
 *         @OA\JsonContent(ref="#/components/schemas/Order"),
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Examples(
 *                 example="pedido_pendente",
 *                 summary="Pedido pendente",
 *                 value={
 *                     "id": 1,
 *                     "status": "pending",
 *                     "total": 150.75,
 *                     "items": {{"product_id": 1, "quantity": 2, "price": 75.50}}
 *                 }
 *             ),
 *             @OA\Examples(
 *                 example="pedido_completo",
 *                 summary="Pedido completo",
 *                 value={
 *                     "id": 2,
 *                     "status": "completed",
 *                     "total": 250.25,
 *                     "items": {{"product_id": 3, "quantity": 1, "price": 250.25}}
 *                 }
 *             )
 *         )
 *     )
 * )
 */</code></pre>
                </div>
            </section>

            <section class="manual-section" id="interface">
                <h2>Interface UI do Swagger</h2>
                <p>O Swagger UI fornece uma interface interativa para explorar e testar sua API.</p>

                <h3>Geração da documentação</h3>
                <p>Para gerar a documentação, execute:</p>

                <div class="code-block">
                    <pre><code class="language-bash">php artisan l5-swagger:generate</code></pre>
                </div>

                <p>Isso analisará as anotações em seu código e gerará o arquivo JSON ou YAML de especificação.</p>

                <h3>Acessando a interface</h3>
                <p>Por padrão, você pode acessar a interface do Swagger em:</p>

                <div class="code-block">
                    <pre><code>http://seu-site.local/api/documentation</code></pre>
                </div>

                <div class="tip-box">
                    <div class="tip-icon">💡</div>
                    <div class="tip-content">
                        <p>Você pode configurar a URL da documentação no arquivo de configuração
                            <code>l5-swagger.php</code>.
                        </p>
                    </div>
                </div>

                <h3>Personalizando a interface</h3>
                <p>Você pode personalizar a aparência da interface do Swagger:</p>

                <div class="code-block">
                    <pre><code class="language-php">// config/l5-swagger.php
'documentations' => [
    'default' => [
        // ...
        'ui' => [
            'display' => [
                'doc_expansion' => env('L5_SWAGGER_UI_DOC_EXPANSION', 'none'),
                'filter' => env('L5_SWAGGER_UI_FILTERS', true),
            ],
            'theme' => [
                'title' => 'API Docs',
                'body_theme' => 'light',
                'primary_color' => '#3E5CAA',
            ],
        ],
    ],
],</code></pre>
                </div>
            </section>

            <section class="manual-section" id="automacao">
                <h2>Automação da Documentação</h2>
                <p>Para manter sua documentação sempre atualizada, recomenda-se automatizar o processo de geração.</p>

                <h3>Geração automática em ambientes de desenvolvimento</h3>
                <p>Configure o Laravel para regenerar a documentação automaticamente em ambientes de desenvolvimento:
                </p>

                <div class="code-block">
                    <pre><code class="language-php">// AppServiceProvider.php
public function register()
{
    if ($this->app->environment('local', 'development')) {
        $this->app->register(\L5Swagger\L5SwaggerServiceProvider::class);
        
        // Registrar comando para executar depois que o app está pronto
        $this->app->booted(function () {
            $this->app->make('command.l5-swagger.generate')->handle();
        });
    }
}</code></pre>
                </div>

                <h3>Integração com CI/CD</h3>
                <p>Inclua a geração da documentação no seu pipeline de CI/CD:</p>

                <div class="code-block">
                    <pre><code class="language-yaml"># .github/workflows/deploy.yml
# ...
jobs:
  deploy:
    # ...
    steps:
      # ...
      - name: Generate API Documentation
        run: php artisan l5-swagger:generate
      # ...</code></pre>
                </div>
            </section>

            <section class="manual-section" id="boas-praticas">
                <h2>Boas Práticas</h2>
                <p>Seguir estas boas práticas ajudará você a manter uma documentação de API de alta qualidade.</p>

                <div class="best-practices">
                    <div class="practice">
                        <h3>1. Mantenha a documentação próxima ao código</h3>
                        <p>As anotações de Swagger devem estar diretamente acima dos métodos que implementam os
                            endpoints. Isso ajuda a manter a documentação atualizada quando o código muda.</p>
                    </div>

                    <div class="practice">
                        <h3>2. Seja descritivo</h3>
                        <p>Forneça descrições claras e úteis para endpoints, parâmetros e modelos. Um bom resumo em uma
                            linha e uma descrição mais detalhada são importantes.</p>
                    </div>

                    <div class="practice">
                        <h3>3. Use tags para agrupar endpoints</h3>
                        <p>Agrupe endpoints relacionados com tags consistentes para tornar a documentação organizada e
                            fácil de navegar.</p>
                    </div>

                    <div class="practice">
                        <h3>4. Documente todos os possíveis códigos de resposta</h3>
                        <p>Não apenas os casos de sucesso, mas também erros comuns (401, 403, 404, 422, 500, etc.) e
                            seus formatos de resposta esperados.</p>
                    </div>

                    <div class="practice">
                        <h3>5. Use esquemas reutilizáveis</h3>
                        <p>Defina modelos para suas entidades principais e reutilize-os em diferentes endpoints para
                            manter a consistência.</p>
                    </div>

                    <div class="practice">
                        <h3>6. Forneça exemplos realistas</h3>
                        <p>Exemplos de requisição e resposta devem ser realistas e representativos, mas sem expor dados
                            sensíveis.</p>
                    </div>

                    <div class="practice">
                        <h3>7. Versione sua documentação</h3>
                        <p>Se sua API tem várias versões, certifique-se de que a documentação reflete isso claramente.
                        </p>
                    </div>

                    <div class="practice">
                        <h3>8. Valide regularmente</h3>
                        <p>Use ferramentas como o editor Swagger para validar sua especificação OpenAPI e garantir que
                            não haja erros ou inconsistências.</p>
                    </div>
                </div>
            </section>

            <section class="manual-section" id="conclusao">
                <h2>Conclusão</h2>
                <p>A documentação de API com Swagger/OpenAPI é uma prática essencial para qualquer projeto Laravel
                    moderno que expõe APIs. Uma documentação bem mantida é um investimento que:</p>
                <ul>
                    <li>Reduz o tempo de integração para novos desenvolvedores</li>
                    <li>Minimiza mal-entendidos sobre como a API deve funcionar</li>
                    <li>Serve como um contrato entre equipes de front-end e back-end</li>
                    <li>Facilita testes e debugging</li>
                    <li>Pode ser usada para gerar código cliente automaticamente</li>
                </ul>

                <p>Com as ferramentas apresentadas neste manual, você está equipado para criar e manter uma documentação
                    de API robusta e útil para seu projeto Laravel.</p>

                <div class="resources-box">
                    <h3>Recursos Adicionais</h3>
                    <ul>
                        <li><a href="https://swagger.io/specification/" target="_blank">OpenAPI Specification</a></li>
                        <li><a href="https://github.com/DarkaOnLine/L5-Swagger" target="_blank">L5-Swagger para
                                Laravel</a></li>
                        <li><a href="https://swagger.io/tools/swagger-ui/" target="_blank">Swagger UI</a></li>
                        <li