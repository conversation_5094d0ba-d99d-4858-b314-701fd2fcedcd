/* Base Styles */
:root {
    /* Color Palette */
    --primary-color: #3490dc;
    --primary-dark: #2779bd;
    --primary-light: #6cb2eb;
    --secondary-color: #38c172;
    --secondary-dark: #1a9c5b;
    --secondary-light: #74d99f;
    --frontend-color: #9561e2;
    --frontend-dark: #794acf;
    --frontend-light: #b794f4;
    --auxiliar-color: #f6993f;
    --auxiliar-dark: #de751f;
    --auxiliar-light: #faad63;
    --dark-color: #2d3748;
    --dark-light: #4a5568;
    --light-color: #f8fafc;
    --light-dark: #e2e8f0;
    --danger-color: #e3342f;
    --warning-color: #ffed4a;
    --info-color: #3490dc;
    --success-color: #38c172;

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, Oxygen, Ubuntu, <PERSON><PERSON><PERSON>, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-family-mono: 'Fira Code', 'Courier New', Courier, monospace;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;

    /* Box Shadow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-sans);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f9fafb;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--spacing-md);
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-lg);
}

h2 {
    font-size: 2rem;
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--light-dark);
}

h3 {
    font-size: 1.5rem;
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

h4 {
    font-size: 1.25rem;
    margin-top: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

code {
    font-family: var(--font-family-mono);
    background-color: var(--light-dark);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.9em;
}

pre {
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    overflow-x: auto;
    margin-bottom: var(--spacing-lg);
}

pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
}

blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: var(--spacing-md);
    margin-left: 0;
    margin-right: 0;
    margin-bottom: var(--spacing-lg);
    color: var(--dark-light);
}

ul, ol {
    margin-bottom: var(--spacing-lg);
    padding-left: var(--spacing-xl);
}

li {
    margin-bottom: var(--spacing-sm);
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-lg);
}

th, td {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--light-dark);
}

th {
    background-color: var(--light-dark);
    text-align: left;
    font-weight: 600;
}

tr:nth-child(even) {
    background-color: var(--light-color);
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: var(--spacing-xxl) var(--spacing-lg);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.hero-section h1 {
    margin-bottom: var(--spacing-md);
}

.hero-section p {
    font-size: 1.25rem;
    max-width: 800px;
    margin: 0 auto;
}

.section-title {
    color: var(--primary-color);
    position: relative;
    display: inline-block;
    margin-bottom: var(--spacing-md);
}

.section-title.frontend {
    color: var(--frontend-color);
}

.section-title.auxiliar {
    color: var(--auxiliar-color);
}

.section-title.coming-soon-section {
    color: var(--dark-light);
}

/* Manual Cards */
.manual-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

.manual-card {
    background-color: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.manual-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.manual-card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: var(--spacing-md);
}

.manual-card-header.frontend {
    background: linear-gradient(135deg, var(--frontend-color) 0%, var(--frontend-dark) 100%);
}

.manual-card-header.auxiliar {
    background: linear-gradient(135deg, var(--auxiliar-color) 0%, var(--auxiliar-dark) 100%);
}

.manual-card-body {
    padding: var(--spacing-md);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.manual-card-body p {
    flex-grow: 1;
    margin-bottom: var(--spacing-lg);
}

.manual-card.coming-soon {
    opacity: 0.8;
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.tag {
    background-color: var(--light-dark);
    color: var(--dark-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

.btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    text-align: center;
    font-weight: 500;
    transition: background-color 0.2s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background-color: var(--primary-dark);
    text-decoration: none;
    color: white;
}

.btn-frontend {
    background-color: var(--frontend-color);
}

.btn-frontend:hover {
    background-color: var(--frontend-dark);
}

.btn-auxiliar {
    background-color: var(--auxiliar-color);
}

.btn-auxiliar:hover {
    background-color: var(--auxiliar-dark);
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Search */
.search-container {
    margin-bottom: var(--spacing-xl);
}

#searchInput {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--light-dark);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

#searchInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 144, 220, 0.25);
}

/* Suggest Section */
.suggest-section {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    margin-bottom: var(--spacing-xxl);
    box-shadow: var(--shadow-md);
}

.suggest-section p {
    max-width: 600px;
    margin: 0 auto var(--spacing-lg);
}

/* Footer */
.manual-footer {
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: var(--spacing-xl) var(--spacing-lg);
    text-align: center;
}

.footer-content p {
    margin-bottom: var(--spacing-sm);
}

/* Manual Content Styles */
.manual-content {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xxl);
}

.manual-header {
    margin-bottom: var(--spacing-xl);
}

.manual-header h1 {
    margin-bottom: var(--spacing-sm);
}

.manual-header .manual-metadata {
    color: var(--dark-light);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-md);
}

.manual-toc {
    background-color: var(--light-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-xl);
}

.manual-toc h3 {
    margin-top: 0;
    margin-bottom: var(--spacing-sm);
    font-size: 1.2rem;
}

.manual-toc ul {
    list-style-type: none;
    padding-left: 0;
}

.manual-toc ul ul {
    padding-left: var(--spacing-lg);
}

.manual-toc li {
    margin-bottom: var(--spacing-xs);
}

.manual-section {
    margin-bottom: var(--spacing-xl);
}

.code-block-header {
    background-color: var(--dark-light);
    color: var(--light-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-top-left-radius: var(--border-radius-md);
    border-top-right-radius: var(--border-radius-md);
    font-family: var(--font-family-mono);
    font-size: 0.9rem;
}

.code-block-header + pre {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    margin-top: 0;
}

.note, .warning, .tip, .important {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    position: relative;
    padding-left: var(--spacing-xl);
}

.note {
    background-color: rgba(52, 144, 220, 0.1);
    border-left: 4px solid var(--info-color);
}

.warning {
    background-color: rgba(246, 153, 63, 0.1);
    border-left: 4px solid var(--warning-color);
}

.tip {
    background-color: rgba(56, 193, 114, 0.1);
    border-left: 4px solid var(--success-color);
}

.important {
    background-color: rgba(227, 52, 47, 0.1);
    border-left: 4px solid var(--danger-color);
}

.note::before, .warning::before, .tip::before, .important::before {
    font-weight: bold;
    display: block;
    margin-bottom: var(--spacing-sm);
}

.note::before {
    content: "Nota";
    color: var(--info-color);
}

.warning::before {
    content: "Atenção";
    color: var(--warning-color);
}

.tip::before {
    content: "Dica";
    color: var(--success-color);
}

.important::before {
    content: "Importante";
    color: var(--danger-color);
}

/* Diagrams */
.diagram {
    max-width: 100%;
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.diagram img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
}

.diagram-caption {
    margin-top: var(--spacing-sm);
    color: var(--dark-light);
    font-style: italic;
    font-size: 0.9rem;
}

/* Navigation */
.manual-nav {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--light-dark);
}

.manual-nav a {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    background-color: var(--light-color);
    transition: background-color 0.2s ease;
}

.manual-nav a:hover {
    background-color: var(--light-dark);
    text-decoration: none;
}

.manual-nav .prev::before {
    content: "←";
    margin-right: var(--spacing-sm);
}

.manual-nav .next::after {
    content: "→";
    margin-left: var(--spacing-sm);
}

/* Responsive */
@media (max-width: 768px) {
    .manual-grid {
        grid-template-columns: 1fr;
    }

    .container {
        padding: var(--spacing-md);
    }

    .hero-section {
        padding: var(--spacing-xl) var(--spacing-md);
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    .manual-content {
        padding: var(--spacing-lg);
    }
}
.mermaid {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
}
