<?php

namespace App\Controllers;

use App\Services\ServiceInterface;
use App\Responses\ResponseInterface;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Schema(
 *     schema="SuccessResponse",
 *     title="Success Response",
 *     description="Resposta de sucesso padrão",
 *     @OA\Property(property="success", type="boolean", example=true),
 *     @OA\Property(property="message", type="string", example="Operação realizada com sucesso"),
 *     @OA\Property(property="data", type="object", nullable=true)
 * )
 */
abstract class ControllerAbstract
{
    protected ServiceInterface $service;
    protected ResponseInterface $response;

    public function __construct(ServiceInterface $service, ResponseInterface $response)
    {
        $this->service = $service;
        $this->response = $response;
    }

    protected function successResponse($data = null, string $message = '', int $statusCode = 200): JsonResponse
    {
        return $this->response->success($data, $message, $statusCode);
    }

    protected function createdResponse($data = null, string $message = ''): JsonResponse
    {
        return $this->response->created($data, $message);
    }

    protected function handleException(\Exception $e, string $message = null): JsonResponse
    {
        $statusCode = $e->getCode() >= 400 ? $e->getCode() : 500;

        if ($statusCode === 400) {
            return $this->response->badRequest($message ?? $e->getMessage());
        } elseif ($statusCode === 401) {
            return $this->response->unauthorized($message ?? $e->getMessage());
        } elseif ($statusCode === 403) {
            return $this->response->forbidden($message ?? $e->getMessage());
        } elseif ($statusCode === 404) {
            return $this->response->notFound($message ?? $e->getMessage());
        } elseif ($statusCode === 422) {
            return $this->response->validationError([], $message ?? $e->getMessage());
        } else {
            return $this->response->serverError($message ?? $e->getMessage());
        }
    }
}
