<?php

namespace App\Controllers;

use App\Services\ServiceInterface;
use App\Responses\ResponseInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="Exemplos",
 *     description="Operações de exemplo"
 * )
 */
class ExampleController
{
    protected ServiceInterface $service;
    protected ResponseInterface $response;

    /**
     * Construtor com injeção de dependências
     */
    public function __construct(ServiceInterface $service, ResponseInterface $response)
    {
        $this->service = $service;
        $this->response = $response;
    }

    /**
     * @OA\Get(
     *     path="/api/examples",
     *     summary="Lista todos os exemplos",
     *     tags={"Exemplos"},
     *     @OA\Response(
     *         response=200,
     *         description="Lista de exemplos",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/ExampleModel")),
     *             @OA\Property(property="message", type="string", example="Exemplos listados com sucesso")
     *         )
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $examples = $this->service->getPaginated($perPage);

        return $this->response->success($examples, 'Exemplos listados com sucesso');
    }

    /**
     * @OA\Get(
     *     path="/api/examples/{id}",
     *     summary="Obtém um exemplo específico",
     *     tags={"Exemplos"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID do exemplo",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Exemplo encontrado",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", ref="#/components/schemas/ExampleModel"),
     *             @OA\Property(property="message", type="string", example="Exemplo encontrado com sucesso")
     *         )
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        $example = $this->service->find($id);

        if (!$example) {
            return $this->response->notFound('Exemplo não encontrado');
        }

        return $this->response->success($example, 'Exemplo encontrado com sucesso');
    }
}
