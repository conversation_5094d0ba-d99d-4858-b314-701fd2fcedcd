<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual de Boas Práticas para Commits Git</title>
    <link rel="stylesheet" href="css/manual.css">
</head>

<body>
    <header class="manual-header">
        <h1>Manual de Boas Práticas para Commits Git</h1>
        <p class="version">Versão 1.0 - Última atualização: Julho 2023</p>
    </header>

    <nav class="manual-nav">
        <ul>
            <li><a href="#introducao">Introdução</a></li>
            <li><a href="#mensagens">Mensagens de Commit</a></li>
            <li><a href="#estrutura">Estrutura de Commits</a></li>
            <li><a href="#workflow">Workflow</a></li>
            <li><a href="#ferramentas">Ferramentas</a></li>
            <li><a href="#problemas">Problemas Comuns</a></li>
            <li><a href="#checklist">Checklist</a></li>
        </ul>
    </nav>

    <section id="introducao" class="manual-section">
        <h2>1. Introdução</h2>

        <div class="intro-text">
            <p>Commits são o alicerce do controle de versão. Cada commit representa um snapshot do seu código em um
                determinado
                momento e, quando bem feitos, contam a história do seu projeto de forma clara e organizada.</p>

            <p>Este manual apresenta as melhores práticas para a criação de commits efetivos que facilitam a
                colaboração, revisão de código e manutenção de projetos a longo prazo.</p>
        </div>

        <div class="key-points">
            <h3>Por que commits bem feitos são importantes?</h3>
            <ul>
                <li>Facilitam o entendimento da evolução do código</li>
                <li>Permitem rastrear bugs e regressões mais facilmente</li>
                <li>Simplificam o processo de revisão de código</li>
                <li>Possibilitam a geração automática de changelogs</li>
                <li>Melhoram a colaboração em equipe</li>
            </ul>
        </div>
    </section>

    <section id="mensagens" class="manual-section">
        <h2>2. Mensagens de Commit</h2>

        <div class="subsection">
            <h3>2.1. Anatomia de uma Boa Mensagem de Commit</h3>

            <div class="content-block">
                <p>Uma mensagem de commit completa segue uma estrutura específica que maximiza sua utilidade:</p>

                <div class="code-block">
                    <pre>
tipo(escopo): resumo conciso

Descrição detalhada do que foi feito e por quê.
Pode conter múltiplas linhas e até parágrafos.

- Pontos específicos podem ser listados
- Utilizando marcadores para facilitar a leitura

Resolve: #123
Relacionado: #456
                    </pre>
                </div>

                <div class="explanation">
                    <ul>
                        <li><strong>Tipo</strong>: Categoriza o commit (feat, fix, docs, style, refactor, test, chore)
                        </li>
                        <li><strong>Escopo</strong>: Opcional, indica a parte do código afetada (módulo, componente)
                        </li>
                        <li><strong>Resumo</strong>: Descrição breve e concisa, no imperativo presente (até 50
                            caracteres)</li>
                        <li><strong>Corpo</strong>: Detalhes do que e por que foi alterado (não o como)</li>
                        <li><strong>Rodapé</strong>: Referências a issues, breaking changes, etc.</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="subsection">
            <h3>2.2. Tipos de Commit</h3>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Tipo</th>
                            <th>Descrição</th>
                            <th>Exemplo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>feat</td>
                            <td>Nova funcionalidade adicionada</td>
                            <td>feat(auth): adiciona autenticação por biometria</td>
                        </tr>
                        <tr>
                            <td>fix</td>
                            <td>Correção de bug</td>
                            <td>fix(api): corrige erro 500 ao enviar payload vazio</td>
                        </tr>
                        <tr>
                            <td>docs</td>
                            <td>Alterações na documentação</td>
                            <td>docs: atualiza README com instruções de instalação</td>
                        </tr>
                        <tr>
                            <td>style</td>
                            <td>Mudanças que não alteram o comportamento (formatação, espaços)</td>
                            <td>style: aplica padrão de formatação do linter</td>
                        </tr>
                        <tr>
                            <td>refactor</td>
                            <td>Alterações no código que não corrigem bugs nem adicionam features</td>
                            <td>refactor(orders): reorganiza estrutura do serviço de pedidos</td>
                        </tr>
                        <tr>
                            <td>test</td>
                            <td>Adições ou correções de testes</td>
                            <td>test(cart): adiciona testes para regras de desconto</td>
                        </tr>
                        <tr>
                            <td>chore</td>
                            <td>Tarefas de manutenção, ajustes de build, etc.</td>
                            <td>chore: atualiza dependências para as últimas versões</td>
                        </tr>
                        <tr>
                            <td>perf</td>
                            <td>Melhorias de performance</td>
                            <td>perf(queries): otimiza consulta na listagem de produtos</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="best-practice">
                <h4>Para que o versionamento semântico funcione bem:</h4>
                <ul>
                    <li>Prefixe breaking changes com <code>BREAKING CHANGE:</code> no corpo do commit</li>
                    <li>Use <code>!</code> após o tipo para indicar uma mudança significativa:
                        <code>feat!: remove suporte a API legada</code>
                    </li>
                    <li>Seja consistente com os tipos de commit em todo o projeto</li>
                </ul>
            </div>
        </div>

        <div class="subsection">
            <h3>2.3. Exemplos Comparativos</h3>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Ruim</th>
                            <th>Bom</th>
                            <th>Explicação</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>correções</td>
                            <td>fix(checkout): corrige cálculo de frete para CEPs internacionais</td>
                            <td>A boa mensagem indica o escopo e descreve especificamente o que foi corrigido</td>
                        </tr>
                        <tr>
                            <td>Adicionei login com Google</td>
                            <td>feat(auth): implementa autenticação via Google OAuth</td>
                            <td>A segunda opção usa o tipo correto e o tempo verbal imperativo</td>
                        </tr>
                        <tr>
                            <td>Atualizei os testes porque estavam quebrados</td>
                            <td>test: corrige testes de integração após mudanças na API</td>
                            <td>A boa mensagem explica o que foi feito de forma concisa</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <section id="estrutura" class="manual-section">
        <h2>3. Estrutura de Commits</h2>

        <div class="subsection">
            <h3>3.1. Tamanho e Escopo</h3>

            <div class="content-block">
                <p>Commits devem ser atômicos: representar uma única alteração lógica que pode ser entendida
                    isoladamente, testada e revertida se necessário.</p>

                <div class="comparison-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Práticas Ruins</th>
                                <th>Boas Práticas</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <ul>
                                        <li>Commits muito grandes que incluem múltiplas alterações não relacionadas</li>
                                        <li>Commits com mensagens como "WIP" ou "Fix stuff"</li>
                                        <li>Múltiplos commits para uma única alteração lógica</li>
                                        <li>Commits que quebram a build ou os testes</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li>Cada commit representa uma alteração lógica completa</li>
                                        <li>O sistema continua funcionando após cada commit</li>
                                        <li>Testes passam para cada commit</li>
                                        <li>Fácil de entender o propósito do commit apenas pela mensagem</li>
                                    </ul>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="subsection">
            <h3>3.2. Estruturando Commits em Uma Feature</h3>

            <div class="content-block">
                <p>Ao implementar uma funcionalidade completa, estruture seus commits para contar uma história lógica:
                </p>

                <div class="example-commits">
                    <pre>
feat(models): adiciona schema do modelo de Pedido
test(models): adiciona testes unitários para modelo de Pedido
feat(controllers): implementa endpoints básicos de listagem de Pedidos
feat(controllers): adiciona filtros avançados na API de Pedidos 
docs(api): atualiza documentação da API com novos endpoints de Pedidos
test(api): adiciona testes de integração para API de Pedidos
                    </pre>
                </div>

                <div class="tip">
                    <h4>Dica para manter commits atômicos:</h4>
                    <p>Use <code>git add -p</code> para selecionar partes específicas de arquivos modificados.
                        Isso permite criar commits coesos mesmo quando você modificou diferentes aspectos em um mesmo
                        arquivo.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="workflow" class="manual-section">
        <h2>4. Workflow</h2>

        <div class="subsection">
            <h3>4.1. Commits e Branches</h3>

            <div class="content-block">
                <p>Um bom workflow de Git inclui uma estratégia clara para branches e commits:</p>

                <div class="best-practice">
                    <h4>Gitflow Workflow</h4>
                    <ul>
                        <li><strong>main/master</strong>: Contém código estável e pronto para produção</li>
                        <li><strong>develop</strong>: Branch de integração para novas features</li>
                        <li><strong>feature/*</strong>: Branches para desenvolvimento de novas funcionalidades</li>
                        <li><strong>hotfix/*</strong>: Branches para correções urgentes em produção</li>
                        <li><strong>release/*</strong>: Branches para preparação de releases</li>
                    </ul>

                    <p>Com o Gitflow, os commits em feature branches podem ser mais granulares, enquanto os merges para
                        develop e main são mais consolidados via squash ou rebase interativo.</p>
                </div>

                <div class="best-practice">
                    <h4>Trunk-Based Development</h4>
                    <ul>
                        <li>Uso de branch principal como base principal de desenvolvimento</li>
                        <li>Branches de feature de curta duração</li>
                        <li>Integração contínua com merges frequentes ao trunk</li>
                        <li>Uso de feature flags para funcionalidades incompletas</li>
                    </ul>

                    <p>No trunk-based development, commits precisam ser ainda mais atômicos e não quebrar a build, já
                        que são
                        integrados frequentemente à branch principal.</p>
                </div>
            </div>
        </div>

        <div class="subsection">
            <h3>4.2. Commits em Equipe</h3>

            <div class="content-block">
                <div class="best-practice">
                    <h4>Boas Práticas</h4>
                    <ul>
                        <li>Estabeleça um padrão de commits e o documente (ex: Conventional Commits)</li>
                        <li>Utilize hooks de pre-commit para validar mensagens de commit</li>
                        <li>Faça commits frequentes em sua branch local</li>
                        <li>Antes de pushar, considere usar rebase interativo para organizar commits</li>
                        <li>Não reescreva histórico já publicado (commits que outros já baixaram)</li>
                        <li>Cada Pull Request deve ser fácil de revisar, com commits bem organizados</li>
                    </ul>
                </div>

                <div class="code-block">
                    <h4>Reescrevendo histórico local para limpeza:</h4>
                    <pre>
# Reorganizar os últimos 3 commits
git rebase -i HEAD~3

# No editor interativo, você pode:
# - reordenar commits (mudando a ordem das linhas)
# - combinar commits (usando 'squash' ou 'fixup')
# - editar mensagens de commit (usando 'reword')
# - dividir commits (usando 'edit')
                    </pre>
                </div>
            </div>
        </div>
    </section>

    <section id="ferramentas" class="manual-section">
        <h2>5. Ferramentas</h2>

        <div class="subsection">
            <h3>5.1. Automatizando Boas Práticas</h3>

            <div class="tools-list">
                <h4>Ferramentas Recomendadas</h4>
                <ul>
                    <li>
                        <strong>commitlint</strong>
                        <p>Verifica se suas mensagens de commit aderem a uma convenção específica.</p>
                        <pre>npm install -g @commitlint/cli @commitlint/config-conventional</pre>
                    </li>
                    <li>
                        <strong>commitizen</strong>
                        <p>CLI interativo para criar mensagens de commit padronizadas.</p>
                        <pre>npm install -g commitizen cz-conventional-changelog</pre>
                    </li>
                    <li>
                        <strong>husky</strong>
                        <p>Facilita a configuração de Git hooks para executar scripts antes de commits/pushes.</p>
                        <pre>npm install husky --save-dev</pre>
                    </li>
                    <li>
                        <strong>standard-version</strong>
                        <p>Utilitário para versionamento e geração de CHANGELOG baseado nas mensagens de commit.</p>
                        <pre>npm install -g standard-version</pre>
                    </li>
                    <li>
                        <strong>git-cz</strong>
                        <p>Interface alternativa para o commitizen.</p>
                        <pre>npm install -g git-cz</pre>
                    </li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Exemplo: Configuração de husky com commitlint</h4>
                <pre>
// package.json
{
  "husky": {
    "hooks": {
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "commitlint": {
    "extends": ["@commitlint/config-conventional"]
  }
}
                </pre>
            </div>
        </div>

        <div class="subsection">
            <h3>5.2. Análise de Commits</h3>

            <div class="tools-list">
                <h4>Ferramentas de Visualização e Análise</h4>
                <ul>
                    <li>
                        <strong>git log</strong>
                        <p>Visualização padrão do histórico de commits no terminal.</p>
                        <pre>git log --oneline --graph --decorate</pre>
                    </li>
                    <li>
                        <strong>GitKraken/Sourcetree</strong>
                        <p>Clientes Git gráficos para visualizar histórico de commits e branches.</p>
                    </li>
                    <li>
                        <strong>git-standup</strong>
                        <p>Mostra seus commits recentes, útil para standups.</p>
                        <pre>npm install -g git-standup</pre>
                    </li>
                    <li>
                        <strong>git-quick-stats</strong>
                        <p>Estatísticas rápidas sobre o repositório e contribuições.</p>
                        <pre>npm install -g git-quick-stats</pre>
                    </li>
                </ul>
            </div>

            <div class="tip">
                <h4>Alias útil para visualização de logs:</h4>
                <pre>
git config --global alias.lg "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit"

# Use com:
git lg
                </pre>
            </div>
        </div>
    </section>

    <section id="problemas" class="manual-section">
        <h2>6. Problemas Comuns</h2>

        <div class="subsection">
            <h3>6.1. Situações Problemáticas e Soluções</h3>

            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Problema</th>
                            <th>Solução</th>
                            <th>Comando</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Commit feito na branch errada</td>
                            <td>Crie uma nova branch a partir da atual, volte a branch original para o estado anterior
                            </td>
                            <td>
                                <pre>
git branch nova-branch
git reset --hard HEAD~1
git checkout nova-branch
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Mensagem de commit errada no último commit</td>
                            <td>Amende o último commit com nova mensagem</td>
                            <td>
                                <pre>
git commit --amend -m "Nova mensagem"
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Commit tem arquivos demais</td>
                            <td>Resete o último commit, adicione os arquivos corretos e commite novamente</td>
                            <td>
                                <pre>
git reset HEAD~1
git add arquivo-correto1 arquivo-correto2
git commit -m "Mensagem correta"
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Commit está faltando arquivos</td>
                            <td>Adicione os arquivos faltantes e amende o último commit</td>
                            <td>
                                <pre>
git add arquivo-faltante
git commit --amend --no-edit
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Precisa reorganizar os últimos commits</td>
                            <td>Use rebase interativo para reordenar, combinar ou ajustar commits</td>
                            <td>
                                <pre>
git rebase -i HEAD~5
                                </pre>
                            </td>
                        </tr>
                        <tr>
                            <td>Commit de segredos ou dados sensíveis</td>
                            <td>Remova completamente do histórico usando git-filter-branch ou BFG</td>
                            <td>
                                <pre>
# Use com extrema cautela
bfg --delete-files arquivo-sensível.txt
                                </pre>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="subsection">
            <h3>6.2. Prevenção de Problemas</h3>

            <div class="best-practice">
                <h4>Prevenção de Commits Problemáticos</h4>
                <ul>
                    <li>Sempre revise o <code>git status</code> e <code>git diff --staged</code> antes de commitar</li>
                    <li>Use <code>git add -p</code> para adicionar alterações seletivamente</li>
                    <li>Configure .gitignore adequadamente para evitar commits de arquivos temporários ou gerados</li>
                    <li>Utilize ferramentas como git-secrets ou git-leaks para detectar informações sensíveis</li>
                    <li>Implemente hooks de pre-commit para validações automáticas</li>
                    <li>Faça uma pausa antes de pushar para revisar seus commits</li>
                </ul>
            </div>

            <div class="code-block">
                <h4>Hook de pre-commit para prevenir commits de dados sensíveis:</h4>
                <pre>
#!/bin/sh
# .git/hooks/pre-commit

PATTERN="API_KEY|SECRET|PASSWORD|TOKEN"

if git diff --cached | grep -i "$PATTERN"; then
  echo "ERRO: Possível vazamento de informações sensíveis detectado."
  echo "Revise o commit e remova qualquer segredo, senha ou token."
  exit 1
fi
                </pre>
            </div>
        </div>
    </section>

    <section id="checklist" class="manual-section">
        <h2>7. Checklist de Boas Práticas</h2>

        <div class="checklist-container">
            <div class="checklist-group">
                <h3>Mensagem do Commit</h3>
                <ul class="checklist">
                    <li>
                        <input type="checkbox" id="check-msg-1">
                        <label for="check-msg-1">Segue o formato padrão (tipo, escopo, descrição)</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-msg-2">
                        <label for="check-msg-2">Título com no máximo 50 caracteres</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-msg-3">
                        <label for="check-msg-3">Texto no imperativo presente (ex: "adiciona" em vez de
                            "adicionado")</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-msg-4">
                        <label for="check-msg-4">Descrição explica o que e por que, não como</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-msg-5">
                        <label for="check-msg-5">Referências a issues ou tickets quando aplicável</label>
                    </li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Conteúdo do Commit</h3>
                <ul class="checklist">
                    <li>
                        <input type="checkbox" id="check-content-1">
                        <label for="check-content-1">Representa uma única alteração lógica</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-content-2">
                        <label for="check-content-2">Não contém arquivos não relacionados à alteração</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-content-3">
                        <label for="check-content-3">Código é compilável/executável após o commit</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-content-4">
                        <label for="check-content-4">Testes relacionados são incluídos ou atualizados</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-content-5">
                        <label for="check-content-5">Não contém código comentado ou de debug temporário</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-content-6">
                        <label for="check-content-6">Não contém informações sensíveis (senhas, tokens, chaves)</label>
                    </li>
                </ul>
            </div>

            <div class="checklist-group">
                <h3>Processo</h3>
                <ul class="checklist">
                    <li>
                        <input type="checkbox" id="check-process-1">
                        <label for="check-process-1">Revisou alterações com git diff antes de commitar</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-process-2">
                        <label for="check-process-2">Executou linters e formatadores relevantes</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-process-3">
                        <label for="check-process-3">Testes passam localmente após as alterações</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-process-4">
                        <label for="check-process-4">Está na branch correta</label>
                    </li>
                    <li>
                        <input type="checkbox" id="check-process-5">
                        <label for="check-process-5">Commit está alinhado ao escopo do trabalho atual
                            (issue/tarefa)</label>
                    </li>
                </ul>
            </div>
        </div>

        <div class="conclusion">
            <h3>Próximos Passos</h3>
            <p>Adotar boas práticas de commits é um processo contínuo que melhora com o tempo. Para avançar:</p>
            <ol>
                <li>Estabeleça padrões de commit em sua equipe ou projeto</li>
                <li>Configure ferramentas de validação automática</li>
                <li>Revise periodicamente o histórico de commits para identificar áreas de melhoria</li>
                <li>Realize treinamentos ou workshops sobre boas práticas de Git</li>
                <li>Integre verificações de qualidade de commits no processo de CI/CD</li>
            </ol>
        </div>
    </section>

    <footer class="manual-footer">
        <div class="footer-content">
            <p>Manual de Boas Práticas para Commits © 2023</p>
            <p>Para atualizações e mais recursos, visite nossa <a href="#">documentação online</a>.</p>
        </div>
    </footer>

</body>

</html>